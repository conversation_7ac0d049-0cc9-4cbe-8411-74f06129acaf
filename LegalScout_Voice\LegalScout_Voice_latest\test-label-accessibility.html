<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Label Accessibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-results {
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Label Accessibility Test</h1>
        <p>This test will scan for label accessibility issues in the agent preview.</p>
        
        <button onclick="runTest()" style="padding: 10px 20px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Run Label Test
        </button>
        
        <div id="results" class="test-results"></div>
    </div>

    <div class="test-container">
        <h2>📱 Agent Preview (Dashboard)</h2>
        <iframe id="preview-iframe" src="/dashboard" title="Dashboard Preview"></iframe>
    </div>

    <script>
        function runTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>🔄 Running test...</p>';
            
            const iframe = document.getElementById('preview-iframe');
            
            // Wait for iframe to load, then test
            setTimeout(() => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const results = testLabels(iframeDoc);
                    displayResults(results);
                } catch (error) {
                    // If we can't access iframe content due to cross-origin, test current page
                    console.log('Testing current page instead of iframe due to cross-origin restrictions');
                    const results = testLabels(document);
                    displayResults(results);
                }
            }, 2000);
        }

        function testLabels(doc) {
            const results = {
                errors: [],
                warnings: [],
                success: [],
                summary: {}
            };

            // Test 1: Find labels with 'for' attributes that don't match any element
            const labels = doc.querySelectorAll('label[for]');
            results.summary.totalLabels = labels.length;
            
            labels.forEach((label, index) => {
                const forValue = label.getAttribute('for');
                const targetElement = doc.getElementById(forValue);
                const labelText = label.textContent.trim();
                
                if (!targetElement) {
                    results.errors.push({
                        type: 'missing-target',
                        message: `Label "${labelText}" has for="${forValue}" but no element with id="${forValue}" exists`,
                        element: label,
                        forValue: forValue,
                        labelText: labelText
                    });
                } else if (!['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'].includes(targetElement.tagName)) {
                    results.warnings.push({
                        type: 'invalid-target',
                        message: `Label "${labelText}" points to ${targetElement.tagName} element (should be form element)`,
                        element: label,
                        target: targetElement,
                        forValue: forValue
                    });
                } else {
                    results.success.push({
                        type: 'valid-association',
                        message: `Label "${labelText}" correctly associated with ${targetElement.tagName}[type="${targetElement.type || 'N/A'}"]`,
                        forValue: forValue
                    });
                }
            });

            // Test 2: Find form elements without labels
            const formElements = doc.querySelectorAll('input, textarea, select');
            results.summary.totalFormElements = formElements.length;
            
            formElements.forEach((element) => {
                if (element.id) {
                    const associatedLabel = doc.querySelector(`label[for="${element.id}"]`);
                    const parentLabel = element.closest('label');
                    
                    if (!associatedLabel && !parentLabel) {
                        results.warnings.push({
                            type: 'missing-label',
                            message: `Form element ${element.tagName}[id="${element.id}"] has no associated label`,
                            element: element
                        });
                    }
                }
            });

            // Test 3: Look for specific problematic patterns
            const problematicLabels = doc.querySelectorAll('label[for="FORM_ELEMENT"], label[for="form_element"], label[for=""], label[for*="undefined"]');
            problematicLabels.forEach((label) => {
                const forValue = label.getAttribute('for');
                results.errors.push({
                    type: 'problematic-pattern',
                    message: `Label has problematic for attribute: "${forValue}"`,
                    element: label,
                    forValue: forValue,
                    labelText: label.textContent.trim()
                });
            });

            return results;
        }

        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            let html = '<h3>🧪 Test Results</h3>';
            
            // Summary
            html += `<div class="success">
                <strong>Summary:</strong> 
                ${results.summary.totalLabels || 0} labels found, 
                ${results.summary.totalFormElements || 0} form elements found
            </div>`;

            // Errors
            if (results.errors.length > 0) {
                html += `<h4>❌ Errors (${results.errors.length})</h4>`;
                results.errors.forEach((error, index) => {
                    html += `<div class="error">
                        <strong>${index + 1}.</strong> ${error.message}<br>
                        <small>Type: ${error.type} | For: "${error.forValue || 'N/A'}"</small>
                    </div>`;
                });
            }

            // Warnings
            if (results.warnings.length > 0) {
                html += `<h4>⚠️ Warnings (${results.warnings.length})</h4>`;
                results.warnings.forEach((warning, index) => {
                    html += `<div class="warning">
                        <strong>${index + 1}.</strong> ${warning.message}
                    </div>`;
                });
            }

            // Success
            if (results.success.length > 0) {
                html += `<h4>✅ Valid Associations (${results.success.length})</h4>`;
                results.success.forEach((success, index) => {
                    html += `<div class="success">
                        <strong>${index + 1}.</strong> ${success.message}
                    </div>`;
                });
            }

            if (results.errors.length === 0 && results.warnings.length === 0) {
                html += '<div class="success"><strong>🎉 No accessibility issues found!</strong></div>';
            }

            resultsDiv.innerHTML = html;
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(runTest, 1000);
        });
    </script>
</body>
</html>
