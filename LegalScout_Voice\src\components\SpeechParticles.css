.speech-particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  opacity: 1; /* Full opacity for better visibility */
  background: transparent !important;
}

/* No trailing transparency - clean background */
.speech-particles-canvas {
  /* These variables will be inherited from the root if available */
  --canvas-trail-base-r: 0;
  --canvas-trail-base-g: 0;
  --canvas-trail-base-b: 0;
  --canvas-trail-alpha: 0; /* No trail effect */
}

/* Ensure the canvas respects dark/light mode */
body.light-mode .speech-particles-canvas {
  --canvas-trail-base-r: 255;
  --canvas-trail-base-g: 255;
  --canvas-trail-base-b: 255;
}

body.dark-mode .speech-particles-canvas {
  --canvas-trail-base-r: 0;
  --canvas-trail-base-g: 0;
  --canvas-trail-base-b: 0;
}
