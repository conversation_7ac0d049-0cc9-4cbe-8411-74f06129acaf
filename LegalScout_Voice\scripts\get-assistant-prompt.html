<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vapi Assistant Prompt Retriever</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .success {
      color: #4CAF50;
      font-weight: bold;
    }
    .error {
      color: #f44336;
      font-weight: bold;
    }
    #output {
      margin-top: 20px;
    }
    .section {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .section h3 {
      margin-top: 0;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      margin: 8px 0;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Vapi Assistant Prompt Retriever</h1>
  
  <div class="section">
    <h3>Configuration</h3>
    <div>
      <label for="apiKey">Vapi API Key:</label>
      <input type="text" id="apiKey" value="6734febc-fc65-4669-93b0-929b31ff6564">
    </div>
    <div>
      <label for="assistantId">Assistant ID:</label>
      <input type="text" id="assistantId" value="e3fff1dd-2e82-4cce-ac6c-8c3271eb0865">
    </div>
    <button id="retrieveButton">Retrieve Assistant Details</button>
  </div>
  
  <div id="output"></div>
  
  <script type="module">
    // Import MCP SDK from CDN
    import { Client } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/index.js';
    import { SSEClientTransport } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/sse.js';
    
    const outputElement = document.getElementById('output');
    const apiKeyInput = document.getElementById('apiKey');
    const assistantIdInput = document.getElementById('assistantId');
    const retrieveButton = document.getElementById('retrieveButton');
    
    function createSection(title) {
      const section = document.createElement('div');
      section.className = 'section';
      
      const heading = document.createElement('h3');
      heading.textContent = title;
      section.appendChild(heading);
      
      return section;
    }
    
    function addContent(section, content) {
      const pre = document.createElement('pre');
      pre.textContent = content;
      section.appendChild(pre);
      return section;
    }
    
    function addProperty(section, label, value) {
      const p = document.createElement('p');
      p.innerHTML = `<strong>${label}:</strong> ${value || 'Not specified'}`;
      section.appendChild(p);
      return section;
    }
    
    function log(message, type = 'info') {
      const line = document.createElement('div');
      line.textContent = message;
      if (type === 'success') {
        line.classList.add('success');
      } else if (type === 'error') {
        line.classList.add('error');
      }
      outputElement.appendChild(line);
      console.log(message);
    }
    
    async function retrieveAssistantDetails() {
      outputElement.innerHTML = '';
      
      const apiKey = apiKeyInput.value.trim();
      if (!apiKey) {
        log('Please enter a valid Vapi API key', 'error');
        return;
      }
      
      const assistantId = assistantIdInput.value.trim();
      if (!assistantId) {
        log('Please enter a valid Assistant ID', 'error');
        return;
      }
      
      log(`Retrieving assistant details for ID: ${assistantId}`);
      log(`Using API Key: ${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`);
      
      try {
        // Initialize MCP client
        log('\nInitializing MCP client...');
        const mcpClient = new Client({
          name: 'legalscout-prompt-retriever',
          version: '1.0.0',
        });
        
        // Create SSE transport for connection to remote Vapi MCP server
        log('Creating SSE transport...');
        const transport = new SSEClientTransport({
          url: 'https://mcp.vapi.ai/sse',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        
        log('Connecting to Vapi MCP server via SSE...');
        await mcpClient.connect(transport);
        log('Connected successfully', 'success');
        
        try {
          // Get assistant details
          log(`\nRetrieving assistant details for ID: ${assistantId}`);
          const assistantResponse = await mcpClient.callTool({
            name: 'get_assistant',
            arguments: {
              assistantId: assistantId
            },
          });
          
          if (assistantResponse && assistantResponse.content) {
            log('Assistant details retrieved successfully', 'success');
            
            const assistant = assistantResponse.content;
            
            // Create assistant details section
            const detailsSection = createSection('Assistant Details');
            addProperty(detailsSection, 'ID', assistant.id);
            addProperty(detailsSection, 'Name', assistant.name);
            addProperty(detailsSection, 'Description', assistant.description);
            addProperty(detailsSection, 'Created At', new Date(assistant.createdAt).toLocaleString());
            outputElement.appendChild(detailsSection);
            
            // Extract and display the system prompt
            if (assistant.systemPrompt) {
              const promptSection = createSection('System Prompt');
              addContent(promptSection, assistant.systemPrompt);
              outputElement.appendChild(promptSection);
            } else if (assistant.model && assistant.model.messages) {
              // Look for system messages in the model configuration
              const systemMessages = assistant.model.messages.filter(msg => msg.role === 'system');
              if (systemMessages.length > 0) {
                const messagesSection = createSection('System Messages');
                
                systemMessages.forEach((msg, index) => {
                  const messageHeading = document.createElement('h4');
                  messageHeading.textContent = `System Message ${index + 1}`;
                  messagesSection.appendChild(messageHeading);
                  
                  const messageContent = document.createElement('pre');
                  messageContent.textContent = msg.content;
                  messagesSection.appendChild(messageContent);
                });
                
                outputElement.appendChild(messagesSection);
              } else {
                log('No system prompt or messages found', 'error');
              }
            } else {
              log('No system prompt found', 'error');
            }
            
            // Display model information
            if (assistant.model) {
              const modelSection = createSection('Model Information');
              addProperty(modelSection, 'Provider', assistant.model.provider);
              addProperty(modelSection, 'Model', assistant.model.model);
              addProperty(modelSection, 'Temperature', assistant.model.temperature);
              outputElement.appendChild(modelSection);
            }
            
            // Display voice information
            if (assistant.voice) {
              const voiceSection = createSection('Voice Information');
              addProperty(voiceSection, 'Provider', assistant.voice.provider);
              addProperty(voiceSection, 'Voice ID', assistant.voice.voiceId);
              addProperty(voiceSection, 'Speed', assistant.voice.speed);
              outputElement.appendChild(voiceSection);
            }
            
            // Display tools
            if (assistant.tools && assistant.tools.length > 0) {
              const toolsSection = createSection('Tools');
              
              assistant.tools.forEach((tool, index) => {
                const toolHeading = document.createElement('h4');
                toolHeading.textContent = `Tool ${index + 1}`;
                toolsSection.appendChild(toolHeading);
                
                addProperty(toolsSection, 'Name', tool.name);
                addProperty(toolsSection, 'Description', tool.description);
              });
              
              outputElement.appendChild(toolsSection);
            }
            
            // Display raw JSON
            const rawSection = createSection('Raw Assistant JSON');
            const rawJson = document.createElement('pre');
            rawJson.textContent = JSON.stringify(assistant, null, 2);
            rawSection.appendChild(rawJson);
            outputElement.appendChild(rawSection);
          } else {
            log('Failed to retrieve assistant details', 'error');
            log(`Response: ${JSON.stringify(assistantResponse)}`, 'error');
          }
        } finally {
          log('\nDisconnecting from server...');
          await mcpClient.close();
          log('Disconnected', 'success');
        }
        
        log('\nOperation completed successfully', 'success');
      } catch (error) {
        log(`\nOperation failed with error: ${error.message}`, 'error');
        console.error(error);
      }
    }
    
    retrieveButton.addEventListener('click', retrieveAssistantDetails);
  </script>
</body>
</html>
