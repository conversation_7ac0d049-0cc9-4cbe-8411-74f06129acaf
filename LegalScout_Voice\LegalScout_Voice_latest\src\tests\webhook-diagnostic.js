/**
 * Webhook Diagnostic Script
 * 
 * This script tests the webhook endpoint manually to diagnose why
 * Vapi calls are not creating consultation records.
 */

import fetch from 'node-fetch';
import { supabase } from '../lib/supabase.js';

// Configuration
const WEBHOOK_URL = process.env.NODE_ENV === 'production' 
  ? 'https://legalscout.app/api/webhook/vapi-call'
  : 'http://localhost:5175/api/webhook/vapi-call';

const TEST_CALL_DATA = {
  id: 'diagnostic-test-' + Date.now(),
  assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
  status: 'completed',
  duration: 180,
  start_time: new Date(Date.now() - 180000).toISOString(),
  end_time: new Date().toISOString(),
  customer: {
    phone_number: '+19145893180'
  },
  transcripts: [
    {
      role: 'assistant',
      message: 'Hello! I\'m <PERSON>, your legal assistant. How can I help you today?'
    },
    {
      role: 'user',
      message: 'Hi, I was in a car accident last week and I\'m not sure what to do.'
    },
    {
      role: 'assistant', 
      message: 'I\'m sorry to hear about your accident. Can you tell me more about what happened?'
    },
    {
      role: 'user',
      message: 'I was stopped at a red light and someone rear-ended me. My back has been hurting since then.'
    }
  ],
  messages: [],
  tool_executions: [],
  metadata: {
    urgency: 'High',
    practice_area: 'Personal Injury',
    client_name: 'Test Client',
    client_email: '<EMAIL>'
  }
};

/**
 * Test webhook endpoint
 */
async function testWebhookEndpoint() {
  console.log('🔍 Testing webhook endpoint...');
  console.log('URL:', WEBHOOK_URL);
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vapi-Webhook-Test/1.0'
      },
      body: JSON.stringify(TEST_CALL_DATA)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      console.log('✅ Webhook endpoint responded successfully');
      return JSON.parse(responseText);
    } else {
      console.log('❌ Webhook endpoint returned error');
      return null;
    }
  } catch (error) {
    console.error('❌ Error calling webhook endpoint:', error.message);
    return null;
  }
}

/**
 * Check if call record was created
 */
async function checkCallRecord() {
  console.log('\n🔍 Checking for call record in Supabase...');
  
  try {
    const { data, error } = await supabase
      .from('call_records')
      .select('*')
      .eq('call_id', TEST_CALL_DATA.id)
      .single();

    if (error) {
      console.log('❌ No call record found:', error.message);
      return null;
    }

    console.log('✅ Call record found:', data);
    return data;
  } catch (error) {
    console.error('❌ Error checking call record:', error.message);
    return null;
  }
}

/**
 * Check if consultation was created
 */
async function checkConsultation() {
  console.log('\n🔍 Checking for consultation record in Supabase...');
  
  try {
    const { data, error } = await supabase
      .from('consultations')
      .select('*')
      .eq('metadata->call_id', TEST_CALL_DATA.id)
      .single();

    if (error) {
      console.log('❌ No consultation found:', error.message);
      return null;
    }

    console.log('✅ Consultation found:', data);
    return data;
  } catch (error) {
    console.error('❌ Error checking consultation:', error.message);
    return null;
  }
}

/**
 * Check attorney configuration
 */
async function checkAttorneyConfig() {
  console.log('\n🔍 Checking attorney configuration...');
  
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      console.log('❌ Attorney not found:', error.message);
      return null;
    }

    console.log('✅ Attorney found:');
    console.log('  ID:', data.id);
    console.log('  Name:', data.name);
    console.log('  Assistant ID:', data.vapi_assistant_id);
    console.log('  Phone:', data.phone);
    
    return data;
  } catch (error) {
    console.error('❌ Error checking attorney:', error.message);
    return null;
  }
}

/**
 * Test assistant ID mapping
 */
async function testAssistantMapping() {
  console.log('\n🔍 Testing assistant ID mapping...');
  
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('id, email, vapi_assistant_id')
      .eq('vapi_assistant_id', TEST_CALL_DATA.assistant_id);

    if (error) {
      console.log('❌ Error querying assistant mapping:', error.message);
      return null;
    }

    if (data.length === 0) {
      console.log('❌ No attorney found for assistant ID:', TEST_CALL_DATA.assistant_id);
      return null;
    }

    console.log('✅ Assistant mapping found:');
    data.forEach(attorney => {
      console.log(`  Attorney: ${attorney.email} (${attorney.id})`);
    });
    
    return data[0];
  } catch (error) {
    console.error('❌ Error testing assistant mapping:', error.message);
    return null;
  }
}

/**
 * Clean up test data
 */
async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete call record
    await supabase
      .from('call_records')
      .delete()
      .eq('call_id', TEST_CALL_DATA.id);

    // Delete consultation
    await supabase
      .from('consultations')
      .delete()
      .eq('metadata->call_id', TEST_CALL_DATA.id);

    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up:', error.message);
  }
}

/**
 * Main diagnostic function
 */
async function runDiagnostic() {
  console.log('🚀 Starting Vapi Webhook Diagnostic\n');
  
  // Check attorney configuration first
  const attorney = await checkAttorneyConfig();
  if (!attorney) {
    console.log('❌ Cannot proceed without attorney configuration');
    return;
  }

  // Test assistant mapping
  const mapping = await testAssistantMapping();
  if (!mapping) {
    console.log('❌ Assistant ID mapping issue detected');
  }

  // Test webhook endpoint
  const webhookResult = await testWebhookEndpoint();
  if (!webhookResult) {
    console.log('❌ Webhook test failed');
    return;
  }

  // Wait a moment for processing
  console.log('\n⏳ Waiting for data processing...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Check results
  const callRecord = await checkCallRecord();
  const consultation = await checkConsultation();

  // Summary
  console.log('\n📊 DIAGNOSTIC SUMMARY:');
  console.log('  Webhook endpoint:', webhookResult ? '✅ Working' : '❌ Failed');
  console.log('  Call record created:', callRecord ? '✅ Yes' : '❌ No');
  console.log('  Consultation created:', consultation ? '✅ Yes' : '❌ No');
  console.log('  Assistant mapping:', mapping ? '✅ Working' : '❌ Failed');

  // Clean up
  await cleanup();
}

// Run diagnostic if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDiagnostic().catch(console.error);
}

export { runDiagnostic, testWebhookEndpoint, checkCallRecord, checkConsultation };
