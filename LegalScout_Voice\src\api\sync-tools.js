/**
 * Synchronization Tools API
 * 
 * This file contains API routes for the synchronization tools.
 */

import { syncAttorneyProfile, manageAuthState, validateConfiguration, checkPreviewConsistency } from '../services/syncTools.js';

// Sync Attorney Profile
export const syncAttorneyProfileHandler = async (req, res) => {
  try {
    const { attorneyId, forceUpdate } = req.body;
    
    // Call the syncAttorneyProfile function
    const result = await syncAttorneyProfile({ attorneyId, forceUpdate });
    
    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error syncing attorney profile:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Manage Auth State
export const manageAuthStateHandler = async (req, res) => {
  try {
    const { authData, action } = req.body;
    
    // Call the manageAuthState function
    const result = await manageAuthState({ authData, action });
    
    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error managing auth state:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Validate Configuration
export const validateConfigurationHandler = async (req, res) => {
  try {
    const { attorneyId, configData } = req.body;
    
    // Call the validateConfiguration function
    const result = await validateConfiguration({ attorneyId, configData });
    
    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error validating configuration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Check Preview Consistency
export const checkPreviewConsistencyHandler = async (req, res) => {
  try {
    const { attorneyId } = req.body;
    
    // Call the checkPreviewConsistency function
    const result = await checkPreviewConsistency({ attorneyId });
    
    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error checking preview consistency:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
