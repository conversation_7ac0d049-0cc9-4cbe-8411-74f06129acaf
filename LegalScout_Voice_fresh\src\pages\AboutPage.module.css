/* About Page Specific Styles */
.sectionHeader {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  text-align: center;
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
  padding-bottom: 1rem;
}

.sectionHeader::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  border-radius: 2px;
}

.featureCard {
  position: relative;
  background: rgba(30, 41, 59, 0.2);
  border-radius: 16px;
  padding: 2.5rem 2rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.4s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
  height: 100%;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(46, 123, 243, 0.08),
    rgba(79, 209, 234, 0.08));
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.featureCard:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 40px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(67, 190, 214, 0.2);
}

.featureCard:hover::before {
  opacity: 1;
}

.featureCard:hover .featureIcon {
  transform: scale(1.2);
  background: rgba(67, 190, 214, 0.2);
  color: var(--accent-primary);
}

.featureIcon {
  position: relative;
  z-index: 2;
  font-size: 3rem;
  margin-bottom: 1.75rem;
  color: #43bed6;
  transition: all 0.4s ease;
  background: rgba(67, 190, 214, 0.1);
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.stepCard {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stepCard::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: transform 0.5s ease;
  transform: rotate(45deg);
}

.stepCard:hover::before {
  transform: rotate(45deg) translate(50%, 50%);
}

.stepNumber {
  position: relative;
  z-index: 2;
}

.stepCard:hover .stepNumber {
  animation: pulse 1.5s infinite;
}

.faqCard {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.faqCard:hover {
  transform: scale(1.02);
}

.faqQuestion {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.faqAnswerWrapper {
  overflow: hidden;
  transition: all 0.3s ease;
}

.faqAnswer {
  padding: 0 1.5rem 1.5rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
}

.faqDetail {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
  color: #43bed6;
  font-size: 0.875rem;
  line-height: 1.6;
}

.ctaButton {
  position: relative;
  overflow: hidden;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(270deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
  background-size: 200% 100%;
  border-radius: 50px;
  z-index: -1;
  animation: gradientMove 3s linear infinite;
}

.ctaButton:hover {
  transform: translateY(-2px);
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  100% { background-position: 200% 50%; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Scroll Progress Bar */
.scrollProgress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  transform-origin: 0%;
  z-index: 1000;
}

/* AboutPage Module CSS - Enhanced Layout and Robin's Egg Blue Theme */

.page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: var(--bg-primary);
}

.scrollProgress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg,
    rgba(46, 123, 243, 0.8),
    rgba(67, 190, 214, 0.8),
    rgba(79, 209, 234, 0.8),
    rgba(46, 123, 243, 0.8));
  background-size: 300% 100%;
  transform-origin: 0%;
  z-index: 1000;
  animation: gradientShift 8s linear infinite;
}

.heroSection {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 0 1rem;
}

.heroBackground {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(46, 123, 243, 0.05), rgba(67, 190, 214, 0.05));
  z-index: 0;
}

.heroContent {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 0 1.5rem;
  max-width: 900px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 2rem;
  background: linear-gradient(to right, var(--accent-primary), #43bed6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-align: center;
  width: 100%;
  max-width: 800px;
  padding: 0 1rem 0.5rem;
}

.heroDescription {
  font-size: 1.35rem;
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto 2rem;
  color: var(--text-primary);
  text-align: center;
  opacity: 0.9;
  padding: 0 1rem;
}

.scrollPrompt {
  position: absolute;
  bottom: 2.5rem;
  left: 40%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 0.85rem;
  opacity: 0.95;
  letter-spacing: 0.02rem;
  text-align: center;
  width: 100%;
  max-width: 180px;
  margin: 0;
  font-weight: 500;
  z-index: 10;
}

.scrollArrow {
  margin-top: 0.5rem;
  display: block;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
  width: 24px !important;
  height: 24px !important;
}

.scrollIndicator {
  width: 30px;
  height: 50px;
  border: 2px solid var(--text-secondary);
  border-radius: 15px;
  margin-top: 0.5rem;
  position: relative;
}

.scrollDot {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--text-secondary);
  left: 50%;
  top: 8px;
  transform: translateX(-50%);
  border-radius: 50%;
}

.section {
  position: relative;
  padding: 5rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

.sectionTitle {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 3.5rem;
  text-align: center;
  background: linear-gradient(to right, var(--accent-primary), #43bed6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -1.2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--accent-primary), #43bed6);
  border-radius: 2px;
}

.featuresContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 2rem;
  width: 100%;
  max-width: 1000px;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(46, 123, 243, 0.08),
    rgba(79, 209, 234, 0.08));
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.featureCard:hover::before {
  opacity: 1;
}

.featureTitle {
  position: relative;
  z-index: 2;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.featureDescription {
  position: relative;
  z-index: 2;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.featureHighlight {
  position: relative;
  z-index: 2;
  font-size: 0.875rem;
  color: #43bed6;
  font-weight: 500;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.4s ease;
}

.featureCard:hover .featureHighlight {
  opacity: 1;
  transform: translateY(0);
}

.stepsContainer {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
  max-width: 1000px;
}

.stepCard {
  position: relative;
  flex: 1;
  background: rgba(30, 41, 59, 0.2);
  border-radius: 16px;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: transform 0.4s ease;
}

.stepNumber {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-primary), #43bed6);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.25rem;
  box-shadow: 0 8px 15px rgba(67, 190, 214, 0.3);
}

.stepTitle {
  position: relative;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.stepDescription {
  position: relative;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.stepCard:hover {
  transform: translateY(-8px);
}

.faqContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 1.5rem;
  width: 100%;
  max-width: 1000px;
}

.faqItem {
  position: relative;
  background: rgba(30, 41, 59, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.faqItem:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(67, 190, 214, 0.15);
}

.faqQuestion {
  position: relative;
  padding: 1.5rem 1.5rem;
  font-size: 1.15rem;
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: color 0.3s ease;
}

.faqItem:hover .faqQuestion {
  color: #43bed6;
}

.faqArrow {
  color: #43bed6;
  transition: transform 0.3s ease, color 0.3s ease;
  background: rgba(67, 190, 214, 0.1);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
}

.faqAnswerWrapper {
  overflow: hidden;
  transition: all 0.3s ease;
}

.faqAnswer {
  padding: 0 1.5rem 1.5rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
}

.faqDetail {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
  color: #43bed6;
  font-size: 0.875rem;
  line-height: 1.6;
}

.ctaSection {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.8),
    rgba(15, 23, 42, 0.85));
  border-radius: 24px;
  padding: 4rem 2rem;
  margin: 5rem auto;
  max-width: 900px;
  width: 90%;
  text-align: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(67, 190, 214, 0.1);
  position: relative;
  overflow: hidden;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(67, 190, 214, 0.15),
    transparent 60%
  );
  z-index: 0;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.ctaText {
  font-size: 1.15rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  line-height: 1.6;
}

.ctaButton {
  position: relative;
  display: inline-block;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(45deg, var(--accent-primary), #43bed6);
  border-radius: 50px;
  text-decoration: none;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(46, 123, 243, 0.4);
  z-index: 1;
}

.ctaButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(46, 123, 243, 0.5);
}

.canvasContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.scene3d {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.testimonialSection {
  padding: 6rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.testimonialContainer {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
}

.testimonialCard {
  position: relative;
  background: rgba(30, 41, 59, 0.2);
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.quoteIcon {
  font-size: 2rem;
  color: var(--accent-primary);
  opacity: 0.5;
  margin-bottom: 1rem;
}

.testimonialText {
  font-size: 1.25rem;
  line-height: 1.7;
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonialAuthor {
  font-weight: 600;
  color: var(--text-primary);
}

.testimonialRole {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.testimonialDots {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  gap: 0.5rem;
}

.testimonialDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--text-secondary);
  opacity: 0.3;
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonialDot.active {
  opacity: 1;
  background: var(--accent-primary);
  transform: scale(1.2);
}

.parallaxBg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0.05;
  z-index: -1;
}

.statsSection {
  padding: 4rem 1rem;
  text-align: center;
  position: relative;
}

.statsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.statItem {
  flex: 1;
  min-width: 200px;
}

.statNumber {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1rem;
}

.statLabel {
  font-size: 1.125rem;
  color: var(--text-secondary);
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .featuresContainer, .faqContainer {
    grid-template-columns: 1fr;
  }

  .stepsContainer {
    flex-direction: column;
  }

  .featureCard, .stepCard, .faqItem {
    padding: 1.5rem;
  }

  .stepNumber {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .ctaTitle {
    font-size: 1.75rem;
  }
}

@media (min-width: 768px) {
  .heroTitle {
    font-size: 5rem;
  }

  .heroDescription {
    font-size: 1.5rem;
  }
}

.availabilityContainer {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
  max-width: 1200px;
}

.availabilityCard {
  position: relative;
  flex: 1;
  min-width: 250px;
  max-width: 380px;
  background: rgba(30, 41, 59, 0.15);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.availabilityCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(67, 190, 214, 0.15);
}

.availabilityIcon {
  color: #43bed6;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  background: rgba(67, 190, 214, 0.08);
  border-radius: 50%;
  padding: 1rem;
  transition: all 0.4s ease;
}

.availabilityCard:hover .availabilityIcon {
  background: rgba(67, 190, 214, 0.15);
  transform: scale(1.1);
}

.availabilityTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.availabilityDescription {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .availabilityContainer {
    flex-direction: column;
    align-items: center;
  }

  .availabilityCard {
    max-width: 100%;
    padding: 2rem;
  }
}