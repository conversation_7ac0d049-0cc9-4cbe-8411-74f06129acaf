<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant Subdomain Diagnostics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .status.running {
            color: #f59e0b;
        }
        
        .status.success {
            color: #10b981;
        }
        
        .status.error {
            color: #ef4444;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
        }
        
        .test-result.pass {
            background: #f0fdf4;
            border-color: #10b981;
        }
        
        .test-result.fail {
            background: #fef2f2;
            border-color: #ef4444;
        }
        
        .test-result.warn {
            background: #fffbeb;
            border-color: #f59e0b;
        }
        
        .test-result.info {
            background: #eff6ff;
            border-color: #3b82f6;
        }
        
        .test-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .test-data {
            background: #f8fafc;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .summary {
            background: #f8fafc;
            padding: 20px;
            border-radius: 6px;
            margin-top: 30px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
        }
        
        .stat.pass .stat-number {
            color: #10b981;
        }
        
        .stat.fail .stat-number {
            color: #ef4444;
        }
        
        .stat.warn .stat-number {
            color: #f59e0b;
        }
        
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .config-info {
            background: #eff6ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Assistant Subdomain Diagnostics</h1>
        
        <div class="config-info">
            <strong>Test Configuration:</strong><br>
            Subdomain: <code>assistant1test</code><br>
            Expected Assistant ID: <code>d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d</code><br>
            Expected Attorney ID: <code>87756a2c-a398-43f2-889a-b8815684df71</code><br>
            Expected Firm: <code>LegalScout</code>
        </div>
        
        <div class="test-controls">
            <button id="runTests" onclick="runDiagnostics()">🚀 Run All Tests</button>
            <button id="clearResults" onclick="clearResults()">🗑️ Clear Results</button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div id="results" class="results"></div>
        
        <div id="summary" class="summary" style="display: none;"></div>
        
        <div id="console" class="console-output" style="display: none;"></div>
    </div>

    <script src="assistant-subdomain-diagnostics.js"></script>
    <script>
        let diagnostics = null;
        let consoleOutput = [];
        
        // Capture console output
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleOutput.push(args.join(' '));
            updateConsoleDisplay();
        };
        
        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('console');
            consoleDiv.innerHTML = consoleOutput.join('\n');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function setStatus(message, className = '') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${className}`;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
            document.getElementById('console').style.display = 'none';
            consoleOutput = [];
            setStatus('');
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            results.results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.status.toLowerCase()}`;
                
                const emoji = {
                    'PASS': '✅',
                    'FAIL': '❌', 
                    'WARN': '⚠️',
                    'INFO': 'ℹ️'
                }[result.status] || '📋';
                
                div.innerHTML = `
                    <div class="test-header">${emoji} ${result.test}: ${result.message}</div>
                    ${result.data ? `<div class="test-data">${JSON.stringify(result.data, null, 2)}</div>` : ''}
                `;
                
                resultsDiv.appendChild(div);
            });
            
            // Show summary
            const summaryDiv = document.getElementById('summary');
            summaryDiv.innerHTML = `
                <h3>📊 Test Summary</h3>
                <div class="summary-stats">
                    <div class="stat pass">
                        <div class="stat-number">${results.passed}</div>
                        <div>Passed</div>
                    </div>
                    <div class="stat fail">
                        <div class="stat-number">${results.failed}</div>
                        <div>Failed</div>
                    </div>
                    <div class="stat warn">
                        <div class="stat-number">${results.warnings}</div>
                        <div>Warnings</div>
                    </div>
                </div>
                <div style="text-align: center; font-weight: bold;">
                    ${results.failed === 0 ? 
                        '🎉 All critical tests passed! The assistant subdomain system should work.' : 
                        '🚨 Critical failures detected. The assistant subdomain system needs fixes.'
                    }
                </div>
            `;
            summaryDiv.style.display = 'block';
            
            // Show console output
            document.getElementById('console').style.display = 'block';
        }
        
        async function runDiagnostics() {
            const runButton = document.getElementById('runTests');
            runButton.disabled = true;
            runButton.textContent = '🔄 Running Tests...';
            
            clearResults();
            setStatus('🔄 Running diagnostics...', 'running');
            
            try {
                diagnostics = new AssistantSubdomainDiagnostics();
                const results = await diagnostics.runAllTests();
                
                displayResults(results);
                
                if (results.failed === 0) {
                    setStatus('✅ All tests completed successfully!', 'success');
                } else {
                    setStatus(`❌ ${results.failed} test(s) failed`, 'error');
                }
            } catch (error) {
                setStatus(`💥 Test runner crashed: ${error.message}`, 'error');
                console.error('Test runner error:', error);
            } finally {
                runButton.disabled = false;
                runButton.textContent = '🚀 Run All Tests';
            }
        }
        
        // Auto-run on page load
        window.addEventListener('load', () => {
            console.log('🧪 Assistant Subdomain Diagnostics Test Runner loaded');
            console.log('Click "Run All Tests" to start diagnostics');
        });
    </script>
</body>
</html>
