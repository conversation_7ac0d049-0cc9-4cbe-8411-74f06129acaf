/**
 * Comprehensive tests for assistant variable propagation
 * Tests: Dropdown → UI State → Vapi Service → API Calls
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AssistantDropdown } from '../src/components/AssistantDropdown';
import { useAssistantStore } from '../src/stores/assistantStore';
import { vapiAssistantService } from '../src/services/vapiAssistantService';
import { vapiMcpService } from '../src/services/vapiMcpService';

// Mock services
vi.mock('../src/services/vapiAssistantService');
vi.mock('../src/services/vapiMcpService');
vi.mock('../src/stores/assistantStore');

describe('Assistant Variable Propagation Tests', () => {
  const mockAssistants = [
    {
      id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
      name: 'Damon Legal Assistant',
      model: { provider: 'openai', model: 'gpt-4' },
      voice: { provider: '11labs', voiceId: 'echo' }
    },
    {
      id: 'test-assistant-2',
      name: 'Test Assistant 2',
      model: { provider: 'openai', model: 'gpt-3.5-turbo' },
      voice: { provider: '11labs', voiceId: 'cho' }
    }
  ];

  const mockStore = {
    currentAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
    assistants: mockAssistants,
    setCurrentAssistantId: vi.fn(),
    loadAssistants: vi.fn(),
    getCurrentAssistant: vi.fn(() => mockAssistants[0])
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useAssistantStore.mockReturnValue(mockStore);
    vapiAssistantService.getAllAssistants.mockResolvedValue(mockAssistants);
    vapiMcpService.getAssistant.mockResolvedValue(mockAssistants[0]);
  });

  describe('1. Dropdown Population Test', () => {
    it('should populate dropdown with all available assistants', async () => {
      render(<AssistantDropdown />);
      
      await waitFor(() => {
        expect(screen.getByText('Damon Legal Assistant')).toBeInTheDocument();
        expect(screen.getByText('Test Assistant 2')).toBeInTheDocument();
      });
    });

    it('should show current assistant as selected', async () => {
      render(<AssistantDropdown />);
      
      await waitFor(() => {
        const selectedOption = screen.getByDisplayValue('Damon Legal Assistant');
        expect(selectedOption).toBeInTheDocument();
      });
    });
  });

  describe('2. Assistant Selection Propagation Test', () => {
    it('should update store when assistant is selected', async () => {
      render(<AssistantDropdown />);
      
      const dropdown = await screen.findByRole('combobox');
      fireEvent.change(dropdown, { target: { value: 'test-assistant-2' } });
      
      await waitFor(() => {
        expect(mockStore.setCurrentAssistantId).toHaveBeenCalledWith('test-assistant-2');
      });
    });

    it('should fetch assistant details when selection changes', async () => {
      render(<AssistantDropdown />);
      
      const dropdown = await screen.findByRole('combobox');
      fireEvent.change(dropdown, { target: { value: 'test-assistant-2' } });
      
      await waitFor(() => {
        expect(vapiMcpService.getAssistant).toHaveBeenCalledWith('test-assistant-2');
      });
    });
  });

  describe('3. UI State Propagation Test', () => {
    it('should propagate assistant data to UI components', async () => {
      const TestComponent = () => {
        const { getCurrentAssistant } = useAssistantStore();
        const assistant = getCurrentAssistant();
        
        return (
          <div>
            <span data-testid="assistant-name">{assistant?.name}</span>
            <span data-testid="assistant-model">{assistant?.model?.model}</span>
            <span data-testid="assistant-voice">{assistant?.voice?.voiceId}</span>
          </div>
        );
      };

      render(<TestComponent />);
      
      expect(screen.getByTestId('assistant-name')).toHaveTextContent('Damon Legal Assistant');
      expect(screen.getByTestId('assistant-model')).toHaveTextContent('gpt-4');
      expect(screen.getByTestId('assistant-voice')).toHaveTextContent('echo');
    });
  });

  describe('4. Vapi Service Integration Test', () => {
    it('should call Vapi service with correct assistant ID', async () => {
      const testAssistantId = 'test-assistant-2';
      
      // Simulate assistant selection
      await vapiAssistantService.switchAssistant(testAssistantId);
      
      expect(vapiMcpService.getAssistant).toHaveBeenCalledWith(testAssistantId);
    });

    it('should update assistant configuration via Vapi', async () => {
      const updateData = {
        name: 'Updated Assistant Name',
        model: { provider: 'openai', model: 'gpt-4-turbo' }
      };
      
      vapiMcpService.updateAssistant = vi.fn().mockResolvedValue({ success: true });
      
      await vapiAssistantService.updateAssistant('cd0b44b7-397e-410d-8835-ce9c3ba584b2', updateData);
      
      expect(vapiMcpService.updateAssistant).toHaveBeenCalledWith(
        'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
        updateData
      );
    });
  });

  describe('5. End-to-End Propagation Test', () => {
    it('should propagate assistant change from dropdown to Vapi call', async () => {
      // Mock call creation
      vapiMcpService.createCall = vi.fn().mockResolvedValue({
        id: 'test-call-id',
        assistantId: 'test-assistant-2'
      });

      // Render dropdown and change selection
      render(<AssistantDropdown />);
      const dropdown = await screen.findByRole('combobox');
      fireEvent.change(dropdown, { target: { value: 'test-assistant-2' } });

      // Wait for store update
      await waitFor(() => {
        expect(mockStore.setCurrentAssistantId).toHaveBeenCalledWith('test-assistant-2');
      });

      // Simulate call creation with new assistant
      await vapiAssistantService.createCall({
        assistantId: 'test-assistant-2',
        customer: { phoneNumber: '+1234567890' }
      });

      expect(vapiMcpService.createCall).toHaveBeenCalledWith({
        assistantId: 'test-assistant-2',
        customer: { phoneNumber: '+1234567890' }
      });
    });
  });

  describe('6. Error Handling Tests', () => {
    it('should handle assistant loading errors gracefully', async () => {
      vapiAssistantService.getAllAssistants.mockRejectedValue(new Error('API Error'));
      
      render(<AssistantDropdown />);
      
      await waitFor(() => {
        expect(screen.getByText(/error loading assistants/i)).toBeInTheDocument();
      });
    });

    it('should handle assistant switching errors', async () => {
      vapiMcpService.getAssistant.mockRejectedValue(new Error('Assistant not found'));
      
      const result = await vapiAssistantService.switchAssistant('invalid-id');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Assistant not found');
    });
  });

  describe('7. UI Configuration Persistence Test', () => {
    it('should save UI config when assistant changes', async () => {
      const mockSaveConfig = vi.fn();
      vi.mock('../src/services/supabaseService', () => ({
        saveAssistantUIConfig: mockSaveConfig
      }));

      render(<AssistantDropdown />);
      const dropdown = await screen.findByRole('combobox');
      fireEvent.change(dropdown, { target: { value: 'test-assistant-2' } });

      await waitFor(() => {
        expect(mockSaveConfig).toHaveBeenCalledWith(
          'test-assistant-2',
          expect.any(Object)
        );
      });
    });
  });
});
