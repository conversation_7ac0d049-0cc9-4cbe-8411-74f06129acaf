/* Call Control Page Styles */

.call-control-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  background-color: #f8f9fa;
  color: #333;
  font-family: 'Inter', sans-serif;
}

/* Dark theme adjustments */
[data-theme="dark"] .call-control-container {
  background-color: #121212;
  color: #f8f9fa;
}

/* Loading and Error States */
.call-control-container.loading,
.call-control-container.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Header */
.call-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.call-control-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.call-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.in-progress {
  background-color: #28a745;
  box-shadow: 0 0 0 rgba(40, 167, 69, 0.4);
  animation: pulse 2s infinite;
}

.status-indicator.completed,
.status-indicator.ended {
  background-color: #6c757d;
}

.status-indicator.unknown {
  background-color: #ffc107;
}

.status-indicator.error {
  background-color: #dc3545;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* Call Info */
.call-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.attorney-info h2 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
}

.attorney-info p {
  margin: 0;
  color: #6c757d;
}

.call-details p {
  margin: 5px 0;
  font-size: 14px;
}

/* Dark theme adjustments for call info */
[data-theme="dark"] .call-info {
  background-color: #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .attorney-info h2 {
  color: #f8f9fa;
}

[data-theme="dark"] .attorney-info p {
  color: #adb5bd;
}

[data-theme="dark"] .call-details p {
  color: #e9ecef;
}

/* Call Visualization */
.call-visualization {
  height: 100px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Transcript Container */
.transcript-container {
  flex: 1;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.transcript-container h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.transcript-messages {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.transcript-message {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  max-width: 80%;
}

.transcript-message.assistant {
  background-color: #e9f5ff;
  align-self: flex-start;
  margin-right: auto;
}

.transcript-message.user {
  background-color: #f0f0f0;
  align-self: flex-end;
  margin-left: auto;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.message-role {
  font-weight: 600;
}

.message-time {
  color: #6c757d;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
}

/* Dark theme adjustments for transcript container */
[data-theme="dark"] .transcript-container {
  background-color: #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .transcript-container h3 {
  color: #f8f9fa;
}

[data-theme="dark"] .transcript-message.assistant {
  background-color: #0d3a58;
  color: #e9ecef;
}

[data-theme="dark"] .transcript-message.user {
  background-color: #2c2c2c;
  color: #e9ecef;
}

[data-theme="dark"] .message-role {
  color: #f8f9fa;
}

[data-theme="dark"] .message-time {
  color: #adb5bd;
}

[data-theme="dark"] .message-content {
  color: #e9ecef;
}

/* Control Panel */
.control-panel {
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.message-input {
  display: flex;
  margin-bottom: 15px;
}

.message-input input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
}

.message-input button {
  padding: 10px 15px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.message-input button:hover {
  background-color: #0069d9;
}

.message-input button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
}

.take-over-button,
.end-call-button {
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.take-over-button {
  background-color: #17a2b8;
  color: #fff;
}

.take-over-button:hover {
  background-color: #138496;
}

.end-call-button {
  background-color: #dc3545;
  color: #fff;
}

.end-call-button:hover {
  background-color: #c82333;
}

.take-over-button:disabled,
.end-call-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Dark theme adjustments for control panel */
[data-theme="dark"] .control-panel {
  background-color: #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .message-input input {
  background-color: #2c2c2c;
  border-color: #444;
  color: #e9ecef;
}

[data-theme="dark"] .message-input button {
  background-color: #0062cc;
}

[data-theme="dark"] .message-input button:hover {
  background-color: #0056b3;
}

[data-theme="dark"] .take-over-button {
  background-color: #138496;
}

[data-theme="dark"] .take-over-button:hover {
  background-color: #0f6674;
}

[data-theme="dark"] .end-call-button {
  background-color: #c82333;
}

[data-theme="dark"] .end-call-button:hover {
  background-color: #b21f2d;
}

[data-theme="dark"] .take-over-button:disabled,
[data-theme="dark"] .end-call-button:disabled,
[data-theme="dark"] .message-input button:disabled {
  background-color: #495057;
  color: #adb5bd;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .call-info {
    flex-direction: column;
  }

  .attorney-info {
    margin-bottom: 15px;
  }

  .transcript-message {
    max-width: 90%;
  }

  .control-buttons {
    flex-direction: column;
  }

  .take-over-button {
    margin-bottom: 10px;
  }
}
