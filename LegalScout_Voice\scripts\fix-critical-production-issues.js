/**
 * Fix Critical Production Issues
 * 
 * This script addresses the critical issues preventing attorneys from being
 * immediately up and running with their voice assistant.
 */

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

async function testVapiApiAccess() {
  console.log('🔍 Testing Vapi API Access...');
  
  try {
    // Test with different headers to see what works
    const testConfigs = [
      {
        name: 'Standard Bearer Token',
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      },
      {
        name: 'API Key Header',
        headers: {
          'X-API-Key': VAPI_PRIVATE_KEY,
          'Content-Type': 'application/json'
        }
      },
      {
        name: 'Vapi-Token Header',
        headers: {
          'Vapi-Token': VAPI_PRIVATE_KEY,
          'Content-Type': 'application/json'
        }
      }
    ];

    for (const config of testConfigs) {
      console.log(`\n📡 Testing: ${config.name}`);
      
      try {
        const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
          method: 'GET',
          headers: config.headers
        });

        console.log(`Status: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`✅ SUCCESS with ${config.name}`);
          console.log(`Assistant Name: ${data.name}`);
          console.log(`First Message: ${data.firstMessage || 'MISSING'}`);
          return { success: true, config, data };
        } else {
          const errorText = await response.text();
          console.log(`❌ FAILED: ${errorText}`);
        }
      } catch (error) {
        console.log(`❌ ERROR: ${error.message}`);
      }
    }

    return { success: false };
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error };
  }
}

async function testVapiKeyValidity() {
  console.log('\n🔑 Testing Vapi Key Validity...');
  
  try {
    // Test listing assistants (simpler endpoint)
    const response = await fetch('https://api.vapi.ai/assistant', {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`List Assistants Status: ${response.status}`);
    
    if (response.ok) {
      const assistants = await response.json();
      console.log(`✅ Key is valid - Found ${assistants.length} assistants`);
      
      // Check if our specific assistant exists
      const ourAssistant = assistants.find(a => a.id === ASSISTANT_ID);
      if (ourAssistant) {
        console.log(`✅ Our assistant found: ${ourAssistant.name}`);
        return { valid: true, assistantExists: true, assistant: ourAssistant };
      } else {
        console.log(`⚠️ Our assistant ID not found in list`);
        console.log(`Available assistants:`, assistants.map(a => ({ id: a.id, name: a.name })));
        return { valid: true, assistantExists: false, assistants };
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Key invalid or expired: ${errorText}`);
      return { valid: false, error: errorText };
    }
  } catch (error) {
    console.error('❌ Key test failed:', error);
    return { valid: false, error: error.message };
  }
}

async function generateWorkingAssistantLink() {
  console.log('\n🔗 Generating Working Assistant Link...');
  
  try {
    const keyTest = await testVapiKeyValidity();
    
    if (!keyTest.valid) {
      console.log('❌ Cannot generate link - API key invalid');
      return null;
    }

    if (!keyTest.assistantExists) {
      console.log('⚠️ Assistant not found, but key is valid');
      console.log('Available assistants for link generation:');
      keyTest.assistants.forEach(assistant => {
        console.log(`- ${assistant.name} (${assistant.id})`);
      });
      
      if (keyTest.assistants.length > 0) {
        const firstAssistant = keyTest.assistants[0];
        console.log(`\n🔗 Using first available assistant: ${firstAssistant.name}`);
        return {
          assistantId: firstAssistant.id,
          link: `https://legalscout.net/simple-preview?assistantId=${firstAssistant.id}`,
          name: firstAssistant.name
        };
      }
    } else {
      console.log(`✅ Generating link for: ${keyTest.assistant.name}`);
      return {
        assistantId: ASSISTANT_ID,
        link: `https://legalscout.net/simple-preview?assistantId=${ASSISTANT_ID}`,
        name: keyTest.assistant.name
      };
    }

    return null;
  } catch (error) {
    console.error('❌ Link generation failed:', error);
    return null;
  }
}

async function checkCurrentAttorneySetup() {
  console.log('\n👤 Checking Current Attorney Setup...');
  
  // Check what's needed for immediate attorney onboarding
  const requirements = [
    'Valid Vapi API key',
    'Working assistant ID',
    'Functional voice interface',
    'Attorney profile in database',
    'Subdomain routing'
  ];

  console.log('📋 Requirements for immediate attorney onboarding:');
  requirements.forEach((req, index) => {
    console.log(`${index + 1}. ${req}`);
  });

  return requirements;
}

async function main() {
  console.log('🚨 CRITICAL PRODUCTION ISSUES DIAGNOSIS\n');
  
  // Test 1: Vapi API Access
  const apiTest = await testVapiApiAccess();
  
  // Test 2: Key Validity
  const keyTest = await testVapiKeyValidity();
  
  // Test 3: Generate Working Link
  const linkResult = await generateWorkingAssistantLink();
  
  // Test 4: Check Attorney Setup Requirements
  const requirements = await checkCurrentAttorneySetup();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DIAGNOSIS SUMMARY');
  console.log('='.repeat(60));
  
  console.log('\n🔑 API Key Status:');
  if (keyTest.valid) {
    console.log('✅ Vapi API key is VALID');
  } else {
    console.log('❌ Vapi API key is INVALID or EXPIRED');
    console.log('🔧 FIX: Update VITE_VAPI_PRIVATE_KEY in environment variables');
  }
  
  console.log('\n🤖 Assistant Status:');
  if (keyTest.assistantExists) {
    console.log(`✅ Assistant ${ASSISTANT_ID} exists and accessible`);
  } else if (keyTest.valid) {
    console.log(`⚠️ Assistant ${ASSISTANT_ID} not found, but other assistants available`);
  } else {
    console.log('❌ Cannot access assistants due to API key issues');
  }
  
  console.log('\n🔗 Attorney Link Status:');
  if (linkResult) {
    console.log('✅ Working assistant link available:');
    console.log(`   ${linkResult.link}`);
    console.log(`   Assistant: ${linkResult.name}`);
    console.log('\n🎉 ATTORNEY CAN BE UP AND RUNNING IMMEDIATELY!');
    console.log('   Just copy and paste the link above');
  } else {
    console.log('❌ No working assistant link available');
    console.log('🔧 FIX REQUIRED: Resolve API key or assistant issues first');
  }
  
  console.log('\n💡 Next Steps:');
  if (linkResult) {
    console.log('1. ✅ Attorney can use the working link immediately');
    console.log('2. Fix the 401 errors in production for dashboard access');
    console.log('3. Resolve CORS issues for MCP connections');
    console.log('4. Stop the iframe manager infinite loop');
  } else {
    console.log('1. ❌ Fix Vapi API key authentication');
    console.log('2. Verify assistant exists and is accessible');
    console.log('3. Test voice interface functionality');
    console.log('4. Then attorney can be immediately up and running');
  }
}

// Run the diagnosis
main().catch(error => {
  console.error('💥 Diagnosis failed:', error);
  process.exit(1);
});
