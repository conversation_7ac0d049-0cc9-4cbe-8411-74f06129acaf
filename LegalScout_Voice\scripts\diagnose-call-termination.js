#!/usr/bin/env node

/**
 * Diagnose Call Termination Issues
 * 
 * This script checks common causes of Vapi call termination:
 * 1. Assistant configuration issues
 * 2. Voice provider/model mismatches
 * 3. API key problems
 * 4. Model configuration errors
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

console.log('🔍 Diagnosing Call Termination Issues');
console.log('=====================================\n');

async function checkAssistantConfiguration() {
  console.log('📋 Checking Assistant Configuration...');
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to fetch assistant: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const assistant = await response.json();
    
    console.log('✅ Assistant found successfully');
    console.log(`   Name: ${assistant.name}`);
    console.log(`   ID: ${assistant.id}`);
    
    // Check voice configuration
    console.log('\n🎤 Voice Configuration:');
    if (assistant.voice) {
      console.log(`   Provider: ${assistant.voice.provider || 'Not set'}`);
      console.log(`   Voice ID: ${assistant.voice.voiceId || 'Not set'}`);
      console.log(`   Model: ${assistant.voice.model || 'Not set'}`);
    } else {
      console.log('   ❌ No voice configuration found!');
    }
    
    // Check model configuration
    console.log('\n🤖 Model Configuration:');
    if (assistant.model) {
      console.log(`   Provider: ${assistant.model.provider || 'Not set'}`);
      console.log(`   Model: ${assistant.model.model || 'Not set'}`);
      console.log(`   Temperature: ${assistant.model.temperature || 'Not set'}`);
      console.log(`   Max Tokens: ${assistant.model.maxTokens || 'Not set'}`);
    } else {
      console.log('   ❌ No model configuration found!');
    }
    
    // Check first message
    console.log('\n💬 First Message:');
    if (assistant.firstMessage) {
      console.log(`   "${assistant.firstMessage}"`);
    } else {
      console.log('   ❌ No first message configured!');
    }
    
    // Check system message
    console.log('\n📝 System Message:');
    if (assistant.model && assistant.model.messages && assistant.model.messages.length > 0) {
      const systemMessage = assistant.model.messages.find(m => m.role === 'system');
      if (systemMessage) {
        console.log(`   "${systemMessage.content.substring(0, 100)}..."`);
      } else {
        console.log('   ❌ No system message found!');
      }
    } else {
      console.log('   ❌ No messages configured!');
    }
    
    return assistant;
    
  } catch (error) {
    console.log(`❌ Error checking assistant: ${error.message}`);
    return null;
  }
}

async function checkCommonIssues(assistant) {
  console.log('\n🔍 Checking Common Call Termination Issues...');
  
  const issues = [];
  
  // Issue 1: Missing or invalid voice configuration
  if (!assistant.voice) {
    issues.push('❌ No voice configuration - calls will fail');
  } else {
    if (!assistant.voice.provider) {
      issues.push('❌ Voice provider not set');
    }
    if (!assistant.voice.voiceId) {
      issues.push('❌ Voice ID not set');
    }
    
    // Check for common voice provider issues
    if (assistant.voice.provider === '11labs' && !assistant.voice.voiceId) {
      issues.push('❌ 11Labs requires a specific voice ID');
    }
    
    if (assistant.voice.provider === 'openai' && !assistant.voice.voiceId) {
      issues.push('❌ OpenAI requires a voice ID (alloy, echo, fable, onyx, nova, shimmer)');
    }
  }
  
  // Issue 2: Missing or invalid model configuration
  if (!assistant.model) {
    issues.push('❌ No model configuration - calls will fail');
  } else {
    if (!assistant.model.provider) {
      issues.push('❌ Model provider not set');
    }
    if (!assistant.model.model) {
      issues.push('❌ Model not specified');
    }
  }
  
  // Issue 3: Missing first message
  if (!assistant.firstMessage) {
    issues.push('⚠️  No first message - assistant may not speak first');
  }
  
  // Issue 4: Missing system message
  if (!assistant.model || !assistant.model.messages || assistant.model.messages.length === 0) {
    issues.push('⚠️  No system messages - assistant may not know how to behave');
  }
  
  // Issue 5: Check for conflicting configurations
  if (assistant.voice && assistant.voice.provider === 'openai' && assistant.voice.voiceId === 'sarah') {
    issues.push('❌ OpenAI does not have a "sarah" voice - use alloy, echo, fable, onyx, nova, or shimmer');
  }
  
  if (issues.length === 0) {
    console.log('✅ No obvious configuration issues found');
  } else {
    console.log(`Found ${issues.length} potential issues:`);
    issues.forEach(issue => console.log(`   ${issue}`));
  }
  
  return issues;
}

async function suggestFixes(issues, assistant) {
  console.log('\n🔧 Suggested Fixes:');
  
  if (issues.length === 0) {
    console.log('✅ Configuration looks good. Call termination might be due to:');
    console.log('   - Network connectivity issues');
    console.log('   - Temporary Vapi service issues');
    console.log('   - Browser microphone permissions');
    console.log('   - Audio device conflicts');
    return;
  }
  
  console.log('Apply these fixes to resolve call termination:');
  
  // Voice fixes
  if (issues.some(i => i.includes('voice'))) {
    console.log('\n🎤 Voice Configuration Fixes:');
    console.log('   1. Set voice provider to "openai" or "11labs"');
    console.log('   2. For OpenAI: use voice ID "alloy", "echo", "fable", "onyx", "nova", or "shimmer"');
    console.log('   3. For 11Labs: use a valid 11Labs voice ID');
  }
  
  // Model fixes
  if (issues.some(i => i.includes('model') || i.includes('Model'))) {
    console.log('\n🤖 Model Configuration Fixes:');
    console.log('   1. Set model provider to "openai"');
    console.log('   2. Set model to "gpt-4" or "gpt-3.5-turbo"');
    console.log('   3. Add system message with instructions');
  }
  
  // Message fixes
  if (issues.some(i => i.includes('message'))) {
    console.log('\n💬 Message Configuration Fixes:');
    console.log('   1. Add a first message like "Hello! How can I help you today?"');
    console.log('   2. Add system message with assistant instructions');
  }
}

async function createFixedAssistantConfig() {
  console.log('\n🛠️  Creating Fixed Assistant Configuration...');
  
  const fixedConfig = {
    name: "LegalScout Assistant",
    firstMessage: "Hello! I'm Scout from LegalScout. How can I help you today?",
    voice: {
      provider: "openai",
      voiceId: "alloy"
    },
    model: {
      provider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 500,
      messages: [
        {
          role: "system",
          content: "You are Scout, a helpful legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with appropriate legal services. Be professional, empathetic, and helpful. Ask relevant questions to understand their situation."
        }
      ]
    }
  };
  
  console.log('📋 Recommended configuration:');
  console.log(JSON.stringify(fixedConfig, null, 2));
  
  return fixedConfig;
}

async function runDiagnosis() {
  console.log('🚀 Starting call termination diagnosis...\n');
  
  const assistant = await checkAssistantConfiguration();
  
  if (!assistant) {
    console.log('❌ Cannot proceed without assistant data');
    return;
  }
  
  const issues = await checkCommonIssues(assistant);
  await suggestFixes(issues, assistant);
  
  if (issues.length > 0) {
    await createFixedAssistantConfig();
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Apply the suggested fixes to your assistant configuration');
  console.log('2. Test the call again');
  console.log('3. Check browser console for any additional errors');
  console.log('4. Ensure microphone permissions are granted');
  
  console.log('\n🎉 Diagnosis complete!');
}

runDiagnosis().catch(console.error);
