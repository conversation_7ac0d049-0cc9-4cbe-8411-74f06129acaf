/**
 * Tests for Enhanced Vapi Assistant Manager
 */

import { enhancedVapiAssistantManager } from '../services/EnhancedVapiAssistantManager';
import { supabase } from '../lib/supabase';

// Mock supabase
jest.mock('../lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    single: jest.fn()
  }
}));

// Mock MCP client
jest.mock('@modelcontextprotocol/sdk/client/index.js', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    callTool: jest.fn().mockResolvedValue({ content: { id: 'test-assistant-id' } })
  }))
}));

// Mock SSE transport
jest.mock('@modelcontextprotocol/sdk/client/sse.js', () => ({
  SSEClientTransport: jest.fn().mockImplementation(() => ({}))
}));

// Mock fetch
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ id: 'test-assistant-id' })
  })
);

describe('EnhancedVapiAssistantManager', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock supabase response
    supabase.single.mockResolvedValue({
      data: {
        id: 'test-attorney-id',
        vapi_assistant_id: 'test-assistant-id',
        updated_at: new Date().toISOString()
      },
      error: null
    });
  });
  
  describe('initialize', () => {
    it('should initialize with direct API key', async () => {
      const result = await enhancedVapiAssistantManager.initialize({
        directApiKey: 'test-api-key'
      });
      
      expect(result).toBe(true);
      expect(enhancedVapiAssistantManager.directApiKey).toBe('test-api-key');
      expect(enhancedVapiAssistantManager.useDirect).toBe(true);
    });
    
    it('should initialize with MCP', async () => {
      const result = await enhancedVapiAssistantManager.initialize({
        mcpUrl: 'https://test-mcp-url.com'
      });
      
      expect(result).toBe(true);
      expect(enhancedVapiAssistantManager.mcpConnected).toBe(true);
    });
  });
  
  describe('ensureAssistant', () => {
    it('should return attorney if assistant exists', async () => {
      // Mock verifyAssistant to return true
      enhancedVapiAssistantManager.verifyAssistant = jest.fn().mockResolvedValue(true);
      
      const attorney = {
        id: 'test-attorney-id',
        vapi_assistant_id: 'test-assistant-id'
      };
      
      const result = await enhancedVapiAssistantManager.ensureAssistant(attorney);
      
      expect(result).toEqual(attorney);
      expect(enhancedVapiAssistantManager.verifyAssistant).toHaveBeenCalledWith('test-assistant-id');
    });
    
    it('should create a new assistant if assistant does not exist', async () => {
      // Mock verifyAssistant to return false
      enhancedVapiAssistantManager.verifyAssistant = jest.fn().mockResolvedValue(false);
      
      // Mock createAssistant
      enhancedVapiAssistantManager.createAssistant = jest.fn().mockResolvedValue({
        id: 'new-assistant-id'
      });
      
      // Mock updateAttorneyAssistantId
      enhancedVapiAssistantManager.updateAttorneyAssistantId = jest.fn().mockResolvedValue({
        id: 'test-attorney-id',
        vapi_assistant_id: 'new-assistant-id'
      });
      
      const attorney = {
        id: 'test-attorney-id',
        vapi_assistant_id: 'test-assistant-id'
      };
      
      const result = await enhancedVapiAssistantManager.ensureAssistant(attorney);
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        vapi_assistant_id: 'new-assistant-id'
      });
      expect(enhancedVapiAssistantManager.verifyAssistant).toHaveBeenCalledWith('test-assistant-id');
      expect(enhancedVapiAssistantManager.createAssistant).toHaveBeenCalledWith(attorney);
      expect(enhancedVapiAssistantManager.updateAttorneyAssistantId).toHaveBeenCalledWith('test-attorney-id', 'new-assistant-id');
    });
    
    it('should create a new assistant if attorney has no assistant ID', async () => {
      // Mock createAssistant
      enhancedVapiAssistantManager.createAssistant = jest.fn().mockResolvedValue({
        id: 'new-assistant-id'
      });
      
      // Mock updateAttorneyAssistantId
      enhancedVapiAssistantManager.updateAttorneyAssistantId = jest.fn().mockResolvedValue({
        id: 'test-attorney-id',
        vapi_assistant_id: 'new-assistant-id'
      });
      
      const attorney = {
        id: 'test-attorney-id'
      };
      
      const result = await enhancedVapiAssistantManager.ensureAssistant(attorney);
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        vapi_assistant_id: 'new-assistant-id'
      });
      expect(enhancedVapiAssistantManager.createAssistant).toHaveBeenCalledWith(attorney);
      expect(enhancedVapiAssistantManager.updateAttorneyAssistantId).toHaveBeenCalledWith('test-attorney-id', 'new-assistant-id');
    });
  });
  
  describe('verifyAssistant', () => {
    it('should return true if assistant exists', async () => {
      // Initialize with direct API key
      await enhancedVapiAssistantManager.initialize({
        directApiKey: 'test-api-key'
      });
      
      const result = await enhancedVapiAssistantManager.verifyAssistant('test-assistant-id');
      
      expect(result).toBe(true);
      expect(fetch).toHaveBeenCalledWith('https://api.vapi.ai/assistants/test-assistant-id', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        }
      });
    });
    
    it('should return false if assistant does not exist', async () => {
      // Initialize with direct API key
      await enhancedVapiAssistantManager.initialize({
        directApiKey: 'test-api-key'
      });
      
      // Mock fetch to return 404
      global.fetch.mockImplementationOnce(() =>
        Promise.resolve({
          ok: false,
          status: 404
        })
      );
      
      const result = await enhancedVapiAssistantManager.verifyAssistant('non-existent-assistant-id');
      
      expect(result).toBe(false);
    });
  });
  
  describe('createAssistant', () => {
    it('should create a new assistant', async () => {
      // Initialize with direct API key
      await enhancedVapiAssistantManager.initialize({
        directApiKey: 'test-api-key'
      });
      
      const attorney = {
        id: 'test-attorney-id',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        ai_model: 'gpt-4o'
      };
      
      const result = await enhancedVapiAssistantManager.createAssistant(attorney);
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(fetch).toHaveBeenCalledWith('https://api.vapi.ai/assistants', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        },
        body: expect.any(String)
      });
      
      // Parse the request body
      const requestBody = JSON.parse(fetch.mock.calls[0][1].body);
      
      // Check that the request body contains the expected values
      expect(requestBody.name).toBe('Test Law Firm');
      expect(requestBody.firstMessage).toBe('Welcome to Test Law Firm');
      expect(requestBody.instructions).toBe('You are a legal assistant for Test Law Firm');
      expect(requestBody.voice.provider).toBe('playht');
      expect(requestBody.voice.voiceId).toBe('ranger');
      expect(requestBody.llm.model).toBe('gpt-4o');
    });
  });
  
  describe('updateAttorneyAssistantId', () => {
    it('should update attorney assistant ID', async () => {
      const result = await enhancedVapiAssistantManager.updateAttorneyAssistantId('test-attorney-id', 'new-assistant-id');
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        vapi_assistant_id: 'test-assistant-id',
        updated_at: expect.any(String)
      });
      expect(supabase.from).toHaveBeenCalledWith('attorneys');
      expect(supabase.update).toHaveBeenCalledWith({
        vapi_assistant_id: 'new-assistant-id',
        updated_at: expect.any(String)
      });
      expect(supabase.eq).toHaveBeenCalledWith('id', 'test-attorney-id');
    });
  });
});
