/**
 * System Status Checker
 * 
 * This utility checks the current state of all system components
 * and provides a comprehensive status report for debugging.
 */

import { supabase } from '../lib/supabase-fixed';
import { getVapiApiKey } from '../config/vapiConfig';

/**
 * Check the authentication status
 */
export const checkAuthStatus = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      return {
        status: 'error',
        message: 'Authentication error',
        error: error.message
      };
    }
    
    if (!user) {
      return {
        status: 'unauthenticated',
        message: 'No authenticated user'
      };
    }
    
    return {
      status: 'authenticated',
      message: 'User authenticated successfully',
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      }
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Failed to check auth status',
      error: error.message
    };
  }
};

/**
 * Check the attorney data in Supabase
 */
export const checkAttorneyData = async () => {
  try {
    const authStatus = await checkAuthStatus();
    
    if (authStatus.status !== 'authenticated') {
      return {
        status: 'error',
        message: 'Cannot check attorney data - user not authenticated',
        dependency: authStatus
      };
    }
    
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', authStatus.user.email)
      .single();
    
    if (error) {
      return {
        status: 'error',
        message: 'Error fetching attorney data',
        error: error.message
      };
    }
    
    if (!attorney) {
      return {
        status: 'not_found',
        message: 'No attorney record found for authenticated user'
      };
    }
    
    return {
      status: 'found',
      message: 'Attorney data found',
      attorney: {
        id: attorney.id,
        email: attorney.email,
        firm_name: attorney.firm_name,
        vapi_assistant_id: attorney.vapi_assistant_id,
        vapi_instructions: attorney.vapi_instructions,
        subdomain: attorney.subdomain
      }
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Failed to check attorney data',
      error: error.message
    };
  }
};

/**
 * Check Vapi API configuration
 */
export const checkVapiConfig = () => {
  try {
    const publicKey = getVapiApiKey('client');
    const serverKey = getVapiApiKey('server');
    
    return {
      status: 'configured',
      message: 'Vapi configuration loaded',
      config: {
        hasPublicKey: !!publicKey,
        hasServerKey: !!serverKey,
        publicKeyPrefix: publicKey ? `${publicKey.substring(0, 8)}...` : 'none',
        serverKeyPrefix: serverKey ? `${serverKey.substring(0, 8)}...` : 'none'
      }
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Failed to check Vapi configuration',
      error: error.message
    };
  }
};

/**
 * Check Vapi assistant accessibility
 */
export const checkVapiAssistant = async (assistantId) => {
  if (!assistantId) {
    return {
      status: 'no_id',
      message: 'No assistant ID provided'
    };
  }
  
  try {
    const serverKey = getVapiApiKey('server');
    
    if (!serverKey) {
      return {
        status: 'no_key',
        message: 'No Vapi server key available'
      };
    }
    
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${serverKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 401) {
      return {
        status: 'unauthorized',
        message: 'Vapi API key unauthorized',
        keyUsed: `${serverKey.substring(0, 8)}...`
      };
    }
    
    if (response.status === 404) {
      return {
        status: 'not_found',
        message: 'Assistant not found in Vapi',
        assistantId
      };
    }
    
    if (!response.ok) {
      return {
        status: 'api_error',
        message: `Vapi API error: ${response.status} ${response.statusText}`,
        status_code: response.status
      };
    }
    
    const assistant = await response.json();
    
    return {
      status: 'accessible',
      message: 'Assistant accessible via Vapi API',
      assistant: {
        id: assistant.id,
        name: assistant.name,
        createdAt: assistant.createdAt,
        updatedAt: assistant.updatedAt
      }
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Failed to check Vapi assistant',
      error: error.message
    };
  }
};

/**
 * Run comprehensive system status check
 */
export const runSystemStatusCheck = async () => {
  console.log('🔍 Running comprehensive system status check...');
  
  const results = {
    timestamp: new Date().toISOString(),
    checks: {}
  };
  
  // Check authentication
  console.log('Checking authentication...');
  results.checks.auth = await checkAuthStatus();
  
  // Check attorney data
  console.log('Checking attorney data...');
  results.checks.attorney = await checkAttorneyData();
  
  // Check Vapi configuration
  console.log('Checking Vapi configuration...');
  results.checks.vapiConfig = checkVapiConfig();
  
  // Check Vapi assistant if we have an ID
  if (results.checks.attorney.status === 'found' && results.checks.attorney.attorney.vapi_assistant_id) {
    console.log('Checking Vapi assistant...');
    results.checks.vapiAssistant = await checkVapiAssistant(results.checks.attorney.attorney.vapi_assistant_id);
  }
  
  // Determine overall status
  const hasErrors = Object.values(results.checks).some(check => check.status === 'error');
  const hasWarnings = Object.values(results.checks).some(check => 
    ['unauthorized', 'not_found', 'no_key', 'unauthenticated'].includes(check.status)
  );
  
  results.overall = {
    status: hasErrors ? 'error' : hasWarnings ? 'warning' : 'healthy',
    message: hasErrors ? 'System has errors' : hasWarnings ? 'System has warnings' : 'System is healthy'
  };
  
  console.log('✅ System status check complete:', results.overall);
  return results;
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.systemStatusChecker = {
    checkAuthStatus,
    checkAttorneyData,
    checkVapiConfig,
    checkVapiAssistant,
    runSystemStatusCheck
  };
}
