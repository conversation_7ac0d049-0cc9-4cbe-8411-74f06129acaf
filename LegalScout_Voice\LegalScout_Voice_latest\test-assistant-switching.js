/**
 * Test assistant switching functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;

console.log('🔄 Testing Assistant Switching');
console.log('==============================');

async function testAssistantSwitching() {
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    
    // Test data
    const attorneyId = '87756a2c-a398-43f2-889a-b8815684df71';
    const testAssistantId = '4e899c0a-b435-45c4-abcd-8abd3ff13ec3'; // LegalScout Assistant - Damon
    
    console.log('👤 Attorney ID:', attorneyId);
    console.log('🤖 Test Assistant ID:', testAssistantId);
    
    // Step 1: Test updating current_assistant_id in attorneys table
    console.log('\n📝 Step 1: Updating current_assistant_id...');
    const { data: updateData, error: updateError } = await supabase
      .from('attorneys')
      .update({ 
        current_assistant_id: testAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorneyId)
      .select();

    if (updateError) {
      console.log('❌ Failed to update current_assistant_id:', updateError.message);
      return;
    }
    
    console.log('✅ Successfully updated current_assistant_id');
    
    // Step 2: Test creating assistant config
    console.log('\n📝 Step 2: Creating assistant config...');
    const configData = {
      attorney_id: attorneyId,
      assistant_id: testAssistantId,
      firm_name: 'LegalScout',
      primary_color: '#2563eb',
      secondary_color: '#1e40af',
      button_color: '#3b82f6',
      background_color: '#ffffff',
      background_opacity: 1.0,
      button_opacity: 1.0,
      practice_area_background_opacity: 0.1,
      text_background_color: '#ffffff',
      welcome_message: 'Hello, how can I help you today?',
      vapi_instructions: 'You are a legal assistant helping clients with their legal needs.',
      vapi_context: '',
      voice_provider: '11labs',
      voice_id: 'sarah',
      ai_model: 'gpt-4o',
      custom_fields: {}
    };

    const { data: configResult, error: configError } = await supabase
      .from('assistant_ui_configs')
      .upsert(configData)
      .select()
      .single();

    if (configError) {
      console.log('❌ Failed to create assistant config:', configError.message);
      console.log('   Error details:', configError);
      return;
    }
    
    console.log('✅ Successfully created assistant config');
    console.log('   Config ID:', configResult.id);
    
    // Step 3: Test retrieving the config
    console.log('\n📝 Step 3: Retrieving assistant config...');
    const { data: retrievedConfig, error: retrieveError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('attorney_id', attorneyId)
      .eq('assistant_id', testAssistantId)
      .single();

    if (retrieveError) {
      console.log('❌ Failed to retrieve assistant config:', retrieveError.message);
      return;
    }
    
    console.log('✅ Successfully retrieved assistant config');
    console.log('   Firm name:', retrievedConfig.firm_name);
    console.log('   Voice provider:', retrievedConfig.voice_provider);
    console.log('   AI model:', retrievedConfig.ai_model);
    
    // Step 4: Test switching back to original assistant
    console.log('\n📝 Step 4: Switching back to original assistant...');
    const originalAssistantId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
    
    const { data: switchBackData, error: switchBackError } = await supabase
      .from('attorneys')
      .update({ 
        current_assistant_id: originalAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorneyId)
      .select();

    if (switchBackError) {
      console.log('❌ Failed to switch back to original assistant:', switchBackError.message);
      return;
    }
    
    console.log('✅ Successfully switched back to original assistant');
    
    console.log('\n🎉 ASSISTANT SWITCHING TEST COMPLETED SUCCESSFULLY!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Can update current_assistant_id in attorneys table');
    console.log('   ✅ Can create assistant_ui_configs records');
    console.log('   ✅ Can retrieve assistant_ui_configs records');
    console.log('   ✅ RLS policy is working correctly');
    console.log('\n📝 The assistant switching should now work in the dashboard!');
    
  } catch (error) {
    console.log(`\n❌ Test failed with error: ${error.message}`);
    console.log(error.stack);
  }
}

// Run the test
testAssistantSwitching();
