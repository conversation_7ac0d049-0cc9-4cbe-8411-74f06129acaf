.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  gap: 0.5rem;
  -webkit-user-select: none;
  user-select: none;
}

.page-button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 41, 59, 0.7);
  color: #cbd5e1;
  border: 1px solid rgba(100, 116, 139, 0.5);
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-button:hover {
  background: rgba(51, 65, 85, 0.8);
  border-color: rgba(148, 163, 184, 0.7);
}

.page-button.active {
  background: rgba(37, 99, 235, 0.7);
  color: white;
  border-color: rgba(59, 130, 246, 0.7);
  font-weight: bold;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(30, 41, 59, 0.4);
}

.nav-button {
  font-size: 1rem;
  font-weight: bold;
}

.ellipsis {
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

/* Results summary styles */
.results-summary {
  text-align: center;
  color: #94a3b8;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Mobile responsive styles */
@media (max-width: 640px) {
  .pagination {
    gap: 0.25rem;
  }
  
  .page-button {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }
  
  .ellipsis {
    width: 1.5rem;
  }
} 