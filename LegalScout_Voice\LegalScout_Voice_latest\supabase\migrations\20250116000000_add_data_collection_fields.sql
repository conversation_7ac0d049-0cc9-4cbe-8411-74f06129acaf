-- Migration: Add data collection fields to assistant_ui_configs table
-- This enables per-assistant data collection configuration

-- Add data collection fields to assistant_ui_configs table
ALTER TABLE assistant_ui_configs 
ADD COLUMN IF NOT EXISTS summary_prompt TEXT,
ADD COLUMN IF NOT EXISTS structured_data_prompt TEXT,
ADD COLUMN IF NOT EXISTS success_evaluation_prompt TEXT,
ADD COLUMN IF NOT EXISTS structured_data_schema JSONB;

-- Add comments for the new columns
COMMENT ON COLUMN assistant_ui_configs.summary_prompt IS 'Prompt used to generate consultation summaries for this assistant';
COMMENT ON COLUMN assistant_ui_configs.structured_data_prompt IS 'Prompt used to extract structured data from consultations for this assistant';
COMMENT ON COLUMN assistant_ui_configs.success_evaluation_prompt IS 'Prompt used to evaluate call success for this assistant';
COMMENT ON COLUMN assistant_ui_configs.structured_data_schema IS 'JSON schema defining the structure of extracted data for this assistant';

-- Create indexes for performance (optional, but good practice)
CREATE INDEX IF NOT EXISTS assistant_ui_configs_summary_prompt_idx ON assistant_ui_configs USING gin(to_tsvector('english', summary_prompt));
CREATE INDEX IF NOT EXISTS assistant_ui_configs_structured_data_prompt_idx ON assistant_ui_configs USING gin(to_tsvector('english', structured_data_prompt));

-- Migration function to copy existing attorney-level data collection settings to assistant configs
CREATE OR REPLACE FUNCTION migrate_data_collection_to_assistant_configs()
RETURNS void AS $$
DECLARE
  attorney_record RECORD;
  config_record RECORD;
BEGIN
  RAISE NOTICE 'Starting migration of data collection settings to assistant configs...';
  
  -- Loop through all attorneys that have data collection settings
  FOR attorney_record IN 
    SELECT * FROM attorneys 
    WHERE (summary_prompt IS NOT NULL AND summary_prompt != '')
    OR (structured_data_prompt IS NOT NULL AND structured_data_prompt != '')
    OR (success_evaluation_prompt IS NOT NULL AND success_evaluation_prompt != '')
    OR (structured_data_schema IS NOT NULL)
  LOOP
    RAISE NOTICE 'Processing attorney: % (ID: %)', attorney_record.firm_name, attorney_record.id;
    
    -- Update all assistant configs for this attorney
    FOR config_record IN 
      SELECT * FROM assistant_ui_configs 
      WHERE attorney_id = attorney_record.id
    LOOP
      UPDATE assistant_ui_configs 
      SET 
        summary_prompt = COALESCE(attorney_record.summary_prompt, summary_prompt),
        structured_data_prompt = COALESCE(attorney_record.structured_data_prompt, structured_data_prompt),
        success_evaluation_prompt = COALESCE(attorney_record.success_evaluation_prompt, success_evaluation_prompt),
        structured_data_schema = COALESCE(attorney_record.structured_data_schema, structured_data_schema),
        updated_at = NOW()
      WHERE id = config_record.id;
      
      RAISE NOTICE 'Updated assistant config for assistant: % (Config ID: %)', 
        config_record.assistant_id, config_record.id;
    END LOOP;
  END LOOP;
  
  RAISE NOTICE 'Completed migration of data collection settings to assistant configs';
END;
$$ LANGUAGE plpgsql;

-- Run the migration function
SELECT migrate_data_collection_to_assistant_configs();

-- Drop the migration function after use
DROP FUNCTION migrate_data_collection_to_assistant_configs();

-- Add a note about the migration
COMMENT ON TABLE assistant_ui_configs IS 'Stores UI configuration settings per assistant for each attorney. Includes data collection settings migrated from attorney-level settings.';
