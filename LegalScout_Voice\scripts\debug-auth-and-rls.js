/**
 * Debug Authentication and RLS Issues
 * 
 * This script thoroughly debugs authentication and RLS policy issues
 * to identify why consultations still can't be created.
 */

async function debugAuthAndRls() {
  console.log('🔍 Debugging Authentication and RLS Issues...\n');

  try {
    // Get supabase client
    const supabase = window.supabase || window.supabaseClient;
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }

    // 1. Check authentication state
    console.log('1️⃣ Checking Authentication State:');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('❌ Error getting user:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ No authenticated user');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    console.log('User ID:', user.id);
    console.log('User role:', user.role);
    console.log('User aud:', user.aud);

    // 2. Check session
    console.log('\n2️⃣ Checking Session:');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Error getting session:', sessionError);
    } else if (!session) {
      console.error('❌ No active session');
    } else {
      console.log('✅ Active session found');
      console.log('Access token exists:', !!session.access_token);
      console.log('Token expires at:', new Date(session.expires_at * 1000));
    }

    // 3. Check attorney linkage
    console.log('\n3️⃣ Checking Attorney Linkage:');
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, email, name, user_id')
      .eq('user_id', user.id);

    if (attorneyError) {
      console.error('❌ Error fetching attorneys:', attorneyError);
    } else {
      console.log(`✅ Found ${attorneys.length} attorneys linked to user:`);
      attorneys.forEach(attorney => {
        console.log(`  - ${attorney.name} (${attorney.email}) - ID: ${attorney.id}`);
      });
    }

    // 4. Test RLS policy directly
    console.log('\n4️⃣ Testing RLS Policy Query:');
    
    if (attorneys && attorneys.length > 0) {
      const attorney = attorneys[0];
      
      // Test the exact query that RLS policy uses
      const { data: rlsTest, error: rlsError } = await supabase
        .from('attorneys')
        .select('id')
        .eq('user_id', user.id);

      if (rlsError) {
        console.error('❌ RLS test query failed:', rlsError);
      } else {
        console.log('✅ RLS test query successful:', rlsTest.map(a => a.id));
      }

      // 5. Try a simple select on consultations
      console.log('\n5️⃣ Testing Consultations SELECT:');
      const { data: selectTest, error: selectError } = await supabase
        .from('consultations')
        .select('id, client_name')
        .eq('attorney_id', attorney.id)
        .limit(1);

      if (selectError) {
        console.error('❌ SELECT test failed:', selectError);
      } else {
        console.log('✅ SELECT test successful:', selectTest.length, 'consultations found');
      }

      // 6. Check if we can insert with minimal data
      console.log('\n6️⃣ Testing Minimal INSERT:');
      const minimalConsultation = {
        attorney_id: attorney.id,
        client_name: 'Minimal Test',
        status: 'new'
      };

      const { data: insertTest, error: insertError } = await supabase
        .from('consultations')
        .insert(minimalConsultation)
        .select();

      if (insertError) {
        console.error('❌ Minimal INSERT failed:', insertError);
        console.log('Error code:', insertError.code);
        console.log('Error details:', insertError.details);
        console.log('Error hint:', insertError.hint);
      } else {
        console.log('✅ Minimal INSERT successful!');
        console.log('Created consultation:', insertTest[0]);
        
        // Clean up
        await supabase
          .from('consultations')
          .delete()
          .eq('id', insertTest[0].id);
        console.log('🧹 Test consultation cleaned up');
      }
    }

    // 7. Check Supabase client configuration
    console.log('\n7️⃣ Checking Supabase Client Config:');
    console.log('Supabase URL:', supabase.supabaseUrl);
    console.log('Supabase Key (first 20 chars):', supabase.supabaseKey?.substring(0, 20) + '...');
    console.log('Auth headers present:', !!supabase.auth?.session?.access_token);

    // 8. Try using service role temporarily (if available)
    console.log('\n8️⃣ Checking Alternative Approaches:');
    
    // Check if there's a service role key available (shouldn't be in frontend, but let's check)
    const serviceKey = window.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (serviceKey) {
      console.log('⚠️ Service key found in frontend (security risk!)');
    } else {
      console.log('✅ No service key in frontend (good for security)');
    }

    // 9. Manual RLS policy check
    console.log('\n9️⃣ Manual RLS Policy Verification:');
    
    // This query simulates what the RLS policy does
    const rlsQuery = `
      SELECT EXISTS(
        SELECT 1 FROM attorneys 
        WHERE attorneys.user_id = '${user.id}' 
        AND attorneys.id = '${attorneys?.[0]?.id}'
      ) as can_access;
    `;
    
    console.log('RLS simulation query:', rlsQuery);
    
    // We can't run raw SQL from frontend, but we can simulate it
    const { data: rlsSimulation, error: rlsSimError } = await supabase
      .from('attorneys')
      .select('id')
      .eq('user_id', user.id)
      .eq('id', attorneys?.[0]?.id);

    if (rlsSimError) {
      console.error('❌ RLS simulation failed:', rlsSimError);
    } else {
      console.log('✅ RLS simulation result:', rlsSimulation.length > 0 ? 'ALLOWED' : 'DENIED');
    }

    // 10. Summary and recommendations
    console.log('\n📋 DIAGNOSIS SUMMARY:');
    console.log('='.repeat(50));
    console.log(`Authentication: ${user ? '✅ OK' : '❌ FAILED'}`);
    console.log(`Session: ${session ? '✅ OK' : '❌ FAILED'}`);
    console.log(`Attorney Linkage: ${attorneys?.length > 0 ? '✅ OK' : '❌ FAILED'}`);
    console.log(`RLS Policy: ${rlsSimulation?.length > 0 ? '✅ OK' : '❌ FAILED'}`);

    if (insertError) {
      console.log('\n❌ CONSULTATION INSERT STILL FAILING');
      console.log('💡 POSSIBLE SOLUTIONS:');
      
      if (insertError.code === '42501') {
        console.log('1. RLS policy issue - check policy conditions');
        console.log('2. User ID mismatch - verify attorney.user_id = auth.uid()');
        console.log('3. Session expired - try refreshing the page');
      } else if (insertError.code === '401' || insertError.message.includes('Unauthorized')) {
        console.log('1. Authentication issue - check session validity');
        console.log('2. API key issue - verify Supabase configuration');
        console.log('3. CORS issue - check allowed origins');
      }
      
      console.log('\n🔧 IMMEDIATE ACTIONS:');
      console.log('1. Try refreshing the page and running this again');
      console.log('2. Check if you can manually create a consultation in Supabase dashboard');
      console.log('3. Verify RLS policies in Supabase dashboard');
    } else {
      console.log('\n🎉 CONSULTATION INSERT WORKING!');
      console.log('The issue has been resolved. Try creating consultations now.');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Instructions
console.log(`
🔍 AUTHENTICATION & RLS DEBUG:
==============================

Run: debugAuthAndRls()

This will thoroughly check:
- Authentication state
- Session validity  
- Attorney linkage
- RLS policies
- Consultation permissions

This should identify exactly why consultations can't be created.
`);

// Auto-run
if (typeof window !== 'undefined' && window.location && window.location.pathname.includes('dashboard')) {
  console.log('🚀 Auto-running auth and RLS debug...');
  debugAuthAndRls();
}
