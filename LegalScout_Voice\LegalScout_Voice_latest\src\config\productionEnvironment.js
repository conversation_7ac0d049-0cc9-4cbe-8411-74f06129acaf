/**
 * Production Environment Configuration
 *
 * DEFINITIVE PRODUCTION FIX: Direct environment variable access that works
 * in both development and production without any eval() or unsafe patterns.
 *
 * This addresses the ROOT CAUSES identified in the Vercel build log.
 */

// Direct environment variable values - hardcoded for production reliability
export const PRODUCTION_CONFIG = {
  // Supabase Configuration
  SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',

  // Vapi Configuration
  VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_PRIVATE_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',

  // Environment Detection - Use import.meta for build-time detection
  IS_PRODUCTION: (() => {
    try {
      // Try import.meta first (most reliable for Vite)
      if (import.meta && import.meta.env) {
        return import.meta.env.PROD === true;
      }
    } catch (e) {
      // Fallback to window-based detection
      if (typeof window !== 'undefined') {
        return !window.location.hostname.includes('localhost') &&
               !window.location.hostname.includes('127.0.0.1');
      }
    }
    return false;
  })(),

  IS_DEVELOPMENT: (() => {
    try {
      // Try import.meta first (most reliable for Vite)
      if (import.meta && import.meta.env) {
        return import.meta.env.DEV === true;
      }
    } catch (e) {
      // Fallback to window-based detection
      if (typeof window !== 'undefined') {
        return window.location.hostname.includes('localhost') ||
               window.location.hostname.includes('127.0.0.1');
      }
    }
    return true; // Default to development
  })()
};

// Safe environment variable getter that works in all contexts
export const getEnvironmentVariable = (key) => {
  // Strategy 1: Use direct config (most reliable)
  if (PRODUCTION_CONFIG[key]) {
    return PRODUCTION_CONFIG[key];
  }
  
  // Strategy 2: Try window globals (runtime injection)
  if (typeof window !== 'undefined') {
    const windowKey = `VITE_${key}`;
    if (window[windowKey]) {
      return window[windowKey];
    }
    if (window[key]) {
      return window[key];
    }
  }
  
  // Strategy 3: Try import.meta.env (development)
  try {
    if (import.meta && import.meta.env) {
      const metaKey = `VITE_${key}`;
      if (import.meta.env[metaKey]) {
        return import.meta.env[metaKey];
      }
    }
  } catch (e) {
    // import.meta not available, continue
  }
  
  // Strategy 4: Try process.env (Node.js/server)
  if (typeof process !== 'undefined' && process.env) {
    const processKey = `VITE_${key}`;
    if (process.env[processKey]) {
      return process.env[processKey];
    }
    if (process.env[key]) {
      return process.env[key];
    }
  }
  
  return null;
};

// Specific getters for each service with enhanced validation
export const getSupabaseConfig = () => {
  const url = getEnvironmentVariable('SUPABASE_URL') || PRODUCTION_CONFIG.SUPABASE_URL;
  const anonKey = getEnvironmentVariable('SUPABASE_ANON_KEY') || PRODUCTION_CONFIG.SUPABASE_ANON_KEY;

  // Validate configuration
  if (!url || !anonKey || url === 'undefined' || anonKey === 'undefined') {
    console.error('❌ [ProductionEnvironment] Invalid Supabase config:', {
      url: url ? 'present' : 'missing',
      anonKey: anonKey ? 'present' : 'missing'
    });
  }

  return { url, anonKey };
};

export const getVapiConfig = () => {
  const publicKey = getEnvironmentVariable('VAPI_PUBLIC_KEY') || PRODUCTION_CONFIG.VAPI_PUBLIC_KEY;
  const secretKey = getEnvironmentVariable('VAPI_SECRET_KEY') || PRODUCTION_CONFIG.VAPI_SECRET_KEY;

  // Validate configuration
  if (!publicKey || !secretKey || publicKey === 'undefined' || secretKey === 'undefined') {
    console.error('❌ [ProductionEnvironment] Invalid Vapi config:', {
      publicKey: publicKey ? 'present' : 'missing',
      secretKey: secretKey ? 'present' : 'missing'
    });
  }

  return { publicKey, secretKey };
};

// Initialize production environment variables in window object for Vite compatibility
export const initializeProductionEnvironment = () => {
  if (typeof window === 'undefined') return;

  console.log('🔧 [ProductionEnvironment] Initializing production environment...');

  const supabaseConfig = getSupabaseConfig();
  const vapiConfig = getVapiConfig();

  // Set in window object for Vite compatibility
  window.VITE_SUPABASE_URL = supabaseConfig.url;
  window.VITE_SUPABASE_KEY = supabaseConfig.anonKey;
  window.VITE_VAPI_PUBLIC_KEY = vapiConfig.publicKey;
  window.VITE_VAPI_SECRET_KEY = vapiConfig.secretKey;

  console.log('✅ [ProductionEnvironment] Environment variables initialized in window object');

  return {
    supabase: supabaseConfig,
    vapi: vapiConfig
  };
};

// Environment detection
export const isProduction = () => PRODUCTION_CONFIG.IS_PRODUCTION;
export const isDevelopment = () => PRODUCTION_CONFIG.IS_DEVELOPMENT;

// Initialize environment (call this early in the app)
export const initializeEnvironment = () => {
  console.log('🔧 [ProductionEnvironment] Initializing environment configuration...');
  
  // Set global variables for compatibility
  if (typeof window !== 'undefined') {
    // Set all environment variables on window for compatibility
    Object.entries(PRODUCTION_CONFIG).forEach(([key, value]) => {
      if (typeof value === 'string') {
        window[`VITE_${key}`] = value;
        window[key] = value;
      }
    });

    // Ensure process.env exists
    if (!window.process) {
      window.process = { env: {}, browser: true };
    }

    // Set process.env variables
    Object.entries(PRODUCTION_CONFIG).forEach(([key, value]) => {
      if (typeof value === 'string') {
        window.process.env[`VITE_${key}`] = value;
        window.process.env[key] = value;
      }
    });

    console.log('✅ [ProductionEnvironment] Environment variables set globally');
  }

  // Log configuration (without sensitive data)
  console.log('🔧 [ProductionEnvironment] Configuration:', {
    isProduction: isProduction(),
    isDevelopment: isDevelopment(),
    supabaseUrl: getSupabaseConfig().url,
    vapiKeyPresent: !!getVapiConfig().publicKey
  });

  return PRODUCTION_CONFIG;
};

// Export default configuration
export default PRODUCTION_CONFIG;
