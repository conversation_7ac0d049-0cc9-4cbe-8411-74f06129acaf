/* Website Importer - Dashboard Integration */
.website-importer {
  background-color: rgba(255, 255, 255, 0.03);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-medium);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: var(--transition-default);
  box-shadow: var(--shadow-soft);
}

.website-importer:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.02);
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.importer-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.importer-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.1);
  padding: 0.75rem;
  border-radius: 50%;
  box-shadow: var(--shadow-soft);
  flex-shrink: 0;
}

.importer-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
}

.importer-header p {
  margin: 0.25rem 0 0 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.importer-form {
  margin-bottom: 1rem;
}

.url-input-group {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.url-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition-default);
}

.url-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.url-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.import-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-default);
  white-space: nowrap;
}

.import-button:hover:not(:disabled) {
  background-color: var(--primary-color);
  filter: brightness(0.9);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.import-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.import-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-small);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  margin-top: 0.5rem;
}

.import-message.error {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.import-message.success {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.import-features {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
  margin-top: 0.5rem;
}

/* Dark theme styles */
[data-theme="dark"] .website-importer {
  background-color: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .website-importer:hover {
  border-color: var(--dark-accent);
  background-color: rgba(100, 181, 246, 0.05);
}

[data-theme="dark"] .importer-icon {
  color: var(--dark-accent);
  background: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .importer-header h4 {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .importer-header p {
  color: var(--dark-text-secondary);
}

[data-theme="dark"] .url-input {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .url-input:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .url-input::placeholder {
  color: var(--dark-text-secondary);
}

[data-theme="dark"] .import-button {
  background-color: var(--dark-accent);
}

[data-theme="dark"] .import-button:hover:not(:disabled) {
  background-color: var(--dark-accent-hover);
}

[data-theme="dark"] .import-features {
  color: var(--dark-text-secondary);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .url-input-group {
    flex-direction: column;
  }

  .import-button {
    justify-content: center;
  }
}
