/**
 * React Context Fix Test
 * 
 * Specifically tests and fixes the React Context issues identified in the logs
 */

class ReactContextFixTest {
  constructor() {
    this.fixes = [];
    this.errors = [];
  }

  async runTest() {
    console.log('🔧 React Context Fix Test');
    console.log('=========================');

    try {
      await this.checkReactAvailability();
      await this.fixContextImports();
      await this.testContextCreation();
      await this.validateAssistantAwareContext();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ React Context fix test failed:', error);
      this.errors.push(`Test failure: ${error.message}`);
    }
  }

  async checkReactAvailability() {
    console.log('\n🔍 Checking React availability...');
    
    try {
      // Check if React is globally available
      if (typeof React === 'undefined') {
        console.log('⚠️ React not found globally, attempting to load...');
        
        // Try to load React from CDN
        await this.loadReactFromCDN();
      }

      if (typeof React !== 'undefined') {
        console.log('✅ React is available');
        console.log(`📦 React version: ${React.version || 'unknown'}`);
        this.fixes.push('React availability confirmed');
      } else {
        this.errors.push('React is not available');
      }
    } catch (error) {
      console.error('❌ React availability check failed:', error);
      this.errors.push(`React availability: ${error.message}`);
    }
  }

  async loadReactFromCDN() {
    return new Promise((resolve, reject) => {
      // Load React
      const reactScript = document.createElement('script');
      reactScript.src = 'https://unpkg.com/react@18/umd/react.production.min.js';
      reactScript.onload = () => {
        // Load ReactDOM
        const reactDOMScript = document.createElement('script');
        reactDOMScript.src = 'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js';
        reactDOMScript.onload = () => {
          console.log('✅ React loaded from CDN');
          resolve();
        };
        reactDOMScript.onerror = () => reject(new Error('Failed to load ReactDOM from CDN'));
        document.head.appendChild(reactDOMScript);
      };
      reactScript.onerror = () => reject(new Error('Failed to load React from CDN'));
      document.head.appendChild(reactScript);
    });
  }

  async fixContextImports() {
    console.log('\n🔧 Fixing Context imports...');
    
    try {
      // Create a safe React context import function
      window.createSafeReactContext = (defaultValue) => {
        if (typeof React !== 'undefined' && React.createContext) {
          return React.createContext(defaultValue);
        } else {
          console.warn('React.createContext not available, using fallback');
          return {
            Provider: ({ children }) => children,
            Consumer: ({ children }) => children(defaultValue),
            displayName: 'FallbackContext'
          };
        }
      };

      // Create safe hooks
      window.useSafeContext = (context) => {
        if (typeof React !== 'undefined' && React.useContext) {
          return React.useContext(context);
        } else {
          console.warn('React.useContext not available, using fallback');
          return context._currentValue || null;
        }
      };

      window.useSafeState = (initialState) => {
        if (typeof React !== 'undefined' && React.useState) {
          return React.useState(initialState);
        } else {
          console.warn('React.useState not available, using fallback');
          return [initialState, () => {}];
        }
      };

      window.useSafeEffect = (effect, deps) => {
        if (typeof React !== 'undefined' && React.useEffect) {
          return React.useEffect(effect, deps);
        } else {
          console.warn('React.useEffect not available, using fallback');
          // Execute effect immediately for fallback
          if (typeof effect === 'function') {
            effect();
          }
        }
      };

      console.log('✅ Safe React context functions created');
      this.fixes.push('Safe React context functions created');
    } catch (error) {
      console.error('❌ Context import fix failed:', error);
      this.errors.push(`Context import fix: ${error.message}`);
    }
  }

  async testContextCreation() {
    console.log('\n🧪 Testing context creation...');
    
    try {
      // Test creating a context
      const testContext = window.createSafeReactContext({ test: true });
      
      if (testContext) {
        console.log('✅ Test context created successfully');
        console.log('📦 Context structure:', Object.keys(testContext));
        this.fixes.push('Context creation test passed');
      } else {
        this.errors.push('Failed to create test context');
      }
    } catch (error) {
      console.error('❌ Context creation test failed:', error);
      this.errors.push(`Context creation: ${error.message}`);
    }
  }

  async validateAssistantAwareContext() {
    console.log('\n🔍 Validating AssistantAwareContext...');
    
    try {
      // Create a mock AssistantAwareContext
      const mockAssistantAwareContext = window.createSafeReactContext({
        currentAssistant: null,
        assistants: [],
        switchAssistant: () => {},
        loading: false,
        error: null
      });

      // Test the context
      if (mockAssistantAwareContext) {
        console.log('✅ AssistantAwareContext mock created successfully');
        
        // Make it globally available for testing
        window.MockAssistantAwareContext = mockAssistantAwareContext;
        
        this.fixes.push('AssistantAwareContext mock validated');
      } else {
        this.errors.push('Failed to create AssistantAwareContext mock');
      }
    } catch (error) {
      console.error('❌ AssistantAwareContext validation failed:', error);
      this.errors.push(`AssistantAwareContext validation: ${error.message}`);
    }
  }

  generateReport() {
    console.log('\n📊 React Context Fix Report');
    console.log('===========================');
    
    if (this.fixes.length > 0) {
      console.log('\n✅ Applied Fixes:');
      this.fixes.forEach(fix => console.log(`  - ${fix}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log(`\n📈 Summary: ${this.fixes.length} fixes applied, ${this.errors.length} errors`);
    
    if (this.errors.length === 0) {
      console.log('🎉 All React Context issues resolved!');
    } else {
      console.log('🚨 Some React Context issues remain - see errors above');
    }

    // Provide usage instructions
    console.log('\n📖 Usage Instructions:');
    console.log('  - Use window.createSafeReactContext() instead of React.createContext()');
    console.log('  - Use window.useSafeContext() instead of React.useContext()');
    console.log('  - Use window.useSafeState() instead of React.useState()');
    console.log('  - Use window.useSafeEffect() instead of React.useEffect()');
  }
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  window.ReactContextFixTest = ReactContextFixTest;
  
  // Auto-run after page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const test = new ReactContextFixTest();
        test.runTest();
      }, 1000);
    });
  } else {
    setTimeout(() => {
      const test = new ReactContextFixTest();
      test.runTest();
    }, 1000);
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ReactContextFixTest;
}
