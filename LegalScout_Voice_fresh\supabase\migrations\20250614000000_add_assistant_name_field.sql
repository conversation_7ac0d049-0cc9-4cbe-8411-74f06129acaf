-- Migration: Add assistant_name field to assistant_ui_configs table
-- This field stores the Agent Name from the Agent menu (titleText)

-- Add assistant_name column to assistant_ui_configs table
ALTER TABLE assistant_ui_configs 
ADD COLUMN IF NOT EXISTS assistant_name TEXT;

-- Add comment for the new column
COMMENT ON COLUMN assistant_ui_configs.assistant_name IS 'The agent name from Agent menu (titleText field)';

-- Create index for performance
CREATE INDEX IF NOT EXISTS assistant_ui_configs_assistant_name_idx ON assistant_ui_configs (assistant_name);

-- Migration function to populate assistant_name from existing data
CREATE OR REPLACE FUNCTION populate_assistant_name_from_title_text()
RETURNS void AS $$
DECLARE
  config_record RECORD;
  attorney_record RECORD;
BEGIN
  -- Loop through all assistant configs that don't have assistant_name set
  FOR config_record IN 
    SELECT * FROM assistant_ui_configs 
    WHERE assistant_name IS NULL OR assistant_name = ''
  LOOP
    -- Get the corresponding attorney record
    SELECT * INTO attorney_record 
    FROM attorneys 
    WHERE id = config_record.attorney_id;
    
    -- Update assistant_name with title_text from attorney record
    IF attorney_record.title_text IS NOT NULL AND attorney_record.title_text != '' THEN
      UPDATE assistant_ui_configs 
      SET assistant_name = attorney_record.title_text
      WHERE id = config_record.id;
      
      RAISE NOTICE 'Updated assistant_name for config % with value: %', 
        config_record.id, attorney_record.title_text;
    ELSE
      -- Fallback to firm_name if title_text is empty
      UPDATE assistant_ui_configs 
      SET assistant_name = COALESCE(attorney_record.firm_name, 'LegalScout')
      WHERE id = config_record.id;
      
      RAISE NOTICE 'Updated assistant_name for config % with fallback firm_name: %', 
        config_record.id, COALESCE(attorney_record.firm_name, 'LegalScout');
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run the migration
SELECT populate_assistant_name_from_title_text();

-- Drop the migration function (no longer needed)
DROP FUNCTION populate_assistant_name_from_title_text();
