<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi SDK Status Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <h1>🔍 Vapi SDK Status Test</h1>
    
    <div class="status-card info">
        <h3>Environment Variables</h3>
        <div id="env-status">Checking...</div>
    </div>

    <div class="status-card info">
        <h3>Vapi SDK Loading Status</h3>
        <div id="sdk-status">Checking...</div>
    </div>

    <div class="status-card info">
        <h3>Test Actions</h3>
        <button onclick="testVapiImport()">Test NPM Import</button>
        <button onclick="testCDNLoad()">Test CDN Load</button>
        <button onclick="testVapiInstance()">Test Vapi Instance</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="status-card info">
        <h3>Test Results</h3>
        <div id="test-results"></div>
    </div>

    <script>
        // Check environment variables
        function checkEnvironment() {
            const envDiv = document.getElementById('env-status');
            const envVars = {
                'VITE_VAPI_PUBLIC_KEY': import.meta.env.VITE_VAPI_PUBLIC_KEY,
                'VITE_VAPI_SECRET_KEY': import.meta.env.VITE_VAPI_SECRET_KEY,
                'VITE_VAPI_BASE_URL': import.meta.env.VITE_VAPI_BASE_URL
            };

            let html = '<ul>';
            for (const [key, value] of Object.entries(envVars)) {
                const status = value ? '✅ SET' : '❌ NOT SET';
                const preview = value ? `(${value.substring(0, 8)}...)` : '';
                html += `<li><strong>${key}:</strong> ${status} ${preview}</li>`;
            }
            html += '</ul>';
            envDiv.innerHTML = html;
        }

        // Check SDK status
        function checkSDKStatus() {
            const sdkDiv = document.getElementById('sdk-status');
            let html = '<ul>';
            
            // Check window.Vapi
            html += `<li><strong>window.Vapi:</strong> ${typeof window.Vapi !== 'undefined' ? '✅ Available' : '❌ Not Available'}</li>`;
            
            // Check if it's a function
            if (typeof window.Vapi !== 'undefined') {
                html += `<li><strong>Vapi Type:</strong> ${typeof window.Vapi}</li>`;
                html += `<li><strong>Vapi Constructor:</strong> ${typeof window.Vapi === 'function' ? '✅ Valid' : '❌ Invalid'}</li>`;
            }

            html += '</ul>';
            sdkDiv.innerHTML = html;
        }

        // Test NPM import
        async function testVapiImport() {
            const resultsDiv = document.getElementById('test-results');
            try {
                addResult('🔄 Testing NPM import...', 'info');
                const VapiModule = await import('@vapi-ai/web');
                addResult('✅ NPM import successful', 'success');
                addResult(`Module structure: ${JSON.stringify(Object.keys(VapiModule), null, 2)}`, 'info');
                
                const VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
                if (typeof VapiClass === 'function') {
                    addResult('✅ Valid Vapi constructor found in module', 'success');
                    window.Vapi = VapiClass;
                    checkSDKStatus();
                } else {
                    addResult('❌ No valid Vapi constructor in module', 'error');
                }
            } catch (error) {
                addResult(`❌ NPM import failed: ${error.message}`, 'error');
            }
        }

        // Test CDN load
        async function testCDNLoad() {
            try {
                addResult('🔄 Testing CDN load...', 'info');
                
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.5/dist/vapi.js';
                script.async = true;
                script.crossOrigin = 'anonymous';

                const loadPromise = new Promise((resolve, reject) => {
                    script.onload = () => {
                        addResult('✅ CDN script loaded successfully', 'success');
                        checkSDKStatus();
                        resolve();
                    };
                    script.onerror = () => {
                        addResult('❌ CDN script failed to load', 'error');
                        reject(new Error('CDN load failed'));
                    };
                });

                document.head.appendChild(script);
                await loadPromise;
            } catch (error) {
                addResult(`❌ CDN load failed: ${error.message}`, 'error');
            }
        }

        // Test Vapi instance creation
        async function testVapiInstance() {
            try {
                addResult('🔄 Testing Vapi instance creation...', 'info');
                
                if (typeof window.Vapi !== 'function') {
                    addResult('❌ Vapi constructor not available', 'error');
                    return;
                }

                const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
                if (!apiKey) {
                    addResult('❌ No API key available', 'error');
                    return;
                }

                const vapi = new window.Vapi(apiKey);
                addResult('✅ Vapi instance created successfully', 'success');
                addResult(`Instance methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)).join(', ')}`, 'info');
                
            } catch (error) {
                addResult(`❌ Vapi instance creation failed: ${error.message}`, 'error');
            }
        }

        // Helper function to add results
        function addResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status-card ${type}`;
            div.innerHTML = `<p>${message}</p>`;
            resultsDiv.appendChild(div);
        }

        // Clear results
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Initialize on load
        window.addEventListener('load', () => {
            checkEnvironment();
            checkSDKStatus();
            
            // Auto-refresh SDK status every 2 seconds
            setInterval(checkSDKStatus, 2000);
        });
    </script>
</body>
</html>
