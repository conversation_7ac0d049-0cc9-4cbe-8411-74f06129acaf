import React, { useState } from 'react';
import useVapi from '../hooks/use-vapi';
import EnhancedCallController from './call/EnhancedCallController';
import './MeetingScheduler.css';

/**
 * Meeting Scheduler Component
 * 
 * This component provides a UI for scheduling meetings with attorneys
 * using Vapi's function calling capabilities. It's inspired by the
 * Vapi Blocks Meeting Scheduler but styled to match LegalScout's UI.
 */
const MeetingScheduler = ({ 
  attorneyId, 
  attorneyName, 
  assistantId,
  className 
}) => {
  const [scheduledMeeting, setScheduledMeeting] = useState(null);
  const [error, setError] = useState(null);
  
  // Function to handle successful meeting scheduling
  const handleMeetingScheduled = (meeting) => {
    setScheduledMeeting(meeting);
    setError(null);
  };
  
  // Function to handle errors
  const handleError = (errorMessage) => {
    setError(errorMessage);
  };
  
  // Function to reset the scheduler
  const handleReset = () => {
    setScheduledMeeting(null);
    setError(null);
  };
  
  return (
    <div className={`meeting-scheduler ${className || ''}`}>
      <div className="scheduler-header">
        <h2 className="scheduler-title">Schedule a Consultation</h2>
        <p className="scheduler-description">
          Speak with our AI assistant to schedule a consultation with {attorneyName || 'an attorney'}.
        </p>
      </div>
      
      <div className="scheduler-content">
        {scheduledMeeting ? (
          <div className="meeting-confirmation">
            <div className="confirmation-icon">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="confirmation-title">Consultation Scheduled!</h3>
            <div className="confirmation-details">
              <p><strong>Date:</strong> {scheduledMeeting.date}</p>
              <p><strong>Time:</strong> {scheduledMeeting.time}</p>
              <p><strong>Attorney:</strong> {scheduledMeeting.attorney || attorneyName}</p>
              {scheduledMeeting.location && (
                <p><strong>Location:</strong> {scheduledMeeting.location}</p>
              )}
            </div>
            <p className="confirmation-message">
              A confirmation email has been sent to your email address. You'll also receive a reminder before your consultation.
            </p>
            <button 
              className="reset-button"
              onClick={handleReset}
            >
              Schedule Another Consultation
            </button>
          </div>
        ) : error ? (
          <div className="scheduling-error">
            <div className="error-icon">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 className="error-title">Scheduling Error</h3>
            <p className="error-message">{error}</p>
            <button 
              className="retry-button"
              onClick={handleReset}
            >
              Try Again
            </button>
          </div>
        ) : (
          <div className="call-controller-container">
            <EnhancedCallController 
              assistantId={assistantId}
              showTranscript={true}
              showVisualization={true}
            />
            <div className="instructions">
              <h3 className="instructions-title">How to Schedule</h3>
              <ol className="instructions-list">
                <li>Click "Start Call" to speak with our AI assistant</li>
                <li>Tell the assistant you'd like to schedule a consultation</li>
                <li>Provide your name, email, and preferred dates/times</li>
                <li>The assistant will check availability and confirm your appointment</li>
              </ol>
            </div>
          </div>
        )}
      </div>
      
      <div className="scheduler-footer">
        <p className="privacy-note">
          Your information will only be used to schedule and manage your consultation.
        </p>
      </div>
    </div>
  );
};

export default MeetingScheduler;
