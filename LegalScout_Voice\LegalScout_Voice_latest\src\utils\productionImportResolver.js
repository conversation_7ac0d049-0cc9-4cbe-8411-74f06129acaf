/**
 * Production Import Resolver
 * 
 * Resolves the dynamic/static import conflicts that cause build warnings
 * by providing a consistent import strategy for production builds.
 */

// Cache for resolved modules to prevent duplicate imports
const moduleCache = new Map();

/**
 * Resolves imports consistently for production builds
 * @param {string} modulePath - The module path to import
 * @param {boolean} preferStatic - Whether to prefer static imports
 * @returns {Promise} - The imported module
 */
export async function resolveImport(modulePath, preferStatic = true) {
  // Check cache first
  if (moduleCache.has(modulePath)) {
    return moduleCache.get(modulePath);
  }

  let module;
  
  try {
    if (preferStatic) {
      // For production, use static imports when possible
      module = await getStaticImport(modulePath);
    } else {
      // Use dynamic import
      module = await import(modulePath);
    }
    
    // Cache the result
    moduleCache.set(modulePath, module);
    return module;
  } catch (error) {
    console.warn(`[ProductionImportResolver] Failed to import ${modulePath}:`, error);
    throw error;
  }
}

/**
 * Gets static imports for commonly used modules
 */
async function getStaticImport(modulePath) {
  switch (modulePath) {
    case '../config/vapiConfig.js':
    case './config/vapiConfig.js':
    case '/src/config/vapiConfig.js':
      return import('../config/vapiConfig.js');
      
    case '../config/mcp.config.js':
    case './config/mcp.config.js':
    case '/src/config/mcp.config.js':
      return import('../config/mcp.config.js');
      
    case '../services/EnhancedVapiMcpService.js':
    case './services/EnhancedVapiMcpService.js':
    case '/src/services/EnhancedVapiMcpService.js':
      return import('../services/EnhancedVapiMcpService.js');
      
    case '../lib/supabase.js':
    case './lib/supabase.js':
    case '/src/lib/supabase.js':
      return import('../lib/supabase.js');
      
    case '../services/vapiMcpService.js':
    case './services/vapiMcpService.js':
    case '/src/services/vapiMcpService.js':
      return import('../services/vapiMcpService.js');
      
    case '../utils/loggerUtils.js':
    case './utils/loggerUtils.js':
    case '/src/utils/loggerUtils.js':
      return import('../utils/loggerUtils.js');
      
    default:
      // For other modules, use dynamic import
      return import(modulePath);
  }
}

/**
 * Preloads commonly used modules to reduce runtime import overhead
 */
export async function preloadCriticalModules() {
  const criticalModules = [
    '../config/vapiConfig.js',
    '../config/mcp.config.js',
    '../services/EnhancedVapiMcpService.js',
    '../lib/supabase.js',
    '../services/vapiMcpService.js'
  ];
  
  console.log('[ProductionImportResolver] Preloading critical modules...');
  
  const preloadPromises = criticalModules.map(async (modulePath) => {
    try {
      await resolveImport(modulePath);
      console.log(`[ProductionImportResolver] ✅ Preloaded ${modulePath}`);
    } catch (error) {
      console.warn(`[ProductionImportResolver] ⚠️ Failed to preload ${modulePath}:`, error);
    }
  });
  
  await Promise.allSettled(preloadPromises);
  console.log('[ProductionImportResolver] Critical modules preloading complete');
}

/**
 * Clears the module cache (useful for development)
 */
export function clearModuleCache() {
  moduleCache.clear();
  console.log('[ProductionImportResolver] Module cache cleared');
}

/**
 * Gets cache statistics
 */
export function getCacheStats() {
  return {
    size: moduleCache.size,
    modules: Array.from(moduleCache.keys())
  };
}

export default {
  resolveImport,
  preloadCriticalModules,
  clearModuleCache,
  getCacheStats
};
