/**
 * Cleanup Contaminated Data Utility
 * 
 * Fixes the "Joe's Pizza" contamination and ensures proper user/firm separation
 */

import { supabase } from '../lib/supabase';
import { userFirmSeparationService } from '../services/userFirmSeparationService';

export class DataCleanupUtility {
  constructor() {
    this.cleanupLog = [];
  }

  /**
   * Main cleanup function
   * @param {string} userId - User ID to clean up
   */
  async cleanupUserData(userId) {
    console.log('🧹 Starting data cleanup for user:', userId);
    this.cleanupLog = [];

    try {
      // Step 1: Identify contaminated data
      await this.identifyContamination(userId);

      // Step 2: Clean up attorney records
      await this.cleanupAttorneyRecords(userId);

      // Step 3: Fix Vapi assistant configurations
      await this.fixVapiAssistantConfigs(userId);

      // Step 4: Create proper user/firm separation
      await this.createProperSeparation(userId);

      // Step 5: Verify cleanup
      await this.verifyCleanup(userId);

      console.log('✅ Data cleanup completed successfully');
      return {
        success: true,
        log: this.cleanupLog
      };

    } catch (error) {
      console.error('❌ Data cleanup failed:', error);
      return {
        success: false,
        error: error.message,
        log: this.cleanupLog
      };
    }
  }

  /**
   * Identify contaminated data
   * @param {string} userId - User ID
   */
  async identifyContamination(userId) {
    this.log('🔍 Identifying contaminated data...');

    try {
      // Check attorneys table for contamination
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        this.log(`⚠️ Error checking attorneys: ${error.message}`);
        return;
      }

      if (attorneys && attorneys.length > 0) {
        attorneys.forEach(attorney => {
          this.log(`📋 Found attorney record: ${attorney.firm_name} (${attorney.email})`);
          
          // Check for contamination patterns
          if (attorney.firm_name && attorney.firm_name.toLowerCase().includes('pizza')) {
            this.log(`🚨 CONTAMINATION DETECTED: ${attorney.firm_name}`);
          }
          
          if (attorney.vapi_assistant_id) {
            this.log(`🤖 Vapi Assistant ID: ${attorney.vapi_assistant_id}`);
          }
        });
      } else {
        this.log('📋 No attorney records found');
      }

    } catch (error) {
      this.log(`❌ Error in contamination check: ${error.message}`);
    }
  }

  /**
   * Clean up attorney records
   * @param {string} userId - User ID
   */
  async cleanupAttorneyRecords(userId) {
    this.log('🧹 Cleaning up attorney records...');

    try {
      // Get user info to determine correct firm name
      const { data: user } = await supabase.auth.getUser();
      const userEmail = user?.user?.email;

      // Determine correct firm name
      const correctFirmName = this.getCorrectFirmName(userEmail);
      this.log(`✅ Correct firm name should be: ${correctFirmName}`);

      // Update contaminated records
      const { data: updatedAttorneys, error } = await supabase
        .from('attorneys')
        .update({
          firm_name: correctFirmName,
          welcome_message: `Hello! I'm Scout from ${correctFirmName}. How can I help you with your legal needs today?`,
          vapi_instructions: `You are Scout, a legal assistant for ${correctFirmName}. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.`,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select();

      if (error) {
        this.log(`❌ Error updating attorneys: ${error.message}`);
      } else {
        this.log(`✅ Updated ${updatedAttorneys?.length || 0} attorney records`);
      }

    } catch (error) {
      this.log(`❌ Error in attorney cleanup: ${error.message}`);
    }
  }

  /**
   * Fix Vapi assistant configurations
   * @param {string} userId - User ID
   */
  async fixVapiAssistantConfigs(userId) {
    this.log('🤖 Fixing Vapi assistant configurations...');

    try {
      // Get attorney records to find assistant IDs
      const { data: attorneys } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id, firm_name, email')
        .eq('user_id', userId);

      if (!attorneys || attorneys.length === 0) {
        this.log('⚠️ No attorney records found for Vapi cleanup');
        return;
      }

      // For each assistant, we would update via Vapi API
      // Note: This requires the Vapi MCP service to be available
      for (const attorney of attorneys) {
        if (attorney.vapi_assistant_id) {
          this.log(`🔧 Would update Vapi assistant: ${attorney.vapi_assistant_id}`);
          this.log(`   New name: ${attorney.firm_name} Legal Assistant`);
          
          // TODO: Implement actual Vapi update when MCP service is available
          // const updatedAssistant = await vapiService.updateAssistant(
          //   attorney.vapi_assistant_id,
          //   {
          //     name: `${attorney.firm_name} Legal Assistant`,
          //     firstMessage: `Hello! I'm Scout from ${attorney.firm_name}. How can I help you today?`,
          //     instructions: `You are Scout, a legal assistant for ${attorney.firm_name}...`
          //   }
          // );
        }
      }

    } catch (error) {
      this.log(`❌ Error in Vapi cleanup: ${error.message}`);
    }
  }

  /**
   * Create proper user/firm separation
   * @param {string} userId - User ID
   */
  async createProperSeparation(userId) {
    this.log('🏗️ Creating proper user/firm separation...');

    try {
      // Use the separation service to create clean profiles
      const { userProfile, firmProfile } = await userFirmSeparationService.cleanupContaminatedData(userId);

      this.log(`✅ Created user profile: ${userProfile.email}`);
      this.log(`✅ Created firm profile: ${firmProfile.firm_name}`);

    } catch (error) {
      this.log(`❌ Error in separation creation: ${error.message}`);
    }
  }

  /**
   * Verify cleanup was successful
   * @param {string} userId - User ID
   */
  async verifyCleanup(userId) {
    this.log('✅ Verifying cleanup...');

    try {
      // Check attorney records
      const { data: attorneys } = await supabase
        .from('attorneys')
        .select('firm_name, email, vapi_assistant_id')
        .eq('user_id', userId);

      if (attorneys && attorneys.length > 0) {
        attorneys.forEach(attorney => {
          this.log(`📋 Verified attorney: ${attorney.firm_name} (${attorney.email})`);
          
          // Check for remaining contamination
          if (attorney.firm_name && attorney.firm_name.toLowerCase().includes('pizza')) {
            this.log(`🚨 WARNING: Contamination still present in ${attorney.firm_name}`);
          } else {
            this.log(`✅ Clean: ${attorney.firm_name}`);
          }
        });
      }

    } catch (error) {
      this.log(`❌ Error in verification: ${error.message}`);
    }
  }

  /**
   * Get correct firm name for user email
   * @param {string} email - User email
   * @returns {string} Correct firm name
   */
  getCorrectFirmName(email) {
    const firmMappings = {
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'Network Legal'
    };

    return firmMappings[email] || 'LegalScout';
  }

  /**
   * Log cleanup actions
   * @param {string} message - Log message
   */
  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    this.cleanupLog.push(logEntry);
  }

  /**
   * Quick fix for immediate contamination
   * @param {string} userEmail - User email
   */
  async quickFixContamination(userEmail) {
    console.log('🚀 Quick fix for contamination...');

    try {
      const correctFirmName = this.getCorrectFirmName(userEmail);
      
      // Update all attorney records for this email
      const { data, error } = await supabase
        .from('attorneys')
        .update({
          firm_name: correctFirmName,
          welcome_message: `Hello! I'm Scout from ${correctFirmName}. How can I help you with your legal needs today?`,
          vapi_instructions: `You are Scout, a legal assistant for ${correctFirmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
          updated_at: new Date().toISOString()
        })
        .eq('email', userEmail)
        .select();

      if (error) {
        console.error('❌ Quick fix failed:', error);
        return false;
      }

      console.log(`✅ Quick fix applied to ${data?.length || 0} records`);
      console.log(`✅ Firm name corrected to: ${correctFirmName}`);
      
      return true;

    } catch (error) {
      console.error('❌ Quick fix error:', error);
      return false;
    }
  }
}

// Export utility functions
export const dataCleanupUtility = new DataCleanupUtility();

// Quick fix function for immediate use
export const quickFixContamination = async (userEmail = '<EMAIL>') => {
  return await dataCleanupUtility.quickFixContamination(userEmail);
};

// Full cleanup function
export const fullDataCleanup = async (userId) => {
  return await dataCleanupUtility.cleanupUserData(userId);
};
