import React from 'react';

/**
 * Enhanced Fallback for Framer Motion
 *
 * This provides basic animation capabilities without using Framer Motion
 * and includes mock implementations of all Framer Motion features
 * including LayoutGroupContext to prevent errors.
 */

// Create a mock LayoutGroupContext if it doesn't exist globally
if (typeof window !== 'undefined' && !window.LayoutGroupContext) {
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext'
  };
  console.log('[FramerMotionFallback] Created global LayoutGroupContext');
}

// Export LayoutGroupContext for direct imports
export const LayoutGroupContext = window.LayoutGroupContext || {
  Provider: function(props) { return props.children || null; },
  Consumer: function(props) { return props.children ? props.children({}) : null; },
  displayName: 'LayoutGroupContext'
};

/**
 * Fallback component to use when Framer Motion fails to load
 * This provides basic animation capabilities without using Framer Motion
 */
const FramerMotionFallback = ({ children, className, style, initial, animate, exit, transition, layoutId, layout, layoutDependency, ...props }) => {
  // Create a combined style object from the style prop and animation props
  const combinedStyle = {
    ...style,
    transition: transition ? `all ${transition.duration || 0.3}s ease` : 'all 0.3s ease',
  };

  // Apply opacity if specified in animate
  if (animate && typeof animate.opacity !== 'undefined') {
    combinedStyle.opacity = animate.opacity;
  } else if (initial && typeof initial.opacity !== 'undefined') {
    combinedStyle.opacity = initial.opacity;
  }

  // Apply transform if specified in animate
  if (animate) {
    const transforms = [];

    if (typeof animate.x !== 'undefined') {
      transforms.push(`translateX(${animate.x}px)`);
    } else if (initial && typeof initial.x !== 'undefined') {
      transforms.push(`translateX(${initial.x}px)`);
    }

    if (typeof animate.y !== 'undefined') {
      transforms.push(`translateY(${animate.y}px)`);
    } else if (initial && typeof initial.y !== 'undefined') {
      transforms.push(`translateY(${initial.y}px)`);
    }

    if (typeof animate.scale !== 'undefined') {
      transforms.push(`scale(${animate.scale})`);
    } else if (initial && typeof initial.scale !== 'undefined') {
      transforms.push(`scale(${initial.scale})`);
    }

    if (typeof animate.rotate !== 'undefined') {
      transforms.push(`rotate(${animate.rotate}deg)`);
    } else if (initial && typeof initial.rotate !== 'undefined') {
      transforms.push(`rotate(${initial.rotate}deg)`);
    }

    if (transforms.length > 0) {
      combinedStyle.transform = transforms.join(' ');
    }
  }

  // Get the appropriate element type from props or default to div
  const Element = props.as || 'div';
  delete props.as; // Remove 'as' prop before spreading

  return (
    <Element className={className} style={combinedStyle} {...props}>
      {children}
    </Element>
  );
};

// Create a proxy-based motion object that handles any component type
const motion = new Proxy({}, {
  get: function(target, prop) {
    // Return a component that renders the appropriate HTML element
    return (props) => <FramerMotionFallback as={prop} {...props} />;
  }
});

// Add specific implementations for common components
motion.div = (props) => <FramerMotionFallback as="div" {...props} />;
motion.span = (props) => <FramerMotionFallback as="span" {...props} />;
motion.button = (props) => <FramerMotionFallback as="button" {...props} />;
motion.a = (props) => <FramerMotionFallback as="a" {...props} />;
motion.ul = (props) => <FramerMotionFallback as="ul" {...props} />;
motion.li = (props) => <FramerMotionFallback as="li" {...props} />;
motion.p = (props) => <FramerMotionFallback as="p" {...props} />;
motion.h1 = (props) => <FramerMotionFallback as="h1" {...props} />;
motion.h2 = (props) => <FramerMotionFallback as="h2" {...props} />;
motion.h3 = (props) => <FramerMotionFallback as="h3" {...props} />;
motion.img = (props) => <FramerMotionFallback as="img" {...props} />;
motion.svg = (props) => <FramerMotionFallback as="svg" {...props} />;
motion.path = (props) => <FramerMotionFallback as="path" {...props} />;

// Create AnimatePresence component
const AnimatePresence = ({ children, mode, initial, onExitComplete }) => {
  return <>{children}</>;
};

// Create LayoutGroup component
const LayoutGroup = ({ children, id }) => {
  return <>{children}</>;
};

// Create utility hooks
const useAnimation = () => ({
  start: () => Promise.resolve(),
  stop: () => {},
  set: () => {}
});

const useMotionValue = (initial) => ({
  get: () => initial,
  set: () => {},
  onChange: () => {}
});

const useTransform = () => ({
  get: () => 0
});

const useSpring = () => ({
  get: () => 0
});

const useScroll = () => ({
  scrollY: {
    get: () => 0,
    onChange: () => {}
  },
  scrollYProgress: {
    get: () => 0,
    onChange: () => {}
  }
});

const useCycle = (...args) => [args[0], () => {}];

// Export all components and hooks
export {
  motion,
  AnimatePresence,
  LayoutGroup,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useScroll,
  useCycle
};

export default FramerMotionFallback;
