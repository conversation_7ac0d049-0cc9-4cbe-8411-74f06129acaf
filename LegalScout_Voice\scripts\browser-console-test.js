/**
 * <PERSON><PERSON>er Console Test for Consultations
 * 
 * Copy and paste this code into your browser console while on the dashboard
 * to diagnose consultation loading issues in real-time.
 */

// Test function to run in browser console
async function testConsultationsInBrowser() {
  console.log('🔍 Testing Consultations in Browser...\n');

  try {
    // Check if supabase is available
    if (typeof window.supabase === 'undefined') {
      console.error('❌ Supabase client not found on window object');
      console.log('💡 Try accessing from React DevTools or check if supabase is imported');
      return;
    }

    const supabase = window.supabase;

    // 1. Check current user
    console.log('1️⃣ Checking current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('❌ Error getting user:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ No authenticated user found');
      return;
    }
    
    console.log('✅ Current user:', user.email);

    // 2. Find attorney record
    console.log('\n2️⃣ Finding attorney record...');
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();

    if (attorneyError) {
      console.error('❌ Error finding attorney:', attorneyError);
      console.log('Trying alternative emails...');
      
      // Try alternative emails
      const altEmails = ['<EMAIL>', '<EMAIL>'];
      let foundAttorney = null;
      
      for (const email of altEmails) {
        const { data: altAttorney, error: altError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('email', email)
          .single();
        
        if (!altError && altAttorney) {
          foundAttorney = altAttorney;
          console.log(`✅ Found attorney with email: ${email}`);
          break;
        }
      }
      
      if (!foundAttorney) {
        console.error('❌ No attorney record found for any email');
        return;
      }
      
      var currentAttorney = foundAttorney;
    } else {
      var currentAttorney = attorney;
      console.log('✅ Attorney found:', currentAttorney.name);
    }

    console.log('Attorney ID:', currentAttorney.id);

    // 3. Test consultations query
    console.log('\n3️⃣ Testing consultations query...');
    const { data: consultations, error: consultationError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', currentAttorney.id)
      .order('created_at', { ascending: false });

    if (consultationError) {
      console.error('❌ Error fetching consultations:', consultationError);
      return;
    }

    console.log(`✅ Found ${consultations.length} consultations`);
    
    if (consultations.length > 0) {
      console.log('📋 Latest consultations:');
      consultations.slice(0, 5).forEach((consultation, index) => {
        console.log(`  ${index + 1}. ${consultation.client_name} - ${consultation.created_at} (${consultation.status})`);
      });
    } else {
      console.log('❌ No consultations found for this attorney');
    }

    // 4. Check all consultations in database
    console.log('\n4️⃣ Checking all consultations in database...');
    const { data: allConsultations, error: allError } = await supabase
      .from('consultations')
      .select('id, attorney_id, client_name, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (allError) {
      console.error('❌ Error fetching all consultations:', allError);
    } else {
      console.log(`📊 Total recent consultations in DB: ${allConsultations.length}`);
      allConsultations.forEach(c => {
        const isYours = c.attorney_id === currentAttorney.id ? '👤 YOURS' : '';
        console.log(`  - ${c.client_name} (${c.attorney_id}) ${isYours}`);
      });
    }

    // 5. Check call_records
    console.log('\n5️⃣ Checking call records...');
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('*')
      .eq('attorney_id', currentAttorney.id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (callError) {
      console.error('❌ Error fetching call records:', callError);
    } else {
      console.log(`📞 Found ${callRecords.length} call records`);
      callRecords.forEach(record => {
        console.log(`  - Call ${record.call_id} - ${record.status} (${record.created_at})`);
      });
    }

    // 6. Test real-time subscription
    console.log('\n6️⃣ Testing real-time subscription...');
    const channel = supabase
      .channel('test-consultations')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'consultations',
          filter: `attorney_id=eq.${currentAttorney.id}`
        },
        (payload) => {
          console.log('🔔 Real-time update received:', payload);
        }
      )
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
      });

    // 7. Create test consultation
    console.log('\n7️⃣ Creating test consultation...');
    const testConsultation = {
      attorney_id: currentAttorney.id,
      client_name: 'Browser Test Client',
      summary: 'Test consultation created from browser console',
      status: 'new'
    };

    const { data: newConsultation, error: insertError } = await supabase
      .from('consultations')
      .insert(testConsultation)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Error creating test consultation:', insertError);
    } else {
      console.log('✅ Test consultation created:', newConsultation.id);
      
      // Wait a moment then clean up
      setTimeout(async () => {
        await supabase
          .from('consultations')
          .delete()
          .eq('id', newConsultation.id);
        console.log('🧹 Test consultation cleaned up');
      }, 3000);
    }

    // 8. Summary
    console.log('\n📋 BROWSER TEST SUMMARY:');
    console.log('='.repeat(50));
    console.log(`Current User: ${user.email}`);
    console.log(`Attorney ID: ${currentAttorney.id}`);
    console.log(`Consultations Found: ${consultations.length}`);
    console.log(`Call Records Found: ${callRecords?.length || 0}`);
    
    if (consultations.length === 0) {
      console.log('\n❌ ISSUE: No consultations found');
      console.log('💡 POSSIBLE CAUSES:');
      console.log('   - Webhook not configured properly');
      console.log('   - Calls not being processed');
      console.log('   - Wrong attorney ID being used');
      console.log('   - Data in different table (check briefs table)');
    } else {
      console.log('\n✅ CONSULTATIONS FOUND: Check if UI is updating');
      console.log('💡 IF UI NOT UPDATING:');
      console.log('   - Check React component state');
      console.log('   - Verify useEffect dependencies');
      console.log('   - Check for JavaScript errors');
    }

    // Clean up subscription
    setTimeout(() => {
      supabase.removeChannel(channel);
      console.log('🔌 Real-time subscription cleaned up');
    }, 5000);

  } catch (error) {
    console.error('❌ Browser test failed:', error);
  }
}

// Instructions for use
console.log(`
🔧 BROWSER CONSOLE TEST INSTRUCTIONS:
=====================================

1. Open your browser's Developer Tools (F12)
2. Go to the Console tab
3. Make sure you're on the LegalScout dashboard page
4. Copy and paste this entire script into the console
5. Run: testConsultationsInBrowser()

This will test:
- User authentication
- Attorney record lookup
- Consultations query
- Real-time subscriptions
- Test data creation

The results will help identify why consultations aren't showing up.
`);

// Auto-run if in browser environment
if (typeof window !== 'undefined' && window.location) {
  console.log('🚀 Auto-running browser test...');
  testConsultationsInBrowser();
}
