/**
 * Production-specific main entry point
 * This file ensures proper initialization order for production builds
 */

import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'

// Critical: Import CSS first
import './index.css'
import 'leaflet/dist/leaflet.css'
import './fixes/interactionFix.css'

// Import error boundaries first
import ErrorBoundary from './utils/ErrorBoundary.jsx'
import ProductionErrorBoundary from './components/ProductionErrorBoundary.jsx'

// Import contexts
import { ThemeProvider } from './contexts/ThemeContext.jsx'
import { AttorneyStateProvider } from './contexts/AttorneyStateContext.jsx'
import SyncAuthProvider from './components/SyncAuthProvider.jsx'

// Import main app component last
import App from './App.jsx'

// Import schema generator to make it available globally
import './utils/schemaGenerator.js'

// Production-specific initialization
function initializeProduction() {
  console.log('🚀 [Production] Initializing production environment...');
  
  // Ensure global objects exist
  if (typeof window !== 'undefined') {
    // Process polyfill
    if (!window.process) {
      window.process = {
        env: { NODE_ENV: 'production' },
        browser: true,
        version: '',
        versions: { node: '' }
      };
    }
    
    // Global flag for production mode
    window.__PRODUCTION_MODE__ = true;
    
    console.log('✅ [Production] Environment initialized');
  }
}

// Initialize production environment
initializeProduction();

// Production-safe React rendering
function renderApp() {
  try {
    const rootElement = document.getElementById('root');
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    const root = ReactDOM.createRoot(rootElement);
    
    root.render(
      <React.StrictMode>
        <ProductionErrorBoundary>
          <ErrorBoundary showDetails={false} onReset={() => window.location.reload()}>
            <BrowserRouter>
              <ThemeProvider>
                <AttorneyStateProvider>
                  <SyncAuthProvider>
                    <App />
                  </SyncAuthProvider>
                </AttorneyStateProvider>
              </ThemeProvider>
            </BrowserRouter>
          </ErrorBoundary>
        </ProductionErrorBoundary>
      </React.StrictMode>
    );
    
    console.log('✅ [Production] React app rendered successfully');
  } catch (error) {
    console.error('❌ [Production] Failed to render React app:', error);
    
    // Fallback error display
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          font-family: Arial, sans-serif;
          text-align: center;
        ">
          <h1>LegalScout</h1>
          <p>Loading application...</p>
          <button onclick="window.location.reload()" style="
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
          ">Refresh Page</button>
        </div>
      `;
    }
  }
}

// Wait for DOM to be ready, then render
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', renderApp);
} else {
  renderApp();
}
