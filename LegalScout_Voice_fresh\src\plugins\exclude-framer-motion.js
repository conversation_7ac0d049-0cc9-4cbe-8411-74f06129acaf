/**
 * Vite plugin to exclude framer-motion from the build
 * 
 * This plugin completely excludes framer-motion from the build,
 * replacing it with empty modules that don't cause errors.
 */

export default function excludeFramerMotion() {
  const framerId = 'framer-motion';
  const virtualModuleId = 'virtual:framer-motion-stub';
  const resolvedVirtualModuleId = '\0' + virtualModuleId;

  return {
    name: 'vite-plugin-exclude-framer-motion',
    
    resolveId(id) {
      // Check if the import is for framer-motion or any of its submodules
      if (id === framerId || id.startsWith(framerId + '/')) {
        console.log(`[ExcludeFramerMotion] Intercepting import: ${id}`);
        return resolvedVirtualModuleId;
      }
      
      // Check for specific problematic modules
      if (id.includes('MotionConfigContext') || id.includes('LayoutGroupContext')) {
        console.log(`[ExcludeFramerMotion] Intercepting specific module: ${id}`);
        return resolvedVirtualModuleId;
      }
      
      return null;
    },
    
    load(id) {
      if (id === resolvedVirtualModuleId) {
        console.log('[ExcludeFramerMotion] Providing empty module stub');
        
        // Create a stub module that exports empty components and functions
        return `
          // Empty stub for framer-motion
          const noop = () => {};
          const Component = (props) => props.children || null;
          
          // Create a mock context
          const createContext = (defaultValue) => ({
            Provider: Component,
            Consumer: Component,
            displayName: 'MockContext',
            _currentValue: defaultValue,
            _currentValue2: defaultValue,
            _threadCount: 0,
            _defaultValue: defaultValue
          });
          
          // Create mock contexts
          export const MotionConfigContext = createContext({});
          export const LayoutGroupContext = createContext({});
          
          // Export common components and functions
          export const motion = new Proxy({}, {
            get: (target, prop) => Component
          });
          
          export const AnimatePresence = Component;
          export const MotionConfig = Component;
          export const LazyMotion = Component;
          export const m = motion;
          
          // Export common hooks and utilities
          export const useAnimation = () => ({ start: noop, stop: noop });
          export const useMotionValue = () => ({ get: () => 0, set: noop });
          export const useTransform = () => 0;
          export const useSpring = () => ({ get: () => 0, set: noop });
          export const useCycle = () => [0, noop];
          export const useMotionTemplate = () => '';
          export const useInView = () => ({ inView: false, ref: {} });
          
          // Export common variants
          export const Variants = {};
          
          // Default export
          export default {
            motion,
            AnimatePresence,
            MotionConfig,
            LazyMotion,
            m,
            useAnimation,
            useMotionValue,
            useTransform,
            useSpring,
            useCycle,
            useMotionTemplate,
            useInView,
            Variants,
            MotionConfigContext,
            LayoutGroupContext
          };
        `;
      }
      
      return null;
    }
  };
}
