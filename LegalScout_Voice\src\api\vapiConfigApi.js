/**
 * Vapi Configuration API
 * 
 * API routes for fetching and managing Vapi configuration.
 */

import { supabase } from '../lib/supabase';

/**
 * Fetch Vapi configuration from the MCP server
 * @returns {Promise<Object>} The Vapi configuration
 */
export async function fetchVapiConfig() {
  try {
    // Use the MCP server to fetch Vapi configuration
    const response = await fetch('/vapi-mcp-server/list_assistants');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch Vapi assistants: ${response.status} ${response.statusText}`);
    }
    
    const assistants = await response.json();
    
    // Get voice providers from assistants
    const voiceProviders = extractVoiceProviders(assistants);
    
    // Get LLM providers from assistants
    const llmProviders = extractLlmProviders(assistants);
    
    // Get default voice and LLM
    const defaultVoice = getDefaultVoice(assistants);
    const defaultLlm = getDefaultLlm(assistants);
    
    return {
      voiceProviders,
      llmProviders,
      defaultVoice,
      defaultLlm
    };
  } catch (error) {
    console.error('[VapiConfigApi] Error fetching Vapi configuration:', error);
    throw error;
  }
}

/**
 * Extract voice providers from assistants
 * @param {Array} assistants - The assistants
 * @returns {Array} The voice providers
 */
function extractVoiceProviders(assistants) {
  const providersMap = new Map();
  
  // Extract voice providers from assistants
  assistants.forEach(assistant => {
    if (assistant.voice && assistant.voice.provider) {
      const providerId = assistant.voice.provider;
      
      if (!providersMap.has(providerId)) {
        providersMap.set(providerId, {
          id: providerId,
          name: getProviderName(providerId),
          voices: new Map()
        });
      }
      
      // Add voice to provider
      if (assistant.voice.voiceId) {
        const provider = providersMap.get(providerId);
        const voiceId = assistant.voice.voiceId;
        
        if (!provider.voices.has(voiceId)) {
          provider.voices.set(voiceId, {
            id: voiceId,
            name: getVoiceName(providerId, voiceId)
          });
        }
      }
    }
  });
  
  // Convert to array
  return Array.from(providersMap.values()).map(provider => ({
    id: provider.id,
    name: provider.name,
    voices: Array.from(provider.voices.values())
  }));
}

/**
 * Extract LLM providers from assistants
 * @param {Array} assistants - The assistants
 * @returns {Array} The LLM providers
 */
function extractLlmProviders(assistants) {
  const providersMap = new Map();
  
  // Extract LLM providers from assistants
  assistants.forEach(assistant => {
    if (assistant.llm) {
      let providerId, modelId;
      
      // Handle string or object LLM
      if (typeof assistant.llm === 'string') {
        // Parse string LLM (e.g., "openai/gpt-4o")
        const parts = assistant.llm.split('/');
        providerId = parts[0];
        modelId = parts[1];
      } else if (assistant.llm.provider && assistant.llm.model) {
        providerId = assistant.llm.provider;
        modelId = assistant.llm.model;
      }
      
      if (providerId && modelId) {
        if (!providersMap.has(providerId)) {
          providersMap.set(providerId, {
            id: providerId,
            name: getLlmProviderName(providerId),
            models: new Map()
          });
        }
        
        // Add model to provider
        const provider = providersMap.get(providerId);
        
        if (!provider.models.has(modelId)) {
          provider.models.set(modelId, {
            id: modelId,
            name: getModelName(providerId, modelId)
          });
        }
      }
    }
  });
  
  // Convert to array
  return Array.from(providersMap.values()).map(provider => ({
    id: provider.id,
    name: provider.name,
    models: Array.from(provider.models.values())
  }));
}

/**
 * Get default voice from assistants
 * @param {Array} assistants - The assistants
 * @returns {Object} The default voice
 */
function getDefaultVoice(assistants) {
  // Find the most common voice
  const voiceCounts = new Map();
  
  assistants.forEach(assistant => {
    if (assistant.voice && assistant.voice.provider && assistant.voice.voiceId) {
      const key = `${assistant.voice.provider}/${assistant.voice.voiceId}`;
      voiceCounts.set(key, (voiceCounts.get(key) || 0) + 1);
    }
  });
  
  // Get the most common voice
  let mostCommonVoice = null;
  let maxCount = 0;
  
  voiceCounts.forEach((count, key) => {
    if (count > maxCount) {
      maxCount = count;
      mostCommonVoice = key;
    }
  });
  
  // Parse the most common voice
  if (mostCommonVoice) {
    const [provider, voiceId] = mostCommonVoice.split('/');
    return { provider, voiceId };
  }
  
  // Default to 11labs/sarah
  return { provider: '11labs', voiceId: 'sarah' };
}

/**
 * Get default LLM from assistants
 * @param {Array} assistants - The assistants
 * @returns {Object} The default LLM
 */
function getDefaultLlm(assistants) {
  // Find the most common LLM
  const llmCounts = new Map();
  
  assistants.forEach(assistant => {
    if (assistant.llm) {
      let key;
      
      if (typeof assistant.llm === 'string') {
        key = assistant.llm;
      } else if (assistant.llm.provider && assistant.llm.model) {
        key = `${assistant.llm.provider}/${assistant.llm.model}`;
      }
      
      if (key) {
        llmCounts.set(key, (llmCounts.get(key) || 0) + 1);
      }
    }
  });
  
  // Get the most common LLM
  let mostCommonLlm = null;
  let maxCount = 0;
  
  llmCounts.forEach((count, key) => {
    if (count > maxCount) {
      maxCount = count;
      mostCommonLlm = key;
    }
  });
  
  // Parse the most common LLM
  if (mostCommonLlm) {
    const [provider, model] = mostCommonLlm.split('/');
    return { provider, model };
  }
  
  // Default to openai/gpt-4o
  return { provider: 'openai', model: 'gpt-4o' };
}

/**
 * Get provider name
 * @param {string} providerId - The provider ID
 * @returns {string} The provider name
 */
function getProviderName(providerId) {
  const providers = {
    '11labs': 'Eleven Labs',
    'openai': 'OpenAI',
    'azure': 'Azure',
    'deepgram': 'Deepgram',
    'hume': 'Hume',
    'lmnt': 'LMNT',
    'neuphonic': 'Neuphonic',
    'playht': 'Play.ht',
    'rime-ai': 'Rime AI',
    'smallest-ai': 'Smallest AI',
    'tavus': 'Tavus',
    'sesame': 'Sesame'
  };
  
  return providers[providerId] || providerId;
}

/**
 * Get voice name
 * @param {string} providerId - The provider ID
 * @param {string} voiceId - The voice ID
 * @returns {string} The voice name
 */
function getVoiceName(providerId, voiceId) {
  // Common voice names for 11labs
  if (providerId === '11labs') {
    const voices = {
      'sarah': 'Sarah',
      'josh': 'Josh',
      'rachel': 'Rachel',
      'adam': 'Adam',
      'antoni': 'Antoni',
      'bella': 'Bella',
      'charlie': 'Charlie',
      'dorothy': 'Dorothy',
      'emily': 'Emily',
      'fin': 'Fin',
      'gigi': 'Gigi',
      'harry': 'Harry'
    };
    
    return voices[voiceId] || voiceId;
  }
  
  // Common voice names for OpenAI
  if (providerId === 'openai') {
    const voices = {
      'alloy': 'Alloy',
      'echo': 'Echo',
      'fable': 'Fable',
      'onyx': 'Onyx',
      'nova': 'Nova',
      'shimmer': 'Shimmer'
    };
    
    return voices[voiceId] || voiceId;
  }
  
  return voiceId;
}

/**
 * Get LLM provider name
 * @param {string} providerId - The provider ID
 * @returns {string} The provider name
 */
function getLlmProviderName(providerId) {
  const providers = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic',
    'google': 'Google',
    'azure': 'Azure',
    'cohere': 'Cohere',
    'mistral': 'Mistral'
  };
  
  return providers[providerId] || providerId;
}

/**
 * Get model name
 * @param {string} providerId - The provider ID
 * @param {string} modelId - The model ID
 * @returns {string} The model name
 */
function getModelName(providerId, modelId) {
  // Common model names for OpenAI
  if (providerId === 'openai') {
    const models = {
      'gpt-4o': 'GPT-4o',
      'gpt-4o-mini': 'GPT-4o Mini',
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo'
    };
    
    return models[modelId] || modelId;
  }
  
  // Common model names for Anthropic
  if (providerId === 'anthropic') {
    const models = {
      'claude-3-7-sonnet-20250219': 'Claude 3.7 Sonnet',
      'claude-3-5-haiku-20241022': 'Claude 3.5 Haiku',
      'claude-3-opus': 'Claude 3 Opus',
      'claude-3-sonnet': 'Claude 3 Sonnet',
      'claude-3-haiku': 'Claude 3 Haiku'
    };
    
    return models[modelId] || modelId;
  }
  
  // Common model names for Google
  if (providerId === 'google') {
    const models = {
      'gemini-1.5-pro': 'Gemini 1.5 Pro',
      'gemini-1.5-flash': 'Gemini 1.5 Flash',
      'gemini-2.0-flash': 'Gemini 2.0 Flash',
      'gemini-2.0-pro': 'Gemini 2.0 Pro'
    };
    
    return models[modelId] || modelId;
  }
  
  return modelId;
}

/**
 * Create a Vapi assistant
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} The created assistant
 */
export async function createVapiAssistant(config) {
  try {
    // Use the MCP server to create a Vapi assistant
    const response = await fetch('/vapi-mcp-server/create_assistant', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create Vapi assistant: ${response.status} ${response.statusText}`);
    }
    
    const assistant = await response.json();
    
    return assistant;
  } catch (error) {
    console.error('[VapiConfigApi] Error creating Vapi assistant:', error);
    throw error;
  }
}

/**
 * Update a Vapi assistant
 * @param {string} assistantId - The assistant ID
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} The updated assistant
 */
export async function updateVapiAssistant(assistantId, config) {
  try {
    // First get the current assistant
    const currentAssistant = await getVapiAssistant(assistantId);
    
    if (!currentAssistant) {
      throw new Error(`Assistant not found: ${assistantId}`);
    }
    
    // Merge with current assistant
    const updatedConfig = {
      ...currentAssistant,
      ...config,
      assistantId
    };
    
    // Use the MCP server to update a Vapi assistant
    const response = await fetch(`/vapi-mcp-server/update_assistant`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updatedConfig)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update Vapi assistant: ${response.status} ${response.statusText}`);
    }
    
    const assistant = await response.json();
    
    return assistant;
  } catch (error) {
    console.error('[VapiConfigApi] Error updating Vapi assistant:', error);
    throw error;
  }
}

/**
 * Get a Vapi assistant
 * @param {string} assistantId - The assistant ID
 * @returns {Promise<Object>} The assistant
 */
export async function getVapiAssistant(assistantId) {
  try {
    // Use the MCP server to get a Vapi assistant
    const response = await fetch(`/vapi-mcp-server/get_assistant?assistantId=${assistantId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to get Vapi assistant: ${response.status} ${response.statusText}`);
    }
    
    const assistant = await response.json();
    
    return assistant;
  } catch (error) {
    console.error('[VapiConfigApi] Error getting Vapi assistant:', error);
    throw error;
  }
}

// Export a default object for convenience
export default {
  fetchVapiConfig,
  createVapiAssistant,
  updateVapiAssistant,
  getVapiAssistant
};
