/**
 * Enhanced mock implementation of framer-motion
 * This file provides functional implementations of all the components and hooks from framer-motion
 */

import { LayoutGroupContext } from './LayoutGroupContext';
import { MotionConfigContext } from './MotionConfigContext';
import React from 'react';

// Create empty mock components and functions
const noop = () => {};

// Create a more functional motion component that passes props to children
const createMotionComponent = (type) => {
  const MotionComponent = ({ children, ...props }) => {
    // Extract animation props that we want to handle
    const {
      initial, animate, exit, transition, variants,
      whileHover, whileTap, whileFocus, whileDrag, whileInView,
      ...otherProps
    } = props;

    // Pass all other props to the underlying component
    return React.createElement(
      type,
      otherProps,
      children
    );
  };
  return MotionComponent;
};

// Create a proxy for motion that returns appropriate components
export const motion = new Proxy({}, {
  get: (target, prop) => {
    if (prop === 'custom') return createMotionComponent('div');
    if (prop === 'div') return createMotionComponent('div');
    if (prop === 'span') return createMotionComponent('span');
    if (prop === 'button') return createMotionComponent('button');
    if (prop === 'a') return createMotionComponent('a');
    if (prop === 'ul') return createMotionComponent('ul');
    if (prop === 'li') return createMotionComponent('li');
    if (prop === 'p') return createMotionComponent('p');
    if (prop === 'h1') return createMotionComponent('h1');
    if (prop === 'h2') return createMotionComponent('h2');
    if (prop === 'h3') return createMotionComponent('h3');
    if (prop === 'h4') return createMotionComponent('h4');
    if (prop === 'h5') return createMotionComponent('h5');
    if (prop === 'h6') return createMotionComponent('h6');
    if (prop === 'img') return createMotionComponent('img');
    if (prop === 'svg') return createMotionComponent('svg');
    if (prop === 'path') return createMotionComponent('path');
    if (prop === 'circle') return createMotionComponent('circle');
    if (prop === 'rect') return createMotionComponent('rect');
    if (prop === 'line') return createMotionComponent('line');
    if (prop === 'polyline') return createMotionComponent('polyline');
    if (prop === 'polygon') return createMotionComponent('polygon');
    if (prop === 'g') return createMotionComponent('g');
    if (prop === 'defs') return createMotionComponent('defs');
    if (prop === 'mask') return createMotionComponent('mask');
    if (prop === 'clipPath') return createMotionComponent('clipPath');
    if (prop === 'linearGradient') return createMotionComponent('linearGradient');
    if (prop === 'radialGradient') return createMotionComponent('radialGradient');
    if (prop === 'stop') return createMotionComponent('stop');
    if (prop === 'filter') return createMotionComponent('filter');
    if (prop === 'feGaussianBlur') return createMotionComponent('feGaussianBlur');
    if (prop === 'feOffset') return createMotionComponent('feOffset');
    if (prop === 'feComposite') return createMotionComponent('feComposite');
    if (prop === 'feColorMatrix') return createMotionComponent('feColorMatrix');
    if (prop === 'feMerge') return createMotionComponent('feMerge');
    if (prop === 'feMergeNode') return createMotionComponent('feMergeNode');
    if (prop === 'feBlend') return createMotionComponent('feBlend');
    if (prop === 'feFlood') return createMotionComponent('feFlood');

    // Default to div for unknown elements
    return createMotionComponent('div');
  }
});

// Create a functional AnimatePresence component
export const AnimatePresence = ({ children, ...props }) => {
  // Just render children directly
  return children || null;
};

// Other components
export const MotionConfig = ({ children, ...props }) => children || null;
export const LazyMotion = ({ children, ...props }) => children || null;
export const m = motion;

// Export common hooks and utilities with more functionality
export const useAnimation = () => ({
  start: noop,
  stop: noop,
  set: noop,
  get: () => ({})
});

export const useMotionValue = (initial = 0) => ({
  get: () => initial,
  set: noop,
  onChange: noop,
  current: initial
});

export const useTransform = (value, input, output) => ({
  get: () => (typeof output === 'function' ? output(0) : (output && output[0]) || 0),
  set: noop,
  onChange: noop
});

export const useSpring = (initial = 0, config = {}) => ({
  get: () => initial,
  set: noop,
  onChange: noop,
  current: initial
});

export const useCycle = (...items) => {
  return [items[0] || 0, () => {}];
};

export const useMotionTemplate = (strings, ...values) => '';

export const useInView = (options = {}) => ({
  inView: true,
  ref: { current: null },
  entry: null
});

export const useScroll = (options = {}) => ({
  scrollX: { get: () => 0, onChange: noop, current: 0 },
  scrollY: { get: () => 0, onChange: noop, current: 0 },
  scrollXProgress: { get: () => 0, onChange: noop, current: 0 },
  scrollYProgress: { get: () => 0, onChange: noop, current: 0 }
});

// Export common variants
export const Variants = {};

// Export contexts
export { LayoutGroupContext, MotionConfigContext };

// Default export
export default {
  motion,
  AnimatePresence,
  MotionConfig,
  LazyMotion,
  m,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useCycle,
  useMotionTemplate,
  useInView,
  useScroll,
  Variants,
  MotionConfigContext,
  LayoutGroupContext
};
