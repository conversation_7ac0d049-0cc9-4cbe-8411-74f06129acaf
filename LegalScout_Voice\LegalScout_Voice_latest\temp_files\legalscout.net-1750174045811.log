demo:18 🚀 [LegalScout] Initializing environment...
demo:40 ✅ [LegalScout] Environment initialized
demo:129 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
demo:216 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-ccf88fd2.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-ccf88fd2.js:48 🚀 [Supabase-Fixed] Initializing client...
index-ccf88fd2.js:48 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: false, isProduction: true, baseUrl: 'https://legalscout.net', hasUrl: true, hasKey: true}
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at index-ccf88fd2.js:48:76815
Ote @ index-ccf88fd2.js:48
VE @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at index-ccf88fd2.js:48:76815
VE @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
index-ccf88fd2.js:50 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
index-ccf88fd2.js:54 [VapiLoader] Starting Vapi SDK loading process
index-ccf88fd2.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
index-ccf88fd2.js:141 [VapiMcpService] Created clean fetch from iframe
index-ccf88fd2.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Auto-initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at index-ccf88fd2.js:48:76815
(anonymous) @ index-ccf88fd2.js:48
Promise.catch
(anonymous) @ index-ccf88fd2.js:48
index-ccf88fd2.js:48 ⚠️ [Supabase-Fixed] Client not initialized, initializing now...
get @ index-ccf88fd2.js:48
initializeRealtimeSubscriptions @ index-ccf88fd2.js:385
await in initializeRealtimeSubscriptions
D1e @ index-ccf88fd2.js:385
(anonymous) @ index-ccf88fd2.js:385
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Object.get (index-ccf88fd2.js:48:76420)
    at D1e.initializeRealtimeSubscriptions (index-ccf88fd2.js:385:47685)
Ote @ index-ccf88fd2.js:48
VE @ index-ccf88fd2.js:48
get @ index-ccf88fd2.js:48
initializeRealtimeSubscriptions @ index-ccf88fd2.js:385
await in initializeRealtimeSubscriptions
D1e @ index-ccf88fd2.js:385
(anonymous) @ index-ccf88fd2.js:385
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Object.get (index-ccf88fd2.js:48:76420)
    at D1e.initializeRealtimeSubscriptions (index-ccf88fd2.js:385:47685)
VE @ index-ccf88fd2.js:48
get @ index-ccf88fd2.js:48
initializeRealtimeSubscriptions @ index-ccf88fd2.js:385
await in initializeRealtimeSubscriptions
D1e @ index-ccf88fd2.js:385
(anonymous) @ index-ccf88fd2.js:385
index-ccf88fd2.js:385 ❌ [AssistantSyncManager] Error initializing real-time subscriptions: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Object.get (index-ccf88fd2.js:48:76425)
    at D1e.initializeRealtimeSubscriptions (index-ccf88fd2.js:385:47685)
initializeRealtimeSubscriptions @ index-ccf88fd2.js:385
await in initializeRealtimeSubscriptions
D1e @ index-ccf88fd2.js:385
(anonymous) @ index-ccf88fd2.js:385
index-ccf88fd2.js:48 ⚠️ [AssistantContextValidator] No valid assistant context found: {assistantId: null, attorneyId: undefined, subdomain: undefined, isValid: false, reason: 'no_valid_assistant_id'}
resolveAssistantContext @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
useMemo @ index-ccf88fd2.js:38
jr.useMemo @ index-ccf88fd2.js:9
UP @ index-ccf88fd2.js:48
uk @ index-ccf88fd2.js:38
nj @ index-ccf88fd2.js:40
ej @ index-ccf88fd2.js:40
ZJ @ index-ccf88fd2.js:40
px @ index-ccf88fd2.js:40
X5 @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Object.get (index-ccf88fd2.js:48:76420)
    at D1e.initializeRealtimeSubscriptions (index-ccf88fd2.js:385:47685)
Lte @ index-ccf88fd2.js:48
Rte @ index-ccf88fd2.js:48
Ote @ index-ccf88fd2.js:48
VE @ index-ccf88fd2.js:48
get @ index-ccf88fd2.js:48
initializeRealtimeSubscriptions @ index-ccf88fd2.js:385
await in initializeRealtimeSubscriptions
D1e @ index-ccf88fd2.js:385
(anonymous) @ index-ccf88fd2.js:385
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:78261
    at index-ccf88fd2.js:48:79117
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
Ote @ index-ccf88fd2.js:48
VE @ index-ccf88fd2.js:48
Ts @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:78261
    at index-ccf88fd2.js:48:79117
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
VE @ index-ccf88fd2.js:48
Ts @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:79155
    at index-ccf88fd2.js:48:79869
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
Ote @ index-ccf88fd2.js:48
VE @ index-ccf88fd2.js:48
Ts @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:79155
    at index-ccf88fd2.js:48:79869
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
VE @ index-ccf88fd2.js:48
Ts @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 🔐 [AuthContext-P1] Unexpected error checking auth: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:78261
    at index-ccf88fd2.js:48:79117
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
(anonymous) @ index-ccf88fd2.js:48
await in (anonymous)
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
index-ccf88fd2.js:48 Failed to set up auth listener: TypeError: Cannot read properties of undefined (reading 'headers')
    at new Lte (index-ccf88fd2.js:48:69953)
    at Rte (index-ccf88fd2.js:48:72420)
    at Ote (index-ccf88fd2.js:48:73424)
    at VE (index-ccf88fd2.js:48:73984)
    at Ts (index-ccf88fd2.js:48:74496)
    at index-ccf88fd2.js:48:79155
    at index-ccf88fd2.js:48:79869
    at yE (index-ccf88fd2.js:40:24270)
    at Tp (index-ccf88fd2.js:40:42393)
    at index-ccf88fd2.js:40:40710
(anonymous) @ index-ccf88fd2.js:48
await in (anonymous)
(anonymous) @ index-ccf88fd2.js:48
yE @ index-ccf88fd2.js:40
Tp @ index-ccf88fd2.js:40
(anonymous) @ index-ccf88fd2.js:40
O @ index-ccf88fd2.js:25
J @ index-ccf88fd2.js:25
