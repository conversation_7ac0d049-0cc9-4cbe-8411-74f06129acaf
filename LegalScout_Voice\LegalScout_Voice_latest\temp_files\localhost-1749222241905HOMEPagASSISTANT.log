emergency-api-key-fix.js:27 Uncaught SyntaxError: Cannot use import statement outside a module (at emergency-api-key-fix.js:27:16)
critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
(index):29 ✅ Vapi keys set globally
(index):40 ✅ Fixed assistant ID in localStorage
(index):50 ✅ Supabase keys set globally - should load correct assistant by domain
(index):61 🚀 [EMERGENCY] Starting emergency critical fixes...
(index):65 🔧 [EMERGENCY] Adding process polyfill
(index):72 ✅ [EMERGENCY] Process polyfill added
(index):83 🔧 [EMERGENCY] Development mode: false (forced production)
(index):113 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
(index):116 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:343 [StandaloneAttorneyManager] Using default Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at production-cors-fix.js:62:16)
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:105 🧹 [CleanAuthSolution] Removing expired token: sb-utopqxsvudgrtiwenlzl-auth-token
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
(index):232 Supabase loaded from CDN
(index):242 Creating Supabase client from CDN
(index):246 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:248 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:249 [VapiMcpService] API key being used: 310f0d43...
vapiMcpService.js:261 [VapiMcpService] Using direct API key for server operations: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:21 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5174'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
critical-production-fix.js:232 🚀 [CriticalProductionFix] Initializing all critical fixes...
critical-production-fix.js:19 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
critical-production-fix.js:54 ✅ [CriticalProductionFix] Vapi API key configuration fixed
critical-production-fix.js:175 🔧 [CriticalProductionFix] Fixing environment variables...
critical-production-fix.js:203 ✅ [CriticalProductionFix] Environment variables fixed
critical-production-fix.js:59 🌐 [CriticalProductionFix] Fixing CORS issues...
critical-production-fix.js:115 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
critical-production-fix.js:120 🛡️ [CriticalProductionFix] Fixing CSP issues...
critical-production-fix.js:170 ✅ [CriticalProductionFix] CSP issues fixed
critical-production-fix.js:208 📦 [CriticalProductionFix] Fixing import statement issues...
critical-production-fix.js:227 ✅ [CriticalProductionFix] Import statement issues fixed
critical-production-fix.js:241 🎉 [CriticalProductionFix] All critical fixes applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
index.ts:5 Loaded contentScript
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
:5174/home:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
supabase.js:116 Supabase connection test successful!
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (3) ['damonkost', 'generalcounselonline', 'scout']
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
ActiveCheckHelper.ts:21 received intentional event
debugConfig.js:30 [App] Call transition started {timestamp: '2025-06-06T15:03:42.569Z'}
debugConfig.js:88 User Journey [call_transition_started]: {timestamp: '2025-06-06T15:03:42.570Z', __DEV__: {…}}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
ActiveCheckHelper.ts:8 updating page active status
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
debugConfig.js:30 [App] Call started {timestamp: '2025-06-06T15:03:44.992Z'}
debugConfig.js:88 User Journey [call_started]: {timestamp: '2025-06-06T15:03:44.994Z', __DEV__: {…}}
App.jsx:875 [App] Starting call - setting callActive to true
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
callDebugger.js:75 [CallDebugger:VapiCall] Debugger initialized
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:75 [CallDebugger:VapiCall] Using default assistant with minimal overrides
VapiCall.jsx:96 VapiCall: Using default assistant with minimal overrides
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:795 [useVapiCall] Checking for Vapi public key...
useVapiCall.js:803 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
useVapiCall.js:804 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
useVapiCall.js:805 [useVapiCall] Using API key: 310f0d43...
useVapiCall.js:815 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
useVapiCall.js:817 [useVapiCall] Creating Vapi instance directly using official pattern
useVapiCall.js:821 [useVapiCall] Loading Vapi SDK using vapiLoader
vapiLoader.js:18 [VapiLoader] Vapi SDK already loaded
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:248 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:249 [VapiMcpService] API key being used: 310f0d43...
vapiMcpService.js:261 [VapiMcpService] Using direct API key for server operations: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: {}
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:75 [CallDebugger:VapiCall] Using default assistant with minimal overrides
VapiCall.jsx:96 VapiCall: Using default assistant with minimal overrides
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:248 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:249 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: {}
useVapiCall.js:826 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
vapiLoader.js:165 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
vapiEmissionsService.js:35 VapiEmissionsService: Initialized successfully
vapiEmissionsService.js:35 VapiEmissionsService: Initialized successfully
useVapiCall.js:831 [useVapiCall] Vapi instance created successfully
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:317 Window message received for processing: {source: 'react-devtools-bridge', payload: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:1102 Requesting microphone permission...
useVapiCall.js:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 1200ms before starting call
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:1107 Available audio input devices: (5) [{…}, {…}, {…}, {…}, {…}]
useVapiCall.js:1125 Using audio constraints: {audio: {…}}
App.jsx:880 [App] Forcing call-card-container to be visible
useVapiCall.js:1129 Microphone permission granted with device: Default - Microphone (Yeti Classic)
useVapiCall.js:1177 Starting call with existing Vapi instance using direct pattern
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/call/web
critical-production-fix.js:48 [CriticalProductionFix] Using PUBLIC key for client operations: client
critical-production-fix.js:49 [CriticalProductionFix] PUBLIC key value: 310f0d43...
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantOverrides: 'Not set', vapi: true, processedConfig: true}
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', propAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', forceDefaultAssistant: true, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', subdomain: 'default'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: idle -> connecting
useVapiCall.js:1217 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js:1228 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js:1230 [useVapiCall] Call started successfully: null
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VapiCall.jsx:317 Window message received for processing: {source: 'react-devtools-bridge', payload: {…}}
useVapiCall.js:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: {firmName: 'LegalScout', welcomeMessage: "Hello, I'm Scout from LegalScout. How can I help you today?", voiceId: 'sarah', voiceProvider: '11labs'}
VapiCall.jsx:1790 mergedAssistantOverrides: {firstMessage: "Hello, I'm Scout from LegalScout. How can I help you today?", model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1809 Using default assistant - skipping manual welcome message to avoid duplication
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
VapiCall.jsx:1775 Added force-visible class to call interface
VapiCall.jsx:1785 Forced call-card-container to be visible
VM379 speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
VM379 speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
VM379 speech-particles.js:525 🎨 Converting user color: #4B74AA
VM379 speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
VM379 speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
VM379 speech-particles.js:537 🎨 Converting assistant color: #2C3E50
VM379 speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
VM379 speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
VM379 speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
speech-particles.js:525 🎨 Converting user color: #4B74AA
speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #2C3E50
speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VM379 speech-particles.js:309 Speech particles: Setting up visualization
VM379 speech-particles.js:318 Speech particles: Canvas found, initializing visualization
VM379 speech-particles.js:328 Speech particles: Visualization started
speech-particles.js:309 Speech particles: Setting up visualization
speech-particles.js:318 Speech particles: Canvas found, initializing visualization
speech-particles.js:328 Speech particles: Visualization started
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VM382:4 enumerateDevices took longer than expected: 96
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
$ @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
_ @ VM382:4
U.camPreferences @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 58
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
F @ VM382:4
g.getCamStream @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
iA @ VM382:4
f.camOperation @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 229
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
nA @ VM382:4
A.stream @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 96
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VM382:4 enumerateDevices took longer than expected: 118
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
b @ VM382:4
g.getCurrentDevices @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
NA @ VM382:4
hA.initialState @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
hA @ VM382:4
wA @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VM382:4 enumerateDevices took longer than expected: 73
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.getCurrentDevices @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
u @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VapiCall.jsx:294 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
useVapiCall.js:1181 Direct Vapi call started successfully: {id: 'b7f02839-40e3-48e2-89ce-c081f71e0dcb', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-06T15:03:45.903Z', updatedAt: '2025-06-06T15:03:45.903Z', type: 'webCall', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492222289990.7551510195004479', error: null, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492222289990.7551510195004479', error: null, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492222289990.7551510195004479', error: null, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-66ld5', what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-66ld5', what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-66ld5', what: 'iframe-call-message', …}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '6e84f700-31d8-4e0d-b492-5d884177cf3b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '6e84f700-31d8-4e0d-b492-5d884177cf3b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '6e84f700-31d8-4e0d-b492-5d884177cf3b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: 'listening', fromId: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: 'listening', fromId: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
useVapiCall.js:56 Call started - setting status to CONNECTED
useVapiCall.js:56 Call started - setting status to CONNECTED
useVapiCall.js:56 Call started - setting status to CONNECTED
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: '996c192f-a0b3-4f98-9236-f43d6d5f4db0', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '6e84f700-31d8-4e0d-b492-5d884177cf3b', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', …}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {6e84f700-31d8-4e0d-b492-5d884177cf3b: 0, 996c192f-a0b3-4f98-9236-f43d6d5f4db0: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VM382:4 Meeting ended due to ejection: Meeting has ended
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
p @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
VapiCall.jsx:294 Global iframe message received: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492222260720.1221018682565237'}
overrideMethod @ hook.js:608
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
useVapiCall.js:95 Continuing despite Vapi error
overrideMethod @ hook.js:608
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492222260720.1221018682565237'}
overrideMethod @ hook.js:608
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
useVapiCall.js:95 Continuing despite Vapi error
overrideMethod @ hook.js:608
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492222260720.1221018682565237'}
overrideMethod @ hook.js:608
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
useVapiCall.js:95 Continuing despite Vapi error
overrideMethod @ hook.js:608
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js?v=306847c5:7167
emit @ @vapi-ai_web.js?v=306847c5:9833
(anonymous) @ @vapi-ai_web.js?v=306847c5:9924
w.emit @ @vapi-ai_web.js?v=306847c5:3072
value @ @vapi-ai_web.js?v=306847c5:6899
value @ @vapi-ai_web.js?v=306847c5:6747
i2 @ @vapi-ai_web.js?v=306847c5:4796
postMessage
value @ VM382:4
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
msgSigChannel @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:294 Global iframe message received: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: connecting -> ended
debugConfig.js:30 [App] Call ended {timestamp: '2025-06-06T15:03:51.208Z', data: {…}}
debugConfig.js:88 User Journey [call_ended]: {timestamp: '2025-06-06T15:03:51.209Z', __DEV__: {…}}
App.jsx:1019 [App.jsx] Received empty data in endCall, likely during initialization - resetting state
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
debugConfig.js:30 [App] Call ended {timestamp: '2025-06-06T15:03:51.211Z', data: {…}}
debugConfig.js:88 User Journey [call_ended]: {timestamp: '2025-06-06T15:03:51.212Z', __DEV__: {…}}
App.jsx:1019 [App.jsx] Received empty data in endCall, likely during initialization - resetting state
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
debugConfig.js:30 [App] Call ended {timestamp: '2025-06-06T15:03:51.216Z', data: {…}}
debugConfig.js:88 User Journey [call_ended]: {timestamp: '2025-06-06T15:03:51.217Z', __DEV__: {…}}
App.jsx:1019 [App.jsx] Received empty data in endCall, likely during initialization - resetting state
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17492222260720.1221018682565237', from: 'embedded'}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VapiCall.jsx:317 Window message received for processing: {source: 'react-devtools-bridge', payload: {…}}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {source: 'react-devtools-bridge', payload: {…}}
useVapiCall.js:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
useVapiCall.js:893 Stopping active call during cleanup
useVapiCall.js:899 Calling onEndCall callback during unmount
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
debugConfig.js:30 [App] Call ended {timestamp: '2025-06-06T15:03:51.305Z', data: {…}}
debugConfig.js:88 User Journey [call_ended]: {timestamp: '2025-06-06T15:03:51.306Z', __DEV__: {…}}
App.jsx:1019 [App.jsx] Received empty data in endCall, likely during initialization - resetting state
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:123 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VM382:4 error applying mic processor. KrispInitError: Error enabling Krisp filter: Cannot read properties of null (reading 'disable')
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
Y @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
o @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
H.track @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
m.newBaseStream @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
z @ VM382:4
j @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:5741
(anonymous) @ @vapi-ai_web.js?v=306847c5:5740
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:5752
start @ @vapi-ai_web.js?v=306847c5:10015
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 59
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
A.stream @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
z @ VM382:4
j @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:5741
(anonymous) @ @vapi-ai_web.js?v=306847c5:5740
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:5752
start @ @vapi-ai_web.js?v=306847c5:10015
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 92
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
A.stream @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
z @ VM382:4
j @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:5741
(anonymous) @ @vapi-ai_web.js?v=306847c5:5740
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:5752
start @ @vapi-ai_web.js?v=306847c5:10015
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
VM382:4 enumerateDevices took longer than expected: 83
overrideMethod @ hook.js:608
value @ VM382:4
value @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
g.enumDWrapper @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
z @ VM382:4
j @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
Promise.then
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
dispatch @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
g @ VM382:4
i @ VM382:4
eval @ VM382:4
eval @ VM382:4
eval @ VM382:4
C @ VM382:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:5741
(anonymous) @ @vapi-ai_web.js?v=306847c5:5740
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:5752
start @ @vapi-ai_web.js?v=306847c5:10015
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
