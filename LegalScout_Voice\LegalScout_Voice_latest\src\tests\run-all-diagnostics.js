/**
 * Run All Diagnostics Script
 * 
 * This script runs all diagnostic tests to identify why Vapi calls
 * are not appearing in the dashboard.
 */

import { runConfigCheck } from './vapi-config-check.js';
import { runDiagnostic } from './webhook-diagnostic.js';
import { supabase } from '../lib/supabase-fixed.js';

/**
 * Check Supabase connection and data
 */
async function checkSupabaseData() {
  console.log('🔍 Checking Supabase data...\n');
  
  try {
    // Check attorney record
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (attorneyError) {
      console.log('❌ Attorney not found:', attorneyError.message);
      return false;
    }

    console.log('✅ Attorney found:');
    console.log('  ID:', attorney.id);
    console.log('  Name:', attorney.name);
    console.log('  Email:', attorney.email);
    console.log('  Assistant ID:', attorney.vapi_assistant_id);
    console.log('  Phone:', attorney.phone || 'Not set');

    // Check consultations
    const { data: consultations, error: consultError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', attorney.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (consultError) {
      console.log('❌ Error fetching consultations:', consultError.message);
    } else {
      console.log(`\n✅ Found ${consultations.length} consultations`);
      if (consultations.length > 0) {
        console.log('Recent consultations:');
        consultations.slice(0, 3).forEach((consultation, index) => {
          console.log(`  ${index + 1}. ${consultation.client_name} - ${consultation.practice_area}`);
          console.log(`     Created: ${consultation.created_at}`);
          console.log(`     Call ID: ${consultation.metadata?.call_id || 'N/A'}`);
        });
      }
    }

    // Check call records
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('*')
      .eq('attorney_id', attorney.id)
      .order('start_time', { ascending: false })
      .limit(10);

    if (callError) {
      console.log('❌ Error fetching call records:', callError.message);
    } else {
      console.log(`\n✅ Found ${callRecords.length} call records`);
      if (callRecords.length > 0) {
        console.log('Recent call records:');
        callRecords.slice(0, 3).forEach((record, index) => {
          console.log(`  ${index + 1}. Call ID: ${record.call_id}`);
          console.log(`     Status: ${record.status}`);
          console.log(`     Duration: ${record.duration || 'N/A'} seconds`);
          console.log(`     Start: ${record.start_time || 'N/A'}`);
        });
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Error checking Supabase data:', error.message);
    return false;
  }
}

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...\n');
  
  const requiredVars = [
    'VAPI_TOKEN',
    'VAPI_WEBHOOK_SECRET',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY'
  ];

  const optionalVars = [
    'VITE_VAPI_SECRET_KEY',
    'VAPI_SECRET_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  let allGood = true;

  console.log('Required variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: ${value.substring(0, 8)}...`);
    } else {
      console.log(`  ❌ ${varName}: Not set`);
      allGood = false;
    }
  });

  console.log('\nOptional variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: ${value.substring(0, 8)}...`);
    } else {
      console.log(`  ⚠️  ${varName}: Not set`);
    }
  });

  return allGood;
}

/**
 * Check webhook endpoint accessibility
 */
async function checkWebhookAccessibility() {
  console.log('\n🔍 Checking webhook endpoint accessibility...\n');
  
  const webhookUrls = [
    'https://legalscout.app/api/webhook/vapi-call',
    'http://localhost:5175/api/webhook/vapi-call'
  ];

  for (const url of webhookUrls) {
    try {
      console.log(`Testing: ${url}`);
      const response = await fetch(url, { method: 'GET' });
      
      if (response.status === 405) {
        console.log(`  ✅ Accessible (405 Method Not Allowed as expected)`);
      } else {
        console.log(`  ⚠️  Unexpected status: ${response.status}`);
      }
    } catch (error) {
      console.log(`  ❌ Not accessible: ${error.message}`);
    }
  }
}

/**
 * Check recent Vapi calls vs Supabase records
 */
async function checkCallSyncStatus() {
  console.log('\n🔍 Checking call sync status...\n');
  
  try {
    // Get recent Vapi calls using MCP (if available)
    console.log('Attempting to fetch recent Vapi calls...');
    
    // For now, we'll check what we know from the earlier analysis
    const knownVapiCalls = [
      'b1b909d4-5882-4773-ae39-23f5019724aa',
      'b8c66d6c-d13e-4cc7-8ac8-5eb1a4950314', 
      '7ac42ec3-c832-4ca5-80e8-7b65db906dac',
      '41845239-4529-4571-b64f-672bfd3647a8',
      '1cef9cf9-4684-4f9c-b53e-1dab1ada4736'
    ];

    console.log(`Found ${knownVapiCalls.length} known Vapi calls`);

    // Check which ones have corresponding records in Supabase
    for (const callId of knownVapiCalls) {
      const { data: callRecord } = await supabase
        .from('call_records')
        .select('id')
        .eq('call_id', callId)
        .single();

      const { data: consultation } = await supabase
        .from('consultations')
        .select('id')
        .eq('metadata->call_id', callId)
        .single();

      console.log(`Call ${callId}:`);
      console.log(`  Call record: ${callRecord ? '✅ Found' : '❌ Missing'}`);
      console.log(`  Consultation: ${consultation ? '✅ Found' : '❌ Missing'}`);
    }

  } catch (error) {
    console.error('❌ Error checking call sync status:', error.message);
  }
}

/**
 * Main diagnostic runner
 */
async function runAllDiagnostics() {
  console.log('🚀 Running Complete LegalScout Voice Diagnostic Suite\n');
  console.log('=' .repeat(60));
  
  // 1. Check environment variables
  console.log('\n1. ENVIRONMENT VARIABLES');
  console.log('=' .repeat(30));
  const envOk = checkEnvironmentVariables();
  
  // 2. Check Supabase data
  console.log('\n2. SUPABASE DATA');
  console.log('=' .repeat(30));
  const supabaseOk = await checkSupabaseData();
  
  // 3. Check webhook accessibility
  console.log('\n3. WEBHOOK ACCESSIBILITY');
  console.log('=' .repeat(30));
  await checkWebhookAccessibility();
  
  // 4. Check Vapi configuration
  console.log('\n4. VAPI CONFIGURATION');
  console.log('=' .repeat(30));
  await runConfigCheck();
  
  // 5. Check call sync status
  console.log('\n5. CALL SYNC STATUS');
  console.log('=' .repeat(30));
  await checkCallSyncStatus();
  
  // 6. Run webhook diagnostic
  console.log('\n6. WEBHOOK DIAGNOSTIC');
  console.log('=' .repeat(30));
  await runDiagnostic();
  
  // Final summary
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 FINAL DIAGNOSTIC SUMMARY');
  console.log('=' .repeat(60));
  
  console.log('Environment variables:', envOk ? '✅ OK' : '❌ Issues found');
  console.log('Supabase connection:', supabaseOk ? '✅ OK' : '❌ Issues found');
  console.log('');
  console.log('Next steps:');
  console.log('1. Review the detailed output above');
  console.log('2. Fix any configuration issues identified');
  console.log('3. Test with a live call to verify the fix');
  console.log('4. Check the dashboard to see if calls appear');
}

// Run diagnostics if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllDiagnostics().catch(console.error);
}

export { runAllDiagnostics };
