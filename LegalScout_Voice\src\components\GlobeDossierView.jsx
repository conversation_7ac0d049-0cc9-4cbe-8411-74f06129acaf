import React, { useEffect, useRef, useState } from 'react';
import './GlobeDossierView.css';
import './Map.css';

// Use global THREE object loaded from CDN to avoid multiple instances
const THREE = window.THREE;
const OrbitControls = window.THREE?.OrbitControls;

const GlobeDossierView = ({
  isVisible = true,
  caseData = {},
  attorneys = [],
  onSelectAttorney = () => {}
}) => {
  console.log("GlobeDossierView render called, isVisible:", isVisible, "caseData:", caseData, "attorneys:", attorneys);

  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const globeRef = useRef(null);
  const [hasLocation, setHasLocation] = useState(false);
  const [locationName, setLocationName] = useState(null);

  // Set up Three.js scene
  useEffect(() => {
    if (!isVisible || !mountRef.current) return;

    // Check if Three.js is available
    if (!window.THREE) {
      console.error("THREE is not loaded. Make sure it's included in your HTML.");
      return;
    }

    // Clear any existing canvas
    while (mountRef.current.firstChild) {
      mountRef.current.removeChild(mountRef.current.firstChild);
    }

    // Scene setup
    const scene = new THREE.Scene();

    // Get container dimensions - use smallest viewport dimension
    const container = mountRef.current;
    const vw = Math.min(window.innerWidth * 0.8, 600); // 80% of viewport width, max 600px
    const vh = Math.min(window.innerHeight - 120, 600); // Viewport height minus margins, max 600px
    const size = Math.min(vw, vh); // Use the smaller dimension

    // Camera setup with fixed aspect ratio
    const camera = new THREE.PerspectiveCamera(
      45, // Standard FOV
      1, // Force 1:1 aspect ratio
      0.1,
      1000
    );
    camera.position.z = 250; // Adjust camera distance

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      logarithmicDepthBuffer: true,
      powerPreference: "high-performance"
    });

    renderer.setSize(size, size);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.setClearColor(0x000000, 0);

    // Append renderer
    container.appendChild(renderer.domElement);

    // Create globe
    const Globe = window.ThreeGlobe;
    if (!Globe) {
      console.error("ThreeGlobe is not loaded");
      return;
    }

    const globe = new Globe()
      .globeImageUrl('//unpkg.com/three-globe/example/img/earth-blue-marble.jpg')
      .bumpImageUrl('//unpkg.com/three-globe/example/img/earth-topology.png')
      .hexPolygonsData([])
      .hexPolygonResolution(3)
      .hexPolygonMargin(0.7)
      .showAtmosphere(true)
      .atmosphereColor('#4169E1')
      .atmosphereAltitude(0.15);

    // Scale globe
    globe.scale.set(1, 1, 1);
    scene.add(globe);
    globeRef.current = globe;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.05;
    controls.enableZoom = false;
    controls.enablePan = false;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;
    controls.minPolarAngle = Math.PI / 3;
    controls.maxPolarAngle = Math.PI * 2/3;

    // Animation loop
    let frameId;
    const animate = () => {
      controls.update();
      renderer.render(scene, camera);
      frameId = requestAnimationFrame(animate);
    };

    // Start animation
    animate();

    // Handle resize
    const handleResize = () => {
      const newVw = Math.min(window.innerWidth * 0.8, 600);
      const newVh = Math.min(window.innerHeight - 120, 600);
      const newSize = Math.min(newVw, newVh);

      // Update renderer size
      renderer.setSize(newSize, newSize);

      // Update renderer and canvas style
      const canvas = renderer.domElement;
      canvas.style.width = `${newSize}px`;
      canvas.style.height = `${newSize}px`;

      // Don't update camera aspect ratio to maintain sphere shape
      camera.updateProjectionMatrix();
    };

    // Add resize observer for more reliable size updates
    const resizeObserver = new ResizeObserver(() => {
      handleResize();
    });
    resizeObserver.observe(container);

    // Also listen for window resize
    window.addEventListener('resize', handleResize);

    // Store scene reference
    sceneRef.current = { scene, camera, renderer, controls, frameId };

    // Cleanup
    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', handleResize);
      if (frameId) {
        cancelAnimationFrame(frameId);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      if (globe) {
        scene.remove(globe);
        globe.dispose();
      }
      renderer.dispose();
      controls.dispose();
    };
  }, [isVisible]);

  // Track selected location for highlighting
  useEffect(() => {
    // Check if we have location data in caseData
    if (caseData && caseData.location) {
      console.log("Location data detected:", caseData.location);
      setHasLocation(true);

      // Try to get a display name for the location
      if (caseData.location.address) {
        setLocationName(caseData.location.address);
      } else if (caseData.location.city) {
        setLocationName(caseData.location.city + (caseData.location.state ? `, ${caseData.location.state}` : ''));
      } else if (caseData.location.state) {
        setLocationName(caseData.location.state);
      } else {
        setLocationName("Selected Location");
      }
    } else {
      setHasLocation(false);
      setLocationName(null);
    }
  }, [caseData]);

  // Get attorneys for this location
  const locationAttorneys = attorneys.filter(attorney => attorney.location);

  return (
    <div
      className={`globe-background ${isVisible ? 'visible' : 'hidden'}`}
      style={{
        position: 'fixed',
        top: 60,
        left: 0,
        width: '100vw',
        height: 'calc(100vh - 60px)',
        backgroundColor: '#000',
        color: 'white',
        zIndex: 0,
        overflow: 'hidden',
        pointerEvents: 'none',
        margin: 0,
        padding: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <div
        className="globe-renderer-container"
        ref={mountRef}
        style={{
          position: 'relative',
          width: 'min(80vh, 80vw)',
          height: 'min(80vh, 80vw)',
          margin: 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          aspectRatio: '1',
          overflow: 'visible'
        }}
      />

      {/* Location Information - only show when hasLocation is true (but disabled temporarily) */}
      {false && hasLocation && (
        <div className="location-info">
          <h3>{locationName}</h3>
          <p>
            {locationAttorneys.length > 0
              ? `${locationAttorneys.length} attorneys available in this area`
              : 'Searching for attorneys in this area...'}
          </p>
        </div>
      )}

      {/* Debug information - minimized */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info minimal">
          <div>L: {hasLocation ? '✓' : '✗'}</div>
          <div>A: {attorneys.length}</div>
        </div>
      )}
    </div>
  );
};

export default GlobeDossierView;