import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aP<PERSON>, FaSync, FaExclamationTriangle, FaChevronDown, FaCheck } from 'react-icons/fa';
import { assistantUIConfigService } from '../../services/assistantUIConfigService';
import { assistantDataRefreshService } from '../../services/assistantDataRefreshService';
import { useAuth } from '../../contexts/AuthContext';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';
import { useAssistantSync } from '../../hooks/useAssistantSync';
import './EnhancedAssistantDropdown.css';

/**
 * Enhanced Assistant Dropdown Component
 * Allows switching between multiple assistants with per-assistant UI configurations
 */
const EnhancedAssistantDropdown = ({
  attorney,
  onAssistantChange,
  onConfigLoad,
  disabled = false,
  variant = 'full' // 'full', 'minimal', or 'header-compact'
}) => {
  const { user } = useAuth();
  // DIRECT ACCESS TO ASSISTANT CONTEXT
  const { setCurrentAssistant } = useAssistantAware();

  // Use the comprehensive sync system
  const {
    syncAssistantSelection,
    syncDataModification,
    isLoading: syncLoading,
    error: syncError,
    currentAssistantId: syncCurrentAssistantId,
    forceRefresh
  } = useAssistantSync({
    autoSync: false, // We'll handle sync manually in this component
    dataTypes: ['ui_config', 'subdomain', 'assistant_data']
  });

  const [availableAssistants, setAvailableAssistants] = useState([]);
  const [currentAssistantId, setCurrentAssistantId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [assistantSubdomains, setAssistantSubdomains] = useState({});
  const dropdownRef = useRef(null);

  // Helper function to validate assistant ID
  const isValidAssistantId = (assistantId) => {
    if (!assistantId || typeof assistantId !== 'string') return false;

    // CRITICAL FIX: Accept both Vapi IDs and Supabase UUIDs
    // Vapi IDs are typically longer alphanumeric strings
    // Supabase UUIDs are valid assistant identifiers in our system

    // Reject only clearly invalid IDs
    if (assistantId.includes('mock') || assistantId.includes('undefined') || assistantId === 'null') {
      console.warn('[EnhancedAssistantDropdown] Rejecting invalid assistant ID:', assistantId);
      return false;
    }

    // Accept any reasonable length ID (both Vapi and Supabase formats)
    return assistantId.length > 10;
  };

  /**
   * Load assistants using centralized service - UNIFIED APPROACH
   */
  const loadAssistants = useCallback(async () => {
    if (!attorney?.id) {
      console.error('No attorney ID available for loading assistants');
      setError('Attorney information not available');
      return;
    }

    try {
      setLoading(true);
      setError(''); // Clear any previous errors

      console.log('🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney:', attorney.id);

      // Use the centralized service that both dropdown and VeryCoolAssistants use
      const { AssistantDataService } = await import('../../services/assistantDataService');
      const assistants = await AssistantDataService.getAssistantsForAttorney(attorney.id);

      console.log('📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service:', assistants);

      // Transform to dropdown format (maintaining compatibility)
      const assistantList = assistants.map(assistant => ({
        id: assistant.id,
        name: assistant.name,
        hasConfig: assistant.hasConfig,
        configData: assistant.config,
        source: assistant.source,
        vapiData: assistant.vapiData,
        image_url: assistant.image_url,
        subdomain: assistant.subdomain
      }));

      setAvailableAssistants(assistantList);
      console.log('✅ [EnhancedAssistantDropdown] Assistant list ready:', assistantList.map(a => ({
        id: a.id,
        name: a.name,
        source: a.source,
        hasSubdomain: !!a.subdomain
      })));

      // CRITICAL FIX: If no current assistant is selected but we have assistants, select the first one
      if (!currentAssistantId && assistantList.length > 0) {
        const firstAssistant = assistantList[0];
        console.log('🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first:', firstAssistant.id);
        setCurrentAssistantId(firstAssistant.id);

        // Also notify the context
        setCurrentAssistant(firstAssistant.id);

        // Update attorney manager
        try {
          const manager = window.standaloneAttorneyManager;
          if (manager && manager.attorney) {
            const updatedAttorney = {
              ...manager.attorney,
              current_assistant_id: firstAssistant.id
            };
            manager.attorney = updatedAttorney;
            manager.notifySubscribers();
          }
        } catch (error) {
          console.warn('Could not update attorney manager:', error);
        }
      }

    } catch (error) {
      console.error('❌ [EnhancedAssistantDropdown] Error loading assistants:', error);
      setError('Failed to load assistants: ' + error.message);
      setAvailableAssistants([]);
    } finally {
      setLoading(false);
    }
  }, [attorney?.id, currentAssistantId, setCurrentAssistant]);

  /**
   * Load current assistant selection with improved validation
   */
  const loadCurrentAssistant = useCallback(async () => {
    if (!attorney?.id) return;

    try {
      const currentId = await assistantUIConfigService.getCurrentAssistantId(attorney.id);
      const fallbackId = attorney.vapi_assistant_id;
      const selectedId = currentId || fallbackId;

      // CRITICAL FIX: Enhanced validation to prevent attorney ID confusion
      if (selectedId && isValidAssistantId(selectedId)) {
        // Additional check: ensure this isn't the attorney ID
        if (selectedId === attorney.id) {
          console.error('[EnhancedAssistantDropdown] 🚨 CRITICAL: Attorney ID detected as assistant ID:', selectedId);
          console.error('[EnhancedAssistantDropdown] This indicates a serious data integrity issue');
          setCurrentAssistantId(null);
          setError('Data integrity issue detected - please contact support');
          return;
        }

        console.log('[EnhancedAssistantDropdown] Setting valid current assistant ID:', selectedId);
        setCurrentAssistantId(selectedId);
      } else {
        if (selectedId) {
          console.warn('[EnhancedAssistantDropdown] Invalid assistant ID found:', selectedId);
          if (selectedId === attorney.id) {
            console.error('[EnhancedAssistantDropdown] 🚨 This is the attorney ID, not an assistant ID!');
          }
        } else {
          console.log('[EnhancedAssistantDropdown] No current assistant ID found');
        }
        setCurrentAssistantId(null);
      }
    } catch (error) {
      console.error('Error loading current assistant:', error);
      // Don't fallback to potentially invalid assistant ID
      setCurrentAssistantId(null);
      setError('Failed to load current assistant selection');
    }
  }, [attorney?.id, attorney?.vapi_assistant_id]);

  /**
   * Load assistant subdomains
   */
  const loadAssistantSubdomains = useCallback(async () => {
    if (!attorney?.id) return;

    try {
      // Import supabase directly for faster loading
      const { supabase } = await import('../../lib/supabase');

      const { data: subdomains, error } = await supabase
        .from('assistant_subdomains')
        .select('assistant_id, subdomain, is_primary')
        .eq('attorney_id', attorney.id)
        .eq('is_active', true);

      if (error) {
        console.error('Error loading assistant subdomains:', error);
        return;
      }

      // Create a mapping of assistant_id -> subdomain
      const subdomainMap = {};
      subdomains?.forEach(mapping => {
        subdomainMap[mapping.assistant_id] = mapping.subdomain;
      });

      setAssistantSubdomains(subdomainMap);
      console.log('🌐 Loaded assistant subdomains:', subdomainMap);
    } catch (error) {
      console.error('Error loading assistant subdomains:', error);
    }
  }, [attorney?.id]);

  // Load available assistants and current selection
  useEffect(() => {
    if (!attorney?.id) return;

    loadAssistants();
    loadCurrentAssistant();
    loadAssistantSubdomains();
  }, [attorney?.id, loadAssistants, loadCurrentAssistant, loadAssistantSubdomains]);

  // Subscribe to centralized assistant data changes
  useEffect(() => {
    if (!attorney?.id) return;

    const handleDataChange = (eventType, data) => {
      console.log('🔄 [EnhancedAssistantDropdown] Assistant data changed:', eventType, data);

      if (eventType === 'assistant_deleted' || eventType === 'assistant_created' || eventType === 'cache_invalidated') {
        // Refresh the dropdown when data changes
        loadAssistants();
      }
    };

    // Subscribe to the centralized service
    let unsubscribe;

    const setupSubscription = async () => {
      try {
        const { AssistantDataService } = await import('../../services/assistantDataService');
        unsubscribe = AssistantDataService.subscribe(handleDataChange);
        console.log('✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service');
      } catch (error) {
        console.error('❌ [EnhancedAssistantDropdown] Failed to subscribe to assistant data service:', error);
      }
    };

    setupSubscription();

    // Also listen for legacy events for backward compatibility
    const handleAssistantDeleted = (event) => {
      console.log('🗑️ [EnhancedAssistantDropdown] Legacy deletion event:', event.detail);
      loadAssistants();
    };

    window.addEventListener('assistantDeleted', handleAssistantDeleted);

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      window.removeEventListener('assistantDeleted', handleAssistantDeleted);
    };
  }, [attorney?.id, loadAssistants]);

  // Handle clicking outside dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Listen for assistant image updates
  useEffect(() => {
    const handleAssistantImageUpdated = (event) => {
      const { assistantId, imageUrl } = event.detail;
      console.log('🖼️ Assistant image updated, refreshing dropdown:', { assistantId, imageUrl });

      // Refresh the assistants list to get updated config data
      if (attorney?.id) {
        loadAssistants();
      }
    };

    window.addEventListener('assistantImageUpdated', handleAssistantImageUpdated);
    return () => window.removeEventListener('assistantImageUpdated', handleAssistantImageUpdated);
  }, [attorney?.id, loadAssistants]);

  /**
   * Handle assistant selection change - ENHANCED WITH SYNC SYSTEM AND VALIDATION
   */
  const handleAssistantChange = async (assistantId) => {
    if (!assistantId || assistantId === currentAssistantId) return;

    // CRITICAL FIX: Enhanced validation to prevent data integrity issues
    if (assistantId !== 'create_new') {
      if (!isValidAssistantId(assistantId)) {
        console.error('[EnhancedAssistantDropdown] Invalid assistant ID rejected:', assistantId);
        setError('Invalid assistant ID format');
        return;
      }

      // Additional check: ensure this isn't the attorney ID
      if (assistantId === attorney?.id) {
        console.error('[EnhancedAssistantDropdown] 🚨 CRITICAL: Attempted to select attorney ID as assistant ID:', assistantId);
        setError('Cannot select attorney ID as assistant - data integrity issue detected');
        return;
      }
    }

    try {
      setLoading(true);
      setError('');

      if (assistantId === 'create_new') {
        // Handle creating new assistant
        console.log('🆕 Creating new assistant...');

        try {
          // SYSTEMATIC: Use centralized assistant creation
          const { AssistantDataService } = await import('../../services/assistantDataService');

          const assistantName = AssistantDataService.generateUniqueAssistantName(
            attorney.firm_name,
            availableAssistants
          );

          const newAssistant = await AssistantDataService.createCompleteAssistant(
            attorney.id,
            {
              name: assistantName,
              firm_name: attorney.firm_name,
              firstMessage: `Hello! I'm an AI assistant from ${attorney.firm_name || 'LegalScout'}. How can I help you today?`,
              instructions: 'You are a helpful legal assistant. Be professional, accurate, and helpful.',
              voice: {
                provider: 'openai',
                voiceId: 'echo'
              },
              baseSubdomain: attorney.subdomain || 'assistant'
            }
          );

          console.log('✅ [Dropdown] Complete assistant created systematically:', newAssistant.id);

          // Refresh the assistants list
          await loadAssistants();

          // Use the comprehensive sync system for the new assistant
          await syncAssistantSelection(attorney.id, newAssistant.id);

          setCurrentAssistantId(newAssistant.id);

          if (onAssistantChange) {
            await onAssistantChange(newAssistant.id);
          }

          console.log('✅ Successfully created and switched to new assistant with sync system');
        } catch (createError) {
          console.error('❌ Error creating new assistant:', createError);
          setError('Failed to create new assistant: ' + createError.message);
        }
        return;
      }

      console.log('🔄 Switching to assistant using comprehensive sync system:', assistantId);

      // Use the comprehensive sync system for assistant selection
      const syncResult = await syncAssistantSelection(attorney.id, assistantId);

      // Get the updated config after sync
      const config = await assistantUIConfigService.getAssistantConfig(attorney.id, assistantId);

      setCurrentAssistantId(assistantId);

      // Notify parent components
      if (onAssistantChange) {
        await onAssistantChange(assistantId, syncResult);
      }

      if (onConfigLoad && config) {
        onConfigLoad(config);
      }

      console.log('✅ Successfully switched assistant using sync system:', {
        assistantId,
        syncResult,
        config
      });
      
    } catch (error) {
      console.error('Error switching assistant:', error);
      setError('Failed to switch assistant: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Combine loading states from both local and sync system
  const isComponentLoading = loading || syncLoading;

  // Combine errors from both local and sync system
  const componentError = error || syncError;

  /**
   * Get assistant display name with additional info
   */
  const getAssistantDisplayName = (assistant) => {
    if (!assistant) return 'Unknown Assistant';

    const name = assistant.name || `Assistant ${assistant.id?.slice(0, 8) || 'Unknown'}`;
    const toolCount = assistant.toolIds?.length || 0;
    const hasConfig = assistant.hasConfig;

    let suffix = '';
    if (toolCount > 0) suffix += ` (${toolCount} tools)`;
    if (hasConfig) suffix += ' ✓';

    return name + suffix;
  };

  /**
   * Get assistant image URL from config or generate default
   */
  const getAssistantImageUrl = (assistant) => {
    // Safety check for assistant object
    if (!assistant) {
      return getDefaultAvatarImage('AS');
    }

    console.log(`🖼️ Getting image for assistant ${assistant.id}:`, {
      hasConfigData: !!assistant.configData,
      hasAssistantImageUrl: !!assistant.configData?.assistant_image_url,
      assistantImageUrl: assistant.configData?.assistant_image_url?.substring(0, 50) + '...',
      hasDirectImageUrl: !!assistant.image_url,
      directImageUrl: assistant.image_url?.substring(0, 50) + '...'
    });

    // Check if assistant has a custom uploaded image in config
    if (assistant.configData?.assistant_image_url &&
        assistant.configData.assistant_image_url.trim() !== '') {
      console.log(`🖼️ ✅ Using custom image for assistant ${assistant.id}`);
      return assistant.configData.assistant_image_url;
    }

    // Check if assistant has an image_url field directly
    if (assistant.image_url && assistant.image_url.trim() !== '') {
      console.log(`Using direct image_url for assistant ${assistant.id}:`, assistant.image_url);
      return assistant.image_url;
    }

    // Generate assistant-specific initials from name
    const name = assistant.name || 'Assistant';
    const initials = getInitialsFromName(name, assistant.id);

    console.log(`🖼️ Generating assistant-level avatar for ${assistant.id} with initials:`, initials);
    return getDefaultAvatarImage(initials, assistant.id);
  };

  /**
   * Extract meaningful initials from assistant name (assistant-level specific)
   */
  const getInitialsFromName = (name, assistantId) => {
    if (!name || name === 'Assistant' || name === 'Unknown Assistant') {
      // Use last 2 characters of assistant ID as unique fallback
      if (assistantId && assistantId.length >= 2) {
        return assistantId.slice(-2).toUpperCase();
      }
      return 'AI';
    }

    // Split by spaces and get first letter of each word
    const words = name.trim().split(/\s+/);

    if (words.length === 1) {
      // Single word - take first two characters
      return words[0].substring(0, 2).toUpperCase();
    } else {
      // Multiple words - take first letter of first two words
      return words.slice(0, 2).map(word => word[0]).join('').toUpperCase();
    }
  };

  /**
   * Generate a default avatar image with initials (assistant-level specific)
   */
  const getDefaultAvatarImage = (initials, assistantId) => {
    try {
      // Generate a unique color for each assistant based on their ID
      const colors = [
        '#4B74AA', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
        '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
      ];

      // Use assistant ID to determine color (consistent per assistant)
      let colorIndex = 0;
      if (assistantId) {
        // Sum character codes for consistent color selection
        colorIndex = assistantId.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0) % colors.length;
      }

      const backgroundColor = colors[colorIndex];

      // Create a simple colored avatar with initials
      const svgContent = `
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="${backgroundColor}"/>
          <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">${initials}</text>
        </svg>
      `;
      return `data:image/svg+xml;base64,${btoa(svgContent)}`;
    } catch (error) {
      console.error('Error generating assistant-level avatar:', error);
      // Fallback to a simple colored circle
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="#4B74AA"/>
        </svg>
      `)}`;
    }
  };

  /**
   * Get assistant status for indicator dot
   */
  const getAssistantStatus = (assistant) => {
    const subdomain = assistantSubdomains[assistant.id];

    // Check if assistant has basic configuration
    const hasBasicConfig = assistant?.name && assistant?.name !== 'Unknown Assistant';

    if (!hasBasicConfig) {
      return {
        status: 'incomplete',
        color: '#9ca3af', // Gray
        label: 'Incomplete setup'
      };
    }

    if (!subdomain) {
      return {
        status: 'no_subdomain',
        color: '#ef4444', // Red - No subdomain assigned
        label: 'No subdomain assigned'
      };
    }

    return {
      status: 'deployed',
      color: '#10b981', // Green - Has subdomain (deployed)
      label: `Deployed: ${subdomain}.legalscout.net`
    };
  };

  /**
   * Get assistant description
   */
  const getAssistantDescription = (assistant) => {
    const parts = [];
    
    if (assistant.voice?.provider && assistant.voice?.voiceId) {
      parts.push(`Voice: ${assistant.voice.provider}/${assistant.voice.voiceId}`);
    }
    
    if (assistant.llm?.model) {
      parts.push(`Model: ${assistant.llm.model}`);
    }
    
    if (assistant.toolIds?.length > 0) {
      parts.push(`${assistant.toolIds.length} tools`);
    }
    
    return parts.join(' • ');
  };

  // Header-compact variant - ultra minimal for header integration
  if (variant === 'header-compact') {
    return (
      <div className="enhanced-assistant-dropdown">
        <div className="custom-dropdown" ref={dropdownRef}>
          <div
            className={`dropdown-trigger ${isDropdownOpen ? 'open' : ''} ${loading ? 'loading' : ''}`}
            onClick={() => !disabled && !loading && setIsDropdownOpen(!isDropdownOpen)}
            disabled={disabled || loading}
          >
            <div className="assistant-info">
              <div className="assistant-name">
                {currentAssistantId ? (
                  (() => {
                    const selectedAssistant = availableAssistants.find(a => a.id === currentAssistantId);
                    return selectedAssistant?.name || 'Select Assistant';
                  })()
                ) : (
                  'Select Assistant'
                )}
              </div>
            </div>
            <div className="dropdown-chevron">
              {isDropdownOpen ? <FaChevronDown style={{ transform: 'rotate(180deg)' }} /> : <FaChevronDown />}
            </div>
          </div>

          {isDropdownOpen && (
            <div className="dropdown-menu">
              {loading ? (
                <div className="dropdown-loading">
                  <FaSpinner className="spinning" />
                  <span>Loading assistants...</span>
                </div>
              ) : error ? (
                <div className="dropdown-error">
                  <FaExclamationTriangle />
                  <span>{error}</span>
                </div>
              ) : (
                <>
                  {availableAssistants.map((assistant) => {
                    const status = getAssistantStatus(assistant);
                    return (
                      <div
                        key={assistant.id}
                        className={`dropdown-item ${currentAssistantId === assistant.id ? 'selected' : ''}`}
                        onClick={() => {
                          handleAssistantChange(assistant.id);
                          setIsDropdownOpen(false);
                        }}
                        title={status.label}
                      >
                        <div className="assistant-info">
                          <div className="assistant-name">{assistant.name}</div>
                          <div className="assistant-subdomain">
                            {assistantSubdomains[assistant.id] ?
                              `${assistantSubdomains[assistant.id]}.legalscout.net` :
                              'No subdomain assigned'
                            }
                          </div>
                        </div>
                        <div
                          className="status-dot-compact"
                          style={{ backgroundColor: status.color }}
                          title={status.label}
                        ></div>
                        {currentAssistantId === assistant.id && <FaCheck className="selected-icon" />}
                      </div>
                    );
                  })}

                  <div className="dropdown-divider"></div>

                  <div
                    className="dropdown-item create-new"
                    onClick={() => {
                      handleAssistantChange('create_new');
                      setIsDropdownOpen(false);
                    }}
                  >
                    <div className="assistant-info">
                      <div className="assistant-name">Create New Assistant</div>
                    </div>
                    <FaPlus className="create-icon" />
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Minimal variant for header
  if (variant === 'minimal') {
    return (
      <div className="enhanced-assistant-dropdown">
        <div className="custom-dropdown" ref={dropdownRef}>

          <div
            className={`dropdown-trigger ${disabled || isComponentLoading ? 'disabled' : ''}`}
            onClick={() => !disabled && !isComponentLoading && setIsDropdownOpen(!isDropdownOpen)}
          >
            <div className="selected-assistant">
              {currentAssistantId ? (
                (() => {
                  const selectedAssistant = availableAssistants.find(a => a.id === currentAssistantId);
                  const subdomain = assistantSubdomains[currentAssistantId];
                  const status = selectedAssistant ? getAssistantStatus(selectedAssistant) : null;
                  return (
                    <>
                      <div className="assistant-avatar">
                        <img
                          src={getAssistantImageUrl(selectedAssistant)}
                          alt={selectedAssistant?.name || 'Assistant'}
                          className="assistant-image-mini"
                          onError={(e) => {
                            const fallbackInitials = selectedAssistant?.name ?
                              getInitialsFromName(selectedAssistant.name, selectedAssistant.id) : 'AS';
                            e.target.src = getDefaultAvatarImage(fallbackInitials, selectedAssistant?.id);
                          }}
                        />
                        {status && (
                          <div
                            className="status-dot-mini"
                            style={{ backgroundColor: status.color }}
                            title={status.label}
                          ></div>
                        )}
                      </div>
                      <div className="assistant-info">
                        <div className="assistant-name">
                          {selectedAssistant?.name || 'Unknown Assistant'}
                        </div>
                        <div className="assistant-subdomain">
                          {subdomain ? `${subdomain}.legalscout.net` : 'No subdomain assigned'}
                        </div>
                      </div>
                    </>
                  );
                })()
              ) : (
                <div className="assistant-info">
                  <div className="assistant-name">Select Assistant</div>
                  <div className="assistant-subdomain">Choose an AI assistant to configure</div>
                </div>
              )}
            </div>
            <FaChevronDown className={`dropdown-arrow ${isDropdownOpen ? 'open' : ''}`} />
          </div>

          {isDropdownOpen && (
            <div className="dropdown-menu vertical-elegant">
              {availableAssistants.map(assistant => {
                const status = getAssistantStatus(assistant);
                return (
                  <div
                    key={assistant.id}
                    className={`dropdown-item ${assistant.id === currentAssistantId ? 'selected' : ''}`}
                    onClick={() => {
                      handleAssistantChange(assistant.id);
                      setIsDropdownOpen(false);
                    }}
                    title={`${assistant.name || `Assistant ${assistant.id.slice(0, 8)}`} - ${status.label}`}
                  >
                    <div className="assistant-avatar">
                      <img
                        src={getAssistantImageUrl(assistant)}
                        alt={assistant?.name || 'Assistant'}
                        className="assistant-image"
                        onError={(e) => {
                          const fallbackInitials = getInitialsFromName(assistant.name, assistant.id);
                          e.target.src = getDefaultAvatarImage(fallbackInitials, assistant.id);
                        }}
                      />
                      <div
                        className="status-dot"
                        style={{ backgroundColor: status.color }}
                        title={status.label}
                      ></div>
                    </div>
                    <div className="assistant-info">
                      <div className="assistant-name">
                        {assistant.name || `Assistant ${assistant.id.slice(0, 8)}`}
                      </div>
                      <div className="assistant-subdomain">
                        {assistantSubdomains[assistant.id] ?
                          `${assistantSubdomains[assistant.id]}.legalscout.net` :
                          'No subdomain assigned'
                        }
                      </div>
                    </div>
                    {assistant.id === currentAssistantId && (
                      <FaCheck className="selected-icon" />
                    )}
                  </div>
                );
              })}

              <div className="dropdown-divider"></div>

              <div
                className="dropdown-item create-new"
                onClick={() => {
                  handleAssistantChange('create_new');
                  setIsDropdownOpen(false);
                }}
                title="Create New Assistant"
              >
                <div className="assistant-avatar">
                  <div className="assistant-image create-icon">
                    <FaPlus />
                  </div>
                </div>
                <div className="assistant-info">
                  <div className="assistant-name">Create New Assistant</div>
                  <div className="assistant-subdomain">Add a new AI assistant</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Full variant for Agent tab
  return (
    <div className="enhanced-assistant-dropdown dashboard-card">
      <h3>Assistant Selection</h3>
      <p className="card-description">
        Choose which AI assistant to configure. Each assistant can have unique settings and behavior.
      </p>

      <div className="form-group">
        <div className="dropdown-header">
          <label htmlFor="assistant-select">
            <FaRobot className="label-icon" />
            Current Assistant
          </label>
          <div className="header-actions">
            {isComponentLoading && <FaSpinner className="loading-spinner spinning" />}
            <button
              type="button"
              className="create-assistant-btn"
              onClick={() => handleAssistantChange('create_new')}
              disabled={isComponentLoading}
              title="Create New Assistant"
            >
              <FaPlus />
              New Assistant
            </button>
          </div>
        </div>

        <div className="custom-dropdown" ref={dropdownRef}>
          <div
            className={`dropdown-trigger ${disabled || isComponentLoading ? 'disabled' : ''}`}
            onClick={() => !disabled && !isComponentLoading && setIsDropdownOpen(!isDropdownOpen)}
          >
            <div className="selected-assistant">
              {currentAssistantId ? (
                (() => {
                  const selectedAssistant = availableAssistants.find(a => a.id === currentAssistantId);
                  const subdomain = assistantSubdomains[currentAssistantId];
                  const status = selectedAssistant ? getAssistantStatus(selectedAssistant) : null;
                  return (
                    <>
                      <div className="assistant-avatar">
                        <img
                          src={getAssistantImageUrl(selectedAssistant)}
                          alt={selectedAssistant?.name || 'Assistant'}
                          className="assistant-image-mini"
                          onError={(e) => {
                            const fallbackInitials = selectedAssistant?.name ?
                              getInitialsFromName(selectedAssistant.name, selectedAssistant.id) : 'AS';
                            e.target.src = getDefaultAvatarImage(fallbackInitials, selectedAssistant?.id);
                          }}
                        />
                        {status && (
                          <div
                            className="status-dot-mini"
                            style={{ backgroundColor: status.color }}
                            title={status.label}
                          ></div>
                        )}
                      </div>
                      <div className="assistant-info">
                        <div className="assistant-name">
                          {selectedAssistant?.name || 'Unknown Assistant'}
                        </div>
                        <div className="assistant-subdomain">
                          {subdomain ? `${subdomain}.legalscout.net` : 'No subdomain assigned'}
                        </div>
                      </div>
                    </>
                  );
                })()
              ) : (
                <div className="assistant-info">
                  <div className="assistant-name">Select Assistant</div>
                  <div className="assistant-subdomain">Choose an AI assistant to configure</div>
                </div>
              )}
            </div>
            <FaChevronDown className={`dropdown-arrow ${isDropdownOpen ? 'open' : ''}`} />
          </div>

          {isDropdownOpen && (
            <div className="dropdown-menu vertical-elegant">
              {availableAssistants.map(assistant => {
                const status = getAssistantStatus(assistant);
                return (
                  <div
                    key={assistant.id}
                    className={`dropdown-item ${assistant.id === currentAssistantId ? 'selected' : ''}`}
                    onClick={() => {
                      handleAssistantChange(assistant.id);
                      setIsDropdownOpen(false);
                    }}
                    title={`${assistant.name || `Assistant ${assistant.id.slice(0, 8)}`} - ${status.label}`}
                  >
                    <div className="assistant-avatar">
                      <img
                        src={getAssistantImageUrl(assistant)}
                        alt={assistant?.name || 'Assistant'}
                        className="assistant-image"
                        onError={(e) => {
                          const fallbackInitials = getInitialsFromName(assistant.name, assistant.id);
                          e.target.src = getDefaultAvatarImage(fallbackInitials, assistant.id);
                        }}
                      />
                      <div
                        className="status-dot"
                        style={{ backgroundColor: status.color }}
                        title={status.label}
                      ></div>
                    </div>
                    <div className="assistant-info">
                      <div className="assistant-name">
                        {assistant.name || `Assistant ${assistant.id.slice(0, 8)}`}
                      </div>
                      <div className="assistant-subdomain">
                        {assistantSubdomains[assistant.id] ?
                          `${assistantSubdomains[assistant.id]}.legalscout.net` :
                          'No subdomain assigned'
                        }
                      </div>
                    </div>
                    {assistant.id === currentAssistantId && (
                      <FaCheck className="selected-icon" />
                    )}
                  </div>
                );
              })}

              <div className="dropdown-divider"></div>

              <div
                className="dropdown-item create-new"
                onClick={() => {
                  handleAssistantChange('create_new');
                  setIsDropdownOpen(false);
                }}
                title="Create New Assistant"
              >
                <div className="assistant-avatar">
                  <div className="assistant-image create-icon">
                    <FaPlus />
                  </div>
                </div>
                <div className="assistant-info">
                  <div className="assistant-name">Create New Assistant</div>
                  <div className="assistant-subdomain">Add a new AI assistant</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Assistant Details */}
      {currentAssistantId && (
        <div className="assistant-details">
          {(() => {
            const currentAssistant = availableAssistants.find(a => a.id === currentAssistantId);
            if (!currentAssistant) return null;

            return (
              <div className="assistant-info">
                <div className="assistant-name">
                  {currentAssistant.name || 'Unnamed Assistant'}
                </div>
                <div className="assistant-description">
                  {getAssistantDescription(currentAssistant)}
                </div>
                {currentAssistant.hasConfig && (
                  <div className="config-status">
                    <span className="config-indicator">✓ UI Configuration Available</span>
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      )}

      {componentError && (
        <div className="dropdown-error">
          <FaExclamationTriangle />
          <span>{componentError}</span>
        </div>
      )}

      {/* Quick Actions */}
      <div className="dropdown-actions">
        <button
          type="button"
          className="action-btn"
          onClick={loadAssistants}
          disabled={isComponentLoading}
          title="Refresh assistant list"
        >
          <FaSync />
          Refresh
        </button>
      </div>
    </div>
  );
};

export default EnhancedAssistantDropdown;
