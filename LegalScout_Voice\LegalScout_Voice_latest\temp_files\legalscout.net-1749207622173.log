 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
production-cors-fix.js:62 Uncaught 
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: Array(1)
dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src-elem 'self' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io".

 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 Environment Variable Verification
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
overrideMethod @ installHook.js:1
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 Loaded contentScript
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
overrideMethod @ installHook.js:1
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
production-cors-fix.js:62 Uncaught 
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a: 401
overrideMethod @ installHook.js:1
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
production-cors-fix.js:62 Uncaught 
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 Environment Variable Verification
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-8684bd7e.js:71
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ installHook.js:1
 [VapiMcpService] Assistant not found with ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ installHook.js:1
 [RobustStateHandler] Assistant ID exists but assistant not found in Vapi: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ installHook.js:1
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: Array(1)
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 ✅ [CleanAuthSolution] Auth state monitoring set up
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 Environment Variable Verification
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-8684bd7e.js:71
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
api.vapi.ai/assistant:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ installHook.js:1
 [VapiMcpService] Error creating assistant: 
overrideMethod @ installHook.js:1
 [RobustStateHandler] Error creating Vapi assistant: 
overrideMethod @ installHook.js:1
 ❌ [RobustStateHandler] Assistant creation attempt 1 failed: 
overrideMethod @ installHook.js:1
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 received intentional event
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 [10:55:21] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a: 401
overrideMethod @ installHook.js:1
api.vapi.ai/assistant:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Error listing assistants: 
overrideMethod @ installHook.js:1
 [AgentTab] Error loading assistants from Vapi MCP: 
overrideMethod @ installHook.js:1
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ installHook.js:1
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ installHook.js:1
 [VapiMcpService] Assistant not found with ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ installHook.js:1
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [10:55:21] [VapiMcpService] Retrieving assistant Object
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
production-cors-fix.js:62 Uncaught 
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 Environment Variable Verification
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-8684bd7e.js:71
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 updating page active status
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
api.vapi.ai/assistant:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ installHook.js:1
 [VapiMcpService] Error creating assistant: 
overrideMethod @ installHook.js:1
 [RobustStateHandler] Error creating Vapi assistant: 
overrideMethod @ installHook.js:1
 ❌ [RobustStateHandler] Assistant creation attempt 2 failed: 
overrideMethod @ installHook.js:1
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [10:55:24] [VapiMcpService] Assistant verified in Vapi Object
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-8684bd7e.js:71
 enumerateDevices took longer than expected: 109
value @ unknown
api.vapi.ai/assistant:1  Failed to load resource: the server responded with a status of 401 ()
 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ installHook.js:1
 [VapiMcpService] Error creating assistant: 
overrideMethod @ installHook.js:1
 [RobustStateHandler] Error creating Vapi assistant: 
overrideMethod @ installHook.js:1
 ❌ [RobustStateHandler] Assistant creation attempt 3 failed: 
overrideMethod @ installHook.js:1
 ❌ [RobustStateHandler] Error creating default assistant: 
overrideMethod @ installHook.js:1
 ❌ [RobustStateHandler] Failed to create default assistant
overrideMethod @ installHook.js:1
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 enumerateDevices took longer than expected: 120
value @ unknown
 enumerateDevices took longer than expected: 62
value @ unknown
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ installHook.js:1
Refused to load the script 'blob:https://legalscout.net/3bccf2d8-51e7-42ee-804f-e6d2f08e194d' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

simple-preview:1 Refused to load the script 'blob:https://legalscout.net/3bccf2d8-51e7-42ee-804f-e6d2f08e194d' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

 KrispSDK - KrispSDK:createNoiseFilter 
error @ unknown
 error applying mic processor. KrispInitError: Error creating krisp filter: Error: WORKLET_NOT_SUPPORTED
value @ unknown
 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ installHook.js:1
 Meeting ended due to ejection: Meeting has ended
value @ unknown
 Vapi error: Object
onError @ pages-8684bd7e.js:29
 Continuing despite Vapi error
onError @ pages-8684bd7e.js:29
 Vapi error: Object
onError @ pages-8684bd7e.js:29
 Continuing despite Vapi error
onError @ pages-8684bd7e.js:29
 Vapi error: Object
onError @ pages-8684bd7e.js:29
 Continuing despite Vapi error
onError @ pages-8684bd7e.js:29
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ installHook.js:1
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
 received intentional event
 updating page active status
 received intentional event
 updating page active status
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [10:55:45] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ installHook.js:1
api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: the server responded with a status of 400 ()
 [EnhancedVapiMcpService] Operation failed (attempt 1/3): 
overrideMethod @ installHook.js:1
api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: the server responded with a status of 400 ()
 [EnhancedVapiMcpService] Operation failed (attempt 2/3): 
overrideMethod @ installHook.js:1
api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a:1  Failed to load resource: the server responded with a status of 400 ()
 [EnhancedVapiMcpService] Operation failed (attempt 3/3): 
overrideMethod @ installHook.js:1
 [AgentTab] ❌ Enhanced Vapi service failed, trying fallback: 
overrideMethod @ installHook.js:1
 [2025-06-06T10:55:50.255Z] [VapiAssistantService] Synchronizing attorney data to Vapi: 695b5caf-4884-456d-a3b1-7765427b6095 
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1  Failed to load resource: net::ERR_FAILED
hook.js:608 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
overrideMethod @ hook.js:608
loggerUtils.js:133 [2025-06-06T10:55:50.294Z] [VapiAssistantService] Updating existing assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
loggerUtils.js:133 [2025-06-06T10:55:50.411Z] [VapiAssistantService] Found existing assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
loggerUtils.js:133 [2025-06-06T10:55:50.411Z] [VapiAssistantService] Updating assistant configuration: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
hook.js:608 [2025-06-06T10:55:50.556Z] [VapiAssistantService] Error updating assistant configuration: Cannot read properties of undefined (reading 'model') 
overrideMethod @ hook.js:608
hook.js:608 [2025-06-06T10:55:50.556Z] [VapiAssistantService] Error synchronizing attorney to Vapi: Cannot read properties of undefined (reading 'model') 
overrideMethod @ hook.js:608
hook.js:608 [AgentTab] Error synchronizing voice with Vapi: TypeError: Cannot read properties of undefined (reading 'model')
    at za.updateAssistantConfiguration (vapiAssistantService.js:388:57)
    at async za.syncAttorneyToVapi (vapiAssistantService.js:680:32)
    at async Ke (AgentTab.jsx:991:32)
overrideMethod @ hook.js:608
VM105:4 enumerateDevices took longer than expected: 156
value @ VM105:4
VM105:4 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
Refused to load the script 'blob:https://legalscout.net/2ed24e17-3c6a-4fd4-80b5-f0abded3aa3d' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

simple-preview:1 Refused to load the script 'blob:https://legalscout.net/2ed24e17-3c6a-4fd4-80b5-f0abded3aa3d' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

VM105:4 KrispSDK - KrispSDK:createNoiseFilter AbortError: Unable to load a worklet's module.
error @ VM105:4
VM105:4 error applying mic processor. KrispInitError: Error creating krisp filter: Error: WORKLET_NOT_SUPPORTED
value @ VM105:4
VM105:4 Meeting ended due to ejection: Meeting has ended
value @ VM105:4
useVapiCall.js:94 Vapi error: Object
onError @ useVapiCall.js:94
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
useVapiCall.js:94 Vapi error: Object
onError @ useVapiCall.js:94
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
useVapiCall.js:94 Vapi error: Object
onError @ useVapiCall.js:94
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
vapiLogger.js:103 [10:59:52] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
mcp.vapi.ai/mcp:1 
            
            
           Failed to load resource: net::ERR_FAILED
hook.js:608 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
vapiLogger.js:103 [10:59:53] [VapiMcpService] Retrieving assistant Object
vapiLogger.js:103 [10:59:53] [VapiMcpService] Assistant verified in Vapi Object
VM129:4 enumerateDevices took longer than expected: 197
value @ VM129:4
VM129:4 enumerateDevices took longer than expected: 57
value @ VM129:4
VM129:4 enumerateDevices took longer than expected: 74
value @ VM129:4
VM129:4 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
Refused to load the script 'blob:https://legalscout.net/6ecdf5b1-55ab-4e76-b6eb-c092645ca3db' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

simple-preview:1 Refused to load the script 'blob:https://legalscout.net/6ecdf5b1-55ab-4e76-b6eb-c092645ca3db' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

VM129:4 KrispSDK - KrispSDK:createNoiseFilter AbortError: Unable to load a worklet's module.
error @ VM129:4
VM129:4 error applying mic processor. KrispInitError: Error creating krisp filter: Error: WORKLET_NOT_SUPPORTED
value @ VM129:4
VM129:4 enumerateDevices took longer than expected: 55
value @ VM129:4
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
VM135:4 enumerateDevices took longer than expected: 64
value @ VM135:4
VM135:4 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
Refused to load the script 'blob:https://legalscout.net/7150d1ac-ea01-4337-9b8d-b93db2855ecb' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

simple-preview:1 Refused to load the script 'blob:https://legalscout.net/7150d1ac-ea01-4337-9b8d-b93db2855ecb' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

VM135:4 KrispSDK - KrispSDK:createNoiseFilter AbortError: Unable to load a worklet's module.
error @ VM135:4
VM135:4 error applying mic processor. KrispInitError: Error creating krisp filter: Error: WORKLET_NOT_SUPPORTED
value @ VM135:4
VM135:4 Meeting ended due to ejection: Meeting has ended
value @ VM135:4
value @ VM135:4
p @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492076124370.648259330281936'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492076124370.648259330281936'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17492076124370.648259330281936'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM135:4
value @ VM135:4
value @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
msgSigChannel @ VM135:4
eval @ VM135:4
g @ VM135:4
i @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
eval @ VM135:4
