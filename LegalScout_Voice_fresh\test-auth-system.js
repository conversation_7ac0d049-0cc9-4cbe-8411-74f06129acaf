/**
 * Authentication System Test Script
 * 
 * Run this in the browser console to test the unified authentication system
 */

console.log('🧪 Testing Unified Authentication System...');

// Test 1: Check if unified auth service is available
async function testUnifiedAuthService() {
  console.log('\n📋 Test 1: Unified Auth Service Availability');
  
  try {
    const { unifiedAuthService } = await import('./src/services/unifiedAuthService.js');
    console.log('✅ UnifiedAuthService imported successfully');
    
    // Check if service has expected methods
    const expectedMethods = ['initialize', 'signInWithGoogle', 'handleOAuthCallback', 'signOut', 'getAuthState'];
    expectedMethods.forEach(method => {
      if (typeof unifiedAuthService[method] === 'function') {
        console.log(`✅ Method ${method} is available`);
      } else {
        console.log(`❌ Method ${method} is missing`);
      }
    });
    
    return unifiedAuthService;
  } catch (error) {
    console.error('❌ Failed to import UnifiedAuthService:', error);
    return null;
  }
}

// Test 2: Check environment configuration
function testEnvironmentConfig() {
  console.log('\n📋 Test 2: Environment Configuration');
  
  // Check environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY;
  const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
  
  console.log('Environment Variables:');
  console.log(`✅ Supabase URL: ${supabaseUrl ? 'Set' : 'Missing'}`);
  console.log(`✅ Supabase Key: ${supabaseKey ? 'Set' : 'Missing'}`);
  console.log(`✅ Google Client ID: ${googleClientId ? 'Set' : 'Missing'}`);
  
  // Check current environment
  const hostname = window.location.hostname;
  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1';
  const isProduction = hostname.includes('vercel.app') || hostname.includes('legalscout.net');
  
  console.log('Environment Detection:');
  console.log(`🌍 Hostname: ${hostname}`);
  console.log(`🔧 Development: ${isDevelopment}`);
  console.log(`🚀 Production: ${isProduction}`);
  console.log(`📍 Base URL: ${window.location.origin}`);
  
  return {
    hasSupabaseUrl: !!supabaseUrl,
    hasSupabaseKey: !!supabaseKey,
    hasGoogleClientId: !!googleClientId,
    isDevelopment,
    isProduction,
    baseUrl: window.location.origin
  };
}

// Test 3: Check redirect URL configuration
async function testRedirectUrls() {
  console.log('\n📋 Test 3: Redirect URL Configuration');
  
  try {
    const { unifiedAuthService } = await import('./src/services/unifiedAuthService.js');
    const redirectUrls = unifiedAuthService.getRedirectUrls();
    
    console.log('Redirect URLs:');
    console.log(`🔧 Development: ${redirectUrls.development}`);
    console.log(`🚀 Production: ${redirectUrls.production}`);
    console.log(`📍 Current: ${redirectUrls.current}`);
    console.log(`🌍 Is Development: ${redirectUrls.isDevelopment}`);
    
    return redirectUrls;
  } catch (error) {
    console.error('❌ Error getting redirect URLs:', error);
    return null;
  }
}

// Test 4: Test Supabase client initialization
async function testSupabaseClient() {
  console.log('\n📋 Test 4: Supabase Client Initialization');
  
  try {
    const { getSupabaseClient, isSupabaseConfigured } = await import('./src/lib/supabase.js');
    
    const isConfigured = isSupabaseConfigured();
    console.log(`✅ Supabase Configured: ${isConfigured}`);
    
    if (isConfigured) {
      const client = await getSupabaseClient();
      console.log('✅ Supabase client initialized successfully');
      
      // Test auth methods
      if (client.auth) {
        console.log('✅ Auth methods available');
        
        // Check current session
        try {
          const { data: { session }, error } = await client.auth.getSession();
          if (error) {
            console.log('⚠️ Session check error:', error.message);
          } else {
            console.log(`📋 Current session: ${session ? 'Active' : 'None'}`);
            if (session) {
              console.log(`👤 User: ${session.user.email}`);
            }
          }
        } catch (sessionError) {
          console.log('⚠️ Session check failed:', sessionError.message);
        }
      } else {
        console.log('❌ Auth methods not available');
      }
    }
    
    return isConfigured;
  } catch (error) {
    console.error('❌ Error testing Supabase client:', error);
    return false;
  }
}

// Test 5: Test authentication state
async function testAuthState() {
  console.log('\n📋 Test 5: Authentication State');
  
  try {
    const { unifiedAuthService } = await import('./src/services/unifiedAuthService.js');
    
    // Initialize the service
    await unifiedAuthService.initialize();
    
    // Get current state
    const authState = unifiedAuthService.getAuthState();
    console.log('Authentication State:');
    console.log(`👤 User: ${authState.user ? authState.user.email : 'None'}`);
    console.log(`📋 Session: ${authState.session ? 'Active' : 'None'}`);
    console.log(`⏳ Loading: ${authState.loading}`);
    console.log(`❌ Error: ${authState.error || 'None'}`);
    console.log(`🔐 Authenticated: ${unifiedAuthService.isAuthenticated()}`);
    
    return authState;
  } catch (error) {
    console.error('❌ Error testing auth state:', error);
    return null;
  }
}

// Test 6: Simulate authentication flow (without actually signing in)
async function testAuthFlow() {
  console.log('\n📋 Test 6: Authentication Flow Simulation');
  
  try {
    const { unifiedAuthService } = await import('./src/services/unifiedAuthService.js');
    
    console.log('🔐 Testing sign-in flow (simulation only)...');
    
    // Test the redirect URL generation
    const redirectUrls = unifiedAuthService.getRedirectUrls();
    console.log(`✅ Redirect URL would be: ${redirectUrls.current}`);
    
    // Test callback URL detection
    const currentUrl = window.location.href;
    const isCallbackUrl = currentUrl.includes('/auth/callback');
    console.log(`📍 Current URL: ${currentUrl}`);
    console.log(`🔄 Is callback URL: ${isCallbackUrl}`);
    
    if (isCallbackUrl) {
      console.log('🔄 This appears to be a callback URL - testing callback handling...');
      // Note: We won't actually call handleOAuthCallback as it might interfere with real auth
      console.log('⚠️ Callback handling test skipped to avoid interference');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error testing auth flow:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running Authentication System Tests...');
  
  const service = await testUnifiedAuthService();
  if (!service) {
    console.log('❌ Cannot continue tests without unified auth service');
    return;
  }
  
  const envConfig = testEnvironmentConfig();
  const redirectUrls = await testRedirectUrls();
  const supabaseConfigured = await testSupabaseClient();
  const authState = await testAuthState();
  const authFlowTest = await testAuthFlow();
  
  console.log('\n✅ Test Suite Completed!');
  console.log('\n📝 Summary:');
  console.log('1. Unified auth service should be available with all methods');
  console.log('2. Environment variables should be properly configured');
  console.log('3. Redirect URLs should be environment-appropriate');
  console.log('4. Supabase client should initialize successfully');
  console.log('5. Authentication state should be properly managed');
  console.log('6. Authentication flow should be ready for testing');
  
  console.log('\n🔍 Current Status:');
  console.log(`   - Environment: ${envConfig.isDevelopment ? 'Development' : 'Production'}`);
  console.log(`   - Supabase: ${supabaseConfigured ? 'Configured' : 'Not Configured'}`);
  console.log(`   - Auth State: ${authState?.user ? 'Authenticated' : 'Not Authenticated'}`);
  console.log(`   - Ready for Auth: ${supabaseConfigured && service ? 'Yes' : 'No'}`);
  
  return {
    service,
    envConfig,
    redirectUrls,
    supabaseConfigured,
    authState,
    authFlowTest
  };
}

// Auto-run tests
runAllTests();

// Export for manual testing
window.authSystemTests = {
  runAllTests,
  testUnifiedAuthService,
  testEnvironmentConfig,
  testRedirectUrls,
  testSupabaseClient,
  testAuthState,
  testAuthFlow
};

console.log('\n💡 You can run individual tests by calling:');
console.log('   window.authSystemTests.testAuthState()');
console.log('   window.authSystemTests.testSupabaseClient()');
console.log('   etc.');
