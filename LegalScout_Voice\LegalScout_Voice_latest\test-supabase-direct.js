// Simple script to test Supabase connection with direct credentials
import { createClient } from '@supabase/supabase-js';

// Direct Supabase credentials
const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs';

console.log('Using direct Supabase credentials:');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey.substring(0, 10) + '...' + supabaseKey.substring(supabaseKey.length - 10));

// Create Supabase client
try {
  console.log('Creating Supabase client...');
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  // Test connection
  console.log('Testing connection...');
  supabase
    .from('attorneys')
    .select('*')
    .limit(1)
    .then(({ data, error }) => {
      if (error) {
        console.error('Error querying attorneys table:', error);
      } else {
        console.log('Connection successful!');
        console.log('Data:', data);
      }
    })
    .catch(error => {
      console.error('Unexpected error:', error);
    });
} catch (error) {
  console.error('Error creating Supabase client:', error);
}
