export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const response = await fetch('https://api.makecomputer.io/v1/actions/composio/initiate-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MCP_API_KEY}`
      },
      body: JSON.stringify(req.body)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to initiate connection');
    }

    // Return the authorization URL and any other necessary data
    return res.status(200).json({
      authUrl: data.authorizationUrl,
      connectionId: data.connectionId
    });
  } catch (error) {
    console.error('Connection initiation error:', error);
    return res.status(500).json({ error: 'Failed to initiate connection' });
  }
} 