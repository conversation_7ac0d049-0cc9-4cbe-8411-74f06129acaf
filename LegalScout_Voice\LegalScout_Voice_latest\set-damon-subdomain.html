<!DOCTYPE html>
<html>
<head>
    <title>Set Damon Subdomain</title>
</head>
<body>
    <h1>Setting Test Subdomain to 'damon'</h1>
    <p id="status">Setting up...</p>
    
    <script>
        // Set the test subdomain to 'damon'
        localStorage.setItem('legalscout_test_subdomain', 'damon');
        
        document.getElementById('status').innerHTML = 
            'Test subdomain set to "damon"<br>' +
            '<a href="http://localhost:4173/dashboard">Go to Dashboard</a>';
        
        console.log('Test subdomain set to: damon');
        
        // Auto-redirect after 2 seconds
        setTimeout(() => {
            window.location.href = 'http://localhost:4173/dashboard';
        }, 2000);
    </script>
</body>
</html>
