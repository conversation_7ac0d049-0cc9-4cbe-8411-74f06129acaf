import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import useVapiCall from '../hooks/useVapiCall';
import TestToolExecution from '../components/TestToolExecution';
import { vapiService } from '../services/vapiService.jsx';
import { MOCK_DOSSIER_DATA } from '../constants/vapiConstants';

/**
 * A debug page specifically for testing Vapi Tool execution
 */
const ToolDebugger = () => {
  const navigate = useNavigate();
  const [apiKey, setApiKey] = useState('');
  const [isVapiInitialized, setIsVapiInitialized] = useState(false);
  const [useMockData, setUseMockData] = useState(true);
  
  // Use hook with subdomain
  const {
    vapi,
    status,
    dossierData,
    errorMessage,
    startCall,
    stopCall
  } = useVapiCall({ 
    subdomain: 'default',
    onEndCall: () => console.log('Call ended from debugger'),
  });

  // Initialize Vapi with the provided API key
  const initializeVapi = () => {
    if (!apiKey.trim()) {
      alert('Please enter a valid Vapi API key');
      return;
    }

    try {
      const vapiInstance = vapiService.initialize(apiKey, 'default');
      if (vapiInstance) {
        setIsVapiInitialized(true);
        console.log('Vapi initialized with API key');
      }
    } catch (error) {
      console.error('Error initializing Vapi:', error);
      alert(`Error initializing Vapi: ${error.message}`);
    }
  };

  return (
    <div className="tool-debugger">
      <header>
        <h1>Vapi Tool Execution Debugger</h1>
        <button onClick={() => navigate('/')} className="back-button">
          Back to Home
        </button>
      </header>

      <div className="debugger-content">
        {!isVapiInitialized ? (
          <div className="initialization-form">
            <h2>Initialize Vapi</h2>
            <p>Enter your Vapi API key to initialize the SDK:</p>
            <div className="api-key-input">
              <input
                type="text"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Your Vapi API key"
              />
              <button onClick={initializeVapi}>Initialize</button>
            </div>
          </div>
        ) : (
          <>
            <div className="dossier-mock-control">
              <label>
                <input
                  type="checkbox"
                  checked={useMockData}
                  onChange={(e) => setUseMockData(e.target.checked)}
                />
                Use mock dossier data for testing
              </label>
            </div>

            <TestToolExecution 
              vapi={vapi} 
              dossierData={useMockData ? MOCK_DOSSIER_DATA : dossierData} 
            />

            <div className="developer-notes">
              <h3>Developer Notes</h3>
              <p>This page allows you to test Vapi tool execution directly without starting a call. The tool execution handler has been enhanced to properly handle the toolCallId in both the request and response.</p>
              <ul>
                <li>Select a tool from the list above</li>
                <li>Configure the parameters in JSON format</li>
                <li>Click "Execute Tool" to send the request to Make.com</li>
                <li>Check both the logs on this page and the browser console for detailed logs</li>
              </ul>
              <p>All webhook requests now include the toolCallId in both the request payload and headers. Responses are expected in the format:</p>
              <pre>{`{
  "results": [
    {
      "toolCallId": "your-tool-call-id",
      "result": "Result data here"
    }
  ]
}`}</pre>
              <p>If the response doesn't follow this format, it will be automatically reformatted by the webhook handler.</p>
            </div>
          </>
        )}
      </div>

      <style jsx>{`
        .tool-debugger {
          padding: 20px;
          max-width: 960px;
          margin: 0 auto;
        }

        header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
        }

        h1 {
          margin: 0;
          color: #333;
        }

        .back-button {
          background: #333;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }

        .back-button:hover {
          background: #444;
        }

        .initialization-form {
          background: #f5f5f5;
          padding: 30px;
          border-radius: 8px;
          margin-bottom: 20px;
        }

        .api-key-input {
          display: flex;
          margin-top: 15px;
        }

        .api-key-input input {
          flex: 1;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px 0 0 4px;
          font-size: 16px;
        }

        .api-key-input button {
          padding: 10px 20px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 0 4px 4px 0;
          cursor: pointer;
          font-size: 16px;
        }

        .dossier-mock-control {
          margin-bottom: 20px;
          background: #fff;
          padding: 15px;
          border-radius: 4px;
          border: 1px solid #eee;
        }

        .developer-notes {
          margin-top: 30px;
          background: #f9f9f9;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #007bff;
        }

        pre {
          background: #f0f0f0;
          padding: 15px;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 14px;
        }

        ul {
          margin-left: 20px;
        }
      `}</style>
    </div>
  );
};

export default ToolDebugger; 