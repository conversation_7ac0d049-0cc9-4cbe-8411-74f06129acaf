/**
 * Test Main Entry Point
 * 
 * This bypasses the main app entirely to test if the issue is in the app logic
 * or in the build/environment setup.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import TestApp from './src/TestApp';

// Comprehensive error handling
window.addEventListener('error', (event) => {
  console.error('🚨 Global Error:', event.error);
  console.error('Stack:', event.error?.stack);
  console.error('Filename:', event.filename);
  console.error('Line:', event.lineno);
  console.error('Column:', event.colno);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Unhandled Promise Rejection:', event.reason);
  console.error('Promise:', event.promise);
});

// Log environment info immediately
console.log('🚀 Test Main Entry Point Starting...');
console.log('Environment:', {
  NODE_ENV: import.meta.env.NODE_ENV,
  MODE: import.meta.env.MODE,
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD,
  BASE_URL: import.meta.env.BASE_URL
});

// Test React availability
console.log('React version:', React.version);
console.log('ReactDOM version:', ReactDOM.version);

// Test DOM readiness
if (document.readyState === 'loading') {
  console.log('DOM is still loading...');
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing React...');
    initializeReact();
  });
} else {
  console.log('DOM already loaded, initializing React...');
  initializeReact();
}

function initializeReact() {
  try {
    const rootElement = document.getElementById('root');
    
    if (!rootElement) {
      console.error('❌ Root element not found!');
      document.body.innerHTML = `
        <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: monospace;">
          <h1>❌ Root Element Missing</h1>
          <p>The #root element was not found in the DOM.</p>
          <p>Current body content:</p>
          <pre>${document.body.innerHTML}</pre>
        </div>
      `;
      return;
    }

    console.log('✅ Root element found:', rootElement);
    
    const root = ReactDOM.createRoot(rootElement);
    console.log('✅ React root created');
    
    root.render(
      <React.StrictMode>
        <TestApp />
      </React.StrictMode>
    );
    
    console.log('✅ React app rendered');
    
  } catch (error) {
    console.error('❌ Failed to initialize React:', error);
    console.error('Stack:', error.stack);
    
    // Fallback error display
    document.body.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: monospace;">
        <h1>❌ React Initialization Failed</h1>
        <p><strong>Error:</strong> ${error.message}</p>
        <p><strong>Stack:</strong></p>
        <pre>${error.stack}</pre>
        <h2>Environment Info:</h2>
        <pre>${JSON.stringify({
          NODE_ENV: import.meta.env.NODE_ENV,
          MODE: import.meta.env.MODE,
          DEV: import.meta.env.DEV,
          PROD: import.meta.env.PROD,
          userAgent: navigator.userAgent,
          url: window.location.href
        }, null, 2)}</pre>
      </div>
    `;
  }
}
