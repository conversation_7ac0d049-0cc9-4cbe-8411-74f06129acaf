/**
 * Vapi MCP Debugger
 * 
 * A focused debugging utility for Vapi MCP server integration.
 * This module provides tools for:
 * - Structured logging for MCP connection issues
 * - Network request/response logging for API calls
 * - Environment variable verification
 * - Connection status tracking
 */

// Default configuration
const DEFAULT_CONFIG = {
  enabled: true,
  logToConsole: true,
  logToMemory: true,
  maxLogEntries: 100,
  networkLogging: {
    enabled: true,
    includeHeaders: true,
    includeBody: true,
    maxBodyLength: 500
  }
};

// In-memory log storage
const connectionLogs = [];
const networkLogs = [];
const errors = [];

// Current configuration
let config = { ...DEFAULT_CONFIG };

/**
 * Configure the Vapi MCP debugger
 * @param {Object} newConfig - Configuration options
 */
export const configureDebugger = (newConfig = {}) => {
  config = { ...DEFAULT_CONFIG, ...newConfig };
  
  // Make configuration available globally for debugging
  if (typeof window !== 'undefined') {
    window.VapiMcpDebugger = {
      config,
      connectionLogs,
      networkLogs,
      errors,
      getLogs: () => [...connectionLogs],
      getNetworkLogs: () => [...networkLogs],
      getErrors: () => [...errors],
      clearLogs: () => {
        connectionLogs.length = 0;
        console.log('Vapi MCP connection logs cleared');
      },
      clearNetworkLogs: () => {
        networkLogs.length = 0;
        console.log('Vapi MCP network logs cleared');
      },
      clearErrors: () => {
        errors.length = 0;
        console.log('Vapi MCP errors cleared');
      },
      clearAll: () => {
        connectionLogs.length = 0;
        networkLogs.length = 0;
        errors.length = 0;
        console.log('All Vapi MCP logs cleared');
      },
      // Utility to check environment variables
      checkEnvironment: () => checkVapiEnvironment(),
      // Utility to test connection
      testConnection: async () => {
        try {
          const result = await testVapiMcpConnection();
          console.log('Vapi MCP connection test result:', result);
          return result;
        } catch (error) {
          console.error('Vapi MCP connection test failed:', error);
          return { success: false, error: error.message };
        }
      }
    };
  }
};

/**
 * Log a connection event
 * @param {string} status - Connection status
 * @param {string} message - Log message
 * @param {Object} details - Additional details
 */
export const logConnection = (status, message, details = {}) => {
  if (!config.enabled) return;
  
  const entry = {
    timestamp: new Date().toISOString(),
    status,
    message,
    details: { ...details }
  };
  
  // Add to logs
  connectionLogs.push(entry);
  
  // Trim logs if they exceed max entries
  if (connectionLogs.length > config.maxLogEntries) {
    connectionLogs.splice(0, connectionLogs.length - config.maxLogEntries);
  }
  
  // Log to console
  if (config.logToConsole) {
    const statusColor = 
      status === 'success' ? 'color: green; font-weight: bold' :
      status === 'error' ? 'color: red; font-weight: bold' :
      status === 'warning' ? 'color: orange; font-weight: bold' :
      'color: blue; font-weight: bold';
    
    console.groupCollapsed(`%c[Vapi MCP] ${status.toUpperCase()}: ${message}`, statusColor);
    console.log('Details:', details);
    console.log('Timestamp:', entry.timestamp);
    console.groupEnd();
  }
  
  // Add to errors if it's an error
  if (status === 'error') {
    errors.push(entry);
  }
};

/**
 * Log a network request or response
 * @param {string} type - 'request' or 'response'
 * @param {string} url - Request URL
 * @param {string} method - HTTP method
 * @param {number} status - HTTP status code (for responses)
 * @param {Object} headers - HTTP headers
 * @param {Object|string} body - Request or response body
 */
export const logNetwork = (type, url, method, status, headers, body) => {
  if (!config.enabled || !config.networkLogging.enabled) return;
  
  const entry = {
    timestamp: new Date().toISOString(),
    type,
    url,
    method,
    status,
    headers: config.networkLogging.includeHeaders ? headers : undefined,
    body: config.networkLogging.includeBody ? (
      typeof body === 'string' 
        ? (body.length > config.networkLogging.maxBodyLength 
            ? body.substring(0, config.networkLogging.maxBodyLength) + '...' 
            : body)
        : body
    ) : undefined
  };
  
  // Add to logs
  networkLogs.push(entry);
  
  // Trim logs if they exceed max entries
  if (networkLogs.length > config.maxLogEntries) {
    networkLogs.splice(0, networkLogs.length - config.maxLogEntries);
  }
  
  // Log to console
  if (config.logToConsole) {
    const typeColor = type === 'request' ? 'color: blue' : 'color: green';
    const statusColor = 
      (status >= 200 && status < 300) ? 'color: green' :
      (status >= 400) ? 'color: red' :
      'color: orange';
    
    if (type === 'request') {
      console.groupCollapsed(`%c[Vapi MCP] ${method} ${url}`, typeColor);
      console.log('Headers:', headers);
      console.log('Body:', body);
      console.groupEnd();
    } else {
      console.groupCollapsed(`%c[Vapi MCP] Response: %c${status}%c ${url}`, typeColor, statusColor, 'color: black');
      console.log('Headers:', headers);
      console.log('Body:', body);
      console.groupEnd();
    }
  }
};

/**
 * Check Vapi environment variables
 * @returns {Object} Environment status
 */
export const checkVapiEnvironment = () => {
  const environment = {
    VITE_VAPI_PUBLIC_KEY: typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set',
    VITE_VAPI_SECRET_KEY: typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY ? 'Set' : 'Not set',
    VAPI_TOKEN: typeof process !== 'undefined' && process.env?.VAPI_TOKEN ? 'Set' : 'Not set',
    localStorage_vapi_api_key: typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key') ? 'Set' : 'Not set',
    isDevelopment: typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development',
    origin: typeof window !== 'undefined' ? window.location.origin : 'Unknown'
  };
  
  // Log to console
  if (config.logToConsole) {
    console.group('[Vapi MCP] Environment Check');
    console.table(environment);
    console.groupEnd();
  }
  
  return environment;
};

/**
 * Test Vapi MCP connection
 * @returns {Promise<Object>} Connection test result
 */
export const testVapiMcpConnection = async () => {
  const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||
                (typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key'));
  
  if (!apiKey) {
    logConnection('error', 'No API key available for connection test');
    return { success: false, error: 'No API key available' };
  }
  
  // Test direct connection to Vapi API
  try {
    logConnection('info', 'Testing direct connection to Vapi API', { apiKey: `${apiKey.substring(0, 5)}...` });
    
    const endpoints = [
      'https://api.vapi.ai/v1/assistants?limit=1',
      'https://api.vapi.ai/assistants?limit=1',
      'https://api.vapi.ai/api/v1/assistants?limit=1',
      'https://api.vapi.ai/api/assistants?limit=1'
    ];
    
    for (const endpoint of endpoints) {
      try {
        logNetwork('request', endpoint, 'GET', null, { 'Authorization': `Bearer ${apiKey}` }, null);
        
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        const responseBody = await response.json();
        
        logNetwork('response', endpoint, 'GET', response.status, response.headers, responseBody);
        
        if (response.ok) {
          logConnection('success', `Direct connection successful with endpoint: ${endpoint}`);
          return { 
            success: true, 
            endpoint, 
            mode: 'direct',
            status: response.status,
            data: responseBody
          };
        } else {
          logConnection('warning', `Endpoint ${endpoint} returned status: ${response.status}`);
        }
      } catch (error) {
        logConnection('warning', `Error with endpoint ${endpoint}`, { error: error.message });
      }
    }
    
    logConnection('error', 'All direct API endpoints failed');
    return { success: false, error: 'All direct API endpoints failed', mode: 'direct' };
  } catch (error) {
    logConnection('error', 'Error testing Vapi connection', { error: error.message });
    return { success: false, error: error.message };
  }
};

// Initialize the debugger
configureDebugger();

// Export default instance
export default {
  configureDebugger,
  logConnection,
  logNetwork,
  checkVapiEnvironment,
  testVapiMcpConnection
};
