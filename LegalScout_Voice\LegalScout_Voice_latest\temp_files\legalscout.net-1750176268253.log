 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🚀 [Supabase-Fixed] Initializing client...
 🔧 [Supabase-Fixed] Creating client for environment: Object
 ❌ [Supabase-Fixed] Failed to create client: 
Hte @ index-57aef433.js:48
 ❌ [Supabase-Fixed] Initialization failed: 
$I @ index-57aef433.js:48
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
 [VapiLoader] Starting Vapi SDK loading process
index-57aef433.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
index-57aef433.js:141 [VapiMcpService] Created clean fetch from iframe
index-57aef433.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized Object
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at gxe._performInitialization (index-57aef433.js:426:51396)
    at gxe.initialize (index-57aef433.js:426:51150)
    at index-57aef433.js:426:55223
Hte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at gxe._performInitialization (index-57aef433.js:426:51396)
    at gxe.initialize (index-57aef433.js:426:51150)
    at index-57aef433.js:426:55223
$I @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Auto-initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at index-57aef433.js:48:76515
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: channel
get @ index-57aef433.js:48
index-57aef433.js:385 ❌ [AssistantSyncManager] Error initializing real-time subscriptions: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Proxy.<anonymous> (index-57aef433.js:48:80847)
    at X1e.initializeRealtimeSubscriptions (index-57aef433.js:385:47685)
initializeRealtimeSubscriptions @ index-57aef433.js:385
index-57aef433.js:426 ❌ [UnifiedAuth-Fixed] Initialization error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at gxe._performInitialization (index-57aef433.js:426:51396)
    at gxe.initialize (index-57aef433.js:426:51150)
    at index-57aef433.js:426:55223
_performInitialization @ index-57aef433.js:426
index-57aef433.js:48 ⚠️ [AssistantContextValidator] No valid assistant context found: Object
resolveAssistantContext @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Minimal client failed, trying full config: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:77597)
    at zI (index-57aef433.js:48:78393)
    at index-57aef433.js:48:81248
qte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at index-57aef433.js:48:81248
qte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at index-57aef433.js:48:81248
zI @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Auto-initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at index-57aef433.js:48:81248
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:82702
    at index-57aef433.js:48:83558
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
Hte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:82702
    at index-57aef433.js:48:83558
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
$I @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:83596
    at index-57aef433.js:48:84310
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
Hte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:83596
    at index-57aef433.js:48:84310
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
$I @ index-57aef433.js:48
index-57aef433.js:48 🔐 [AuthContext-P1] Unexpected error checking auth: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:82702
    at index-57aef433.js:48:83558
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 Failed to set up auth listener: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at index-57aef433.js:48:83596
    at index-57aef433.js:48:84310
    at wE (index-57aef433.js:40:24270)
    at Ap (index-57aef433.js:40:42393)
    at index-57aef433.js:40:40710
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: from
get @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [AssistantAwareContext] Subdomain lookup failed: Supabase client not initialized. Call getSupabaseClient() first.
m @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: from
get @ index-57aef433.js:48
index-57aef433.js:334 Error getting assistant config: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Proxy.<anonymous> (index-57aef433.js:48:80847)
    at t7.getAssistantConfig (index-57aef433.js:334:6660)
    at m (index-57aef433.js:48:92052)
getAssistantConfig @ index-57aef433.js:334
index-57aef433.js:141 [VapiMcpService] UUID format detected as assistant ID: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
getAssistant @ index-57aef433.js:141
index-57aef433.js:141 [VapiMcpService] This might be a Supabase attorney ID instead of a Vapi assistant ID
getAssistant @ index-57aef433.js:141
index-57aef433.js:331 [2025-06-17T16:04:13.432Z] [VapiAssistantService] Retrieved assistant: 50e13a9e-22dd-4fe8-a03e-de627c5206c1 
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: from
get @ index-57aef433.js:48
index-57aef433.js:334 Error saving assistant config: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Proxy.<anonymous> (index-57aef433.js:48:80847)
    at t7.saveAssistantConfig (index-57aef433.js:334:7593)
    at Xu.syncAssistantNameFromVapi (index-57aef433.js:385:43872)
    at async m (index-57aef433.js:48:92317)
saveAssistantConfig @ index-57aef433.js:334
index-57aef433.js:385 [AssistantDataService] Could not sync assistant name from Vapi: Supabase client not initialized. Call getSupabaseClient() first.
syncAssistantNameFromVapi @ index-57aef433.js:385
index-57aef433.js:48 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ index-57aef433.js:48
index-57aef433.js:48 ⚠️ [Supabase-Fixed] Minimal client failed, trying full config: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:77597)
    at zI (index-57aef433.js:48:78393)
    at Ua (index-57aef433.js:48:78905)
    at VEe.loadAttorneyById (index-57aef433.js:480:35775)
    at index-57aef433.js:480:28911
qte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at Ua (index-57aef433.js:48:78905)
    at VEe.loadAttorneyById (index-57aef433.js:480:35775)
    at index-57aef433.js:480:28911
qte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at Ua (index-57aef433.js:48:78905)
    at VEe.loadAttorneyById (index-57aef433.js:480:35775)
    at index-57aef433.js:480:28911
zI @ index-57aef433.js:48
index-57aef433.js:480 [AttorneyProfileManager] Error loading attorney by id: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at Ua (index-57aef433.js:48:78905)
    at VEe.loadAttorneyById (index-57aef433.js:480:35775)
    at index-57aef433.js:480:28911
loadAttorneyById @ index-57aef433.js:480
index-57aef433.js:480 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at qte (index-57aef433.js:48:78125)
    at zI (index-57aef433.js:48:78393)
    at Ua (index-57aef433.js:48:78905)
    at VEe.loadAttorneyById (index-57aef433.js:480:35775)
    at index-57aef433.js:480:28911
(anonymous) @ index-57aef433.js:480
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
Hte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
$I @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Google OAuth error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
s8 @ index-57aef433.js:48
index-57aef433.js:50 ❌ [UnifiedAuth] Google sign-in error: Error: Cannot read properties of undefined (reading 'headers')
    at tre.signInWithGoogle (index-57aef433.js:50:3511)
    at async S (index-57aef433.js:426:43882)
signInWithGoogle @ index-57aef433.js:50
index-57aef433.js:426 ❌ [AuthOverlay] Google sign-in error: Error: Cannot read properties of undefined (reading 'headers')
    at S (index-57aef433.js:426:43930)
S @ index-57aef433.js:426
index-57aef433.js:48 ❌ [Supabase-Fixed] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
Hte @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Initialization failed: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
$I @ index-57aef433.js:48
index-57aef433.js:48 ❌ [Supabase-Fixed] Google OAuth error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new $te (index-57aef433.js:48:69953)
    at OT (index-57aef433.js:48:72419)
    at Hte (index-57aef433.js:48:73425)
    at $I (index-57aef433.js:48:73984)
    at zo (index-57aef433.js:48:74496)
    at s8 (index-57aef433.js:48:74535)
    at tre.signInWithGoogle (index-57aef433.js:50:3489)
    at S (index-57aef433.js:426:43891)
    at Object.kK (index-57aef433.js:37:9864)
    at PK (index-57aef433.js:37:10018)
s8 @ index-57aef433.js:48
index-57aef433.js:50 ❌ [UnifiedAuth] Google sign-in error: Error: Cannot read properties of undefined (reading 'headers')
    at tre.signInWithGoogle (index-57aef433.js:50:3511)
    at async S (index-57aef433.js:426:43882)
signInWithGoogle @ index-57aef433.js:50
index-57aef433.js:426 ❌ [AuthOverlay] Google sign-in error: Error: Cannot read properties of undefined (reading 'headers')
    at S (index-57aef433.js:426:43930)
S @ index-57aef433.js:426
