/**
 * Vapi Credentials Service
 * 
 * A secure service for managing Vapi API credentials.
 * This service ensures that sensitive credentials are only used server-side
 * and never exposed to the client.
 */

// Environment variables for Vapi credentials
const VAPI_SECRET_KEY = import.meta.env.VITE_VAPI_SECRET_KEY;
const VAPI_PUBLIC_KEY = import.meta.env.VITE_VAPI_PUBLIC_KEY;
const VAPI_HOST_URL = import.meta.env.VITE_VAPI_HOST_URL || 'https://api.vapi.ai';

// OAuth2 credentials
const VAPI_OAUTH2_URL = import.meta.env.VITE_VAPI_OAUTH2_URL;
const VAPI_OAUTH2_CLIENT_ID = import.meta.env.VITE_VAPI_OAUTH2_CLIENT_ID;
const VAPI_OAUTH2_CLIENT_SECRET = import.meta.env.VITE_VAPI_OAUTH2_CLIENT_SECRET;
const VAPI_OAUTH2_SCOPE = import.meta.env.VITE_VAPI_OAUTH2_SCOPE;

// Cache for OAuth token
let oauthTokenCache = null;
let tokenExpiryTime = 0;

/**
 * Get Vapi API credentials
 * @returns {Object} The Vapi API credentials
 */
export function getVapiCredentials() {
  return {
    secretKey: VAPI_SECRET_KEY,
    publicKey: VAPI_PUBLIC_KEY,
    hostUrl: VAPI_HOST_URL
  };
}

/**
 * Get Vapi OAuth2 credentials
 * @returns {Object} The Vapi OAuth2 credentials
 */
export function getVapiOAuth2Credentials() {
  return {
    url: VAPI_OAUTH2_URL,
    clientId: VAPI_OAUTH2_CLIENT_ID,
    clientSecret: VAPI_OAUTH2_CLIENT_SECRET,
    scope: VAPI_OAUTH2_SCOPE
  };
}

/**
 * Check if Vapi credentials are configured
 * @returns {boolean} Whether Vapi credentials are configured
 */
export function isVapiConfigured() {
  return !!VAPI_SECRET_KEY && !!VAPI_PUBLIC_KEY;
}

/**
 * Get OAuth token for Vapi API
 * @returns {Promise<string>} The OAuth token
 */
export async function getVapiOAuthToken() {
  // If we have a cached token that's still valid, use it
  const now = Date.now();
  if (oauthTokenCache && now < tokenExpiryTime) {
    return oauthTokenCache;
  }

  // If OAuth is not configured, return null
  if (!VAPI_OAUTH2_URL || !VAPI_OAUTH2_CLIENT_ID || !VAPI_OAUTH2_CLIENT_SECRET) {
    return null;
  }

  try {
    // Request a new token
    const response = await fetch(VAPI_OAUTH2_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: VAPI_OAUTH2_CLIENT_ID,
        client_secret: VAPI_OAUTH2_CLIENT_SECRET,
        scope: VAPI_OAUTH2_SCOPE || ''
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to get OAuth token: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Cache the token
    oauthTokenCache = data.access_token;
    
    // Set expiry time (subtract 5 minutes for safety)
    const expiresIn = data.expires_in || 3600;
    tokenExpiryTime = now + (expiresIn - 300) * 1000;
    
    return oauthTokenCache;
  } catch (error) {
    console.error('[VapiCredentialsService] Error getting OAuth token:', error);
    return null;
  }
}

/**
 * Create headers for Vapi API requests
 * @param {boolean} includeSecretKey - Whether to include the secret key
 * @returns {Promise<Object>} The headers for Vapi API requests
 */
export async function createVapiHeaders(includeSecretKey = false) {
  const headers = {
    'Content-Type': 'application/json'
  };

  // Add public key if available
  if (VAPI_PUBLIC_KEY) {
    headers['X-API-KEY'] = VAPI_PUBLIC_KEY;
  }

  // Add secret key if requested and available
  if (includeSecretKey && VAPI_SECRET_KEY) {
    headers['X-API-SECRET'] = VAPI_SECRET_KEY;
  }

  // Add OAuth token if available
  const oauthToken = await getVapiOAuthToken();
  if (oauthToken) {
    headers['Authorization'] = `Bearer ${oauthToken}`;
  }

  return headers;
}

/**
 * Make a request to the Vapi API
 * @param {string} endpoint - The API endpoint
 * @param {Object} options - The request options
 * @returns {Promise<Object>} The API response
 */
export async function callVapiApi(endpoint, options = {}) {
  try {
    // Default options
    const defaultOptions = {
      method: 'GET',
      headers: await createVapiHeaders(true),
      body: null
    };

    // Merge options
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    };

    // Convert body to JSON string if it's an object
    if (mergedOptions.body && typeof mergedOptions.body === 'object') {
      mergedOptions.body = JSON.stringify(mergedOptions.body);
    }

    // Make the request
    const url = `${VAPI_HOST_URL}${endpoint}`;
    const response = await fetch(url, mergedOptions);

    // Handle non-OK responses
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    // Parse the response
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('[VapiCredentialsService] Error calling Vapi API:', error);
    throw error;
  }
}

// Export a default object for convenience
export default {
  getVapiCredentials,
  getVapiOAuth2Credentials,
  isVapiConfigured,
  getVapiOAuthToken,
  createVapiHeaders,
  callVapiApi
};
