/**
 * Move Orphaned Assistants to "Recycle Bin" by renaming them
 * 
 * Since Vapi folders might be UI-only, we'll rename the orphaned assistants
 * to indicate they're in the recycle bin
 */

// The one assistant that should be kept (linked to "damon" attorney)
const KEEP_ASSISTANT_ID = 'eb8533fa-902e-46be-8ce9-df20f5c550d7';

// Orphaned assistants to rename
const ORPHANED_ASSISTANTS = [
  '368e963b-761c-45bb-91e9-8f96b8483f4d',
  'efcadef8-7d6e-49b1-91fb-e74d4223e695',
  'e0705a0d-0511-4d91-b185-56feba033b76',
  'edd3008b-ac5e-4026-97fb-d556cc1def1e',
  'a53344e3-1edb-4917-9b2d-fbd28f4e5dbf',
  'd1dae707-d4b9-4728-9a29-210ccc3b4f5e',
  '5b264951-c02d-43a0-99e0-902578192706',
  'fd273605-12af-438b-9fa4-31cc0dfb4af4',
  '2ecce4a5-a2ca-4a9a-a75d-8821b8294589'
];

async function moveToRecycleBin(dryRun = true) {
  console.log(`🗑️  ${dryRun ? 'DRY RUN - ' : ''}Moving orphaned assistants to Recycle Bin...\n`);
  
  try {
    const results = {
      renamed: [],
      failed: [],
      notFound: []
    };
    
    for (let i = 0; i < ORPHANED_ASSISTANTS.length; i++) {
      const assistantId = ORPHANED_ASSISTANTS[i];
      const newName = `[RECYCLE BIN] Orphaned Assistant ${i + 1}`;
      
      console.log(`Processing: ${assistantId}`);
      
      if (dryRun) {
        console.log(`   Would rename to: "${newName}"`);
        continue;
      }
      
      try {
        // Use MCP server to update the assistant name
        const updateResult = await window.mcp.invoke('update_assistant_vapi-mcp-server', {
          assistantId: assistantId,
          name: newName
        });
        
        if (updateResult) {
          console.log(`   ✅ Renamed to: "${newName}"`);
          results.renamed.push(assistantId);
        } else {
          console.log(`   ❌ Failed to rename`);
          results.failed.push(assistantId);
        }
        
      } catch (error) {
        console.error(`   ❌ Error renaming ${assistantId}:`, error.message);
        results.failed.push(assistantId);
      }
      
      // Small delay between updates
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    if (!dryRun) {
      console.log('\n📊 Rename Summary:');
      console.log(`   ✅ Successfully renamed: ${results.renamed.length}`);
      console.log(`   ❌ Failed to rename: ${results.failed.length}`);
      console.log(`   ⚠️  Not found: ${results.notFound.length}`);
    }
    
  } catch (error) {
    console.error('💥 Script failed:', error);
  }
}

// Check for command line arguments
const args = process.argv.slice(2);
const shouldRename = args.includes('--rename');

console.log('🚀 Vapi Assistant Recycle Bin Tool\n');

if (shouldRename) {
  console.log('⚠️  RENAME MODE ENABLED - This will modify assistant names!\n');
}

moveToRecycleBin(!shouldRename).then(() => {
  if (!shouldRename) {
    console.log('\n💡 To perform actual renaming, run: node scripts/move-to-recycle-bin.js --rename');
  }
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
