 🔄 [ForceNewAssistant] Forcing system to use new assistant...
 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
 🚨 [CriticalProductionFix] Starting critical production fixes...
 ✅ Vapi keys set globally
 ✅ Fixed assistant ID in localStorage
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using default Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 [vite] connecting...
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [vite] connected.
 [DashboardIframeManager] Iframe observer set up successfully
 ✅ [CleanAuthSolution] Auth state monitoring set up
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: Object
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 🚀 [ForceNewAssistant] Initializing force new assistant...
 🧹 [ForceNewAssistant] Clearing all localStorage...
 ✅ [ForceNewAssistant] Storage cleared
 🎯 [ForceNewAssistant] Setting new assistant ID globally...
 ✅ [ForceNewAssistant] New assistant ID set: 4e899c0a-b435-45c4-abcd-8abd3ff13ec3
 🔧 [ForceNewAssistant] Overriding assistant functions...
 ✅ [ForceNewAssistant] Assistant functions overridden
 🌐 [ForceNewAssistant] Intercepting API calls to replace assistant ID...
 ✅ [ForceNewAssistant] API call interception set up
 🔄 [ForceNewAssistant] Forcing component reload...
 [ForceNewAssistant] React detected, dispatching state update event
 ✅ [ForceNewAssistant] Component reload triggered
 🎉 [ForceNewAssistant] Force new assistant complete!
 ✅ New assistant ID: 4e899c0a-b435-45c4-abcd-8abd3ff13ec3
 🔄 Please refresh the page to ensure all changes take effect
 🔄 [ForceNewAssistant] Auto-refresh disabled to prevent loops
 🔑 [EmergencyApiKeyFix] Setting global environment variables...
 ✅ [EmergencyApiKeyFix] Global environment variables set
 🔧 [EmergencyApiKeyFix] Creating API key helper function...
 ✅ [EmergencyApiKeyFix] API key helper function created
 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
 ✅ [EmergencyApiKeyFix] Fetch override applied
 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
 [EmergencyApiKeyFix] Updating VapiMcpService API key...
 ✅ [EmergencyApiKeyFix] Existing services updated
 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
 🚀 [CriticalProductionFix] Initializing all critical fixes...
 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
 ✅ [CriticalProductionFix] Vapi API key configuration fixed
 🔧 [CriticalProductionFix] Fixing environment variables...
 ✅ [CriticalProductionFix] Environment variables fixed
 🌐 [CriticalProductionFix] Fixing CORS issues...
 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
 🛡️ [CriticalProductionFix] Fixing CSP issues...
 ✅ [CriticalProductionFix] CSP issues fixed
 📦 [CriticalProductionFix] Fixing import statement issues...
 ✅ [CriticalProductionFix] Import statement issues fixed
 🎉 [CriticalProductionFix] All critical fixes applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 Auth callback - environment mode: development
 Using real authentication in all environments
 Development mode detected, using fallback Supabase configuration
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 Auth callback - environment mode: development
 Using real authentication in all environments
 Development mode detected, using fallback Supabase configuration
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
index.ts:5 Loaded contentScript
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... Object
production-cors-fix.js:154 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
force-new-assistant.js:121 [ForceNewAssistant] Intercepting Vapi API call: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
force-new-assistant.js:126 [ForceNewAssistant] Replaced assistant ID in URL: https://api.vapi.ai/assistant/4e899c0a-b435-45c4-abcd-8abd3ff13ec3
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: Array(1)
callback:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
hook.js:608 Auth callback error: Error: No session found. Authentication may have failed.
    at handleCallback (AuthCallback.jsx:31:17)
overrideMethod @ hook.js:608
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
supabase.js:116 Supabase connection test successful!
hook.js:608 Auth callback error: Error: No session found. Authentication may have failed.
    at handleCallback (AuthCallback.jsx:31:17)
overrideMethod @ hook.js:608
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
production-cors-fix.js:167 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:685 ✅ [RobustStateHandler] Robust state handling initialized
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
debugConfig.js:30 [App] Available subdomains for testing: Array(3)
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! Array(1)
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! Array(1)
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
