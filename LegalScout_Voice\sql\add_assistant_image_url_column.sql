-- Add assistant_image_url column to assistant_ui_configs table
-- This stores the profile image for each assistant that appears in the dropdown and navbar

-- Add the column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'assistant_ui_configs' 
        AND column_name = 'assistant_image_url'
    ) THEN
        ALTER TABLE assistant_ui_configs 
        ADD COLUMN assistant_image_url TEXT;
        
        COMMENT ON COLUMN assistant_ui_configs.assistant_image_url IS 'URL or data URI for the assistant profile image displayed in dropdown and navbar';
        
        RAISE NOTICE 'Added assistant_image_url column to assistant_ui_configs table';
    ELSE
        RAISE NOTICE 'assistant_image_url column already exists in assistant_ui_configs table';
    END IF;
END $$;

-- Update any existing records to have a default assistant image if needed
-- (This is optional - we can generate default images dynamically)

-- Create an index for performance if the table gets large
CREATE INDEX IF NOT EXISTS idx_assistant_ui_configs_image_url 
ON assistant_ui_configs(assistant_image_url) 
WHERE assistant_image_url IS NOT NULL;

-- Grant permissions
GRANT SELECT, UPDATE ON assistant_ui_configs TO authenticated;
GRANT SELECT, UPDATE ON assistant_ui_configs TO service_role;
