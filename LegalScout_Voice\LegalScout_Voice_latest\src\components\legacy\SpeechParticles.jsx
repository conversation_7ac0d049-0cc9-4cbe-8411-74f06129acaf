/**
 * LEGACY COMPONENT - KEPT FOR REFERENCE ONLY
 *
 * This is the original SpeechParticles component that has been replaced by EnhancedSpeechParticles.
 * It is kept here for reference purposes only.
 *
 * For new implementations, please use EnhancedSpeechParticles instead.
 */

import React, { useEffect, useRef, useState } from 'react';
import '../SpeechParticles.css';
import '../SpeechMistVisualization.css';

const SpeechParticles = ({ className }) => {
  const canvasRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneStreamRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [microphoneActive, setMicrophoneActive] = useState(false);

  // Initialize the visualization
  useEffect(() => {
    console.log('SpeechParticles: Initializing component');

    // Load the speech particles script
    const script = document.createElement('script');
    script.src = '/speech-particles.js';
    script.async = true;

    // Add event listeners to track script loading
    script.onload = () => {
      console.log('SpeechParticles: Script loaded successfully');
      if (window.updateAudioSource) {
        console.log('SpeechParticles: updateAudioSource function is available');

        // Force a user interaction to ensure audio context can start
        // This helps with browsers that require user interaction to start audio
        const resumeAudioContext = () => {
          if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
            audioContextRef.current.resume().then(() => {
              console.log('SpeechParticles: Audio context resumed by user interaction');
            });
          }
        };

        // Add listeners to common user interactions
        document.addEventListener('click', resumeAudioContext, { once: true });
        document.addEventListener('touchstart', resumeAudioContext, { once: true });
        document.addEventListener('keydown', resumeAudioContext, { once: true });

        console.log('SpeechParticles: Added user interaction listeners for audio context');
      } else {
        console.error('SpeechParticles: updateAudioSource function is not available');
      }
    };

    script.onerror = (error) => {
      console.error('SpeechParticles: Error loading script:', error);
    };

    document.body.appendChild(script);

    // Clean up
    return () => {
      console.log('SpeechParticles: Cleaning up component');
      try {
        document.body.removeChild(script);
      } catch (error) {
        console.warn('SpeechParticles: Error removing script:', error);
      }

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      if (microphoneStreamRef.current) {
        microphoneStreamRef.current.getTracks().forEach(track => track.stop());
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Set up microphone input processing
  useEffect(() => {
    const setupMicrophone = async () => {
      try {
        console.log('SpeechParticles: Setting up microphone...');

        // Create audio context with explicit options for better compatibility
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        audioContextRef.current = new AudioContext({
          latencyHint: 'interactive',
          sampleRate: 44100
        });

        // Resume audio context (needed for some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
          console.log('SpeechParticles: Audio context resumed');
        }

        // Create and configure analyser with more sensitive settings
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 1024; // Smaller FFT size for faster response
        analyserRef.current.smoothingTimeConstant = 0.5; // Less smoothing for more responsive visualization
        analyserRef.current.minDecibels = -85; // Lower threshold to detect quieter sounds
        analyserRef.current.maxDecibels = -30; // Upper threshold for louder sounds

        console.log('SpeechParticles: Requesting microphone access...');

        // Get microphone stream with explicit constraints
        const constraints = {
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        microphoneStreamRef.current = stream;

        console.log('SpeechParticles: Microphone access granted, connecting to analyser...');

        // Create a gain node to boost the microphone signal
        const gainNode = audioContextRef.current.createGain();
        gainNode.gain.value = 3.0; // Significantly boost the signal (increased from 1.5)

        // Connect microphone to gain node to analyser (proper audio graph)
        const source = audioContextRef.current.createMediaStreamSource(stream);
        source.connect(gainNode);
        gainNode.connect(analyserRef.current);

        console.log('SpeechParticles: Microphone connected with boosted gain:', gainNode.gain.value);

        console.log('SpeechParticles: Microphone setup complete, starting processing...');
        setMicrophoneActive(true);

        // Start processing microphone input
        processMicrophoneInput();
      } catch (error) {
        console.error('SpeechParticles: Error accessing microphone:', error);

        // Try to provide more helpful error messages
        if (error.name === 'NotAllowedError') {
          console.error('SpeechParticles: Microphone access denied by user or system');
        } else if (error.name === 'NotFoundError') {
          console.error('SpeechParticles: No microphone found on this device');
        }
      }
    };

    // Only set up microphone if the canvas is ready
    if (canvasRef.current) {
      setupMicrophone();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // Properly clean up microphone resources
      if (microphoneStreamRef.current) {
        console.log('SpeechParticles: Stopping microphone tracks');
        microphoneStreamRef.current.getTracks().forEach(track => track.stop());
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        console.log('SpeechParticles: Closing audio context');
        audioContextRef.current.close();
      }
    };
  }, [canvasRef.current]);

  // Process microphone input and update visualization
  const processMicrophoneInput = () => {
    // Check if we have all required references
    if (!analyserRef.current || !microphoneActive || !window.updateAudioSource) {
      // Log the specific missing references for debugging
      const missing = [];
      if (!analyserRef.current) missing.push('analyserRef');
      if (!microphoneActive) missing.push('microphoneActive');
      if (!window.updateAudioSource) missing.push('window.updateAudioSource');

      console.log('SpeechParticles: Missing required references:', missing.join(', '));

      // Don't retry indefinitely - stop after a reasonable number of attempts
      // This prevents the infinite loop that's causing the console spam
      return;
    }

    try {
      // Get both frequency and time domain data for better analysis
      const bufferLength = analyserRef.current.frequencyBinCount;
      const freqDataArray = new Uint8Array(bufferLength);
      const timeDataArray = new Uint8Array(bufferLength);

      analyserRef.current.getByteFrequencyData(freqDataArray);
      analyserRef.current.getByteTimeDomainData(timeDataArray);

      // Calculate RMS amplitude from time domain data (more accurate for voice)
      let sumSquares = 0;
      for (let i = 0; i < bufferLength; i++) {
        // Convert from 0-255 to -1 to 1 range
        const amplitude = (timeDataArray[i] / 128) - 1;
        sumSquares += amplitude * amplitude;
      }
      const rms = Math.sqrt(sumSquares / bufferLength);

      // Calculate average from frequency data (good for detecting speech frequencies)
      let freqSum = 0;
      for (let i = 0; i < bufferLength; i++) {
        freqSum += freqDataArray[i];
      }
      const freqAverage = freqSum / bufferLength;

      // Use the higher of the two methods for better sensitivity
      // Apply a higher weight to the RMS value as it's better for voice detection
      const rmsWeighted = rms * 1.5; // Boost RMS value
      const freqWeighted = freqAverage / 100; // Increased from 128 for more sensitivity
      const rawAmplitude = Math.max(rmsWeighted, freqWeighted);
      const normalizedAmplitude = Math.min(rawAmplitude, 1); // Normalize to 0-1 range

      // Calculate dominant frequency - focus on speech frequencies
      let maxValue = 0;
      let dominantIndex = 0;

      // Focus on the frequency range most relevant to human speech (roughly 80-600 Hz)
      const minFreq = 80;
      const maxFreq = 600;
      const nyquist = audioContextRef.current.sampleRate / 2;
      const minIndex = Math.floor(bufferLength * minFreq / nyquist);
      const maxIndex = Math.floor(bufferLength * maxFreq / nyquist);

      for (let i = minIndex; i <= maxIndex; i++) {
        if (freqDataArray[i] > maxValue) {
          maxValue = freqDataArray[i];
          dominantIndex = i;
        }
      }

      // Convert index to frequency in Hz
      const dominantFrequency = dominantIndex * audioContextRef.current.sampleRate / (bufferLength * 2);

      // Extremely low threshold for maximum sensitivity
      if (normalizedAmplitude > 0.005) { // Even lower threshold to catch very quiet speech
        // Apply very aggressive scaling to make it much more visible
        const scaledAmplitude = Math.min(normalizedAmplitude * 10, 1); // 10x scaling for maximum visibility

        // Force a minimum amplitude to ensure visibility even with quiet sounds
        const finalAmplitude = Math.max(0.3, scaledAmplitude);

        // Update visualization with user audio - using the boosted amplitude
        window.updateAudioSource(finalAmplitude, dominantFrequency, 'user');
      } else {
        // When user is silent, set amplitude to null to allow fallback to ambient mode
        window.updateAudioSource(null, null, null);
      }
    } catch (error) {
      console.error('SpeechParticles: Error processing microphone input:', error);
    }

    // Continue processing in the next frame only if we have all required references
    if (analyserRef.current && microphoneActive && window.updateAudioSource) {
      animationFrameRef.current = requestAnimationFrame(processMicrophoneInput);
    }
  };

  // Set up Vapi audio level event listener
  useEffect(() => {
    const handleVapiAudioLevel = (event) => {
      if (!event.data || !window.updateAudioSource) return;

      // Check if this is a Vapi iframe message with audio level data
      if (event.data.what === 'iframe-call-message' &&
          event.data.action === 'remote-participants-audio-level' &&
          event.data.participantsAudioLevel) {

        // Extract audio levels
        const audioLevels = Object.values(event.data.participantsAudioLevel);

        if (audioLevels.length > 0) {
          // Convert to numbers and filter out NaN values
          const numericLevels = audioLevels
            .map(level => typeof level === 'number' ? level : parseFloat(level))
            .filter(level => !isNaN(level));

          if (numericLevels.length > 0) {
            // Find the maximum level
            const level = Math.max(...numericLevels);

            // Scale the level to be more visible (raw levels are often very small)
            // Apply a more aggressive scaling to make it more dramatic
            const scaledLevel = Math.min(level * 8, 1);

            // Use a default frequency range for assistant speech
            const baseFrequency = 200; // Base frequency for assistant voice
            const frequencyVariation = 150; // More variation for more dynamic visualization
            const assistantFrequency = baseFrequency + (frequencyVariation * scaledLevel);

            // Only update if we have significant audio
            if (scaledLevel > 0.05) {
              // Update the visualization for assistant audio
              window.updateAudioSource(scaledLevel, assistantFrequency, 'assistant');
            } else {
              // When assistant is silent, allow fallback to ambient mode
              window.updateAudioSource(null, null, null);
            }
          }
        }
      }
    };

    // Add event listener for Vapi audio level events
    window.addEventListener('message', handleVapiAudioLevel);

    // Also listen for direct volume level events from the VapiCall component
    const handleVolumeChange = (event) => {
      if (event.detail && typeof event.detail.level === 'number' && window.updateAudioSource) {
        const level = event.detail.level;

        // Apply a more aggressive scaling to make it more dramatic
        const scaledLevel = Math.min(level * 5, 1);

        if (scaledLevel > 0.05) {
          // Use a default frequency for assistant speech with more variation
          const assistantFrequency = 200 + (150 * scaledLevel);

          // Update the visualization for assistant audio
          window.updateAudioSource(scaledLevel, assistantFrequency, 'assistant');
        } else {
          // When assistant is silent, allow fallback to ambient mode
          window.updateAudioSource(null, null, null);
        }
      }
    };

    // Add custom event listener for volume changes
    window.addEventListener('vapi-volume-change', handleVolumeChange);

    return () => {
      window.removeEventListener('message', handleVapiAudioLevel);
      window.removeEventListener('vapi-volume-change', handleVolumeChange);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      id="mistCanvas"
      className={`speech-particles-canvas speech-mist-canvas ${className || ''}`}
    />
  );
};

export default SpeechParticles;
