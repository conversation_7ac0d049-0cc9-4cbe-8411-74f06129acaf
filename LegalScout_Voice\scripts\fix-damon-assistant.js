#!/usr/bin/env node

/**
 * Quick Fix for <PERSON>'s Assistant Conflict
 * 
 * ULTRA-THINKING: This script specifically fixes the assistant ID mismatch
 * for <EMAIL> by updating the database to use the correct assistant.
 * 
 * Root Cause: Database has wrong assistant ID (5c924e25-f521-4c5f-922b-1ffe052a8482)
 * Solution: Update to correct assistant ID (f9b97d13-f9c4-40af-a660-62ba5925ff2a)
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_KEY
);

const VAPI_API_KEY = process.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

async function validateAssistantInVapi(assistantId) {
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      return { valid: false, error: `HTTP ${response.status}` };
    }

    const assistant = await response.json();
    return { 
      valid: true, 
      assistant,
      name: assistant.name,
      createdAt: assistant.createdAt 
    };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

async function fixDamonAssistant() {
  console.log('🔧 ULTRA-THINKING Assistant Fix for Damon');
  console.log('🎯 Root Cause: Database has wrong assistant ID');
  console.log('💡 Solution: Update to correct assistant ID\n');

  const email = '<EMAIL>';
  const wrongAssistantId = '5c924e25-f521-4c5f-922b-1ffe052a8482';
  const correctAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

  try {
    // Step 1: Get current attorney record
    console.log(`📋 Getting attorney record for ${email}...`);
    const { data: attorney, error: fetchError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .single();

    if (fetchError || !attorney) {
      console.error(`❌ Attorney not found: ${email}`);
      return false;
    }

    console.log(`👤 Attorney: ${attorney.firm_name} (${attorney.email})`);
    console.log(`🤖 Current assistant ID: ${attorney.vapi_assistant_id}`);

    // Step 2: Validate both assistants in Vapi
    console.log('\n🔍 Validating assistants in Vapi...');
    
    const wrongValidation = await validateAssistantInVapi(wrongAssistantId);
    const correctValidation = await validateAssistantInVapi(correctAssistantId);

    console.log(`❌ Wrong assistant (${wrongAssistantId}): ${wrongValidation.valid ? `✅ "${wrongValidation.name}"` : `❌ ${wrongValidation.error}`}`);
    console.log(`✅ Correct assistant (${correctAssistantId}): ${correctValidation.valid ? `✅ "${correctValidation.name}"` : `❌ ${correctValidation.error}`}`);

    // Step 3: Verify the correct assistant exists
    if (!correctValidation.valid) {
      console.error(`❌ Correct assistant ${correctAssistantId} not found in Vapi!`);
      console.error(`   Error: ${correctValidation.error}`);
      return false;
    }

    // Step 4: Check if update is needed
    if (attorney.vapi_assistant_id === correctAssistantId) {
      console.log(`✅ Assistant ID is already correct! No update needed.`);
      return true;
    }

    // Step 5: Update the database
    console.log(`\n🔧 Updating assistant ID from ${attorney.vapi_assistant_id} to ${correctAssistantId}...`);
    
    const { error: updateError } = await supabase
      .from('attorneys')
      .update({ 
        vapi_assistant_id: correctAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorney.id);

    if (updateError) {
      console.error(`❌ Failed to update database:`, updateError.message);
      return false;
    }

    // Step 6: Verify the update
    console.log(`✅ Database updated successfully!`);
    
    const { data: updatedAttorney, error: verifyError } = await supabase
      .from('attorneys')
      .select('vapi_assistant_id')
      .eq('id', attorney.id)
      .single();

    if (verifyError || !updatedAttorney) {
      console.error(`❌ Failed to verify update:`, verifyError?.message);
      return false;
    }

    console.log(`🎉 Verification successful!`);
    console.log(`   New assistant ID: ${updatedAttorney.vapi_assistant_id}`);
    console.log(`   Assistant name: "${correctValidation.name}"`);
    console.log(`   Created: ${new Date(correctValidation.createdAt).toLocaleDateString()}`);

    console.log(`\n✨ ULTRA-THINKING Fix Complete!`);
    console.log(`🔄 Please refresh your dashboard to see the changes.`);
    console.log(`🎯 The dropdown should now show "LegalScout Assistant" as connected.`);

    return true;

  } catch (error) {
    console.error(`💥 Fatal error:`, error.message);
    return false;
  }
}

// Run the fix
fixDamonAssistant()
  .then(success => {
    if (success) {
      console.log('\n🎊 Fix completed successfully!');
      process.exit(0);
    } else {
      console.log('\n💔 Fix failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
