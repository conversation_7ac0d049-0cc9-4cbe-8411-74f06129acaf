-- Create a function to call the ensure-vapi-assistant edge function
CREATE OR REPLACE FUNCTION ensure_attorney_assistant()
RETURNS TRIGGER AS $$
DECLARE
  result RECORD;
BEGIN
  -- Call the edge function to ensure the attorney has a valid assistant
  SELECT
    content INTO result
  FROM
    http((
      'POST',
      CONCAT(current_setting('app.settings.supabase_functions_endpoint', TRUE), '/functions/v1/ensure-vapi-assistant'),
      ARRAY[
        http_header('Authorization', CONCAT('Bearer ', current_setting('app.settings.service_role_key', TRUE))),
        http_header('Content-Type', 'application/json')
      ],
      'application/json',
      json_build_object('attorneyId', NEW.id)::text
    )::http_request);
  
  -- Log the result
  RAISE NOTICE 'Ensure attorney assistant result: %', result;
  
  -- Return the new record
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to ensure new attorneys get assistants
CREATE OR REPLACE TRIGGER ensure_attorney_assistant_trigger
AFTER INSERT ON attorneys
FOR EACH ROW
EXECUTE FUNCTION ensure_attorney_assistant();

-- Create a trigger to ensure updated attorneys get assistants synced
CREATE OR REPLACE TRIGGER sync_attorney_assistant_trigger
AFTER UPDATE OF firm_name, welcome_message, vapi_instructions, voice_provider, voice_id, ai_model, subdomain, summary_prompt, success_evaluation_prompt, structured_data_prompt, structured_data_schema ON attorneys
FOR EACH ROW
WHEN (OLD.vapi_assistant_id IS NULL OR
      OLD.firm_name IS DISTINCT FROM NEW.firm_name OR
      OLD.welcome_message IS DISTINCT FROM NEW.welcome_message OR
      OLD.vapi_instructions IS DISTINCT FROM NEW.vapi_instructions OR
      OLD.voice_provider IS DISTINCT FROM NEW.voice_provider OR
      OLD.voice_id IS DISTINCT FROM NEW.voice_id OR
      OLD.ai_model IS DISTINCT FROM NEW.ai_model OR
      OLD.subdomain IS DISTINCT FROM NEW.subdomain OR
      OLD.summary_prompt IS DISTINCT FROM NEW.summary_prompt OR
      OLD.success_evaluation_prompt IS DISTINCT FROM NEW.success_evaluation_prompt OR
      OLD.structured_data_prompt IS DISTINCT FROM NEW.structured_data_prompt OR
      OLD.structured_data_schema IS DISTINCT FROM NEW.structured_data_schema)
EXECUTE FUNCTION ensure_attorney_assistant();
