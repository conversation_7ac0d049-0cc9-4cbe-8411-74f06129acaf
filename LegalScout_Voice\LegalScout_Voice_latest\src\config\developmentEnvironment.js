/**
 * Development Environment Configuration
 * 
 * This ensures development environment works properly without interfering
 * with Vite's built-in environment variable handling.
 */

// Development environment detection
export const isDevelopment = () => {
  try {
    // Check import.meta.env first (most reliable in Vite)
    if (import.meta && import.meta.env && import.meta.env.DEV === true) {
      return true;
    }
  } catch (e) {}
  
  // Fallback to hostname check
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost');
  }
  
  return false;
};

// Development environment initializer
export const initializeDevelopmentEnvironment = () => {
  console.log('🛠️ [DevEnvironment] Initializing development environment...');
  
  // Ensure global objects exist for compatibility
  if (typeof window !== 'undefined') {
    // Process polyfill for development
    if (!window.process) {
      window.process = {
        env: { NODE_ENV: 'development' },
        browser: true,
        version: '',
        versions: { node: '' }
      };
    }
    
    // Set development mode flag
    window.__ENVIRONMENT_MODE__ = 'development';
    window.__IS_DEVELOPMENT__ = true;
    
    console.log('✅ [DevEnvironment] Development environment initialized');
  }
  
  return true;
};

// Get environment variables in development (use Vite's built-in handling)
export const getDevEnvironmentVariable = (key) => {
  try {
    // Use Vite's import.meta.env directly in development
    if (import.meta && import.meta.env) {
      // Try with VITE_ prefix first
      const viteKey = key.startsWith('VITE_') ? key : `VITE_${key}`;
      if (import.meta.env[viteKey]) {
        return import.meta.env[viteKey];
      }
      
      // Try without prefix
      if (import.meta.env[key]) {
        return import.meta.env[key];
      }
    }
  } catch (e) {
    console.warn('[DevEnvironment] Could not access import.meta.env:', e);
  }
  
  return null;
};

// Development-specific service configurations
export const getDevSupabaseConfig = () => ({
  url: getDevEnvironmentVariable('SUPABASE_URL') || getDevEnvironmentVariable('VITE_SUPABASE_URL'),
  anonKey: getDevEnvironmentVariable('SUPABASE_ANON_KEY') || getDevEnvironmentVariable('VITE_SUPABASE_ANON_KEY')
});

export const getDevVapiConfig = () => ({
  publicKey: getDevEnvironmentVariable('VAPI_PUBLIC_KEY') || getDevEnvironmentVariable('VITE_VAPI_PUBLIC_KEY'),
  secretKey: getDevEnvironmentVariable('VAPI_SECRET_KEY') || getDevEnvironmentVariable('VITE_VAPI_SECRET_KEY'),
  privateKey: getDevEnvironmentVariable('VAPI_PRIVATE_KEY') || getDevEnvironmentVariable('VITE_VAPI_PRIVATE_KEY')
});

export default {
  isDevelopment,
  initializeDevelopmentEnvironment,
  getDevEnvironmentVariable,
  getDevSupabaseConfig,
  getDevVapiConfig
};
