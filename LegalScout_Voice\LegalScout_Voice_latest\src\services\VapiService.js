/**
 * Simplified Vapi Service
 *
 * This service consolidates all Vapi functionality into a single, reliable interface.
 * It replaces the multiple scattered services with one consistent approach.
 */

// Dynamic import of Vapi to ensure consistent module loading
import { getVapiApiKey, validateVapiConfig } from '../config/vapiConfig.js';
import { enhancedVapiMcpService } from './EnhancedVapiMcpService.js';
import { loadVapiSDK, createVapiInstance } from '../utils/vapiLoader.js';

class VapiService {
  constructor() {
    this.vapiInstance = null;
    this.isInitialized = false;
    this.mcpConnected = false;
    this.debug = {
      log: (...args) => console.log('[VapiService]', ...args),
      error: (...args) => console.error('[VapiService]', ...args),
      warn: (...args) => console.warn('[VapiService]', ...args)
    };
  }

  /**
   * Initialize the Vapi service
   * @param {Object} options - Initialization options
   * @returns {Promise<boolean>} Success status
   */
  async initialize(options = {}) {
    try {
      if (this.isInitialized) {
        this.debug.log('Already initialized');
        return true;
      }

      this.debug.log('Initializing Vapi service...');

      // Validate configuration
      const validation = validateVapiConfig();
      if (!validation.isValid) {
        this.debug.error('Invalid Vapi configuration:', validation.warnings);
        return false;
      }

      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => this.debug.warn(warning));
      }

      // Initialize MCP service for server operations
      await this.initializeMcp();

      this.isInitialized = true;
      this.debug.log('Vapi service initialized successfully');
      return true;
    } catch (error) {
      this.debug.error('Failed to initialize Vapi service:', error);
      return false;
    }
  }

  /**
   * Initialize MCP service for server operations
   */
  async initializeMcp() {
    try {
      const apiKey = getVapiApiKey('server');
      if (!apiKey) {
        this.debug.warn('No API key available for MCP operations');
        return false;
      }

      const connected = await enhancedVapiMcpService.connect(apiKey);
      this.mcpConnected = connected;

      if (connected) {
        this.debug.log('MCP service connected successfully');
      } else {
        this.debug.warn('MCP service connection failed');
      }

      return connected;
    } catch (error) {
      this.debug.error('Error initializing MCP service:', error);
      return false;
    }
  }

  /**
   * Create a Vapi instance for voice calls with enhanced debugging
   * @param {Object} options - Instance options
   * @returns {Promise<Object>} Vapi instance
   */
  async createVapiInstance(options = {}) {
    try {
      const apiKey = getVapiApiKey('client');
      if (!apiKey) {
        throw new Error('No API key available for client operations');
      }

      this.debug.log('🔧 Creating Vapi instance for voice calls...');
      this.debug.log('🔑 Using API key:', apiKey.substring(0, 8) + '...');

      // Use our reliable Vapi loader
      this.debug.log('📦 Loading Vapi SDK using vapiLoader...');
      await loadVapiSDK();

      this.debug.log('🏗️ Creating Vapi instance with vapiLoader...');
      const vapiInstance = await createVapiInstance(apiKey);

      if (!vapiInstance) {
        throw new Error('Failed to create Vapi instance');
      }

      this.vapiInstance = vapiInstance;
      this.debug.log('✅ Vapi instance created successfully');

      // Test the instance
      this.debug.log('🧪 Testing Vapi instance methods:', {
        hasStart: typeof vapiInstance.start === 'function',
        hasStop: typeof vapiInstance.stop === 'function',
        hasOn: typeof vapiInstance.on === 'function'
      });

      return vapiInstance;
    } catch (error) {
      this.debug.error('❌ Error creating Vapi instance:', error);
      this.debug.error('📊 Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
      throw error;
    }
  }

  /**
   * Start a voice call with simplified logic
   * @param {Object|string} vapiInstanceOrAssistantId - Vapi instance or assistant ID
   * @param {Object} options - Call options (when first param is instance)
   * @returns {Promise<boolean>} Success status
   */
  async startCall(vapiInstanceOrAssistantId, options = {}) {
    try {
      let vapiInstance, assistantId;

      // Handle both old and new calling patterns
      if (typeof vapiInstanceOrAssistantId === 'string') {
        // Old pattern: startCall(assistantId, options)
        assistantId = vapiInstanceOrAssistantId;
        vapiInstance = this.vapiInstance || await this.createVapiInstance();
      } else {
        // New pattern: startCall(vapiInstance, { assistantId, assistantOverrides })
        vapiInstance = vapiInstanceOrAssistantId;
        assistantId = options.assistantId;
      }

      if (!assistantId) {
        throw new Error('Assistant ID is required');
      }

      if (!vapiInstance) {
        throw new Error('Vapi instance is required');
      }

      this.debug.log('🚀 Starting call with assistant:', assistantId);
      this.debug.log('📋 Call options:', JSON.stringify(options, null, 2));

      // SIMPLIFIED APPROACH: For existing assistants, always use just the assistant ID
      // This follows the official Vapi Web SDK pattern: vapi.start(assistantId)

      this.debug.log('✅ Using simplified call pattern - assistant ID only');
      this.debug.log('🎯 Assistant ID to use:', assistantId);

      // Validate assistant ID format (should be UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(assistantId)) {
        throw new Error(`Invalid assistant ID format: ${assistantId}`);
      }

      // Start the call with just the assistant ID (no overrides)
      this.debug.log('🔄 Calling vapi.start() with assistant ID...');
      const callResult = await vapiInstance.start(assistantId);

      this.debug.log('✅ Call started successfully:', callResult);
      return true;
    } catch (error) {
      this.debug.error('❌ Error starting call:', error);
      this.debug.error('📊 Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5).join('\n')
      });
      throw error;
    }
  }

  /**
   * Stop the current call
   */
  async stopCall() {
    try {
      if (this.vapiInstance) {
        await this.vapiInstance.stop();
        this.debug.log('Call stopped');
      }
    } catch (error) {
      this.debug.error('Error stopping call:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners on a Vapi instance
   * @param {Object} vapiInstance - Vapi instance
   * @param {Object} callbacks - Event callbacks
   */
  setupEventListeners(vapiInstance, callbacks = {}) {
    if (!vapiInstance) {
      this.debug.error('No Vapi instance provided for event listeners');
      return;
    }

    // Map callback keys to Vapi event names
    const eventMapping = {
      'call-start': callbacks.onCallStart || callbacks.onCallStarted,
      'call-end': callbacks.onCallEnd,
      'speech-start': callbacks.onSpeechStart,
      'speech-end': callbacks.onSpeechEnd,
      'message': callbacks.onMessage,
      'error': callbacks.onError,
      'volume-level': callbacks.onVolumeLevel
    };

    // Set up event listeners with proper mapping
    Object.entries(eventMapping).forEach(([event, callback]) => {
      if (callback && typeof callback === 'function') {
        this.debug.log(`Setting up event listener for: ${event}`);
        vapiInstance.on(event, callback);
      }
    });

    this.debug.log('Event listeners set up for Vapi instance');
  }

  /**
   * Remove event listeners from a Vapi instance
   * @param {Object} vapiInstance - Vapi instance
   */
  removeEventListeners(vapiInstance) {
    if (!vapiInstance || typeof vapiInstance.off !== 'function') {
      this.debug.warn('Cannot remove event listeners - invalid Vapi instance');
      return;
    }

    // Remove all event listeners
    const events = ['call-start', 'call-end', 'speech-start', 'speech-end', 'message', 'error', 'volume-level'];

    events.forEach(event => {
      try {
        // Only try to remove if the method exists
        if (typeof vapiInstance.off === 'function') {
          // Remove all listeners for this event without specifying callback
          vapiInstance.off(event);
          this.debug.log(`Removed event listener for: ${event}`);
        } else if (typeof vapiInstance.removeAllListeners === 'function') {
          vapiInstance.removeAllListeners(event);
          this.debug.log(`Removed all listeners for: ${event}`);
        }
      } catch (error) {
        // Silently ignore listener removal errors as they're not critical
        this.debug.log(`Could not remove event listener for ${event} (not critical):`, error.message);
      }
    });

    this.debug.log('Event listeners removed from Vapi instance');
  }

  /**
   * Get the current Vapi instance
   */
  getVapiInstance() {
    return this.vapiInstance;
  }

  /**
   * Check if MCP service is connected
   */
  isMcpConnected() {
    return this.mcpConnected;
  }

  /**
   * Get MCP service for server operations
   */
  getMcpService() {
    return enhancedVapiMcpService;
  }

  /**
   * Create an assistant using MCP service
   * @param {Object} assistantConfig - Assistant configuration
   * @returns {Promise<Object>} Created assistant
   */
  async createAssistant(assistantConfig) {
    try {
      if (!this.mcpConnected) {
        await this.initializeMcp();
      }

      if (!this.mcpConnected) {
        throw new Error('MCP service not available for assistant creation');
      }

      return await enhancedVapiMcpService.createAssistant(assistantConfig);
    } catch (error) {
      this.debug.error('Error creating assistant:', error);
      throw error;
    }
  }

  /**
   * Get assistant by ID using MCP service
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} Assistant data
   */
  async getAssistant(assistantId) {
    try {
      if (!this.mcpConnected) {
        await this.initializeMcp();
      }

      if (!this.mcpConnected) {
        throw new Error('MCP service not available for assistant retrieval');
      }

      return await enhancedVapiMcpService.getAssistant(assistantId);
    } catch (error) {
      this.debug.error('Error getting assistant:', error);
      throw error;
    }
  }

  /**
   * List all assistants using MCP service
   * @returns {Promise<Array>} List of assistants
   */
  async listAssistants() {
    try {
      if (!this.mcpConnected) {
        await this.initializeMcp();
      }

      if (!this.mcpConnected) {
        throw new Error('MCP service not available for listing assistants');
      }

      return await enhancedVapiMcpService.listAssistants();
    } catch (error) {
      this.debug.error('Error listing assistants:', error);
      throw error;
    }
  }

  /**
   * Reset the service (for testing or error recovery)
   */
  reset() {
    this.vapiInstance = null;
    this.isInitialized = false;
    this.mcpConnected = false;
    this.debug.log('Vapi service reset');
  }
}

// Create and export singleton instance
export const vapiService = new VapiService();
export default vapiService;
