<!DOCTYPE html>
<html>
<head>
    <title>Test MCP Endpoint</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 MCP Endpoint Test</h1>
    <p>This page tests the Vapi MCP server endpoints to diagnose connection issues.</p>
    
    <button onclick="testEndpoints()">🧪 Test All Endpoints</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function testEndpoint(name, url, method = 'GET', body = null) {
            addResult(`🔄 Testing ${name}: ${url}`, 'info');
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const responseText = await response.text();
                
                if (response.ok) {
                    addResult(`✅ ${name} - SUCCESS (${response.status})`, 'success');
                    if (responseText) {
                        addResult(`<pre>${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}</pre>`, 'info');
                    }
                } else {
                    addResult(`❌ ${name} - FAILED (${response.status} ${response.statusText})`, 'error');
                    if (responseText) {
                        addResult(`<pre>${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}</pre>`, 'error');
                    }
                }
            } catch (error) {
                addResult(`❌ ${name} - ERROR: ${error.message}`, 'error');
            }
        }
        
        async function testEndpoints() {
            clearResults();
            addResult('🚀 Starting MCP endpoint tests...', 'info');
            
            // Test basic API endpoint
            await testEndpoint('Basic API Check', '/api/env');
            
            // Test MCP server endpoints
            await testEndpoint('MCP Server (GET)', '/api/vapi-mcp-server');
            await testEndpoint('MCP Server (POST)', '/api/vapi-mcp-server', 'POST', {
                jsonrpc: '2.0',
                method: 'initialize',
                params: {
                    protocolVersion: '2024-11-05',
                    capabilities: {},
                    clientInfo: {
                        name: 'test-client',
                        version: '1.0.0'
                    }
                },
                id: 1
            });
            
            // Test SSE endpoint
            await testEndpoint('MCP SSE Endpoint', '/api/vapi-mcp-server/sse');
            
            // Test OPTIONS for CORS
            await testEndpoint('MCP Server (OPTIONS)', '/api/vapi-mcp-server', 'OPTIONS');
            
            addResult('🏁 All tests completed!', 'info');
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(testEndpoints, 1000);
        });
    </script>
</body>
</html>
