import React from 'react';
import './Pagination.css';

/**
 * Pagination component for navigating between pages of results
 * 
 * @param {Object} props
 * @param {number} props.currentPage - Current active page (1-indexed)
 * @param {number} props.totalPages - Total number of pages
 * @param {Function} props.onPageChange - Function called when page is changed
 * @param {number} [props.visiblePages=5] - Number of page buttons to show
 */
const Pagination = ({ currentPage, totalPages, onPageChange, visiblePages = 5 }) => {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  // Calculate range of visible pages
  const startPage = Math.max(
    1,
    Math.min(
      currentPage - Math.floor(visiblePages / 2),
      totalPages - visiblePages + 1
    )
  );
  
  const endPage = Math.min(startPage + visiblePages - 1, totalPages);

  // Generate page buttons
  const pageButtons = [];
  for (let i = startPage; i <= endPage; i++) {
    pageButtons.push(
      <button
        key={i}
        onClick={() => onPageChange(i)}
        className={`page-button ${currentPage === i ? 'active' : ''}`}
        aria-current={currentPage === i ? 'page' : undefined}
      >
        {i}
      </button>
    );
  }

  return (
    <div className="pagination">
      {/* Previous page button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="page-button nav-button"
        aria-label="Previous page"
      >
        ←
      </button>
      
      {/* Show first page button and ellipsis if not at start */}
      {startPage > 1 && (
        <>
          <button 
            onClick={() => onPageChange(1)} 
            className="page-button"
          >
            1
          </button>
          {startPage > 2 && <span className="ellipsis">...</span>}
        </>
      )}
      
      {/* Page number buttons */}
      {pageButtons}
      
      {/* Show last page button and ellipsis if not at end */}
      {endPage < totalPages && (
        <>
          {endPage < totalPages - 1 && <span className="ellipsis">...</span>}
          <button 
            onClick={() => onPageChange(totalPages)} 
            className="page-button"
          >
            {totalPages}
          </button>
        </>
      )}
      
      {/* Next page button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="page-button nav-button"
        aria-label="Next page"
      >
        →
      </button>
    </div>
  );
};

export default Pagination; 