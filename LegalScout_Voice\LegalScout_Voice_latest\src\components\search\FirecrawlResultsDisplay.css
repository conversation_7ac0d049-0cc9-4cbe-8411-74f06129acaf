/* Firecrawl Results Display Styles */

.firecrawl-results {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.results-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #e74c3c;
}

.results-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px 20px;
}

.results-footer {
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #95a5a6;
  text-align: right;
}

.results-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #7f8c8d;
  font-style: italic;
}

/* Simple Results Layout */
.results-simple {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.result-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.result-card.simple {
  padding: 15px;
}

.result-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  line-height: 1.4;
}

.result-title a {
  color: #2980b9;
  text-decoration: none;
}

.result-title a:hover {
  text-decoration: underline;
}

.result-source {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.result-summary {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

/* Detailed Results Layout */
.result-card.detailed {
  padding: 20px;
}

.result-content {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  max-height: 300px;
  overflow-y: auto;
}

.result-content img {
  max-width: 100%;
  height: auto;
}

.result-content a {
  color: #2980b9;
  text-decoration: none;
}

.result-content a:hover {
  text-decoration: underline;
}

/* Legal Results Layout */
.result-card.legal {
  padding: 20px;
  border-left: 4px solid #3498db;
}

.result-citation {
  font-family: monospace;
  background-color: rgba(52, 152, 219, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  margin: 8px 0;
  display: inline-block;
}

/* Dark Theme Support */
[data-theme="dark"] .firecrawl-results {
  background-color: rgba(30, 30, 30, 0.95);
  color: #f5f5f5;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .results-header {
  background-color: rgba(40, 40, 40, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .results-header h3 {
  color: #ecf0f1;
}

[data-theme="dark"] .close-button {
  color: #bdc3c7;
}

[data-theme="dark"] .close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e74c3c;
}

[data-theme="dark"] .results-footer {
  border-color: rgba(255, 255, 255, 0.1);
  color: #95a5a6;
}

[data-theme="dark"] .result-card {
  background-color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .result-title a {
  color: #3498db;
}

[data-theme="dark"] .result-summary,
[data-theme="dark"] .result-content {
  color: #bdc3c7;
}

[data-theme="dark"] .result-source {
  color: #95a5a6;
}

[data-theme="dark"] .result-content {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .firecrawl-results {
    max-width: 95%;
    max-height: 80vh;
  }
}
