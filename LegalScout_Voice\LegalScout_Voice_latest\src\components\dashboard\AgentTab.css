.agent-tab .dashboard-card {
  margin-bottom: 1.5rem;
}

.agent-tab .form-group {
  margin-bottom: 1.25rem;
}

.assistant-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.create-assistant-button {
  margin-top: 0.75rem;
  padding: 0.5rem 1rem;
  background-color: #D85722;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.create-assistant-button:hover {
  background-color: #c04d1e;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.create-assistant-button:active {
  transform: translateY(0);
  box-shadow: none;
}

/* Assistant Info Section Styles */
.assistant-info-section {
  margin-bottom: 1.5rem;
}

.assistant-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.assistant-main-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.assistant-icon {
  font-size: 1.2rem;
  color: #4B74AA;
}

.assistant-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.assistant-name {
  font-weight: 600;
  font-size: 1rem;
  color: #2C3E50;
}

.assistant-id {
  font-size: 0.75rem;
  color: #666;
  font-family: 'Courier New', monospace;
}

.assistant-actions {
  display: flex;
  gap: 0.5rem;
}

.diagnostics-toggle-button,
.refresh-assistant-button {
  padding: 0.5rem;
  background-color: transparent;
  border: 1px solid #4B74AA;
  border-radius: 4px;
  color: #4B74AA;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.diagnostics-toggle-button:hover,
.refresh-assistant-button:hover {
  background-color: #4B74AA;
  color: white;
}

.create-assistant-button.secondary {
  background-color: transparent;
  border: 1px solid #D85722;
  color: #D85722;
}

.create-assistant-button.secondary:hover {
  background-color: #D85722;
  color: white;
}

.diagnostics-section {
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(75, 116, 170, 0.1);
}

.voice-upload-container,
.knowledge-upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
}

.logo-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.2s;
}

.logo-upload:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.file-input-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.file-input-label span {
  font-weight: 500;
  margin-top: 10px;
}

.file-input-label small {
  color: var(--text-secondary);
}

.file-input {
  display: none;
}

.logo-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.logo-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.remove-logo-button {
  padding: 8px 16px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.remove-logo-button:hover {
  background-color: #d32f2f;
}

.voice-upload-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.knowledge-upload-container .upload-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  max-width: fit-content;
}

.knowledge-upload-container .upload-button:hover {
  background-color: #3A5D8F;
}

.system-prompt-textarea {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.03);
}

.diagnostics-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.diagnostics-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.diagnostics-button:hover {
  background-color: #5a6268;
}

/* Toggle switch */
.toggle-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  background-color: #ccc;
  border-radius: 24px;
  transition: .4s;
  margin-right: 10px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .toggle-slider {
  background-color: #4B74AA;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-label {
  font-weight: 500;
}

/* Sync button */
.sync-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.sync-button:hover {
  background-color: #3A5D8F;
}

.sync-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.sync-button.syncing {
  background-color: #ffc107;
  color: #212529;
}

.sync-button.success {
  background-color: #28a745;
}

.sync-button.error {
  background-color: #dc3545;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Agent Header with Assistant Dropdown */
.agent-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
  gap: 1.5rem;
}

.agent-title-section {
  width: 100%;
}

.agent-title-section h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2C3E50;
}

.agent-title-section .tab-description {
  margin: 0;
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Assistant Dropdown - Full Width */
.assistant-dropdown {
  width: 100%;
}

.assistant-dropdown label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  margin: 0;
}

.assistant-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  font-size: 0.95rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.assistant-select:hover {
  border-color: #4B74AA;
  box-shadow: 0 2px 6px rgba(75, 116, 170, 0.15);
}

.assistant-select:focus {
  outline: none;
  border-color: #4B74AA;
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1), 0 2px 6px rgba(75, 116, 170, 0.15);
}

/* Dark theme support */
[data-theme="dark"] .agent-title-section h2 {
  color: #f8fafc;
}

[data-theme="dark"] .agent-title-section .tab-description {
  color: #cbd5e1;
}

[data-theme="dark"] .assistant-select {
  background-color: #2a2a2a;
  border-color: #444;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .assistant-select:hover,
[data-theme="dark"] .assistant-select:focus {
  border-color: #4B74AA;
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.2), 0 2px 6px rgba(75, 116, 170, 0.25);
}

/* Screen reader only class for accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

[data-theme="dark"] .assistant-dropdown label {
  color: #ccc;
}

.loading-indicator {
  font-size: 0.75rem;
  color: #666;
  margin-left: 0.5rem;
  font-style: italic;
}

[data-theme="dark"] .loading-indicator {
  color: #999;
}

/* Responsive design */
@media (max-width: 768px) {
  .agent-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .assistant-dropdown {
    align-items: stretch;
    min-width: auto;
  }

  .assistant-select {
    min-width: auto;
    width: 100%;
  }

  /* Ensure all form elements are clickable on mobile */
  .agent-tab * {
    pointer-events: auto !important;
    touch-action: manipulation;
  }

  .agent-tab input,
  .agent-tab textarea,
  .agent-tab select,
  .agent-tab button {
    z-index: 10;
    position: relative;
  }
}

/* Practice Area Selector in Agent Tab */
.practice-area-selector-agent {
  margin-top: 10px;
}

.practice-area-selector-agent .selector-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.practice-area-selector-agent .selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.practice-area-selector-agent .label-icon {
  color: var(--primary-color, #4B74AA);
}

.practice-area-selector-agent .selector-controls {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.practice-area-selector-agent .practice-area-select {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  background-color: var(--input-bg, #fff);
  color: var(--text-primary);
  font-size: 14px;
}

.practice-area-selector-agent .apply-template-btn {
  padding: 10px 16px;
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.practice-area-selector-agent .apply-template-btn:hover:not(:disabled) {
  background-color: var(--primary-color-hover, #3a5a8a);
  transform: translateY(-1px);
}

.practice-area-selector-agent .apply-template-btn:disabled {
  background-color: var(--disabled-color, #ccc);
  cursor: not-allowed;
  transform: none;
}

.practice-area-selector-agent .practice-area-info {
  margin-top: 12px;
  padding: 12px;
  background-color: var(--info-bg, rgba(75, 116, 170, 0.1));
  border: 1px solid var(--info-border, rgba(75, 116, 170, 0.2));
  border-radius: 6px;
}

.practice-area-selector-agent .info-text {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Dark mode support */
[data-theme="dark"] .practice-area-selector-agent .practice-area-select {
  background-color: var(--input-bg-dark, #333);
  border-color: var(--border-color-dark, #555);
  color: var(--text-primary-dark, #f0f0f0);
}

[data-theme="dark"] .practice-area-selector-agent .practice-area-info {
  background-color: var(--info-bg-dark, rgba(75, 116, 170, 0.15));
  border-color: var(--info-border-dark, rgba(75, 116, 170, 0.3));
}

/* Responsive design for practice area selector */
@media (max-width: 768px) {
  .practice-area-selector-agent .selector-controls {
    flex-direction: column;
    gap: 8px;
  }

  .practice-area-selector-agent .apply-template-btn {
    width: 100%;
  }
}
