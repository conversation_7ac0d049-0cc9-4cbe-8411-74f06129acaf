import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { getAttorneyConfigAsync } from '../config/attorneys';
import { getVapiLogHistory, clearVapiLogHistory, exportVapiLogs } from '../utils/vapiLogger';
import VapiStatusSummary from '../components/VapiStatusSummary';

const VapiIntegrationTest = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [selectedSubdomain, setSelectedSubdomain] = useState('');
  const [availableSubdomains, setAvailableSubdomains] = useState([]);

  useEffect(() => {
    loadAvailableSubdomains();

    // Update logs every 2 seconds
    const interval = setInterval(() => {
      setLogs(getVapiLogHistory());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const loadAvailableSubdomains = async () => {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('subdomain, firm_name, vapi_assistant_id')
        .not('subdomain', 'is', null);

      if (!error && data) {
        setAvailableSubdomains(data);
        if (data.length > 0) {
          setSelectedSubdomain(data[0].subdomain);
        }
      }
    } catch (error) {
      console.error('Error loading subdomains:', error);
    }
  };

  const runComprehensiveTest = async () => {
    setLoading(true);
    clearVapiLogHistory();
    setTestResults({});

    const results = {};

    try {
      // Test 1: Load attorney profile
      console.log('🧪 Test 1: Loading attorney profile...');
      results.profileLoad = { status: 'running', message: 'Loading attorney profile...' };
      setTestResults({ ...results });

      const profile = await getAttorneyConfigAsync(selectedSubdomain);

      if (profile && profile.firmName) {
        results.profileLoad = {
          status: 'success',
          message: `Profile loaded: ${profile.firmName}`,
          data: {
            id: profile.id,
            firmName: profile.firmName,
            vapi_assistant_id: profile.vapi_assistant_id,
            vapiSyncStatus: profile.vapiSyncStatus,
            vapiAssistantName: profile.vapiAssistantName
          }
        };
      } else {
        results.profileLoad = {
          status: 'error',
          message: 'Failed to load attorney profile'
        };
      }

      // Test 2: Vapi Connection Test
      console.log('🧪 Test 2: Testing Vapi connection...');
      results.vapiConnection = { status: 'running', message: 'Testing Vapi connection...' };
      setTestResults({ ...results });

      try {
        const { enhancedVapiMcpService } = await import('../services/EnhancedVapiMcpService');
        const apiKey = import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VITE_VAPI_PUBLIC_KEY;

        if (!apiKey) {
          results.vapiConnection = {
            status: 'error',
            message: 'No Vapi API key found in environment'
          };
        } else {
          const connected = await enhancedVapiMcpService.connect(apiKey);

          if (connected) {
            results.vapiConnection = {
              status: 'success',
              message: `Connected to Vapi (${enhancedVapiMcpService.useDirect ? 'Direct API' : 'MCP'})`
            };
          } else {
            results.vapiConnection = {
              status: 'error',
              message: 'Failed to connect to Vapi'
            };
          }
        }
      } catch (error) {
        results.vapiConnection = {
          status: 'error',
          message: `Vapi connection error: ${error.message}`
        };
      }

      // Test 3: Assistant Verification
      if (profile && profile.vapi_assistant_id) {
        console.log('🧪 Test 3: Verifying assistant...');
        results.assistantVerification = { status: 'running', message: 'Verifying assistant...' };
        setTestResults({ ...results });

        try {
          const { enhancedVapiMcpService } = await import('../services/EnhancedVapiMcpService');
          const assistant = await enhancedVapiMcpService.getAssistant(profile.vapi_assistant_id);

          if (assistant) {
            results.assistantVerification = {
              status: 'success',
              message: `Assistant verified: ${assistant.name}`,
              data: {
                id: assistant.id,
                name: assistant.name,
                model: assistant.model?.model,
                voice: assistant.voice?.voiceId
              }
            };
          } else {
            results.assistantVerification = {
              status: 'error',
              message: 'Assistant not found in Vapi'
            };
          }
        } catch (error) {
          results.assistantVerification = {
            status: 'error',
            message: `Assistant verification error: ${error.message}`
          };
        }
      } else {
        results.assistantVerification = {
          status: 'warning',
          message: 'No assistant ID found in profile'
        };
      }

      // Test 4: Field Mapping Verification
      console.log('🧪 Test 4: Verifying field mapping...');
      results.fieldMapping = { status: 'running', message: 'Verifying field mapping...' };
      setTestResults({ ...results });

      const requiredFields = [
        'firmName', 'vapi_assistant_id', 'vapiInstructions', 'welcomeMessage',
        'voiceId', 'voiceProvider', 'aiModel', 'primaryColor', 'secondaryColor'
      ];

      const missingFields = requiredFields.filter(field => !profile[field]);
      const presentFields = requiredFields.filter(field => !!profile[field]);

      results.fieldMapping = {
        status: missingFields.length === 0 ? 'success' : 'warning',
        message: `${presentFields.length}/${requiredFields.length} required fields present`,
        data: {
          present: presentFields,
          missing: missingFields
        }
      };

    } catch (error) {
      results.error = {
        status: 'error',
        message: `Test suite error: ${error.message}`
      };
    }

    setTestResults(results);
    setLoading(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      case 'running': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'running': return '🔄';
      default: return '⏳';
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Vapi Integration Test Suite</h1>

      {/* Status Summary */}
      <VapiStatusSummary logs={logs} />

      <div style={{ marginBottom: '20px' }}>
        <label>
          Select Attorney Subdomain:
          <select
            value={selectedSubdomain}
            onChange={(e) => setSelectedSubdomain(e.target.value)}
            style={{ marginLeft: '10px', padding: '5px' }}
          >
            {availableSubdomains.map(attorney => (
              <option key={attorney.subdomain} value={attorney.subdomain}>
                {attorney.subdomain} - {attorney.firm_name}
                {attorney.vapi_assistant_id ? ' (has assistant)' : ' (no assistant)'}
              </option>
            ))}
          </select>
        </label>

        <button
          onClick={runComprehensiveTest}
          disabled={loading || !selectedSubdomain}
          style={{
            marginLeft: '20px',
            padding: '10px 20px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Running Tests...' : 'Run Comprehensive Test'}
        </button>
      </div>

      {/* Test Results */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        <div>
          <h2>Test Results</h2>
          {Object.entries(testResults).map(([testName, result]) => (
            <div
              key={testName}
              style={{
                padding: '15px',
                margin: '10px 0',
                border: `2px solid ${getStatusColor(result.status)}`,
                borderRadius: '8px',
                backgroundColor: '#f9fafb'
              }}
            >
              <h3 style={{ margin: '0 0 10px 0', color: getStatusColor(result.status) }}>
                {getStatusIcon(result.status)} {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </h3>
              <p style={{ margin: '0 0 10px 0' }}>{result.message}</p>
              {result.data && (
                <pre style={{
                  backgroundColor: '#f3f4f6',
                  padding: '10px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>

        {/* Live Logs */}
        <div>
          <h2>Live Vapi Logs</h2>
          <div style={{
            height: '500px',
            overflow: 'auto',
            backgroundColor: '#1f2937',
            color: '#f9fafb',
            padding: '15px',
            borderRadius: '8px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {logs.map((log, index) => (
              <div key={index} style={{ marginBottom: '5px' }}>
                <span style={{ color: '#9ca3af' }}>[{log.timestamp.split('T')[1].split('.')[0]}]</span>
                <span style={{ color: '#60a5fa' }}> [{log.component}]</span>
                <span style={{ color: getStatusColor(log.level.toLowerCase()) }}> {log.level}</span>
                <span> {log.message}</span>
                {log.data && (
                  <div style={{ marginLeft: '20px', color: '#d1d5db' }}>
                    {JSON.stringify(log.data, null, 2)}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div style={{ marginTop: '10px' }}>
            <button
              onClick={() => setLogs([])}
              style={{
                padding: '5px 10px',
                marginRight: '10px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '3px'
              }}
            >
              Clear Logs
            </button>
            <button
              onClick={() => {
                const logsJson = exportVapiLogs();
                const blob = new Blob([logsJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'vapi-logs.json';
                a.click();
              }}
              style={{
                padding: '5px 10px',
                backgroundColor: '#059669',
                color: 'white',
                border: 'none',
                borderRadius: '3px'
              }}
            >
              Export Logs
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VapiIntegrationTest;
