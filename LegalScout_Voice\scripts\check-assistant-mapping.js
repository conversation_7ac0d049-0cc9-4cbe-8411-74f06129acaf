/**
 * Assistant ID Mapping Diagnostic
 * 
 * This script checks the relationship between attorney records and Vapi assistant IDs
 * to diagnose why consultations aren't being created properly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAssistantMapping() {
  console.log('🔍 Checking Assistant ID Mapping...\n');

  try {
    // 1. Check all attorney records and their assistant IDs
    console.log('1️⃣ Attorney Records and Assistant IDs:');
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, email, name, vapi_assistant_id')
      .order('created_at', { ascending: false });

    if (attorneyError) {
      console.error('❌ Error fetching attorneys:', attorneyError);
      return;
    }

    console.log(`Found ${attorneys.length} attorneys:`);
    attorneys.forEach(attorney => {
      const assistantStatus = attorney.vapi_assistant_id ? '✅ HAS ASSISTANT' : '❌ NO ASSISTANT';
      console.log(`  - ${attorney.name || 'Unnamed'} (${attorney.email})`);
      console.log(`    Attorney ID: ${attorney.id}`);
      console.log(`    Assistant ID: ${attorney.vapi_assistant_id || 'None'} ${assistantStatus}`);
      console.log('');
    });

    // 2. Check your specific assistant ID from memory
    const yourAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // From your memory
    console.log('2️⃣ Checking Your Known Assistant ID:');
    console.log(`Your Assistant ID: ${yourAssistantId}`);
    
    const attorneyWithYourAssistant = attorneys.find(a => a.vapi_assistant_id === yourAssistantId);
    if (attorneyWithYourAssistant) {
      console.log(`✅ Found attorney with your assistant ID: ${attorneyWithYourAssistant.name} (${attorneyWithYourAssistant.email})`);
    } else {
      console.log('❌ No attorney record found with your assistant ID');
      console.log('💡 This could be why consultations aren\'t being created!');
    }
    console.log('');

    // 3. Check call_records for assistant ID patterns
    console.log('3️⃣ Checking Call Records for Assistant IDs:');
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('call_id, assistant_id, attorney_id, status, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (callError) {
      console.error('❌ Error fetching call records:', callError);
    } else {
      console.log(`Found ${callRecords.length} recent call records:`);
      callRecords.forEach(record => {
        const attorney = attorneys.find(a => a.id === record.attorney_id);
        const attorneyName = attorney ? attorney.name : 'Unknown Attorney';
        console.log(`  - Call ${record.call_id}`);
        console.log(`    Assistant ID: ${record.assistant_id}`);
        console.log(`    Attorney: ${attorneyName} (${record.attorney_id})`);
        console.log(`    Status: ${record.status} (${record.created_at})`);
        console.log('');
      });
    }

    // 4. Check consultations table for any data
    console.log('4️⃣ Checking Consultations Table:');
    const { data: consultations, error: consultationError } = await supabase
      .from('consultations')
      .select('id, attorney_id, client_name, metadata, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (consultationError) {
      console.error('❌ Error fetching consultations:', consultationError);
    } else {
      console.log(`Found ${consultations.length} consultations:`);
      consultations.forEach(consultation => {
        const attorney = attorneys.find(a => a.id === consultation.attorney_id);
        const attorneyName = attorney ? attorney.name : 'Unknown Attorney';
        const callId = consultation.metadata?.call_id || 'No call ID';
        const assistantId = consultation.metadata?.assistant_id || 'No assistant ID';
        
        console.log(`  - ${consultation.client_name} for ${attorneyName}`);
        console.log(`    Call ID: ${callId}`);
        console.log(`    Assistant ID: ${assistantId}`);
        console.log(`    Created: ${consultation.created_at}`);
        console.log('');
      });
    }

    // 5. Analysis and recommendations
    console.log('📋 ASSISTANT ID ANALYSIS:');
    console.log('='.repeat(50));
    
    const damonEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const damonAttorneys = attorneys.filter(a => damonEmails.includes(a.email));
    
    console.log(`Damon's Attorney Records: ${damonAttorneys.length}`);
    damonAttorneys.forEach(attorney => {
      console.log(`  - ${attorney.email}: ${attorney.vapi_assistant_id || 'NO ASSISTANT ID'}`);
    });
    
    console.log(`\nYour Known Assistant ID: ${yourAssistantId}`);
    
    if (!attorneyWithYourAssistant) {
      console.log('\n❌ CRITICAL ISSUE: Your assistant ID is not linked to any attorney record!');
      console.log('\n💡 SOLUTIONS:');
      console.log('1. Update one of your attorney records with the correct assistant ID');
      console.log('2. Check if you\'re using a different assistant ID than expected');
      console.log('3. Verify the webhook is receiving calls with the correct assistant ID');
      
      // Suggest which attorney to update
      const primaryAttorney = damonAttorneys.find(a => a.email === '<EMAIL>') || damonAttorneys[0];
      if (primaryAttorney) {
        console.log(`\n🔧 RECOMMENDED ACTION:`);
        console.log(`Update attorney ${primaryAttorney.email} (ID: ${primaryAttorney.id}) with assistant ID: ${yourAssistantId}`);
      }
    } else {
      console.log('\n✅ Assistant ID mapping looks correct');
      console.log('💡 Issue might be elsewhere - check webhook configuration or call processing');
    }

  } catch (error) {
    console.error('❌ Assistant mapping check failed:', error);
  }
}

// Run the check
checkAssistantMapping()
  .then(() => {
    console.log('\n✅ Assistant mapping check complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Assistant mapping check failed:', error);
    process.exit(1);
  });
