 🚀 [PRODUCTION CSP] Environment: PRODUCTION
 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
 🚀 [PRODUCTION CSP] Final eval status: WORKING
production-environment-validator.js:39 Uncaught 
 🔧 [ProductionEvalFix] Starting production eval fix...
 🔧 [ProductionEvalFix] Environment detected: Object
 🧪 [CSP TEST] Running CSP verification tests...
 🧪 [CSP TEST] ✅ Eval test PASSED: 4
 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
 🧪 [CSP TEST] All tests completed
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using default Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 🔧 [FinalLabelFix] Starting final label accessibility fix...
 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
 ✅ [FinalLabelFix] Final label fix initialized and monitoring
 🧪 [TestAssistantIDPropagation] Starting test...
 ✅ [TestAssistantIDPropagation] Test script loaded
 🧹 [VapiAssistantCleanup] Starting cleanup utility...
 ✅ [VapiAssistantCleanup] Cleanup utility loaded
 💡 Usage:
   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: Object
 🔧 [ProductionEvalFix] Initialization complete
 🧪 [CSP TEST] ✅ setTimeout string test PASSED
 [DashboardIframeManager] Iframe observer set up successfully
 ✅ [CleanAuthSolution] Auth state monitoring set up
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
production-eval-fix.js:47 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
production-eval-fix.js:202 🔧 [ProductionEvalFix] Status: Object
production-eval-fix.js:203 🔧 [ProductionEvalFix] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
production-cors-fix.js:238 [ProductionCorsFix] 🚀 Initializing all production fixes...
production-cors-fix.js:116 [ProductionCorsFix] 🌍 Ensuring production environment variables...
production-cors-fix.js:141 [ProductionCorsFix] ✅ Environment variables configured
production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
production-cors-fix.js:111 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:146 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:166 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:171 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:188 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:193 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:206 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:250 [ProductionCorsFix] 🎉 All production fixes initialized successfully
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: Array(1)
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
production-cors-fix.js:211 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
production-cors-fix.js:224 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
test-assistant-id-propagation.js:157 🔄 Auto-running tests...
test-assistant-id-propagation.js:122 🚀 Running all tests...
test-assistant-id-propagation.js:9 🔍 Testing assistant ID propagation...
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
test-assistant-id-propagation.js:71 🛡️ Testing CSP fix...
test-assistant-id-propagation.js:81 ✅ CSP includes vercel.live domain
test-assistant-id-propagation.js:91 🌐 Testing Vapi API endpoints...
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
test-assistant-id-propagation.js:105 ♿ Testing form accessibility...
test-assistant-id-propagation.js:115 ✅ No problematic labels found
test-assistant-id-propagation.js:131 📊 Test Results: Object
test-assistant-id-propagation.js:136 🎯 Tests passed: 2/4
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
