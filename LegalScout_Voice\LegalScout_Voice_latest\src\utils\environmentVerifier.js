/**
 * Production-Safe Environment Variable Verifier
 *
 * This utility ensures environment variables work in both development and production,
 * with comprehensive fallback strategies and error handling.
 */

// Safe environment variable access with multiple fallback strategies
const getEnvVar = (key) => {
  // Strategy 1: Window globals (production runtime injection)
  if (typeof window !== 'undefined' && window[key]) {
    return window[key];
  }

  // Strategy 2: Vite import.meta.env (development and build time)
  // PRODUCTION-SAFE FIX: Skip import.meta access to avoid build errors
  // This will be handled by the build-time variable replacement in vite.config.js

  // Strategy 3: Process environment (Node.js/server-side)
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key];
  }

  return null;
};

// Environment detection with production-safe fallbacks
const detectEnvironment = () => {
  let mode = 'production';
  let isDev = false;
  let isProd = true;

  try {
    // Check window location first (most reliable in browser)
    if (typeof window !== 'undefined') {
      const hostname = window.location?.hostname || '';
      const port = window.location?.port || '';

      isDev = hostname === 'localhost' ||
              hostname === '127.0.0.1' ||
              port === '5174' ||
              port === '5173';

      if (isDev) {
        mode = 'development';
        isProd = false;
      }
    }

    // Check Vite environment if available
    // PRODUCTION-SAFE FIX: Skip import.meta access to avoid build errors
    // Environment detection will be handled by build-time variable replacement
  } catch (e) {
    // Fallback to production mode if any errors
    console.warn('[EnvironmentVerifier] Error detecting environment, defaulting to production:', e.message);
  }

  return { mode, isDev, isProd };
};

// Required environment variables with multiple fallback keys
const REQUIRED_ENV_VARS = {
  // Supabase
  SUPABASE_URL: ['VITE_SUPABASE_URL', 'REACT_APP_SUPABASE_URL', 'SUPABASE_URL'],
  SUPABASE_KEY: ['VITE_SUPABASE_KEY', 'VITE_SUPABASE_ANON_KEY', 'REACT_APP_SUPABASE_KEY', 'SUPABASE_ANON_KEY'],

  // Vapi
  VAPI_PUBLIC_KEY: ['VITE_VAPI_PUBLIC_KEY', 'VAPI_PUBLIC_KEY'],
  VAPI_SECRET_KEY: ['VITE_VAPI_SECRET_KEY', 'VITE_VAPI_PRIVATE_KEY', 'VAPI_SECRET_KEY', 'VAPI_PRIVATE_KEY', 'VAPI_TOKEN']
};

// Production-safe fallback values
const FALLBACK_VALUES = {
  SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
  VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564'
};

// Get environment variable with comprehensive fallback strategy
const getEnvVarWithFallback = (configKey) => {
  const possibleKeys = REQUIRED_ENV_VARS[configKey] || [configKey];

  for (const key of possibleKeys) {
    const value = getEnvVar(key);
    if (value && value.trim() !== '') {
      return value.trim();
    }
  }

  // Return fallback if available
  const fallback = FALLBACK_VALUES[configKey];
  if (fallback) {
    console.warn(`[EnvironmentVerifier] Using fallback value for ${configKey}`);
    return fallback;
  }

  return null;
};

/**
 * Verifies that all required environment variables are set
 * @returns {Object} Object containing verification results
 */
export const verifyEnvironment = () => {
  const env = detectEnvironment();
  const results = {
    environment: env,
    allVariablesPresent: true,
    missingVariables: [],
    variables: {}
  };

  console.log(`🔍 [EnvironmentVerifier] Verifying ${env.mode} environment...`);

  // Check each required variable
  Object.keys(REQUIRED_ENV_VARS).forEach(configKey => {
    const value = getEnvVarWithFallback(configKey);
    const isPresent = Boolean(value);
    const isMasked = configKey.includes('KEY') || configKey.includes('SECRET');

    results.variables[configKey] = {
      isPresent,
      value: isMasked ? (isPresent ? '****' : undefined) : value,
      length: value ? value.length : 0
    };

    if (!isPresent) {
      results.allVariablesPresent = false;
      results.missingVariables.push(configKey);
      console.error(`❌ [EnvironmentVerifier] ${configKey}: Missing - this will cause failures`);
    } else {
      console.log(`✅ [EnvironmentVerifier] ${configKey}: Found (${value.length} chars)`);
    }
  });

  return results;
};

// Enhanced global variable injection for production compatibility
const injectGlobalVariables = () => {
  if (typeof window === 'undefined') return;

  console.log('[EnvironmentVerifier] Injecting global variables for production compatibility...');

  Object.keys(REQUIRED_ENV_VARS).forEach(configKey => {
    const value = getEnvVarWithFallback(configKey);
    if (value) {
      // Set multiple formats for maximum compatibility
      const possibleKeys = REQUIRED_ENV_VARS[configKey];
      possibleKeys.forEach(key => {
        if (!window[key]) {
          window[key] = value;
        }
      });

      // Also set the config key itself
      window[configKey] = value;
    }
  });

  // Ensure process.env exists for compatibility
  if (!window.process) {
    window.process = {
      env: {},
      browser: true,
      version: '',
      versions: { node: '' }
    };
  }

  // Copy environment variables to process.env for compatibility
  Object.keys(REQUIRED_ENV_VARS).forEach(configKey => {
    const value = getEnvVarWithFallback(configKey);
    if (value) {
      const possibleKeys = REQUIRED_ENV_VARS[configKey];
      possibleKeys.forEach(key => {
        window.process.env[key] = value;
      });
    }
  });

  console.log('✅ [EnvironmentVerifier] Global variables injected');
};

/**
 * Logs environment verification results to the console
 */
export const logEnvironmentStatus = () => {
  const results = verifyEnvironment();

  console.group('🔍 Environment Variable Verification');
  console.log('Environment:', results.environment.mode);
  console.log('All required variables present:', results.allVariablesPresent ? '✅ Yes' : '❌ No');

  if (!results.allVariablesPresent) {
    console.error('❌ Missing variables:', results.missingVariables.join(', '));
    console.error('This will cause application failures!');
  }

  console.log('Variables status:');
  Object.entries(results.variables).forEach(([name, info]) => {
    const status = info.isPresent ? '✅ Present' : '❌ Missing';
    const details = info.value ? `(${info.value})` : '';
    console.log(`- ${name}: ${status} ${details}`);
  });

  console.groupEnd();

  return results;
};

/**
 * Initialize environment verification with enhanced error handling
 */
export const initEnvironmentVerification = () => {
  try {
    console.log('🚀 [EnvironmentVerifier] Starting comprehensive environment verification...');

    // Inject global variables first for production compatibility
    injectGlobalVariables();

    // Then verify all variables
    const results = logEnvironmentStatus();

    if (results.missingVariables.length > 0) {
      console.error('🚨 [EnvironmentVerifier] CRITICAL: Missing environment variables:', results.missingVariables);
      console.error('Attempting emergency fallback injection...');

      // Emergency fallback - inject all fallback values
      if (typeof window !== 'undefined') {
        Object.keys(FALLBACK_VALUES).forEach(key => {
          const value = FALLBACK_VALUES[key];
          const possibleKeys = REQUIRED_ENV_VARS[key] || [key];
          possibleKeys.forEach(envKey => {
            if (!window[envKey]) {
              window[envKey] = value;
              console.log(`🆘 [EnvironmentVerifier] Emergency fallback set: ${envKey}`);
            }
          });
        });
      }
    } else {
      console.log('✅ [EnvironmentVerifier] All environment variables verified successfully');
    }

    return results;
  } catch (error) {
    console.error('🚨 [EnvironmentVerifier] FATAL ERROR during verification:', error);

    // Last resort emergency fallback
    if (typeof window !== 'undefined') {
      console.log('🆘 [EnvironmentVerifier] Applying last resort emergency fallback values...');
      Object.keys(FALLBACK_VALUES).forEach(key => {
        const value = FALLBACK_VALUES[key];
        const possibleKeys = REQUIRED_ENV_VARS[key] || [key];
        possibleKeys.forEach(envKey => {
          window[envKey] = value;
        });
      });
    }

    return null;
  }
};

// Export utilities
export { getEnvVar, getEnvVarWithFallback, detectEnvironment };

export default {
  verifyEnvironment,
  logEnvironmentStatus,
  initEnvironmentVerification,
  getEnvVar,
  getEnvVarWithFallback,
  detectEnvironment
};
