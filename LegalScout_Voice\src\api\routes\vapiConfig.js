/**
 * Vapi Configuration API Routes
 * 
 * Express routes for the Vapi configuration API.
 */

import express from 'express';
import { fetchVapiConfig, createVapiAssistant, updateVapiAssistant, getVapiAssistant } from '../vapiConfigApi';

const router = express.Router();

/**
 * GET /api/vapi/config
 * 
 * Get Vapi configuration
 */
router.get('/config', async (req, res) => {
  try {
    const config = await fetchVapiConfig();
    res.json(config);
  } catch (error) {
    console.error('[VapiConfigRoutes] Error fetching Vapi configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * POST /api/vapi/assistants
 * 
 * Create a Vapi assistant
 */
router.post('/assistants', async (req, res) => {
  try {
    const assistant = await createVapiAssistant(req.body);
    res.json(assistant);
  } catch (error) {
    console.error('[VapiConfigRoutes] Error creating Vapi assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * PATCH /api/vapi/assistants/:id
 * 
 * Update a Vapi assistant
 */
router.patch('/assistants/:id', async (req, res) => {
  try {
    const assistant = await updateVapiAssistant(req.params.id, req.body);
    res.json(assistant);
  } catch (error) {
    console.error('[VapiConfigRoutes] Error updating Vapi assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/assistants/:id
 * 
 * Get a Vapi assistant
 */
router.get('/assistants/:id', async (req, res) => {
  try {
    const assistant = await getVapiAssistant(req.params.id);
    res.json(assistant);
  } catch (error) {
    console.error('[VapiConfigRoutes] Error getting Vapi assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
