import { createServer } from 'vite'

async function startVite() {
  try {
    console.log('Starting Vite server...')
    const server = await createServer({
      server: {
        port: 5174,
        host: true
      }
    })
    
    await server.listen()
    console.log('Vite server started successfully!')
    console.log(`Server running at: http://localhost:5174`)
    
    // Keep the server running
    process.on('SIGINT', () => {
      console.log('Shutting down Vite server...')
      server.close()
      process.exit(0)
    })
    
  } catch (error) {
    console.error('Error starting Vite server:', error)
    process.exit(1)
  }
}

startVite()
