.very-cool-assistants {
  padding: 12px;
  max-height: calc(100vh - 200px);
  min-height: 600px;
  overflow-y: auto;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.assistants-header {
  margin-bottom: 8px;
  text-align: center;
}

.assistants-header h2 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.assistants-description {
  color: #888;
  font-size: 12px;
  margin: 0;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  min-height: 300px;
}

.loading-state .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state-icon {
  font-size: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.empty-state h3 {
  color: #475569;
  margin: 0 0 8px 0;
  font-size: 18px;
}

.empty-state p {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 16px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #2563eb;
}

.assistants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 6px;
  max-width: 100%;
}

.assistant-card {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #444;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.assistant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.assistant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  border-color: #4B74AA;
}

.assistant-card:hover::before {
  opacity: 1;
}

.assistant-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  gap: 6px;
  position: relative;
}

.assistant-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.assistant-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.assistant-info {
  flex: 1;
  min-width: 0;
}

.assistant-name {
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.assistant-subdomain {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #4B74AA;
}

.assistant-subdomain svg {
  width: 10px;
  height: 10px;
}

.no-subdomain {
  color: #94a3b8;
  font-style: italic;
}

.assistant-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px;
  background: #333;
  border-radius: 4px;
  border: 1px solid #555;
}

.stat-icon {
  color: #4B74AA;
  font-size: 11px;
  flex-shrink: 0;
  width: 12px;
  height: 12px;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.1;
}

.stat-label {
  font-size: 9px;
  color: #aaa;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .very-cool-assistants {
    padding: 16px;
  }
  
  .assistants-header h2 {
    font-size: 24px;
  }
  
  .assistant-card {
    padding: 16px;
  }
  
  .assistant-avatar {
    width: 40px;
    height: 40px;
  }
  
  .assistant-name {
    font-size: 14px;
  }
  
  .assistant-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .stat-item {
    padding: 6px;
  }
  
  .stat-value {
    font-size: 12px;
  }
  
  .stat-label {
    font-size: 9px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .very-cool-assistants {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
  
  .assistants-header h2 {
    color: #f1f5f9;
  }
  
  .assistants-description {
    color: #94a3b8;
  }
  
  .assistant-card {
    background: #334155;
    border-color: #475569;
  }
  
  .assistant-card:hover {
    border-color: #3b82f6;
  }
  
  .assistant-name {
    color: #f1f5f9;
  }
  
  .assistant-subdomain {
    color: #94a3b8;
  }
  
  .stat-item {
    background: #475569;
    border-color: #64748b;
  }
  
  .stat-value {
    color: #f1f5f9;
  }
  
  .stat-label {
    color: #94a3b8;
  }
  
  .empty-state h3 {
    color: #e2e8f0;
  }
  
  .empty-state p {
    color: #94a3b8;
  }
}

/* Delete Assistant Button */
.delete-assistant-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  font-size: 10px;
}

.assistant-card:hover .delete-assistant-btn {
  opacity: 1;
}

.delete-assistant-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Delete Confirmation Modal */
.delete-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-confirmation-modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.warning-icon {
  color: #dc3545;
  font-size: 24px;
}

.modal-header h3 {
  margin: 0;
  color: #1a1a1a;
  font-size: 1.25rem;
}

.modal-content {
  margin-bottom: 24px;
}

.modal-content p {
  margin: 0 0 16px 0;
  color: #333;
  line-height: 1.5;
}

.warning-list {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
}

.warning-list p {
  margin: 0 0 8px 0;
  font-weight: 500;
  color: #856404;
}

.warning-list ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
  color: #856404;
}

.warning-list li {
  margin-bottom: 4px;
}

.final-warning {
  color: #dc3545 !important;
  font-weight: 600 !important;
  text-align: center;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 12px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-actions .btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-actions .btn-secondary {
  background: #6c757d;
  color: white;
}

.modal-actions .btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.modal-actions .btn-danger {
  background: #dc3545;
  color: white;
}

.modal-actions .btn-danger:hover:not(:disabled) {
  background: #c82333;
}

@media (max-width: 768px) {
  .delete-confirmation-modal {
    margin: 20px;
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .modal-actions .btn {
    width: 100%;
  }
}

/* Assistant Groups */
.assistant-group {
  margin-bottom: 24px;
}

.group-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #444;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.group-description {
  margin: 0;
  font-size: 11px;
  color: #aaa;
}

/* Status Indicators */
.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.status-indicator.deployed {
  background-color: #10b981; /* Green */
}

.status-indicator.undeployed {
  background-color: #ef4444; /* Red */
}

/* Status dots on assistant cards */
.assistant-avatar {
  position: relative;
}

.status-dot {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.status-dot.deployed {
  background-color: #10b981; /* Green */
}

.status-dot.undeployed {
  background-color: #ef4444; /* Red */
}

/* Assistant card variants */
.assistant-card.deployed {
  border-left: 3px solid #10b981;
}

.assistant-card.undeployed {
  border-left: 3px solid #ef4444;
}

/* Dark theme support for new elements */
@media (prefers-color-scheme: dark) {
  .group-header {
    border-bottom-color: #475569;
  }

  .group-title {
    color: #f1f5f9;
  }

  .group-description {
    color: #94a3b8;
  }
}
