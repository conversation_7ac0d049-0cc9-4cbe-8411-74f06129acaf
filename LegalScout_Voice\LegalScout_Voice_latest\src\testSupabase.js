import { supabase, isSupabaseConfigured } from './lib/supabase-fixed';

/**
 * Test function to verify Supabase connection
 * This can be imported and called from any component to test the connection
 */
export async function testSupabaseConnection() {
  console.log('=== SUPABASE CONNECTION TEST ===');
  
  // Check if Supabase is configured
  const configured = isSupabaseConfigured();
  console.log('Supabase configured:', configured);
  
  if (!configured) {
    console.error('Supabase is not configured properly');
    return { success: false, error: 'Not configured' };
  }
  
  try {
    // Try to query the public.attorneys table
    console.log('Testing query to attorneys table...');
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Supabase query error:', error);
      return { success: false, error };
    }
    
    console.log('Supabase query successful!');
    console.log('Data:', data);
    
    return { success: true, data };
  } catch (e) {
    console.error('Unexpected error testing Supabase:', e);
    return { success: false, error: e };
  }
}

// Export a function to log Supabase environment variables (without revealing sensitive info)
export function logSupabaseConfig() {
  // Import environment variables using import.meta.env
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 
                      import.meta.env.REACT_APP_SUPABASE_URL;
  
  const supabaseKeyExists = Boolean(
    import.meta.env.VITE_SUPABASE_KEY || 
    import.meta.env.VITE_SUPABASE_ANON_KEY || 
    import.meta.env.REACT_APP_SUPABASE_KEY || 
    import.meta.env.REACT_APP_SUPABASE_ANON_KEY
  );
  
  console.log('=== SUPABASE CONFIG TEST ===');
  console.log('Supabase URL configured:', Boolean(supabaseUrl));
  console.log('Supabase Key configured:', supabaseKeyExists);
  
  if (supabaseUrl) {
    console.log('Supabase URL:', supabaseUrl);
  }
  
  return {
    urlConfigured: Boolean(supabaseUrl),
    keyConfigured: supabaseKeyExists
  };
}
