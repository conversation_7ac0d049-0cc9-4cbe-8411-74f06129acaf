/* Base styles for the shimmer wave effect - improved visibility */
.text-shimmer-wave {
  position: relative;
  white-space: pre-wrap;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  color: transparent !important; /* Force transparent to ensure gradient shows through */
  background-size: 300% 100%;
  animation: shimmerWave 8s ease-in-out infinite;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  z-index: 999 !important; /* Ensure it's above all elements */
  font-weight: 600 !important;
  font-size: 1.05rem !important;
  line-height: 1.5 !important;
  letter-spacing: 0.01em;
  padding: 0;
  margin: 0;
  display: inline-block !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
  will-change: background-position;
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.5) !important;
}

/* Rainbow gradient by default - ultra bright colors */
.text-shimmer-wave.rainbow {
  background-image: linear-gradient(
    60deg,
    #ff6eb4 0%,
    #fff78a 15%, 
    #53ffed 30%,
    #66c6ff 45%,
    #ff6eb4 60%,
    #fff78a 75%,
    #53ffed 90%,
    #66c6ff 100%
  ) !important;
  font-weight: 600 !important;
  filter: brightness(2) contrast(1.5) !important;
  text-shadow: 0 0 7px rgba(255, 255, 255, 0.5) !important;
}

/* Custom gradient option - enhanced brightness */
.text-shimmer-wave.custom-gradient {
  background-image: linear-gradient(
    60deg,
    #66c6ff 0%, 
    #53ffed 20%,
    #fff78a 40%,
    #66c6ff 60%,
    #53ffed 80%,
    #fff78a 100%
  ) !important;
  font-weight: 600 !important;
  filter: brightness(2) contrast(1.5) !important;
  text-shadow: 0 0 7px rgba(102, 198, 255, 0.6) !important;
}

/* Faster shimmer wave animation */
@keyframes shimmerWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Ensure better browser compatibility */
@-webkit-keyframes shimmerWave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Special styles when inside message containers */
.message-content .text-shimmer-wave {
  filter: brightness(2.5) contrast(1.8) !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.7) !important;
  font-weight: 600 !important;
  z-index: 999 !important;
  letter-spacing: 0.02em !important;
}

/* User message specific shimmer */
.message.user .message-content .text-shimmer-wave {
  text-shadow: 0 0 12px rgba(102, 198, 255, 0.9) !important;
  filter: brightness(2.7) contrast(2.0) !important;
}

/* Assistant message specific shimmer */
.message.assistant .message-content .text-shimmer-wave {
  text-shadow: 0 0 12px rgba(255, 110, 180, 0.9) !important;
  filter: brightness(2.7) contrast(2.0) !important;
}

/* Make sure text remains visible on any background */
.text-shimmer-wave::selection {
  background: rgba(0, 0, 0, 0.2);
  color: currentColor;
}

/* Ensure shimmer is visible on mobile */
@media (max-width: 768px) {
  .text-shimmer-wave {
    background-size: 200% 100%;
    font-size: 1rem !important;
  }
} 