/**
 * Test script to verify the subdomain fix
 * 
 * This script tests the assistant-subdomain mapping system to ensure
 * that subdomains are properly resolved to assistants and attorneys.
 */

import { AssistantSubdomainService } from './src/services/assistantSubdomainService.js';
import { getAttorneyConfigAsync } from './src/config/attorneys.js';

async function testSubdomainFix() {
  console.log('🧪 Testing Subdomain Fix...\n');

  const testSubdomain = 'assistant1test';
  
  try {
    // Test 1: Assistant Subdomain Service
    console.log('📋 Test 1: Assistant Subdomain Service');
    console.log(`🔍 Looking up subdomain: ${testSubdomain}`);
    
    const service = new AssistantSubdomainService();
    const mapping = await service.getAssistantBySubdomain(testSubdomain);
    
    if (mapping) {
      console.log('✅ Assistant mapping found:');
      console.log(`   Assistant ID: ${mapping.assistant_id}`);
      console.log(`   Attorney ID: ${mapping.attorney_id}`);
      console.log(`   Firm Name: ${mapping.firm_name}`);
      console.log(`   Is Primary: ${mapping.is_primary}`);
    } else {
      console.log('❌ No assistant mapping found');
      return false;
    }

    // Test 2: Attorney Config Loading
    console.log('\n📋 Test 2: Attorney Config Loading');
    console.log(`🔍 Loading attorney config for subdomain: ${testSubdomain}`);
    
    const attorneyConfig = await getAttorneyConfigAsync(testSubdomain);
    
    if (attorneyConfig) {
      console.log('✅ Attorney config loaded:');
      console.log(`   Firm Name: ${attorneyConfig.firmName}`);
      console.log(`   Vapi Assistant ID: ${attorneyConfig.vapi_assistant_id}`);
      console.log(`   Current Assistant ID: ${attorneyConfig.current_assistant_id}`);
      console.log(`   Assistant Subdomain: ${attorneyConfig.assistant_subdomain}`);
      console.log(`   Loaded Via: ${attorneyConfig.loadedVia}`);
      console.log(`   Vapi Sync Status: ${attorneyConfig.vapiSyncStatus}`);
    } else {
      console.log('❌ No attorney config loaded');
      return false;
    }

    // Test 3: Verify Data Consistency
    console.log('\n📋 Test 3: Data Consistency Check');
    
    const assistantIdMatch = mapping.assistant_id === attorneyConfig.vapi_assistant_id;
    const attorneyIdMatch = mapping.attorney_id === attorneyConfig.id;
    const subdomainMatch = mapping.subdomain === attorneyConfig.assistant_subdomain;
    
    console.log(`   Assistant ID Match: ${assistantIdMatch ? '✅' : '❌'}`);
    console.log(`   Attorney ID Match: ${attorneyIdMatch ? '✅' : '❌'}`);
    console.log(`   Subdomain Match: ${subdomainMatch ? '✅' : '❌'}`);
    
    const allMatch = assistantIdMatch && attorneyIdMatch && subdomainMatch;
    
    if (allMatch) {
      console.log('\n🎉 All tests passed! Subdomain fix is working correctly.');
      return true;
    } else {
      console.log('\n❌ Data consistency issues found.');
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Run the test
testSubdomainFix()
  .then(success => {
    if (success) {
      console.log('\n✅ Subdomain fix verification completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Subdomain fix verification failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Test script crashed:', error);
    process.exit(1);
  });
