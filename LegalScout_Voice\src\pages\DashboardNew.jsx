import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase-fixed';
import { useAuth } from '../contexts/AuthContext.jsx';
import { useTheme } from '../contexts/ThemeContext.jsx';
import { useStandaloneAttorney } from '../hooks/useStandaloneAttorney';
import { findAttorney, findAttorneyByUserId, createAttorney } from '../utils/attorneyUtils';
import { ensureVapiAssistant, findAttorneyByEmailAndEnsureVapiAssistant } from '../utils/vapiAssistantUtils';
import { autoAssistantReconciler } from '../services/AutoAssistantReconciler';
import AttorneySelector from '../components/AttorneySelector';
import { FaHeadset, FaUser, FaChartBar, FaPlug, FaSignOutAlt, FaArrowLeft, FaArrowRight, FaExpand, FaCompress, FaChartLine, FaLink, FaCode, FaLinkedin, FaFacebook, FaTwitter, FaEnvelope, FaShare, FaSms, FaComment, FaCog, FaPhone, FaProjectDiagram, FaUsers, FaTools } from 'react-icons/fa';
import { MdDarkMode, MdLightMode } from 'react-icons/md';
import EnhancedPreviewNew from '../components/preview/EnhancedPreviewNew';
import './Dashboard.css';
import './DashboardNew.css';
import '../components/dashboard/Dashboard.css';

// Import components
import ProfileTab from '../components/dashboard/ProfileTab';
import AgentTab from '../components/dashboard/AgentTab';
import ToolsTab from '../components/dashboard/ToolsTab';
import ConsultationsTab from '../components/dashboard/ConsultationsTab';
import VeryCoolAssistants from '../components/dashboard/VeryCoolAssistants';
import CustomFieldsTab from '../components/dashboard/CustomFieldsTab';
import AnalysisConfigTab from '../components/dashboard/AnalysisConfigTab';

import CallsTab from '../components/dashboard/CallsTab';
import SessionsTab from '../components/dashboard/WorkflowTab';
import AssistantAwareShare from '../components/dashboard/AssistantAwareShare';
import ProgressiveSetupGuide from '../components/dashboard/ProgressiveSetupGuide';
import EnhancedAssistantDropdown from '../components/dashboard/EnhancedAssistantDropdown';
// import AttorneyStateDebugger from '../components/AttorneyStateDebugger'; // Removed for production



const DashboardNew = () => {
  const { user, signOut, isLoading: authIsLoading } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const isDark = theme === 'dark';

  // DEBUG: Log auth hook results
  console.log('[DashboardNew] 🔍 useAuth() results:', {
    user: !!user,
    userEmail: user?.email,
    authIsLoading,
    timestamp: new Date().toISOString()
  });

  // Use standalone attorney manager
  const {
    attorney,
    error: attorneyError,
    updateAttorney,
    refreshAttorney,
    loadAttorneyForUser
  } = useStandaloneAttorney();

  // Debug logging removed for production

  // State
  const [activeTab, setActiveTab] = useState('profile');
  const [activeSubTab, setActiveSubTab] = useState(null); // For secondary tabs
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [copySuccess, setCopySuccess] = useState('');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarPinned, setSidebarPinned] = useState(true);
  const [multipleAttorneys, setMultipleAttorneys] = useState([]);
  const [showAttorneySelector, setShowAttorneySelector] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobilePreviewMode, setMobilePreviewMode] = useState('fab'); // 'fab', 'modal', 'panel', 'cards'
  const [mobilePreviewOpen, setMobilePreviewOpen] = useState(false);
  const [previewConfig, setPreviewConfig] = useState({
    firmName: 'Your Law Firm',
    attorneyName: 'Your Name',
    practiceAreas: [],
    state: '',
    practiceDescription: "Your AI legal assistant is ready to help",
    primaryColor: '#4B74AA',
    secondaryColor: '#2C3E50',
    backgroundColor: '#1a1a1a',
    backgroundOpacity: 0.9,
    buttonText: 'Start Consultation',
    buttonOpacity: 1,
    practiceAreaBackgroundOpacity: 0.1,
    textBackgroundColor: '#634C38',
    welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
    informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",
    logoUrl: '/PRIMARY CLEAR.png',
    mascot: '/PRIMARY CLEAR.png',
    vapiInstructions: '',
    vapiContext: '',
    theme: 'dark',
    useEnhancedPreview: true
  });
  const [isFullPreview, setIsFullPreview] = useState(false);
  const [previewUpdating, setPreviewUpdating] = useState(false);
  const [previewLoaded, setPreviewLoaded] = useState(false);
  const [previewError, setPreviewError] = useState(false);
  const previewRef = useRef(null);

  // Listen for messages from the preview iframe
  useEffect(() => {
    const handleMessage = (event) => {
      // Check if the message is from the preview
      if (event.data && (
        event.data.type === 'PREVIEW_READY' ||
        event.data.type === 'PREVIEW_PING' ||
        event.data.type === 'PREVIEW_UPDATE_RECEIVED'
      )) {
        // console.log('Received message from preview:', event.data.type);

        // If the preview is ready, send the current config
        if (event.data.type === 'PREVIEW_READY') {
          // console.log('Preview is ready, sending current config');
          sendConfigToPreviewIframe(previewConfig);
        }

        // If the preview acknowledged an update, log it and turn off updating indicator
        if (event.data.type === 'PREVIEW_UPDATE_RECEIVED') {
          // console.log('Preview acknowledged update of:', event.data.updatedProps);
          setPreviewUpdating(false);
        }
      }
    };

    // Add event listener
    window.addEventListener('message', handleMessage);

    // Clean up
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [previewConfig]);

  // FIXED: Re-enabled auto-reconciler with better validation to prevent sync mismatches
  useEffect(() => {
    // Only initialize when auth is fully loaded and we have a user
    if (authIsLoading || !user?.id) {
      console.log('[DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler');
      return;
    }

    console.log('[DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts');

    // Re-enabled: Auto-reconciler initialization with better timing
    autoAssistantReconciler.initializeForDashboard();

    // Listen for assistant reconciliation events (keep for compatibility)
    const handleAssistantReconciled = (event) => {
      console.log('[DashboardNew] 🎉 Assistant reconciled:', event.detail);

      // Refresh attorney data to get the updated assistant ID
      if (refreshAttorney) {
        refreshAttorney();
      }

      // Update preview config with new assistant ID
      if (event.detail.assistantId) {
        setPreviewConfig(prev => ({
          ...prev,
          vapiAssistantId: event.detail.assistantId,
          vapi_assistant_id: event.detail.assistantId
        }));
      }
    };

    window.addEventListener('assistantReconciled', handleAssistantReconciled);

    return () => {
      window.removeEventListener('assistantReconciled', handleAssistantReconciled);
    };
  }, [refreshAttorney, authIsLoading, user?.id]);

  // Note: We're now using the imported ensureVapiAssistant function from attorneyUtils.js

  // Create refs to track fetch attempts and attorney selector state
  const hasAttemptedFetch = useRef(false);
  const hasShownAttorneySelector = useRef(false);

  // 🎯 SIMPLIFIED LOADING LOGIC - Single source of truth
  useEffect(() => {
    const initializeDashboard = async () => {
      console.log('[DashboardNew] 🚀 Initializing dashboard with simplified logic...');
      console.log('[DashboardNew] 🔍 DEBUG AUTH STATE:', {
        authIsLoading,
        hasUser: !!user,
        userEmail: user?.email,
        userId: user?.id,
        timestamp: new Date().toISOString()
      });

      // Skip if auth is still loading
      if (authIsLoading) {
        console.log('[DashboardNew] ⏳ Auth still loading, waiting...');
        return;
      }

      // CRITICAL FIX: Only show error if auth is NOT loading AND no user
      if (!user?.email) {
        console.log('[DashboardNew] ❌ No authenticated user found');
        // Only set error if we're sure auth has finished loading
        if (!authIsLoading) {
          setError('Please sign in to access the dashboard');
          setLoading(false);
        }
        return;
      }

      console.log('[DashboardNew] ✅ User authenticated:', user.email);

      // Use the standalone attorney manager as single source of truth
      if (!window.standaloneAttorneyManager) {
        console.log('[DashboardNew] ⏳ Waiting for attorney manager to initialize...');
        return;
      }

      // If we already have an attorney, we're done
      if (attorney?.id) {
        console.log('[DashboardNew] ✅ Attorney already loaded:', attorney.id);
        setLoading(false);
        return;
      }

      console.log('[DashboardNew] 🔄 Loading attorney for user...');
      setLoading(true);
    };

    initializeDashboard();
  }, [authIsLoading, user?.email, attorney?.id]); // Simple dependencies

  // Fetch attorney data - with loading loop prevention
  useEffect(() => {
    // Stabilize authIsLoading to prevent undefined values
    const isAuthLoading = authIsLoading === undefined ? false : authIsLoading;

    console.log('[DashboardNew] Fetch Attorney Effect triggered.');
    console.log(`[DashboardNew] Dependencies: user?.id=${user?.id}, authIsLoading=${isAuthLoading}, loadAttorneyForUser exists=${!!loadAttorneyForUser}`);

    // Skip if we've already attempted to fetch in this render cycle
    if (hasAttemptedFetch.current) {
      console.log('[DashboardNew] Already attempted fetch in this render cycle, skipping');
      return;
    }

    // Skip if we're showing the attorney selector - this means we've already loaded attorneys
    if (hasShownAttorneySelector.current && showAttorneySelector) {
      console.log('[DashboardNew] Attorney selector is active, skipping fetch');
      return;
    }

    // Mark that we've attempted to fetch
    hasAttemptedFetch.current = true;

    // Set a timeout to reset the fetch attempt flag after a reasonable time
    // This prevents getting stuck in a state where we can never fetch again
    const resetTimeout = setTimeout(() => {
      hasAttemptedFetch.current = false;
    }, 5000); // Reset after 5 seconds

    const fetchAttorneyData = async () => {
      console.log('[DashboardNew] fetchAttorneyData called.');

      // Check for manager readiness as well
      if (!window.standaloneAttorneyManager) {
        console.warn('[DashboardNew] StandaloneAttorneyManager not ready yet, using fallback.');
        // Instead of waiting indefinitely, use the attorney from the hook if available
        if (attorney) {
          console.log('[DashboardNew] Using attorney from hook:', attorney.id);
          setLoading(false);
          return;
        }

        // 💀 NO MORE DEVELOPMENT ATTORNEYS! Force the robust state handler to run
        console.error('💀 [DashboardNew] NO ATTORNEY FOUND - FORCING ROBUST STATE HANDLER!');

        // Force the robust state handler to run immediately
        if (typeof window.resolveAttorneyState === 'function') {
          try {
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (!userError && user?.email) {
              console.log('💀 [DashboardNew] FORCING robust state handler for:', user.email);
              const stateResult = await window.resolveAttorneyState(user.email);
              if (stateResult.success) {
                console.log('💀 [DashboardNew] ✅ FORCED robust state handler succeeded!');
                setLoading(false);
                return;
              }
            }
          } catch (error) {
            console.error('💀 [DashboardNew] FORCED robust state handler failed:', error);
          }
        }

        // If all else fails, show error instead of creating mock
        setError('No attorney profile found. Please contact support.');
        setLoading(false);
        return;

        // In production, show error after timeout
        setTimeout(() => {
          setError('Attorney profile manager not available. Please refresh the page.');
          setLoading(false);
        }, 5000);

        return;
      }

      // Only proceed if auth is NOT loading AND user/id are available
      if (authIsLoading || !user || !user.id) {
        console.log(`[DashboardNew] Skipping fetch action: authIsLoading=${authIsLoading}, user=${!!user}, userId=${user?.id})`);

        // If auth is not loading but no user, set loading false
        if (!authIsLoading && !user) {
          setLoading(false);
        }

        // 💀 NO MORE MOCK ATTORNEYS! If no user, show proper error
        if (!user && !authIsLoading) {
          console.error('💀 [DashboardNew] NO USER FOUND - Authentication required!');
          setError('Authentication required. Please sign in.');
          setLoading(false);
        }

        return;
      }

      // If we reach here, user and user.id are valid
      setLoading(true);
      setError(null); // Clear previous errors

      try {
        console.log(`[DashboardNew] Attempting to load attorney for user: ${user.id}`);

        // Load attorney by authenticated user (email-based authentication)
        let loadedAttorney = null;

        console.log(`[DashboardNew] Loading attorney for authenticated user: ${user.email}`);

        // 🎯 ALWAYS try robust state handler first
        console.log('[DashboardNew] 🛡️ Checking for robust state handler...');

        if (typeof window.resolveAttorneyState === 'function') {
          console.log('[DashboardNew] ✅ Robust state handler found, calling it...');

          try {
            const stateResult = await window.resolveAttorneyState(user.email);
            console.log('[DashboardNew] 🔍 Robust state handler result:', stateResult);

            if (stateResult.success && stateResult.attorney) {
              console.log('[DashboardNew] ✅ Attorney state resolved via robust handler:', {
                id: stateResult.attorney.id,
                email: stateResult.attorney.email,
                assistantId: stateResult.attorney.vapi_assistant_id,
                needsCreation: stateResult.needsCreation
              });

              loadedAttorney = stateResult.attorney;

              // Update the standalone manager with the resolved attorney
              if (window.standaloneAttorneyManager) {
                window.standaloneAttorneyManager.attorney = loadedAttorney;
                window.standaloneAttorneyManager.saveToLocalStorage(loadedAttorney);
                window.standaloneAttorneyManager.notifySubscribers();
              }

              // Skip the rest of the loading logic since we have the attorney
              console.log('[DashboardNew] ✅ Robust state handler succeeded, skipping fallback logic');

              // Return early to skip all fallback logic
              return;
            } else {
              console.warn('[DashboardNew] ⚠️ Robust state handler failed:', stateResult.error);
            }
          } catch (robustError) {
            console.error('[DashboardNew] ❌ Robust state handler error:', robustError);
          }
        } else {
          console.warn('[DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)');
        }

        // Fallback to original logic if robust handler didn't work
        console.log('[DashboardNew] 🔄 Using fallback attorney loading logic...');

        // First try to load attorney using the user ID
        try {
          loadedAttorney = await loadAttorneyForUser(user.id);
        } catch (userIdError) {
          console.error('[DashboardNew] Error loading attorney by user ID:', userIdError);
          // Continue to email fallback
        }

        // If no attorney found by user ID, try by email
        if (!loadedAttorney && user.email) {
          console.log(`[DashboardNew] No attorney found by user ID, trying email: ${user.email}`);
          try {
            // Use our utility function to find attorney by email and ensure VAPI assistant
            loadedAttorney = await findAttorneyByEmailAndEnsureVapiAssistant(user.email);

            if (loadedAttorney) {
              console.log('[DashboardNew] Attorney found by email and VAPI assistant ensured:', loadedAttorney.id);

              // Update the attorney in the standalone manager
              if (window.standaloneAttorneyManager) {
                window.standaloneAttorneyManager.attorney = loadedAttorney;
                window.standaloneAttorneyManager.saveToLocalStorage(loadedAttorney);
                window.standaloneAttorneyManager.notifySubscribers();
              }
            }
          } catch (emailError) {
            console.error('[DashboardNew] Error finding attorney by email:', emailError);
          }
        }

        // If attorney was found, ensure it has a VAPI assistant ID
        if (loadedAttorney) {
          console.log('[DashboardNew] Attorney loaded successfully:', loadedAttorney.id);

          // Ensure the attorney has a VAPI assistant ID
          if (!loadedAttorney.vapi_assistant_id) {
            console.log('[DashboardNew] Attorney has no VAPI assistant ID, creating one...');
            try {
              loadedAttorney = await ensureVapiAssistant(loadedAttorney);

              // Update the attorney in the standalone manager
              if (window.standaloneAttorneyManager) {
                window.standaloneAttorneyManager.attorney = loadedAttorney;
                window.standaloneAttorneyManager.saveToLocalStorage(loadedAttorney);
                window.standaloneAttorneyManager.notifySubscribers();
              }

              console.log('[DashboardNew] VAPI assistant created:', loadedAttorney.vapi_assistant_id);
            } catch (vapiError) {
              console.error('[DashboardNew] Error creating VAPI assistant:', vapiError);
            }
          }
        } else {
          console.warn('[DashboardNew] No attorney profile found for this user or loading failed.');

          // 💀 NO MORE DEVELOPMENT ATTORNEYS! Force proper error handling
          console.error('💀 [DashboardNew] NO ATTORNEY PROFILE FOUND - FORCING ROBUST STATE HANDLER!');

          // Try one more time with the robust state handler
          if (typeof window.resolveAttorneyState === 'function' && user?.email) {
            try {
              console.log('💀 [DashboardNew] LAST CHANCE: Forcing robust state handler for:', user.email);
              const stateResult = await window.resolveAttorneyState(user.email);
              if (stateResult.success && stateResult.attorney) {
                console.log('💀 [DashboardNew] ✅ LAST CHANCE SUCCESS!');

                // Update the attorney in the standalone manager
                if (window.standaloneAttorneyManager) {
                  window.standaloneAttorneyManager.attorney = stateResult.attorney;
                  window.standaloneAttorneyManager.saveToLocalStorage(stateResult.attorney);
                  window.standaloneAttorneyManager.notifySubscribers();
                }
                return; // Exit early on success
              }
            } catch (error) {
              console.error('💀 [DashboardNew] LAST CHANCE FAILED:', error);
            }
          }

          // If everything fails, show proper error
          setError('No attorney profile found for your account. Please contact support or try creating a profile.');
        }
      } catch (error) {
        console.error('[DashboardNew] Error calling loadAttorneyForUser:', error);
        setError(`Failed to load attorney profile: ${error.message}`);
      } finally {
        // Always set loading to false to prevent infinite loading
        setLoading(false);
      }
    };

    // Set a timeout to ensure loading state doesn't get stuck
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.warn('[DashboardNew] Loading timeout reached, forcing loading state to false');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    // Don't call fetchAttorneyData until auth is resolved AND manager is ready
    if (!authIsLoading && user?.id && window.standaloneAttorneyManager) {
      console.log('[DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.');
      fetchAttorneyData();
    } else {
      console.log(`[DashboardNew] Waiting... (authIsLoading: ${authIsLoading}, userId: ${user?.id}, managerReady: ${!!window.standaloneAttorneyManager})`);

      // If we've been waiting too long, try to proceed anyway
      if (!authIsLoading && (attorney || window.standaloneAttorneyManager)) {
        console.log('[DashboardNew] Proceeding with available data');
        fetchAttorneyData();
      }
    }

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      clearTimeout(resetTimeout);
      // Reset the flags when the component unmounts
      hasAttemptedFetch.current = false;
      hasShownAttorneySelector.current = false;
    };
  }, [user?.id, loadAttorneyForUser]); // Removed authIsLoading from dependencies since we handle it inside

  // Effect to listen for multipleAttorneysAvailable event
  useEffect(() => {
    const handleMultipleAttorneys = (event) => {
      console.log('[DashboardNew] Multiple attorneys available:', event.detail);
      setMultipleAttorneys(event.detail.attorneys);

      // Only show selector if there are multiple attorneys
      const shouldShowSelector = event.detail.attorneys.length > 1;
      setShowAttorneySelector(shouldShowSelector);

      // Mark that we've shown the attorney selector to prevent re-fetching
      if (shouldShowSelector) {
        hasShownAttorneySelector.current = true;
        console.log('[DashboardNew] Attorney selector shown, preventing further fetches');
      }
    };

    window.addEventListener('multipleAttorneysAvailable', handleMultipleAttorneys);

    return () => {
      window.removeEventListener('multipleAttorneysAvailable', handleMultipleAttorneys);
    };
  }, []);

  // Effect to listen for vapiAssistantChanged event
  useEffect(() => {
    const handleVapiAssistantChanged = (event) => {
      console.log('[DashboardNew] VAPI assistant changed:', event.detail);

      // Update the preview config with the new VAPI assistant ID
      setPreviewConfig(prev => ({
        ...prev,
        vapiAssistantId: event.detail.vapiAssistantId
      }));
    };

    // Listen for the vapiAssistantChanged event
    window.addEventListener('vapiAssistantChanged', handleVapiAssistantChanged);

    // Listen for the updatePreviewConfig event
    const handleUpdatePreviewConfig = (event) => {
      console.log('[DashboardNew] Update preview config:', event.detail);
      setPreviewConfig(prev => ({
        ...prev,
        ...event.detail
      }));
    };

    window.addEventListener('updatePreviewConfig', handleUpdatePreviewConfig);

    // Expose the updatePreviewConfig function globally
    window.updatePreviewConfig = (config) => {
      setPreviewConfig(prev => ({
        ...prev,
        ...config
      }));
    };

    return () => {
      window.removeEventListener('vapiAssistantChanged', handleVapiAssistantChanged);
      window.removeEventListener('updatePreviewConfig', handleUpdatePreviewConfig);
    };
  }, []);

  // Handle attorney selection from dropdown
  const handleAttorneySelection = (attorneyId) => {
    console.log(`[DashboardNew] Attorney selected from dropdown: ${attorneyId}`);
    if (window.standaloneAttorneyManager) {
      window.standaloneAttorneyManager.switchAttorney(attorneyId);
    }
  };

  // Handle assistant selection from Very Cool Assistants
  const handleAssistantSelect = async (assistantId) => {
    console.log(`[DashboardNew] Assistant selected: ${assistantId}`);

    try {
      // Update attorney in Supabase first - only update current_assistant_id, not vapi_assistant_id
      const { error } = await supabase
        .from('attorneys')
        .update({
          current_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorney.id);

      if (error) throw error;

      // Update local state using the correct updateAttorney function - only current_assistant_id
      await updateAttorney({
        current_assistant_id: assistantId
      });

      // Trigger preview config update
      handlePreviewConfigUpdate({
        currentAssistantId: assistantId,
        vapiAssistantId: assistantId
      });

      console.log('✅ Assistant switched successfully');
    } catch (error) {
      console.error('❌ Error switching assistant:', error);
    }
  };

  /**
   * Handle assistant change from enhanced dropdown in header
   */
  const handleAssistantChange = async (assistantId) => {
    if (assistantId === 'create_new') {
      // Navigate to agent tab to create new assistant
      setActiveTab('agent');
      setActiveSubTab(null);
      return;
    }

    // Use the existing handleAssistantSelect logic
    return handleAssistantSelect(assistantId);
  };

  /**
   * Handle loading assistant configuration from header dropdown
   */
  const handleConfigLoad = (config) => {
    if (!config) return;

    console.log('🔧 [DashboardNew] Loading assistant config from header:', config);

    // Update preview config with loaded configuration
    if (config.assistant_name) {
      handlePreviewConfigUpdate({ titleText: config.assistant_name });
    }

    // Refresh preview to show updated config
    if (activeTab === 'agent') {
      setTimeout(() => {
        refreshPreview();
      }, 300);
    }
  };

  // Effect to update preview config whenever the attorney state (from the hook) changes
  useEffect(() => {
    if (attorney) {
      console.log('[DashboardNew] Attorney state updated, updating previewConfig.');
      console.log('[DashboardNew] Attorney Vapi Assistant ID:', attorney.vapi_assistant_id);

      const updatedConfig = {
        firmName: attorney.firm_name || 'LegalScout Assistant', // Fallback for compatibility
        attorneyName: attorney.name,
        practiceAreas: attorney.practice_areas,
        state: attorney.state,
        practiceDescription: attorney.practice_description,
        primaryColor: attorney.primary_color,
        secondaryColor: attorney.secondary_color,
        backgroundColor: attorney.background_color,
        backgroundOpacity: attorney.background_opacity,
        buttonText: attorney.button_text,
        buttonOpacity: attorney.button_opacity,
        practiceAreaBackgroundOpacity: attorney.practice_area_background_opacity,
        textBackgroundColor: attorney.text_background_color,
        welcomeMessage: attorney.welcome_message,
        informationGathering: attorney.information_gathering_prompt,
        logoUrl: attorney.logo_url,
        mascot: attorney.mascot,
        vapiInstructions: attorney.vapi_instructions,
        vapiContext: attorney.vapi_context,
        theme: attorney.theme,
        // CRITICAL: Include the Vapi assistant ID for the preview
        vapiAssistantId: attorney.vapi_assistant_id,
        vapi_assistant_id: attorney.vapi_assistant_id // Include both formats for compatibility
      };

      setPreviewConfig(prev => {
        const newConfig = { ...prev, ...updatedConfig };
        console.log('[DashboardNew] Updated preview config with assistant ID:', newConfig.vapiAssistantId);

        // Send the updated config to the preview iframe immediately
        sendConfigToPreviewIframe(newConfig);

        return newConfig;
      });
    } else {
      // Optionally reset previewConfig if attorney becomes null
      // console.log('[DashboardNew] Attorney state is null, potentially reset previewConfig.');
    }
    // Depend on the attorney object from the hook
  }, [attorney]);

  // Handle tab change
  const handleTabChange = (tab) => {
    console.log('[DashboardNew] Tab changed to:', tab);
    setActiveTab(tab);

    // Always reset sub-tab when changing primary tabs (including agent)
    // This ensures clicking "Agent" always goes back to Agent Config
    setActiveSubTab(null);

    // Don't set a default sub-tab for agent - show Agent tab first
    // Sub-tabs will be available for selection
  };

  // Handle sub-tab change
  const handleSubTabChange = (subTab) => {
    console.log('[DashboardNew] Sub-tab changed to:', subTab);
    setActiveSubTab(subTab);
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      // Clear localStorage first
      localStorage.removeItem('attorney');
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('attorney_id');
      localStorage.removeItem('attorney_version');

      // Then sign out from Supabase
      await signOut();

      // Redirect to the main site (not a subdomain) to ensure clean state
      window.location.href = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
    } catch (error) {
      console.error('Error signing out:', error);
      // Even if there's an error, still try to redirect to clear the state
      window.location.href = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
    }
  };

  // Handle preview config update
  const handlePreviewConfigUpdate = async (newConfig) => {
    // Check if this is an assistant change
    if (newConfig._assistantChanged) {
      console.log('[DashboardNew] Assistant change detected, updating attorney state');

      // Remove the special flag before updating
      const { _assistantChanged, ...cleanConfig } = newConfig;

      // Update the attorney state using the correct updateAttorney function
      try {
        await updateAttorney({
          current_assistant_id: cleanConfig.current_assistant_id || cleanConfig.vapi_assistant_id,
          vapi_assistant_id: cleanConfig.vapi_assistant_id
        });
        console.log('[DashboardNew] Attorney state updated successfully for assistant change');
      } catch (error) {
        console.error('[DashboardNew] Error updating attorney state for assistant change:', error);
      }

      // Update preview config
      setPreviewConfig(prev => {
        const updatedConfig = {
          ...prev,
          ...cleanConfig,
          vapiAssistantId: cleanConfig.vapi_assistant_id,
          currentAssistantId: cleanConfig.current_assistant_id || cleanConfig.vapi_assistant_id
        };
        console.log('Updated preview config for assistant change:', updatedConfig);

        // Send the updated config to the preview iframe
        sendConfigToPreviewIframe(updatedConfig);
        return updatedConfig;
      });

      return; // Exit early for assistant changes
    }

    setPreviewConfig(prev => {
      const updatedConfig = { ...prev, ...newConfig };
      console.log('Updated preview config:', updatedConfig);

      // Send the updated config to the preview iframe
      sendConfigToPreviewIframe(updatedConfig);
      return updatedConfig; // Return the updated config for the state setter
    });

    // Update attorney data in the state manager asynchronously
    if (attorney && attorney.id) { // Ensure attorney and id are valid
      const attorneyUpdates = {};
      const assistantUpdates = {};

      // Map preview config fields to attorney fields (only non-assistant-specific fields)
      if (newConfig.attorneyName !== undefined) attorneyUpdates.name = newConfig.attorneyName;

      // Map assistant-specific fields to assistant config updates
      if (newConfig.firmName !== undefined) {
        assistantUpdates.agent_name = newConfig.firmName;
      }

      // Handle image URLs at assistant level only (prevent attorney table timeouts)
      if (newConfig.logoUrl !== undefined) {
        assistantUpdates.logo_url = newConfig.logoUrl;
      }
      if (newConfig.assistant_image_url !== undefined) {
        assistantUpdates.assistant_image_url = newConfig.assistant_image_url;
      }
      if (newConfig.practiceAreas !== undefined) attorneyUpdates.practice_areas = newConfig.practiceAreas;
      if (newConfig.state !== undefined) attorneyUpdates.state = newConfig.state;
      if (newConfig.practiceDescription !== undefined) attorneyUpdates.practice_description = newConfig.practiceDescription;
      if (newConfig.primaryColor !== undefined) attorneyUpdates.primary_color = newConfig.primaryColor;
      if (newConfig.secondaryColor !== undefined) attorneyUpdates.secondary_color = newConfig.secondaryColor;
      if (newConfig.backgroundColor !== undefined) attorneyUpdates.background_color = newConfig.backgroundColor;
      if (newConfig.backgroundOpacity !== undefined) attorneyUpdates.background_opacity = newConfig.backgroundOpacity;
      if (newConfig.buttonText !== undefined) attorneyUpdates.button_text = newConfig.buttonText;
      if (newConfig.buttonOpacity !== undefined) attorneyUpdates.button_opacity = newConfig.buttonOpacity;
      if (newConfig.practiceAreaBackgroundOpacity !== undefined) attorneyUpdates.practice_area_background_opacity = newConfig.practiceAreaBackgroundOpacity;
      if (newConfig.textBackgroundColor !== undefined) attorneyUpdates.text_background_color = newConfig.textBackgroundColor;
      if (newConfig.welcomeMessage !== undefined) attorneyUpdates.welcome_message = newConfig.welcomeMessage;
      // NOTE: informationGathering should be stored in Vapi assistant, not Supabase

      // IMPORTANT: Skip logoUrl and assistant_image_url - these are handled at assistant level only
      // Saving large data URLs to attorney table causes statement timeouts
      if (newConfig.logoUrl !== undefined && !newConfig.logoUrl.startsWith('data:')) {
        // Only save storage URLs, not data URLs
        if (newConfig.logoUrl.includes('supabase') || newConfig.logoUrl === '') {
          attorneyUpdates.logo_url = newConfig.logoUrl;
        }
      }

      // NOTE: mascot should use assistant_ui_configs.logo_url or assistant_image_url, not a separate field
      // Skip mascot field - it's handled at assistant level, not attorney level
      if (newConfig.vapiInstructions !== undefined) attorneyUpdates.vapi_instructions = newConfig.vapiInstructions;
      if (newConfig.vapiContext !== undefined) attorneyUpdates.vapi_context = newConfig.vapiContext;
      if (newConfig.theme !== undefined) attorneyUpdates.theme = newConfig.theme;
      // Add other mappings as needed

      // Save assistant-level updates if any
      if (Object.keys(assistantUpdates).length > 0) {
        try {
          const currentAssistantId = attorney.current_assistant_id || attorney.vapi_assistant_id;
          if (currentAssistantId) {
            console.log('[DashboardNew] Updating assistant config:', assistantUpdates);

            // Import and use the assistant config service
            const { AssistantConfigService } = await import('../services/assistantConfigService');
            await AssistantConfigService.updateAssistantConfig(currentAssistantId, attorney.id, assistantUpdates);

            console.log('[DashboardNew] Assistant config updated successfully');
          } else {
            console.warn('[DashboardNew] No assistant ID available for assistant config update');
          }
        } catch (error) {
          console.error('[DashboardNew] Failed to update assistant config:', error);
          setError(`Failed to save assistant changes: ${error.message}`);
        }
      }

      // Filter out any data URLs that might have slipped through
      const filteredAttorneyUpdates = {};
      Object.entries(attorneyUpdates).forEach(([key, value]) => {
        // Skip any field that contains a data URL to prevent timeouts
        if (typeof value === 'string' && value.startsWith('data:')) {
          console.warn(`[DashboardNew] Skipping ${key} with data URL to prevent timeout`);
          return;
        }
        filteredAttorneyUpdates[key] = value;
      });

      // Only call updateAttorney if there are actual updates after filtering
      if (Object.keys(filteredAttorneyUpdates).length > 0) {
        // Check if any of the updates are Vapi-relevant (removed firm_name since it's now assistant-level)
        const vapiRelevantFields = [
          'welcome_message',
          'vapi_instructions',
          'voice_provider',
          'voice_id',
          'ai_model',
          'vapi_assistant_id'
        ];

        const nonVapiFields = [
          'logo_url',
          'profile_image',
          'button_image',
          'primary_color',
          'secondary_color',
          'button_color',
          'background_color',
          'address',
          'phone',
          'practice_areas',
          'practice_description',
          'scheduling_link',
          'custom_fields',
          'summary_prompt',
          'structured_data_prompt',
          'structured_data_schema'
          // NOTE: 'mascot' removed - handled at assistant level via assistant_ui_configs
        ];

        const changedFields = Object.keys(filteredAttorneyUpdates);
        const vapiRelevantChanges = changedFields.filter(field => vapiRelevantFields.includes(field));
        const nonVapiChanges = changedFields.filter(field => nonVapiFields.includes(field));

        if (nonVapiChanges.length > 0 && vapiRelevantChanges.length === 0) {
          console.log('[DashboardNew] Only non-Vapi fields changed:', nonVapiChanges.join(', '));
          console.log('[DashboardNew] Skipping Vapi sync for UI-only changes');
        }

        try {
          console.log('[DashboardNew] Calling updateAttorney with:', filteredAttorneyUpdates);
          // Await the asynchronous updateAttorney call
          const updated = await updateAttorney(filteredAttorneyUpdates);
          console.log('[DashboardNew] Attorney updated successfully:', updated);
          // Optionally show a success message to the user
        } catch (error) {
          console.error('[DashboardNew] Failed to update attorney:', error);
          setError(`Failed to save changes: ${error.message}`); // Show error to user
        }
      }
    } else {
      // Only log in development to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.warn('[DashboardNew] Cannot update attorney - attorney data is invalid or missing ID');
      }
      // Optionally inform the user that saves cannot happen until profile is loaded
    }
  };

  // Function to send config to preview iframe with improved detection
  const sendConfigToPreviewIframe = (config) => {
    // Set updating state to show visual indicator
    setPreviewUpdating(true);

    // Use the enhanced iframe manager if available
    if (window.DashboardIframeManager) {
      console.log('[DashboardNew] Using enhanced iframe manager');

      window.DashboardIframeManager.sendConfigToPreview(config)
        .then(results => {
          const successCount = results.filter(r => r.value && r.value.success).length;
          console.log(`[DashboardNew] Config sent to ${successCount} iframes successfully`);

          // Clear updating state
          setTimeout(() => setPreviewUpdating(false), 500);
        })
        .catch(error => {
          console.error('[DashboardNew] Error sending config via iframe manager:', error);
          setPreviewUpdating(false);
        });

      return;
    }

    // Fallback to original method if manager not available
    console.log('[DashboardNew] Using fallback iframe communication');

    // Use a timeout to ensure the iframe has loaded
    setTimeout(() => {
      try {
        // First try to get the iframe from the previewRef
        let iframe = null;
        if (previewRef.current) {
          iframe = previewRef.current.querySelector('iframe');
        }

        // If iframe not found, try to find it in the document
        if (!iframe) {
          const allIframes = document.querySelectorAll('iframe');
          console.log(`Found ${allIframes.length} iframes on the page`);

          // Try to find the preview iframe by its src or id
          for (let i = 0; i < allIframes.length; i++) {
            const src = allIframes[i].src || '';
            const id = allIframes[i].id || '';

            if (
              src.includes('preview') ||
              src.includes('enhanced-preview') ||
              id.includes('preview') ||
              allIframes[i].className.includes('preview')
            ) {
              console.log('Found preview iframe:', src);
              iframe = allIframes[i];
              break;
            }
          }

          // If still not found, just use the first iframe as a fallback
          if (!iframe && allIframes.length > 0) {
            console.log('Using first iframe as fallback');
            iframe = allIframes[0];
          }
        }

        // Send message to the iframe if found
        if (iframe && iframe.contentWindow) {
          console.log('[DashboardNew] Sending updated config to preview iframe');
          console.log('[DashboardNew] Config being sent:', config);
          console.log('[DashboardNew] Assistant ID in config:', config.vapiAssistantId || config.vapi_assistant_id);

          // First try to check if iframe is loaded
          if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
            iframe.contentWindow.postMessage({
              type: 'UPDATE_PREVIEW_CONFIG',
              config: config
            }, '*');
          } else {
            // If not loaded, add a load event listener
            iframe.addEventListener('load', () => {
              iframe.contentWindow.postMessage({
                type: 'UPDATE_PREVIEW_CONFIG',
                config: config
              }, '*');
            }, { once: true });

            // Also try sending the message anyway
            iframe.contentWindow.postMessage({
              type: 'UPDATE_PREVIEW_CONFIG',
              config: config
            }, '*');
          }
        } else {
          console.warn('No suitable preview iframe found or contentWindow not accessible');
          // Reset updating state after a delay if no iframe found
          setTimeout(() => setPreviewUpdating(false), 1000);
        }
      } catch (error) {
        console.error('Error sending config to preview iframe:', error);
        // Reset updating state on error
        setPreviewUpdating(false);
      }

      // Set a timeout to reset the updating state if we don't get an acknowledgment
      setTimeout(() => {
        setPreviewUpdating(false);
      }, 3000);
    }, 300); // Reduced timeout for better responsiveness
  };

  // Toggle full preview
  const toggleFullPreview = () => {
    setIsFullPreview(!isFullPreview);
  };

  // Function to force sync attorney to Vapi
  const forceSyncToVapi = async () => {
    if (!attorney || !attorney.vapi_assistant_id) {
      console.warn('No attorney or assistant ID available for sync');
      return;
    }

    try {
      console.log('🔄 [DashboardNew] Force syncing attorney to Vapi...');
      console.log('🔄 [DashboardNew] Attorney ID:', attorney.id);
      console.log('🔄 [DashboardNew] Assistant ID:', attorney.vapi_assistant_id);

      // Import the sync helper
      const { updateVapiAssistant } = await import('../services/EnhancedSyncHelpers');

      // Get current assistant config for sync
      const currentAssistantId = attorney.current_assistant_id || attorney.vapi_assistant_id;
      let assistantConfig = null;

      try {
        const { AssistantConfigService } = await import('../services/assistantConfigService');
        assistantConfig = await AssistantConfigService.getAssistantConfig(currentAssistantId, attorney.id);
      } catch (error) {
        console.warn('[DashboardNew] Could not load assistant config, using attorney fallback:', error);
      }

      // Sync the assistant data to Vapi (prioritize assistant config over attorney data)
      const result = await updateVapiAssistant(attorney.vapi_assistant_id, {
        name: assistantConfig?.agent_name || attorney.firm_name || 'LegalScout Assistant',
        firstMessage: assistantConfig?.welcome_message || attorney.welcome_message || 'Hello, how can I help you today?',
        instructions: assistantConfig?.vapi_instructions || attorney.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
        voice: {
          provider: assistantConfig?.voice_provider || attorney.voice_provider || '11labs',
          voiceId: assistantConfig?.voice_id || attorney.voice_id || 'sarah'
        },
        ai_model: assistantConfig?.ai_model || attorney.ai_model || 'gpt-4o'
      });

      console.log('✅ [DashboardNew] Sync completed:', result);
      if (window.showSuccessNotification) {
        window.showSuccessNotification('Assistant synced successfully! The system instructions should now be updated.');
      }
    } catch (error) {
      console.error('❌ [DashboardNew] Sync failed:', error);
      if (window.showErrorNotification) {
        window.showErrorNotification(`Sync failed: ${error.message}`);
      }
    }
  };

  // Function to manually refresh the preview
  const refreshPreview = () => {
    try {
      console.log('Refreshing preview iframe');

      // First try to get the iframe from the previewRef
      let iframe = null;
      if (previewRef.current) {
        iframe = previewRef.current.querySelector('iframe');
      }

      // If iframe not found, try to find it in the document
      if (!iframe) {
        const allIframes = document.querySelectorAll('iframe');
        if (allIframes.length > 0) {
          // Try to find the preview iframe
          for (let i = 0; i < allIframes.length; i++) {
            const src = allIframes[i].src || '';
            if (src.includes('preview') || src.includes('enhanced-preview')) {
              iframe = allIframes[i];
              break;
            }
          }

          // If still not found, use the first iframe
          if (!iframe) {
            iframe = allIframes[0];
          }
        }
      }

      // If iframe found, reload it
      if (iframe) {
        // Store the current src
        const currentSrc = iframe.src;

        // Reload the iframe
        iframe.src = currentSrc;

        // Add a load event listener to send the config after reload
        iframe.addEventListener('load', () => {
          // Send the current config to the iframe
          sendConfigToPreviewIframe(previewConfig);
        }, { once: true });
      } else {
        // If no iframe found, just try to send the config
        sendConfigToPreviewIframe(previewConfig);
      }
    } catch (error) {
      console.error('Error refreshing preview:', error);
    }
  };

  // Toggle sidebar collapsed state
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Toggle sidebar pinned state
  const toggleSidebarPin = () => {
    setSidebarPinned(!sidebarPinned);
  };

  // Copy text to clipboard function
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopySuccess(`${type} copied!`);
        setTimeout(() => setCopySuccess(''), 2000);
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
        setCopySuccess('Failed to copy');
      });
  };

  // Mobile menu handlers
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  // Handle tab change with mobile menu close
  const handleTabChangeWithMobileClose = (tab) => {
    handleTabChange(tab);
    closeMobileMenu();
  };

  // Handle sub-tab change with mobile menu close
  const handleSubTabChangeWithMobileClose = (subTab) => {
    handleSubTabChange(subTab);
    closeMobileMenu();
  };

  // Close mobile menu when clicking overlay
  const handleOverlayClick = () => {
    closeMobileMenu();
  };

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        closeMobileMenu();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [mobileMenuOpen]);

  // Mobile preview handlers
  const openMobilePreview = (mode = 'modal') => {
    setMobilePreviewMode(mode);
    setMobilePreviewOpen(true);
  };

  const closeMobilePreview = () => {
    setMobilePreviewOpen(false);
  };

  const handleMobilePreviewOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      closeMobilePreview();
    }
  };

  // Generate preview URL for mobile - Use EXACT same pattern as dashboard preview
  const getMobilePreviewUrl = () => {
    // Use the exact same URL pattern as the working dashboard preview
    return `/simple-preview?subdomain=${attorney?.subdomain || 'default'}&theme=${isDark ? 'dark' : 'light'}&loadFromSupabase=true&useEnhancedPreview=true`;
  };

  // Expose mobile preview functions globally for AgentTab
  useEffect(() => {
    window.openMobilePreview = openMobilePreview;
    return () => {
      delete window.openMobilePreview;
    };
  }, []);

  // Render loading state - but only if we've been loading for at least 500ms
  // This prevents flickering for quick loads
  const [showLoadingUI, setShowLoadingUI] = useState(false);

  useEffect(() => {
    let timeoutId;
    if (loading) {
      // Only show loading UI after a delay to prevent flickering
      timeoutId = setTimeout(() => {
        setShowLoadingUI(true);
      }, 500);
    } else {
      setShowLoadingUI(false);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading]);

  if (loading && showLoadingUI) {
    return (
      <div className="dashboard-loading">
        <div className="spinner"></div>
        <p>Loading attorney profile...</p>
        <p className="loading-message">This may take a moment if you have multiple profiles</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="dashboard-error">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Home</button>
      </div>
    );
  }

  // Render dashboard
  // console.log('Current activeTab:', activeTab);
  return (
    <div className={`dashboard-container ${isDark ? 'dark' : 'light'} ${mobileMenuOpen ? 'mobile-menu-open' : ''}`}>
      {/* Header */}
      <header className="dashboard-header">
        {/* Mobile menu toggle */}
        <button
          className={`mobile-menu-toggle ${mobileMenuOpen ? 'active' : ''}`}
          onClick={() => {
            console.log('Hamburger clicked!');
            setMobileMenuOpen(!mobileMenuOpen);
          }}
          aria-label="Toggle mobile menu"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>

        <div className="dashboard-logo-section">
          <div className="dashboard-logo" onClick={() => handleTabChangeWithMobileClose('profile')} style={{ cursor: 'pointer' }}>
            <img
              src={attorney?.logo_url || previewConfig.logoUrl || "/PRIMARY CLEAR.png"}
              alt={previewConfig.firmName ? `${previewConfig.firmName} Logo` : "LegalScout Logo"}
              className="attorney-logo"
            />
            <h1>{previewConfig.firmName || attorney?.firm_name || 'Attorney Dashboard'}</h1>
          </div>
          {/* Compact Assistant Selector to the right of firm name */}
          {attorney && (
            <div className="header-assistant-selector-compact">
              <EnhancedAssistantDropdown
                attorney={attorney}
                onAssistantChange={handleAssistantChange}
                onConfigLoad={handleConfigLoad}
                disabled={loading}
                variant="header-compact"
              />
            </div>
          )}
        </div>

        <div className="dashboard-actions">
          <button className="theme-toggle" onClick={toggleTheme}>
            {isDark ? <MdLightMode /> : <MdDarkMode />}
          </button>
          <button className="sign-out-button" onClick={handleSignOut}>
            <FaSignOutAlt />
            <span>Sign Out</span>
          </button>
        </div>
      </header>

      {/* Mobile overlay */}
      <div
        className={`mobile-overlay ${mobileMenuOpen ? 'active' : ''}`}
        onClick={handleOverlayClick}
      ></div>

      {/* Main content */}
      <div className="dashboard-content">
        {/* Sidebar */}
        <div className={`dashboard-sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${sidebarPinned ? 'pinned' : ''} ${mobileMenuOpen ? 'mobile-open' : ''}`}>
          <div className="sidebar-controls">
            <button
              className="sidebar-toggle"
              onClick={toggleSidebar}
              title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {sidebarCollapsed ? <FaArrowRight /> : <FaArrowLeft />}
            </button>
            <button
              className={`sidebar-pin ${sidebarPinned ? 'active' : ''}`}
              onClick={toggleSidebarPin}
              title={sidebarPinned ? "Unpin sidebar" : "Pin sidebar"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width="14" height="14" fill="currentColor">
                <path d="M32 32C32 14.3 46.3 0 64 0H320c17.7 0 32 14.3 32 32s-14.3 32-32 32H290.5l11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3H32c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64H64C46.3 64 32 49.7 32 32zM160 384h64v96c0 17.7-14.3 32-32 32s-32-14.3-32-32V384z"/>
              </svg>
            </button>
          </div>
          <nav className="dashboard-nav">
            {/* Primary Tabs - Always Visible */}
            <button
              className={activeTab === 'agent' ? 'active' : ''}
              onClick={() => handleTabChangeWithMobileClose('agent')}
            >
              <FaHeadset />
              <span>Agent</span>
              <div className="nav-cta">
                {!attorney?.current_assistant_id && (
                  <span className="cta-dot new">•</span>
                )}
              </div>
            </button>

            {/* Agent Sub-tabs - Only visible when Agent is active */}
            {activeTab === 'agent' && (
              <div className="sub-tabs">
                <button
                  className={`sub-tab ${activeSubTab === 'customFields' ? 'active' : ''}`}
                  onClick={() => handleSubTabChangeWithMobileClose('customFields')}
                >
                  <FaCog />
                  <span>Data Collection</span>
                </button>
                <button
                  className={`sub-tab ${activeSubTab === 'tools' ? 'active' : ''}`}
                  onClick={() => handleSubTabChangeWithMobileClose('tools')}
                >
                  <FaTools />
                  <span>Tools</span>
                </button>
                <button
                  className={`sub-tab ${activeSubTab === 'calls' ? 'active' : ''}`}
                  onClick={() => handleSubTabChangeWithMobileClose('calls')}
                >
                  <FaPhone />
                  <span>Call</span>
                </button>
              </div>
            )}

            <button
              className={activeTab === 'consultations' ? 'active' : ''}
              onClick={() => handleTabChangeWithMobileClose('consultations')}
            >
              <FaChartBar />
              <span>Briefs</span>
            </button>

            <button
              className={activeTab === 'workflow' ? 'active' : ''}
              onClick={() => handleTabChangeWithMobileClose('workflow')}
            >
              <FaUsers />
              <span>Sessions</span>
            </button>

            {/* Share section divider */}
            <div className="sidebar-divider"></div>

            {/* Assistant-Aware Quick Share section */}
            <div className="sidebar-share-section">
              <AssistantAwareShare
                key={attorney?.current_assistant_id || attorney?.vapi_assistant_id || 'default'}
                variant="compact"
                showTitle={false}
                className="sidebar-share-component"
                onCopySuccess={(type) => {
                  setCopySuccess(`${type} copied!`);
                  setTimeout(() => setCopySuccess(''), 2000);
                }}
                onCopyError={(type, error) => {
                  setCopySuccess(`Failed to copy ${type.toLowerCase()}`);
                  setTimeout(() => setCopySuccess(''), 2000);
                }}
              />
              {copySuccess && <div className="copy-success">{copySuccess}</div>}
            </div>

            {/* Profile tab at the bottom */}
            <button
              className={`${activeTab === 'profile' ? 'active' : ''} profile-button-bottom`}
              onClick={() => handleTabChangeWithMobileClose('profile')}
            >
              <FaUser />
              <span>Profile</span>
              <div className="nav-cta">
                {(!attorney?.name || !attorney?.subdomain) && (
                  <span className="cta-dot incomplete">•</span>
                )}
              </div>
            </button>
          </nav>
        </div>

        {/* Main panel */}
        <div className={`dashboard-main ${isFullPreview ? 'preview-expanded' : ''}`}>
          {/* Tab content */}
          <div className={`tab-content ${isFullPreview ? 'hidden' : ''} ${['profile', 'consultations', 'workflow'].includes(activeTab) ? 'full-width' : ''}`}>
            {/* Profile Tab - Shows Integrations in preview panel area */}
            {activeTab === 'profile' && (
              <ProfileTab
                attorney={attorney}
                onUpdate={handlePreviewConfigUpdate}
                previewConfig={previewConfig}
              />
            )}

            {/* Agent Tab - Shows based on sub-tab selection */}
            {activeTab === 'agent' && !activeSubTab && (
              <AgentTab
                attorney={attorney}
                onUpdate={handlePreviewConfigUpdate}
                previewConfig={previewConfig}
              />
            )}

            {/* Agent Sub-tabs */}
            {activeTab === 'agent' && activeSubTab === 'customFields' && (
              <CustomFieldsTab
                key={`customfields-${attorney?.current_assistant_id || attorney?.vapi_assistant_id || 'default'}`}
                attorney={attorney}
                onUpdate={handlePreviewConfigUpdate}
              />
            )}

            {activeTab === 'agent' && activeSubTab === 'tools' && (
              <ToolsTab
                key={`tools-${attorney?.current_assistant_id || attorney?.vapi_assistant_id || 'default'}`}
                attorney={attorney}
                onUpdate={handlePreviewConfigUpdate}
              />
            )}

            {activeTab === 'agent' && activeSubTab === 'calls' && (
              <CallsTab
                attorney={attorney}
              />
            )}



            {/* Briefs Tab */}
            {activeTab === 'consultations' && (
              <ConsultationsTab
                attorney={attorney}
              />
            )}

            {/* Sessions Tab */}
            {activeTab === 'workflow' && (
              <SessionsTab
                attorney={attorney}
                onUpdate={handlePreviewConfigUpdate}
                previewConfig={previewConfig}
              />
            )}
          </div>

          {/* Preview panel - Shows Very Cool Assistants for Profile, Agent preview for Agent */}
          {(activeTab === 'profile' || (activeTab === 'agent' && !activeSubTab)) && (
            <div className={`preview-panel ${isFullPreview ? 'expanded' : ''}`}>
            {/* Show Very Cool Assistants for Profile tab */}
            {activeTab === 'profile' && (
              <div className="assistants-panel">
                <VeryCoolAssistants
                  attorney={attorney}
                  onAssistantSelect={handleAssistantSelect}
                  onNavigateToAgent={() => setActiveTab('agent')}
                />
              </div>
            )}

            {/* Show Agent preview for Agent tab */}
            {activeTab === 'agent' && (
              <>
                <div className="preview-controls">
                  <button onClick={refreshPreview} title="Refresh Preview">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                    </svg>
                    <span>Refresh</span>
                  </button>
                  <button onClick={forceSyncToVapi} title="Sync to Vapi" style={{ backgroundColor: '#4CAF50', color: 'white' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <span>Sync Assistant</span>
                  </button>
                  <button onClick={toggleFullPreview}>
                    {isFullPreview ? <FaCompress /> : <FaExpand />}
                    {isFullPreview ? 'Compact View' : 'Full View'}
                  </button>
                  {previewUpdating && (
                    <div className="preview-updating-indicator">
                      <div className="spinner-small"></div>
                      <span>Updating...</span>
                    </div>
                  )}
                </div>
                <div
                  className="preview-container"
                  ref={previewRef}
                  style={{
                    position: 'relative',
                    left: 'auto',
                    top: 'auto',
                    transform: 'none',
                    width: '100%',
                    height: '100%'
                  }}
                >
                  {/* Debug message to ensure container is rendering */}
                  {!previewLoaded && <div className="preview-loading">Loading preview...</div>}
                  <iframe
                    ref={(iframe) => {
                      if (iframe) {
                        // Don't send config immediately on load - wait for PREVIEW_READY message
                        iframe.onload = () => {
                          console.log('[DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message');
                          setPreviewLoaded(true);
                        };
                      }
                    }}
                    src={`/simple-preview?subdomain=${attorney?.subdomain || 'default'}&theme=${isDark ? 'dark' : 'light'}&loadFromSupabase=true&useEnhancedPreview=true`}
                    title="Agent Preview"
                    className="preview-iframe"
                    id="preview-iframe"
                    style={{
                      width: '100%',
                      height: '100%',
                      position: 'relative',
                      left: 'auto',
                      top: 'auto'
                    }}
                    onError={(e) => {
                      console.error('Preview iframe error:', e);
                      console.error('Preview iframe src:', `/simple-preview?subdomain=${attorney?.subdomain || 'default'}&theme=${isDark ? 'dark' : 'light'}&loadFromSupabase=true&useEnhancedPreview=true`);
                      console.error('Current location:', window.location.href);
                      setPreviewError(true);
                    }}
                  />
                </div>
              </>
            )}
          </div>
          )}
        </div>
      </div>

      {/* Mobile Preview Components */}
      {/* Swipe-up Preview Indicator - Show on both profile and agent tabs */}
      {(activeTab === 'agent' || activeTab === 'profile') && (
        <div
          className={`mobile-preview-swipe-indicator ${mobilePreviewOpen ? 'hidden' : ''}`}
          onClick={() => openMobilePreview('panel')}
          title="Swipe up to preview agent"
        >
          <div className="swipe-indicator-handle"></div>
          <div className="swipe-indicator-text">Preview Agent</div>
          <div className="swipe-indicator-arrow">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
            </svg>
          </div>
        </div>
      )}

      {/* Mobile Preview Modal */}
      <div
        className={`mobile-preview-modal ${mobilePreviewOpen && mobilePreviewMode === 'modal' ? 'active' : ''}`}
        onClick={handleMobilePreviewOverlayClick}
      >
        <div className="mobile-preview-content">
          <div className="mobile-preview-header">
            <h3>Agent Preview</h3>
            <button
              className="mobile-preview-close"
              onClick={closeMobilePreview}
            >
              ×
            </button>
          </div>
          <iframe
            ref={(iframe) => {
              if (iframe) {
                // Don't send config immediately on load - wait for PREVIEW_READY message
                iframe.onload = () => {
                  console.log('[DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message');
                };
              }
            }}
            src={getMobilePreviewUrl()}
            className="mobile-preview-iframe"
            title="Agent Preview"
          />
        </div>
      </div>

      {/* Mobile Preview Panel (Slide-up) */}
      <div className={`mobile-preview-panel ${mobilePreviewOpen && mobilePreviewMode === 'panel' ? 'active' : ''}`}>
        <div
          className="mobile-preview-panel-handle"
          onClick={closeMobilePreview}
        ></div>
        <div className="mobile-preview-panel-content">
          <iframe
            ref={(iframe) => {
              if (iframe) {
                // Don't send config immediately on load - wait for PREVIEW_READY message
                iframe.onload = () => {
                  console.log('[DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message');
                };
              }
            }}
            src={getMobilePreviewUrl()}
            className="mobile-preview-panel-iframe"
            title="Agent Preview"
          />
        </div>
      </div>

      {/* Attorney State Debugger removed for production */}

      {/* Progressive Setup Guide - Contextual shepherd */}
      <ProgressiveSetupGuide
        attorney={attorney}
        activeTab={activeTab}
        onNavigateToSignup={() => navigate('/auth')}
        onNavigateToDomain={() => {
          setActiveTab('agent'); // Changed to agent tab since subdomain is there
          setActiveSubTab(null);
          setMobileMenuOpen(false);
        }}
        onNavigateToAgent={() => {
          setActiveTab('agent');
          setActiveSubTab(null);
          setMobileMenuOpen(false);
        }}
        onHighlightElement={(elementId) => {
          // TODO: Implement element highlighting
          console.log('Highlighting element:', elementId);
        }}
      />
    </div>
  );
};

export default DashboardNew;
