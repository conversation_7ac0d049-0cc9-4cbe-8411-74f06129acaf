import React from 'react';

const MessageBubble = ({ message, isDarkTheme }) => {
  const isUser = message.role === 'user';
  
  const bubbleStyle = {
    display: 'flex',
    flexDirection: 'column',
    maxWidth: '80%',
    marginBottom: '10px',
    alignSelf: isUser ? 'flex-end' : 'flex-start',
    marginLeft: isUser ? 'auto' : '0',
    marginRight: isUser ? '0' : 'auto',
  };
  
  const contentStyle = {
    padding: '12px 16px',
    borderRadius: '18px',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
    wordBreak: 'break-word',
    backgroundColor: isUser 
      ? (isDarkTheme ? '#1a75c7' : '#0084ff')
      : (isDarkTheme ? '#383838' : '#e9e9eb'),
    color: isUser 
      ? 'white' 
      : (isDarkTheme ? '#e0e0e0' : '#333'),
    borderBottomRightRadius: isUser ? '4px' : '18px',
    borderBottomLeftRadius: isUser ? '18px' : '4px'
  };
  
  return (
    <div style={bubbleStyle}>
      <div style={contentStyle}>
        {message.content}
      </div>
    </div>
  );
};

export default MessageBubble;
