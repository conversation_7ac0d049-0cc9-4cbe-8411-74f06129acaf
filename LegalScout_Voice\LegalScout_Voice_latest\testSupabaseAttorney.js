import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

// Check if credentials are properly configured
if (!supabaseUrl || !supabaseKey || 
    supabaseUrl === 'your-supabase-url' || 
    supabaseKey === 'your-anon-key') {
  console.error('⚠️ Supabase credentials not configured properly in .env file');
  console.log('Please update the .env file with your actual Supabase URL and anon key');
  process.exit(1);
}

console.log('🔑 Supabase credentials found in environment variables');
console.log(`URL: ${supabaseUrl}`);
console.log(`Key: ${supabaseKey.substring(0, 3)}...${supabaseKey.substring(supabaseKey.length - 3)}`);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test subdomain to look up
const testSubdomain = process.argv[2] || 'test-law-firm';

async function testAttorneyLookup() {
  try {
    console.log(`\n🔍 Looking up attorney with subdomain: ${testSubdomain}`);
    
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', testSubdomain)
      .single();
    
    if (error) {
      console.error('Error querying attorney:', error.message);
      if (error.message.includes('does not exist')) {
        console.log('\n❓ The attorneys table does not exist yet. You may need to run the schema creation script.');
      }
      return;
    }
    
    if (!data) {
      console.log(`\n❌ No attorney found with subdomain: ${testSubdomain}`);
      return;
    }
    
    console.log('\n✅ Successfully found attorney record!');
    console.log('\n📋 Attorney record:');
    console.log(JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

// Run the test
testAttorneyLookup()
  .then(() => {
    console.log('\n✅ Test complete');
  })
  .catch(error => {
    console.error('\n❌ Error during test:', error.message);
  });
