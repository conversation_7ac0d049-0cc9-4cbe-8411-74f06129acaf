/**
 * Call History Service
 *
 * This service provides functions for fetching and managing call history
 * from Supabase.
 */

import { supabase } from '../lib/supabase';
import { vapiMcpService } from './vapiMcpService';

class CallHistoryService {
  /**
   * Get call history for an attorney
   * @param {string} attorneyId - The attorney ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - The call history
   */
  async getCallHistory(attorneyId, options = {}) {
    try {
      // Set default options
      const {
        limit = 20,
        page = 1,
        status = null,
        startDate = null,
        endDate = null,
        sortBy = 'start_time',
        sortDirection = 'desc'
      } = options;

      console.log(`[CallHistoryService] Fetching call history for attorney: ${attorneyId}`);

      // QUICK FIX: Fetch calls directly from Vapi API instead of Supabase
      // This ensures we see calls immediately after they're created
      try {
        const vapiCalls = await vapiMcpService.listCalls();
        console.log(`[CallHistoryService] Retrieved ${vapiCalls?.length || 0} calls from Vapi`);

        if (vapiCalls && vapiCalls.length > 0) {
          // Transform Vapi calls to match our expected format
          const transformedCalls = vapiCalls.map(call => ({
            call_id: call.id,
            attorney_id: attorneyId, // Associate with current attorney
            customer_phone: call.customer?.number || call.phoneNumber || 'Unknown',
            start_time: call.createdAt || call.startedAt || new Date().toISOString(),
            end_time: call.endedAt || null,
            duration: call.duration || 0,
            status: call.status || 'unknown',
            vapi_call_id: call.id,
            raw_data: call
          }));

          // Apply filters
          let filteredCalls = transformedCalls;

          if (status) {
            filteredCalls = filteredCalls.filter(call => call.status === status);
          }

          if (startDate) {
            filteredCalls = filteredCalls.filter(call =>
              new Date(call.start_time) >= new Date(startDate)
            );
          }

          if (endDate) {
            filteredCalls = filteredCalls.filter(call =>
              new Date(call.start_time) <= new Date(endDate)
            );
          }

          // Sort calls
          filteredCalls.sort((a, b) => {
            const aValue = new Date(a.start_time);
            const bValue = new Date(b.start_time);
            return sortDirection === 'desc' ? bValue - aValue : aValue - bValue;
          });

          // Apply pagination
          const offset = (page - 1) * limit;
          const paginatedCalls = filteredCalls.slice(offset, offset + limit);

          return {
            calls: paginatedCalls,
            total: filteredCalls.length,
            page,
            limit
          };
        }
      } catch (vapiError) {
        console.warn('[CallHistoryService] Failed to fetch from Vapi, falling back to Supabase:', vapiError);
      }

      // Fallback to Supabase if Vapi fails
      const offset = (page - 1) * limit;

      let query = supabase
        .from('call_records')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order(sortBy, { ascending: sortDirection === 'asc' })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      if (startDate) {
        query = query.gte('start_time', startDate);
      }

      if (endDate) {
        query = query.lte('start_time', endDate);
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return {
        calls: data || [],
        total: count || 0,
        page,
        limit
      };
    } catch (error) {
      console.error('Error getting call history:', error);
      throw error;
    }
  }
  
  /**
   * Get call details by ID
   * @param {string} callId - The call ID
   * @returns {Promise<Object>} - The call details
   */
  async getCallDetails(callId) {
    try {
      const { data, error } = await supabase
        .from('call_records')
        .select('*')
        .eq('call_id', callId)
        .single();
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting call details:', error);
      throw error;
    }
  }
  
  /**
   * Get call statistics for an attorney
   * @param {string} attorneyId - The attorney ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - The call statistics
   */
  async getCallStatistics(attorneyId, options = {}) {
    try {
      const { startDate = null, endDate = null } = options;

      // Try to get statistics from Vapi calls first
      try {
        const vapiCalls = await vapiMcpService.listCalls();

        if (vapiCalls && vapiCalls.length > 0) {
          // Filter calls by date range if provided
          let filteredCalls = vapiCalls;

          if (startDate) {
            filteredCalls = filteredCalls.filter(call =>
              new Date(call.createdAt || call.startedAt) >= new Date(startDate)
            );
          }

          if (endDate) {
            filteredCalls = filteredCalls.filter(call =>
              new Date(call.createdAt || call.startedAt) <= new Date(endDate)
            );
          }

          // Calculate statistics
          const totalCalls = filteredCalls.length;
          const completedCalls = filteredCalls.filter(call =>
            call.status === 'completed' || call.status === 'ended'
          ).length;
          const failedCalls = filteredCalls.filter(call =>
            call.status === 'failed' || call.status === 'error'
          ).length;
          const totalDuration = filteredCalls.reduce((sum, call) => sum + (call.duration || 0), 0);
          const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;

          return {
            totalCalls,
            completedCalls,
            failedCalls,
            totalDuration,
            averageDuration,
            completionRate: totalCalls > 0 ? (completedCalls / totalCalls) * 100 : 0
          };
        }
      } catch (vapiError) {
        console.warn('[CallHistoryService] Failed to get statistics from Vapi, falling back to Supabase:', vapiError);
      }

      // Fallback to Supabase
      let query = supabase
        .from('call_records')
        .select('status, duration')
        .eq('attorney_id', attorneyId);

      if (startDate) {
        query = query.gte('start_time', startDate);
      }

      if (endDate) {
        query = query.lte('start_time', endDate);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      const totalCalls = data.length;
      const completedCalls = data.filter(call => call.status === 'completed').length;
      const failedCalls = data.filter(call => call.status === 'failed').length;
      const totalDuration = data.reduce((sum, call) => sum + (call.duration || 0), 0);
      const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;

      return {
        totalCalls,
        completedCalls,
        failedCalls,
        totalDuration,
        averageDuration,
        completionRate: totalCalls > 0 ? (completedCalls / totalCalls) * 100 : 0
      };
    } catch (error) {
      console.error('Error getting call statistics:', error);
      throw error;
    }
  }
  
  /**
   * Get recent calls for an attorney
   * @param {string} attorneyId - The attorney ID
   * @param {number} limit - The maximum number of calls to return
   * @returns {Promise<Array>} - The recent calls
   */
  async getRecentCalls(attorneyId, limit = 5) {
    try {
      const { data, error } = await supabase
        .from('call_records')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order('start_time', { ascending: false })
        .limit(limit);
      
      if (error) {
        throw error;
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting recent calls:', error);
      throw error;
    }
  }
  
  /**
   * Format call duration
   * @param {number} seconds - The duration in seconds
   * @returns {string} - The formatted duration
   */
  formatDuration(seconds) {
    if (!seconds) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  
  /**
   * Format call status
   * @param {string} status - The call status
   * @returns {Object} - The formatted status with label and color
   */
  formatStatus(status) {
    switch (status) {
      case 'completed':
        return { label: 'Completed', color: 'green' };
      case 'failed':
        return { label: 'Failed', color: 'red' };
      case 'in_progress':
        return { label: 'In Progress', color: 'blue' };
      case 'queued':
        return { label: 'Queued', color: 'orange' };
      case 'scheduled':
        return { label: 'Scheduled', color: 'purple' };
      default:
        return { label: status, color: 'gray' };
    }
  }
}

// Export a singleton instance
export const callHistoryService = new CallHistoryService();
