import React from 'react';
import { withDevTools, createDebugger } from '../utils/debugConfig';

// Create debugger for CallSummary component
const debug = createDebugger('CallSummary');

const CallSummary = ({ data }) => {
  if (!data) {
    return (
      <div className="call-summary-empty">
        <h2>No Call Summary Available</h2>
        <p>There is no summary data available for this call.</p>
      </div>
    );
  }

  return (
    <div className="call-summary">
      <h2>Call Summary</h2>
      
      <div className="summary-section">
        <h3>Thank you for using LegalScout</h3>
        <p>We've gathered the following information about your case:</p>
      </div>
      
      <div className="summary-details">
        {data.issue && (
          <div className="summary-item">
            <strong>Legal Issue:</strong> {data.issue}
          </div>
        )}
        
        {data.practiceArea && (
          <div className="summary-item">
            <strong>Practice Area:</strong> {data.practiceArea}
          </div>
        )}
        
        {data.location?.address && (
          <div className="summary-item">
            <strong>Location:</strong> {data.location.address}
          </div>
        )}
        
        {data.noteworthy && (
          <div className="summary-item">
            <strong>Case Details:</strong> {data.noteworthy}
          </div>
        )}
        
        {data.urgency && (
          <div className="summary-item">
            <strong>Urgency Level:</strong> {data.urgency}
          </div>
        )}
      </div>
      
      <div className="next-steps">
        <h3>Next Steps</h3>
        {data.urgency === "High" ? (
          <p>
            Based on the urgency of your case, we recommend speaking with an attorney 
            as soon as possible. You can use the "Start New Consultation" button below 
            to connect with an available attorney.
          </p>
        ) : (
          <p>
            Thank you for your consultation. We've sent additional resources to your 
            email (if provided). You can start a new consultation anytime if you need
            further assistance.
          </p>
        )}
      </div>
      
      <div className="summary-actions">
        <button className="email-summary-button">Email Summary</button>
        <button className="download-summary-button">Download PDF</button>
      </div>
    </div>
  );
};

// Export the component with enhanced DevTools support
export default withDevTools(CallSummary, {
  displayName: 'CallSummary',
  type: 'display',
  description: 'Displays a summary of the call details and next steps',
  responsibleFor: ['call summary display', 'case information display', 'next steps guidance']
}); 