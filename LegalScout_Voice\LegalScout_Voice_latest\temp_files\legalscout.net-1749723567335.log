production-test.html:258 Uncaught SyntaxError: Cannot use 'import.meta' outside a module (at production-test.html:258:60)
index.ts:5 Loaded contentScript
production-test.html:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
ActiveCheckHelper.ts:21 received intentional event
production-test.html:164 Uncaught ReferenceError: runFullDiagnostics is not defined
    at HTMLButtonElement.onclick (production-test.html:164:63)
onclick @ production-test.html:164
ActiveCheckHelper.ts:8 updating page active status(index):14 🚀 [PRODUCTION CSP] Environment: PRODUCTION
(index):19 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
(index):48 🚀 [PRODUCTION CSP] Final eval status: WORKING
production-eval-fix.js:14 🔧 [ProductionEvalFix] Starting production eval fix...
production-eval-fix.js:24 🔧 [ProductionEvalFix] Environment detected: {isProduction: true, isVercel: true, hostname: 'legalscout.net'}
(index):57 🧪 [CSP TEST] Running CSP verification tests...
(index):62 🧪 [CSP TEST] ✅ Eval test PASSED: 4
(index):71 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
(index):83 🧪 [CSP TEST] All tests completed
(index):101 ✅ Vapi keys set globally
(index):122 ✅ Supabase keys set globally - should load correct assistant by domain
(index):133 🚀 [EMERGENCY] Starting emergency critical fixes...
(index):137 🔧 [EMERGENCY] Adding process polyfill
(index):144 ✅ [EMERGENCY] Process polyfill added
(index):155 🔧 [EMERGENCY] Development mode: false (forced production)
(index):185 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
(index):188 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
production-eval-fix.js:163 🔧 [ProductionEvalFix] Initializing...
production-eval-fix.js:66 🔧 [ProductionEvalFix] CSP Policy check: {hasCSP: true, hasUnsafeEval: true, policyLength: 1159}
production-eval-fix.js:34 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
production-eval-fix.js:47 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
production-eval-fix.js:202 🔧 [ProductionEvalFix] Status: {evalWorks: true, functionConstructorWorks: true, cspInfo: {…}, isProduction: true, isVercel: true, …}
production-eval-fix.js:203 🔧 [ProductionEvalFix] Initialization complete
VM3160:1 🧪 [CSP TEST] ✅ setTimeout string test PASSED
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
final-label-fix.js:9 🔧 [FinalLabelFix] Starting final label accessibility fix...
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:183 ✅ [FinalLabelFix] Final label fix initialized and monitoring
test-assistant-id-propagation.js:5 🧪 [TestAssistantIDPropagation] Starting test...
test-assistant-id-propagation.js:161 ✅ [TestAssistantIDPropagation] Test script loaded
vapi-assistant-cleanup.js:5 🧹 [VapiAssistantCleanup] Starting cleanup utility...
vapi-assistant-cleanup.js:280 ✅ [VapiAssistantCleanup] Cleanup utility loaded
vapi-assistant-cleanup.js:281 💡 Usage:
vapi-assistant-cleanup.js:282   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
vapi-assistant-cleanup.js:283   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
vapi-assistant-cleanup.js:284   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
production-eval-fix.js:163 🔧 [ProductionEvalFix] Initializing...
production-eval-fix.js:66 🔧 [ProductionEvalFix] CSP Policy check: {hasCSP: true, hasUnsafeEval: true, policyLength: 1159}
production-eval-fix.js:34 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
production-eval-fix.js:47 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
production-eval-fix.js:202 🔧 [ProductionEvalFix] Status: {evalWorks: true, functionConstructorWorks: true, cspInfo: {…}, isProduction: true, isVercel: true, …}
production-eval-fix.js:203 🔧 [ProductionEvalFix] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
production-cors-fix.js:238 [ProductionCorsFix] 🚀 Initializing all production fixes...
production-cors-fix.js:116 [ProductionCorsFix] 🌍 Ensuring production environment variables...
production-cors-fix.js:141 [ProductionCorsFix] ✅ Environment variables configured
production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
production-cors-fix.js:111 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:146 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:166 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:171 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:188 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:193 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:206 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:250 [ProductionCorsFix] 🎉 All production fixes initialized successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
index.ts:5 Loaded contentScript
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
production-cors-fix.js:211 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
legalscout.net/:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
production-cors-fix.js:224 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
test-assistant-id-propagation.js:157 🔄 Auto-running tests...
test-assistant-id-propagation.js:122 🚀 Running all tests...
test-assistant-id-propagation.js:9 🔍 Testing assistant ID propagation...
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
overrideMethod @ hook.js:608
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:71 🛡️ Testing CSP fix...
test-assistant-id-propagation.js:81 ✅ CSP includes vercel.live domain
test-assistant-id-propagation.js:91 🌐 Testing Vapi API endpoints...
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
overrideMethod @ hook.js:608
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:105 ♿ Testing form accessibility...
test-assistant-id-propagation.js:115 ✅ No problematic labels found
test-assistant-id-propagation.js:131 📊 Test Results: {assistantPropagation: false, cspFix: true, vapiEndpoints: false, formAccessibility: true}
test-assistant-id-propagation.js:136 🎯 Tests passed: 2/4
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
overrideMethod @ hook.js:608
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
