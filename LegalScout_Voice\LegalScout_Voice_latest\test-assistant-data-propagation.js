/**
 * Test Assistant Data Propagation
 * 
 * This script tests the complete assistant data flow to ensure:
 * 1. Assistant IDs are properly validated and not confused with attorney IDs
 * 2. Assistant selection properly propagates to all components
 * 3. Data integrity is maintained across the system
 * 4. All components show consistent assistant information
 */

console.log('🧪 Starting Assistant Data Propagation Test');

// Test configuration
const TEST_CONFIG = {
  // Use the actual attorney ID from the logs
  attorneyId: '87756a2c-a398-43f2-889a-b8815684df71',
  // Valid assistant IDs from the logs
  validAssistantIds: [
    '1d3471b7-8694-4844-b3ef-e05720693efc',
    '50e13a9e-22dd-4fe8-a03e-de627c5206c1'
  ],
  // Invalid assistant ID (attorney ID being used as assistant ID)
  invalidAssistantId: '87756a2c-a398-43f2-889a-b8815684df71'
};

/**
 * Test 1: Validate Assistant ID Format Detection
 */
async function testAssistantIdValidation() {
  console.log('\n📋 Test 1: Assistant ID Validation');
  
  try {
    // Test valid assistant IDs
    for (const assistantId of TEST_CONFIG.validAssistantIds) {
      console.log(`✅ Testing valid assistant ID: ${assistantId}`);
      
      // Check if this ID is different from attorney ID
      const isAttorneyId = assistantId === TEST_CONFIG.attorneyId;
      console.log(`   - Is attorney ID: ${isAttorneyId}`);
      console.log(`   - Length: ${assistantId.length}`);
      console.log(`   - Format: ${assistantId.includes('-') ? 'UUID' : 'Other'}`);
    }
    
    // Test invalid assistant ID (attorney ID)
    console.log(`❌ Testing invalid assistant ID (attorney ID): ${TEST_CONFIG.invalidAssistantId}`);
    const isInvalidCase = TEST_CONFIG.invalidAssistantId === TEST_CONFIG.attorneyId;
    console.log(`   - This IS the attorney ID: ${isInvalidCase}`);
    
    console.log('✅ Assistant ID validation test completed');
    
  } catch (error) {
    console.error('❌ Assistant ID validation test failed:', error);
  }
}

/**
 * Test 2: Check Current Attorney Data Structure
 */
async function testAttorneyDataStructure() {
  console.log('\n📋 Test 2: Attorney Data Structure');
  
  try {
    // Get attorney data from localStorage
    const storedAttorney = localStorage.getItem('attorney');
    if (!storedAttorney) {
      console.warn('⚠️ No attorney data in localStorage');
      return;
    }
    
    const attorney = JSON.parse(storedAttorney);
    console.log('📄 Attorney data structure:');
    console.log(`   - ID: ${attorney.id}`);
    console.log(`   - Firm Name: ${attorney.firm_name}`);
    console.log(`   - Vapi Assistant ID: ${attorney.vapi_assistant_id}`);
    console.log(`   - Current Assistant ID: ${attorney.current_assistant_id}`);
    
    // Check for data integrity issues
    const issues = [];
    
    if (attorney.vapi_assistant_id === attorney.id) {
      issues.push('🚨 CRITICAL: vapi_assistant_id matches attorney ID');
    }
    
    if (attorney.current_assistant_id === attorney.id) {
      issues.push('🚨 CRITICAL: current_assistant_id matches attorney ID');
    }
    
    if (attorney.vapi_assistant_id && attorney.vapi_assistant_id.includes('mock')) {
      issues.push('⚠️ WARNING: vapi_assistant_id contains "mock"');
    }
    
    if (issues.length > 0) {
      console.log('🚨 Data integrity issues found:');
      issues.forEach(issue => console.log(`   ${issue}`));
    } else {
      console.log('✅ No data integrity issues found');
    }
    
  } catch (error) {
    console.error('❌ Attorney data structure test failed:', error);
  }
}

/**
 * Test 3: Check Assistant Context State
 */
async function testAssistantContextState() {
  console.log('\n📋 Test 3: Assistant Context State');
  
  try {
    // Check if AssistantAwareContext is available
    const contextElement = document.querySelector('[data-assistant-context]');
    if (!contextElement) {
      console.warn('⚠️ Assistant context element not found');
      return;
    }
    
    // Check React DevTools or context state
    console.log('🔍 Checking assistant context state...');
    
    // Look for assistant-related elements in the DOM
    const assistantDropdown = document.querySelector('.enhanced-assistant-dropdown');
    const shareComponent = document.querySelector('[class*="assistant-aware-share"]');
    const previewFrame = document.querySelector('iframe[src*="simple-preview"]');
    
    console.log(`   - Assistant dropdown found: ${!!assistantDropdown}`);
    console.log(`   - Share component found: ${!!shareComponent}`);
    console.log(`   - Preview frame found: ${!!previewFrame}`);
    
    if (previewFrame) {
      console.log(`   - Preview frame src: ${previewFrame.src}`);
    }
    
  } catch (error) {
    console.error('❌ Assistant context state test failed:', error);
  }
}

/**
 * Test 4: Check Database Assistant Configs
 */
async function testDatabaseAssistantConfigs() {
  console.log('\n📋 Test 4: Database Assistant Configs');
  
  try {
    // Check if Supabase is available
    if (!window.supabase && !window.supabaseClient) {
      console.warn('⚠️ Supabase client not available');
      return;
    }
    
    const supabase = window.supabase || window.supabaseClient;
    
    // Get assistant configs for the attorney
    const { data: configs, error } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_id, assistant_name, firm_name')
      .eq('attorney_id', TEST_CONFIG.attorneyId);
    
    if (error) {
      console.error('❌ Error fetching assistant configs:', error);
      return;
    }
    
    console.log(`📊 Found ${configs?.length || 0} assistant configs:`);
    configs?.forEach((config, index) => {
      console.log(`   ${index + 1}. ${config.assistant_id}: ${config.assistant_name || 'Unnamed'}`);
      
      // Check if assistant ID matches attorney ID (data integrity issue)
      if (config.assistant_id === TEST_CONFIG.attorneyId) {
        console.log('      🚨 CRITICAL: This assistant ID matches the attorney ID!');
      }
    });
    
    // Get subdomain mappings
    const { data: subdomains, error: subdomainError } = await supabase
      .from('assistant_subdomains')
      .select('assistant_id, subdomain, is_active')
      .eq('attorney_id', TEST_CONFIG.attorneyId);
    
    if (!subdomainError && subdomains) {
      console.log(`🌐 Found ${subdomains.length} subdomain mappings:`);
      subdomains.forEach((mapping, index) => {
        console.log(`   ${index + 1}. ${mapping.assistant_id} → ${mapping.subdomain}.legalscout.net (active: ${mapping.is_active})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Database assistant configs test failed:', error);
  }
}

/**
 * Test 5: Simulate Assistant Selection
 */
async function testAssistantSelection() {
  console.log('\n📋 Test 5: Assistant Selection Simulation');
  
  try {
    // Find the assistant dropdown
    const dropdown = document.querySelector('.enhanced-assistant-dropdown');
    if (!dropdown) {
      console.warn('⚠️ Assistant dropdown not found');
      return;
    }
    
    console.log('🎯 Found assistant dropdown, checking current state...');
    
    // Check current selection
    const currentSelection = dropdown.querySelector('.dropdown-trigger .assistant-name');
    if (currentSelection) {
      console.log(`   - Current selection: ${currentSelection.textContent}`);
    }
    
    // Check available options
    const dropdownItems = dropdown.querySelectorAll('.dropdown-item');
    console.log(`   - Available options: ${dropdownItems.length}`);
    
    dropdownItems.forEach((item, index) => {
      const name = item.querySelector('.assistant-name')?.textContent;
      const subdomain = item.querySelector('.assistant-subdomain')?.textContent;
      console.log(`     ${index + 1}. ${name} (${subdomain})`);
    });
    
  } catch (error) {
    console.error('❌ Assistant selection test failed:', error);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Running Assistant Data Propagation Tests...');
  
  await testAssistantIdValidation();
  await testAttorneyDataStructure();
  await testAssistantContextState();
  await testDatabaseAssistantConfigs();
  await testAssistantSelection();
  
  console.log('\n✅ All tests completed!');
  console.log('\n📋 Summary:');
  console.log('   - Check the console output above for any 🚨 CRITICAL issues');
  console.log('   - Look for data integrity problems (attorney ID used as assistant ID)');
  console.log('   - Verify assistant selection propagates correctly');
  console.log('   - Ensure all components show consistent data');
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Run tests after a short delay to ensure page is loaded
  setTimeout(runAllTests, 1000);
} else {
  // Export for Node.js testing
  module.exports = {
    testAssistantIdValidation,
    testAttorneyDataStructure,
    testAssistantContextState,
    testDatabaseAssistantConfigs,
    testAssistantSelection,
    runAllTests
  };
}
