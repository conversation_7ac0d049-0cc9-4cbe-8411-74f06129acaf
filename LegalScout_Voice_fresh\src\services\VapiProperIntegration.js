/**
 * Vapi Proper Integration Service
 * 
 * Based on official Vapi documentation patterns:
 * - https://docs.vapi.ai/
 * - https://docs.vapi.ai/sdk/mcp-server
 * - https://www.vapiblocks.com/
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';

class VapiProperIntegration {
  constructor() {
    this.apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
    this.publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
    this.mcpClient = null;
    this.transport = null;
  }

  /**
   * Initialize MCP connection using proper Vapi patterns
   * Following: https://docs.vapi.ai/sdk/mcp-server#custom-mcp-client-integration
   */
  async initializeMCP() {
    try {
      console.log('[VapiProperIntegration] Skipping MCP initialization - using direct API only');

      // Skip MCP initialization to avoid CORS issues
      // Use direct API calls instead
      this.mcpClient = null;
      this.transport = null;

      await this.mcpClient.connect(this.transport);
      console.log('[VapiProperIntegration] ✅ MCP connected successfully');
      return true;
    } catch (error) {
      console.warn('[VapiProperIntegration] MCP connection failed, using direct API:', error.message);
      return false;
    }
  }

  /**
   * Get assistant using proper MCP tools
   * Following: https://docs.vapi.ai/sdk/mcp-server#core-tools
   */
  async getAssistant(assistantId) {
    try {
      // Try MCP first
      if (this.mcpClient) {
        const response = await this.mcpClient.callTool({
          name: 'get_assistant',
          arguments: { assistantId }
        });
        
        const assistant = this.parseToolResponse(response);
        if (assistant) {
          console.log('[VapiProperIntegration] ✅ Assistant retrieved via MCP');
          return assistant;
        }
      }

      // Fallback to direct API
      return await this.getAssistantDirect(assistantId);
    } catch (error) {
      console.error('[VapiProperIntegration] Error getting assistant:', error);
      return await this.getAssistantDirect(assistantId);
    }
  }

  /**
   * Direct API call as fallback
   * Following: https://docs.vapi.ai/api-reference
   */
  async getAssistantDirect(assistantId) {
    try {
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistant = await response.json();
      console.log('[VapiProperIntegration] ✅ Assistant retrieved via direct API');
      return assistant;
    } catch (error) {
      console.error('[VapiProperIntegration] Direct API failed:', error);
      throw error;
    }
  }

  /**
   * List all assistants using MCP
   * Following: https://docs.vapi.ai/sdk/mcp-server#core-tools
   */
  async listAssistants() {
    try {
      if (this.mcpClient) {
        const response = await this.mcpClient.callTool({
          name: 'list_assistants',
          arguments: {}
        });
        
        const assistants = this.parseToolResponse(response);
        if (assistants) {
          console.log('[VapiProperIntegration] ✅ Assistants listed via MCP');
          return assistants;
        }
      }

      // Fallback to direct API
      return await this.listAssistantsDirect();
    } catch (error) {
      console.error('[VapiProperIntegration] Error listing assistants:', error);
      return await this.listAssistantsDirect();
    }
  }

  /**
   * Direct API for listing assistants
   */
  async listAssistantsDirect() {
    try {
      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistants = await response.json();
      console.log('[VapiProperIntegration] ✅ Assistants listed via direct API');
      return assistants;
    } catch (error) {
      console.error('[VapiProperIntegration] Direct API list failed:', error);
      throw error;
    }
  }

  /**
   * Create outbound call using MCP
   * Following: https://docs.vapi.ai/sdk/mcp-server#core-tools
   */
  async createCall(callConfig) {
    try {
      if (this.mcpClient) {
        const response = await this.mcpClient.callTool({
          name: 'create_call',
          arguments: callConfig
        });
        
        const call = this.parseToolResponse(response);
        if (call) {
          console.log('[VapiProperIntegration] ✅ Call created via MCP');
          return call;
        }
      }

      // Fallback to direct API
      return await this.createCallDirect(callConfig);
    } catch (error) {
      console.error('[VapiProperIntegration] Error creating call:', error);
      return await this.createCallDirect(callConfig);
    }
  }

  /**
   * Direct API for creating calls
   */
  async createCallDirect(callConfig) {
    try {
      const response = await fetch('https://api.vapi.ai/call', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(callConfig)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const call = await response.json();
      console.log('[VapiProperIntegration] ✅ Call created via direct API');
      return call;
    } catch (error) {
      console.error('[VapiProperIntegration] Direct API call creation failed:', error);
      throw error;
    }
  }

  /**
   * Parse MCP tool response
   * Following: https://docs.vapi.ai/sdk/mcp-server#detailed-example-build-a-client-with-nodejs
   */
  parseToolResponse(response) {
    if (!response?.content) return response;
    
    const textItem = response.content.find(item => item.type === 'text');
    if (textItem?.text) {
      try {
        return JSON.parse(textItem.text);
      } catch {
        return textItem.text;
      }
    }
    return response;
  }

  /**
   * Get Vapi Web SDK configuration for client-side
   * Following: https://docs.vapi.ai/quickstart/web
   */
  getWebSDKConfig(assistantId) {
    return {
      apiKey: this.publicKey, // Use public key for client-side
      assistant: assistantId,
      config: {
        // Web call configuration
        transcriber: {
          provider: "deepgram",
          model: "nova-2"
        },
        voice: {
          provider: "11labs",
          voiceId: "sarah"
        }
      }
    };
  }

  /**
   * Generate attorney-ready link using proper patterns
   */
  generateAttorneyLink(assistantId, subdomain = null) {
    const baseUrl = subdomain ? 
      `https://${subdomain}.legalscout.net` : 
      'https://legalscout.net';
    
    // Use VapiBlocks pattern for voice interface
    return `${baseUrl}/simple-preview?assistantId=${assistantId}&useVapiBlocks=true`;
  }

  /**
   * Cleanup MCP connection
   */
  async cleanup() {
    try {
      if (this.mcpClient) {
        await this.mcpClient.close();
        console.log('[VapiProperIntegration] ✅ MCP connection closed');
      }
    } catch (error) {
      console.error('[VapiProperIntegration] Cleanup error:', error);
    }
  }
}

// Create singleton instance
export const vapiProperIntegration = new VapiProperIntegration();
export default vapiProperIntegration;
