/**
 * Call Debugger Utility
 * 
 * This utility provides comprehensive debugging for Vapi calls,
 * tracking initialization, status changes, events, and errors.
 * 
 * Usage:
 * 1. Import the debugger: import { CallDebugger } from '../utils/callDebugger';
 * 2. Create an instance: const debugger = new CallDebugger('HomeScreen');
 * 3. Log events: debugger.log('Starting call');
 * 4. Track call lifecycle: debugger.trackCall(callObject);
 */

// Enable or disable debugging globally
const DEBUG_ENABLED = true;

// Configure which components to debug
const DEBUG_COMPONENTS = {
  'HomeScreen': true,
  'VapiCall': true,
  'useVapiCall': true,
  'CallController': true,
  'App': true,
  'MCP': true
};

/**
 * Call Debugger class for tracking call-related events and issues
 */
export class CallDebugger {
  /**
   * Create a new call debugger
   * @param {string} componentName - Name of the component using the debugger
   * @param {Object} options - Additional options
   */
  constructor(componentName, options = {}) {
    this.componentName = componentName;
    this.enabled = options.enabled !== undefined ? options.enabled : DEBUG_ENABLED;
    this.componentEnabled = DEBUG_COMPONENTS[componentName] || false;
    this.callId = null;
    this.startTime = null;
    this.events = [];
    this.errors = [];
    this.networkRequests = [];
    this.callStatus = 'idle';
    
    // Initialize with a session ID for tracking related logs
    this.sessionId = `call-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    this.log('Debugger initialized');
  }
  
  /**
   * Log a message with timestamp
   * @param {string} message - Message to log
   * @param {Object} data - Optional data to include
   */
  log(message, data = null) {
    if (!this.enabled || !this.componentEnabled) return;
    
    const timestamp = new Date().toISOString();
    const logPrefix = `[CallDebugger:${this.componentName}]`;
    
    // Add to events array
    this.events.push({
      timestamp,
      message,
      data
    });
    
    // Log to console
    if (data) {
      console.log(`${logPrefix} ${message}`, data);
    } else {
      console.log(`${logPrefix} ${message}`);
    }
  }
  
  /**
   * Log an error
   * @param {string} message - Error message
   * @param {Error|Object} error - Error object
   */
  error(message, error) {
    if (!this.enabled || !this.componentEnabled) return;
    
    const timestamp = new Date().toISOString();
    const logPrefix = `[CallDebugger:${this.componentName}]`;
    
    // Add to errors array
    this.errors.push({
      timestamp,
      message,
      error: error instanceof Error ? { 
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    });
    
    // Log to console
    console.error(`${logPrefix} ERROR: ${message}`, error);
  }
  
  /**
   * Track a call object and its lifecycle
   * @param {Object} call - Call object to track
   */
  trackCall(call) {
    if (!this.enabled || !this.componentEnabled) return;
    if (!call) {
      this.error('trackCall called with null call object');
      return;
    }
    
    this.callId = call.id || 'unknown';
    this.startTime = new Date();
    this.log(`Tracking call: ${this.callId}`, call);
    
    // If the call object has event listeners, hook into them
    if (call.on && typeof call.on === 'function') {
      try {
        call.on('call-start', (data) => this.onCallEvent('call-start', data));
        call.on('call-end', (data) => this.onCallEvent('call-end', data));
        call.on('error', (data) => this.onCallEvent('error', data));
        call.on('speech-start', (data) => this.onCallEvent('speech-start', data));
        call.on('speech-end', (data) => this.onCallEvent('speech-end', data));
        call.on('transcription', (data) => this.onCallEvent('transcription', data));
        this.log('Call event listeners attached');
      } catch (error) {
        this.error('Failed to attach call event listeners', error);
      }
    }
  }
  
  /**
   * Handle call events
   * @param {string} eventName - Name of the event
   * @param {Object} data - Event data
   */
  onCallEvent(eventName, data) {
    this.log(`Call event: ${eventName}`, data);
    
    // Update call status based on events
    if (eventName === 'call-start') {
      this.callStatus = 'active';
    } else if (eventName === 'call-end') {
      this.callStatus = 'ended';
      this.summarizeCall();
    } else if (eventName === 'error') {
      this.error(`Call error event: ${data?.message || 'Unknown error'}`, data);
    }
  }
  
  /**
   * Track network request related to calls
   * @param {string} url - Request URL
   * @param {string} method - HTTP method
   * @param {Object} requestData - Request data
   * @param {Object} response - Response data
   */
  trackNetworkRequest(url, method, requestData, response) {
    if (!this.enabled || !this.componentEnabled) return;
    
    this.networkRequests.push({
      timestamp: new Date().toISOString(),
      url,
      method,
      requestData,
      response: response ? {
        status: response.status,
        ok: response.ok,
        data: response.data
      } : null
    });
    
    this.log(`Network ${method} request to ${url}`, { requestData, response });
  }
  
  /**
   * Update call status
   * @param {string} status - New call status
   */
  updateStatus(status) {
    if (!this.enabled || !this.componentEnabled) return;
    
    const prevStatus = this.callStatus;
    this.callStatus = status;
    
    this.log(`Call status changed: ${prevStatus} -> ${status}`);
    
    // If call ended, summarize
    if (status === 'ended' || status === 'error') {
      this.summarizeCall();
    }
  }
  
  /**
   * Summarize call information
   */
  summarizeCall() {
    if (!this.enabled || !this.componentEnabled) return;
    if (!this.startTime) return;
    
    const endTime = new Date();
    const duration = (endTime - this.startTime) / 1000; // in seconds
    
    this.log(`Call summary for ${this.callId}`, {
      duration: `${duration.toFixed(2)} seconds`,
      status: this.callStatus,
      events: this.events.length,
      errors: this.errors.length,
      networkRequests: this.networkRequests.length
    });
  }
  
  /**
   * Get all debug information as an object
   * @returns {Object} Debug information
   */
  getDebugInfo() {
    return {
      componentName: this.componentName,
      sessionId: this.sessionId,
      callId: this.callId,
      startTime: this.startTime,
      callStatus: this.callStatus,
      events: this.events,
      errors: this.errors,
      networkRequests: this.networkRequests
    };
  }
}

// Create a global registry of debuggers for cross-component tracking
const debuggerRegistry = {};

/**
 * Get or create a call debugger for a component
 * @param {string} componentName - Component name
 * @param {Object} options - Debugger options
 * @returns {CallDebugger} Call debugger instance
 */
export function getCallDebugger(componentName, options = {}) {
  if (!debuggerRegistry[componentName]) {
    debuggerRegistry[componentName] = new CallDebugger(componentName, options);
  }
  return debuggerRegistry[componentName];
}

/**
 * Get all registered debuggers
 * @returns {Object} Map of component names to debugger instances
 */
export function getAllDebuggers() {
  return debuggerRegistry;
}

export default {
  CallDebugger,
  getCallDebugger,
  getAllDebuggers
};
