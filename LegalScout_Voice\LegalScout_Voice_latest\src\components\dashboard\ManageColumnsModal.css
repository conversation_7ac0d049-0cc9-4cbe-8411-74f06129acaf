/* Manage Columns Modal Styles */

/* Modal Overlay */
.manage-columns-modal .modal-overlay,
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.manage-columns-modal {
  max-width: 900px;
  width: 95vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.manage-columns-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.manage-columns-modal .modal-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1f2937;
  font-size: 1.25rem;
}

.manage-columns-modal .close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.manage-columns-modal .close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.manage-columns-modal .modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
  background: white;
  color: #1f2937;
}

/* Controls */
.column-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.filter-controls,
.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-controls label,
.sort-controls label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.filter-controls select,
.sort-controls select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #1f2937;
  font-size: 0.875rem;
}

.bulk-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

.bulk-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.bulk-action-button.delete {
  background: #dc2626;
  color: white;
}

.bulk-action-button.delete:hover {
  background: #b91c1c;
}

.bulk-action-button.clear {
  background: #6b7280;
  color: white;
}

.bulk-action-button.clear:hover {
  background: #4b5563;
}

/* Column Statistics */
.column-stats {
  display: flex;
  gap: 2rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

/* Column List */
.columns-list {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.list-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 120px;
}

.header-labels {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  flex: 1;
  margin-left: 2rem;
}

/* Column Items */
.column-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  transition: all 0.2s;
  cursor: grab;
}

.column-item:hover {
  background: #f9fafb;
}

.column-item.selected {
  background: #dbeafe;
  border-color: #93c5fd;
}

.column-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.column-item:active {
  cursor: grabbing;
}

.column-select {
  width: 40px;
  display: flex;
  justify-content: center;
}

.drag-handle {
  width: 40px;
  display: flex;
  justify-content: center;
  color: #9ca3af;
  cursor: grab;
}

.drag-handle:hover {
  color: #6b7280;
}

.column-info {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  flex: 1;
  margin-left: 1rem;
  align-items: center;
}

.column-name {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.column-name strong {
  color: #1f2937;
  font-size: 0.875rem;
}

.column-name small {
  color: #6b7280;
  font-size: 0.75rem;
  font-family: monospace;
}

.column-type {
  color: #6b7280;
  font-size: 0.875rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.visible {
  background: #dcfce7;
  color: #166534;
}

.status-badge.hidden {
  background: #fef2f2;
  color: #991b1b;
}

.column-created {
  color: #6b7280;
  font-size: 0.875rem;
}

.column-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.action-btn.toggle {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.toggle:hover {
  background: #3b82f6;
  color: white;
}

.action-btn.edit {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.edit:hover {
  background: #f59e0b;
  color: white;
}

.action-btn.delete {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.delete:hover {
  background: #dc2626;
  color: white;
}

/* Empty State */
.empty-state {
  padding: 3rem;
  text-align: center;
  color: #6b7280;
}

/* Modal Footer */
.manage-columns-modal .modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.footer-info small {
  color: #6b7280;
  font-size: 0.875rem;
}

.close-button-footer {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button-footer:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manage-columns-modal {
    width: 98vw;
    margin: 1rem;
  }

  .column-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .bulk-actions {
    margin-left: 0;
  }

  .column-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .header-labels,
  .column-info {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .column-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .drag-handle {
    display: none;
  }
}
