dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
VM340:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM341:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
VM327:229 [vite] connecting...
VM327:325 [vite] connected.
supabase.js?t=*************:190 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
EnhancedPreview.css:30 [VapiLoader] Starting Vapi SDK loading process
EnhancedPreview.css:34 [VapiLoader] Attempting to import @vapi-ai/web package
ToolsTab.css:94 [VapiMcpService] Created clean fetch from iframe
ToolsTab.css:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
AssistantAwareShare.jsx:14 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
ConsultationMapView.jsx:168 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationMapView.jsx:23 [AttorneyProfileManager] Auto-initializing from localStorage
ConsultationMapView.jsx:22 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
overlay.ts:9 🚀 [LegalScout] Starting React app...
overlay.ts:9 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
overlay.ts:9 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
overlay.ts:9 ✅ [LegalScout] React app rendered successfully
565.js:19 [ErrorBoundary] Creating React placeholder
565.js:23 [ErrorBoundary] Adding createContext placeholder
565.js:55 [ErrorBoundary] Adding useState placeholder
565.js:55 [ErrorBoundary] Adding useEffect placeholder
565.js:55 [ErrorBoundary] Adding useLayoutEffect placeholder
565.js:55 [ErrorBoundary] Adding useRef placeholder
565.js:55 [ErrorBoundary] Adding useCallback placeholder
565.js:55 [ErrorBoundary] Adding useMemo placeholder
565.js:55 [ErrorBoundary] Adding useContext placeholder
565.js:55 [ErrorBoundary] Adding forwardRef placeholder
565.js:55 [ErrorBoundary] Adding createElement placeholder
565.js:55 [ErrorBoundary] Adding cloneElement placeholder
565.js:55 [ErrorBoundary] Adding createRef placeholder
565.js:55 [ErrorBoundary] Adding Component placeholder
565.js:55 [ErrorBoundary] Adding PureComponent placeholder
565.js:55 [ErrorBoundary] Adding Fragment placeholder
565.js:55 [ErrorBoundary] Adding Children placeholder
565.js:55 [ErrorBoundary] Adding isValidElement placeholder
565.js:61 [ErrorBoundary] React polyfills applied
prepareInjection.js:1 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
prepareInjection.js:1 🔥 [App.jsx] App component is starting!
prepareInjection.js:1 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
EnhancedPreview.css:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
EnhancedPreview.css:51 [VapiLoader] ✅ Vapi SDK validation successful
App.css:63 🔐 [AuthContext] Starting auth initialization...
App.css:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=*************:27 🔧 [Supabase] EMERGENCY: Bypassing broken Supabase client, using stub only
supabase.js?t=*************:28 🔧 [Supabase] Headers/fetch environment is corrupted by browser extension or hook
AttorneySelector.css:3 🚧 Creating stub Supabase client for fallback
App.css:356 Using real authentication in all environments
App.css:63 🔐 [AuthContext] Starting auth initialization...
App.css:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
App.css:356 Using real authentication in all environments
AttorneySelector.css:14 🚧 [Stub] Auth state change listener registered
AttorneySelector.css:14 🚧 [Stub] Auth state change listener registered
App.css:126 🔐 [AuthContext] No session found
App.css:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
App.css:126 🔐 [AuthContext] No session found
App.css:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
VM618:69 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
ConsultationMapView.jsx:9 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
AttorneySelector.css:19 🚧 [Stub] Querying table: attorneys
AttorneySelector.css:24 🚧 [Stub] SELECT * FROM attorneys
AttorneySelector.css:28 🚧 [Stub] WHERE id = 87756a2c-a398-43f2-889a-b8815684df71
AttorneySelector.css:40 🚧 [Stub] SINGLE result
ConsultationMapView.jsx:34 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
VM618:28 received intentional event
App.jsx:418 🔐 [App] Starting Google sign-in...
supabase.js?t=*************:181 🔐 [Supabase] Starting Google sign-in with emergency auth...
supabase.js?t=*************:80 🚨 [EmergencyAuth] Starting direct Google OAuth...
supabase.js?t=*************:92 🚨 [EmergencyAuth] Redirecting to: https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/authorize?provider=google&redirect_to=http%3A%2F%2Flocalhost%3A5174%2Fauth%2Fcallback&apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
Navigated to https://accounts.google.com/o/oauth2/v2/auth?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU&client_id=************-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com&redirect_to=http%3A%2F%2Flocalhost%3A5174%2Fauth%2Fcallback&redirect_uri=https%3A%2F%2Futopqxsvudgrtiwenlzl.supabase.co%2Fauth%2Fv1%2Fcallback&response_type=code&scope=email+profile&state=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************.Jw56xV_CbIHrxc7fZUF1RSOwQ99Oes2DI9JHhNdkKFo

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
[Report Only] Refused to frame 'https://accounts.youtube.com/' because an ancestor violates the following Content Security Policy directive: "frame-ancestors 'self'".

chunk-Q72EVS5P.js?v=7a56b667:69 Loaded contentScript
App.css:126 ChatGPT Assistant content script loaded
chunk-Q72EVS5P.js?v=7a56b667:28 received intentional event
chunk-Q72EVS5P.js?v=7a56b667:28 updating page active status
Navigated to http://localhost:5174/auth/callback
callback:26 🚀 [LegalScout] Initializing environment...
callback:48 ✅ [LegalScout] Environment initialized
callback:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
supabase.js?t=*************:190 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=*************:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=*************:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=*************:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 🔐 [AuthCallback] Starting OAuth callback handling...
 🔐 [AuthCallback] Checking for OAuth tokens...
 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FIXJD5ebRykdK86y7Y1_jwbsVmzKaKl7_8I4VHV9fOI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=vzgk5kcxi2jc&token_type=bearer
 🔐 [AuthCallback] Query params: 
 🔐 [AuthCallback] Access token found: true
 🔐 [AuthCallback] Found OAuth tokens in URL
 🔐 [AuthCallback] Processing authentication for: <EMAIL>
 🔧 [AuthCallback] Calling fixAuthProfile...
 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AuthProfileFixer] Access token length: 1297
 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-14T18:16:15.234617+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext] Starting auth initialization...
 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
 🔧 [Supabase] EMERGENCY: Bypassing broken Supabase client, using stub only
 🔧 [Supabase] Headers/fetch environment is corrupted by browser extension or hook
 🚧 Creating stub Supabase client for fallback
 Using real authentication in all environments
 🔐 [AuthCallback] Starting OAuth callback handling...
 🔐 [AuthCallback] Checking for OAuth tokens...
 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FIXJD5ebRykdK86y7Y1_jwbsVmzKaKl7_8I4VHV9fOI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=vzgk5kcxi2jc&token_type=bearer
 🔐 [AuthCallback] Query params: 
 🔐 [AuthCallback] Access token found: true
 🔐 [AuthCallback] Found OAuth tokens in URL
 🔐 [AuthCallback] Processing authentication for: <EMAIL>
 🔧 [AuthCallback] Calling fixAuthProfile...
 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AuthProfileFixer] Access token length: 1297
 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-14T18:16:15.234617+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext] Starting auth initialization...
 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
 Using real authentication in all environments
 🚧 [Stub] Auth state change listener registered
 🚧 [Stub] Auth state change listener registered
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 Loaded contentScript
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 [useStandaloneAttorney] Manager not ready, will retry...
 [AuthProfileFixer] 🔍 Method 1 response status: 200
 [AuthProfileFixer] 🔍 Method 1 response status: 200
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [AuthProfileFixer] 🔍 Method 1 response data length: 1
 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-14T18:16:15.234617+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
 💾 [AuthCallback] Attorney profile stored in localStorage
 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
 [useStandaloneAttorney] Manager not ready, will retry...
 [AuthProfileFixer] 🔍 Method 1 response data length: 1
 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-14T18:16:15.234617+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
 💾 [AuthCallback] Attorney profile stored in localStorage
 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
auth/callback#access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FIXJD5ebRykdK86y7Y1_jwbsVmzKaKl7_8I4VHV9fOI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=vzgk5kcxi2jc&token_type=bearer:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
 [useStandaloneAttorney] Manager not ready, will retry...
 🚧 [Stub] Querying table: assistant_subdomains
 🚧 [Stub] SELECT subdomain FROM assistant_subdomains
 🚧 [Stub] WHERE assistant_id = d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🚧 [Stub] WHERE attorney_id = 87756a2c-a398-43f2-889a-b8815684df71
 🚧 [Stub] WHERE is_active = true
 🚧 [Stub] SINGLE result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
 [useStandaloneAttorney] Manager not ready, will retry...
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 🚧 [Stub] Querying table: attorneys
 🚧 [Stub] SELECT * FROM attorneys
 🚧 [Stub] WHERE id = 87756a2c-a398-43f2-889a-b8815684df71
 🚧 [Stub] SINGLE result
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 [VapiConfig] Using SECRET key for server operations (server)
 [VapiConfig] Server key: 6734febc...
 [VapiMcpService] Got API key from vapiConfig module
 [VapiMcpService] Using SECRET key for server operations: 6734febc...
 [VapiMcpService] INFO: Connection attempt 1/3 {attempt: 1, maxAttempts: 3, apiKey: '6734f...'}
 [VapiMcpService] Attempting MCP connection to: /api/vapi-mcp-server
 [VapiMcpService] Using Streamable HTTP transport (recommended)
 [VapiMcpService] Using local MCP proxy at: /api/vapi-mcp-server
 [VapiMcpService] Successfully configured to use local MCP proxy
 [VapiMcpService] Getting assistant: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [VapiMcpService] Error getting assistant: 
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:588
await in getAssistant
syncAssistantNameFromVapi @ src/services/assista…t=*************:188
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:72
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:57
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [VapiMcpService] Returning mock assistant due to error
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:595
await in getAssistant
syncAssistantNameFromVapi @ src/services/assista…t=*************:188
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:72
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:57
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Manager not ready, will retry...
 [AssistantDataService] Could not sync assistant name from Vapi: 
overrideMethod @ installHook.js:1
syncAssistantNameFromVapi @ src/services/assista…t=*************:202
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:72
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:57
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', subdomain: null, assistantName: null}
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
Navigated to http://localhost:5174/dashboard
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 [vite] connecting...
 [vite] connected.
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=*************:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=*************:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=*************:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=*************:27 🔧 [Supabase] EMERGENCY: Bypassing broken Supabase client, using stub only
supabase.js?t=*************:28 🔧 [Supabase] Headers/fetch environment is corrupted by browser extension or hook
mockSupabase.js:3 🚧 Creating stub Supabase client for fallback
AuthContext.jsx:485 Using real authentication in all environments
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
mockSupabase.js:14 🚧 [Stub] Auth state change listener registered
mockSupabase.js:14 🚧 [Stub] Auth state change listener registered
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
index.ts:5 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AttorneyProfileManager.js?t=*************:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
mockSupabase.js:19 🚧 [Stub] Querying table: attorneys
mockSupabase.js:24 🚧 [Stub] SELECT * FROM attorneys
mockSupabase.js:28 🚧 [Stub] WHERE id = 87756a2c-a398-43f2-889a-b8815684df71
mockSupabase.js:40 🚧 [Stub] SINGLE result
AttorneyProfileManager.js?t=*************:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
