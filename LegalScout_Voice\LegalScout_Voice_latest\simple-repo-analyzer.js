#!/usr/bin/env node

/**
 * Simple Repository Analyzer
 * Writes results directly to files to avoid console issues
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const log = (message) => {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}\n`;
  fs.appendFileSync('analysis.log', logEntry);
};

const writeResult = (filename, data) => {
  fs.writeFileSync(filename, JSON.stringify(data, null, 2));
};

async function analyzeRepo() {
  const results = {
    timestamp: new Date().toISOString(),
    status: 'starting',
    steps: [],
    findings: {
      newComponents: [],
      newServices: [],
      configChanges: [],
      recommendations: []
    }
  };

  try {
    log('Starting repository analysis...');
    results.steps.push('Analysis started');

    // Step 1: Test Git availability
    log('Testing Git...');
    const gitVersion = execSync('git --version', { encoding: 'utf8' }).trim();
    results.steps.push(`Git available: ${gitVersion}`);

    // Step 2: Clone the other LegalScout repo
    const repoUrl = 'https://github.com/damonkost/LegalScout.git';
    const tempDir = '.temp-analysis';
    
    log(`Cloning ${repoUrl}...`);
    
    // Clean up existing temp directory
    if (fs.existsSync(tempDir)) {
      execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
    }

    // Clone repository
    execSync(`git clone --depth 1 "${repoUrl}" "${tempDir}"`, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    results.steps.push('Repository cloned successfully');
    log('Clone completed');

    // Step 3: Analyze directory structure
    log('Analyzing directory structure...');
    
    const otherRepoStructure = analyzeDirectoryStructure(tempDir);
    const currentRepoStructure = analyzeDirectoryStructure('.');
    
    results.otherRepo = otherRepoStructure;
    results.currentRepo = currentRepoStructure;
    
    // Step 4: Find new components
    log('Finding new components...');
    
    const otherComponents = findJSFiles(path.join(tempDir, 'src/components'));
    const currentComponents = findJSFiles('src/components');
    
    const newComponents = otherComponents.filter(comp => 
      !currentComponents.some(curr => path.basename(curr) === path.basename(comp))
    );
    
    results.findings.newComponents = newComponents.map(comp => ({
      name: path.basename(comp),
      path: comp,
      analysis: analyzeFile(comp)
    }));
    
    log(`Found ${newComponents.length} new components`);

    // Step 5: Find new services
    log('Finding new services...');
    
    const otherServices = findJSFiles(path.join(tempDir, 'src/services'));
    const currentServices = findJSFiles('src/services');
    
    const newServices = otherServices.filter(service => 
      !currentServices.some(curr => path.basename(curr) === path.basename(service))
    );
    
    results.findings.newServices = newServices.map(service => ({
      name: path.basename(service),
      path: service,
      analysis: analyzeFile(service)
    }));
    
    log(`Found ${newServices.length} new services`);

    // Step 6: Compare package.json
    log('Comparing package.json...');
    
    const otherPackage = readPackageJson(path.join(tempDir, 'package.json'));
    const currentPackage = readPackageJson('package.json');
    
    if (otherPackage && currentPackage) {
      const newDeps = findNewDependencies(otherPackage, currentPackage);
      results.findings.configChanges.push({
        file: 'package.json',
        newDependencies: newDeps
      });
      log(`Found ${newDeps.length} new dependencies`);
    }

    // Step 7: Generate recommendations
    log('Generating recommendations...');
    
    const highValueComponents = results.findings.newComponents.filter(comp => 
      comp.analysis.score >= 3
    );
    
    const highValueServices = results.findings.newServices.filter(service => 
      service.analysis.score >= 3
    );
    
    results.findings.recommendations = [
      ...highValueComponents.map(comp => ({
        type: 'component',
        name: comp.name,
        reason: 'High-value component with modern patterns',
        action: 'Consider copying to current repo'
      })),
      ...highValueServices.map(service => ({
        type: 'service',
        name: service.name,
        reason: 'High-value service with modern patterns',
        action: 'Consider copying to current repo'
      }))
    ];

    // Step 8: Cleanup
    log('Cleaning up...');
    execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
    
    results.status = 'completed';
    results.steps.push('Analysis completed successfully');
    
    log('Analysis completed successfully');

  } catch (error) {
    log(`Error: ${error.message}`);
    results.status = 'error';
    results.error = error.message;
    results.steps.push(`Error: ${error.message}`);
  }

  // Write final results
  writeResult('repo-analysis-results.json', results);
  log('Results written to repo-analysis-results.json');
  
  return results;
}

function analyzeDirectoryStructure(dirPath) {
  const structure = {};
  
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    structure.directories = items
      .filter(item => item.isDirectory() && !item.name.startsWith('.'))
      .map(item => item.name);
    
    structure.files = items
      .filter(item => item.isFile())
      .map(item => item.name);
      
  } catch (error) {
    structure.error = error.message;
  }
  
  return structure;
}

function findJSFiles(dirPath) {
  const files = [];
  
  if (!fs.existsSync(dirPath)) {
    return files;
  }
  
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item.name);
      
      if (item.isFile() && (item.name.endsWith('.js') || item.name.endsWith('.jsx'))) {
        files.push(fullPath);
      } else if (item.isDirectory() && !item.name.startsWith('.')) {
        files.push(...findJSFiles(fullPath));
      }
    });
  } catch (error) {
    log(`Error reading directory ${dirPath}: ${error.message}`);
  }
  
  return files;
}

function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const analysis = {
      size: content.length,
      lines: content.split('\n').length,
      hasReactHooks: /use[A-Z]\w+/.test(content),
      hasVapi: /vapi|Vapi/.test(content),
      hasAssistant: /assistant|Assistant/.test(content),
      hasMCP: /mcp|MCP/.test(content),
      hasService: /Service|service/.test(content),
      hasAsync: /async|await/.test(content),
      score: 0
    };
    
    // Calculate score
    if (analysis.hasReactHooks) analysis.score += 2;
    if (analysis.hasVapi) analysis.score += 3;
    if (analysis.hasAssistant) analysis.score += 2;
    if (analysis.hasMCP) analysis.score += 3;
    if (analysis.hasService) analysis.score += 1;
    if (analysis.hasAsync) analysis.score += 1;
    
    return analysis;
    
  } catch (error) {
    return { error: error.message, score: 0 };
  }
}

function readPackageJson(filePath) {
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch (error) {
    return null;
  }
}

function findNewDependencies(otherPackage, currentPackage) {
  const otherDeps = { ...otherPackage.dependencies, ...otherPackage.devDependencies };
  const currentDeps = { ...currentPackage.dependencies, ...currentPackage.devDependencies };
  
  return Object.keys(otherDeps).filter(dep => !currentDeps[dep]);
}

// Run the analysis
analyzeRepo().then(results => {
  // Create a simple summary file
  const summary = {
    status: results.status,
    timestamp: results.timestamp,
    newComponentsCount: results.findings?.newComponents?.length || 0,
    newServicesCount: results.findings?.newServices?.length || 0,
    recommendationsCount: results.findings?.recommendations?.length || 0,
    highValueItems: results.findings?.recommendations?.map(r => r.name) || []
  };
  
  writeResult('analysis-summary.json', summary);
  
  // Also write a simple text summary
  const textSummary = `
REPOSITORY ANALYSIS SUMMARY
===========================
Status: ${summary.status}
Timestamp: ${summary.timestamp}

New Components Found: ${summary.newComponentsCount}
New Services Found: ${summary.newServicesCount}
Recommendations: ${summary.recommendationsCount}

High-Value Items:
${summary.highValueItems.map(item => `- ${item}`).join('\n')}

See repo-analysis-results.json for detailed findings.
`;
  
  fs.writeFileSync('analysis-summary.txt', textSummary);
  
}).catch(error => {
  fs.writeFileSync('analysis-error.txt', `Analysis failed: ${error.message}`);
});
