/**
 * Dashboard Sync Status
 *
 * This component displays the synchronization status in the dashboard
 * and provides buttons to check consistency and sync the attorney profile.
 *
 * Enhanced to use the AttorneyProfileManager for more robust synchronization.
 */

import React, { useEffect, useRef, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSync } from '../contexts/SyncContext';
import { useAttorneyProfile } from '../hooks/useAttorneyProfile';
import SyncStatus from './SyncStatus';
import './SyncStatus.css';

/**
 * Dashboard Sync Status Component
 *
 * @param {Object} props - Component props
 * @param {boolean} props.compact - Whether to display in compact mode for header
 * @returns {JSX.Element} The component
 */
const DashboardSyncStatus = ({ compact = false }) => {
  const { attorney: authAttorney, user, isLoading: authIsLoading } = useAuth();
  const { checkConsistency, syncProfile, isSyncing: contextIsSyncing, syncStatus: contextSyncStatus, lastSyncTime: contextLastSyncTime } = useSync();

  // Use our enhanced attorney profile hook
  const {
    attorney: enhancedAttorney,
    loading,
    error,
    syncStatus: enhancedSyncStatus,
    lastSyncTime: enhancedLastSyncTime,
    forceSync,
    checkSyncStatus
  } = useAttorneyProfile();

  // Use the enhanced values if available, otherwise fall back to context values
  const attorney = enhancedAttorney || authAttorney;
  const isSyncing = loading || contextIsSyncing;
  const syncStatus = enhancedSyncStatus || contextSyncStatus;
  const lastSyncTime = enhancedLastSyncTime || contextLastSyncTime;

  const initialCheckDone = useRef(false);
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);

  // Check consistency only once when attorney changes
  useEffect(() => {
    // Only check consistency if we have an attorney ID and haven't checked yet
    if (attorney?.id && !initialCheckDone.current) {
      console.log('Performing initial consistency check for attorney:', attorney.id);
      initialCheckDone.current = true;

      checkConsistency(attorney.id).catch(error => {
        console.error('Error checking consistency:', error);
      });
    }
  }, [attorney?.id, checkConsistency]);

  // Function to handle profile creation or linking
  const handleCreateProfile = async () => {
    // Set loading state
    setIsCreatingProfile(true);

    try {
      // Log the current auth state to debug
      console.log('Auth state when creating profile:', {
        hasUser: !!user,
        hasAttorney: !!attorney,
        userEmail: user?.email,
        userId: user?.id
      });

      // Get the current user directly from the hook state
      const currentUser = user;

      if (!currentUser || !currentUser.email) {
        console.error('Cannot create profile: No authenticated user found or email missing');
        alert('Unable to create profile: You are not properly authenticated. Please try logging out and logging in again.');
        return;
      }

      // Use the current user for the rest of the function
      const activeUser = currentUser;

      console.log('Attempting to find or create attorney profile for user:', activeUser.email);

      // Get email and name from user object
      const userEmail = activeUser.email || activeUser.user_metadata?.email || '';
      const userName = activeUser.user_metadata?.full_name ||
                      activeUser.user_metadata?.name ||
                      activeUser.user_metadata?.user_name ||
                      'Attorney';

      // Generate a subdomain from firm name or email
      const generateSubdomain = (firmName = null) => {
        if (firmName) {
          return firmName
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
            .substring(0, 30); // Limit length
        }

        if (!userEmail) return 'default';
        const emailPrefix = userEmail.split('@')[0];
        return emailPrefix.toLowerCase().replace(/[^a-z0-9]/g, '');
      };

      // Get Supabase client
      const supabase = window.supabase;

      console.log('DashboardSyncStatus: window.supabase is', window.supabase ? 'defined' : 'undefined');

      if (!supabase) {
        console.error('Supabase client not available');
        return;
      }

      // First, try to find an existing attorney record by email (case insensitive)
      console.log('Searching for existing attorney record with email:', userEmail);
      const { data: existingAttorneys, error: searchError } = await supabase
        .from('attorneys')
        .select('*')
        .ilike('email', userEmail);

      if (searchError) {
        console.error('Error searching for existing attorney:', searchError);
      }

      // If we found existing records
      if (existingAttorneys && existingAttorneys.length > 0) {
        console.log('Found existing attorney records:', existingAttorneys.length);

        // Use the first record found
        const existingAttorney = existingAttorneys[0];
        console.log('Using existing attorney record:', existingAttorney);

        // Update the auth_id/user_id to match the current user
        const { data: updatedAttorney, error: updateError } = await supabase
          .from('attorneys')
          .update({
            user_id: activeUser.id,
            updated_at: new Date()
          })
          .eq('id', existingAttorney.id)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating existing attorney record:', updateError);
        } else {
          console.log('Successfully linked attorney record to current user:', updatedAttorney);

          // Refresh the page to load the updated profile
          window.location.reload();
          return;
        }
      }

      // If no existing record found or update failed, create a new one
      console.log('Creating new attorney record for:', userEmail);
      const firmName = `${userName}'s Law Firm`;
      const { data: newAttorney, error } = await supabase
        .from('attorneys')
        .insert([
          {
            email: userEmail,
            name: userName,
            firm_name: firmName,
            is_active: true,
            user_id: activeUser.id,
            subdomain: generateSubdomain(firmName),
            vapi_instructions: `You are a legal assistant for ${userName}. Help potential clients understand their legal needs and collect relevant information for consultation.`
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error creating attorney profile:', error);

        // If creation failed, it might be due to a unique constraint
        // Try one more approach - find by subdomain
        const subdomain = generateSubdomain(firmName);
        console.log('Trying to find by subdomain:', subdomain);

        const { data: subdomainAttorneys, error: subdomainError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('subdomain', subdomain);

        if (subdomainError) {
          console.error('Error searching by subdomain:', subdomainError);
          return;
        }

        if (subdomainAttorneys && subdomainAttorneys.length > 0) {
          const subdomainAttorney = subdomainAttorneys[0];
          console.log('Found attorney by subdomain:', subdomainAttorney);

          // Update the auth_id/user_id to match the current user
          const { data: updatedAttorney, error: updateError } = await supabase
            .from('attorneys')
            .update({
              user_id: activeUser.id,
              email: userEmail, // Update email to match current user
              updated_at: new Date()
            })
            .eq('id', subdomainAttorney.id)
            .select()
            .single();

          if (updateError) {
            console.error('Error updating attorney by subdomain:', updateError);
          } else {
            console.log('Successfully linked attorney record by subdomain:', updatedAttorney);

            // Refresh the page to load the updated profile
            window.location.reload();
            return;
          }
        }

        return;
      }

      console.log('Successfully created attorney profile:', newAttorney);

      // Refresh the page to load the new profile
      window.location.reload();
    } catch (error) {
      console.error('Error in handleCreateProfile:', error);
      alert(`Error creating profile: ${error.message || 'Unknown error'}`);
    } finally {
      // Reset loading state after a short delay to ensure UI updates
      setTimeout(() => {
        setIsCreatingProfile(false);
      }, 500);
    }
  };

  if (!attorney) {
    return (
      <div className="dashboard-sync-status">
        <div className="sync-status-error">
          <h3>No Attorney Profile Found</h3>
          <p>We couldn't find your attorney profile linked to your current login. This can happen if:</p>
          <ul>
            <li>This is your first time logging in with this account</li>
            <li>You previously created a profile with a different email or login method</li>
            <li>Your account exists but isn't properly linked to your current login</li>
          </ul>
          <p><strong>Don't worry!</strong> Clicking the button below will:</p>
          <ol>
            <li>Look for any existing profiles that match your email</li>
            <li>Link your current login to that profile if found</li>
            <li>Create a new profile only if no matching profile exists</li>
          </ol>
          <button
            className="primary-button"
            onClick={handleCreateProfile}
            disabled={authIsLoading || !user || !user.email || isCreatingProfile}
          >
            {authIsLoading ? 'Checking Auth...' : (isCreatingProfile ? 'Processing...' : 'Link or Create My Profile')}
          </button>
        </div>
      </div>
    );
  }

  // If compact mode is enabled, render a simplified version for the header
  if (compact) {
    return (
      <div className="dashboard-sync-status compact">
        <div className="compact-sync-status">
          <div className="sync-status-indicator">
            <span className={`status-dot ${syncStatus.consistent ? 'status-ok' : 'status-error'}`}></span>
            <span className="sync-status-text">
              {syncStatus.consistent ? 'Synchronized' : 'Out of Sync'}
            </span>
          </div>
          <div className="compact-sync-actions">
            <button
              onClick={() => {
                if (enhancedAttorney) {
                  checkSyncStatus();
                } else if (attorney?.id) {
                  checkConsistency(attorney.id);
                }
              }}
              disabled={isSyncing}
              className="compact-sync-button"
              title="Check Consistency"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 12a9 9 0 0 1-9 9"></path>
                <path d="M3 12a9 9 0 0 1 9-9"></path>
                <path d="M21 12c0-4.97-4.03-9-9-9"></path>
                <path d="M3 12c0 4.97 4.03 9 9 9"></path>
              </svg>
            </button>
            <button
              onClick={() => {
                if (enhancedAttorney) {
                  forceSync();
                } else if (attorney?.id) {
                  syncProfile(attorney.id, true);
                }
              }}
              disabled={isSyncing}
              className="compact-sync-button"
              title="Force Sync"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 2v6h-6"></path>
                <path d="M3 12a9 9 0 0 1 15-6.7l3-3"></path>
                <path d="M3 22v-6h6"></path>
                <path d="M21 12a9 9 0 0 1-15 6.7l-3 3"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Regular full-size version
  return (
    <div className="dashboard-sync-status">
      <SyncStatus attorneyId={attorney.id} />
    </div>
  );
};

export default DashboardSyncStatus;
