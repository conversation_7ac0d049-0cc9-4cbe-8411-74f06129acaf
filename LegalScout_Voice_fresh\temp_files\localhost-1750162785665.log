Navigated to http://localhost:5179/dashboard
dashboard:28 🚀 [LegalScout] Initializing environment...
dashboard:50 ✅ [LegalScout] Environment initialized
dashboard:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
chunk-Q72EVS5P.js?v=9711cfb6:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
standaloneAttorneyManagerFix.js:143 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
standaloneAttorneyManagerFix.js:122 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js:130 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:138 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
standaloneAttorneyManagerFix.js:158 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
main.jsx:28 🚀 [LegalScout] Starting React app...
main.jsx:44 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:59 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:90 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
App.jsx:604 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
App.jsx:604 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:619 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:629 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:649 🔍 [App] Subdomain detected: default
App.jsx:657 🏠 [App] Localhost detected - treating as main domain
App.jsx:712 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:619 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:629 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:649 🔍 [App] Subdomain detected: default
App.jsx:657 🏠 [App] Localhost detected - treating as main domain
App.jsx:712 🏁 [App] Initialization complete
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:31.651Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:31.652Z'}
chunk-Q72EVS5P.js?v=9711cfb6:521 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    at form
    at div
    at WebsiteImporter (http://localhost:5179/src/components/dashboard/WebsiteImporter.jsx:23:28)
    at form
    at div
    at ProfileTab (http://localhost:5179/src/components/dashboard/ProfileTab.jsx:31:23)
    at div
    at div
    at div
    at div
    at DashboardNew (http://localhost:5179/src/pages/DashboardNew.jsx:48:55)
    at RenderedRoute (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:5722:26)
    at Routes (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6454:3)
    at main
    at div
    at AssistantAwareProvider (http://localhost:5179/src/contexts/AssistantAwareContext.jsx:30:42)
    at App (http://localhost:5179/src/App.jsx?t=1750162635885:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5179/src/contexts/AuthContext.jsx:43:32)
    at InnerAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:20:30)
    at Provider (http://localhost:5179/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5179/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:28:29)
    at AttorneyStateProvider (http://localhost:5179/src/contexts/AttorneyStateContext.jsx:31:41)
    at ThemeProvider (http://localhost:5179/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5179/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5179/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=9711cfb6:521
error @ chunk-Q72EVS5P.js?v=9711cfb6:505
validateDOMNesting @ chunk-Q72EVS5P.js?v=9711cfb6:8267
createInstance @ chunk-Q72EVS5P.js?v=9711cfb6:8339
completeWork @ chunk-Q72EVS5P.js?v=9711cfb6:16311
completeUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19252
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19234
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:19:31.734Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] 🔄 Loading attorney for user...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
 [DashboardNew] fetchAttorneyData called.
 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
 [DashboardNew] 🛡️ Checking for robust state handler...
 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:282
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] 🔄 Using fallback attorney loading logic...
 [StandaloneAttorneyManagerFix] Loading attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AttorneyProfileManager] Loading attorney by userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AttorneyProfileManager] Error loading attorney by userId: 
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:19:31.743Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] 🔄 Loading attorney for user...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
 [DashboardNew] fetchAttorneyData called.
 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
 [DashboardNew] 🛡️ Checking for robust state handler...
 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:282
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] 🔄 Using fallback attorney loading logic...
 [StandaloneAttorneyManagerFix] Loading attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AttorneyProfileManager] Loading attorney by userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AttorneyProfileManager] Error loading attorney by userId: 
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 [StandaloneAttorneyManagerFix] Error loading attorney: 
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [StandaloneAttorneyManagerFix] Error loading attorney: 
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Error loading attorney for user: 
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Error loading attorney for user: 
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:286
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] Error loading attorney by user ID: 
fetchAttorneyData @ DashboardNew.jsx:288
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
 [DashboardNew] Error loading attorney by user ID: 
fetchAttorneyData @ DashboardNew.jsx:288
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:359
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:31.818Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:31.818Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-2f157a27.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 [VeryCoolAssistants] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:19:32.038Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] ✅ Attorney already loaded: LegalScout
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-2f157a27.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
supabase.js:150 ✅ [Supabase-Fixed] Client test passed
supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.114Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.117Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.119Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.121Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:28 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:50 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:28 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:50 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'Loading assistant URL...', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.358Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.359Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.417Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:32.418Z'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
vapiAssistantUtils.js:169 Found attorney by email: 87756a2c-a398-43f2-889a-b8815684df71
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:412 [DashboardNew] Error finding attorney by email: TypeError: window.standaloneAttorneyManager.saveToLocalStorage is not a function
    at fetchAttorneyData (DashboardNew.jsx:407:50)
fetchAttorneyData @ DashboardNew.jsx:412
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
vapiAssistantUtils.js:169 Found attorney by email: 87756a2c-a398-43f2-889a-b8815684df71
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:412 [DashboardNew] Error finding attorney by email: TypeError: window.standaloneAttorneyManager.saveToLocalStorage is not a function
    at fetchAttorneyData (DashboardNew.jsx:407:50)
fetchAttorneyData @ DashboardNew.jsx:412
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
standaloneAttorneyManagerFix.js:143 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
standaloneAttorneyManagerFix.js:122 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js:130 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:138 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
standaloneAttorneyManagerFix.js:158 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
main.jsx:28 🚀 [LegalScout] Starting React app...
main.jsx:44 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:59 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:90 ✅ [LegalScout] React app rendered successfully
supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🔧 [StandaloneAttorneyManagerFix] Initializing...
 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
main.jsx:28 🚀 [LegalScout] Starting React app...
main.jsx:44 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:59 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:90 ✅ [LegalScout] React app rendered successfully
DashboardNew.jsx:1587 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
SimplePreviewPage.jsx:86 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:87 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:110 SimplePreviewPage: Loading assistant config for subdomain: damon
SimplePreviewPage.jsx:59 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:79 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:604 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:35.813Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:35.814Z'}
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
AssistantAwareContext.jsx:76 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: damon
simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: damon
simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:619 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:629 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:649 🔍 [App] Subdomain detected: default
App.jsx:657 🏠 [App] Localhost detected - treating as main domain
App.jsx:712 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:619 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:629 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:649 🔍 [App] Subdomain detected: default
App.jsx:657 🏠 [App] Localhost detected - treating as main domain
App.jsx:712 🏁 [App] Initialization complete
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
AssistantAwareContext.jsx:76 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:35.987Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:35.988Z'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.016Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.018Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.019Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.022Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 [DashboardNew] Using fallback iframe communication
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 [DashboardNew] Using fallback iframe communication
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.231Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.232Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 [DashboardNew] Using fallback iframe communication
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.350Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.351Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [VeryCoolAssistants] Loaded assistants from centralized service: (2) [{…}, {…}]
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.507Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.507Z'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.522Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.524Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.525Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.527Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.603Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.604Z'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.624Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.626Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.627Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:36.630Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
supabase.js:150 ✅ [Supabase-Fixed] Client test passed
supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/jpeg;base64,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
 primaryColor: #10b981
 secondaryColor: #059669
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.155Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.157Z'}
 [DashboardNew] Using fallback iframe communication
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.375Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.375Z'}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
SimplePreviewPage.jsx:69 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
SimplePreviewPage.jsx:115 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
SimplePreviewPage.jsx:151 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
SimplePreviewPage.jsx:163 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
supabase.js:150 ✅ [Supabase-Fixed] Client test passed
supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:109 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
AssistantAwareContext.jsx:364 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
AssistantAwareContext.jsx:389 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.600Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.601Z'}
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.624Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.626Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.626Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.628Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
SimplePreviewPage.jsx:69 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
SimplePreviewPage.jsx:115 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
SimplePreviewPage.jsx:151 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
SimplePreviewPage.jsx:163 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.872Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.873Z'}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.900Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:37.901Z'}
 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.2f157a27-067c-439e-823c-f0a2bbdd66e0 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.1d3471b7-8694-4844-b3ef-e05720693efc 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:38.140Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:19:38.141Z'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Error loading stats for assistant 2f157a27-067c-439e-823c-f0a2bbdd66e0: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:102
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:81
loadAssistants @ VeryCoolAssistants.jsx:72
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Error loading stats for assistant 1d3471b7-8694-4844-b3ef-e05720693efc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:102
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:81
loadAssistants @ VeryCoolAssistants.jsx:72
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
AssistantAwareContext.jsx:153 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
AssistantAwareContext.jsx:153 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
DashboardNew.jsx:480 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:480
setTimeout
(anonymous) @ DashboardNew.jsx:478
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
