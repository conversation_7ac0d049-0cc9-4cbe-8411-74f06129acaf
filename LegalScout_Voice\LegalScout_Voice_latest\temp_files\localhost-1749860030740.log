dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
dashboard:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=1749844974250:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749844974250:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749844974250:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
hook.js:608 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>. Error Component Stack
    at form (<anonymous>)
    at div (<anonymous>)
    at WebsiteImporter (WebsiteImporter.jsx:11:28)
    at form (<anonymous>)
    at div (<anonymous>)
    at ProfileTab (ProfileTab.jsx:21:23)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:38:34)
    at RenderedRoute (react-router-dom.js?v=7a56b667:5722:26)
    at Routes (react-router-dom.js?v=7a56b667:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at AssistantAwareProvider (AssistantAwareContext.jsx:22:42)
    at App (App.jsx:389:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at ThemeProvider (ThemeContext.jsx:27:33)
    at Router (react-router-dom.js?v=7a56b667:6397:13)
    at BrowserRouter (react-router-dom.js?v=7a56b667:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
printWarning @ chunk-Q72EVS5P.js?v=7a56b667:521
error @ chunk-Q72EVS5P.js?v=7a56b667:505
validateDOMNesting @ chunk-Q72EVS5P.js?v=7a56b667:8267
createInstance @ chunk-Q72EVS5P.js?v=7a56b667:8339
completeWork @ chunk-Q72EVS5P.js?v=7a56b667:16311
completeUnitOfWork @ chunk-Q72EVS5P.js?v=7a56b667:19252
performUnitOfWork @ chunk-Q72EVS5P.js?v=7a56b667:19234
workLoopSync @ chunk-Q72EVS5P.js?v=7a56b667:19165
renderRootSync @ chunk-Q72EVS5P.js?v=7a56b667:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=7a56b667:18706
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
ProfileTab.jsx:55 Attorney object in ProfileTab: null
ProfileTab.jsx:56 User object in ProfileTab: null
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:102 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:133 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:177 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:234 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:235 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:486 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: false)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
App.jsx:580 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=1749844974250:29 Using stub client for preview mode to prevent production initialization errors
mockSupabase.js?t=1749830541845:3 🚧 Creating stub Supabase client for fallback
AuthContext.jsx:485 Using real authentication in all environments
ProfileTab.jsx:55 Attorney object in ProfileTab: null
ProfileTab.jsx:56 User object in ProfileTab: null
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:133 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:177 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:234 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:235 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:486 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: false)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
App.jsx:580 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
mockSupabase.js?t=1749830541845:14 🚧 [Stub] Auth state change listener registered
mockSupabase.js?t=1749830541845:14 🚧 [Stub] Auth state change listener registered
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:102 Using email from previewConfig or previous state: 
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:102 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
index.ts:5 Loaded contentScript
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:595 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:605 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:625 🔍 [App] Subdomain detected: default
App.jsx:633 🏠 [App] Localhost detected - treating as main domain
App.jsx:676 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:595 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:605 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:625 🔍 [App] Subdomain detected: default
App.jsx:633 🏠 [App] Localhost detected - treating as main domain
App.jsx:676 🏁 [App] Initialization complete
EnhancedAssistantDropdown.jsx:75 No OAuth user email available Error Component Stack
    at EnhancedAssistantDropdown (EnhancedAssistantDropdown.jsx:15:3)
    at div (<anonymous>)
    at header (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:38:34)
    at RenderedRoute (react-router-dom.js?v=7a56b667:5722:26)
    at Routes (react-router-dom.js?v=7a56b667:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at AssistantAwareProvider (AssistantAwareContext.jsx:22:42)
    at App (App.jsx:389:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at ThemeProvider (ThemeContext.jsx:27:33)
    at Router (react-router-dom.js?v=7a56b667:6397:13)
    at BrowserRouter (react-router-dom.js?v=7a56b667:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
ProfileTab.jsx:55 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:56 User object in ProfileTab: null
VeryCoolAssistants.jsx:93 Error loading assistants: TypeError: supabase.from(...).select(...).eq(...).order is not a function
    at loadAssistants (VeryCoolAssistants.jsx:40:10)
    at VeryCoolAssistants.jsx:23:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=7a56b667:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=7a56b667:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=7a56b667:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=7a56b667:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=7a56b667:18137:11)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=7a56b667:19518:11)
    at flushPassiveEffects (chunk-Q72EVS5P.js?v=7a56b667:19475:22)
    at chunk-Q72EVS5P.js?v=7a56b667:19356:17 Error Component Stack
    at VeryCoolAssistants (VeryCoolAssistants.jsx:11:31)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:38:34)
    at RenderedRoute (react-router-dom.js?v=7a56b667:5722:26)
    at Routes (react-router-dom.js?v=7a56b667:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at AssistantAwareProvider (AssistantAwareContext.jsx:22:42)
    at App (App.jsx:389:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at ThemeProvider (ThemeContext.jsx:27:33)
    at Router (react-router-dom.js?v=7a56b667:6397:13)
    at BrowserRouter (react-router-dom.js?v=7a56b667:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
loadAssistants @ VeryCoolAssistants.jsx:93
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
DashboardNew.jsx:133 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:649 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:650 [DashboardNew] Attorney Vapi Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedAssistantDropdown.jsx:75 No OAuth user email available
overrideMethod @ hook.js:608
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:187 [DashboardNew] 🛡️ No authenticated user found, skipping robust state handler
DashboardNew.jsx:187 [DashboardNew] 🛡️ No authenticated user found, skipping robust state handler
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
DashboardNew.jsx:680 [DashboardNew] Updated preview config with assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:680 [DashboardNew] Updated preview config with assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
EnhancedAssistantDropdown.jsx:198 Error loading assistant subdomains: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantSubdomains (EnhancedAssistantDropdown.jsx:182:10)
overrideMethod @ hook.js:608
loadAssistantSubdomains @ EnhancedAssistantDropdown.jsx:198
await in loadAssistantSubdomains
(anonymous) @ EnhancedAssistantDropdown.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
AssistantAwareContext.jsx:84 Error loading assistant data: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantData (AssistantAwareContext.jsx:59:10)
overrideMethod @ hook.js:608
loadAssistantData @ AssistantAwareContext.jsx:84
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
EnhancedAssistantDropdown.jsx:198 Error loading assistant subdomains: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantSubdomains (EnhancedAssistantDropdown.jsx:182:10)
overrideMethod @ hook.js:608
loadAssistantSubdomains @ EnhancedAssistantDropdown.jsx:198
await in loadAssistantSubdomains
(anonymous) @ EnhancedAssistantDropdown.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:910 Found 0 iframes on the page
DashboardNew.jsx:964 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:964
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:899
(anonymous) @ DashboardNew.jsx:683
basicStateReducer @ chunk-Q72EVS5P.js?v=7a56b667:11723
updateReducer @ chunk-Q72EVS5P.js?v=7a56b667:11814
updateState @ chunk-Q72EVS5P.js?v=7a56b667:12041
useState @ chunk-Q72EVS5P.js?v=7a56b667:12773
useState @ chunk-2N3A5BUM.js?v=7a56b667:1066
DashboardNew @ DashboardNew.jsx:66
renderWithHooks @ chunk-Q72EVS5P.js?v=7a56b667:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=7a56b667:14602
beginWork @ chunk-Q72EVS5P.js?v=7a56b667:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=7a56b667:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=7a56b667:19226
workLoopSync @ chunk-Q72EVS5P.js?v=7a56b667:19165
renderRootSync @ chunk-Q72EVS5P.js?v=7a56b667:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=7a56b667:18706
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
DashboardNew.jsx:910 Found 0 iframes on the page
DashboardNew.jsx:964 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:964
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:899
(anonymous) @ DashboardNew.jsx:683
basicStateReducer @ chunk-Q72EVS5P.js?v=7a56b667:11723
updateReducer @ chunk-Q72EVS5P.js?v=7a56b667:11814
updateState @ chunk-Q72EVS5P.js?v=7a56b667:12041
useState @ chunk-Q72EVS5P.js?v=7a56b667:12773
useState @ chunk-2N3A5BUM.js?v=7a56b667:1066
DashboardNew @ DashboardNew.jsx:66
renderWithHooks @ chunk-Q72EVS5P.js?v=7a56b667:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=7a56b667:14607
beginWork @ chunk-Q72EVS5P.js?v=7a56b667:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=7a56b667:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=7a56b667:19226
workLoopSync @ chunk-Q72EVS5P.js?v=7a56b667:19165
renderRootSync @ chunk-Q72EVS5P.js?v=7a56b667:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=7a56b667:18706
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js?t=1749844974250:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
DashboardNew.jsx:476 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:476
setTimeout
(anonymous) @ DashboardNew.jsx:474
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
EnhancedAssistantDropdown.jsx:75 No OAuth user email available Error Component Stack
    at EnhancedAssistantDropdown (EnhancedAssistantDropdown.jsx:15:3)
    at div (<anonymous>)
    at header (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:38:34)
    at RenderedRoute (react-router-dom.js?v=7a56b667:5722:26)
    at Routes (react-router-dom.js?v=7a56b667:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at AssistantAwareProvider (AssistantAwareContext.jsx:22:42)
    at App (App.jsx:389:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at ThemeProvider (ThemeContext.jsx:27:33)
    at Router (react-router-dom.js?v=7a56b667:6397:13)
    at BrowserRouter (react-router-dom.js?v=7a56b667:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
ProfileTab.jsx:55 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:56 User object in ProfileTab: null
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:82 Updated form data with attorney email from database: <EMAIL>
VeryCoolAssistants.jsx:93 Error loading assistants: TypeError: supabase.from(...).select(...).eq(...).order is not a function
    at loadAssistants (VeryCoolAssistants.jsx:40:10)
    at VeryCoolAssistants.jsx:23:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=7a56b667:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=7a56b667:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=7a56b667:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=7a56b667:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=7a56b667:18137:11)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=7a56b667:19518:11)
    at flushPassiveEffects (chunk-Q72EVS5P.js?v=7a56b667:19475:22)
    at chunk-Q72EVS5P.js?v=7a56b667:19356:17 Error Component Stack
    at VeryCoolAssistants (VeryCoolAssistants.jsx:11:31)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:38:34)
    at RenderedRoute (react-router-dom.js?v=7a56b667:5722:26)
    at Routes (react-router-dom.js?v=7a56b667:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at AssistantAwareProvider (AssistantAwareContext.jsx:22:42)
    at App (App.jsx:389:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at ThemeProvider (ThemeContext.jsx:27:33)
    at Router (react-router-dom.js?v=7a56b667:6397:13)
    at BrowserRouter (react-router-dom.js?v=7a56b667:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
loadAssistants @ VeryCoolAssistants.jsx:93
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
EnhancedAssistantDropdown.jsx:75 No OAuth user email available
overrideMethod @ hook.js:608
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
ProfileTab.jsx:55 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:56 User object in ProfileTab: null
VeryCoolAssistants.jsx:93 Error loading assistants: TypeError: supabase.from(...).select(...).eq(...).order is not a function
    at loadAssistants (VeryCoolAssistants.jsx:40:10)
    at VeryCoolAssistants.jsx:23:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=7a56b667:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=7a56b667:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=7a56b667:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=7a56b667:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=7a56b667:19531:13)
    at flushPassiveEffects (chunk-Q72EVS5P.js?v=7a56b667:19475:22)
    at chunk-Q72EVS5P.js?v=7a56b667:19356:17
    at workLoop (chunk-Q72EVS5P.js?v=7a56b667:197:42)
overrideMethod @ hook.js:608
loadAssistants @ VeryCoolAssistants.jsx:93
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:82 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:71 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:82 Updated form data with attorney email from database: <EMAIL>
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:26 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:48 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:26 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:48 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
EnhancedAssistantDropdown.jsx:198 Error loading assistant subdomains: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantSubdomains (EnhancedAssistantDropdown.jsx:182:10)
overrideMethod @ hook.js:608
loadAssistantSubdomains @ EnhancedAssistantDropdown.jsx:198
await in loadAssistantSubdomains
(anonymous) @ EnhancedAssistantDropdown.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
EnhancedAssistantDropdown.jsx:198 Error loading assistant subdomains: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantSubdomains (EnhancedAssistantDropdown.jsx:182:10)
overrideMethod @ hook.js:608
loadAssistantSubdomains @ EnhancedAssistantDropdown.jsx:198
await in loadAssistantSubdomains
(anonymous) @ EnhancedAssistantDropdown.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
client.ts:19 [vite] connecting...
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
client.ts:155 [vite] connected.
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=1749844974250:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749844974250:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749844974250:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=1749844974250:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749844974250:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749844974250:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: damon
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
supabase.js?t=1749844974250:29 Using stub client for preview mode to prevent production initialization errors
mockSupabase.js?t=1749830541845:3 🚧 Creating stub Supabase client for fallback
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
App.jsx:580 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: damon
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
App.jsx:580 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
mockSupabase.js?t=1749830541845:14 🚧 [Stub] Auth state change listener registered
mockSupabase.js?t=1749830541845:14 🚧 [Stub] Auth state change listener registered
SimplePreviewPage.jsx:76 SimplePreviewPage: No attorney found with subdomain: damon
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:76
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
SimplePreviewPage.jsx:76 SimplePreviewPage: No attorney found with subdomain: damon
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:76
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
SimplePreviewPage.jsx:195 SimplePreviewPage: No attorney data found for subdomain: damon
loadConfig @ SimplePreviewPage.jsx:195
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
SimplePreviewPage.jsx:195 SimplePreviewPage: No attorney data found for subdomain: damon
loadConfig @ SimplePreviewPage.jsx:195
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
DashboardNew.jsx:1598 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 Using stub client for preview mode to prevent production initialization errors
 🚧 Creating stub Supabase client for fallback
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext] Starting auth initialization...
 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
 Using real authentication in all environments
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-12T00:10:52.08589+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext] Starting auth initialization...
 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
 Using real authentication in all environments
 🚧 [Stub] Auth state change listener registered
 🚧 [Stub] Auth state change listener registered
 SimplePreviewPage: No attorney found with subdomain: damon
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:71
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 SimplePreviewPage: No attorney found with subdomain: damon
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:71
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 SimplePreviewPage: No attorney data found for subdomain: damon
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 SimplePreviewPage: No attorney data found for subdomain: damon
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:595 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:605 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:625 🔍 [App] Subdomain detected: default
App.jsx:633 🏠 [App] Localhost detected - treating as main domain
App.jsx:676 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:595 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:605 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:625 🔍 [App] Subdomain detected: default
App.jsx:633 🏠 [App] Localhost detected - treating as main domain
App.jsx:676 🏁 [App] Initialization complete
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview'}
App.jsx:755 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:785 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
AssistantAwareContext.jsx:84 Error loading assistant data: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantData (AssistantAwareContext.jsx:59:10)
loadAssistantData @ AssistantAwareContext.jsx:84
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AssistantAwareContext.jsx:84 Error loading assistant data: TypeError: supabase.from(...).select(...).eq(...).eq is not a function
    at loadAssistantData (AssistantAwareContext.jsx:59:10)
loadAssistantData @ AssistantAwareContext.jsx:84
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:36
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:464 primaryColor: #2563eb
EnhancedPreviewNew.jsx:465 secondaryColor: #1e40af
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.
EnhancedPreviewNew.jsx:467 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useStandaloneAttorney] Manager not ready, will retry...
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:896 [DashboardNew] Using fallback iframe communication
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
DashboardNew.jsx:910 Found 2 iframes on the page
DashboardNew.jsx:923 Found preview iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:938 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:939 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:940 [DashboardNew] Assistant ID in config: f9b97d13-f9c4-40af-a660-62ba5925ff2a
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
Button.jsx:209 Button component received mascot URL: data:image/jpeg;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/jpeg;base64,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
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js?t=1749844974250:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js?t=1749844974250:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749844974250:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
