import React, { useState, useEffect } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import { useAuth } from './contexts/AuthContext'
import { AssistantAwareProvider } from './contexts/AssistantAwareContext'
import Dashboard from './pages/DashboardNew.jsx'
import AnimatedBackground from './components/AnimatedBackground.jsx'
import ThemeToggle from './components/ThemeToggle.jsx'
import Navbar from './components/Navbar.jsx'
import AuthOverlay from './components/AuthOverlay'
import AuthCallback from './pages/AuthCallback'
import SimpleCompleteProfile from './pages/SimpleCompleteProfile'
import LoginPage from './pages/LoginPage'

function App() {
  const location = useLocation();
  const { user } = useAuth();
  const [isDarkTheme, setIsDarkTheme] = useState(true);
  const [showAuthOverlay, setShowAuthOverlay] = useState(false);

  // Theme management
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');
    if (isDarkTheme) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [isDarkTheme]);

  const toggleTheme = () => {
    setIsDarkTheme(prev => !prev);
  };

  const handleAuthSuccess = () => {
    setShowAuthOverlay(false);
  };

  return (
    <AssistantAwareProvider>
      <div className="app-wrapper">
        <AnimatedBackground />
        <ToastContainer
          position="top-center"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme={isDarkTheme ? 'dark' : 'light'}
        />
        
        {/* Simplified header - always show except on dashboard */}
        {location.pathname !== '/dashboard' && (
          <header className="header">
            <div className="logo-container">
              <img src="/nav_logo.webp" alt="LegalScout Logo" className="logo" />
            </div>
            <Navbar isDarkTheme={isDarkTheme} />
            <ThemeToggle isDark={isDarkTheme} onToggle={toggleTheme} />
          </header>
        )}

        <main className="main-content-layer">
          <Routes>
            {/* Simplified routing - no complex subdomain logic */}
            <Route path="/" element={
              user ? <Navigate to="/dashboard" replace /> : (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100vh',
                  color: '#fff',
                  textAlign: 'center',
                  padding: '20px'
                }}>
                  <h1 style={{ marginBottom: '20px', fontSize: '2.5rem' }}>Welcome to LegalScout</h1>
                  <p style={{ marginBottom: '30px', fontSize: '1.2rem', maxWidth: '600px' }}>
                    Your AI-powered legal assistant platform. Connect with attorneys and get legal guidance.
                  </p>
                  <button 
                    onClick={() => setShowAuthOverlay(true)}
                    style={{
                      padding: '15px 30px',
                      fontSize: '1.1rem',
                      backgroundColor: '#3498db',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      cursor: 'pointer'
                    }}
                  >
                    Get Started
                  </button>
                </div>
              )
            } />
            
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/complete-profile" element={<SimpleCompleteProfile />} />
            
            {/* Test route to verify routing works */}
            <Route path="/test" element={
              <div style={{ padding: '20px', textAlign: 'center', color: '#fff' }}>
                <h1>✅ App is Working!</h1>
                <p>If you can see this, the app is functioning correctly.</p>
                <p>Current user: {user ? user.email : 'Not logged in'}</p>
                <button onClick={() => setShowAuthOverlay(true)}>
                  {user ? 'Switch Account' : 'Sign In'}
                </button>
              </div>
            } />
          </Routes>
        </main>

        {/* Auth Overlay */}
        <AuthOverlay
          isOpen={showAuthOverlay}
          onClose={() => setShowAuthOverlay(false)}
          onSuccess={handleAuthSuccess}
        />
      </div>
    </AssistantAwareProvider>
  );
}

export default App;
