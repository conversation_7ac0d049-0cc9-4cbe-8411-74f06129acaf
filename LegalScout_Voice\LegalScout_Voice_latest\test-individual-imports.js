/**
 * Test individual API imports to find the problematic one
 */

console.log('Testing individual imports...');

const imports = [
  './api/website-import.js',
  './api/debug-website-import.js'
];

for (const importPath of imports) {
  try {
    console.log(`Testing: ${importPath}`);
    const module = await import(importPath);
    console.log(`✅ ${importPath} - OK`);
  } catch (error) {
    console.log(`❌ ${importPath} - FAILED`);
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

console.log('Individual import test complete.');
