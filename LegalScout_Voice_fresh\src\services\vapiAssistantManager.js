/**
 * Vapi Assistant Manager Service
 *
 * This service provides robust management of Vapi assistants for attorneys,
 * ensuring consistent creation, updates, and synchronization between Supabase and Vapi.
 *
 * It follows the one-way sync pattern (UI → Supabase → Vapi) and uses Supabase
 * as the primary source of truth.
 */

import { supabase } from '../lib/supabase';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { mcpConfig } from '../config/mcp.config';

class VapiAssistantManager {
  constructor() {
    this.mcpClient = null;
    this.mcpConnected = false;
    this.directApiUrl = 'https://api.vapi.ai';
    this.directApiKey = null;
    this.useDirect = false;
  }

  /**
   * Initialize the Vapi Assistant Manager
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - Whether initialization was successful
   */
  async initialize(options = {}) {
    try {
      console.log('[VapiAssistantManager] Initializing');

      // Set direct API key if provided
      if (options.directApiKey) {
        this.directApiKey = options.directApiKey;
        this.useDirect = true;
        console.log('[VapiAssistantManager] Using direct API mode');
        return true;
      }

      // Otherwise use MCP
      this.mcpClient = new Client({
        name: 'legalscout-vapi-assistant-manager',
        version: '1.0.0',
      });

      // Connect to the MCP server
      const transport = new SSEClientTransport({
        url: options.mcpUrl || mcpConfig.voice.vapi.mcpUrl || '/vapi-mcp-server/sse',
      });

      await this.mcpClient.connect(transport);
      this.mcpConnected = true;

      console.log('[VapiAssistantManager] Initialized with MCP');
      return true;
    } catch (error) {
      console.error('[VapiAssistantManager] Initialization error:', error);
      return false;
    }
  }

  /**
   * Ensure an attorney has a valid Vapi assistant ID
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The updated attorney object
   */
  async ensureAssistant(attorney) {
    if (!attorney) {
      throw new Error('Attorney object is required');
    }

    try {
      console.log(`[VapiAssistantManager] Ensuring assistant for attorney: ${attorney.id}`);

      // Check if attorney already has a valid assistant ID
      if (attorney.vapi_assistant_id) {
        // Verify the assistant exists in Vapi
        const assistantExists = await this.verifyAssistant(attorney.vapi_assistant_id);

        if (assistantExists) {
          console.log(`[VapiAssistantManager] Assistant ${attorney.vapi_assistant_id} verified`);
          return attorney;
        }

        console.log(`[VapiAssistantManager] Assistant ${attorney.vapi_assistant_id} not found in Vapi, will create new one`);
      }

      // Create a new assistant
      const newAssistant = await this.createAssistant(attorney);

      // Update attorney record with new assistant ID
      const updatedAttorney = await this.updateAttorneyAssistantId(attorney.id, newAssistant.id);

      console.log(`[VapiAssistantManager] Created and linked new assistant ${newAssistant.id} for attorney ${attorney.id}`);
      return updatedAttorney;
    } catch (error) {
      console.error(`[VapiAssistantManager] Error ensuring assistant for attorney ${attorney.id}:`, error);
      throw error;
    }
  }

  /**
   * Verify that a Vapi assistant exists
   * @param {string} assistantId - The Vapi assistant ID to verify
   * @returns {Promise<boolean>} - Whether the assistant exists
   */
  async verifyAssistant(assistantId) {
    try {
      if (this.useDirect) {
        const response = await fetch(`${this.directApiUrl}/assistants/${assistantId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        return response.ok;
      }

      // Use MCP to get the assistant
      const response = await this.mcpClient.callTool({
        name: 'get_assistant_vapi-mcp-server',
        arguments: { assistantId }
      });

      return !!response;
    } catch (error) {
      console.error(`[VapiAssistantManager] Error verifying assistant ${assistantId}:`, error);
      return false;
    }
  }

  /**
   * Create a new Vapi assistant for an attorney
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The created assistant
   */
  async createAssistant(attorney) {
    try {
      // Create assistant configuration from attorney data
      const assistantConfig = this.createAssistantConfig(attorney);

      if (this.useDirect) {
        const response = await fetch(`${this.directApiUrl}/assistants`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(assistantConfig)
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
      }

      // Use MCP to create the assistant
      const response = await this.mcpClient.callTool({
        name: 'create_assistant_vapi-mcp-server',
        arguments: assistantConfig
      });

      return response;
    } catch (error) {
      console.error('[VapiAssistantManager] Error creating assistant:', error);
      throw error;
    }
  }

  /**
   * Update an attorney's Vapi assistant ID in Supabase
   * @param {string} attorneyId - The attorney ID
   * @param {string} assistantId - The Vapi assistant ID
   * @returns {Promise<Object>} - The updated attorney object
   */
  async updateAttorneyAssistantId(attorneyId, assistantId) {
    // CRITICAL: Prevent mock assistant IDs from being saved to database
    if (assistantId.includes('mock') || assistantId.includes('MOCK') || assistantId.includes('DO-NOT-SAVE')) {
      console.error(`[VapiAssistantManager] BLOCKED: Attempted to save mock assistant ID to database: ${assistantId}`);
      console.error('This is a critical error - mock IDs should NEVER be saved to the database');
      throw new Error('Mock assistant IDs cannot be saved to database');
    }

    // Validate that assistant ID looks like a real UUID (not a mock)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(assistantId)) {
      console.error(`[VapiAssistantManager] Invalid assistant ID format - must be a valid UUID: ${assistantId}`);
      throw new Error('Assistant ID must be a valid UUID');
    }

    try {
      const { data, error } = await supabase
        .from('attorneys')
        .update({
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error(`[VapiAssistantManager] Error updating attorney ${attorneyId} with assistant ID ${assistantId}:`, error);
      throw error;
    }
  }

  /**
   * Create an assistant configuration from attorney data
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Object} - The assistant configuration
   */
  createAssistantConfig(attorney) {
    return {
      name: attorney.firm_name || 'Legal Assistant',
      firstMessage: attorney.welcome_message || 'Hello, how can I help you with your legal needs today?',
      instructions: attorney.vapi_instructions || 'You are a legal assistant helping potential clients understand their legal needs.',
      voice: {
        provider: attorney.voice_provider || mcpConfig.voice.vapi.defaultProvider || '11labs',
        voiceId: attorney.voice_id || mcpConfig.voice.vapi.defaultVoice || 'sarah'
      },
      llm: {
        provider: "openai",
        model: attorney.ai_model || mcpConfig.assistant.defaultModel || "gpt-4o"
      },
      transcriber: {
        provider: mcpConfig.assistant.defaultTranscriber?.provider || "deepgram",
        model: mcpConfig.assistant.defaultTranscriber?.model || "nova-3"
      }
    };
  }

  /**
   * Sync an attorney's Vapi assistant with the latest data from Supabase
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The sync result
   */
  async syncAssistant(attorney) {
    try {
      if (!attorney.vapi_assistant_id) {
        return await this.ensureAssistant(attorney);
      }

      // Update the assistant with the latest data
      const assistantConfig = this.createAssistantConfig(attorney);

      if (this.useDirect) {
        const response = await fetch(`${this.directApiUrl}/assistants/${attorney.vapi_assistant_id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(assistantConfig)
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
      }

      // Use MCP to update the assistant
      const response = await this.mcpClient.callTool({
        name: 'update_assistant_vapi-mcp-server',
        arguments: {
          assistantId: attorney.vapi_assistant_id,
          ...assistantConfig
        }
      });

      return response;
    } catch (error) {
      console.error(`[VapiAssistantManager] Error syncing assistant for attorney ${attorney.id}:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const vapiAssistantManager = new VapiAssistantManager();
