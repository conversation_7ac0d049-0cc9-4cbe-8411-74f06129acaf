/**
 * Test script to verify the unified assistant data system
 * Run this in the browser console to test the centralized approach
 */

console.log('🧪 Testing Unified Assistant Data System...');

// Test 1: Check if centralized service is available
async function testCentralizedService() {
  console.log('\n📋 Test 1: Centralized Service Availability');
  
  try {
    const { AssistantDataService } = await import('./src/services/assistantDataService.js');
    console.log('✅ AssistantDataService imported successfully');
    
    // Check if service has the expected methods
    const expectedMethods = ['getAssistantsForAttorney', 'deleteAssistant', 'subscribe', 'notifySubscribers'];
    expectedMethods.forEach(method => {
      if (typeof AssistantDataService[method] === 'function') {
        console.log(`✅ Method ${method} is available`);
      } else {
        console.log(`❌ Method ${method} is missing`);
      }
    });
    
    return AssistantDataService;
  } catch (error) {
    console.error('❌ Failed to import AssistantDataService:', error);
    return null;
  }
}

// Test 2: Check component subscription
function testComponentSubscriptions() {
  console.log('\n📋 Test 2: Component Subscriptions');
  
  // Check if dropdown is in DOM
  const dropdown = document.querySelector('.enhanced-assistant-dropdown');
  if (dropdown) {
    console.log('✅ Enhanced assistant dropdown found in DOM');
  } else {
    console.log('❌ Enhanced assistant dropdown not found');
  }
  
  // Check if VeryCoolAssistants is in DOM
  const veryCoolAssistants = document.querySelector('.very-cool-assistants');
  if (veryCoolAssistants) {
    console.log('✅ VeryCoolAssistants component found in DOM');
  } else {
    console.log('❌ VeryCoolAssistants component not found');
  }
}

// Test 3: Simulate data change and check if components respond
async function testDataSynchronization() {
  console.log('\n📋 Test 3: Data Synchronization');
  
  try {
    const { AssistantDataService } = await import('./src/services/assistantDataService.js');
    
    // Subscribe to changes
    let notificationReceived = false;
    const unsubscribe = AssistantDataService.subscribe((eventType, data) => {
      console.log('📡 Received notification:', eventType, data);
      notificationReceived = true;
    });
    
    // Simulate a data change
    console.log('🔄 Simulating data change...');
    AssistantDataService.notifySubscribers('test_event', { test: 'data' });
    
    // Check if notification was received
    setTimeout(() => {
      if (notificationReceived) {
        console.log('✅ Subscription system working correctly');
      } else {
        console.log('❌ Subscription system not working');
      }
      unsubscribe();
    }, 100);
    
  } catch (error) {
    console.error('❌ Error testing data synchronization:', error);
  }
}

// Test 4: Check current assistant data consistency
async function testDataConsistency() {
  console.log('\n📋 Test 4: Data Consistency Check');
  
  // Get attorney from standalone manager
  const manager = window.standaloneAttorneyManager;
  if (!manager || !manager.attorney) {
    console.log('❌ No attorney data available in standalone manager');
    return;
  }
  
  const attorney = manager.attorney;
  console.log('👤 Current attorney:', attorney.id, attorney.firm_name);
  
  try {
    const { AssistantDataService } = await import('./src/services/assistantDataService.js');
    
    // Load assistants using centralized service
    const assistants = await AssistantDataService.getAssistantsForAttorney(attorney.id);
    console.log(`📋 Centralized service found ${assistants.length} assistants`);
    
    // Check dropdown state
    const dropdownAssistants = document.querySelectorAll('.enhanced-assistant-dropdown .dropdown-item:not(.create-new)');
    console.log(`🔽 Dropdown shows ${dropdownAssistants.length} assistants`);
    
    // Check VeryCoolAssistants state
    const assistantCards = document.querySelectorAll('.very-cool-assistants .assistant-card');
    console.log(`🎴 VeryCoolAssistants shows ${assistantCards.length} assistant cards`);
    
    // Check for consistency
    if (assistants.length === dropdownAssistants.length && assistants.length === assistantCards.length) {
      console.log('✅ All components show consistent data');
    } else {
      console.log('❌ Data inconsistency detected!');
      console.log('   - Centralized service:', assistants.length);
      console.log('   - Dropdown:', dropdownAssistants.length);
      console.log('   - VeryCoolAssistants:', assistantCards.length);
    }
    
    return { assistants, dropdownCount: dropdownAssistants.length, cardsCount: assistantCards.length };
    
  } catch (error) {
    console.error('❌ Error checking data consistency:', error);
  }
}

// Test 5: Test deletion simulation
async function testDeletionFlow() {
  console.log('\n📋 Test 5: Deletion Flow Simulation');
  
  try {
    const { AssistantDataService } = await import('./src/services/assistantDataService.js');
    
    // Subscribe to deletion events
    let deletionEventReceived = false;
    const unsubscribe = AssistantDataService.subscribe((eventType, data) => {
      if (eventType === 'assistant_deleted') {
        console.log('🗑️ Deletion event received:', data);
        deletionEventReceived = true;
      }
    });
    
    // Also listen for global events
    let globalEventReceived = false;
    const handleGlobalEvent = (event) => {
      console.log('🌍 Global deletion event received:', event.detail);
      globalEventReceived = true;
    };
    window.addEventListener('assistantDeleted', handleGlobalEvent);
    
    // Simulate deletion notification
    console.log('🔄 Simulating assistant deletion...');
    AssistantDataService.notifySubscribers('assistant_deleted', {
      assistantId: 'test-assistant-123',
      assistantName: 'Test Assistant',
      attorneyId: 'test-attorney-456'
    });
    
    // Check results
    setTimeout(() => {
      if (deletionEventReceived) {
        console.log('✅ Centralized deletion notification working');
      } else {
        console.log('❌ Centralized deletion notification failed');
      }
      
      if (globalEventReceived) {
        console.log('✅ Global deletion event working');
      } else {
        console.log('❌ Global deletion event failed');
      }
      
      unsubscribe();
      window.removeEventListener('assistantDeleted', handleGlobalEvent);
    }, 100);
    
  } catch (error) {
    console.error('❌ Error testing deletion flow:', error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running Unified Assistant System Tests...');
  
  const service = await testCentralizedService();
  if (!service) {
    console.log('❌ Cannot continue tests without centralized service');
    return;
  }
  
  testComponentSubscriptions();
  await testDataSynchronization();
  const consistencyResults = await testDataConsistency();
  await testDeletionFlow();
  
  console.log('\n✅ Test suite completed!');
  console.log('\n📝 Summary:');
  console.log('1. Centralized service should be available and have all methods');
  console.log('2. Both dropdown and VeryCoolAssistants should be in DOM');
  console.log('3. Subscription system should work for data changes');
  console.log('4. All components should show the same number of assistants');
  console.log('5. Deletion events should propagate to all subscribers');
  
  if (consistencyResults) {
    console.log('\n🔍 Current State:');
    console.log(`   - Assistants in database: ${consistencyResults.assistants.length}`);
    console.log(`   - Assistants in dropdown: ${consistencyResults.dropdownCount}`);
    console.log(`   - Assistants in cards: ${consistencyResults.cardsCount}`);
  }
  
  return consistencyResults;
}

// Auto-run tests
runAllTests();

// Export for manual testing
window.unifiedAssistantTests = {
  runAllTests,
  testCentralizedService,
  testComponentSubscriptions,
  testDataSynchronization,
  testDataConsistency,
  testDeletionFlow
};

console.log('\n💡 You can run individual tests by calling:');
console.log('   window.unifiedAssistantTests.testDataConsistency()');
console.log('   window.unifiedAssistantTests.testDeletionFlow()');
console.log('   etc.');
