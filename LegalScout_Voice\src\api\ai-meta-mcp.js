/**
 * AI Meta MCP API
 * 
 * This file contains API routes for interacting with the AI Meta MCP Server.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

// Function to get AI Meta MCP client
const getAiMetaMcpClient = async () => {
  const mcpClient = new Client({
    name: 'legalscout-api-client',
    version: '1.0.0',
  });
  
  // Connect to the AI Meta MCP server
  const transport = new SSEClientTransport({
    url: 'http://localhost:8080/sse', // Adjust port if needed
  });
  
  await mcpClient.connect(transport);
  return mcpClient;
};

// Create function
export const createFunction = async (req, res) => {
  try {
    const { name, description, parameters_schema, implementation_code, execution_environment } = req.body;
    
    // Get AI Meta MCP client
    const mcpClient = await getAiMetaMcpClient();
    
    // Call the define_function tool
    const result = await mcpClient.callTool({
      name: 'define_function',
      arguments: {
        name,
        description,
        parameters_schema,
        implementation_code,
        execution_environment: execution_environment || 'javascript'
      },
    });
    
    // Disconnect from the MCP server
    await mcpClient.disconnect();
    
    // Return the result
    res.json({
      success: true,
      result: result.content && result.content[0] ? result.content[0].text : null
    });
  } catch (error) {
    console.error('Error creating function:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Call function
export const callFunction = async (req, res) => {
  try {
    const { name, arguments: functionArgs } = req.body;
    
    // Get AI Meta MCP client
    const mcpClient = await getAiMetaMcpClient();
    
    // Call the function
    const result = await mcpClient.callTool({
      name,
      arguments: functionArgs,
    });
    
    // Disconnect from the MCP server
    await mcpClient.disconnect();
    
    // Return the result
    res.json({
      success: true,
      result: result.content && result.content[0] ? result.content[0].text : null
    });
  } catch (error) {
    console.error('Error calling function:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// List functions
export const listFunctions = async (req, res) => {
  try {
    // Get AI Meta MCP client
    const mcpClient = await getAiMetaMcpClient();
    
    // Call the list_functions tool
    const result = await mcpClient.callTool({
      name: 'list_functions',
      arguments: {},
    });
    
    // Disconnect from the MCP server
    await mcpClient.disconnect();
    
    // Return the result
    res.json({
      success: true,
      result: result.content && result.content[0] ? result.content[0].text : null
    });
  } catch (error) {
    console.error('Error listing functions:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
