/**
 * Sync Status Component
 *
 * This component displays the current synchronization status and provides
 * buttons to check consistency and sync the attorney profile.
 *
 * Enhanced to use the AttorneyProfileManager for more robust synchronization.
 */

import React from 'react';
import { useSync } from '../contexts/SyncContext';
import { useAttorneyProfile } from '../hooks/useAttorneyProfile';
import './SyncStatus.css';

/**
 * Format a date as a relative time string
 *
 * @param {Date} date - The date to format
 * @returns {string} The formatted date
 */
const formatRelativeTime = (date) => {
  if (!date) return 'Never';

  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);

  if (diffSec < 60) {
    return 'Just now';
  } else if (diffMin < 60) {
    return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;
  } else {
    return date.toLocaleString();
  }
};

/**
 * Sync Status Component
 *
 * @param {Object} props - The component props
 * @param {string} props.attorneyId - The ID of the attorney
 * @returns {JSX.Element} The component
 */
const SyncStatus = ({ attorneyId }) => {
  // Use both the original sync context and our enhanced attorney profile hook
  const {
    checkConsistency,
    syncProfile,
    isSyncing: contextIsSyncing,
    syncStatus: contextSyncStatus,
    lastSyncTime: contextLastSyncTime
  } = useSync();

  // Use our enhanced attorney profile hook
  const {
    attorney,
    loading,
    error,
    syncStatus: enhancedSyncStatus,
    lastSyncTime: enhancedLastSyncTime,
    forceSync,
    checkSyncStatus
  } = useAttorneyProfile();

  // Use the enhanced values if available, otherwise fall back to context values
  const isSyncing = loading || contextIsSyncing;
  const syncStatus = enhancedSyncStatus || contextSyncStatus;
  const lastSyncTime = enhancedLastSyncTime || contextLastSyncTime;

  // Handle check consistency button click
  const handleCheckConsistency = async () => {
    try {
      // Try the enhanced method first
      if (attorney) {
        await checkSyncStatus();
      } else if (attorneyId) {
        // Fall back to the original method
        await checkConsistency(attorneyId);
      } else {
        console.error('No attorney ID available for consistency check');
      }
    } catch (error) {
      console.error('Error checking consistency:', error);
    }
  };

  // Handle sync profile button click
  const handleSyncProfile = async () => {
    try {
      // Try the enhanced method first
      if (attorney) {
        await forceSync();
      } else if (attorneyId) {
        // Fall back to the original method
        await syncProfile(attorneyId, true);
      } else {
        console.error('No attorney ID available for sync');
      }
    } catch (error) {
      console.error('Error syncing profile:', error);
    }
  };

  return (
    <div className="sync-status">
      <div className="sync-status-header">
        <h3>Synchronization Status</h3>
        <div className="sync-status-actions">
          <button
            onClick={handleCheckConsistency}
            disabled={isSyncing}
            className="sync-button check-button"
          >
            {isSyncing ? 'Checking...' : 'Check Consistency'}
          </button>
          <button
            onClick={handleSyncProfile}
            disabled={isSyncing}
            className="sync-button sync-button"
          >
            {isSyncing ? 'Syncing...' : 'Force Sync'}
          </button>
        </div>
      </div>

      <div className="sync-status-content">
        <div className="sync-status-item">
          <span className="sync-status-label">Status:</span>
          <span className={`sync-status-value ${syncStatus.consistent ? 'status-ok' : 'status-error'}`}>
            {syncStatus.consistent ? 'Synchronized' : 'Out of Sync'}
          </span>
        </div>

        <div className="sync-status-item">
          <span className="sync-status-label">Last Checked:</span>
          <span className="sync-status-value">
            {formatRelativeTime(syncStatus.lastChecked)}
          </span>
        </div>

        <div className="sync-status-item">
          <span className="sync-status-label">Last Sync:</span>
          <span className="sync-status-value">
            {formatRelativeTime(lastSyncTime)}
          </span>
        </div>

        {syncStatus.message && (
          <div className="sync-status-message">
            {syncStatus.message}
          </div>
        )}

        {syncStatus.action === 'fixed' && (
          <div className="sync-status-fixed">
            <p>Inconsistencies were automatically fixed.</p>
            {syncStatus.discrepancies && (
              <details>
                <summary>View Details</summary>
                <pre>{JSON.stringify(syncStatus.discrepancies, null, 2)}</pre>
              </details>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SyncStatus;
