.live-call-monitor {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: var(--bg-secondary, #ffffff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Dark theme support */
[data-theme="dark"] .live-call-monitor {
  background: var(--dark-card-bg, #1f2937);
  border: 1px solid var(--dark-border, rgba(100, 181, 246, 0.2));
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.monitor-header {
  background: linear-gradient(135deg, var(--accent-primary, #4B74AA) 0%, var(--accent-secondary, #607D8B) 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

/* Dark theme header */
[data-theme="dark"] .monitor-header {
  background: linear-gradient(135deg, var(--dark-accent, #64B5F6) 0%, var(--accent-secondary, #8b5cf6) 100%);
}

.monitor-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.call-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.call-id {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  opacity: 0.9;
}

.connection-status {
  font-size: 0.9rem;
  font-weight: 500;
}

.connection-status.connected {
  color: #4ade80;
}

.connection-status.disconnected {
  color: #f87171;
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.error-message {
  background: rgba(220, 38, 38, 0.1);
  color: var(--error-color, #dc2626);
  padding: 12px 20px;
  border-left: 4px solid var(--error-color, #dc2626);
  margin: 0;
  border-radius: 4px;
}

/* Dark theme error message */
[data-theme="dark"] .error-message {
  background: rgba(239, 68, 68, 0.15);
  color: #fca5a5;
}

.call-status-panel {
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

/* Dark theme status panel */
[data-theme="dark"] .call-status-panel {
  border-bottom: 1px solid var(--dark-border, rgba(100, 181, 246, 0.2));
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dark theme status labels */
[data-theme="dark"] .status-label {
  color: var(--dark-text-secondary, rgba(255, 255, 255, 0.7));
}

.control-url-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
  display: block;
  margin-bottom: 8px;
}

/* Dark theme control URL label */
[data-theme="dark"] .control-url-label {
  color: var(--dark-text-secondary, rgba(255, 255, 255, 0.7));
}

.status-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

/* Dark theme status values */
[data-theme="dark"] .status-value {
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

/* Audio Visualizer - VapiBlocks inspired */
.audio-visualizer {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  margin-right: 8px;
  height: 20px;
}

.visualizer-bar {
  width: 3px;
  background: linear-gradient(to top, var(--accent-primary, #4B74AA), var(--accent-secondary, #607D8B));
  border-radius: 2px;
  animation: pulse 1.5s ease-in-out infinite;
  transition: all 0.3s ease;
}

@keyframes pulse {
  0%, 100% { transform: scaleY(0.3); }
  50% { transform: scaleY(1); }
}

/* Assistant status enhancements */
.assistant-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.speaking-indicator {
  font-size: 0.75rem;
  color: var(--accent-primary, #4B74AA);
  font-weight: 500;
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.in-progress,
.status-indicator.ringing {
  background: #10b981;
  animation: pulse-dot 2s ease-in-out infinite;
}

.status-indicator.completed {
  background: #3b82f6;
}

.status-indicator.failed,
.status-indicator.ended {
  background: #ef4444;
}

.status-indicator.unknown {
  background: #6b7280;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

.status-value.in-progress {
  color: #059669;
}

.status-value.completed {
  color: #3b82f6;
}

.status-value.failed {
  color: #dc2626;
}

.transcript-panel {
  flex: 1;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  flex-direction: column;
  min-height: 200px;
  max-height: 300px;
}

/* Dark theme transcript panel */
[data-theme="dark"] .transcript-panel {
  border-bottom: 1px solid var(--dark-border, rgba(100, 181, 246, 0.2));
}

.transcript-panel h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #374151);
}

/* Dark theme transcript heading */
[data-theme="dark"] .transcript-panel h4 {
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

.transcript-content {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 15px;
  background: #f9fafb;
}

.transcript-entry {
  margin-bottom: 12px;
  padding: 10px;
  border-radius: 6px;
  background: white;
  border-left: 3px solid #e5e7eb;
}

.transcript-entry.assistant {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.transcript-entry.user {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.transcript-entry .speaker {
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  margin-right: 8px;
}

.transcript-entry .message {
  color: #111827;
  line-height: 1.5;
}

.transcript-entry .timestamp {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 5px;
}

.no-transcript {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 40px 20px;
}

.intervention-panel {
  padding: 20px;
  background: #f9fafb;
}

.intervention-panel h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.control-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-buttons button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.intervention-button {
  background: #f3f4f6;
  color: #374151;
}

.intervention-button:hover {
  background: #e5e7eb;
}

.intervention-button.active {
  background: #dc2626;
  color: white;
}

.takeover-button {
  background: #3b82f6;
  color: white;
}

.takeover-button:hover {
  background: #2563eb;
}

.takeover-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.end-call-button {
  background: #dc2626;
  color: white;
}

.end-call-button:hover {
  background: #b91c1c;
}

.mute-button {
  background: #f59e0b;
  color: white;
}

.mute-button:hover {
  background: #d97706;
}

.unmute-button {
  background: #10b981;
  color: white;
}

.unmute-button:hover {
  background: #059669;
}

/* Message Injection Section */
.message-injection-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

/* Dark theme message injection */
[data-theme="dark"] .message-injection-section {
  border-top: 1px solid var(--dark-border, rgba(100, 181, 246, 0.2));
}

.message-injection-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary, #374151);
}

/* Dark theme label */
[data-theme="dark"] .message-injection-section label {
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

.message-input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.message-input-group input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  font-size: 0.875rem;
  background: var(--bg-secondary, #ffffff);
  color: var(--text-primary, #111827);
}

/* Dark theme input */
[data-theme="dark"] .message-input-group input {
  background: var(--dark-input-bg, rgba(24, 24, 28, 0.4));
  border: 1px solid var(--dark-border, rgba(100, 181, 246, 0.2));
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

.message-input-group input:focus {
  outline: none;
  border-color: var(--accent-primary, #4B74AA);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

.inject-button {
  padding: 10px 16px;
  background: var(--accent-primary, #4B74AA);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  white-space: nowrap;
}

.inject-button:hover:not(:disabled) {
  background: var(--accent-secondary, #607D8B);
  transform: translateY(-1px);
}

.inject-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.message-injection-section small {
  color: var(--text-secondary, #6b7280);
  font-style: italic;
}

/* Dark theme small text */
[data-theme="dark"] .message-injection-section small {
  color: var(--dark-text-secondary, rgba(255, 255, 255, 0.7));
}

.control-url-section {
  margin-bottom: 20px;
}

.control-url-section label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.url-input-group {
  display: flex;
  gap: 8px;
}

.control-url-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
  background: white;
}

.copy-button {
  padding: 8px 12px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-button:hover {
  background: #4b5563;
}

.control-url-section small {
  display: block;
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 5px;
}

.notes-section {
  margin-top: 20px;
}

.notes-section label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.notes-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
}

.notes-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .live-call-monitor {
    width: 95%;
    max-height: 95vh;
  }
  
  .monitor-header {
    padding: 15px;
  }
  
  .monitor-header h3 {
    font-size: 1.25rem;
  }
  
  .call-info {
    align-items: flex-start;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .url-input-group {
    flex-direction: column;
  }
}
