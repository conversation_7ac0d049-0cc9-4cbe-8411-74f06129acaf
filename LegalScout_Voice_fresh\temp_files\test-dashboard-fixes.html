<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Session Agent Change - Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        
        .test-section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4B74AA;
        }
        
        .button {
            background: #4B74AA;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #3a5d8a;
        }
        
        .button.danger {
            background: #d32f2f;
        }
        
        .button.danger:hover {
            background: #b71c1c;
        }
        
        .button.success {
            background: #388e3c;
        }
        
        .button.success:hover {
            background: #2e7d32;
        }
        
        .console-output {
            background: #1e1e1e;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.info {
            background: #1976d2;
            color: white;
        }
        
        .status.success {
            background: #388e3c;
            color: white;
        }
        
        .status.warning {
            background: #f57c00;
            color: white;
        }
        
        .status.error {
            background: #d32f2f;
            color: white;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Dashboard Session Agent Change - Test Suite</h1>
            <p>Diagnostic and surgical fix testing for LegalScout Voice dashboard issues</p>
        </div>

        <div class="test-section">
            <h2>📊 Diagnostic Tests</h2>
            <p>Run comprehensive diagnostics to identify issues without making changes.</p>
            <button class="button" onclick="runDiagnostics()">Run Diagnostics</button>
            <button class="button" onclick="runImpactAssessment()">Run Impact Assessment</button>
            <div id="diagnostic-status" class="status info" style="display: none;">
                Running diagnostics...
            </div>
            <div id="diagnostic-output" class="console-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Surgical Fixes</h2>
            <p>Apply minimal, targeted fixes to restore functionality.</p>
            <button class="button success" onclick="applySurgicalFixes()">Apply Surgical Fixes</button>
            <button class="button" onclick="testFixes()">Test Applied Fixes</button>
            <div id="fix-status" class="status info" style="display: none;">
                Applying fixes...
            </div>
            <div id="fix-output" class="console-output" style="display: none;"></div>
        </div>

        <div class="grid">
            <div class="test-section">
                <h3>🎯 Quick Tests</h3>
                <button class="button" onclick="testIframeDetection()">Test Iframe Detection</button>
                <button class="button" onclick="testStateSync()">Test State Sync</button>
                <button class="button" onclick="testAssistantSwitch()">Test Assistant Switch</button>
                <div id="quick-test-output" class="console-output" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>📋 System Status</h3>
                <div id="system-status">
                    <p>Click "Check System Status" to see current state</p>
                </div>
                <button class="button" onclick="checkSystemStatus()">Check System Status</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🚨 Emergency Actions</h2>
            <p><strong>Warning:</strong> These actions may affect the running system.</p>
            <button class="button danger" onclick="clearAllFixes()">Clear All Emergency Fixes</button>
            <button class="button danger" onclick="resetLocalStorage()">Reset localStorage</button>
            <button class="button" onclick="exportResults()">Export Test Results</button>
        </div>
    </div>

    <script>
        // Console capture for display
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;
        
        let consoleOutput = [];
        
        function captureConsole() {
            console.log = function(...args) {
                consoleOutput.push(`[LOG] ${args.join(' ')}`);
                originalConsoleLog.apply(console, args);
                updateConsoleDisplay();
            };
            
            console.warn = function(...args) {
                consoleOutput.push(`[WARN] ${args.join(' ')}`);
                originalConsoleWarn.apply(console, args);
                updateConsoleDisplay();
            };
            
            console.error = function(...args) {
                consoleOutput.push(`[ERROR] ${args.join(' ')}`);
                originalConsoleError.apply(console, args);
                updateConsoleDisplay();
            };
        }
        
        function updateConsoleDisplay() {
            const outputs = document.querySelectorAll('.console-output');
            outputs.forEach(output => {
                if (output.style.display !== 'none') {
                    output.textContent = consoleOutput.slice(-50).join('\n');
                    output.scrollTop = output.scrollHeight;
                }
            });
        }
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }
        
        function showOutput(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            updateConsoleDisplay();
        }
        
        // Load and run diagnostic scripts
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function runDiagnostics() {
            showStatus('diagnostic-status', 'Loading diagnostic scripts...', 'info');
            showOutput('diagnostic-output');
            
            try {
                await loadScript('./diagnostic-test-suite.js');
                showStatus('diagnostic-status', 'Diagnostics completed. Check console output.', 'success');
            } catch (error) {
                showStatus('diagnostic-status', 'Failed to load diagnostic scripts', 'error');
                console.error('Diagnostic load error:', error);
            }
        }
        
        async function runImpactAssessment() {
            showStatus('diagnostic-status', 'Loading impact assessment...', 'info');
            showOutput('diagnostic-output');
            
            try {
                await loadScript('./impact-assessment.js');
                showStatus('diagnostic-status', 'Impact assessment completed.', 'success');
            } catch (error) {
                showStatus('diagnostic-status', 'Failed to load impact assessment', 'error');
                console.error('Impact assessment load error:', error);
            }
        }
        
        async function applySurgicalFixes() {
            showStatus('fix-status', 'Loading surgical fixes...', 'info');
            showOutput('fix-output');
            
            try {
                await loadScript('./surgical-fix-session-agent-change.js');
                showStatus('fix-status', 'Surgical fixes applied. Check console output.', 'success');
            } catch (error) {
                showStatus('fix-status', 'Failed to apply surgical fixes', 'error');
                console.error('Surgical fix load error:', error);
            }
        }
        
        async function testFixes() {
            showStatus('fix-status', 'Loading test runner...', 'info');
            showOutput('fix-output');
            
            try {
                await loadScript('./test-runner.js');
                showStatus('fix-status', 'Fix testing completed.', 'success');
            } catch (error) {
                showStatus('fix-status', 'Failed to load test runner', 'error');
                console.error('Test runner load error:', error);
            }
        }
        
        // Quick tests
        function testIframeDetection() {
            showOutput('quick-test-output');
            console.log('🔍 Testing iframe detection...');
            
            const iframes = document.querySelectorAll('iframe');
            console.log(`Found ${iframes.length} total iframes`);
            
            if (window.DashboardIframeManager) {
                const count = window.DashboardIframeManager.getIframeCount();
                console.log(`DashboardIframeManager reports ${count} preview iframes`);
            } else {
                console.log('❌ DashboardIframeManager not available');
            }
        }
        
        function testStateSync() {
            showOutput('quick-test-output');
            console.log('🔄 Testing state synchronization...');
            
            if (window.standaloneAttorneyManager) {
                const attorney = window.standaloneAttorneyManager.attorney;
                console.log('Manager attorney:', attorney?.id, attorney?.vapi_assistant_id);
            } else {
                console.log('❌ StandaloneAttorneyManager not available');
            }
            
            const stored = localStorage.getItem('attorney');
            if (stored) {
                try {
                    const parsed = JSON.parse(stored);
                    console.log('Stored attorney:', parsed.id, parsed.vapi_assistant_id);
                } catch (error) {
                    console.log('❌ Corrupted localStorage data');
                }
            } else {
                console.log('❌ No attorney in localStorage');
            }
        }
        
        function testAssistantSwitch() {
            showOutput('quick-test-output');
            console.log('🔀 Testing assistant switch functionality...');
            
            if (window.handleAssistantSelection) {
                console.log('✅ handleAssistantSelection function available');
            } else {
                console.log('❌ handleAssistantSelection function not available');
            }
        }
        
        function checkSystemStatus() {
            const status = document.getElementById('system-status');
            
            const checks = {
                'StandaloneAttorneyManager': !!window.standaloneAttorneyManager,
                'DashboardIframeManager': !!window.DashboardIframeManager,
                'Supabase Client': !!window.supabase,
                'Vapi Service Manager': !!window.vapiServiceManager,
                'Assistant Selection Handler': !!window.handleAssistantSelection,
                'Attorney in localStorage': !!localStorage.getItem('attorney')
            };
            
            let html = '<h4>System Components:</h4><ul>';
            Object.entries(checks).forEach(([name, available]) => {
                const emoji = available ? '✅' : '❌';
                html += `<li>${emoji} ${name}</li>`;
            });
            html += '</ul>';
            
            status.innerHTML = html;
        }
        
        // Emergency actions
        function clearAllFixes() {
            if (confirm('This will clear all emergency fix scripts. Continue?')) {
                console.log('🧹 Clearing emergency fixes...');
                // This would require more complex implementation
                console.log('⚠️ Manual clearing required - remove script tags from DOM');
            }
        }
        
        function resetLocalStorage() {
            if (confirm('This will clear all localStorage data. Continue?')) {
                localStorage.clear();
                console.log('🗑️ localStorage cleared');
                location.reload();
            }
        }
        
        function exportResults() {
            const results = {
                diagnosticResults: window.diagnosticResults || null,
                impactAssessment: window.impactAssessment || null,
                testResults: window.testResults || null,
                surgicalFixResults: window.surgicalFixResults || null,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard-test-results-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // Initialize
        captureConsole();
        console.log('🚀 Dashboard Session Agent Change Test Suite loaded');
        console.log('📝 Use the buttons above to run diagnostics and apply fixes');
    </script>
</body>
</html>
