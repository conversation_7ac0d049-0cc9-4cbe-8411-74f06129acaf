/**
 * Hook for using the Vapi Emissions Service
 *
 * This hook provides a React interface for the Vapi Emissions Service,
 * making it easy to monitor and process emissions from Vapi calls.
 */

import { useState, useEffect, useCallback } from 'react';
import { vapiEmissionsService } from '../services/vapiEmissionsService';

/**
 * Hook for monitoring Vapi call emissions
 * @param {Object} options - Hook options
 * @param {string} options.apiKey - Vapi API key
 * @param {string} options.callId - ID of the call to monitor
 * @returns {Object} - Hook state and methods
 */
const useVapiEmissions = ({ apiKey, callId }) => {
  const [initialized, setInitialized] = useState(false);
  const [monitoring, setMonitoring] = useState(false);
  const [error, setError] = useState(null);
  const [transcripts, setTranscripts] = useState([]);
  const [messages, setMessages] = useState([]);
  const [toolExecutions, setToolExecutions] = useState([]);
  const [callData, setCallData] = useState(null);
  const [dossierData, setDossierData] = useState(null);

  // Initialize the emissions service
  useEffect(() => {
    if (!apiKey) return;

    const initializeService = async () => {
      try {
        const success = await vapiEmissionsService.initialize(apiKey);
        setInitialized(true); // Always set to true to allow the application to continue

        if (!success) {
          console.warn('Vapi Emissions Service initialized in limited mode');
          // Don't set an error, just log a warning
        }
      } catch (err) {
        console.error('Error initializing Vapi Emissions Service:', err);
        // Don't set an error, just log it

        // Still mark as initialized to allow the application to continue
        setInitialized(true);
      }
    };

    initializeService();
  }, [apiKey]);

  // Start monitoring a call
  const startMonitoring = useCallback(async (id = callId) => {
    if (!initialized) {
      console.warn('Vapi Emissions Service not initialized, but continuing anyway');
      // Return true to allow the application to continue
      return true;
    }

    if (!id) {
      console.warn('Call ID is required for monitoring, but continuing anyway');
      // Return true to allow the application to continue
      return true;
    }

    try {
      const success = await vapiEmissionsService.monitorCall(id, {
        onTranscript: (transcript) => {
          setTranscripts(prev => [...prev, transcript]);
        },
        onMessage: (message) => {
          setMessages(prev => [...prev, message]);
        },
        onToolExecution: (toolExecution) => {
          setToolExecutions(prev => [...prev, toolExecution]);
        },
        onCallEnd: (call) => {
          setCallData(call);
          const dossier = vapiEmissionsService.extractDossierData(call);
          if (dossier) {
            setDossierData(dossier);
          }
          setMonitoring(false);
        }
      });

      // Even if monitoring fails, we'll still set monitoring to true
      // to prevent blocking the application
      setMonitoring(true);
      return true;
    } catch (err) {
      console.error('Error starting call monitoring:', err);
      // Don't set an error, just log it

      // Return true to allow the application to continue
      return true;
    }
  }, [initialized, callId]);

  // Stop monitoring a call
  const stopMonitoring = useCallback(() => {
    if (!callId || !monitoring) return;

    vapiEmissionsService.stopMonitoring(callId);
    setMonitoring(false);
  }, [callId, monitoring]);

  // Clean up when the component unmounts
  useEffect(() => {
    return () => {
      if (callId && monitoring) {
        vapiEmissionsService.stopMonitoring(callId);
      }
    };
  }, [callId, monitoring]);

  return {
    initialized,
    monitoring,
    error,
    transcripts,
    messages,
    toolExecutions,
    callData,
    dossierData,
    startMonitoring,
    stopMonitoring
  };
};

export default useVapiEmissions;
