/**
 * Vapi Call Diagnostics Utility
 * 
 * This utility provides comprehensive diagnostics for Vapi call functionality,
 * validating configuration, testing connections, and identifying issues.
 */

import { vapiMcpService } from '../services/vapiMcpService.js';
import { getVapiConfig, validateVapiConfig } from '../config/vapiConfig.js';

/**
 * Comprehensive diagnostic test for Vapi call functionality
 * @param {Object} attorney - Attorney object with assistant ID
 * @returns {Promise<Object>} - Diagnostic results
 */
export async function runVapiCallDiagnostics(attorney) {
  const results = {
    timestamp: new Date().toISOString(),
    attorney: attorney?.id || 'unknown',
    tests: [],
    summary: {
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };

  const addTest = (name, status, message, details = null) => {
    results.tests.push({
      name,
      status, // 'pass', 'fail', 'warning'
      message,
      details,
      timestamp: new Date().toISOString()
    });
    results.summary[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
  };

  console.log('[VapiCallDiagnostics] Starting comprehensive diagnostics...');

  // Test 1: Configuration Validation
  try {
    const config = getVapiConfig();
    const validation = validateVapiConfig();
    
    if (validation.isValid) {
      addTest('Configuration', 'pass', 'Vapi configuration is valid', {
        hasPublicKey: validation.hasPublicKey,
        hasSecretKey: validation.hasSecretKey,
        apiUrl: config.apiUrl,
        mcpUrl: config.mcpUrl
      });
    } else {
      addTest('Configuration', 'fail', 'Vapi configuration is invalid', {
        warnings: validation.warnings,
        hasPublicKey: validation.hasPublicKey,
        hasSecretKey: validation.hasSecretKey
      });
    }
  } catch (error) {
    addTest('Configuration', 'fail', `Configuration error: ${error.message}`, { error: error.stack });
  }

  // Test 2: MCP Service Connection
  try {
    const connected = await vapiMcpService.ensureConnection();
    if (connected) {
      addTest('MCP Connection', 'pass', 'Successfully connected to Vapi MCP service', {
        useDirect: vapiMcpService.useDirect,
        apiUrl: vapiMcpService.directApiUrl
      });
    } else {
      addTest('MCP Connection', 'fail', 'Failed to connect to Vapi MCP service');
    }
  } catch (error) {
    addTest('MCP Connection', 'fail', `MCP connection error: ${error.message}`, { error: error.stack });
  }

  // Test 3: Assistant Validation
  if (attorney?.vapi_assistant_id) {
    try {
      const assistant = await vapiMcpService.getAssistant(attorney.vapi_assistant_id);
      if (assistant && !assistant.mock) {
        addTest('Assistant', 'pass', `Assistant found: ${assistant.name || 'Unnamed'}`, {
          assistantId: attorney.vapi_assistant_id,
          name: assistant.name,
          voice: assistant.voice,
          model: assistant.model
        });
      } else if (assistant?.mock) {
        addTest('Assistant', 'warning', 'Mock assistant detected - may not work in production', {
          assistantId: attorney.vapi_assistant_id,
          mock: true
        });
      } else {
        addTest('Assistant', 'fail', 'Assistant not found or invalid', {
          assistantId: attorney.vapi_assistant_id
        });
      }
    } catch (error) {
      addTest('Assistant', 'fail', `Assistant validation error: ${error.message}`, {
        assistantId: attorney.vapi_assistant_id,
        error: error.stack
      });
    }
  } else {
    addTest('Assistant', 'fail', 'No assistant ID configured for attorney', {
      attorney: attorney?.id,
      firmName: attorney?.firm_name
    });
  }

  // Test 4: Phone Numbers
  try {
    const phoneNumbers = await vapiMcpService.listPhoneNumbers();
    if (phoneNumbers && phoneNumbers.length > 0) {
      addTest('Phone Numbers', 'pass', `Found ${phoneNumbers.length} phone number(s)`, {
        count: phoneNumbers.length,
        numbers: phoneNumbers.map(n => ({
          id: n.id,
          number: n.phone_number || n.phoneNumber,
          name: n.friendly_name || n.name
        }))
      });
    } else {
      addTest('Phone Numbers', 'warning', 'No phone numbers found - calls may not work properly');
    }
  } catch (error) {
    addTest('Phone Numbers', 'warning', `Phone numbers check failed: ${error.message}`, { error: error.stack });
  }

  // Test 5: API Endpoints
  try {
    const endpoints = vapiMcpService.possibleApiEndpoints;
    const workingEndpoints = [];
    
    for (const endpoint of endpoints) {
      try {
        const testUrl = `${endpoint}/assistant?limit=1`;
        const response = await fetch(testUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${vapiMcpService.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          workingEndpoints.push(endpoint);
        }
      } catch (endpointError) {
        // Ignore individual endpoint errors
      }
    }
    
    if (workingEndpoints.length > 0) {
      addTest('API Endpoints', 'pass', `Found ${workingEndpoints.length} working endpoint(s)`, {
        workingEndpoints,
        totalTested: endpoints.length
      });
    } else {
      addTest('API Endpoints', 'fail', 'No working API endpoints found', {
        testedEndpoints: endpoints
      });
    }
  } catch (error) {
    addTest('API Endpoints', 'fail', `API endpoint test failed: ${error.message}`, { error: error.stack });
  }

  // Test 6: Call Creation (Dry Run)
  if (attorney?.vapi_assistant_id && results.summary.failed === 0) {
    try {
      // Test with a dummy phone number to validate call creation logic
      const testPhoneNumber = '+1234567890';
      
      // Don't actually create the call, just validate the parameters
      const callArgs = {
        assistantId: attorney.vapi_assistant_id,
        customer: {
          phoneNumber: testPhoneNumber
        }
      };
      
      addTest('Call Creation (Dry Run)', 'pass', 'Call creation parameters validated', {
        assistantId: attorney.vapi_assistant_id,
        phoneNumber: testPhoneNumber,
        callArgs
      });
    } catch (error) {
      addTest('Call Creation (Dry Run)', 'fail', `Call creation validation failed: ${error.message}`, { error: error.stack });
    }
  } else {
    addTest('Call Creation (Dry Run)', 'warning', 'Skipped due to previous failures or missing assistant');
  }

  // Generate summary
  const totalTests = results.tests.length;
  const successRate = totalTests > 0 ? (results.summary.passed / totalTests * 100).toFixed(1) : 0;
  
  results.summary.total = totalTests;
  results.summary.successRate = successRate;
  results.summary.overallStatus = results.summary.failed === 0 ? 
    (results.summary.warnings === 0 ? 'healthy' : 'warning') : 'error';

  console.log('[VapiCallDiagnostics] Diagnostics completed:', results.summary);
  
  return results;
}

/**
 * Format diagnostic results for display
 * @param {Object} results - Diagnostic results
 * @returns {string} - Formatted results
 */
export function formatDiagnosticResults(results) {
  let output = `\n=== Vapi Call Diagnostics Report ===\n`;
  output += `Timestamp: ${results.timestamp}\n`;
  output += `Attorney: ${results.attorney}\n`;
  output += `Overall Status: ${results.summary.overallStatus.toUpperCase()}\n`;
  output += `Success Rate: ${results.summary.successRate}% (${results.summary.passed}/${results.summary.total})\n\n`;

  results.tests.forEach((test, index) => {
    const status = test.status === 'pass' ? '✅' : test.status === 'warning' ? '⚠️' : '❌';
    output += `${index + 1}. ${status} ${test.name}: ${test.message}\n`;
    
    if (test.details) {
      output += `   Details: ${JSON.stringify(test.details, null, 2).replace(/\n/g, '\n   ')}\n`;
    }
    output += '\n';
  });

  return output;
}

/**
 * Quick health check for Vapi call functionality
 * @param {Object} attorney - Attorney object
 * @returns {Promise<boolean>} - True if healthy
 */
export async function quickHealthCheck(attorney) {
  try {
    const results = await runVapiCallDiagnostics(attorney);
    return results.summary.overallStatus !== 'error';
  } catch (error) {
    console.error('[VapiCallDiagnostics] Health check failed:', error);
    return false;
  }
}
