/**
 * CORS Diagnostics Script
 * 
 * Run this in the browser console to diagnose CORS issues
 */

window.corsDiagnostics = {
  
  async runDiagnostics() {
    console.log('🔍 [CORS Diagnostics] Starting CORS diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      issues: [],
      fixes: [],
      recommendations: []
    };
    
    try {
      // 1. Test API endpoints
      console.log('🌐 [Diagnostics] Testing API endpoints...');
      await this.testApiEndpoints(results);
      
      // 2. Test Supabase proxy
      console.log('🗄️ [Diagnostics] Testing Supabase proxy...');
      await this.testSupabaseProxy(results);
      
      // 3. Test storage uploads
      console.log('📤 [Diagnostics] Testing storage uploads...');
      await this.testStorageUpload(results);
      
      // 4. Test preflight requests
      console.log('✈️ [Diagnostics] Testing preflight requests...');
      await this.testPreflightRequests(results);
      
      // 5. Generate report
      this.generateReport(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ [Diagnostics] Error running diagnostics:', error);
      results.issues.push({
        type: 'diagnostic_error',
        message: error.message,
        severity: 'high'
      });
      return results;
    }
  },
  
  async testApiEndpoints(results) {
    const endpoints = [
      { path: '/api/health', method: 'GET' },
      { path: '/api/env', method: 'GET' },
      { path: '/api/sync-tools/manage-auth-state', method: 'POST', body: { test: true } }
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.path, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: endpoint.body ? JSON.stringify(endpoint.body) : undefined
        });
        
        if (response.ok) {
          results.fixes.push({
            type: 'api_endpoint_ok',
            endpoint: endpoint.path,
            status: response.status,
            method: endpoint.method
          });
        } else {
          results.issues.push({
            type: 'api_endpoint_error',
            endpoint: endpoint.path,
            status: response.status,
            method: endpoint.method,
            severity: 'medium'
          });
        }
        
      } catch (error) {
        if (error.message.includes('CORS')) {
          results.issues.push({
            type: 'cors_error',
            endpoint: endpoint.path,
            message: error.message,
            severity: 'high'
          });
        } else {
          results.issues.push({
            type: 'network_error',
            endpoint: endpoint.path,
            message: error.message,
            severity: 'medium'
          });
        }
      }
    }
  },
  
  async testSupabaseProxy(results) {
    try {
      // Test the Supabase proxy endpoint
      const response = await fetch('/api/supabase-proxy/attorneys?select=id&limit=1', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      });
      
      if (response.ok || response.status === 401) { // 401 is expected without proper auth
        results.fixes.push({
          type: 'supabase_proxy_ok',
          message: 'Supabase proxy is accessible',
          status: response.status
        });
      } else {
        results.issues.push({
          type: 'supabase_proxy_error',
          message: `Supabase proxy returned status ${response.status}`,
          severity: 'high'
        });
      }
      
    } catch (error) {
      if (error.message.includes('CORS')) {
        results.issues.push({
          type: 'supabase_cors_error',
          message: 'Supabase proxy has CORS issues',
          error: error.message,
          severity: 'high'
        });
      } else {
        results.issues.push({
          type: 'supabase_proxy_network_error',
          message: error.message,
          severity: 'medium'
        });
      }
    }
  },
  
  async testStorageUpload(results) {
    try {
      // Create a small test blob
      const testBlob = new Blob(['test'], { type: 'text/plain' });
      const formData = new FormData();
      formData.append('file', testBlob, 'test.txt');
      
      // Test storage upload (this will likely fail, but we're testing CORS)
      const response = await fetch('https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/legalscout_bucket1/test.txt', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': 'Bearer test-token'
        }
      });
      
      // Any response (even 401/403) means CORS is working
      results.fixes.push({
        type: 'storage_cors_ok',
        message: 'Storage endpoint is accessible (CORS working)',
        status: response.status
      });
      
    } catch (error) {
      if (error.message.includes('CORS')) {
        results.issues.push({
          type: 'storage_cors_error',
          message: 'Storage endpoint has CORS issues',
          error: error.message,
          severity: 'high'
        });
        
        results.recommendations.push({
          type: 'storage_cors_fix',
          message: 'Configure CORS for Supabase storage bucket or use proxy'
        });
      } else {
        results.issues.push({
          type: 'storage_network_error',
          message: error.message,
          severity: 'low'
        });
      }
    }
  },
  
  async testPreflightRequests(results) {
    const testEndpoints = [
      '/api/health',
      '/api/sync-tools/manage-auth-state',
      '/api/supabase-proxy/test'
    ];
    
    for (const endpoint of testEndpoints) {
      try {
        // Send an OPTIONS request (preflight)
        const response = await fetch(endpoint, {
          method: 'OPTIONS',
          headers: {
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type, Authorization'
          }
        });
        
        if (response.ok) {
          results.fixes.push({
            type: 'preflight_ok',
            endpoint,
            status: response.status,
            headers: {
              allowOrigin: response.headers.get('Access-Control-Allow-Origin'),
              allowMethods: response.headers.get('Access-Control-Allow-Methods'),
              allowHeaders: response.headers.get('Access-Control-Allow-Headers')
            }
          });
        } else {
          results.issues.push({
            type: 'preflight_failed',
            endpoint,
            status: response.status,
            severity: 'high'
          });
        }
        
      } catch (error) {
        results.issues.push({
          type: 'preflight_error',
          endpoint,
          message: error.message,
          severity: 'high'
        });
      }
    }
  },
  
  generateReport(results) {
    console.log('\n📊 [CORS Diagnostics] REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n🕒 Timestamp: ${results.timestamp}`);
    
    if (results.issues.length > 0) {
      console.log(`\n❌ Issues Found (${results.issues.length}):`);
      results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.type}: ${issue.message}`);
        if (issue.endpoint) {
          console.log(`     Endpoint: ${issue.endpoint}`);
        }
        if (issue.status) {
          console.log(`     Status: ${issue.status}`);
        }
      });
    }
    
    if (results.fixes.length > 0) {
      console.log(`\n✅ Working Components (${results.fixes.length}):`);
      results.fixes.forEach((fix, index) => {
        console.log(`  ${index + 1}. ${fix.type}: ${fix.message}`);
        if (fix.endpoint) {
          console.log(`     Endpoint: ${fix.endpoint}`);
        }
        if (fix.status) {
          console.log(`     Status: ${fix.status}`);
        }
      });
    }
    
    if (results.recommendations.length > 0) {
      console.log(`\n💡 Recommendations (${results.recommendations.length}):`);
      results.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.type}: ${rec.message}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
    
    // Summary
    const corsIssues = results.issues.filter(i => i.type.includes('cors')).length;
    const preflightIssues = results.issues.filter(i => i.type.includes('preflight')).length;
    
    if (corsIssues > 0) {
      console.log(`🚨 CORS ISSUES: ${corsIssues} CORS-related problems detected`);
      console.log('💡 SOLUTION: Check server CORS configuration and headers');
    }
    if (preflightIssues > 0) {
      console.log(`✈️ PREFLIGHT ISSUES: ${preflightIssues} preflight request problems`);
      console.log('💡 SOLUTION: Ensure OPTIONS requests return 200 status');
    }
    if (results.issues.length === 0) {
      console.log('🎉 SUCCESS: No CORS issues detected!');
    }
    
    // Specific guidance
    if (corsIssues > 0 || preflightIssues > 0) {
      console.log('\n🔧 TROUBLESHOOTING STEPS:');
      console.log('1. Check that all API endpoints handle OPTIONS requests');
      console.log('2. Verify CORS headers are set correctly');
      console.log('3. Ensure preflight responses return 200 status');
      console.log('4. Check browser network tab for specific error details');
    }
  }
};

// Auto-run diagnostics
console.log('🔧 [CORS Diagnostics] Diagnostic tool loaded. Run window.corsDiagnostics.runDiagnostics() to start.');
