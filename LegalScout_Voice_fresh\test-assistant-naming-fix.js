/**
 * Test Assistant Naming Fix
 * 
 * This script tests the comprehensive fix for the assistant naming issue
 * to ensure that name changes update the existing assistant instead of creating new ones.
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_API_URL = 'https://api.vapi.ai';

// Test data
const CORRECT_ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';
const CORRECT_ASSISTANT_ID = '1d3471b7-8694-4844-b3ef-e05720693efc';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function main() {
  console.log('🧪 Testing Assistant Naming Fix...\n');

  try {
    // Test 1: Check current state
    console.log('📊 Test 1: Checking Current State');
    
    // Check Vapi assistant
    const vapiResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!vapiResponse.ok) {
      throw new Error(`Vapi assistant not found: ${vapiResponse.status}`);
    }

    const vapiAssistant = await vapiResponse.json();
    console.log(`✅ Current Vapi Assistant Name: "${vapiAssistant.name}"`);
    
    // Check database
    const { data: dbConfigs, error: dbError } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_id, assistant_name, attorney_id')
      .eq('attorney_id', CORRECT_ATTORNEY_ID);

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    console.log('📋 Current Database Configs:');
    dbConfigs?.forEach(config => {
      const isCorrect = config.assistant_id === CORRECT_ASSISTANT_ID;
      const status = isCorrect ? '✅' : '❌';
      console.log(`  ${status} Assistant ID: ${config.assistant_id}, Name: "${config.assistant_name}"`);
    });

    // Test 2: Simulate name change to test the fix
    console.log('\n🔄 Test 2: Simulating Name Change');
    
    const testName = `Test Assistant ${Date.now()}`;
    console.log(`Changing assistant name to: "${testName}"`);
    
    // Update Vapi assistant name
    const updateResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: testName
      })
    });

    if (!updateResponse.ok) {
      throw new Error(`Failed to update Vapi assistant: ${updateResponse.status}`);
    }

    const updatedVapiAssistant = await updateResponse.json();
    console.log(`✅ Vapi Assistant Updated: "${updatedVapiAssistant.name}"`);

    // Update database assistant name
    const { error: updateDbError } = await supabase
      .from('assistant_ui_configs')
      .update({
        assistant_name: testName,
        updated_at: new Date().toISOString()
      })
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .eq('assistant_id', CORRECT_ASSISTANT_ID);

    if (updateDbError) {
      console.warn(`⚠️ Database update warning: ${updateDbError.message}`);
    } else {
      console.log('✅ Database Assistant Name Updated');
    }

    // Test 3: Verify no wrong configs were created
    console.log('\n🔍 Test 3: Verifying No Wrong Configs Created');
    
    const { data: allConfigs, error: allConfigsError } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_id, assistant_name, attorney_id, updated_at')
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .order('updated_at', { ascending: false });

    if (allConfigsError) {
      throw new Error(`Error checking configs: ${allConfigsError.message}`);
    }

    console.log('📋 All Configs After Update:');
    let correctConfigFound = false;
    let wrongConfigsCount = 0;

    allConfigs?.forEach(config => {
      const isCorrect = config.assistant_id === CORRECT_ASSISTANT_ID;
      const isWrong = config.assistant_id === CORRECT_ATTORNEY_ID; // Attorney ID used as assistant ID
      const status = isCorrect ? '✅' : (isWrong ? '❌' : '⚠️');
      
      console.log(`  ${status} Assistant ID: ${config.assistant_id}, Name: "${config.assistant_name}", Updated: ${config.updated_at}`);
      
      if (isCorrect) {
        correctConfigFound = true;
      }
      if (isWrong) {
        wrongConfigsCount++;
      }
    });

    // Test 4: Verify the fix worked
    console.log('\n✅ Test 4: Fix Verification');
    
    const tests = [
      {
        name: 'Correct Assistant Config Exists',
        passed: correctConfigFound,
        description: 'The correct assistant ID has a config record'
      },
      {
        name: 'No Wrong Configs Created',
        passed: wrongConfigsCount === 0,
        description: 'No configs using attorney ID as assistant ID'
      },
      {
        name: 'Vapi Name Updated',
        passed: updatedVapiAssistant.name === testName,
        description: 'Vapi assistant name was updated correctly'
      },
      {
        name: 'Single Config Per Assistant',
        passed: allConfigs?.filter(c => c.assistant_id === CORRECT_ASSISTANT_ID).length === 1,
        description: 'Only one config exists for the correct assistant'
      }
    ];

    const passedTests = tests.filter(test => test.passed).length;
    const totalTests = tests.length;

    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    
    tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.name}: ${test.description}`);
    });

    // Test 5: Clean up and restore original name
    console.log('\n🧹 Test 5: Cleanup');
    
    // Restore original name
    const originalName = "Damon's Assistant";
    console.log(`Restoring original name: "${originalName}"`);
    
    const restoreResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: originalName
      })
    });

    if (restoreResponse.ok) {
      console.log('✅ Vapi assistant name restored');
    }

    const { error: restoreDbError } = await supabase
      .from('assistant_ui_configs')
      .update({
        assistant_name: originalName,
        updated_at: new Date().toISOString()
      })
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .eq('assistant_id', CORRECT_ASSISTANT_ID);

    if (!restoreDbError) {
      console.log('✅ Database assistant name restored');
    }

    // Delete any wrong configs that might have been created
    const { error: deleteWrongError } = await supabase
      .from('assistant_ui_configs')
      .delete()
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .eq('assistant_id', CORRECT_ATTORNEY_ID); // Delete configs where assistant_id = attorney_id

    if (!deleteWrongError) {
      console.log('✅ Wrong configs cleaned up');
    }

    // Final summary
    console.log('\n🎉 Test Summary:');
    if (passedTests === totalTests) {
      console.log('✅ ALL TESTS PASSED! The assistant naming fix is working correctly.');
      console.log('🎯 The system now properly updates existing assistants instead of creating new ones.');
      return true;
    } else {
      console.log(`⚠️ ${totalTests - passedTests} test(s) failed. The fix may need additional work.`);
      return false;
    }

  } catch (error) {
    console.error('❌ Test Error:', error.message);
    return false;
  }
}

// Run the test
main().then((success) => {
  if (success) {
    console.log('\n🏁 Assistant naming fix test completed successfully!');
    process.exit(0);
  } else {
    console.log('\n💥 Assistant naming fix test failed!');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
