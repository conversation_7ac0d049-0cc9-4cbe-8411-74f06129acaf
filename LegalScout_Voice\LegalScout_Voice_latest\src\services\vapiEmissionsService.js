/**
 * Vapi Emissions Service
 *
 * This service captures and processes emissions from Vapi calls using the MCP integration.
 * It provides a more direct and privacy-focused alternative to using external webhooks.
 */

import { vapiMcpService } from './vapiMcpService';
import { supabase } from '../lib/supabase';

class VapiEmissionsService {
  constructor() {
    this.initialized = false;
    this.callListeners = new Map();
  }

  /**
   * Initialize the emissions service
   * @param {string} apiKey - Vapi API key
   * @returns {Promise<boolean>} - Initialization success status
   */
  async initialize(apiKey) {
    if (this.initialized) return true;

    try {
      // Connect to Vapi MCP server
      const connected = await vapiMcpService.connect(apiKey);

      // Even if connection fails, we'll mark as initialized to prevent blocking the application
      this.initialized = true;

      if (!connected) {
        console.warn('VapiEmissionsService: Connected in fallback mode - some features may be limited');
      } else {
        console.log('VapiEmissionsService: Initialized successfully');
      }

      return true;
    } catch (error) {
      console.error('VapiEmissionsService: Initialization failed', error);

      // Mark as initialized anyway to prevent blocking the application
      this.initialized = true;
      console.warn('VapiEmissionsService: Continuing in limited mode without emissions monitoring');

      return true; // Return true to allow the application to continue
    }
  }

  /**
   * Start monitoring a call for emissions
   * @param {string} callId - The ID of the call to monitor
   * @param {Object} options - Monitoring options
   * @param {Function} options.onTranscript - Callback for transcript events
   * @param {Function} options.onMessage - Callback for message events
   * @param {Function} options.onToolExecution - Callback for tool execution events
   * @param {Function} options.onCallEnd - Callback for call end event
   * @returns {Promise<boolean>} - Success status
   */
  async monitorCall(callId, options = {}) {
    if (!this.initialized) {
      console.error('VapiEmissionsService: Not initialized');
      return false;
    }

    try {
      // Get call details
      const call = await vapiMcpService.getCall(callId);
      if (!call) {
        throw new Error(`Call not found: ${callId}`);
      }

      // Create a mutable call reference
      let currentCall = { ...call };

      // Set up polling for call updates
      const pollInterval = setInterval(async () => {
        try {
          const updatedCall = await vapiMcpService.getCall(callId);

          // Process new transcripts
          if (updatedCall.transcripts && options.onTranscript) {
            const newTranscripts = this._getNewTranscripts(currentCall, updatedCall);
            for (const transcript of newTranscripts) {
              options.onTranscript(transcript);
            }
          }

          // Process new messages
          if (updatedCall.messages && options.onMessage) {
            const newMessages = this._getNewMessages(currentCall, updatedCall);
            for (const message of newMessages) {
              options.onMessage(message);
            }
          }

          // Process tool executions
          if (updatedCall.tool_executions && options.onToolExecution) {
            const newToolExecutions = this._getNewToolExecutions(currentCall, updatedCall);
            for (const toolExecution of newToolExecutions) {
              options.onToolExecution(toolExecution);
            }
          }

          // Check if call has ended
          if (updatedCall.status === 'completed' && currentCall.status !== 'completed') {
            if (options.onCallEnd) {
              options.onCallEnd(updatedCall);
            }

            // Store call data in Supabase
            await this.storeCallData(updatedCall);

            // Clear the polling interval
            clearInterval(pollInterval);
          }

          // Update the call reference
          currentCall = updatedCall;
        } catch (error) {
          console.error('VapiEmissionsService: Error polling call', error);
        }
      }, 2000); // Poll every 2 seconds for more responsive updates

      // Set up a separate interval for real-time volume level updates
      const volumeUpdateInterval = setInterval(() => {
        try {
          // Create a synthetic volume level message based on audio activity
          // This is a fallback mechanism when the SDK doesn't emit volume events
          const randomVolume = Math.random() * 0.5; // Random value between 0 and 0.5

          if (options.onMessage) {
            options.onMessage({
              id: `volume-${Date.now()}`,
              type: 'volume-level',
              volumeLevel: randomVolume,
              timestamp: new Date().toISOString()
            });
          }
        } catch (error) {
          console.error('VapiEmissionsService: Error sending volume update', error);
        }
      }, 500); // Update volume level every 500ms for smooth animation

      // Store both intervals in the listeners map
      this.callListeners.set(callId, {
        pollInterval,
        volumeUpdateInterval,
        options
      });

      return true;
    } catch (error) {
      console.error('VapiEmissionsService: Error monitoring call', error);
      return false;
    }
  }

  /**
   * Stop monitoring a call
   * @param {string} callId - The ID of the call to stop monitoring
   */
  stopMonitoring(callId) {
    const listener = this.callListeners.get(callId);
    if (listener) {
      // Clear both intervals
      clearInterval(listener.pollInterval);
      if (listener.volumeUpdateInterval) {
        clearInterval(listener.volumeUpdateInterval);
      }
      this.callListeners.delete(callId);
      console.log('VapiEmissionsService: Stopped monitoring call', callId);
    }
  }

  /**
   * Store call data in Supabase
   * @param {Object} call - The call data to store
   * @returns {Promise<Object>} - The stored call data
   */
  async storeCallData(call) {
    try {
      // Extract relevant data from the call
      const callData = {
        call_id: call.id,
        assistant_id: call.assistant_id,
        customer_phone: call.customer?.phone_number,
        status: call.status,
        duration: call.duration,
        start_time: call.start_time,
        end_time: call.end_time,
        transcripts: call.transcripts || [],
        messages: call.messages || [],
        tool_executions: call.tool_executions || [],
        metadata: call.metadata || {}
      };

      // Store in Supabase
      const { data, error } = await supabase
        .from('call_records')
        .upsert(callData, { onConflict: 'call_id' })
        .select();

      if (error) {
        throw error;
      }

      console.log('VapiEmissionsService: Stored call data', data);
      return data;
    } catch (error) {
      console.error('VapiEmissionsService: Error storing call data', error);
      throw error;
    }
  }

  /**
   * Extract dossier data from call
   * @param {Object} call - The call data
   * @returns {Object} - The extracted dossier data
   */
  extractDossierData(call) {
    try {
      // Find tool executions for the LIVE_DOSSIER tool
      const dossierExecutions = (call.tool_executions || [])
        .filter(execution => execution.tool_id === '4a0d63cf-0b84-4eec-bddf-9c5869439d7e'); // LIVE_DOSSIER tool ID

      if (dossierExecutions.length === 0) {
        return null;
      }

      // Get the latest execution
      const latestExecution = dossierExecutions[dossierExecutions.length - 1];

      // Extract dossier data from the tool execution
      return {
        name: latestExecution.input?.name || '',
        email: latestExecution.input?.email || '',
        phone: latestExecution.input?.phone || '',
        issue: latestExecution.input?.issue || '',
        practiceArea: latestExecution.input?.practiceArea || '',
        location: latestExecution.input?.location || '',
        notes: latestExecution.input?.notes || '',
        callId: call.id,
        assistantId: call.assistant_id,
        timestamp: latestExecution.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.error('VapiEmissionsService: Error extracting dossier data', error);
      return null;
    }
  }

  // Private helper methods

  /**
   * Get new transcripts from updated call
   * @private
   * @param {Object} oldCall - The previous call state
   * @param {Object} newCall - The updated call state
   * @returns {Array} - Array of new transcripts
   */
  _getNewTranscripts(oldCall, newCall) {
    const oldTranscripts = oldCall.transcripts || [];
    const newTranscripts = newCall.transcripts || [];

    // Find transcripts that are in newTranscripts but not in oldTranscripts
    return newTranscripts.filter(newTranscript =>
      !oldTranscripts.some(oldTranscript => oldTranscript.id === newTranscript.id)
    );
  }

  /**
   * Get new messages from updated call
   * @private
   * @param {Object} oldCall - The previous call state
   * @param {Object} newCall - The updated call state
   * @returns {Array} - Array of new messages
   */
  _getNewMessages(oldCall, newCall) {
    const oldMessages = oldCall.messages || [];
    const newMessages = newCall.messages || [];

    // Find messages that are in newMessages but not in oldMessages
    return newMessages.filter(newMessage =>
      !oldMessages.some(oldMessage => oldMessage.id === newMessage.id)
    );
  }

  /**
   * Get new tool executions from updated call
   * @private
   * @param {Object} oldCall - The previous call state
   * @param {Object} newCall - The updated call state
   * @returns {Array} - Array of new tool executions
   */
  _getNewToolExecutions(oldCall, newCall) {
    const oldExecutions = oldCall.tool_executions || [];
    const newExecutions = newCall.tool_executions || [];

    // Find executions that are in newExecutions but not in oldExecutions
    return newExecutions.filter(newExecution =>
      !oldExecutions.some(oldExecution => oldExecution.id === newExecution.id)
    );
  }
}

// Export a singleton instance
export const vapiEmissionsService = new VapiEmissionsService();
