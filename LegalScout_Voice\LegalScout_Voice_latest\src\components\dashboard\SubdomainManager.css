/* Subdomain Manager Styles */

.subdomain-manager {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.subdomain-manager-header {
  margin-bottom: 24px;
}

.subdomain-manager-header h3 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 600;
}

.subdomain-manager-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.subdomain-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.subdomain-input-group {
  display: flex;
  align-items: center;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.subdomain-input-group:focus-within {
  border-color: #4B74AA;
}

.subdomain-input-group input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: white;
}

.subdomain-input-group input.error {
  background-color: #fef2f2;
}

.subdomain-input-group input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.domain-suffix {
  padding: 12px 16px;
  background-color: #f8f9fa;
  color: #666;
  font-size: 16px;
  border-left: 1px solid #e1e5e9;
  white-space: nowrap;
}

.validation-message {
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 4px;
}

.validation-message.success {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.validation-message.error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.preview-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-section label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.preview-url {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

.preview-url a {
  color: #4B74AA;
  text-decoration: none;
  font-family: monospace;
  font-size: 14px;
}

.preview-url a:hover:not(.disabled) {
  text-decoration: underline;
}

.preview-url a.disabled {
  color: #999;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #4B74AA;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #3d5d8a;
}

.btn-primary:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: white;
  color: #4B74AA;
  border: 2px solid #4B74AA;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f8f9fa;
}

.btn-secondary:disabled {
  color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.error-message, .success-message {
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.success-message {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.btn-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  padding: 0;
  margin-left: 12px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.confirmation-modal h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 18px;
}

.confirmation-modal p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.confirmation-modal .warning {
  background-color: #fef3cd;
  color: #856404;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* Dark theme support */
.dark .subdomain-manager {
  background: #1a1a1a;
  color: white;
}

.dark .subdomain-manager-header h3 {
  color: white;
}

.dark .subdomain-manager-header p {
  color: #ccc;
}

.dark .subdomain-input-group {
  border-color: #333;
  background: #2a2a2a;
}

.dark .subdomain-input-group input {
  background: #2a2a2a;
  color: white;
}

.dark .domain-suffix {
  background-color: #333;
  color: #ccc;
  border-left-color: #444;
}

.dark .preview-url {
  background-color: #2a2a2a;
  border-color: #333;
}

.dark .confirmation-modal {
  background: #1a1a1a;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .subdomain-manager {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
  }
  
  .confirmation-modal {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
