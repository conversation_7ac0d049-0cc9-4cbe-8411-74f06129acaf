#!/usr/bin/env node
/**
 * Check Vapi MCP configuration
 *
 * This script checks if the Vapi MCP server is properly configured in the .cursor/mcp.json file.
 *
 * Usage:
 *   node scripts/check-vapi-mcp-config.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the .cursor/mcp.json file
const mcpConfigPath = path.resolve(__dirname, '..', '.cursor', 'mcp.json');

console.log('Checking Vapi MCP configuration...');
console.log('MCP config path:', mcpConfigPath);

try {
  // Read the MCP configuration file
  const mcpConfigContent = fs.readFileSync(mcpConfigPath, 'utf8');
  console.log('MCP config content:', mcpConfigContent);
  
  // Parse the MCP configuration
  const mcpConfig = JSON.parse(mcpConfigContent);
  
  // Check if the Vapi MCP server is configured
  if (mcpConfig.servers && mcpConfig.servers['vapi-mcp-server']) {
    console.log('✅ Vapi MCP server is configured');
    
    // Check if the Vapi MCP server has the correct configuration
    const vapiMcpServer = mcpConfig.servers['vapi-mcp-server'];
    
    if (vapiMcpServer.command === 'npx' && 
        Array.isArray(vapiMcpServer.args) && 
        vapiMcpServer.args.includes('@vapi-ai/mcp-server')) {
      console.log('✅ Vapi MCP server has the correct command and arguments');
    } else {
      console.log('❌ Vapi MCP server has incorrect command or arguments');
      console.log('Expected: command="npx", args=["-y", "@vapi-ai/mcp-server"]');
      console.log('Actual: command="' + vapiMcpServer.command + '", args=' + JSON.stringify(vapiMcpServer.args));
    }
    
    if (vapiMcpServer.env && vapiMcpServer.env.VAPI_TOKEN) {
      console.log('✅ Vapi MCP server has the API key configured');
      console.log('API Key:', vapiMcpServer.env.VAPI_TOKEN.substring(0, 4) + '...' + vapiMcpServer.env.VAPI_TOKEN.substring(vapiMcpServer.env.VAPI_TOKEN.length - 4));
    } else {
      console.log('❌ Vapi MCP server is missing the API key');
    }
  } else {
    console.log('❌ Vapi MCP server is not configured');
  }
} catch (error) {
  console.error('❌ Error reading or parsing MCP configuration:', error);
}
