/**
 * User/Firm Separation Service
 * 
 * Properly separates user identity from firm/practice information
 * and assistant configuration to prevent data contamination.
 */

import { supabase } from '../lib/supabase';

export class UserFirmSeparationService {
  constructor() {
    this.userProfiles = new Map();
    this.firmProfiles = new Map();
  }

  /**
   * Get or create proper user profile
   * @param {Object} user - Authenticated user object
   * @returns {Object} Clean user profile
   */
  async getUserProfile(user) {
    try {
      // Check if we have a cached clean profile
      if (this.userProfiles.has(user.id)) {
        return this.userProfiles.get(user.id);
      }

      // Get user profile from database
      const { data: userProfile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('[UserFirmSeparation] Error fetching user profile:', error);
      }

      // Create clean user profile if doesn't exist
      if (!userProfile) {
        const cleanProfile = await this.createCleanUserProfile(user);
        this.userProfiles.set(user.id, cleanProfile);
        return cleanProfile;
      }

      this.userProfiles.set(user.id, userProfile);
      return userProfile;

    } catch (error) {
      console.error('[UserFirmSeparation] Error in getUserProfile:', error);
      return this.createFallbackUserProfile(user);
    }
  }

  /**
   * Get or create firm profile for user
   * @param {string} userId - User ID
   * @param {Object} firmData - Optional firm data for creation
   * @returns {Object} Firm profile
   */
  async getFirmProfile(userId, firmData = null) {
    try {
      // Check cache first
      if (this.firmProfiles.has(userId)) {
        return this.firmProfiles.get(userId);
      }

      // Get firm profile from database
      const { data: firmProfile, error } = await supabase
        .from('firm_profiles')
        .select('*')
        .eq('owner_user_id', userId)
        .eq('is_primary', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('[UserFirmSeparation] Error fetching firm profile:', error);
      }

      // Create firm profile if doesn't exist
      if (!firmProfile) {
        const newFirmProfile = await this.createFirmProfile(userId, firmData);
        this.firmProfiles.set(userId, newFirmProfile);
        return newFirmProfile;
      }

      this.firmProfiles.set(userId, firmProfile);
      return firmProfile;

    } catch (error) {
      console.error('[UserFirmSeparation] Error in getFirmProfile:', error);
      return this.createFallbackFirmProfile(userId);
    }
  }

  /**
   * Create clean user profile
   * @param {Object} user - Authenticated user
   * @returns {Object} Created user profile
   */
  async createCleanUserProfile(user) {
    const userProfile = {
      user_id: user.id,
      email: user.email,
      full_name: user.user_metadata?.full_name || this.extractNameFromEmail(user.email),
      avatar_url: user.user_metadata?.avatar_url || null,
      phone: user.user_metadata?.phone || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .insert(userProfile)
        .select()
        .single();

      if (error) {
        console.error('[UserFirmSeparation] Error creating user profile:', error);
        return userProfile; // Return local version
      }

      console.log('[UserFirmSeparation] Created clean user profile:', data.user_id);
      return data;

    } catch (error) {
      console.error('[UserFirmSeparation] Database error creating user profile:', error);
      return userProfile;
    }
  }

  /**
   * Create firm profile
   * @param {string} userId - User ID
   * @param {Object} firmData - Firm data
   * @returns {Object} Created firm profile
   */
  async createFirmProfile(userId, firmData = null) {
    // Get user profile to extract name for firm
    const userProfile = await this.getUserProfile({ id: userId });
    
    // Determine firm name based on user
    const firmName = this.determineFirmName(userProfile, firmData);
    
    const firmProfile = {
      owner_user_id: userId,
      firm_name: firmName,
      practice_areas: firmData?.practice_areas || ['General Practice'],
      address: firmData?.address || null,
      phone: firmData?.phone || null,
      website: firmData?.website || null,
      logo_url: firmData?.logo_url || null,
      primary_color: firmData?.primary_color || '#4B74AA',
      secondary_color: firmData?.secondary_color || '#2C3E50',
      is_primary: true,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    try {
      const { data, error } = await supabase
        .from('firm_profiles')
        .insert(firmProfile)
        .select()
        .single();

      if (error) {
        console.error('[UserFirmSeparation] Error creating firm profile:', error);
        return firmProfile; // Return local version
      }

      console.log('[UserFirmSeparation] Created firm profile:', data.firm_name);
      return data;

    } catch (error) {
      console.error('[UserFirmSeparation] Database error creating firm profile:', error);
      return firmProfile;
    }
  }

  /**
   * Determine appropriate firm name for user
   * @param {Object} userProfile - User profile
   * @param {Object} firmData - Optional firm data
   * @returns {string} Firm name
   */
  determineFirmName(userProfile, firmData) {
    // If firm data provided, use it
    if (firmData?.firm_name) {
      return firmData.firm_name;
    }

    // Known user mappings
    const knownFirmMappings = {
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'LegalScout',
      '<EMAIL>': 'Network Legal'
    };

    if (knownFirmMappings[userProfile.email]) {
      return knownFirmMappings[userProfile.email];
    }

    // Generate firm name from user name
    const userName = userProfile.full_name || userProfile.email.split('@')[0];
    return `${userName} Law Firm`;
  }

  /**
   * Generate clean assistant configuration
   * @param {Object} userProfile - User profile
   * @param {Object} firmProfile - Firm profile
   * @param {Object} customizations - Optional customizations
   * @returns {Object} Assistant configuration
   */
  generateAssistantConfig(userProfile, firmProfile, customizations = {}) {
    const assistantName = customizations.assistant_name || 'Scout';
    const firmName = firmProfile.firm_name;

    return {
      name: `${firmName} Legal Assistant`,
      firstMessage: customizations.welcome_message || 
        `Hello! I'm ${assistantName} from ${firmName}. How can I help you with your legal needs today?`,
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: "openai",
        model: customizations.ai_model || "gpt-4o",
        messages: [
          {
            role: "system",
            content: customizations.instructions || 
              `You are ${assistantName}, a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.`
          }
        ]
      },
      voice: {
        provider: customizations.voice_provider || "openai",
        voiceId: customizations.voice_id || "alloy"
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      }
    };
  }

  /**
   * Clean up contaminated data
   * @param {string} userId - User ID to clean
   */
  async cleanupContaminatedData(userId) {
    console.log('[UserFirmSeparation] Cleaning up contaminated data for user:', userId);

    try {
      // Clear caches
      this.userProfiles.delete(userId);
      this.firmProfiles.delete(userId);

      // Get fresh user profile
      const userProfile = await this.getUserProfile({ id: userId });
      
      // Get or create proper firm profile
      const firmProfile = await this.getFirmProfile(userId);

      console.log('[UserFirmSeparation] Cleanup complete:', {
        user: userProfile.email,
        firm: firmProfile.firm_name
      });

      return { userProfile, firmProfile };

    } catch (error) {
      console.error('[UserFirmSeparation] Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Extract name from email
   * @param {string} email - Email address
   * @returns {string} Extracted name
   */
  extractNameFromEmail(email) {
    const localPart = email.split('@')[0];
    return localPart
      .replace(/[._]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Create fallback user profile
   * @param {Object} user - User object
   * @returns {Object} Fallback profile
   */
  createFallbackUserProfile(user) {
    return {
      user_id: user.id,
      email: user.email,
      full_name: user.user_metadata?.full_name || this.extractNameFromEmail(user.email),
      avatar_url: null,
      phone: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_fallback: true
    };
  }

  /**
   * Create fallback firm profile
   * @param {string} userId - User ID
   * @returns {Object} Fallback firm profile
   */
  createFallbackFirmProfile(userId) {
    return {
      owner_user_id: userId,
      firm_name: 'LegalScout',
      practice_areas: ['General Practice'],
      primary_color: '#4B74AA',
      secondary_color: '#2C3E50',
      is_primary: true,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_fallback: true
    };
  }
}

// Export singleton instance
export const userFirmSeparationService = new UserFirmSeparationService();
