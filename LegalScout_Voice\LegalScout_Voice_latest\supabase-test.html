<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Test</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .success {
      color: green;
      font-weight: bold;
    }
    .error {
      color: red;
      font-weight: bold;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    input {
      padding: 8px;
      margin: 5px 0;
      width: 100%;
      box-sizing: border-box;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Supabase Connection Test</h1>
  
  <div class="container">
    <h2>Configuration</h2>
    <div>
      <label for="supabaseUrl">Supabase URL:</label>
      <input type="text" id="supabaseUrl" value="https://utopqxsvudgrtiwenlzl.supabase.co">
    </div>
    <div>
      <label for="supabaseKey">Supabase Anon Key:</label>
      <input type="text" id="supabaseKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs">
    </div>
  </div>
  
  <div class="container">
    <h2>Test Connection</h2>
    <button id="testConnection">Test Connection</button>
    <button id="listTables">List Tables</button>
    <button id="queryAttorneys">Query Attorneys Table</button>
    <div id="connectionResult"></div>
  </div>
  
  <div class="container">
    <h2>Results</h2>
    <pre id="results">No results yet</pre>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get DOM elements
      const testConnectionBtn = document.getElementById('testConnection');
      const listTablesBtn = document.getElementById('listTables');
      const queryAttorneysBtn = document.getElementById('queryAttorneys');
      const connectionResult = document.getElementById('connectionResult');
      const resultsElement = document.getElementById('results');
      
      // Create Supabase client
      function createClient() {
        const supabaseUrl = document.getElementById('supabaseUrl').value;
        const supabaseKey = document.getElementById('supabaseKey').value;
        
        if (!supabaseUrl || !supabaseKey) {
          connectionResult.innerHTML = '<span class="error">Please enter Supabase URL and key</span>';
          return null;
        }
        
        try {
          return supabase.createClient(supabaseUrl, supabaseKey);
        } catch (error) {
          connectionResult.innerHTML = `<span class="error">Error creating client: ${error.message}</span>`;
          return null;
        }
      }
      
      // Test connection
      testConnectionBtn.addEventListener('click', async function() {
        connectionResult.innerHTML = 'Testing connection...';
        resultsElement.textContent = 'Testing...';
        
        const client = createClient();
        if (!client) return;
        
        try {
          // Simple health check
          const { data, error } = await client.rpc('list_tables');
          
          if (error) throw error;
          
          connectionResult.innerHTML = '<span class="success">Connection successful!</span>';
          resultsElement.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          connectionResult.innerHTML = `<span class="error">Connection failed: ${error.message}</span>`;
          resultsElement.textContent = JSON.stringify(error, null, 2);
        }
      });
      
      // List tables
      listTablesBtn.addEventListener('click', async function() {
        connectionResult.innerHTML = 'Listing tables...';
        resultsElement.textContent = 'Fetching...';
        
        const client = createClient();
        if (!client) return;
        
        try {
          const { data, error } = await client.rpc('list_tables');
          
          if (error) throw error;
          
          connectionResult.innerHTML = '<span class="success">Tables retrieved successfully!</span>';
          resultsElement.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          connectionResult.innerHTML = `<span class="error">Failed to list tables: ${error.message}</span>`;
          resultsElement.textContent = JSON.stringify(error, null, 2);
        }
      });
      
      // Query attorneys table
      queryAttorneysBtn.addEventListener('click', async function() {
        connectionResult.innerHTML = 'Querying attorneys table...';
        resultsElement.textContent = 'Fetching...';
        
        const client = createClient();
        if (!client) return;
        
        try {
          const { data, error } = await client
            .from('attorneys')
            .select('*')
            .limit(5);
          
          if (error) throw error;
          
          connectionResult.innerHTML = '<span class="success">Attorneys data retrieved successfully!</span>';
          resultsElement.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          connectionResult.innerHTML = `<span class="error">Failed to query attorneys: ${error.message}</span>`;
          resultsElement.textContent = JSON.stringify(error, null, 2);
        }
      });
    });
  </script>
</body>
</html>
