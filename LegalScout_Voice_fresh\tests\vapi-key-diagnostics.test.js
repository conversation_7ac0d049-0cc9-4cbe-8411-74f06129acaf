/**
 * Comprehensive Vapi API Key Diagnostics Tests
 * 
 * These tests diagnose why the call feature works locally but fails with 401 in production.
 * Focus: Environment variable handling, API key selection, and request headers.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock environment variables for testing
const mockEnvVars = {
  local: {
    VITE_VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
    VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
    VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564',
    NODE_ENV: 'development'
  },
  production: {
    VITE_VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
    VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
    VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564',
    NODE_ENV: 'production'
  }
};

describe('Vapi API Key Diagnostics', () => {
  let originalEnv;
  let originalImportMeta;
  let originalWindow;
  let originalFetch;

  beforeEach(() => {
    // Save original environment
    originalEnv = process.env;
    originalImportMeta = globalThis.import?.meta;
    originalWindow = globalThis.window;
    originalFetch = globalThis.fetch;

    // Mock fetch to capture requests
    globalThis.fetch = vi.fn();
    
    // Clear modules cache to ensure fresh imports
    vi.resetModules();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    globalThis.import = { meta: originalImportMeta };
    globalThis.window = originalWindow;
    globalThis.fetch = originalFetch;
    vi.clearAllMocks();
  });

  describe('Environment Variable Detection', () => {
    test('should detect environment variables in local development', async () => {
      // Setup local environment
      process.env = { ...mockEnvVars.local };
      globalThis.import = {
        meta: {
          env: mockEnvVars.local
        }
      };

      const { getVapiApiKey } = await import('../src/config/vapiConfig.js');
      
      const publicKey = getVapiApiKey('client');
      const secretKey = getVapiApiKey('server');

      expect(publicKey).toBe('310f0d43-27c2-47a5-a76d-e55171d024f7');
      expect(secretKey).toBe('6734febc-fc65-4669-93b0-929b31ff6564');
    });

    test('should detect environment variables in production', async () => {
      // Setup production environment
      process.env = { ...mockEnvVars.production };
      globalThis.import = {
        meta: {
          env: mockEnvVars.production
        }
      };

      const { getVapiApiKey } = await import('../src/config/vapiConfig.js');
      
      const publicKey = getVapiApiKey('client');
      const secretKey = getVapiApiKey('server');

      expect(publicKey).toBe('310f0d43-27c2-47a5-a76d-e55171d024f7');
      expect(secretKey).toBe('6734febc-fc65-4669-93b0-929b31ff6564');
    });

    test('should fallback to hardcoded values when env vars missing', async () => {
      // Setup environment with missing variables
      process.env = {};
      globalThis.import = {
        meta: {
          env: {}
        }
      };

      const { getVapiApiKey } = await import('../src/config/vapiConfig.js');
      
      const publicKey = getVapiApiKey('client');
      const secretKey = getVapiApiKey('server');

      // Should fallback to hardcoded values
      expect(publicKey).toBe('310f0d43-27c2-47a5-a76d-e55171d024f7');
      expect(secretKey).toBe('6734febc-fc65-4669-93b0-929b31ff6564');
    });
  });

  describe('API Key Selection Logic', () => {
    test('should use correct key for phone number requests (server operation)', async () => {
      process.env = { ...mockEnvVars.local };
      globalThis.import = {
        meta: {
          env: mockEnvVars.local
        }
      };

      // Mock successful response
      globalThis.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([{ id: 'test-phone-1', number: '+1234567890' }])
      });

      const { default: vapiMcpService } = await import('../src/services/vapiMcpService.js');
      
      // Force direct API mode to test key usage
      vapiMcpService.useDirect = true;
      vapiMcpService.directApiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Secret key
      
      await vapiMcpService.listPhoneNumbers();

      // Verify the request was made with the secret key
      expect(globalThis.fetch).toHaveBeenCalledWith(
        'https://api.vapi.ai/phone-number',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564'
          })
        })
      );
    });

    test('should detect wrong key usage (public key for server operations)', async () => {
      process.env = { ...mockEnvVars.local };
      globalThis.import = {
        meta: {
          env: mockEnvVars.local
        }
      };

      // Mock 401 response (wrong key)
      globalThis.fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: () => Promise.resolve('{"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}')
      });

      const { default: vapiMcpService } = await import('../src/services/vapiMcpService.js');
      
      // Force direct API mode with WRONG key (public instead of secret)
      vapiMcpService.useDirect = true;
      vapiMcpService.directApiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // Public key (WRONG)
      
      await expect(vapiMcpService.listPhoneNumbers()).rejects.toThrow('Direct API error: 401');

      // Verify the request was made with the wrong key
      expect(globalThis.fetch).toHaveBeenCalledWith(
        'https://api.vapi.ai/phone-number',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7' // Wrong key
          })
        })
      );
    });
  });

  describe('Production vs Local Environment Differences', () => {
    test('should identify key differences between local and production', async () => {
      const results = {};

      // Test local environment
      process.env = { ...mockEnvVars.local };
      globalThis.import = { meta: { env: mockEnvVars.local } };
      
      const localConfig = await import('../src/config/vapiConfig.js');
      results.local = {
        publicKey: localConfig.getVapiApiKey('client'),
        secretKey: localConfig.getVapiApiKey('server'),
        environment: 'development'
      };

      // Clear module cache
      vi.resetModules();

      // Test production environment
      process.env = { ...mockEnvVars.production };
      globalThis.import = { meta: { env: mockEnvVars.production } };
      
      const prodConfig = await import('../src/config/vapiConfig.js');
      results.production = {
        publicKey: prodConfig.getVapiApiKey('client'),
        secretKey: prodConfig.getVapiApiKey('server'),
        environment: 'production'
      };

      // Keys should be the same in both environments
      expect(results.local.publicKey).toBe(results.production.publicKey);
      expect(results.local.secretKey).toBe(results.production.secretKey);

      console.log('Environment comparison:', results);
    });

    test('should simulate production deployment environment variable access', async () => {
      // Simulate Vercel production environment where import.meta.env might be different
      process.env = {
        NODE_ENV: 'production',
        // Simulate missing VITE_ prefixed vars in production
        VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564',
        VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
        VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7'
      };

      // Simulate production build where import.meta.env might be empty or different
      globalThis.import = {
        meta: {
          env: {
            MODE: 'production',
            // VITE_ vars might not be available in production build
          }
        }
      };

      const { getVapiApiKey } = await import('../src/config/vapiConfig.js');
      
      const publicKey = getVapiApiKey('client');
      const secretKey = getVapiApiKey('server');

      // Should still work with fallbacks
      expect(publicKey).toBeTruthy();
      expect(secretKey).toBeTruthy();
      
      console.log('Production simulation - Public key:', publicKey?.substring(0, 8) + '...');
      console.log('Production simulation - Secret key:', secretKey?.substring(0, 8) + '...');
    });
  });

  describe('Request Header Analysis', () => {
    test('should capture and analyze actual request headers', async () => {
      process.env = { ...mockEnvVars.local };
      globalThis.import = { meta: { env: mockEnvVars.local } };

      // Mock fetch to capture request details
      const requestCapture = [];
      globalThis.fetch.mockImplementation((url, options) => {
        requestCapture.push({ url, options });
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([])
        });
      });

      const { default: vapiMcpService } = await import('../src/services/vapiMcpService.js');
      
      vapiMcpService.useDirect = true;
      vapiMcpService.directApiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      
      await vapiMcpService.listPhoneNumbers();

      expect(requestCapture).toHaveLength(1);
      const request = requestCapture[0];
      
      console.log('Captured request:', {
        url: request.url,
        method: request.options.method,
        headers: request.options.headers,
        authHeader: request.options.headers?.Authorization
      });

      // Verify correct headers
      expect(request.options.headers).toHaveProperty('Authorization');
      expect(request.options.headers.Authorization).toBe('Bearer 6734febc-fc65-4669-93b0-929b31ff6564');
      expect(request.options.headers['Content-Type']).toBe('application/json');
    });
  });

  describe('Service Integration Tests', () => {
    test('should test CallManagementSection phone number loading', async () => {
      process.env = { ...mockEnvVars.local };
      globalThis.import = { meta: { env: mockEnvVars.local } };

      // Mock successful phone numbers response
      globalThis.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          { id: 'phone-1', number: '+1234567890' },
          { id: 'phone-2', number: '+0987654321' }
        ])
      });

      const { default: vapiMcpService } = await import('../src/services/vapiMcpService.js');
      
      // Ensure connection and test
      await vapiMcpService.ensureConnection();
      const phoneNumbers = await vapiMcpService.listPhoneNumbers();

      expect(phoneNumbers).toHaveLength(2);
      expect(phoneNumbers[0]).toHaveProperty('id', 'phone-1');
    });

    test('should test error handling for 401 responses', async () => {
      process.env = { ...mockEnvVars.local };
      globalThis.import = { meta: { env: mockEnvVars.local } };

      // Mock 401 response
      globalThis.fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: () => Promise.resolve('{"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}')
      });

      const { default: vapiMcpService } = await import('../src/services/vapiMcpService.js');
      
      vapiMcpService.useDirect = true;
      vapiMcpService.directApiKey = 'wrong-key';
      
      await expect(vapiMcpService.listPhoneNumbers()).rejects.toThrow(/401.*Invalid Key/);
    });
  });
});
