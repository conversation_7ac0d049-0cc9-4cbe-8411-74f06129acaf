// Simple script to test Supabase connection
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY || process.env.REACT_APP_SUPABASE_KEY;

// Fallback values for development
const FALLBACK_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const FALLBACK_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs';

// Log environment variables
console.log('Environment variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl);
console.log('VITE_SUPABASE_KEY:', supabaseKey ? `${supabaseKey.substring(0, 10)}...${supabaseKey.substring(supabaseKey.length - 10)}` : 'missing');

// Check for placeholder values
let finalUrl = supabaseUrl;
let finalKey = supabaseKey;

if (!finalUrl || finalUrl === 'your-supabase-url') {
  console.warn('Using fallback Supabase URL');
  finalUrl = FALLBACK_SUPABASE_URL;
}

if (!finalKey || finalKey === 'your-anon-key') {
  console.warn('Using fallback Supabase key');
  finalKey = FALLBACK_SUPABASE_KEY;
}

console.log('Final Supabase URL:', finalUrl);
console.log('Final Supabase key:', finalKey ? `${finalKey.substring(0, 10)}...${finalKey.substring(finalKey.length - 10)}` : 'missing');

// Create Supabase client
try {
  console.log('Creating Supabase client...');
  const supabase = createClient(finalUrl, finalKey);

  // Test connection
  console.log('Testing connection...');
  supabase
    .from('attorneys')
    .select('*')
    .limit(1)
    .then(({ data, error }) => {
      if (error) {
        console.error('Error querying attorneys table:', error);
      } else {
        console.log('Connection successful!');
        console.log('Data:', data);
      }
    })
    .catch(error => {
      console.error('Unexpected error:', error);
    });
} catch (error) {
  console.error('Error creating Supabase client:', error);
}
