/**
 * Module Resolver
 * 
 * Resolves the dynamic/static import conflicts by providing a unified
 * module loading strategy that works in both development and production.
 * 
 * This addresses the Vite build warnings about modules being both
 * dynamically and statically imported.
 */

// Cache for resolved modules
const moduleCache = new Map();

/**
 * Unified module resolver that handles both static and dynamic imports
 * @param {string} modulePath - The module path to resolve
 * @param {Object} options - Resolution options
 * @returns {Promise} - The resolved module
 */
export async function resolveModule(modulePath, options = {}) {
  const { 
    preferStatic = false, 
    useCache = true, 
    fallbackToStatic = true 
  } = options;

  // Check cache first
  if (useCache && moduleCache.has(modulePath)) {
    return moduleCache.get(modulePath);
  }

  let module;
  
  try {
    if (preferStatic) {
      // Try static import first
      module = await getStaticModule(modulePath);
    } else {
      // Use dynamic import
      module = await import(modulePath);
    }
  } catch (error) {
    if (fallbackToStatic && !preferStatic) {
      // Fallback to static import
      try {
        module = await getStaticModule(modulePath);
      } catch (staticError) {
        console.error(`[ModuleResolver] Failed to resolve ${modulePath}:`, error, staticError);
        throw error;
      }
    } else {
      console.error(`[ModuleResolver] Failed to resolve ${modulePath}:`, error);
      throw error;
    }
  }

  // Cache the result
  if (useCache) {
    moduleCache.set(modulePath, module);
  }

  return module;
}

/**
 * Gets modules using static imports for commonly conflicted modules
 */
async function getStaticModule(modulePath) {
  // Normalize the path
  const normalizedPath = modulePath.replace(/^\.\//, '').replace(/^\.\.\//, '');
  
  switch (normalizedPath) {
    case 'services/vapiAssistantService':
    case '../../services/vapiAssistantService':
    case '../services/vapiAssistantService':
      return import('../services/vapiAssistantService');

    case 'services/EnhancedVapiMcpService':
    case '../../services/EnhancedVapiMcpService':
    case '../services/EnhancedVapiMcpService':
      return import('../services/EnhancedVapiMcpService');

    case 'services/VapiDirectApiService':
    case '../../services/VapiDirectApiService':
    case '../services/VapiDirectApiService':
      return import('../services/VapiDirectApiService');

    case 'services/vapiMcpService':
    case '../../services/vapiMcpService':
    case '../services/vapiMcpService':
      return import('../services/vapiMcpService');

    case 'config/vapiConfig':
    case '../../config/vapiConfig':
    case '../config/vapiConfig':
      return import('../config/vapiConfig');

    case 'config/mcp.config':
    case '../../config/mcp.config':
    case '../config/mcp.config':
      return import('../config/mcp.config');

    case 'lib/supabase':
    case '../../lib/supabase':
    case '../lib/supabase':
      return import('../lib/supabase');

    case 'utils/loggerUtils':
    case '../../utils/loggerUtils':
    case '../utils/loggerUtils':
      return import('./loggerUtils');
      
    default:
      // For other modules, use dynamic import
      return import(modulePath);
  }
}

/**
 * Specialized resolvers for specific service types
 */
export const serviceResolvers = {
  /**
   * Resolve Vapi services with proper fallback chain
   */
  async vapiService(serviceName, options = {}) {
    const serviceMap = {
      'vapiAssistantService': '../services/vapiAssistantService',
      'enhancedVapiMcpService': '../services/EnhancedVapiMcpService',
      'vapiDirectApiService': '../services/VapiDirectApiService',
      'vapiMcpService': '../services/vapiMcpService'
    };
    
    const modulePath = serviceMap[serviceName];
    if (!modulePath) {
      throw new Error(`Unknown Vapi service: ${serviceName}`);
    }
    
    return resolveModule(modulePath, { preferStatic: true, ...options });
  },

  /**
   * Resolve configuration modules
   */
  async config(configName, options = {}) {
    const configMap = {
      'vapiConfig': '../config/vapiConfig',
      'mcpConfig': '../config/mcp.config'
    };
    
    const modulePath = configMap[configName];
    if (!modulePath) {
      throw new Error(`Unknown config: ${configName}`);
    }
    
    return resolveModule(modulePath, { preferStatic: true, ...options });
  },

  /**
   * Resolve utility modules
   */
  async utils(utilName, options = {}) {
    const utilMap = {
      'loggerUtils': './loggerUtils',
      'supabase': '../lib/supabase'
    };
    
    const modulePath = utilMap[utilName];
    if (!modulePath) {
      throw new Error(`Unknown utility: ${utilName}`);
    }
    
    return resolveModule(modulePath, { preferStatic: true, ...options });
  }
};

/**
 * Clear the module cache (useful for development)
 */
export function clearModuleCache() {
  moduleCache.clear();
  console.log('[ModuleResolver] Module cache cleared');
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  return {
    size: moduleCache.size,
    modules: Array.from(moduleCache.keys())
  };
}

/**
 * Preload commonly used modules to reduce runtime overhead
 */
export async function preloadCommonModules() {
  const commonModules = [
    '../services/vapiAssistantService',
    '../services/EnhancedVapiMcpService',
    '../config/vapiConfig',
    '../lib/supabase'
  ];
  
  console.log('[ModuleResolver] Preloading common modules...');
  
  const preloadPromises = commonModules.map(async (modulePath) => {
    try {
      await resolveModule(modulePath, { preferStatic: true });
      console.log(`[ModuleResolver] ✅ Preloaded ${modulePath}`);
    } catch (error) {
      console.warn(`[ModuleResolver] ⚠️ Failed to preload ${modulePath}:`, error);
    }
  });
  
  await Promise.allSettled(preloadPromises);
  console.log('[ModuleResolver] Common modules preloading complete');
}

export default {
  resolveModule,
  serviceResolvers,
  clearModuleCache,
  getCacheStats,
  preloadCommonModules
};
