import React, { useState, useEffect } from 'react';
import { useCallMonitoring } from '../../hooks/useCallMonitoring';
import './LiveCallMonitor.css';

/**
 * Live Call Monitor Component
 * 
 * Provides real-time monitoring of active calls with attorney intervention capabilities
 */
const LiveCallMonitor = ({ callId, onClose, attorney }) => {
  const [interventionMode, setInterventionMode] = useState(false);
  const [notes, setNotes] = useState('');
  const [callControlUrl, setCallControlUrl] = useState(null);
  const [messageToInject, setMessageToInject] = useState('');
  const [isInjecting, setIsInjecting] = useState(false);
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);

  // Use the call monitoring hook for real-time updates
  const {
    call: callData,
    transcripts,
    status,
    loading,
    error: monitoringError,
    endCall,
    sendMessage
  } = useCallMonitoring(callId);

  useEffect(() => {
    if (callId) {
      console.log('[LiveCallMonitor] Monitoring call:', callId);
      // Generate call control URL for attorney
      generateCallControlUrl();
    }
  }, [callId]);

  // Simulate assistant speaking state based on transcript activity
  useEffect(() => {
    if (transcripts && transcripts.length > 0) {
      const lastTranscript = transcripts[transcripts.length - 1];
      const isRecentAssistantMessage = lastTranscript?.role === 'assistant' &&
        (Date.now() - new Date(lastTranscript.timestamp || 0).getTime()) < 3000; // 3 seconds

      setAssistantIsSpeaking(isRecentAssistantMessage);
      setVolumeLevel(isRecentAssistantMessage ? Math.random() * 100 : 0);
    }
  }, [transcripts]);

  const generateCallControlUrl = async () => {
    try {
      // Generate a secure token for call control
      const token = btoa(JSON.stringify({
        callId,
        attorneyId: attorney?.id,
        timestamp: Date.now(),
        expires: Date.now() + (60 * 60 * 1000) // 1 hour
      }));

      const controlUrl = `${window.location.origin}/call-control?token=${token}`;
      setCallControlUrl(controlUrl);
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to generate call control URL:', error);
    }
  };

  // VapiBlocks-inspired audio visualizer component
  const AudioVisualizer = ({ isActive, volumeLevel }) => {
    const bars = Array.from({ length: 5 }, (_, i) => {
      const height = isActive ? Math.random() * 40 + 10 : 5;
      const delay = i * 0.1;

      return (
        <div
          key={i}
          className="visualizer-bar"
          style={{
            height: `${height}px`,
            animationDelay: `${delay}s`,
            opacity: isActive ? 1 : 0.3
          }}
        />
      );
    });

    return (
      <div className="audio-visualizer">
        {bars}
      </div>
    );
  };

  const handleInterventionToggle = async () => {
    try {
      if (!interventionMode) {
        // Start intervention mode
        console.log('[LiveCallMonitor] Starting intervention mode for call:', callId);
        setInterventionMode(true);
        
        // You could add logic here to notify the AI assistant that attorney is monitoring
        // or to enable special intervention features
      } else {
        // Stop intervention mode
        console.log('[LiveCallMonitor] Stopping intervention mode for call:', callId);
        setInterventionMode(false);
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to toggle intervention mode:', error);
    }
  };

  const handleEndCall = async () => {
    try {
      console.log('[LiveCallMonitor] Attorney ending call:', callId);

      // End the call using the hook's endCall function
      const success = await endCall();

      if (success) {
        // Close the monitor
        onClose();
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to end call:', error);
    }
  };

  const handleTakeOver = () => {
    // Open call control in new window/tab
    if (callControlUrl) {
      window.open(callControlUrl, '_blank', 'width=800,height=600');
    }
  };

  const copyControlUrl = () => {
    if (callControlUrl) {
      navigator.clipboard.writeText(callControlUrl);
      // You could add a toast notification here
    }
  };

  const handleInjectMessage = async () => {
    if (!messageToInject.trim() || isInjecting) return;

    setIsInjecting(true);
    try {
      console.log('[LiveCallMonitor] Injecting message:', messageToInject);

      // Use the sendMessage function from the hook
      const success = await sendMessage(messageToInject);

      if (success) {
        console.log('[LiveCallMonitor] Message injected successfully');
        setMessageToInject(''); // Clear the input
      } else {
        console.error('[LiveCallMonitor] Failed to inject message');
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Error injecting message:', error);
    } finally {
      setIsInjecting(false);
    }
  };

  const handleMuteAssistant = async () => {
    try {
      if (!callData?.monitor?.controlUrl) return;

      const response = await fetch(callData.monitor.controlUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'control',
          control: 'mute-assistant'
        })
      });

      if (response.ok) {
        console.log('[LiveCallMonitor] Assistant muted');
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Error muting assistant:', error);
    }
  };

  const handleUnmuteAssistant = async () => {
    try {
      if (!callData?.monitor?.controlUrl) return;

      const response = await fetch(callData.monitor.controlUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'control',
          control: 'unmute-assistant'
        })
      });

      if (response.ok) {
        console.log('[LiveCallMonitor] Assistant unmuted');
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Error unmuting assistant:', error);
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="live-call-monitor">
      <div className="monitor-header">
        <h3>Live Call Monitor</h3>
        <div className="call-info">
          <span className="call-id">Call ID: {callId}</span>
          <span className={`connection-status ${!loading && !monitoringError ? 'connected' : 'disconnected'}`}>
            {!loading && !monitoringError ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
        </div>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      {monitoringError && (
        <div className="error-message">
          Monitoring Error: {monitoringError}
        </div>
      )}

      <div className="call-status-panel">
        <div className="status-grid">
          <div className="status-item">
            <span className="status-label">Status:</span>
            <span className={`status-value ${status || callData?.status}`}>
              {status || callData?.status || 'Unknown'}
            </span>
          </div>
          <div className="status-item">
            <span className="status-label">Duration:</span>
            <span className="status-value">
              {formatDuration(callData?.duration)}
            </span>
          </div>
          <div className="status-item">
            <span className="status-label">Client:</span>
            <span className="status-value">
              {callData?.customer?.number || 'Unknown'}
            </span>
          </div>
          <div className="status-item">
            <span className="status-label">Assistant:</span>
            <span className="status-value assistant-status">
              <AudioVisualizer isActive={assistantIsSpeaking} volumeLevel={volumeLevel} />
              {callData?.assistant?.name || attorney?.firm_name || 'AI Assistant'}
              {assistantIsSpeaking && <span className="speaking-indicator">Speaking...</span>}
            </span>
          </div>
        </div>
      </div>

      <div className="transcript-panel">
        <div className="transcript-header">
          <h4>Live Transcript</h4>
          <div className="transcript-status">
            <span className={`connection-dot ${!loading && !monitoringError ? 'connected' : 'disconnected'}`}></span>
            <span className="status-text">
              {!loading && !monitoringError ? 'Live' : 'Disconnected'}
            </span>
          </div>
        </div>
        <div className="transcript-content">
          {transcripts && transcripts.length > 0 ? (
            transcripts.map((entry, index) => (
              <div key={index} className={`transcript-entry ${entry.role} ${index === transcripts.length - 1 ? 'latest' : ''}`}>
                <div className="transcript-meta">
                  <span className="speaker">
                    {entry.role === 'assistant' ? '🤖 AI' : '👤 Client'}
                  </span>
                  <span className="timestamp">
                    {new Date(entry.timestamp || Date.now()).toLocaleTimeString()}
                  </span>
                </div>
                <div className="message">{entry.message || entry.text}</div>
              </div>
            ))
          ) : (
            <div className="no-transcript">
              <div className="transcript-placeholder">
                <div className="pulse-dot"></div>
                {!loading && !monitoringError ? 'Waiting for conversation to begin...' : 'Not connected to call'}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="intervention-panel">
        <h4>Attorney Controls</h4>
        
        <div className="control-buttons">
          <button
            className={`intervention-button ${interventionMode ? 'active' : ''}`}
            onClick={handleInterventionToggle}
          >
            {interventionMode ? '🔴 Stop Monitoring' : '👁️ Start Monitoring'}
          </button>

          <button
            className="takeover-button"
            onClick={handleTakeOver}
            disabled={!callControlUrl}
          >
            📞 Take Over Call
          </button>

          <button
            className="mute-button"
            onClick={handleMuteAssistant}
            disabled={!callData?.monitor?.controlUrl}
          >
            🔇 Mute AI
          </button>

          <button
            className="unmute-button"
            onClick={handleUnmuteAssistant}
            disabled={!callData?.monitor?.controlUrl}
          >
            🔊 Unmute AI
          </button>

          <button
            className="end-call-button"
            onClick={handleEndCall}
          >
            ⏹️ End Call
          </button>
        </div>

        {/* Message Injection Section */}
        <div className="message-injection-section">
          <label htmlFor="message-inject">Inject Message to Call:</label>
          <div className="message-input-group">
            <input
              id="message-inject"
              type="text"
              value={messageToInject}
              onChange={(e) => setMessageToInject(e.target.value)}
              placeholder="Type message to inject into call..."
              onKeyPress={(e) => e.key === 'Enter' && handleInjectMessage()}
              disabled={isInjecting}
            />
            <button
              onClick={handleInjectMessage}
              className="inject-button"
              disabled={!messageToInject.trim() || isInjecting}
            >
              {isInjecting ? '⏳ Sending...' : '💬 Inject'}
            </button>
          </div>
          <small>This will make the AI assistant say your message during the call</small>
        </div>

        {callControlUrl && (
          <div className="control-url-section">
            <span className="control-url-label">Call Control URL:</span>
            <div className="url-input-group">
              <input
                type="text"
                value={callControlUrl}
                readOnly
                className="control-url-input"
              />
              <button onClick={copyControlUrl} className="copy-button">
                📋 Copy
              </button>
            </div>
            <small>Share this URL to allow remote call control</small>
          </div>
        )}

        <div className="notes-section">
          <label htmlFor="call-notes">Call Notes:</label>
          <textarea
            id="call-notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this call..."
            rows={4}
          />
        </div>
      </div>
    </div>
  );
};

export default LiveCallMonitor;
