/**
 * Test Assistant Naming Fix
 * 
 * This script verifies that the assistant naming fix is working correctly
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_API_URL = 'https://api.vapi.ai';

// Test data
const CORRECT_ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';
const CORRECT_ASSISTANT_ID = '1d3471b7-8694-4844-b3ef-e05720693efc';
const EXPECTED_ASSISTANT_NAME = "Damon's Assistant";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function main() {
  console.log('🧪 Testing Assistant Naming Fix...\n');

  try {
    // Test 1: Verify Vapi assistant name
    console.log('📡 Test 1: Checking Vapi Assistant Name');
    const vapiResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!vapiResponse.ok) {
      throw new Error(`Vapi assistant not found: ${vapiResponse.status}`);
    }

    const vapiAssistant = await vapiResponse.json();
    console.log(`✅ Vapi Assistant Name: "${vapiAssistant.name}"`);
    
    if (vapiAssistant.name === EXPECTED_ASSISTANT_NAME) {
      console.log('✅ PASS: Vapi assistant name is correct');
    } else {
      console.log(`❌ FAIL: Expected "${EXPECTED_ASSISTANT_NAME}", got "${vapiAssistant.name}"`);
    }

    // Test 2: Verify database assistant name
    console.log('\n💾 Test 2: Checking Database Assistant Name');
    const { data: dbConfig, error: dbError } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_name, firm_name')
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .eq('assistant_id', CORRECT_ASSISTANT_ID)
      .single();

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    console.log(`✅ Database Assistant Name: "${dbConfig.assistant_name}"`);
    
    if (dbConfig.assistant_name === EXPECTED_ASSISTANT_NAME) {
      console.log('✅ PASS: Database assistant name is correct');
    } else {
      console.log(`❌ FAIL: Expected "${EXPECTED_ASSISTANT_NAME}", got "${dbConfig.assistant_name}"`);
    }

    // Test 3: Verify no wrong assistant configs exist
    console.log('\n🧹 Test 3: Checking for Wrong Assistant Configs');
    const { data: wrongConfigs, error: wrongError } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_id, assistant_name')
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .neq('assistant_id', CORRECT_ASSISTANT_ID);

    if (wrongError) {
      console.warn(`Warning checking wrong configs: ${wrongError.message}`);
    }

    if (!wrongConfigs || wrongConfigs.length === 0) {
      console.log('✅ PASS: No wrong assistant configs found');
    } else {
      console.log(`❌ FAIL: Found ${wrongConfigs.length} wrong config(s):`);
      wrongConfigs.forEach(config => {
        console.log(`  - Assistant ID: ${config.assistant_id}, Name: "${config.assistant_name}"`);
      });
    }

    // Test 4: Verify attorney record
    console.log('\n👨‍💼 Test 4: Checking Attorney Record');
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('current_assistant_id, vapi_assistant_id, email')
      .eq('id', CORRECT_ATTORNEY_ID)
      .single();

    if (attorneyError) {
      throw new Error(`Attorney error: ${attorneyError.message}`);
    }

    console.log(`✅ Attorney Email: ${attorney.email}`);
    console.log(`✅ Current Assistant ID: ${attorney.current_assistant_id}`);
    console.log(`✅ Vapi Assistant ID: ${attorney.vapi_assistant_id}`);

    if (attorney.current_assistant_id === CORRECT_ASSISTANT_ID) {
      console.log('✅ PASS: Attorney current_assistant_id is correct');
    } else {
      console.log(`❌ FAIL: Expected "${CORRECT_ASSISTANT_ID}", got "${attorney.current_assistant_id}"`);
    }

    // Test 5: Verify name synchronization
    console.log('\n🔄 Test 5: Checking Name Synchronization');
    const namesSynced = (vapiAssistant.name === dbConfig.assistant_name) && 
                       (dbConfig.assistant_name === EXPECTED_ASSISTANT_NAME);

    if (namesSynced) {
      console.log('✅ PASS: All names are synchronized correctly');
    } else {
      console.log('❌ FAIL: Names are not synchronized');
      console.log(`  Vapi: "${vapiAssistant.name}"`);
      console.log(`  Database: "${dbConfig.assistant_name}"`);
      console.log(`  Expected: "${EXPECTED_ASSISTANT_NAME}"`);
    }

    // Summary
    console.log('\n📊 Test Summary:');
    const tests = [
      { name: 'Vapi Assistant Name', passed: vapiAssistant.name === EXPECTED_ASSISTANT_NAME },
      { name: 'Database Assistant Name', passed: dbConfig.assistant_name === EXPECTED_ASSISTANT_NAME },
      { name: 'No Wrong Configs', passed: !wrongConfigs || wrongConfigs.length === 0 },
      { name: 'Attorney Record', passed: attorney.current_assistant_id === CORRECT_ASSISTANT_ID },
      { name: 'Name Synchronization', passed: namesSynced }
    ];

    const passedTests = tests.filter(test => test.passed).length;
    const totalTests = tests.length;

    console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);

    tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.name}`);
    });

    if (passedTests === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED! Assistant naming fix is working correctly.');
      return true;
    } else {
      console.log('\n⚠️ Some tests failed. Assistant naming may need additional fixes.');
      return false;
    }

  } catch (error) {
    console.error('❌ Test Error:', error.message);
    return false;
  }
}

// Run the test
main().then((success) => {
  if (success) {
    console.log('\n🏁 Test completed successfully!');
    process.exit(0);
  } else {
    console.log('\n💥 Test failed!');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
