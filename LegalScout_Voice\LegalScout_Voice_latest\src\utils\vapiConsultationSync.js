/**
 * Vapi Consultation Sync Utilities
 * 
 * Utilities for syncing Vapi call data with consultation records
 */

import { supabase } from '../lib/supabase';

/**
 * Sync a single Vapi call to create a consultation record
 * @param {Object} vapiCall - Vapi call object from MCP server
 * @param {string} attorneyId - Attorney ID to associate with the consultation
 * @returns {Promise<Object>} - Result of the sync operation
 */
export async function syncVapiCallToConsultation(vapiCall, attorneyId) {
  try {
    // Check if consultation already exists for this call
    const { data: existing } = await supabase
      .from('consultations')
      .select('id')
      .eq('metadata->call_id', vapiCall.id)
      .single();

    if (existing) {
      return {
        success: true,
        data: existing,
        message: 'Consultation already exists'
      };
    }

    // Create consultation record
    const consultationRecord = {
      attorney_id: attorneyId,
      client_name: 'Anonymous Client',
      client_email: null,
      client_phone: null,
      summary: generateSummaryFromVapiCall(vapiCall),
      transcript: 'Transcript not available - call ended before completion.',
      duration: calculateCallDuration(vapiCall),
      practice_area: null,
      location: null,
      location_data: {},
      metadata: {
        call_id: vapiCall.id,
        assistant_id: vapiCall.assistantId,
        vapi_status: vapiCall.status,
        vapi_end_reason: vapiCall.endedReason,
        created_at: vapiCall.createdAt,
        updated_at: vapiCall.updatedAt
      },
      status: 'new'
    };

    const { data, error } = await supabase
      .from('consultations')
      .insert(consultationRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error syncing Vapi call to consultation:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Generate a summary from a Vapi call object
 * @param {Object} vapiCall - Vapi call object
 * @returns {string} - Generated summary
 */
function generateSummaryFromVapiCall(vapiCall) {
  const duration = calculateCallDuration(vapiCall);
  const durationMinutes = Math.round(duration / 60);
  const endReason = vapiCall.endedReason || 'unknown';
  
  return `Call ended with reason: ${endReason}. Duration: ${durationMinutes} minutes. Status: ${vapiCall.status}.`;
}

/**
 * Calculate call duration from Vapi call timestamps
 * @param {Object} vapiCall - Vapi call object
 * @returns {number} - Duration in seconds
 */
function calculateCallDuration(vapiCall) {
  if (!vapiCall.createdAt || !vapiCall.updatedAt) {
    return 0;
  }
  
  const startTime = new Date(vapiCall.createdAt);
  const endTime = new Date(vapiCall.updatedAt);
  
  return Math.max(0, Math.round((endTime - startTime) / 1000));
}

/**
 * Get attorney ID from Vapi assistant ID
 * @param {string} assistantId - Vapi assistant ID
 * @returns {Promise<string|null>} - Attorney ID or null if not found
 */
export async function getAttorneyIdFromAssistantId(assistantId) {
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('id')
      .eq('vapi_assistant_id', assistantId)
      .single();

    if (error || !data) {
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error getting attorney ID from assistant ID:', error);
    return null;
  }
}

/**
 * Sync multiple Vapi calls to consultation records
 * @param {Array} vapiCalls - Array of Vapi call objects
 * @returns {Promise<Object>} - Result summary
 */
export async function syncMultipleVapiCalls(vapiCalls) {
  const results = {
    success: 0,
    errors: 0,
    details: []
  };

  for (const call of vapiCalls) {
    try {
      // Get attorney ID for this call
      const attorneyId = await getAttorneyIdFromAssistantId(call.assistantId);
      
      if (!attorneyId) {
        results.errors++;
        results.details.push({
          callId: call.id,
          status: 'error',
          error: 'No attorney found for assistant ID: ' + call.assistantId
        });
        continue;
      }

      const result = await syncVapiCallToConsultation(call, attorneyId);
      
      if (result.success) {
        results.success++;
        results.details.push({
          callId: call.id,
          status: 'success',
          consultationId: result.data.id,
          message: result.message
        });
      } else {
        results.errors++;
        results.details.push({
          callId: call.id,
          status: 'error',
          error: result.error
        });
      }
    } catch (error) {
      results.errors++;
      results.details.push({
        callId: call.id,
        status: 'error',
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Enhanced consultation record with additional computed fields
 * @param {Object} consultation - Consultation record from database
 * @returns {Object} - Enhanced consultation record
 */
export function enhanceConsultationRecord(consultation) {
  return {
    ...consultation,
    duration_formatted: consultation.duration ? `${Math.round(consultation.duration / 60)} min` : 'Unknown',
    status_display: consultation.status === 'new' ? 'New' : consultation.status,
    created_at_formatted: new Date(consultation.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  };
}
