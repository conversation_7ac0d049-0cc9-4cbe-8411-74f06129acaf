import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface KnowledgeBaseProps {
  primaryColor: string;
  secondaryColor: string;
  isDark?: boolean;
}

export const KnowledgeBase = ({ 
  primaryColor, 
  secondaryColor,
  isDark = false
}: KnowledgeBaseProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('Practice Areas');

  // Sample knowledge base categories and items - expanded with more detailed content
  const categories = [
    {
      name: 'Practice Areas',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M20 7h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2zM9 4h6v3H9V4zm11 16H4V9h16v11z"></path>
          <path d="M12 12c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"></path>
        </svg>
      ),
      items: [
        { title: 'Personal Injury', desc: 'Representation for accidents, medical malpractice, and injury claims' },
        { title: 'Family Law', desc: 'Support for divorce, custody, child support, and domestic issues' },
        { title: 'Criminal Defense', desc: 'Defense against misdemeanor and felony charges' },
        { title: 'Estate Planning', desc: 'Will preparation, trusts, and estate administration services' },
        { title: 'Business Law', desc: 'Formation, contracts, compliance, and business litigation' },
        { title: 'Immigration', desc: 'Visa applications, green cards, citizenship, and immigration defense' }
      ]
    },
    {
      name: 'Common Questions',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
      ),
      items: [
        { title: 'How long will my case take?', desc: 'Timeline depends on case complexity and court schedules' },
        { title: 'What are your fees?', desc: 'We offer various fee structures including contingency and flat-rate options' },
        { title: 'Do I need to come to your office?', desc: 'Virtual consultations are available for most matters' },
        { title: 'What documents should I bring?', desc: 'We\'ll provide a personalized checklist based on your case type' },
        { title: 'Will my case go to trial?', desc: 'Most cases settle, but we prepare every case as if it will go to trial' },
        { title: 'Can I switch attorneys?', desc: 'Yes, you have the right to change representation at any time' }
      ]
    },
    {
      name: 'Resources',
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
        </svg>
      ),
      items: [
        { title: 'Legal Guides', desc: 'Step-by-step guides for common legal situations' },
        { title: 'Case Studies', desc: 'Real examples of cases we\'ve successfully handled' },
        { title: 'Client Testimonials', desc: 'Hear from clients about their experiences with our firm' },
        { title: 'Legal Dictionary', desc: 'Common legal terms explained in plain language' },
        { title: 'Court Information', desc: 'Local court details, procedures, and etiquette' },
        { title: 'Forms & Documents', desc: 'Access important legal forms and document templates' }
      ]
    }
  ];

  // Filter items based on search query
  const filteredItems = categories.flatMap(category => 
    category.items.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.desc.toLowerCase().includes(searchQuery.toLowerCase())
    ).map(item => ({
      ...item,
      category: category.name
    }))
  );

  return (
    <div className="p-4 max-w-6xl mx-auto" style={{ 
      margin: 0, 
      padding: '10px',
      background: 'transparent',
      width: '100%',
      maxWidth: '100%'
    }}>
      {/* Header */}
      <div className="mb-4 text-center">
        <h2 
          className="text-xl font-bold mb-1"
          style={{ color: primaryColor }}
        >
          Knowledge Center
        </h2>
        <p className="text-sm opacity-70 mb-3">Find answers to common questions and explore our resources</p>
      </div>

      {/* Search */}
      <div className="mb-4 max-w-2xl mx-auto">
        <div 
          className="relative rounded-full overflow-hidden shadow-sm"
          style={{ backgroundColor: isDark ? 'rgba(51,51,51,0.8)' : 'rgba(240,240,240,0.8)' }}
        >
          <input
            type="text"
            placeholder="Search knowledge base..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full p-4 pl-12 rounded-full"
            style={{ 
              backgroundColor: 'transparent',
              color: isDark ? 'white' : 'black',
              border: 'none',
              fontSize: '16px'
            }}
          />
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            <svg 
              className="w-4 h-4" 
              fill="none" 
              stroke={primaryColor} 
              viewBox="0 0 24 24"
              style={{ width: '16px', height: '16px' }}
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={1.5} 
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
              />
            </svg>
          </div>
          {searchQuery && (
            <button 
              className="absolute right-4 top-1/2 transform -translate-y-1/2"
              onClick={() => setSearchQuery('')}
              aria-label="Clear search"
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke={isDark ? "white" : "black"} 
                viewBox="0 0 24 24"
                opacity="0.5"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M6 18L18 6M6 6l12 12" 
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {searchQuery ? (
        // Search results
        <div className="bg-opacity-90" style={{ 
          backgroundColor: isDark ? 'rgba(17,17,17,0.6)' : 'rgba(255,255,255,0.6)',
          borderRadius: '8px',
          padding: '12px',
          boxShadow: isDark ? '0 4px 12px rgba(0,0,0,0.2)' : '0 4px 12px rgba(0,0,0,0.1)'
        }}>
          <h3 className="text-lg font-medium mb-4">Search Results ({filteredItems.length})</h3>
          
          <div className="space-y-3">
            {filteredItems.length > 0 ? (
              filteredItems.map((item, i) => (
                <motion.div 
                  key={i}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.05 }}
                  className="p-3 rounded-lg"
                  style={{ 
                    backgroundColor: isDark ? 'rgba(34,34,34,0.8)' : 'rgba(240,240,240,0.8)',
                    borderLeft: `3px solid ${primaryColor}`
                  }}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm opacity-70">{item.category}</span>
                  </div>
                  <h4 className="font-medium mt-1">{item.title}</h4>
                  <p className="text-sm mt-1 opacity-80">{item.desc}</p>
                </motion.div>
              ))
            ) : (
              <div className="text-center p-6 opacity-70">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="mx-auto mb-3" strokeWidth="1.5">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <p>No results found for "{searchQuery}"</p>
                <p className="text-sm mt-2">Try different keywords or explore categories below</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Categories view
        <div>
          {/* Category Pills */}
          <div className="flex overflow-x-auto pb-2 mb-3 no-scrollbar">
            <div className="flex space-x-2">
              {categories.map((category, i) => (
                <button
                  key={i}
                  onClick={() => setActiveCategory(category.name)}
                  className="px-4 py-2 rounded-full whitespace-nowrap flex items-center gap-2"
                  style={{ 
                    backgroundColor: activeCategory === category.name 
                      ? primaryColor 
                      : isDark ? 'rgba(51,51,51,0.7)' : 'rgba(240,240,240,0.7)',
                    color: activeCategory === category.name 
                      ? 'white' 
                      : isDark ? 'white' : 'black',
                    fontWeight: activeCategory === category.name ? '500' : 'normal',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <span style={{ color: activeCategory === category.name ? 'white' : primaryColor }}>
                    {category.icon}
                  </span>
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Active Category Content */}
          <div 
            className="bg-opacity-90 rounded-lg p-3"
            style={{ 
              backgroundColor: isDark ? 'rgba(17,17,17,0.6)' : 'rgba(255,255,255,0.6)',
              boxShadow: isDark ? '0 4px 12px rgba(0,0,0,0.2)' : '0 4px 12px rgba(0,0,0,0.1)'
            }}
          >
            {categories
              .filter(category => category.name === activeCategory)
              .map((category, i) => (
                <div key={i}>
                  <div className="mb-4 flex items-center">
                    <span style={{ color: primaryColor }}>
                      {category.icon}
                    </span>
                    <h3 
                      className="text-xl font-semibold ml-2"
                      style={{ color: primaryColor }}
                    >
                      {category.name}
                    </h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {category.items.map((item, j) => (
                      <motion.div 
                        key={j}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: j * 0.1 }}
                      >
                        <button 
                          className="w-full text-left p-4 rounded-lg transition-all flex items-start"
                          style={{ 
                            backgroundColor: isDark ? 'rgba(34,34,34,0.8)' : 'rgba(240,240,240,0.8)',
                            color: isDark ? 'white' : 'black',
                            border: `1px solid ${isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                            height: '100%'
                          }}
                          onMouseOver={(e) => {
                            e.currentTarget.style.backgroundColor = isDark ? 'rgba(51,51,51,0.9)' : 'rgba(248,248,248,0.9)';
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                          }}
                          onMouseOut={(e) => {
                            e.currentTarget.style.backgroundColor = isDark ? 'rgba(34,34,34,0.8)' : 'rgba(240,240,240,0.8)';
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                          }}
                        >
                          <div className="flex-1">
                            <h4 className="font-medium mb-1">{item.title}</h4>
                            <p className="text-sm opacity-70">{item.desc}</p>
                          </div>
                          <div 
                            className="ml-2 flex-shrink-0 mt-1" 
                            style={{ color: primaryColor }}
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M5 12h14M12 5l7 7-7 7"></path>
                            </svg>
                          </div>
                        </button>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}; 