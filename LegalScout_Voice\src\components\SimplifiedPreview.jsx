import React, { useState, useEffect } from 'react';
import VapiCall from './VapiCall';
import Button from './Button';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { processImageUrl } from '../utils/imageStorage';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';
import { createDemoPreviewConfig, createAttorneyPreviewConfig } from '../utils/previewConfigHandler';
import './SimplifiedPreview.css';

/**
 * SimplifiedPreview component - A simplified version of the preview component
 * used for attorney subdomains to display the attorney's customized assistant
 */
const SimplifiedPreview = ({
  // Basic information
  firmName,
  logoUrl,
  mascot,
  buttonImageUrl, // Add buttonImageUrl prop

  // Colors and styling
  primaryColor,
  secondaryColor,
  buttonColor,
  backgroundColor,
  backgroundOpacity,
  buttonText,
  buttonOpacity,
  practiceAreaBackgroundOpacity,
  textBackgroundColor,

  // Content
  practiceDescription,
  welcomeMessage,
  informationGathering,
  officeAddress,
  schedulingLink,
  practiceAreas,

  // Vapi configuration
  vapiInstructions,
  vapiContext,
  vapi_assistant_id,
  voiceId,
  aiModel,
  subdomain = 'default', // Add subdomain prop

  // Custom fields configuration
  customFields,
  summaryPrompt,
  structuredDataPrompt,
  structuredDataSchema,

  // Theme
  theme = 'dark'
}) => {
  const [callActive, setCallActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPreviewHeader, setShowPreviewHeader] = useState(true);
  // Add state management for preventing mount/unmount cycles
  const [isStartingCall, setIsStartingCall] = useState(false);
  const [vapiCallKey, setVapiCallKey] = useState(0);

  // Convert hex colors to RGB for opacity support
  const hexToRgb = (hex) => {
    // Remove # if present
    hex = hex.replace('#', '');

    // Parse hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
  };

  // Get contrast color (black or white) based on background
  const getContrastColor = (hexColor) => {
    // Convert hex to RGB
    let hex = hexColor.replace('#', '');

    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate luminance - standard formula
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return black for bright colors, white for dark colors
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Start the call
  const startCall = () => {
    // Prevent multiple simultaneous call starts
    if (isStartingCall || callActive) {
      console.log('[SimplifiedPreview] Call already starting or active, ignoring duplicate startCall');
      return;
    }

    console.log('[SimplifiedPreview] Starting call with assistant ID:', vapi_assistant_id);

    // Set the flag to indicate we're starting a call
    setIsStartingCall(true);
    setIsLoading(true);

    // Increment the VapiCall key to force a fresh component instance
    setVapiCallKey(prev => prev + 1);

    // Simulate a short loading time
    setTimeout(() => {
      setCallActive(true);
      setIsLoading(false);

      // Reset the starting call flag after a delay to allow for initialization
      setTimeout(() => {
        setIsStartingCall(false);
      }, 2000); // Allow time for VapiCall initialization
    }, 500);
  };

  // End the call
  const endCall = () => {
    setCallActive(false);
  };

  // Handle production URLs in local development for button images
  const handleImageUrl = (url) => {
    if (url && typeof url === 'string' && url.includes('legalscout.ai/static/media')) {
      console.log('Converting production URL to local asset:', url);
      return '/PRIMARY CLEAR.png';
    }
    return processImageUrl(url) || '/PRIMARY CLEAR.png';
  };

  // Always show the firm name header for iframe content
  // This ensures embedded widgets and attorney subdomains have proper branding
  useEffect(() => {
    setShowPreviewHeader(true);
    console.log('SimplifiedPreview: Always showing firm name header for proper branding');
  }, []);

  // Log the props for debugging
  useEffect(() => {
    console.log('SimplifiedPreview props:', {
      firmName,
      primaryColor,
      secondaryColor,
      buttonColor,
      practiceDescription: practiceDescription ? practiceDescription.substring(0, 100) + '...' : 'None',
      showPreviewHeader,
      // ... other props can be added here
    });
  }, [firmName, primaryColor, secondaryColor, buttonColor, practiceDescription, showPreviewHeader]);

  // Dynamic styles
  const previewStyles = {
    '--primary-color': primaryColor,
    '--primary-color-rgb': hexToRgb(primaryColor),
    '--secondary-color': secondaryColor,
    '--secondary-color-rgb': hexToRgb(secondaryColor),
    '--button-color': buttonColor,
    '--button-color-rgb': hexToRgb(buttonColor),
    '--button-text-color': getContrastColor(buttonColor),
    '--background-color': `rgba(${hexToRgb(backgroundColor)}, ${backgroundOpacity})`,
    '--text-background-color': `rgba(${hexToRgb(textBackgroundColor)}, ${practiceAreaBackgroundOpacity})`,
    '--button-opacity': buttonOpacity,
  };

  return (
    <div className={`simplified-preview ${theme}-theme`} style={previewStyles}>
      <div className="preview-content">
        {!callActive ? (
          <>
            {/* Firm name header removed - only show the header with logo and navigation */}

            <div className="preview-practice-description">
              <div className="practice-description-content">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                  components={{
                    // Define custom components for markdown elements
                    h1: ({node, ...props}) => <h1 style={{color: primaryColor || '#4B74AA'}} {...props} />,
                    h2: ({node, ...props}) => <h2 style={{color: secondaryColor || '#2C3E50'}} {...props} />,
                    h3: ({node, ...props}) => <h3 style={{color: secondaryColor || '#2C3E50'}} {...props} />,
                    a: ({node, ...props}) => <a style={{color: buttonColor || '#3498db'}} {...props} />,
                    strong: ({node, ...props}) => <strong style={{color: primaryColor || '#4B74AA'}} {...props} />,
                    em: ({node, ...props}) => <em style={{fontStyle: 'italic'}} {...props} />,
                    blockquote: ({node, ...props}) => (
                      <blockquote
                        style={{
                          borderLeft: `4px solid ${secondaryColor || '#3498db'}`,
                          paddingLeft: '1rem',
                          marginLeft: 0,
                          fontStyle: 'italic'
                        }}
                        {...props}
                      />
                    ),
                    ul: ({node, ...props}) => <ul style={{marginLeft: '1.5rem'}} {...props} />,
                    ol: ({node, ...props}) => <ol style={{marginLeft: '1.5rem'}} {...props} />,
                    li: ({node, ...props}) => <li style={{marginBottom: '0.5rem'}} {...props} />,
                  }}
                >
                  {practiceDescription}
                </ReactMarkdown>
              </div>

              {/* Display practice areas if available */}
              {practiceAreas && practiceAreas.length > 0 && (
                <div className="practice-areas-section">
                  <h3>Practice Areas</h3>
                  <ul className="practice-areas-list">
                    {practiceAreas.map((area, index) => (
                      <li key={index}>{area}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Display office address if available */}
              {officeAddress && (
                <div className="office-address-section">
                  <h3>Office Location</h3>
                  <p>{officeAddress}</p>
                </div>
              )}

              {/* Display scheduling link if available */}
              {schedulingLink && (
                <div className="scheduling-section">
                  <h3>Schedule a Consultation</h3>
                  <a href={schedulingLink} target="_blank" rel="noopener noreferrer" className="scheduling-link">
                    Book an Appointment
                  </a>
                </div>
              )}
            </div>

            <div className="preview-button-container">
              <Button
                onClick={startCall}
                label={buttonText || "Start Consultation"}
                mascot={handleImageUrl(buttonImageUrl || mascot)} /* Use buttonImageUrl if available, otherwise use mascot */
                isLoading={isLoading}
                buttonColor={buttonColor}
                buttonOpacity={buttonOpacity}
              />
            </div>
          </>
        ) : (
          <div className="preview-call-container">
            <VapiCall
              key={vapiCallKey} // Use stable key to prevent unnecessary unmounting
              onEndCall={endCall}
              subdomain={subdomain} // Pass the subdomain to VapiCall
              isDemo={!vapi_assistant_id} // Use demo mode if no assistant ID is provided
              attorneyData={vapi_assistant_id ? (() => {
                const data = {
                  firm_name: firmName,
                  vapi_instructions: vapiInstructions,
                  vapi_context: vapiContext,
                  welcome_message: welcomeMessage,
                  vapi_assistant_id: vapi_assistant_id,
                  voice_id: voiceId,
                  ai_model: aiModel,
                  subdomain: subdomain, // Include subdomain in attorney data
                  // Include appearance colors for speech particles
                  primary_color: primaryColor,
                  secondary_color: secondaryColor,
                  // Include custom fields data
                  custom_fields: customFields,
                  summary_prompt: summaryPrompt,
                  structured_data_prompt: structuredDataPrompt,
                  structured_data_schema: structuredDataSchema
                };
                console.log('🎨 SimplifiedPreview: Passing attorney data with colors and subdomain:', {
                  primary_color: primaryColor,
                  secondary_color: secondaryColor,
                  firmName: firmName,
                  subdomain: subdomain
                });
                return data;
              })() : null}
              customInstructions={{
                firmName: firmName,
                vapiInstructions: vapiInstructions,
                vapiContext: vapiContext,
                initialMessage: welcomeMessage || `Hello, I'm Scout from ${firmName}. How can I help you today?`,
                assistantId: vapi_assistant_id,
                voiceId: voiceId,
                aiModel: aiModel
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplifiedPreview;
