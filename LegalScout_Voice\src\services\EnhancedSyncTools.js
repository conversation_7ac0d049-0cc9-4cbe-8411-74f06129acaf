/**
 * Enhanced Synchronization Tools
 *
 * This file contains enhanced synchronization tools for the application.
 * It implements the one-way sync pattern (UI → Supabase → Vapi) with Supabase as the primary source of truth.
 */

import {
  fetchFromSupabase,
  updateSupabaseAttorney,
  getAttorneyByEmail,
  getAttorneyByAuthId,
  getAttorneyById,
  createAttorney,
  update<PERSON><PERSON>rney,
  fetchFromVapi,
  createVapiAssistant,
  updateVapiAssistant,
  getVapiAssistant,
  findProfileDiscrepancies,
  getValidVoicesForProvider,
  ensureProfilePersistence
} from './EnhancedSyncHelpers.js';

/**
 * Synchronize attorney profile data between Supabase and Vapi
 *
 * This function ensures that the attorney's profile data is consistent
 * between Supabase and Vapi. It checks for discrepancies and updates
 * Vapi to match Supabase (which is considered the source of truth).
 *
 * @param {Object} params - The parameters for the function
 * @param {string} params.attorneyId - The ID of the attorney to synchronize
 * @param {boolean} [params.forceUpdate=false] - Whether to force an update even if no discrepancies are found
 * @returns {Promise<Object>} The result of the synchronization
 */
export const syncAttorneyProfile = async (params) => {
  const { attorneyId, forceUpdate = false } = params;

  console.log(`[EnhancedSyncTools] Syncing attorney profile for ${attorneyId}, forceUpdate: ${forceUpdate}`);

  try {
    // Use the enhanced profile persistence function
    const result = await ensureProfilePersistence({
      attorneyId,
      forceUpdate
    });

    // Log the result
    console.log(`[EnhancedSyncTools] Profile persistence result for ${attorneyId}:`, {
      action: result.action,
      success: result.success,
      sources: result.sources
    });

    // Return a compatible result format
    return {
      action: result.action,
      assistantId: result.vapiResult?.assistantId,
      message: result.message,
      success: result.success,
      sources: result.sources,
      discrepancies: result.vapiResult?.discrepancies || null
    };
  } catch (error) {
    console.error(`[EnhancedSyncTools] Error syncing attorney profile for ${attorneyId}:`, error);
    return {
      action: "error",
      error: error.message,
      message: `Error syncing attorney profile: ${error.message}`
    };
  }
};

/**
 * Manage authentication state across systems
 *
 * This function handles authentication state management, ensuring that
 * when a user logs in, logs out, or refreshes their session, all systems
 * (Supabase, application state, Vapi) are properly synchronized.
 *
 * @param {Object} params - The parameters for the function
 * @param {Object} params.authData - Authentication data
 * @param {string} params.action - Authentication action (login, logout, refresh)
 * @returns {Promise<Object>} The result of the authentication state management
 */
export const manageAuthState = async (params) => {
  const { authData, action } = params;

  console.log(`[EnhancedSyncTools] Managing auth state for action: ${action}`);

  try {
    switch (action) {
      case "login":
        // Handle login (OAuth or email/password)
        const { user, session } = authData;

        if (!user || !user.email) {
          return {
            action: "login",
            success: false,
            message: "Invalid user data provided for login"
          };
        }

        // Check if attorney record exists
        let attorney = await getAttorneyByEmail(user.email);

        if (!attorney) {
          // Create new attorney record
          console.log(`[EnhancedSyncTools] Creating new attorney record for ${user.email}`);
          attorney = await createAttorney({
            email: user.email,
            name: user.user_metadata?.name || user.email.split('@')[0],
            user_id: user.id,
            firm_name: user.user_metadata?.name ? `${user.user_metadata.name}'s Law Firm` : 'My Law Firm',
            welcome_message: 'Hello, how can I help you with your legal needs today?',
            vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs.'
          });
        } else if (!attorney.user_id) {
          // Update existing attorney with user ID
          console.log(`[EnhancedSyncTools] Updating existing attorney record with user_id for ${user.email}`);
          attorney = await updateAttorney(attorney.id, {
            user_id: user.id
          });
        }

        // Ensure attorney profile persistence
        console.log(`[EnhancedSyncTools] Ensuring attorney ${attorney.id} profile persistence`);
        const syncResult = await ensureProfilePersistence({
          attorneyId: attorney.id,
          forceUpdate: true
        });

        return {
          action: "login",
          success: true,
          attorney,
          syncResult,
          session,
          message: "Authentication state synchronized"
        };

      case "logout":
        // Handle logout
        // No specific sync needed for logout
        return {
          action: "logout",
          success: true,
          message: "User logged out successfully"
        };

      case "refresh":
        // Handle session refresh
        if (!authData.user) {
          return {
            action: "refresh",
            success: false,
            message: "No user data provided for refresh"
          };
        }

        // Get attorney data
        const refreshedAttorney = await getAttorneyByAuthId(authData.user.id);

        if (!refreshedAttorney) {
          return {
            action: "refresh",
            success: false,
            message: "Attorney record not found for this auth ID"
          };
        }

        // Ensure profile persistence on refresh
        console.log(`[EnhancedSyncTools] Ensuring profile persistence for attorney ${refreshedAttorney.id} on refresh`);
        const refreshSyncResult = await ensureProfilePersistence({
          attorneyId: refreshedAttorney.id
        });

        return {
          action: "refresh",
          success: true,
          attorney: refreshedAttorney,
          syncResult: refreshSyncResult,
          message: "Authentication state refreshed and synchronized"
        };

      default:
        return {
          action: "unknown",
          success: false,
          message: `Unknown action: ${action}`
        };
    }
  } catch (error) {
    console.error(`[EnhancedSyncTools] Error managing auth state for action ${action}:`, error);
    return {
      action,
      success: false,
      error: error.message,
      message: `Error managing auth state: ${error.message}`
    };
  }
};

/**
 * Validate configuration before updates
 *
 * This function validates the configuration data before it is saved to Supabase.
 * It checks for required fields and ensures that the data is valid.
 *
 * @param {Object} params - The parameters for the function
 * @param {string} params.attorneyId - The ID of the attorney
 * @param {Object} params.configData - The configuration data to validate
 * @returns {Promise<Object>} The validation result
 */
export const validateConfiguration = async (params) => {
  const { attorneyId, configData } = params;

  console.log(`[EnhancedSyncTools] Validating configuration for attorney ${attorneyId}`);
  console.log('[EnhancedSyncTools] Config data:', configData);

  try {
    // Get current attorney data
    const attorney = await getAttorneyById(attorneyId);

    if (!attorney) {
      return {
        valid: false,
        errors: ["Attorney not found"],
        message: "Cannot validate configuration for non-existent attorney"
      };
    }

    // Define required fields for different sections
    const requiredFields = {
      profile: ["name", "email"],
      appearance: ["firm_name"],
      agent: ["welcome_message", "vapi_instructions"],
      voice: ["voice_provider", "voice_id"]
    };

    // Merge current data with new config data
    const mergedConfig = { ...attorney, ...configData };

    // Validate all required fields
    const missingFields = {};
    let hasErrors = false;

    Object.entries(requiredFields).forEach(([section, fields]) => {
      const missing = fields.filter(field => !mergedConfig[field]);
      if (missing.length > 0) {
        missingFields[section] = missing;
        hasErrors = true;
      }
    });

    // Check for specific validation rules
    const validationErrors = [];

    // Validate welcome message length
    if (mergedConfig.welcome_message && mergedConfig.welcome_message.length > 500) {
      validationErrors.push("Welcome message exceeds maximum length of 500 characters");
      hasErrors = true;
    }

    // Validate voice provider and ID combination
    if (mergedConfig.voice_provider && mergedConfig.voice_id) {
      const validVoices = await getValidVoicesForProvider(mergedConfig.voice_provider);
      if (!validVoices.includes(mergedConfig.voice_id)) {
        validationErrors.push(`Voice ID '${mergedConfig.voice_id}' is not valid for provider '${mergedConfig.voice_provider}'`);
        hasErrors = true;
      }
    }

    return {
      valid: !hasErrors,
      missingFields: Object.keys(missingFields).length > 0 ? missingFields : null,
      validationErrors: validationErrors.length > 0 ? validationErrors : null,
      message: hasErrors
        ? "Configuration validation failed"
        : "Configuration is valid"
    };
  } catch (error) {
    console.error(`[EnhancedSyncTools] Error validating configuration for attorney ${attorneyId}:`, error);
    return {
      valid: false,
      error: error.message,
      message: `Error validating configuration: ${error.message}`
    };
  }
};

/**
 * Ensure preview matches deployment
 *
 * This function checks if the preview in the dashboard matches what will be
 * deployed to the attorney's subdomain. It checks for discrepancies between
 * the attorney's profile in Supabase, the Vapi assistant, and the subdomain
 * configuration.
 *
 * @param {Object} params - The parameters for the function
 * @param {string} params.attorneyId - The ID of the attorney
 * @returns {Promise<Object>} The consistency check result
 */
export const checkPreviewConsistency = async (params) => {
  const { attorneyId } = params;

  console.log(`[EnhancedSyncTools] Checking preview consistency for attorney ${attorneyId}`);

  try {
    // Get attorney data from Supabase (source of truth)
    const attorney = await getAttorneyById(attorneyId);

    if (!attorney) {
      return {
        consistent: false,
        errors: ["Attorney not found"],
        message: "Cannot check consistency for non-existent attorney"
      };
    }

    // Check Vapi assistant consistency
    const vapiAssistantId = attorney.vapi_assistant_id;

    if (!vapiAssistantId) {
      return {
        consistent: false,
        errors: ["No Vapi assistant ID found"],
        message: "Attorney record is missing Vapi assistant ID"
      };
    }

    // Get Vapi assistant data
    let vapiAssistant;
    try {
      vapiAssistant = await getVapiAssistant(vapiAssistantId);
    } catch (error) {
      return {
        consistent: false,
        errors: [`Error fetching Vapi assistant: ${error.message}`],
        message: "Failed to fetch Vapi assistant"
      };
    }

    // If Vapi assistant not found, create a new one
    if (!vapiAssistant) {
      console.log(`[EnhancedSyncTools] Vapi assistant ${vapiAssistantId} not found, fixing...`);

      // Ensure profile persistence to fix discrepancies
      const syncResult = await ensureProfilePersistence({
        attorneyId: attorney.id,
        forceUpdate: true
      });

      return {
        consistent: false,
        errors: ["Vapi assistant not found"],
        action: "fixed",
        syncResult,
        message: "Vapi assistant not found and fixed"
      };
    }

    // Compare attorney data with Vapi assistant
    const discrepancies = findProfileDiscrepancies(attorney, vapiAssistant);

    // If discrepancies found, fix them
    if (Object.keys(discrepancies).length > 0) {
      console.log(`[EnhancedSyncTools] Found Vapi discrepancies for attorney ${attorneyId}:`, discrepancies);

      // Ensure profile persistence to fix discrepancies
      const syncResult = await ensureProfilePersistence({
        attorneyId: attorney.id,
        forceUpdate: true
      });

      return {
        consistent: false,
        discrepancies,
        action: "fixed",
        syncResult,
        message: "Vapi assistant discrepancies found and fixed"
      };
    }

    // Everything is consistent
    return {
      consistent: true,
      message: "Preview is consistent with deployment"
    };
  } catch (error) {
    console.error(`[EnhancedSyncTools] Error checking preview consistency for attorney ${attorneyId}:`, error);
    return {
      consistent: false,
      error: error.message,
      message: `Error checking preview consistency: ${error.message}`
    };
  }
};
