/**
 * Comprehensive Call Sync Diagnostic Test Suite
 * 
 * This test suite diagnoses the complete flow from Vapi calls to Briefs page display
 * to identify where call records are not syncing properly.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { supabase } from '../lib/supabase';

// Mock environment variables
const mockEnv = {
  VAPI_PRIVATE_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  SUPABASE_URL: process.env.VITE_SUPABASE_URL,
  SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY
};

describe('Call Sync Diagnostic Tests', () => {
  let testAttorneyId;
  let testAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // User's assistant ID

  beforeEach(async () => {
    // Create a test attorney for our tests
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .insert({
        email: '<EMAIL>',
        firm_name: 'Test Sync Firm',
        subdomain: 'test-sync-' + Date.now(),
        assistant_id: testAssistantId
      })
      .select()
      .single();

    if (error) {
      console.error('Failed to create test attorney:', error);
      throw error;
    }

    testAttorneyId = attorney.id;
    console.log('Created test attorney:', testAttorneyId);
  });

  afterEach(async () => {
    // Clean up test data
    if (testAttorneyId) {
      await supabase.from('call_records').delete().eq('attorney_id', testAttorneyId);
      await supabase.from('consultations').delete().eq('attorney_id', testAttorneyId);
      await supabase.from('attorneys').delete().eq('id', testAttorneyId);
    }
  });

  describe('1. Database Schema Validation', () => {
    test('should have call_records table with correct structure', async () => {
      const { data, error } = await supabase
        .from('call_records')
        .select('*')
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });

    test('should have consultations table with correct structure', async () => {
      const { data, error } = await supabase
        .from('consultations')
        .select('*')
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });

    test('should have attorney_id foreign key in call_records', async () => {
      // Test that we can query call_records with attorney_id
      const { data, error } = await supabase
        .from('call_records')
        .select('attorney_id')
        .eq('attorney_id', testAttorneyId);

      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
    });
  });

  describe('2. Webhook Handler Testing', () => {
    test('should process valid webhook payload', async () => {
      const mockCallData = {
        id: 'test-call-' + Date.now(),
        assistant_id: testAssistantId,
        status: 'completed',
        duration: 120,
        start_time: new Date().toISOString(),
        end_time: new Date().toISOString(),
        customer: {
          phone_number: '+1234567890'
        },
        transcripts: [
          {
            role: 'user',
            message: 'Hello, I need legal help'
          },
          {
            role: 'assistant',
            message: 'I can help you with that'
          }
        ],
        messages: [],
        tool_executions: [],
        metadata: {}
      };

      // Import and test the webhook handler function
      const { storeCallData } = await import('../../api/webhook/vapi-call/index.js');
      
      const result = await storeCallData(mockCallData);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Verify data was stored in call_records
      const { data: callRecord, error } = await supabase
        .from('call_records')
        .select('*')
        .eq('call_id', mockCallData.id)
        .single();

      expect(error).toBeNull();
      expect(callRecord).toBeDefined();
      expect(callRecord.attorney_id).toBe(testAttorneyId);
      expect(callRecord.status).toBe('completed');
    });

    test('should handle missing assistant_id gracefully', async () => {
      const mockCallData = {
        id: 'test-call-invalid',
        status: 'completed'
        // Missing assistant_id
      };

      const { storeCallData } = await import('../../api/webhook/vapi-call/index.js');
      const result = await storeCallData(mockCallData);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('assistant ID');
    });
  });

  describe('3. Attorney-Assistant Mapping', () => {
    test('should correctly map assistant_id to attorney_id', async () => {
      // Test the getAttorneyIdFromAssistantId function
      const { getAttorneyIdFromAssistantId } = await import('../../api/webhook/vapi-call/index.js');
      
      const attorneyId = await getAttorneyIdFromAssistantId(testAssistantId);
      
      expect(attorneyId).toBe(testAttorneyId);
    });

    test('should return null for unknown assistant_id', async () => {
      const { getAttorneyIdFromAssistantId } = await import('../../api/webhook/vapi-call/index.js');
      
      const attorneyId = await getAttorneyIdFromAssistantId('unknown-assistant-id');
      
      expect(attorneyId).toBeNull();
    });
  });

  describe('4. Consultation Creation Flow', () => {
    test('should create consultation from completed call', async () => {
      const mockCallData = {
        id: 'test-call-consultation-' + Date.now(),
        assistant_id: testAssistantId,
        status: 'completed',
        duration: 180,
        start_time: new Date().toISOString(),
        end_time: new Date().toISOString(),
        customer: {
          phone_number: '+1234567890'
        },
        transcripts: [
          {
            role: 'user',
            message: 'I need help with a divorce case'
          }
        ],
        tool_executions: [
          {
            tool_name: 'collect_client_info',
            result: {
              name: 'John Doe',
              email: '<EMAIL>',
              practice_area: 'Family Law',
              location: 'New York, NY'
            }
          }
        ]
      };

      const { storeCallData } = await import('../../api/webhook/vapi-call/index.js');
      const result = await storeCallData(mockCallData);
      
      expect(result.success).toBe(true);

      // Check that consultation was created
      const { data: consultations, error } = await supabase
        .from('consultations')
        .select('*')
        .eq('attorney_id', testAttorneyId)
        .eq('metadata->>call_id', mockCallData.id);

      expect(error).toBeNull();
      expect(consultations).toHaveLength(1);
      expect(consultations[0].client_name).toBe('John Doe');
      expect(consultations[0].practice_area).toBe('Family Law');
    });
  });

  describe('5. Briefs Page Data Loading', () => {
    test('should load consultations for attorney in Briefs page', async () => {
      // Create a test consultation
      const { data: consultation, error } = await supabase
        .from('consultations')
        .insert({
          attorney_id: testAttorneyId,
          client_name: 'Test Client',
          client_email: '<EMAIL>',
          practice_area: 'Test Law',
          status: 'new'
        })
        .select()
        .single();

      expect(error).toBeNull();

      // Test the consultation loading logic from ConsultationsTab
      const { data: loadedConsultations, error: loadError } = await supabase
        .from('consultations')
        .select('*')
        .eq('attorney_id', testAttorneyId)
        .order('created_at', { ascending: false });

      expect(loadError).toBeNull();
      expect(loadedConsultations).toHaveLength(1);
      expect(loadedConsultations[0].client_name).toBe('Test Client');
    });
  });

  describe('6. Environment Configuration', () => {
    test('should have required environment variables', () => {
      expect(mockEnv.VAPI_PRIVATE_KEY).toBeDefined();
      expect(mockEnv.VAPI_PUBLIC_KEY).toBeDefined();
      expect(mockEnv.SUPABASE_URL).toBeDefined();
      expect(mockEnv.SUPABASE_ANON_KEY).toBeDefined();
    });

    test('should have valid Supabase connection', async () => {
      const { data, error } = await supabase
        .from('attorneys')
        .select('id')
        .limit(1);

      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
    });
  });
});
