import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase-fixed';
import { verifySupabaseConfig } from '../utils/supabaseConfigVerifier';

/**
 * Test page for Supabase connection
 * This page tests the Supabase connection and displays the results
 */
const TestSupabase = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [attorneys, setAttorneys] = useState([]);
  const [configStatus, setConfigStatus] = useState(null);
  const [testResult, setTestResult] = useState(null);

  useEffect(() => {
    const testSupabase = async () => {
      try {
        setLoading(true);
        
        // Verify Supabase configuration
        const verifyResult = await verifySupabaseConfig();
        setConfigStatus(verifyResult);
        
        // Query attorneys table
        const { data, error } = await supabase
          .from('attorneys')
          .select('*')
          .limit(10);
        
        if (error) {
          setError(error.message);
          setTestResult({
            success: false,
            message: `Error querying attorneys table: ${error.message}`
          });
        } else {
          setAttorneys(data || []);
          setTestResult({
            success: true,
            message: `Successfully retrieved ${data?.length || 0} attorneys`
          });
        }
      } catch (err) {
        setError(err.message);
        setTestResult({
          success: false,
          message: `Unexpected error: ${err.message}`
        });
      } finally {
        setLoading(false);
      }
    };
    
    testSupabase();
  }, []);

  return (
    <div className="test-supabase-page">
      <h1>Supabase Connection Test</h1>
      
      <div className="test-section">
        <h2>Configuration Status</h2>
        {configStatus ? (
          <div className={`status-box ${configStatus.success ? 'success' : 'error'}`}>
            <p><strong>Success:</strong> {configStatus.success ? 'Yes' : 'No'}</p>
            {configStatus.error && <p><strong>Error:</strong> {configStatus.error}</p>}
            {configStatus.usingFallback && (
              <p className="warning">
                <strong>Warning:</strong> Using fallback Supabase configuration
              </p>
            )}
            {configStatus.useMockData && (
              <p className="warning">
                <strong>Warning:</strong> Using mock data instead of real Supabase data
              </p>
            )}
          </div>
        ) : (
          <p>Loading configuration status...</p>
        )}
      </div>
      
      <div className="test-section">
        <h2>Test Result</h2>
        {loading ? (
          <p>Testing Supabase connection...</p>
        ) : testResult ? (
          <div className={`status-box ${testResult.success ? 'success' : 'error'}`}>
            <p>{testResult.message}</p>
          </div>
        ) : null}
      </div>
      
      <div className="test-section">
        <h2>Attorneys Data</h2>
        {loading ? (
          <p>Loading attorneys...</p>
        ) : error ? (
          <p className="error">Error: {error}</p>
        ) : attorneys.length > 0 ? (
          <table className="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Firm Name</th>
                <th>Subdomain</th>
              </tr>
            </thead>
            <tbody>
              {attorneys.map(attorney => (
                <tr key={attorney.id}>
                  <td>{attorney.id}</td>
                  <td>{attorney.name}</td>
                  <td>{attorney.firm_name}</td>
                  <td>{attorney.subdomain}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No attorneys found</p>
        )}
      </div>
      
      <style jsx>{`
        .test-supabase-page {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          font-family: Arial, sans-serif;
        }
        
        .test-section {
          margin-bottom: 30px;
          border: 1px solid #ddd;
          padding: 20px;
          border-radius: 5px;
        }
        
        .status-box {
          padding: 15px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        
        .success {
          background-color: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }
        
        .error {
          background-color: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        
        .warning {
          background-color: #fff3cd;
          border: 1px solid #ffeeba;
          color: #856404;
          padding: 10px;
          border-radius: 5px;
          margin-top: 10px;
        }
        
        .data-table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .data-table th, .data-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        
        .data-table th {
          background-color: #f2f2f2;
        }
        
        .data-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
      `}</style>
    </div>
  );
};

export default TestSupabase;
