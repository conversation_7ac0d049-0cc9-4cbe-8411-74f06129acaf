import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useVapi from '../hooks/use-vapi';
import './SpeechParticles.css';

/**
 * Enhanced Speech Particles Component
 * 
 * This component combines the existing SpeechParticles visualization with
 * the Vapi Blocks visualization concepts, using the use-vapi hook for
 * consistent audio level tracking.
 */
const EnhancedSpeechParticles = ({ className, style }) => {
  const { volumeLevel, isSessionActive, currentSpeaker } = useVapi();
  const [bars, setBars] = useState(Array(50).fill(5)); // Initialize with low value for inactivity
  const canvasRef = useRef(null);
  
  // Update bars based on volume level
  useEffect(() => {
    if (isSessionActive && volumeLevel > 0) {
      updateBars(volumeLevel);
    } else {
      // Use a subtle ambient animation when not active
      animateAmbient();
    }
  }, [volumeLevel, isSessionActive]);
  
  // Update the visualization bars based on volume
  const updateBars = (volume) => {
    // Scale the volume to make it more visible
    const scaledVolume = Math.min(volume * 10, 1);
    
    // Create a more dynamic visualization with varying heights
    setBars(bars.map(() => {
      // Base height with some randomness
      const baseHeight = scaledVolume * 100;
      // Add randomness for a more natural look
      const randomFactor = Math.random() * 0.5 + 0.75; // Between 0.75 and 1.25
      return baseHeight * randomFactor;
    }));
    
    // If we have access to the updateAudioSource function from the original SpeechParticles
    if (window.updateAudioSource) {
      // Determine the speaker (user or assistant)
      const speaker = currentSpeaker || 'user';
      
      // Calculate a frequency based on the volume (higher volume = higher frequency)
      const baseFrequency = speaker === 'assistant' ? 200 : 300;
      const frequencyVariation = 150;
      const frequency = baseFrequency + (frequencyVariation * scaledVolume);
      
      // Update the original visualization
      window.updateAudioSource(scaledVolume, frequency, speaker);
    }
  };
  
  // Create a subtle ambient animation when not speaking
  const animateAmbient = () => {
    setBars(bars.map(() => {
      // Very subtle movement for ambient state
      return 5 + Math.random() * 10;
    }));
    
    // If we have access to the updateAudioSource function
    if (window.updateAudioSource) {
      // Set to null to allow fallback to ambient mode
      window.updateAudioSource(null, null, null);
    }
  };
  
  // Determine the color based on the speaker
  const getBarColor = () => {
    if (!isSessionActive) return 'rgba(200, 200, 200, 0.5)'; // Inactive gray
    
    if (currentSpeaker === 'assistant') {
      return 'rgba(0, 191, 255, 0.8)'; // Light blue for assistant (per user preference)
    } else if (currentSpeaker === 'user') {
      return 'rgba(0, 200, 100, 0.8)'; // Green for user (per user preference)
    }
    
    return 'rgba(200, 200, 200, 0.5)'; // Default gray
  };
  
  return (
    <div className={`enhanced-speech-particles ${className || ''}`} style={style}>
      {/* Original canvas for SpeechParticles */}
      <canvas
        ref={canvasRef}
        id="mistCanvas"
        className="speech-particles-canvas speech-mist-canvas"
      />
      
      {/* New visualization inspired by Vapi Blocks */}
      <AnimatePresence>
        <motion.div
          className="bars-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <svg 
            width="100%" 
            height="100%" 
            viewBox="0 0 1000 200" 
            preserveAspectRatio="xMidYMid meet"
            className="bars-svg"
          >
            {bars.map((height, index) => (
              <React.Fragment key={index}>
                <motion.rect
                  x={500 + index * 10 - 245}
                  y={100 - height / 2}
                  width="5"
                  height={height}
                  fill={getBarColor()}
                  initial={{ height: 5, y: 97.5 }}
                  animate={{ 
                    height: height, 
                    y: 100 - height / 2,
                    transition: { 
                      duration: 0.1,
                      ease: "easeOut"
                    }
                  }}
                />
                <motion.rect
                  x={500 - index * 10 - 5 - 245}
                  y={100 - height / 2}
                  width="5"
                  height={height}
                  fill={getBarColor()}
                  initial={{ height: 5, y: 97.5 }}
                  animate={{ 
                    height: height, 
                    y: 100 - height / 2,
                    transition: { 
                      duration: 0.1,
                      ease: "easeOut"
                    }
                  }}
                />
              </React.Fragment>
            ))}
          </svg>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default EnhancedSpeechParticles;
