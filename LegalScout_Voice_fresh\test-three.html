<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Test</title>
    <!-- Load Three.js and Three-Globe via CDN for reliable loading -->
    <script src="https://unpkg.com/three@0.174.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.174.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js"></script>
</head>
<body>
    <div id="globe-container" style="width: 100%; height: 500px;"></div>
    
    <script>
        // Wait for everything to load
        window.onload = function() {
            console.log('Window loaded');
            
            // Check if THREE is defined
            console.log('THREE defined:', typeof THREE !== 'undefined');
            
            // Check if ThreeGlobe is defined
            console.log('ThreeGlobe defined:', typeof ThreeGlobe !== 'undefined');
            
            // Try to create a simple scene
            try {
                const scene = new THREE.Scene();
                console.log('Scene created successfully');
                
                // Try to create a globe
                try {
                    const globe = new ThreeGlobe();
                    console.log('Globe created successfully');
                } catch (globeError) {
                    console.error('Error creating globe:', globeError);
                }
            } catch (sceneError) {
                console.error('Error creating scene:', sceneError);
            }
        };
    </script>
</body>
</html>
