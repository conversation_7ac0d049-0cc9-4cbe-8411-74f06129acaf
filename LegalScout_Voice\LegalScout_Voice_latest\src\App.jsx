import React, { useState, useEffect, useRef, Suspense } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import { useAuth } from './contexts/AuthContext'
import { AssistantAwareProvider } from './contexts/AssistantAwareContext'
import { unifiedAuthService } from './services/unifiedAuthService'

// Import all the components from the original app
import Dashboard from './pages/DashboardNew.jsx'
import AnimatedBackground from './components/AnimatedBackground.jsx'
import ThemeToggle from './components/ThemeToggle.jsx'
import Navbar from './components/Navbar.jsx'
import AuthOverlay from './components/AuthOverlay'
import AuthCallback from './pages/AuthCallback'
import SimpleCompleteProfile from './pages/SimpleCompleteProfile'
import LoginPage from './pages/LoginPage'
import AboutPage from './pages/AboutPage.jsx'
import TestComponent from './components/TestComponent.jsx'
import SubdomainTestPage from './pages/SubdomainTestPage.jsx'
import AttorneyProfileTest from './components/AttorneyProfileTest.jsx'
import CrmDemo from './pages/CrmDemo.jsx'
// import Phase1AuthTest from './components/test/Phase1AuthTest.jsx' // PHASE 1 COMPLETE - Test component removed

import SubdomainEditorDemo from './pages/SubdomainEditorDemo.jsx'
import TestSupabase from './pages/TestSupabase.jsx'
import SupabaseTest from './pages/SupabaseTest.jsx'
import ToolDebugger from './pages/ToolDebugger.jsx'
import VapiIntegrationTest from './pages/VapiIntegrationTest.jsx'
import SignInButton from './components/SignInButton.jsx'
import LoginButton from './components/LoginButton.jsx'
// Lazy load heavy components as in the working version
const CallControl = React.lazy(() => import('./pages/CallControl.jsx'));
const SimpleDemoPage = React.lazy(() => import('./pages/SimpleDemoPage.jsx'));
const PreviewPage = React.lazy(() => import('./pages/PreviewPage.jsx'));
import SimplifiedPreview from './components/SimplifiedPreview.jsx'
import SimplePreviewPage from './pages/SimplePreviewPage.jsx'
import MobileActivateAssistant from './components/mobile/MobileActivateAssistant.jsx'

// Additional imports needed for the inline Home component
import VapiCall from './components/VapiCall.jsx'
import Button from './components/Button.jsx'
import CallTransition from './components/CallTransition.jsx'
import { DEFAULT_ASSISTANT_ID } from './constants/vapiConstants'
import MapView from './components/MapView.jsx'
import AttorneyDossier from './components/AttorneyDossier.jsx'
import CallSummary from './components/CallSummary.jsx'
import { withDevTools, createDebugger, trackUserJourney } from './utils/debugConfig'

// Initialize debug for the inline Home component
const debug = createDebugger('Home');

// Import utilities with safe fallbacks
import { getCurrentSubdomain } from './utils/subdomainTester.js'
import { storeImage, processImageUrl } from './utils/imageStorage.js'
import { notificationManager } from './components/SubtleNotification.jsx'

// OPTIMAL: Use simple subdomain service for clean UI/Call separation
const getAssistantConfigAsync = async (subdomain) => {
  try {
    const { simpleSubdomainService } = await import('./services/simpleSubdomainService');
    const config = await simpleSubdomainService.getSubdomainConfig(subdomain);
    return config || null;
  } catch (error) {
    console.warn('Failed to get assistant config:', error);
    return null;
  }
};

const initializeSupabaseConfig = async () => {
  console.log('Supabase config initialization (fallback)');
  return true;
};

const verifySupabaseConfig = async () => {
  console.log('Supabase config verification (fallback)');
  return { success: true };
};

const logSupabaseConfig = () => {
  console.log('Supabase config logging (fallback)');
};

// CRITICAL FIX: Simplified Home component that doesn't break
const SimpleHome = ({ isDarkTheme, setShowAuthOverlay }) => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    color: '#fff',
    textAlign: 'center',
    padding: '20px'
  }}>
    <h1 style={{ marginBottom: '20px', fontSize: '2.5rem' }}>Welcome to LegalScout</h1>
    <p style={{ marginBottom: '30px', fontSize: '1.2rem', maxWidth: '600px' }}>
      Your AI-powered legal assistant platform. Connect with attorneys and get legal guidance.
    </p>
    <button
      onClick={() => setShowAuthOverlay(true)}
      style={{
        padding: '15px 30px',
        fontSize: '1.1rem',
        backgroundColor: '#3498db',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer'
      }}
    >
      Get Started
    </button>
  </div>
);

// CRITICAL FIX: Inline Home component (as it was in the working version)
const Home = ({ isLoading, callActive, showAttorneyInfo, showCallSummary, attorneyProfile, startCall, endCall, callData, subdomain, setShowAttorneyInfo, setShowCallSummary, buttonText, isAttorneySubdomain, hideCreateAgentButton = false, isDarkTheme, vapiCallKey }) => {
  const [showTransition, setShowTransition] = useState(false);
  const [buttonPosition, setButtonPosition] = useState(null);
  const buttonRef = useRef(null);

  // Always declare hooks at the top level - never conditionally
  const [configMapping, setConfigMapping] = useState(null);

  // Always run useEffect - control behavior with conditions inside
  useEffect(() => {
    // Only load configMapping if we need it for attorney subdomain
    if (isAttorneySubdomain && attorneyProfile && !isLoading && attorneyProfile.firmName && subdomain && subdomain !== 'default') {
      import('./utils/configMapping').then(module => {
        setConfigMapping(module);
      });
    }
  }, [isAttorneySubdomain, attorneyProfile, isLoading, subdomain]);

  // Function to handle the button click and start the transition
  const handleStartCall = () => {
    // Get the button position for the transition animation
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setButtonPosition({
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      });
    }

    // Show the transition
    setShowTransition(true);

    // Track the call start
    debug.log('Call transition started', { timestamp: new Date().toISOString() });
    trackUserJourney('call_transition_started');
  };

  // Function to handle transition completion
  const handleTransitionComplete = () => {
    // Actually start the call after the transition completes
    // Hide the transition overlay first
    setShowTransition(false);

    // Then start the call after a brief delay to ensure the UI is updated
    setTimeout(() => {
      startCall();
    }, 100);
  };

  // Debug the condition values
  console.log('🔍 [App.jsx] Condition check:', {
    isAttorneySubdomain,
    hasAttorneyProfile: !!attorneyProfile,
    isLoading,
    firmName: attorneyProfile?.firmName,
    firm_name: attorneyProfile?.firm_name,
    subdomain,
    subdomainNotDefault: subdomain !== 'default'
  });

  // If this is an attorney subdomain and we have a valid attorney profile, render the preview component directly
  if (isAttorneySubdomain && attorneyProfile && !isLoading && (attorneyProfile.firmName || attorneyProfile.firm_name) && subdomain && subdomain !== 'default') {
    console.log('🎯 [App.jsx] Condition met! Rendering iframe for subdomain:', subdomain);

    if (!configMapping) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          backgroundColor: '#f5f7fa'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #4B9CD3',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ marginTop: '20px', color: '#666' }}>Loading attorney profile...</p>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      );
    }

    // Use the EXACT same URL pattern and mechanism as the working dashboard preview
    const previewUrl = `/simple-preview?subdomain=${encodeURIComponent(subdomain)}&theme=${isDarkTheme ? 'dark' : 'light'}&useEnhancedPreview=true`;

    return (
      <div className="attorney-subdomain-page" style={{ width: '100%', height: '100vh', margin: 0, padding: 0 }}>
        <iframe
          ref={(iframe) => {
            if (iframe) {
              // Use the EXACT same mechanism as the working dashboard preview
              // Wait for PREVIEW_READY message before sending config
              const handleMessage = async (event) => {
                if (event.data && event.data.type === 'PREVIEW_READY') {
                  console.log('🎯 [App.jsx] Received PREVIEW_READY from subdomain iframe, sending config...');

                  try {
                    if (!iframe.contentWindow) return;

                    // Use the EXACT same utility function as the working dashboard preview
                    const { previewConfig } = await import('./utils/previewConfigHandler').then(module =>
                      module.createAttorneyPreviewConfig(attorneyProfile)
                    ).catch(() => {
                      // Fallback config if utility fails
                      return {
                        previewConfig: {
                          firmName: attorneyProfile.firm_name || attorneyProfile.firmName || 'Law Firm',
                          attorneyName: attorneyProfile.attorney_name || 'Attorney',
                          vapi_assistant_id: attorneyProfile.vapi_assistant_id,
                          vapiAssistantId: attorneyProfile.vapi_assistant_id
                        }
                      };
                    });

                    // Add the theme and enhanced preview flags
                    previewConfig.theme = isDarkTheme ? 'dark' : 'light';
                    previewConfig.useEnhancedPreview = true;

                    // Ensure the Vapi assistant ID is set (critical for functionality)
                    if (attorneyProfile.vapi_assistant_id) {
                      previewConfig.vapi_assistant_id = attorneyProfile.vapi_assistant_id;
                      previewConfig.vapiAssistantId = attorneyProfile.vapi_assistant_id; // Include both formats for compatibility
                    }

                    // Send the complete config via postMessage (same as dashboard preview)
                    iframe.contentWindow.postMessage({
                      type: 'UPDATE_PREVIEW_CONFIG',
                      config: previewConfig
                    }, '*');

                    console.log('🎯 [App.jsx] Sent complete config to subdomain iframe via postMessage:', previewConfig);
                  } catch (error) {
                    console.error('🚨 [App.jsx] Error sending config via postMessage:', error);
                  }

                  // Remove the event listener after sending config
                  window.removeEventListener('message', handleMessage);
                }
              };

              // Add message listener for PREVIEW_READY
              window.addEventListener('message', handleMessage);

              // Also set up iframe onload as fallback
              iframe.onload = () => {
                console.log('🎯 [App.jsx] Subdomain iframe loaded, waiting for PREVIEW_READY message...');
              };
            }
          }}
          src={previewUrl}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            margin: 0,
            padding: 0
          }}
          title={`${attorneyProfile.firm_name || 'Attorney'} Preview`}
        />
      </div>
    );
  }

  // Otherwise, render the regular home page
  return (
    <>

      {isLoading ? (
        <div className="loading-indicator">Loading...</div>
      ) : (
        <>
          {!callActive && !showAttorneyInfo && !showCallSummary && (
            <div className="start-button-container" ref={buttonRef}>
              <Button
                onClick={handleStartCall}
                label={buttonText || "Get Started"}
                mascot="/PRIMARY CLEAR.png"
                isLoading={false}
              />
            </div>
          )}

          {/* Call Transition Animation */}
          <CallTransition
            isActive={showTransition}
            onTransitionComplete={handleTransitionComplete}
            buttonPosition={buttonPosition}
            mascotUrl="/PRIMARY CLEAR.png"
          />

          {callActive && (
            <div className={`call-card-container ${callActive ? 'active' : ''}`}>
              <div className="call-card">
                <VapiCall
                  key={vapiCallKey} // Use stable key to prevent unnecessary unmounting
                  onEndCall={endCall}
                  subdomain={subdomain}
                  assistantId={DEFAULT_ASSISTANT_ID} // Always use the default assistant ID for the home page
                  forceDefaultAssistant={true} // Force using the default assistant
                  initializationDelay={1200}
                  showDebugPanel={false} // Disable the debug panel
                />
              </div>
            </div>
          )}

          <HomeContent
            showAttorneyInfo={showAttorneyInfo}
            callData={callData}
            showCallSummary={showCallSummary}
            setShowAttorneyInfo={setShowAttorneyInfo}
            setShowCallSummary={setShowCallSummary}
          />
        </>
      )}
    </>
  );
};

// Home component continued
const HomeContent = ({ showAttorneyInfo, callData, showCallSummary, setShowAttorneyInfo, setShowCallSummary }) => (
  <>
    {showAttorneyInfo && callData && (
      <div className="attorney-info-container">
        <div className="map-dossier-container">
          <MapView attorney={callData.attorney} />
          <AttorneyDossier attorney={callData.attorney} />
        </div>
        <button className="back-button" onClick={() => setShowAttorneyInfo(false)}>
          Back to Start
        </button>
      </div>
    )}

    {showCallSummary && callData && (
      <div className="call-summary-container">
        <CallSummary data={callData.summary} />
        <button className="back-button" onClick={() => setShowCallSummary(false)}>
          Back to Start
        </button>
      </div>
    )}
  </>
);

// Use the inline Home component directly
const LazyHome = Home;

// CRITICAL FIX: Lazy load PreviewFrameLoader with fallback
const LazyPreviewFrameLoader = React.lazy(() =>
  import('./components/preview/PreviewFrameLoader.jsx').catch(() => ({
    default: () => <div>Preview loading...</div>
  }))
);

function App() {
  // DEBUG: Very early App component logging
  console.log('🔥 [App.jsx] App component is starting!');

  const location = useLocation();
  const { user } = useAuth();

  // DEBUG: Log auth state
  console.log('🔥 [App.jsx] Auth state:', {
    user: !!user,
    userEmail: user?.email,
    pathname: location.pathname,
    isAuthenticated: !!user,
    hasSession: !!user?.id
  });

  // CRITICAL FIX: Store sign-in page condition for conditional rendering (no early return)
  const shouldShowSignInPage = location.pathname === '/dashboard' && !user;

  // CRITICAL FIX: Initialize all state with safe defaults
  const [callActive, setCallActive] = useState(false)
  const [showAttorneyInfo, setShowAttorneyInfo] = useState(false)
  const [showCallSummary, setShowCallSummary] = useState(false)
  const [callData, setCallData] = useState(null)
  const [subdomain, setSubdomain] = useState('default') // FIXED: Default to 'default' instead of null
  const [isAttorneySubdomain, setIsAttorneySubdomain] = useState(false)
  const [attorneyProfile, setAttorneyProfile] = useState(null)
  const [isDevelopment, setIsDevelopment] = useState(false)
  const [isLoading, setIsLoading] = useState(false) // FIXED: Start with false instead of true
  const [availableSubdomains, setAvailableSubdomains] = useState(['default'])
  const [isDarkTheme, setIsDarkTheme] = useState(true)
  const [showSubdomains, setShowSubdomains] = useState(false)
  const [selectedPracticeArea, setSelectedPracticeArea] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('firm');
  const [showAuthOverlay, setShowAuthOverlay] = useState(false);
  const [configMode, setConfigMode] = useState('url');

  // Add missing state variables with safe defaults
  const [firmName, setFirmName] = useState('Smith & Associates, LLP');
  const [logoUrl, setLogoUrl] = useState('');
  const [state, setState] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2c3e50');
  const [secondaryColor, setSecondaryColor] = useState('#3498db');
  const [buttonColor, setButtonColor] = useState('#3498db');
  const [backgroundColor, setBackgroundColor] = useState('#f0f4f8');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.3);
  const [buttonText, setButtonText] = useState('Start Consultation');
  const [buttonOpacity, setButtonOpacity] = useState(1);
  const [previewHeight, setPreviewHeight] = useState(600);
  const [practiceDescription, setPracticeDescription] = useState('**Welcome to our legal practice**\n\nOur team of experienced attorneys is dedicated to providing you with exceptional legal representation.');
  const [welcomeMessage, setWelcomeMessage] = useState('Hello, I\'m an AI assistant from Smith & Associates. How can I help you today?');
  const [informationGathering, setInformationGathering] = useState('To better assist you, I\'ll need a few details about your situation.');
  const [attorneyName, setAttorneyName] = useState('John Smith');
  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.2);
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');
  const [firmUrl, setFirmUrl] = useState('');
  const [isUrlLoading, setIsUrlLoading] = useState(false);

  const iframeRef = useRef(null);

  // CRITICAL FIX: Move routing decision logging outside of JSX (after state declarations)
  const routingDecision = {
    isAttorneySubdomain,
    user: !!user,
    userEmail: user?.email,
    subdomain,
    hostname: window.location.hostname
  };
  console.log('🔍 [App.jsx] ROOT ROUTE DECISION:', routingDecision);

  const redirectDecision = {
    hasUser: !!user,
    userEmail: user?.email,
    redirectingTo: user ? '/dashboard' : '/home'
  };
  console.log('🔍 [App.jsx] REDIRECT DECISION:', redirectDecision);

  // CRITICAL FIX: Simplified practice areas
  const practiceAreas = {
    'Personal Injury': {
      questions: "I want to know the circumstances of their injury, including the date, location, and how it occurred.",
      practiceDescription: "**Our firm specializes in personal injury law**",
      welcomeMessage: "Welcome to our personal injury consultation.",
      informationGathering: "I want to know the circumstances of your injury."
    },
    'Family Law': {
      questions: "I need to understand the nature of their family law issue.",
      practiceDescription: "**Our firm is dedicated to helping families**",
      welcomeMessage: "Welcome to our family law consultation.",
      informationGathering: "I need to understand the nature of your family law issue."
    },
    'Criminal Defense': {
      questions: "I need to know the charges against the client.",
      practiceDescription: "## Criminal Defense Experts",
      welcomeMessage: "Thank you for considering our firm for your criminal defense needs.",
      informationGathering: "Please tell me about the charges you're facing."
    }
  };

  // CRITICAL FIX: Safe call management functions
  const startCall = (config) => {
    console.log('Starting call with config:', config);
    setCallActive(true);
    setCallData(config);
  };

  const endCall = () => {
    console.log('Ending call');
    setCallActive(false);
    setCallData(null);
  };

  // CRITICAL FIX: Safe Vapi call key
  const vapiCallKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
  // Handle practice area selection
  const handlePracticeAreaChange = (e) => {
    const area = e.target.value;
    setSelectedPracticeArea(area);
    if (area && practiceAreas[area]) {
      setWelcomeMessage(practiceAreas[area].welcomeMessage);
      setInformationGathering(practiceAreas[area].informationGathering);
      setPracticeDescription(practiceAreas[area].practiceDescription);
    }
  };

  // Handle file upload for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        try {
          const imageId = storeImage(reader.result);
          setLogoUrl(imageId);
        } catch (error) {
          console.error('Failed to store image:', error);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => setLogoUrl('');
  const goToPreview = () => setShowPreview(true);

  // CRITICAL FIX: Simplified URL submit without complex scraping
  const handleUrlSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();

    let urlToProcess = firmUrl;
    if (e && e.detail && e.detail.url) {
      urlToProcess = e.detail.url;
      setFirmUrl(urlToProcess);
    }

    if (!urlToProcess) {
      alert('Please enter a URL');
      return;
    }

    setIsUrlLoading(true);

    try {
      // Simple domain extraction without complex scraping
      const domain = urlToProcess.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
      const generatedFirmName = domain
        .split(/[.-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + " Law";

      setFirmName(generatedFirmName);
      setWelcomeMessage('Hello, I\'m an AI assistant from ' + generatedFirmName + '. How can I help you today?');
      setConfigMode('manual');

      toast.success('Basic configuration extracted from URL!');
    } catch (error) {
      console.error('Error processing URL:', error);
      toast.error('Failed to process URL');
    } finally {
      setIsUrlLoading(false);
    }
  };

  // Helper functions
  const hexToRgb = (hex) => {
    hex = hex.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `${r}, ${g}, ${b}`;
  };

  const getContrastColor = (hexColor) => {
    let hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Theme management
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');
    if (isDarkTheme) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [isDarkTheme]);

  const toggleTheme = () => setIsDarkTheme(prev => !prev);
  const handleAuthSuccess = () => setShowAuthOverlay(false);

  // CRITICAL FIX: Safe subdomain detection with timeout and fallbacks
  useEffect(() => {
    console.log('🚀 [App] Initializing with safe subdomain detection...');

    // Set a maximum timeout to prevent infinite loading
    const maxLoadingTimeout = setTimeout(() => {
      console.log('🚀 [App] ⚠️ Maximum loading timeout reached, forcing app to load');
      setIsLoading(false);
      setSubdomain('default');
    }, 2000); // 2 second maximum

    const initializeApp = async () => {
      try {
        // Initialize production environment first (critical for production)
        try {
          const { initializeProductionEnvironment } = await import('./config/productionEnvironment.js');
          initializeProductionEnvironment();
          console.log('✅ Production environment initialized');
        } catch (error) {
          console.warn('⚠️ Production environment initialization failed:', error);
        }

        // Initialize Supabase safely
        try {
          await initializeSupabaseConfig();
          const result = await verifySupabaseConfig();
          if (result.success) {
            console.log('✅ Supabase configured successfully');
          }
        } catch (error) {
          console.warn('⚠️ Supabase initialization failed, continuing with fallback:', error);
        }

        // Determine environment
        const isDev = import.meta.env.MODE === 'development' ||
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1';
        setIsDevelopment(isDev);

        // Get subdomain safely
        let subdomainValue = 'default';
        try {
          subdomainValue = getCurrentSubdomain() || 'default';
        } catch (error) {
          console.warn('⚠️ Subdomain detection failed, using default:', error);
        }

        console.log('🔍 [App] Subdomain detected:', subdomainValue);
        setSubdomain(subdomainValue);

        // Check if attorney subdomain
        const hostname = window.location.hostname;
        const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';

        if (isLocalhost && subdomainValue === 'default') {
          console.log('🏠 [App] Localhost detected - treating as main domain');
          setIsAttorneySubdomain(false);
          setAttorneyProfile(null);
          setIsLoading(false);
          clearTimeout(maxLoadingTimeout);
          return;
        }

        // CORRECT: Use assistant routing service as PRIMARY method (bypasses Supabase client issues)
        let hasAssistant = false;
        let assistantConfig = null;

        if (subdomainValue !== 'default') {
          try {
            console.log('🎯 [App] Checking for assistant on subdomain:', subdomainValue);

            // OPTIMAL: Simple service - UI from Supabase, Calls from Vapi
            const { simpleSubdomainService } = await import('./services/simpleSubdomainService');
            assistantConfig = await simpleSubdomainService.getSubdomainConfig(subdomainValue);
            hasAssistant = !!assistantConfig;

            console.log('🎯 [App] Assistant routing result:', {
              subdomain: subdomainValue,
              hasAssistant,
              firmName: assistantConfig?.firmName,
              assistant_id: assistantConfig?.assistant_id,
              loadedVia: assistantConfig?.loadedVia
            });

            // NO FALLBACK - Assistant routing service is the correct, production-ready method
            if (!hasAssistant) {
              console.log('📭 [App] No assistant found for subdomain:', subdomainValue);
            }
          } catch (error) {
            console.error('❌ [App] Assistant routing service failed:', error);
            // Log the error but don't try fallback methods that use broken Supabase client
          }
        }

        // Update state based on assistant presence
        setIsAttorneySubdomain(hasAssistant); // Keep variable name for compatibility

        if (hasAssistant && assistantConfig) {
          console.log('✅ [App] Assistant config loaded:', assistantConfig.firmName);
          setAttorneyProfile(assistantConfig); // Keep variable name for compatibility
        } else {
          console.log('📭 [App] No assistant found, treating as main domain');
          setIsAttorneySubdomain(false);
          setAttorneyProfile(null);
        }
      } catch (error) {
        console.error('❌ [App] App initialization failed:', error);
      } finally {
        setIsLoading(false);
        clearTimeout(maxLoadingTimeout);
        console.log('🏁 [App] Initialization complete');
      }
    };

    initializeApp();

    return () => clearTimeout(maxLoadingTimeout);
  }, []);

  // CRITICAL FIX: Show loading only briefly, then render app
  if (isLoading) {
    return (
      <AssistantAwareProvider>
        <div className="app-wrapper">
          <AnimatedBackground />
          <div className="loading-container" style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            color: '#fff'
          }}>
            <div className="loading-spinner" style={{
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #3498db',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              animation: 'spin 2s linear infinite',
              marginBottom: '20px'
            }}></div>
            <p>Loading LegalScout...</p>
          </div>
        </div>
      </AssistantAwareProvider>
    );
  }

  // CRITICAL FIX: Handle sign-in page with conditional rendering (no early return)
  if (shouldShowSignInPage) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: '20px',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h1>Welcome to LegalScout</h1>
        <p>Please sign in to access your dashboard</p>
        <button
          onClick={async () => {
            try {
              console.log('🔐 [App] Starting Google sign-in...');
              const { signInWithGoogle } = await import('./lib/supabase');
              await signInWithGoogle();
            } catch (error) {
              console.error('🔐 [App] Sign-in error:', error);
              alert('Sign-in failed. Please try again.');
            }
          }}
          style={{
            padding: '12px 24px',
            backgroundColor: '#4285f4',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '16px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
        >
          Sign in with Google
        </button>
      </div>
    );
  }

  return (
    <AssistantAwareProvider>
      <div className="app-wrapper">
        <AnimatedBackground />
        <ToastContainer
          position="top-center"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme={isDarkTheme ? 'dark' : 'light'}
        />

        {/* Header - show except on dashboard */}
        {location.pathname !== '/dashboard' && (
          <header className="header">
            <div className="logo-container">
              <img src="/nav_logo.webp" alt="LegalScout Logo" className="logo" />
            </div>
            <Navbar isDarkTheme={isDarkTheme} />
            <ThemeToggle isDark={isDarkTheme} onToggle={toggleTheme} />

            {/* Add SignInButton to header for non-attorney subdomains */}
            {!isAttorneySubdomain && !user && (
              <div className="sign-in-button-container">
                <SignInButton onClick={() => setShowAuthOverlay(true)} />
              </div>
            )}
          </header>
        )}

        <main className="main-content-layer">
          <Routes>
            {/* CRITICAL FIX: Safe routing with fallbacks */}
            <Route path="/" element={
              isAttorneySubdomain ? (
                <Suspense fallback={<SimpleHome isDarkTheme={isDarkTheme} setShowAuthOverlay={setShowAuthOverlay} />}>
                  <LazyHome
                    isLoading={false}
                    callActive={callActive}
                    showAttorneyInfo={showAttorneyInfo}
                    showCallSummary={showCallSummary}
                    attorneyProfile={attorneyProfile}
                    startCall={startCall}
                    endCall={endCall}
                    callData={callData}
                    subdomain={subdomain}
                    setShowAttorneyInfo={setShowAttorneyInfo}
                    setShowCallSummary={setShowCallSummary}
                    buttonText={buttonText}
                    isAttorneySubdomain={isAttorneySubdomain}
                    hideCreateAgentButton={true}
                    isDarkTheme={isDarkTheme}
                    vapiCallKey={vapiCallKey}
                  />
                </Suspense>
              ) : (
                user ? <Navigate to="/dashboard" replace /> : <Navigate to="/home" replace />
              )
            } />

            {/* Home route */}
            <Route path="/home" element={
              <Suspense fallback={<SimpleHome isDarkTheme={isDarkTheme} setShowAuthOverlay={setShowAuthOverlay} />}>
                <LazyHome
                  isLoading={false}
                  callActive={callActive}
                  showAttorneyInfo={showAttorneyInfo}
                  showCallSummary={showCallSummary}
                  attorneyProfile={attorneyProfile}
                  startCall={startCall}
                  endCall={endCall}
                  callData={callData}
                  subdomain={subdomain}
                  setShowAttorneyInfo={setShowAttorneyInfo}
                  setShowCallSummary={setShowCallSummary}
                  buttonText={buttonText}
                  isAttorneySubdomain={isAttorneySubdomain}
                  hideCreateAgentButton={true}
                  isDarkTheme={isDarkTheme}
                  vapiCallKey={vapiCallKey}
                />
              </Suspense>
            } />

            {/* All other routes preserved */}
            <Route path="/about" element={<AboutPage />} />
            <Route path="/contact" element={<div>Contact Page Coming Soon</div>} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/test" element={<TestComponent />} />
            <Route path="/subdomain-test" element={<SubdomainTestPage />} />
            <Route path="/attorney-profile-test" element={<AttorneyProfileTest />} />
            <Route path="/complete-profile" element={<SimpleCompleteProfile />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/crm-demo" element={<CrmDemo />} />

            <Route path="/call-control" element={
              <Suspense fallback={<div className="loading-container"><p>Loading call control...</p></div>}>
                <CallControl />
              </Suspense>
            } />

            <Route path="/demo" element={
              <Suspense fallback={<div className="loading-container"><p>Loading demo...</p></div>}>
                <SimpleDemoPage
                  firmName={firmName}
                  logoUrl={logoUrl}
                  state={state}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  setButtonColor={setButtonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  practiceDescription={practiceDescription}
                  previewHeight={previewHeight}
                  setPreviewHeight={setPreviewHeight}
                  attorneyName={attorneyName}
                  selectedPracticeArea={selectedPracticeArea}
                  handlePracticeAreaChange={handlePracticeAreaChange}
                  showPreview={showPreview}
                  setShowPreview={setShowPreview}
                  handleLogoUpload={handleLogoUpload}
                  handleRemoveLogo={handleRemoveLogo}
                  practiceAreas={practiceAreas}
                  activeConfigTab={activeConfigTab}
                  setActiveConfigTab={setActiveConfigTab}
                  buttonText={buttonText}
                  setButtonText={setButtonText}
                  buttonOpacity={buttonOpacity}
                  setButtonOpacity={setButtonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  setTextBackgroundColor={setTextBackgroundColor}
                  goToPreview={goToPreview}
                  setFirmName={setFirmName}
                  setAttorneyName={setAttorneyName}
                  setPracticeDescription={setPracticeDescription}
                  setState={setState}
                  setWelcomeMessage={setWelcomeMessage}
                  setInformationGathering={setInformationGathering}
                  setPrimaryColor={setPrimaryColor}
                  setSecondaryColor={setSecondaryColor}
                  setBackgroundColor={setBackgroundColor}
                  setBackgroundOpacity={setBackgroundOpacity}
                  iframeRef={iframeRef}
                  firmUrl={firmUrl}
                  setFirmUrl={setFirmUrl}
                  isLoading={isUrlLoading}
                  handleUrlSubmit={handleUrlSubmit}
                  isDarkTheme={isDarkTheme}
                  handleGetStarted={() => setShowAuthOverlay(true)}
                />
              </Suspense>
            } />

            <Route path="/demo/preview" element={
              <Suspense fallback={<div className="loading-container"><p>Loading preview...</p></div>}>
                <PreviewPage
                  firmName={firmName}
                  attorneyName={attorneyName}
                  darkMode={isDarkTheme}
                  onToggleDarkMode={() => setIsDarkTheme(!isDarkTheme)}
                />
              </Suspense>
            } />

            <Route path="/preview" element={
              <Suspense fallback={<div className="loading-container"><p>Loading preview...</p></div>}>
                <SimplifiedPreview
                  firmName={firmName}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  practiceDescription={practiceDescription}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  theme={isDarkTheme ? 'dark' : 'light'}
                  logoUrl={logoUrl || "/PRIMARY CLEAR.png"}
                  buttonText={buttonText || "Start Consultation"}
                  buttonOpacity={buttonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  mascot="/PRIMARY CLEAR.png"
                  vapiInstructions="You are a general legal assistant."
                />
              </Suspense>
            } />

            <Route path="/simple-preview" element={<SimplePreviewPage />} />

            <Route path="/preview-frame" element={
              <Suspense fallback={<div>Loading preview frame...</div>}>
                <LazyPreviewFrameLoader />
              </Suspense>
            } />

            {/* Test routes */}
            <Route path="/preview-frame-test" element={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <h1>Simple Preview Test</h1>
                <p>This page is for testing the simple preview route directly.</p>
              </div>
            } />

            <Route path="/test-route" element={
              <div style={{ padding: '20px', textAlign: 'center', color: '#fff' }}>
                <h1>✅ Test Route Works!</h1>
                <p>If you can see this, routing is working correctly.</p>
                <p>Current user: {user ? user.email : 'Not logged in'}</p>
                <button onClick={() => setShowAuthOverlay(true)}>
                  {user ? 'Switch Account' : 'Sign In'}
                </button>
              </div>
            } />

            {/* Additional Development & Testing Routes */}

            <Route path="/subdomain-editor" element={<SubdomainEditorDemo />} />
            <Route path="/test-supabase" element={<TestSupabase />} />
            <Route path="/supabase-test" element={<SupabaseTest />} />
            <Route path="/tool-debugger" element={<ToolDebugger />} />
            <Route path="/vapi-test" element={<VapiIntegrationTest />} />

            {/* Sign-up route (alias for demo) */}
            <Route path="/signup" element={
              <Suspense fallback={<div className="loading-container"><p>Loading sign-up...</p></div>}>
                <SimpleDemoPage
                  firmName={firmName}
                  logoUrl={logoUrl}
                  state={state}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  setButtonColor={setButtonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  practiceDescription={practiceDescription}
                  previewHeight={previewHeight}
                  setPreviewHeight={setPreviewHeight}
                  attorneyName={attorneyName}
                  selectedPracticeArea={selectedPracticeArea}
                  handlePracticeAreaChange={handlePracticeAreaChange}
                  showPreview={showPreview}
                  setShowPreview={setShowPreview}
                  handleLogoUpload={handleLogoUpload}
                  handleRemoveLogo={handleRemoveLogo}
                  practiceAreas={practiceAreas}
                  activeConfigTab={activeConfigTab}
                  setActiveConfigTab={setActiveConfigTab}
                  buttonText={buttonText}
                  setButtonText={setButtonText}
                  buttonOpacity={buttonOpacity}
                  setButtonOpacity={setButtonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  setTextBackgroundColor={setTextBackgroundColor}
                  goToPreview={goToPreview}
                  setFirmName={setFirmName}
                  setAttorneyName={setAttorneyName}
                  setPracticeDescription={setPracticeDescription}
                  setState={setState}
                  setWelcomeMessage={setWelcomeMessage}
                  setInformationGathering={setInformationGathering}
                  setPrimaryColor={setPrimaryColor}
                  setSecondaryColor={setSecondaryColor}
                  setBackgroundColor={setBackgroundColor}
                  setBackgroundOpacity={setBackgroundOpacity}
                  iframeRef={iframeRef}
                  firmUrl={firmUrl}
                  setFirmUrl={setFirmUrl}
                  isLoading={isUrlLoading}
                  handleUrlSubmit={handleUrlSubmit}
                  isDarkTheme={isDarkTheme}
                  handleGetStarted={() => setShowAuthOverlay(true)}
                />
              </Suspense>
            } />
          </Routes>
        </main>

        {/* Auth Overlay */}
        <AuthOverlay
          isOpen={showAuthOverlay}
          onClose={() => setShowAuthOverlay(false)}
          onSuccess={handleAuthSuccess}
        />

        {/* Mobile Activate Assistant */}
        <Suspense fallback={null}>
          <MobileActivateAssistant
            onActivated={(config) => {
              console.log('[App] Assistant activated:', config);
              if (config && config.id) {
                setAttorneyProfile(config);
              }
            }}
          />
        </Suspense>

        {/* PHASE 1 COMPLETE - Test component removed */}
      </div>
    </AssistantAwareProvider>
  );
}

export default withDevTools(App, {
  displayName: 'LegalScoutApp',
  type: 'container',
  description: 'Main application container with safe loading',
  responsibleFor: ['call initiation', 'layout management', 'state control']
});
