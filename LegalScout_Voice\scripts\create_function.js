
    import fs from 'fs';
    import { spawn } from 'child_process';
    import { fileURLToPath } from 'url';
    import path from 'path';

    // Read the function definition
    const functionDef = JSON.parse(fs.readFileSync(process.argv[2], 'utf-8'));

    // Create a simple protocol to communicate with the server
    const serverProcess = spawn('node', [process.argv[3]]);

    // Send the define_function command
    const command = {
        type: 'call_tool',
        id: '1',
        name: 'define_function',
        arguments: functionDef
    };

    serverProcess.stdin.write(JSON.stringify(command) + '\n');

    // Handle server response
    let response = '';
    serverProcess.stdout.on('data', (data) => {
        response += data.toString();

        // Check if we have a complete response
        if (response.includes('\n')) {
            const lines = response.split('\n');
            for (const line of lines) {
                if (line.trim()) {
                    try {
                        const parsed = JSON.parse(line);
                        console.log(JSON.stringify(parsed));
                    } catch (error) {
                        console.error('Error parsing response:', line);
                    }
                }
            }

            // Exit after getting a response
            serverProcess.kill();
            process.exit(0);
        }
    });

    // Handle errors
    serverProcess.stderr.on('data', (data) => {
        console.error(data.toString());
    });

    // Handle server exit
    serverProcess.on('close', (code) => {
        console.log(`Server process exited with code ${code}`);
        process.exit(code);
    });
    