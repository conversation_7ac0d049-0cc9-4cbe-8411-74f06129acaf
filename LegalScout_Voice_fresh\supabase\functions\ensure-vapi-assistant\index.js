// Supabase Edge Function to ensure attorneys have valid Vapi assistants
// This function can be called via a database trigger or directly via the Supabase REST API

// Import Supabase JS client
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
const VAPI_API_KEY = Deno.env.get('VAPI_SECRET_KEY')
const VAPI_API_URL = 'https://api.vapi.ai'

// Create Supabase client with service role key
const supabase = createClient(
  SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Create a Vapi assistant configuration from attorney data
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Object} - The assistant configuration
 */
function createAssistantConfig(attorney) {
  return {
    name: attorney.firm_name || 'Legal Assistant',
    firstMessage: attorney.welcome_message || 'Hello, how can I help you with your legal needs today?',
    instructions: attorney.vapi_instructions || 'You are a legal assistant helping potential clients understand their legal needs.',
    voice: {
      provider: attorney.voice_provider || '11labs',
      voiceId: attorney.voice_id || 'sarah'
    },
    llm: {
      provider: "openai",
      model: attorney.ai_model || "gpt-4o"
    },
    transcriber: {
      provider: "deepgram",
      model: "nova-3"
    }
  }
}

/**
 * Verify that a Vapi assistant exists
 * @param {string} assistantId - The Vapi assistant ID to verify
 * @returns {Promise<boolean>} - Whether the assistant exists
 */
async function verifyAssistant(assistantId) {
  try {
    const response = await fetch(`${VAPI_API_URL}/assistants/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    return response.ok
  } catch (error) {
    console.error(`Error verifying assistant ${assistantId}:`, error)
    return false
  }
}

/**
 * Create a new Vapi assistant
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} - The created assistant
 */
async function createAssistant(config) {
  try {
    const response = await fetch(`${VAPI_API_URL}/assistants`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to create assistant: ${response.status} ${response.statusText} - ${errorText}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error('Error creating assistant:', error)
    throw error
  }
}

/**
 * Update a Vapi assistant
 * @param {string} assistantId - The assistant ID
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} - The updated assistant
 */
async function updateAssistant(assistantId, config) {
  try {
    const response = await fetch(`${VAPI_API_URL}/assistants/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to update assistant: ${response.status} ${response.statusText} - ${errorText}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error(`Error updating assistant ${assistantId}:`, error)
    throw error
  }
}

/**
 * Ensure an attorney has a valid Vapi assistant
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The result object
 */
async function ensureAttorneyAssistant(attorney) {
  try {
    // Check if attorney already has a valid assistant ID
    if (attorney.vapi_assistant_id) {
      // Verify the assistant exists in Vapi
      const assistantExists = await verifyAssistant(attorney.vapi_assistant_id)
      
      if (assistantExists) {
        // Update the assistant with the latest data
        const config = createAssistantConfig(attorney)
        const updatedAssistant = await updateAssistant(attorney.vapi_assistant_id, config)
        
        return {
          success: true,
          action: 'updated',
          assistantId: attorney.vapi_assistant_id,
          attorney: attorney
        }
      }
    }
    
    // Create a new assistant
    const config = createAssistantConfig(attorney)
    const newAssistant = await createAssistant(config)
    
    // Update attorney record with new assistant ID
    const { data, error } = await supabase
      .from('attorneys')
      .update({ 
        vapi_assistant_id: newAssistant.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorney.id)
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    return {
      success: true,
      action: 'created',
      assistantId: newAssistant.id,
      attorney: data
    }
  } catch (error) {
    console.error(`Error ensuring assistant for attorney ${attorney.id}:`, error)
    return {
      success: false,
      error: error.message,
      attorney: attorney
    }
  }
}

// Handle HTTP requests
Deno.serve(async (req) => {
  // CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Content-Type': 'application/json'
  }
  
  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers })
  }
  
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers }
      )
    }
    
    // Parse request body
    const body = await req.json()
    
    // Check for attorney ID
    if (!body.attorneyId) {
      return new Response(
        JSON.stringify({ error: 'Attorney ID is required' }),
        { status: 400, headers }
      )
    }
    
    // Get attorney from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', body.attorneyId)
      .single()
    
    if (error || !attorney) {
      return new Response(
        JSON.stringify({ error: `Attorney not found: ${error?.message || 'Not found'}` }),
        { status: 404, headers }
      )
    }
    
    // Ensure attorney has a valid assistant
    const result = await ensureAttorneyAssistant(attorney)
    
    // Return result
    return new Response(
      JSON.stringify(result),
      { status: result.success ? 200 : 500, headers }
    )
  } catch (error) {
    // Handle errors
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers }
    )
  }
})
