 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
 🚨 [CriticalProductionFix] Starting critical production fixes...
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 🔧 [FinalLabelFix] Starting final label accessibility fix...
 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
 ✅ [FinalLabelFix] Final label fix initialized and monitoring
 🧪 [TestAssistantIDPropagation] Starting test...
 ✅ [TestAssistantIDPropagation] Test script loaded
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
 🧹 [VapiAssistantCleanup] Starting cleanup utility...
 ✅ [VapiAssistantCleanup] Cleanup utility loaded
 💡 Usage:
   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
 🗑️ [PURGE] Starting immediate assistant purge...
 🚀 STARTING PURGE NOW!
 ✅ [PURGE] Purge script loaded and will execute in 3 seconds...
 🛑 To cancel, run: clearTimeout() or refresh the page
fix-joespizza-assistant.js:1 Uncaught 
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module (at production-debug-vapi.js:82:16)
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 [Supabase] Created clean fetch from iframe to bypass interceptors
supabase.js:130 Supabase client initialized successfully with proper headers
supabase.js:133 Testing Supabase connection...
supabase.js:168 Supabase client ready for use
supabase.js:176 Attaching Supabase client to window.supabase
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
AssistantAwareContext.jsx:12 Uncaught TypeError: Cannot read properties of undefined (reading 'createContext')
    at AssistantAwareContext.jsx:12:45
(anonymous) @ AssistantAwareContext.jsx:12
vapiLoader.js:62 [VapiLoader] ❌ Failed to import @vapi-ai/web package: TypeError: Cannot read properties of undefined (reading 'createContext')
    at AssistantAwareContext.jsx:12:45
overrideMethod @ hook.js:608
(anonymous) @ vapiLoader.js:62
await in (anonymous)
Os @ vapiLoader.js:85
(anonymous) @ vapiLoader.js:174
index.ts:5 Loaded contentScript
vapiLoader.js:123 
            
            
           GET https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js net::ERR_NAME_NOT_RESOLVED
(anonymous) @ vapiLoader.js:123
Xn @ vapiLoader.js:94
(anonymous) @ vapiLoader.js:74
await in (anonymous)
Os @ vapiLoader.js:85
(anonymous) @ vapiLoader.js:174
vapiLoader.js:80 [VapiLoader] ❌ CDN fallback also failed: Error: Failed to load from CDN
    at s.onerror (vapiLoader.js:120:14)
overrideMethod @ hook.js:608
(anonymous) @ vapiLoader.js:80
await in (anonymous)
Os @ vapiLoader.js:85
(anonymous) @ vapiLoader.js:174
vapiLoader.js:175 [VapiLoader] ❌ Auto-load failed: Error: Failed to load Vapi SDK from all sources
    at vapiLoader.js:83:13
overrideMethod @ hook.js:608
(anonymous) @ vapiLoader.js:175
Promise.catch
(anonymous) @ vapiLoader.js:174
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
legalscout.net/:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
overrideMethod @ hook.js:608
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
overrideMethod @ hook.js:608
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
overrideMethod @ hook.js:608
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
