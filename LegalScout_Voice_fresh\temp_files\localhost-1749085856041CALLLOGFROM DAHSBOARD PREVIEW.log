Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:475 🚀 [PREVIEW CALL START] Starting consultation...
EnhancedPreviewNew.jsx:476 🎯 [PREVIEW CALL START] Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 💬 [PREVIEW CALL START] Welcome message: Hello! How can I help you today?
 🔊 [PREVIEW CALL START] Voice settings: {voiceId: 'sarah', voiceProvider: '11labs'}
 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Checking for Vapi public key...
 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] Using API key: 310f0d43...
 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
 [useVapiCall] Creating Vapi instance directly using official pattern
 [useVapiCall] Loading Vapi SDK using vapiLoader
 [VapiLoader] Vapi SDK already loaded
 🚀 VapiCall component mounted, preparing to start call...
 📊 Current status: idle
 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🔧 Vapi instance available: false
 ⚙️ Processed config: null
 ⏸️ Not ready to initialize yet - missing assistantId or vapi
 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 ⏸️ Vapi instance: false
 Status changed to: idle
 CALL_STATUS.CONNECTED value: connected
 🔄 VapiCall component received dossier update: {}
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 voiceId: sarah
 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: true
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: {}
useVapiCall.js?t=1749083508362:826 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
vapiLoader.js:165 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
useVapiCall.js?t=1749083508362:831 [useVapiCall] Vapi instance created successfully
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:1102 Requesting microphone permission...
useVapiCall.js?t=1749083508362:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:1107 Available audio input devices: (5) [{…}, {…}, {…}, {…}, {…}]
useVapiCall.js?t=1749083508362:1125 Using audio constraints: {audio: {…}}
useVapiCall.js?t=1749083508362:1129 Microphone permission granted with device: Default - Microphone (Yeti Classic)
useVapiCall.js?t=1749083508362:1177 Starting call with existing Vapi instance using direct pattern
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantOverrides: 'Set', vapi: true, processedConfig: true}
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', propAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', forceDefaultAssistant: false, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', subdomain: 'damonkost'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> connecting
useVapiCall.js?t=1749083508362:1217 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749083508362:1228 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js?t=1749083508362:1230 [useVapiCall] Call started successfully: null
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749083508362:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749083508362:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: {firmName: 'LegalScout', welcomeMessage: 'Hello! How can I help you today?', voiceId: 'sarah', voiceProvider: '11labs', initialMessage: 'Hello! How can I help you today?', …}
VapiCall.jsx:1790 mergedAssistantOverrides: {firstMessage: 'Hello! How can I help you today?', model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1819 Found welcome message from mergedCustomInstructions.welcomeMessage: Hello! How can I help you today?
VapiCall.jsx:1837 Adding welcome message to UI: Hello! How can I help you today?
VapiCall.jsx:1842 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:1845 Voice settings: {voiceId: 'sarah', voiceProvider: '11labs'}
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1775 Added force-visible class to call interface
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749083508362:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VM6650:4 enumerateDevices took longer than expected: 149
value @ VM6650:4
value @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
$ @ VM6650:4
g.enumDWrapper @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
_ @ VM6650:4
U.camPreferences @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
dispatch @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
C @ VM6650:4
postMessage
value @ @vapi-ai_web.js?v=4f7759dd:4818
value @ @vapi-ai_web.js?v=4f7759dd:6681
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:6035
d @ @vapi-ai_web.js?v=4f7759dd:195
s2 @ @vapi-ai_web.js?v=4f7759dd:207
Promise.then
d @ @vapi-ai_web.js?v=4f7759dd:199
s2 @ @vapi-ai_web.js?v=4f7759dd:207
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:212
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:204
o.value @ @vapi-ai_web.js?v=4f7759dd:6052
start @ @vapi-ai_web.js?v=4f7759dd:9971
await in start
(anonymous) @ useVapiCall.js?t=1749083508362:1180
Promise.then
(anonymous) @ useVapiCall.js?t=1749083508362:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1749083508362:759
(anonymous) @ useVapiCall.js?t=1749083508362:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19444
commitRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=4e23eb9f:9135
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:18655
VM6650:4 enumerateDevices took longer than expected: 66
value @ VM6650:4
value @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g.enumDWrapper @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
nA @ VM6650:4
A.stream @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
dispatch @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
C @ VM6650:4
postMessage
value @ @vapi-ai_web.js?v=4f7759dd:4818
value @ @vapi-ai_web.js?v=4f7759dd:6681
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:6035
d @ @vapi-ai_web.js?v=4f7759dd:195
s2 @ @vapi-ai_web.js?v=4f7759dd:207
Promise.then
d @ @vapi-ai_web.js?v=4f7759dd:199
s2 @ @vapi-ai_web.js?v=4f7759dd:207
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:212
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:204
o.value @ @vapi-ai_web.js?v=4f7759dd:6052
start @ @vapi-ai_web.js?v=4f7759dd:9971
await in start
(anonymous) @ useVapiCall.js?t=1749083508362:1180
Promise.then
(anonymous) @ useVapiCall.js?t=1749083508362:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1749083508362:759
(anonymous) @ useVapiCall.js?t=1749083508362:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19444
commitRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=4e23eb9f:9135
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
VM6650:4 enumerateDevices took longer than expected: 59
value @ VM6650:4
value @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g.enumDWrapper @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
b @ VM6650:4
g.getCurrentDevices @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
NA @ VM6650:4
hA.initialState @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
hA @ VM6650:4
wA @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
dispatch @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
Promise.then
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
g @ VM6650:4
i @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
eval @ VM6650:4
C @ VM6650:4
postMessage
value @ @vapi-ai_web.js?v=4f7759dd:4818
value @ @vapi-ai_web.js?v=4f7759dd:6681
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:6035
d @ @vapi-ai_web.js?v=4f7759dd:195
s2 @ @vapi-ai_web.js?v=4f7759dd:207
Promise.then
d @ @vapi-ai_web.js?v=4f7759dd:199
s2 @ @vapi-ai_web.js?v=4f7759dd:207
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:212
(anonymous) @ @vapi-ai_web.js?v=4f7759dd:204
o.value @ @vapi-ai_web.js?v=4f7759dd:6052
start @ @vapi-ai_web.js?v=4f7759dd:9971
await in start
(anonymous) @ useVapiCall.js?t=1749083508362:1180
Promise.then
(anonymous) @ useVapiCall.js?t=1749083508362:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1749083508362:759
(anonymous) @ useVapiCall.js?t=1749083508362:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19444
commitRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=4e23eb9f:9135
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: signaling
 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Direct Vapi call started successfully: {id: '78d49515-a870-456b-abac-078f3675aee2', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-05T01:10:30.344Z', updatedAt: '2025-06-05T01:10:30.344Z', type: 'webCall', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490858299440.4771965745897959', …}
 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490858320180.49820629295310137', error: null, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490858320180.49820629295310137', error: null, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490858320180.49820629295310137', error: null, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490858320180.49820629295310137', error: null, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490858320180.49820629295310137', error: null, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-xvfff', what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-xvfff', what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: sfu
 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-xvfff', what: 'iframe-call-message', …}
 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-xvfff', what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-xvfff', what: 'iframe-call-message', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '6059da63-595d-4e17-835f-98428dbb95cd', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '6059da63-595d-4e17-835f-98428dbb95cd', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-participant', id: '6059da63-595d-4e17-835f-98428dbb95cd', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'update-participant', id: '6059da63-595d-4e17-835f-98428dbb95cd', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'update-participant', id: '6059da63-595d-4e17-835f-98428dbb95cd', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-participant', id: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'update-participant', id: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'update-participant', id: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: 'listening', fromId: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: 'listening', fromId: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: 'listening', fromId: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'app-message', data: 'listening', fromId: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: 'ec9f3c37-ba1f-46a3-848a-f9bcae194d1b', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedRea…en-labs-blocked-free-plan-and-requested-upgrade"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedRea…en-labs-blocked-free-plan-and-requested-upgrade"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedRea…en-labs-blocked-free-plan-and-requested-upgrade"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedRea…en-labs-blocked-free-plan-and-requested-upgrade"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedRea…en-labs-blocked-free-plan-and-requested-upgrade"}', fromId: '6059da63-595d-4e17-835f-98428dbb95cd', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', …}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade'}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade'}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Processing audio level event: {6059da63-595d-4e17-835f-98428dbb95cd: 0, ec9f3c37-ba1f-46a3-848a-f9bcae194d1b: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490858299440.4771965745897959', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490858299440.4771965745897959', …}
 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490858299440.4771965745897959', …}
 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490858299440.4771965745897959', …}
 Meeting ended due to ejection: Meeting has ended
value @ unknown
value @ unknown
p @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Window message received for processing: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490858299440.4771965745897959'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490858299440.4771965745897959'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490858299440.4771965745897959'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:7167
emit @ @vapi-ai_web.js:9833
(anonymous) @ @vapi-ai_web.js:9924
w.emit @ @vapi-ai_web.js:3072
value @ @vapi-ai_web.js:6899
value @ @vapi-ai_web.js:6747
i2 @ @vapi-ai_web.js:4796
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
g @ unknown
i @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: connecting -> ended
 Ending call...
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490858299440.4771965745897959', from: 'embedded'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Component unmounting - performing Vapi cleanup
 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Event listeners removed from Vapi instance
 Stopping active call during cleanup
 Calling onEndCall callback during unmount
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 VapiCall component unmounting, performing cleanup...
 Call was still initializing during unmount, cancelling initialization
 Skipping onEndCall during initialization cancellation to prevent mount/unmount cycle
 SpeechParticles: Cleaning up component
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 voiceId: sarah
 voiceProvider: 11labs
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
 🧹 [CleanAuthSolution] Token refreshed successfully
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
 🧹 [CleanAuthSolution] Token refreshed successfully
 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
 🧹 [CleanAuthSolution] Token refreshed successfully
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
 🧹 [CleanAuthSolution] Token refreshed successfully
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
