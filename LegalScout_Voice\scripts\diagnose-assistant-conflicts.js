#!/usr/bin/env node

/**
 * Vapi Assistant Conflict Diagnos<PERSON>
 * 
 * This script diagnoses and resolves assistant ID conflicts between
 * Supabase and Vapi using the MCP server for robust operations.
 * 
 * Usage:
 *   node scripts/diagnose-assistant-conflicts.js [options]
 * 
 * Options:
 *   --email <email>     Check specific attorney by email
 *   --fix               Automatically fix conflicts where possible
 *   --dry-run           Show what would be done without making changes
 *   --verbose           Show detailed diagnostics
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from 'dotenv';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');
config({ path: join(rootDir, '.env') });

// Import our services
import { vapiAssistantReconciler } from '../src/services/VapiAssistantReconciler.js';
import { supabase } from '../src/lib/supabase.js';

class AssistantDiagnosticTool {
  constructor() {
    this.options = {
      email: null,
      fix: false,
      dryRun: false,
      verbose: false
    };
  }

  /**
   * Parse command line arguments
   */
  parseArgs() {
    const args = process.argv.slice(2);
    
    for (let i = 0; i < args.length; i++) {
      switch (args[i]) {
        case '--email':
          this.options.email = args[++i];
          break;
        case '--fix':
          this.options.fix = true;
          break;
        case '--dry-run':
          this.options.dryRun = true;
          break;
        case '--verbose':
          this.options.verbose = true;
          break;
        case '--help':
          this.showHelp();
          process.exit(0);
        default:
          console.error(`Unknown option: ${args[i]}`);
          this.showHelp();
          process.exit(1);
      }
    }
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log(`
Vapi Assistant Conflict Diagnostic Script

Usage:
  node scripts/diagnose-assistant-conflicts.js [options]

Options:
  --email <email>     Check specific attorney by email
  --fix               Automatically fix conflicts where possible
  --dry-run           Show what would be done without making changes
  --verbose           Show detailed diagnostics
  --help              Show this help message

Examples:
  # Check all attorneys
  node scripts/diagnose-assistant-conflicts.js

  # Check specific attorney
  node scripts/diagnose-assistant-conflicts.js --email <EMAIL>

  # Fix conflicts automatically
  node scripts/diagnose-assistant-conflicts.js --fix

  # Dry run to see what would be fixed
  node scripts/diagnose-assistant-conflicts.js --fix --dry-run
`);
  }

  /**
   * Run diagnostics for a specific attorney
   */
  async checkSpecificAttorney(email) {
    console.log(`🔍 Checking attorney: ${email}`);
    
    try {
      // Get attorney from database
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', email)
        .single();

      if (error || !attorney) {
        console.error(`❌ Attorney not found: ${email}`);
        return false;
      }

      // Run reconciliation
      const result = await vapiAssistantReconciler.reconcileAttorneyAssistant(attorney);
      
      this.displayResult(email, result);
      
      if (this.options.verbose) {
        console.log('\n📋 Detailed Diagnostics:');
        result.diagnostics.forEach(log => console.log(`  ${log}`));
      }

      return result.success;
      
    } catch (error) {
      console.error(`❌ Error checking attorney ${email}:`, error.message);
      return false;
    }
  }

  /**
   * Run diagnostics for all attorneys
   */
  async checkAllAttorneys() {
    console.log('🚀 Running full assistant diagnostics...\n');
    
    try {
      const results = await vapiAssistantReconciler.runFullDiagnostics();
      
      if (!results.success) {
        console.error('❌ Diagnostics failed:', results.error);
        return false;
      }

      // Display results
      console.log('📊 Diagnostic Results:\n');
      
      results.results.forEach(result => {
        this.displayResult(result.attorney, result);
      });

      // Display summary
      console.log('\n📈 Summary:');
      console.log(`  Total attorneys: ${results.summary.total}`);
      console.log(`  ✅ Validated: ${results.summary.validated}`);
      console.log(`  🔗 Matched: ${results.summary.matched}`);
      console.log(`  ⚠️  Conflicts: ${results.summary.conflicts}`);
      console.log(`  🆕 Need creation: ${results.summary.createNeeded}`);
      console.log(`  ❌ Errors: ${results.summary.errors}`);

      if (this.options.verbose) {
        console.log('\n📋 Full Diagnostics Log:');
        vapiAssistantReconciler.getDiagnostics().forEach(log => {
          console.log(`  ${log}`);
        });
      }

      return true;
      
    } catch (error) {
      console.error('❌ Error running diagnostics:', error.message);
      return false;
    }
  }

  /**
   * Display result for a single attorney
   */
  displayResult(email, result) {
    const statusIcon = result.success ? '✅' : '❌';
    const actionIcon = this.getActionIcon(result.action);
    
    console.log(`${statusIcon} ${email}`);
    console.log(`  ${actionIcon} Action: ${result.action}`);
    console.log(`  📝 Message: ${result.message}`);
    
    if (result.assistantId) {
      console.log(`  🤖 Assistant ID: ${result.assistantId}`);
    }
    
    if (result.conflicts && result.conflicts.length > 0) {
      console.log(`  ⚠️  Conflicts:`);
      result.conflicts.forEach(conflict => {
        console.log(`    - ${conflict.type}: ${conflict.reason || 'Multiple matches found'}`);
        if (conflict.assistants) {
          conflict.assistants.forEach(assistant => {
            console.log(`      • ${assistant.name} (${assistant.id}) - ${assistant.matchReason}`);
          });
        }
      });
    }
    
    console.log('');
  }

  /**
   * Get icon for action type
   */
  getActionIcon(action) {
    switch (action) {
      case 'validated': return '✅';
      case 'matched': return '🔗';
      case 'conflict': return '⚠️';
      case 'create_needed': return '🆕';
      case 'error': return '❌';
      default: return '❓';
    }
  }

  /**
   * Main execution function
   */
  async run() {
    console.log('🔧 Vapi Assistant Conflict Diagnostic Tool\n');
    
    this.parseArgs();
    
    if (this.options.dryRun) {
      console.log('🧪 DRY RUN MODE - No changes will be made\n');
    }

    try {
      let success = false;
      
      if (this.options.email) {
        success = await this.checkSpecificAttorney(this.options.email);
      } else {
        success = await this.checkAllAttorneys();
      }

      if (success) {
        console.log('✅ Diagnostics completed successfully');
        
        if (this.options.fix && !this.options.dryRun) {
          console.log('🔧 Conflicts have been automatically resolved where possible');
        } else if (this.options.fix && this.options.dryRun) {
          console.log('🧪 This was a dry run - no changes were made');
        }
      } else {
        console.log('❌ Diagnostics completed with errors');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('💥 Fatal error:', error.message);
      if (this.options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  }
}

// Run the tool if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tool = new AssistantDiagnosticTool();
  tool.run().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

export default AssistantDiagnosticTool;
