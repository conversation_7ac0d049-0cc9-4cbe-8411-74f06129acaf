/**
 * Check and configure Vapi webhook/server URL settings
 * This script helps diagnose webhook configuration issues
 */

import dotenv from 'dotenv';

dotenv.config();

const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;

if (!VAPI_API_KEY) {
  console.error('❌ VAPI_API_KEY not found in environment variables');
  process.exit(1);
}

/**
 * Check current organization settings
 */
async function checkOrganizationSettings() {
  try {
    console.log('🔍 Checking organization settings...');
    
    const response = await fetch('https://api.vapi.ai/org', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const org = await response.json();
    console.log('✅ Organization settings retrieved');
    console.log('   Organization ID:', org.id);
    console.log('   Name:', org.name);
    console.log('   Server URL:', org.serverUrl || 'NOT CONFIGURED');
    console.log('   Server URL Secret:', org.serverUrlSecret ? 'CONFIGURED' : 'NOT CONFIGURED');
    
    return org;
  } catch (error) {
    console.error('❌ Error checking organization settings:', error.message);
    return null;
  }
}

/**
 * Check assistant configuration
 */
async function checkAssistantConfig(assistantId) {
  try {
    console.log(`🔍 Checking assistant ${assistantId}...`);
    
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const assistant = await response.json();
    console.log('✅ Assistant configuration retrieved');
    console.log('   Assistant ID:', assistant.id);
    console.log('   Name:', assistant.name);
    console.log('   Server URL:', assistant.serverUrl || 'NOT CONFIGURED (using org default)');
    console.log('   Server URL Secret:', assistant.serverUrlSecret ? 'CONFIGURED' : 'NOT CONFIGURED');
    
    return assistant;
  } catch (error) {
    console.error('❌ Error checking assistant configuration:', error.message);
    return null;
  }
}

/**
 * Update organization server URL
 */
async function updateOrganizationServerUrl(serverUrl, serverUrlSecret) {
  try {
    console.log('🔧 Updating organization server URL...');
    
    const response = await fetch('https://api.vapi.ai/org', {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serverUrl: serverUrl,
        serverUrlSecret: serverUrlSecret
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Organization server URL updated successfully');
    console.log('   New Server URL:', result.serverUrl);
    
    return result;
  } catch (error) {
    console.error('❌ Error updating organization server URL:', error.message);
    return null;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Checking Vapi webhook configuration...\n');
    
    // Check current organization settings
    const org = await checkOrganizationSettings();
    console.log('');
    
    // Check your specific assistant
    const assistantId = '9621936c-cb51-489c-a869-0c33052f0e42';
    const assistant = await checkAssistantConfig(assistantId);
    console.log('');
    
    // Determine the correct webhook URL for your deployment
    const webhookUrl = 'https://legalscout.app/api/webhook/vapi-call';
    const webhookSecret = 'legalscout-webhook-secret-2024';
    
    console.log('📋 Recommended webhook configuration:');
    console.log('   Webhook URL:', webhookUrl);
    console.log('   Webhook Secret:', webhookSecret);
    console.log('');
    
    // Check if organization server URL needs to be set/updated
    if (!org || !org.serverUrl || org.serverUrl !== webhookUrl) {
      console.log('⚠️  Organization server URL needs to be configured');
      console.log('   Current:', org?.serverUrl || 'NOT SET');
      console.log('   Should be:', webhookUrl);
      console.log('');
      
      // Ask if user wants to update
      console.log('💡 To fix the webhook issue, you need to:');
      console.log('   1. Set the organization-wide server URL in your Vapi dashboard');
      console.log('   2. Or run this script with --update flag to update automatically');
      console.log('');
      console.log('   Manual setup: https://dashboard.vapi.ai/vapi-api');
      console.log('   Automatic setup: node scripts/check-vapi-webhook-config.js --update');
    } else {
      console.log('✅ Organization server URL is correctly configured');
    }
    
    // Check if --update flag is provided
    if (process.argv.includes('--update')) {
      console.log('🔧 Updating organization server URL automatically...');
      await updateOrganizationServerUrl(webhookUrl, webhookSecret);
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
