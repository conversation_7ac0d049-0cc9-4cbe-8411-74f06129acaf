/**
 * Vapi MCP Server API Route
 *
 * This file contains the API route for the Vapi MCP server.
 * It handles SSE connections for the MCP server.
 */

import { createVapiMcpServer } from '../../server/mcpServerSetup';

/**
 * Handle SSE connections for the Vapi MCP server
 * @param {Request} request - The request object
 * @returns {Response} The response object
 */
export async function GET(request) {
  try {
    // Create the Vapi MCP server
    const server = createVapiMcpServer();
    
    // Handle the SSE connection
    return await server.handleRequest(request);
  } catch (error) {
    console.error('Error handling Vapi MCP server request:', error);
    
    // Return an error response
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

/**
 * Handle POST requests for the Vapi MCP server
 * @param {Request} request - The request object
 * @returns {Response} The response object
 */
export async function POST(request) {
  try {
    // Create the Vapi MCP server
    const server = createVapiMcpServer();
    
    // Handle the POST request
    return await server.handleRequest(request);
  } catch (error) {
    console.error('Error handling Vapi MCP server request:', error);
    
    // Return an error response
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

// Export the route handlers
export { GET as default };
