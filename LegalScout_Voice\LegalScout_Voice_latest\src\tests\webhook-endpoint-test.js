/**
 * Webhook Endpoint Test
 * 
 * This script tests if the webhook endpoint is accessible and working
 */

import fetch from 'node-fetch';

const WEBHOOK_URLS = [
  'https://legalscout.net/api/webhook/vapi-call/minimal',
  'https://legalscout.net/api/webhook/vapi-call'
];

const TEST_PAYLOAD = {
  id: 'endpoint-test-' + Date.now(),
  assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
  status: 'completed',
  duration: 60,
  start_time: new Date().toISOString(),
  end_time: new Date().toISOString(),
  customer: { phone_number: '+1234567890' },
  transcripts: [],
  messages: [],
  tool_executions: [],
  metadata: {}
};

async function testEndpoint(url) {
  console.log(`\n🔍 Testing: ${url}`);
  
  try {
    // Test GET request (should return 405)
    const getResponse = await fetch(url, { method: 'GET' });
    console.log(`  GET response: ${getResponse.status} ${getResponse.statusText}`);
    
    if (getResponse.status === 405) {
      console.log('  ✅ Endpoint exists (405 Method Not Allowed as expected)');
    } else {
      console.log(`  ⚠️  Unexpected GET response: ${getResponse.status}`);
    }

    // Test POST request
    const postResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'LegalScout-Webhook-Test/1.0'
      },
      body: JSON.stringify(TEST_PAYLOAD)
    });

    console.log(`  POST response: ${postResponse.status} ${postResponse.statusText}`);
    
    const responseText = await postResponse.text();
    console.log(`  Response body: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);

    if (postResponse.ok) {
      console.log('  ✅ Webhook POST successful');
      return true;
    } else {
      console.log('  ❌ Webhook POST failed');
      return false;
    }

  } catch (error) {
    console.log(`  ❌ Connection error: ${error.message}`);
    return false;
  }
}

async function runEndpointTests() {
  console.log('🚀 Testing Webhook Endpoints\n');
  
  let workingEndpoint = null;
  
  for (const url of WEBHOOK_URLS) {
    const success = await testEndpoint(url);
    if (success && !workingEndpoint) {
      workingEndpoint = url;
    }
  }

  console.log('\n📊 ENDPOINT TEST SUMMARY:');
  if (workingEndpoint) {
    console.log(`  ✅ Working endpoint found: ${workingEndpoint}`);
    console.log('\n💡 NEXT STEPS:');
    console.log('  1. Configure this URL in Vapi webhook settings');
    console.log('  2. Test with a real Vapi call');
    console.log('  3. Check if data appears in Supabase');
  } else {
    console.log('  ❌ No working endpoints found');
    console.log('\n💡 TROUBLESHOOTING:');
    console.log('  1. Check if the application is deployed');
    console.log('  2. Verify API routes are working');
    console.log('  3. Start local development server');
    console.log('  4. Check network connectivity');
  }
}

runEndpointTests().catch(console.error);
