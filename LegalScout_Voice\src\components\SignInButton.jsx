import React from 'react';
import { Link } from 'react-router-dom';
import './SignInButton.css';

const SignInButton = ({ onClick }) => {
  // If onClick is provided, use it (for the auth overlay)
  // Otherwise, navigate to the login page
  if (onClick) {
    return (
      <button className="sign-in-button" onClick={onClick}>
        Sign In/Sign Up
      </button>
    );
  }

  // Use Link for navigation to the login page
  return (
    <Link to="/login" className="sign-in-button">
      Sign In/Sign Up
    </Link>
  );
};

export default SignInButton;
