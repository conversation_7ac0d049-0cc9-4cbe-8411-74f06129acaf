/**
 * Voice Assistant Configuration Component
 *
 * This component handles the configuration of voice assistants for attorneys.
 * It ensures that attorneys have a valid assistant ID and provides
 * UI for editing voice assistant-related fields.
 *
 * It uses direct API calls to ensure reliable connectivity.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { vapiAssistantService } from '../../services/vapiAssistantService';
import { createLogger } from '../../utils/loggerUtils';
import { supabase } from '../../lib/supabase';
import * as vapiDirectApi from '../../utils/vapiDirectApi';
import './VoiceAssistantConfig.css';

const logger = createLogger('VoiceAssistantConfig');

const VoiceAssistantConfig = ({ attorney, onUpdate }) => {
  // State for assistant data
  const [assistantData, setAssistantData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for edited fields
  const [editedFields, setEditedFields] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // Load assistant data when attorney changes
  useEffect(() => {
    const loadAssistantData = async () => {
      // Clear assistant data if attorney is null
      if (!attorney) {
        setAssistantData(null);
        return;
      }

      // Clear assistant data if attorney has no assistant ID
      if (!attorney.vapi_assistant_id) {
        setAssistantData(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Use direct API call to get assistant data
        logger.info(`Loading assistant data for ${attorney.vapi_assistant_id} using direct API`);

        // Check if API key is available
        const apiKey = vapiDirectApi.getApiKey();
        if (!apiKey) {
          setError('No API key available. Please check your environment variables or contact support.');
          return;
        }

        const assistant = await vapiDirectApi.getAssistant(attorney.vapi_assistant_id);

        if (assistant) {
          logger.info(`Loaded assistant data for ${attorney.vapi_assistant_id}`);
          setAssistantData(assistant);
        } else {
          // Fallback to vapiAssistantService if direct API fails
          logger.warn(`Direct API failed, trying vapiAssistantService for ${attorney.vapi_assistant_id}`);

          try {
            // Ensure connection to service
            await vapiAssistantService.ensureConnection();

            // Get assistant data
            const fallbackAssistant = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);

            if (fallbackAssistant) {
              logger.info(`Loaded assistant data using fallback for ${attorney.vapi_assistant_id}`);
              setAssistantData(fallbackAssistant);
            } else {
              logger.warn(`No assistant found with ID ${attorney.vapi_assistant_id}`);
              setError(`No assistant found with ID ${attorney.vapi_assistant_id}. The assistant may have been deleted or the ID is invalid. Please create a new assistant.`);
            }
          } catch (fallbackErr) {
            logger.error(`Fallback also failed: ${fallbackErr.message}`);
            setError(`Error loading assistant data: ${fallbackErr.message}. Please check your network connection and try again.`);
          }
        }
      } catch (err) {
        logger.error(`Error loading assistant data: ${err.message}`);
        setError(`Error loading assistant data: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadAssistantData();
  }, [attorney]);

  // Handle field changes
  const handleFieldChange = (field, value) => {
    setEditedFields(prev => ({
      ...prev,
      [field]: value
    }));

    setHasChanges(true);
  };

  // Save changes to both Supabase and voice service
  const handleSaveChanges = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if attorney data is available
      if (!attorney) {
        throw new Error('Attorney data is not available. Please refresh the page and try again.');
      }

      // Check if assistant ID is available
      if (!attorney.vapi_assistant_id) {
        throw new Error('No assistant ID found. Please create an assistant first.');
      }

      // Create updated attorney data with edited fields
      const updatedAttorneyData = {
        ...attorney,
        ...editedFields
      };

      // Step 1: Update Supabase first (single source of truth)
      logger.info(`Updating attorney data in Supabase for ${attorney.id}`);
      const { error: supabaseError } = await supabase
        .from('attorneys')
        .update(editedFields)
        .eq('id', attorney.id);

      if (supabaseError) {
        throw new Error(`Error updating Supabase: ${supabaseError.message}`);
      }

      // Step 2: Update voice assistant using direct API
      logger.info(`Updating voice assistant ${attorney.vapi_assistant_id} using direct API`);

      // Map attorney fields to API fields
      const updateData = {};

      if (editedFields.welcome_message !== undefined) {
        updateData.firstMessage = editedFields.welcome_message;
      }

      if (editedFields.vapi_instructions !== undefined) {
        updateData.instructions = editedFields.vapi_instructions;
      }

      if (editedFields.voice_id !== undefined) {
        updateData.voice = {
          ...(assistantData?.voice || {}),
          voiceId: editedFields.voice_id,
          provider: editedFields.voice_provider || attorney.voice_provider || 'playht'
        };
      }

      try {
        // Check if API key is available
        const apiKey = vapiDirectApi.getApiKey();
        if (!apiKey) {
          throw new Error('No API key available. Please check your environment variables or contact support.');
        }

        // Try direct API first
        const updatedAssistant = await vapiDirectApi.updateAssistant(
          attorney.vapi_assistant_id,
          updateData
        );

        if (updatedAssistant) {
          logger.info(`Successfully updated voice assistant using direct API`);
          setAssistantData(updatedAssistant);
        } else {
          // Fallback to service
          logger.warn(`Direct API update failed, trying service`);
          const result = await vapiAssistantService.updateAssistantConfiguration(
            attorney.vapi_assistant_id,
            updatedAttorneyData
          );

          if (!result) {
            throw new Error('Failed to update assistant using both direct API and fallback service');
          }

          // Reload assistant data
          const assistant = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);

          if (!assistant) {
            throw new Error('Failed to reload assistant data after update');
          }

          setAssistantData(assistant);
        }
      } catch (apiError) {
        logger.error(`Error updating voice assistant: ${apiError.message}`);
        // Don't throw here, we've already updated Supabase
        setError(`Warning: Supabase was updated but voice service update failed: ${apiError.message}`);
      }

      // Notify parent component of updates
      if (onUpdate) {
        onUpdate(updatedAttorneyData);
      }

      // Reset edited fields
      setEditedFields({});
      setHasChanges(false);

      // Show success message
      setSuccess('Assistant configuration updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      logger.error(`Error saving assistant configuration: ${err.message}`);
      setError(`Error saving assistant configuration: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Create assistant if one doesn't exist
  const handleCreateAssistant = async () => {
    setLoading(true);
    setError(null);

    try {
      // Check if attorney data is available
      if (!attorney) {
        throw new Error('Attorney data is not available. Please refresh the page and try again.');
      }

      logger.info('Creating new assistant for attorney');

      // Create assistant configuration
      const assistantConfig = {
        name: `${attorney.firm_name || 'Law Firm'} Legal Assistant`,
        instructions: attorney.vapi_instructions ||
          `You are a legal assistant for ${attorney.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
        firstMessage: attorney.welcome_message ||
          `Hello, I'm Scout from ${attorney.firm_name || 'your law firm'}. How can I help you today?`,
        firstMessageMode: "assistant-speaks-first",
        llm: {
          provider: "openai",
          model: attorney.ai_model || "gpt-4o"
        },
        voice: {
          provider: attorney.voice_provider || "11labs",
          voiceId: attorney.voice_id || "sarah"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        }
      };

      // Check if API key is available
      const apiKey = vapiDirectApi.getApiKey();
      if (!apiKey) {
        throw new Error('No API key available. Please check your environment variables or contact support.');
      }

      // Try direct API first
      let assistant;
      try {
        logger.info('Attempting to create assistant using direct API');
        assistant = await vapiDirectApi.createAssistant(assistantConfig);

        if (!assistant) {
          throw new Error('Failed to create assistant using direct API');
        }
      } catch (directApiError) {
        logger.warn(`Direct API failed: ${directApiError.message}, trying service`);

        // Try fallback service
        assistant = await vapiAssistantService.createAssistantForAttorney(attorney);

        if (!assistant) {
          throw new Error('Failed to create assistant using both direct API and fallback service');
        }
      }

      if (assistant && assistant.id) {
        logger.info(`Created new assistant with ID ${assistant.id}`);

        // Update Supabase with the new assistant ID
        const { error: supabaseError } = await supabase
          .from('attorneys')
          .update({ vapi_assistant_id: assistant.id })
          .eq('id', attorney.id);

        if (supabaseError) {
          logger.error(`Error updating Supabase with new assistant ID: ${supabaseError.message}`);
          setError(`Assistant created but failed to update database: ${supabaseError.message}`);
        }

        // Update attorney with new assistant ID
        const updatedAttorney = {
          ...attorney,
          vapi_assistant_id: assistant.id
        };

        // Notify parent component of updates
        if (onUpdate) {
          onUpdate(updatedAttorney);
        }

        // Set assistant data
        setAssistantData(assistant);

        // Show success message
        setSuccess('New assistant created successfully');

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        throw new Error('Failed to create assistant');
      }
    } catch (err) {
      logger.error(`Error creating assistant: ${err.message}`);
      setError(`Error creating assistant: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Cancel changes
  const handleCancelChanges = () => {
    setEditedFields({});
    setHasChanges(false);
  };

  // Render component
  return (
    <div className="vapi-assistant-config">
      <h3>Voice Assistant Configuration</h3>

      {loading && <div className="loading">Loading...</div>}

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {success && (
        <div className="success-message">
          {success}
        </div>
      )}

      {/* Show loading message if attorney data is not available yet */}
      {!attorney ? (
        <div className="loading">Loading attorney data...</div>
      ) : !attorney.vapi_assistant_id || attorney.vapi_assistant_id.startsWith('mock-') ? (
        <div className="no-assistant">
          <p>
            {attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')
              ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
              : 'No voice assistant configured for this attorney.'}
          </p>
          <button
            className="create-assistant-button"
            onClick={handleCreateAssistant}
            disabled={loading}
          >
            {attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')
              ? 'Fix Assistant'
              : 'Create Assistant'}
          </button>
        </div>
      ) : (
        <div className="assistant-details">
          <div className="assistant-field">
            <label>Assistant ID:</label>
            <div className="field-value">{attorney.vapi_assistant_id}</div>
          </div>

          {assistantData && (
            <>
              <div className="assistant-field">
                <label>Name:</label>
                <div className="field-value">{assistantData.name}</div>
              </div>

              <div className="assistant-field">
                <label>First Message:</label>
                <textarea
                  value={editedFields.welcome_message !== undefined ? editedFields.welcome_message : (attorney.welcome_message || assistantData.firstMessage || '')}
                  onChange={(e) => handleFieldChange('welcome_message', e.target.value)}
                  placeholder="Enter first message"
                />
                {editedFields.welcome_message !== undefined && (
                  <div className="field-actions">
                    <button
                      className="cancel-button"
                      onClick={() => {
                        const newEditedFields = {...editedFields};
                        delete newEditedFields.welcome_message;
                        setEditedFields(newEditedFields);
                        setHasChanges(Object.keys(newEditedFields).length > 0);
                      }}
                    >
                      Discard
                    </button>
                    <button
                      className="save-button"
                      onClick={async () => {
                        // Save only this field
                        setLoading(true);
                        try {
                          // Update Supabase
                          const { error: supabaseError } = await supabase
                            .from('attorneys')
                            .update({ welcome_message: editedFields.welcome_message })
                            .eq('id', attorney.id);

                          if (supabaseError) throw new Error(`Supabase error: ${supabaseError.message}`);

                          // Update voice service
                          const updatedAssistant = await vapiDirectApi.updateAssistant(
                            attorney.vapi_assistant_id,
                            { firstMessage: editedFields.welcome_message }
                          );

                          if (!updatedAssistant) {
                            throw new Error('Failed to update assistant in voice service. The database was updated, but the voice service update failed.');
                          }

                          // Update local state
                          const newEditedFields = {...editedFields};
                          delete newEditedFields.welcome_message;
                          setEditedFields(newEditedFields);
                          setHasChanges(Object.keys(newEditedFields).length > 0);

                          // Notify parent
                          onUpdate({
                            ...attorney,
                            welcome_message: editedFields.welcome_message
                          });

                          setSuccess('First message updated successfully');
                          setTimeout(() => setSuccess(null), 3000);
                        } catch (err) {
                          setError(`Error updating first message: ${err.message}`);
                        } finally {
                          setLoading(false);
                        }
                      }}
                    >
                      Save
                    </button>
                  </div>
                )}
              </div>

              <div className="assistant-field">
                <label>Instructions:</label>
                <textarea
                  value={editedFields.vapi_instructions !== undefined ? editedFields.vapi_instructions : (attorney.vapi_instructions || assistantData.instructions || '')}
                  onChange={(e) => handleFieldChange('vapi_instructions', e.target.value)}
                  placeholder="Enter instructions"
                />
                {editedFields.vapi_instructions !== undefined && (
                  <div className="field-actions">
                    <button
                      className="cancel-button"
                      onClick={() => {
                        const newEditedFields = {...editedFields};
                        delete newEditedFields.vapi_instructions;
                        setEditedFields(newEditedFields);
                        setHasChanges(Object.keys(newEditedFields).length > 0);
                      }}
                    >
                      Discard
                    </button>
                    <button
                      className="save-button"
                      onClick={async () => {
                        // Save only this field
                        setLoading(true);
                        try {
                          // Update Supabase
                          const { error: supabaseError } = await supabase
                            .from('attorneys')
                            .update({ vapi_instructions: editedFields.vapi_instructions })
                            .eq('id', attorney.id);

                          if (supabaseError) throw new Error(`Supabase error: ${supabaseError.message}`);

                          // Update voice service
                          const updatedAssistant = await vapiDirectApi.updateAssistant(
                            attorney.vapi_assistant_id,
                            { instructions: editedFields.vapi_instructions }
                          );

                          if (!updatedAssistant) {
                            throw new Error('Failed to update assistant in voice service. The database was updated, but the voice service update failed.');
                          }

                          // Update local state
                          const newEditedFields = {...editedFields};
                          delete newEditedFields.vapi_instructions;
                          setEditedFields(newEditedFields);
                          setHasChanges(Object.keys(newEditedFields).length > 0);

                          // Notify parent
                          onUpdate({
                            ...attorney,
                            vapi_instructions: editedFields.vapi_instructions
                          });

                          setSuccess('Instructions updated successfully');
                          setTimeout(() => setSuccess(null), 3000);
                        } catch (err) {
                          setError(`Error updating instructions: ${err.message}`);
                        } finally {
                          setLoading(false);
                        }
                      }}
                    >
                      Save
                    </button>
                  </div>
                )}
              </div>

              <div className="assistant-field">
                <label>Voice:</label>
                <div className="field-value">
                  {assistantData.voice?.provider || 'playht'} / {assistantData.voice?.voiceId || 'sarah'}
                </div>
              </div>

              <div className="assistant-field">
                <label>Model:</label>
                <div className="field-value">
                  {assistantData.llm?.provider || 'openai'} / {assistantData.llm?.model || 'gpt-4o'}
                </div>
              </div>
            </>
          )}

          {hasChanges && (
            <div className="form-actions">
              <button
                className="cancel-button"
                onClick={handleCancelChanges}
                disabled={loading}
              >
                Cancel
              </button>
              <button
                className="save-button"
                onClick={handleSaveChanges}
                disabled={loading}
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceAssistantConfig;
