/**
 * Enhanced logging utility for LegalScout
 *
 * This utility provides structured logging with consistent formatting
 * and additional context for better debugging.
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  VERBOSE: 'verbose'
};

// Default configuration
const DEFAULT_CONFIG = {
  enabled: true,
  logLevel: LOG_LEVELS.INFO,
  includeTimestamp: true,
  includeSource: true
};

// Global configuration that can be modified at runtime
let globalConfig = { ...DEFAULT_CONFIG };

/**
 * Sanitize data for logging by truncating base64 image strings
 * @param {any} data - Data to sanitize
 * @returns {any} - Sanitized data
 */
const sanitizeLogData = (data) => {
  if (typeof data === 'string') {
    // Check for base64 image data
    if (data.startsWith('data:image/') && data.includes('base64,')) {
      const [prefix, base64Data] = data.split('base64,');
      if (base64Data && base64Data.length > 50) {
        return `${prefix}base64,[${base64Data.length} chars]...${base64Data.slice(-10)}`;
      }
    }
    // Check for long base64 strings without data: prefix
    if (data.match(/^[A-Za-z0-9+/]{100,}={0,2}$/)) {
      return `[base64 string: ${data.length} chars]...${data.slice(-10)}`;
    }
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeLogData);
  }

  if (data && typeof data === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeLogData(value);
    }
    return sanitized;
  }

  return data;
};

/**
 * Configure the global logger settings
 * @param {Object} config - Configuration options
 */
export const configureLogger = (config = {}) => {
  globalConfig = { ...DEFAULT_CONFIG, ...config };

  // Make configuration available globally for debugging
  if (typeof window !== 'undefined') {
    window.LegalScoutLogger = globalConfig;
  }
};

/**
 * Create a logger instance for a specific component or module
 * @param {string} source - The source name (component or module)
 * @returns {Object} Logger instance with logging methods
 */
export const createLogger = (source) => {
  // Helper to format log messages
  const formatMessage = (message, data = null) => {
    const parts = [];

    if (globalConfig.includeTimestamp) {
      parts.push(`[${new Date().toISOString()}]`);
    }

    if (globalConfig.includeSource && source) {
      parts.push(`[${source}]`);
    }

    parts.push(message);

    return {
      formattedMessage: parts.join(' '),
      data: data ? sanitizeLogData(data) : data
    };
  };

  // Helper to determine if a log level should be shown
  const shouldLog = (level) => {
    if (!globalConfig.enabled) return false;

    const levels = Object.values(LOG_LEVELS);
    const configLevelIndex = levels.indexOf(globalConfig.logLevel);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex <= configLevelIndex;
  };

  // Create the logger object with methods for each log level
  return {
    error: (message, data = null) => {
      if (shouldLog(LOG_LEVELS.ERROR)) {
        const { formattedMessage, data: formattedData } = formatMessage(message, data);
        console.error(formattedMessage, formattedData || '');
      }
    },

    warn: (message, data = null) => {
      if (shouldLog(LOG_LEVELS.WARN)) {
        const { formattedMessage, data: formattedData } = formatMessage(message, data);
        console.warn(formattedMessage, formattedData || '');
      }
    },

    info: (message, data = null) => {
      if (shouldLog(LOG_LEVELS.INFO)) {
        const { formattedMessage, data: formattedData } = formatMessage(message, data);
        console.info(formattedMessage, formattedData || '');
      }
    },

    debug: (message, data = null) => {
      if (shouldLog(LOG_LEVELS.DEBUG)) {
        const { formattedMessage, data: formattedData } = formatMessage(message, data);
        console.debug(formattedMessage, formattedData || '');
      }
    },

    verbose: (message, data = null) => {
      if (shouldLog(LOG_LEVELS.VERBOSE)) {
        const { formattedMessage, data: formattedData } = formatMessage(message, data);
        console.log(formattedMessage, formattedData || '');
      }
    },

    group: (label) => {
      if (globalConfig.enabled) {
        const { formattedMessage } = formatMessage(label);
        console.group(formattedMessage);
      }
    },

    groupEnd: () => {
      if (globalConfig.enabled) {
        console.groupEnd();
      }
    }
  };
};

// Export log levels for external use
export { LOG_LEVELS };

// Initialize logger configuration
configureLogger();
