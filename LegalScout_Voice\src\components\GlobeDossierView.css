/* Globe Container */
.globe-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block !important;
  border-radius: 12px;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

/* Visibility states */
.globe-container.hidden {
  opacity: 0;
  pointer-events: none;
}

.globe-container.visible {
  opacity: 1;
  pointer-events: all;
}

/* Attorney info sidebar */
.attorney-info {
  position: absolute;
  top: 0;
  right: 0;
  width: 280px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 20px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 10;
  border-radius: 0 12px 12px 0;
}

.attorney-info.visible {
  transform: translateX(0);
}

.attorney-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}

.attorney-info p {
  margin: 6px 0;
  font-size: 14px;
  color: #555;
}

.attorney-info .specialties {
  margin: 15px 0;
}

.attorney-info .specialty-tag {
  display: inline-block;
  background-color: #e0f0ff;
  color: #0066cc;
  padding: 4px 8px;
  margin: 3px;
  border-radius: 4px;
  font-size: 12px;
}

/* Button styles */
.contact-attorney-btn {
  display: block;
  width: 100%;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 15px;
  transition: all 0.2s ease;
}

.contact-attorney-btn:hover {
  background-color: #3a7fcf;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.25);
}

/* Globe tooltips */
.attorney-tooltip {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px 12px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  font-size: 13px;
  color: #333;
  white-space: nowrap;
  pointer-events: none;
  z-index: 100;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-width: 220px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.attorney-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #111;
}

.attorney-firm {
  font-size: 12px;
  margin-bottom: 4px;
  color: #555;
}

.attorney-rating {
  font-size: 12px;
  color: #4a90e2;
  display: flex;
  align-items: center;
}

.attorney-rating:before {
  content: "★";
  margin-right: 4px;
}

/* Background globe styling */
.background-globe-container {
  display: none; /* Hide the background globe container completely */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100vw;
  height: 100vh;
  z-index: -100; /* Move it far behind all content */
  pointer-events: none;
  opacity: 0;
}

/* Style for the canvas created by Three.js */
.background-globe-container canvas {
  width: 100% !important;
  height: 100% !important;
  outline: none;
  filter: brightness(1.5) contrast(1.2);
}

/* Remove old globe styles since Three.js will handle rendering */
.background-globe,
.background-usa,
.globe-lines,
.equator,
.meridian {
  display: none;
}

/* Enhance location info panel for better visibility */
.location-info {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(10, 20, 40, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px;
  color: white;
  max-width: 280px;
  box-shadow: 0 0 20px rgba(67, 126, 255, 0.5);
  border: 1px solid rgba(67, 126, 255, 0.3);
  z-index: 10;
  font-family: 'Arial', sans-serif;
}

.location-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
}

.location-info p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* Pins for locations and attorneys */
.location-pin {
  position: absolute;
  top: 30%;
  left: 25%;
  transform: translate(-50%, -50%);
  z-index: 3;
  pointer-events: none;
}

.attorney-pin {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 3;
  cursor: pointer;
}

.pin-pulse {
  width: 12px;
  height: 12px;
  background-color: rgba(255, 65, 65, 0.9);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pin-pulse.attorney {
  width: 12px;
  height: 12px;
  background-color: rgba(67, 126, 255, 0.8);
}

.pin-marker {
  width: 6px;
  height: 6px;
  background-color: #ff4141;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(255, 65, 65, 0.8);
}

.pin-marker.attorney {
  width: 6px;
  height: 6px;
  background-color: #437eff;
  box-shadow: 0 0 8px rgba(67, 126, 255, 0.8);
}

@keyframes ping {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  70%, 100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes slowRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Globe Dossier container - main wrapper */
.globe-dossier-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 0;
  transition: opacity 0.5s ease;
}

.globe-dossier-container.visible {
  opacity: 1;
  visibility: visible;
}

.globe-dossier-container.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.globe-gradient-overlay {
  display: none; /* Hide the gradient overlay completely */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  pointer-events: none;
  z-index: -100;
  opacity: 0;
}

/* Hide debug info in production */
.debug-info {
  display: none;
}

/* Only show debug info in development */
.debug-info.minimal {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 5px;
  padding: 5px 8px;
  font-size: 12px;
  font-family: monospace;
  color: rgba(255, 255, 255, 0.7);
  z-index: 10;
  display: flex;
  gap: 10px;
}

canvas {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  display: block !important;
  z-index: 2 !important;
}

/* Title bar */
.globe-title-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px 15px;
  background: linear-gradient(to bottom, rgba(25, 47, 89, 0.8) 0%, rgba(25, 47, 89, 0) 100%);
  color: white;
  font-size: 14px;
  font-weight: 500;
  z-index: 5;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .background-globe-container {
    width: 200px;
    height: 200px;
  }

  .location-info {
    max-width: 220px;
    padding: 10px;
  }

  .location-info h3 {
    font-size: 14px;
  }

  .location-info p {
    font-size: 12px;
  }
}

.globe-background {
  display: none; /* Hide the globe background completely */
  position: fixed;
  top: 60px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 60px);
  overflow: hidden;
  pointer-events: none;
  z-index: -100; /* Move it far behind all content */
  background-color: transparent;
  margin: 0;
  padding: 0;
  opacity: 0;
}

.globe-background.visible {
  opacity: 1;
  transition: opacity 0.5s ease-in-out;
}

.globe-background.hidden {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  pointer-events: none;
}

.globe-renderer-container {
  position: relative;
  width: min(600px, 80vw, calc(100vh - 120px));
  height: min(600px, 80vw, calc(100vh - 120px));
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.globe-renderer-container canvas {
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: block !important;
}

/* Location info tooltip */
.location-info {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(10, 20, 40, 0.75);
  border-radius: 8px;
  padding: 15px 20px;
  color: white;
  font-size: 14px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  max-width: 300px;
  text-align: center;
  z-index: 10;
  pointer-events: auto;
}

.location-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.location-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

/* Debug information */
.debug-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  font-family: monospace;
  padding: 5px;
  border-radius: 3px;
  z-index: 999;
  pointer-events: auto;
}

.debug-info.minimal {
  font-size: 8px;
  padding: 3px;
  opacity: 0.6;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .globe-renderer-container {
    max-width: min(90vh - 60px, 90vw);
    max-height: min(90vh - 60px, 90vw);
  }

  .location-info {
    bottom: 70px;
    max-width: 90%;
    padding: 10px 15px;
  }

  .location-info h3 {
    font-size: 14px;
  }

  .location-info p {
    font-size: 12px;
  }
}