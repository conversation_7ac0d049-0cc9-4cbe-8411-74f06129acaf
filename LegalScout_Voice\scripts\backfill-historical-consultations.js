#!/usr/bin/env node

/**
 * Backfill Historical Consultations Script
 * 
 * This script fetches historical call data from Vapi and creates consultation
 * records in Supabase for each attorney based on their assistant IDs.
 * 
 * Usage: node scripts/backfill-historical-consultations.js
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczODk0ODAwNywiZXhwIjoyMDU0NTI0MDA3fQ.Noq994xfKMoQipfGli9fZcgQYig9fZovjqdEnpBe7CM';

// Vapi configuration
const vapiPrivateKey = process.env.VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
const vapiApiUrl = 'https://api.vapi.ai';

/**
 * Get all attorneys with assistant IDs from Supabase
 */
async function getAllAttorneys() {
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  const { data, error } = await supabase
    .from('attorneys')
    .select('id, subdomain, firm_name, vapi_assistant_id')
    .not('vapi_assistant_id', 'is', null);

  if (error) {
    throw new Error(`Failed to fetch attorneys: ${error.message}`);
  }

  return data;
}

/**
 * Get all calls from Vapi for a specific assistant
 */
async function getCallsForAssistant(assistantId) {
  try {
    const response = await fetch(`${vapiApiUrl}/call?assistantId=${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${vapiPrivateKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Vapi API error: ${response.status} - ${response.statusText}`);
    }

    const calls = await response.json();
    return calls;

  } catch (error) {
    console.error(`Failed to fetch calls for assistant ${assistantId}:`, error.message);
    return [];
  }
}

/**
 * Get detailed call data from Vapi
 */
async function getCallDetails(callId) {
  try {
    const response = await fetch(`${vapiApiUrl}/call/${callId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${vapiPrivateKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Vapi API error: ${response.status} - ${response.statusText}`);
    }

    const callDetails = await response.json();
    return callDetails;

  } catch (error) {
    console.error(`Failed to fetch call details for ${callId}:`, error.message);
    return null;
  }
}

/**
 * Check if consultation already exists for a call
 */
async function consultationExists(callId) {
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  const { data, error } = await supabase
    .from('consultations')
    .select('id')
    .eq('metadata->call_id', callId)
    .single();

  return !error && data;
}

/**
 * Check if call is "ok" (successful/completed)
 */
function isCallOk(callData) {
  // Only process calls that ended successfully
  const okEndReasons = [
    'customer-ended-call',
    'assistant-ended-call',
    'pipeline-error-openai-voice-failed',
    'pipeline-error-deepgram-transcriber-failed'
  ];

  // Call must be ended and have a reasonable end reason
  return callData.status === 'ended' &&
         okEndReasons.includes(callData.endedReason);
}

/**
 * Create consultation record from call data
 */
async function createConsultationFromCall(callData, attorneyId) {
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Calculate duration in seconds
  const createdAt = new Date(callData.createdAt);
  const updatedAt = new Date(callData.updatedAt);
  const duration = Math.round((updatedAt - createdAt) / 1000);

  // Generate summary based on available data
  const summary = generateCallSummary(callData);

  // Generate transcript if available
  const transcript = generateTranscript(callData);

  const consultationRecord = {
    attorney_id: attorneyId,
    client_name: callData.customer?.name || 'Historical Client',
    client_email: callData.customer?.email || null,
    client_phone: callData.customer?.phoneNumber || callData.customer?.phone || null,
    summary: summary,
    transcript: transcript,
    duration: duration,
    practice_area: null,
    location: null,
    location_data: {},
    metadata: {
      call_id: callData.id,
      assistant_id: callData.assistantId,
      vapi_call_status: callData.status,
      vapi_end_reason: callData.endedReason,
      historical_backfill: true,
      backfill_date: new Date().toISOString()
    },
    status: 'completed',
    created_at: callData.createdAt,
    updated_at: callData.updatedAt
  };

  const { data, error } = await supabase
    .from('consultations')
    .insert(consultationRecord)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create consultation: ${error.message}`);
  }

  return data;
}

/**
 * Generate call summary from call data
 */
function generateCallSummary(callData) {
  const createdAt = new Date(callData.createdAt);
  const duration = Math.round((new Date(callData.updatedAt) - createdAt) / 1000);
  const durationMinutes = Math.round(duration / 60);
  
  let summary = `Historical call from ${createdAt.toLocaleDateString()} at ${createdAt.toLocaleTimeString()}.`;
  
  if (durationMinutes > 0) {
    summary += ` Duration: ${durationMinutes} minute${durationMinutes !== 1 ? 's' : ''}.`;
  }
  
  if (callData.endedReason) {
    const endReason = callData.endedReason.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    summary += ` Call ended: ${endReason}.`;
  }

  if (callData.customer?.phoneNumber) {
    summary += ` Client phone: ${callData.customer.phoneNumber}.`;
  }

  return summary;
}

/**
 * Generate transcript from call data
 */
function generateTranscript(callData) {
  if (callData.transcript && Array.isArray(callData.transcript)) {
    return callData.transcript
      .map(entry => {
        const speaker = entry.role === 'assistant' ? 'Assistant' : 'Client';
        const timestamp = entry.timestamp ? new Date(entry.timestamp).toLocaleTimeString() : 'Unknown time';
        return `[${timestamp}] ${speaker}: ${entry.text || entry.message || ''}`;
      })
      .join('\n\n');
  }

  return 'Transcript not available for historical call.';
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting historical consultation backfill process...\n');

    console.log('Environment check:');
    console.log('  SUPABASE_URL:', supabaseUrl);
    console.log('  SUPABASE_KEY length:', supabaseKey?.length);
    console.log('  VAPI_PRIVATE_KEY length:', vapiPrivateKey?.length);
    console.log('');

    // Get all attorneys
    console.log('📊 Fetching attorneys from Supabase...');
    const attorneys = await getAllAttorneys();
    
    if (!attorneys || attorneys.length === 0) {
      console.log('❌ No attorneys found with assistant IDs');
      return;
    }

    console.log(`📋 Found ${attorneys.length} attorneys with assistants:\n`);
    
    let totalCallsProcessed = 0;
    let totalConsultationsCreated = 0;
    let totalSkipped = 0;

    // Process each attorney
    for (const attorney of attorneys) {
      console.log(`\n🔄 Processing ${attorney.firm_name} (${attorney.subdomain})...`);
      console.log(`   Assistant ID: ${attorney.vapi_assistant_id}`);

      try {
        // Get all calls for this assistant
        const calls = await getCallsForAssistant(attorney.vapi_assistant_id);
        console.log(`   Found ${calls.length} historical calls`);

        if (calls.length === 0) {
          console.log('   ⏭️  No calls to process');
          continue;
        }

        let attorneyCallsProcessed = 0;
        let attorneyConsultationsCreated = 0;
        let attorneySkipped = 0;

        // Process each call
        for (const call of calls) {
          try {
            // Check if consultation already exists
            const exists = await consultationExists(call.id);
            if (exists) {
              console.log(`   ⏭️  Skipping call ${call.id} (already exists)`);
              attorneySkipped++;
              continue;
            }

            // Get detailed call data
            const callDetails = await getCallDetails(call.id);
            const callData = callDetails || call;

            // Create consultation record
            const consultation = await createConsultationFromCall(callData, attorney.id);
            console.log(`   ✅ Created consultation for call ${call.id}`);
            
            attorneyCallsProcessed++;
            attorneyConsultationsCreated++;

          } catch (error) {
            console.error(`   ❌ Failed to process call ${call.id}:`, error.message);
          }
        }

        console.log(`   📊 Attorney Summary:`);
        console.log(`      Calls processed: ${attorneyCallsProcessed}`);
        console.log(`      Consultations created: ${attorneyConsultationsCreated}`);
        console.log(`      Skipped (already exist): ${attorneySkipped}`);

        totalCallsProcessed += attorneyCallsProcessed;
        totalConsultationsCreated += attorneyConsultationsCreated;
        totalSkipped += attorneySkipped;

      } catch (error) {
        console.error(`   ❌ Failed to process attorney ${attorney.firm_name}:`, error.message);
      }
    }

    console.log('\n📊 Final Summary:');
    console.log(`   📞 Total calls processed: ${totalCallsProcessed}`);
    console.log(`   ✅ Total consultations created: ${totalConsultationsCreated}`);
    console.log(`   ⏭️  Total skipped (already exist): ${totalSkipped}`);
    console.log(`   👥 Attorneys processed: ${attorneys.length}`);

    if (totalConsultationsCreated > 0) {
      console.log('\n🎉 Historical backfill completed successfully!');
      console.log('\n📝 Next steps:');
      console.log('   1. Check the Briefs tab in each attorney\'s dashboard');
      console.log('   2. Verify that historical consultations appear correctly');
      console.log('   3. Test that new calls still work with the webhook system');
    } else {
      console.log('\n📝 No new consultations were created.');
      console.log('   This could mean all historical calls were already processed.');
    }

  } catch (error) {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
console.log('Script starting...');
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

module.exports = { main, getAllAttorneys, getCallsForAssistant, createConsultationFromCall };
