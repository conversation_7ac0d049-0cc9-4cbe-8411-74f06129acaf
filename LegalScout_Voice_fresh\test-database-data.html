<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Data Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #2d5a2d; border: 1px solid #4a8a4a; }
        .error { background: #5a2d2d; border: 1px solid #8a4a4a; }
        .info { background: #2d2d5a; border: 1px solid #4a4a8a; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #4a8a4a; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #5a9a5a; }
    </style>
</head>
<body>
    <h1>🔍 Database Data Test for assistant1test</h1>
    <p>This test checks if the required data exists in the database for subdomain routing.</p>
    
    <button onclick="testSubdomainLookup()">🔍 Test Subdomain Lookup</button>
    <button onclick="testAttorneyData()">👤 Test Attorney Data</button>
    <button onclick="testFullQuery()">🚀 Test Full Query</button>
    <button onclick="clearResults()">🧹 Clear Results</button>
    
    <div id="results"></div>

    <script>
        const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
        const TEST_SUBDOMAIN = 'assistant1test';

        window.testSubdomainLookup = async function() {
            addResult('info', '🔍 Testing v_subdomain_assistant_lookup view...');
            
            try {
                const url = `${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_SUBDOMAIN}&is_active=eq.true`;
                
                addResult('info', `Query URL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                addResult('info', `Response status: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    addResult('error', `❌ Query failed: ${response.status} - ${errorText}`);
                    return;
                }

                const data = await response.json();
                
                if (!data || data.length === 0) {
                    addResult('error', '❌ No data found in v_subdomain_assistant_lookup for assistant1test');
                    addResult('info', 'This means the subdomain mapping does not exist in the database');
                } else {
                    addResult('success', `✅ Found ${data.length} record(s) in subdomain lookup:`);
                    addResult('info', JSON.stringify(data, null, 2));
                }
                
            } catch (error) {
                addResult('error', `❌ Test failed: ${error.message}`);
                console.error('Subdomain lookup error:', error);
            }
        };

        window.testAttorneyData = async function() {
            addResult('info', '👤 Testing attorneys table...');
            
            try {
                // First, let's see what attorneys exist
                const url = `${SUPABASE_URL}/rest/v1/attorneys?select=*&limit=10`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    addResult('error', `❌ Query failed: ${response.status} - ${errorText}`);
                    return;
                }

                const data = await response.json();
                
                if (!data || data.length === 0) {
                    addResult('error', '❌ No attorneys found in database');
                } else {
                    addResult('success', `✅ Found ${data.length} attorney(s):`);
                    data.forEach((attorney, index) => {
                        addResult('info', `Attorney ${index + 1}: ${attorney.firm_name || attorney.name} (ID: ${attorney.id})`);
                    });
                    addResult('info', 'Full attorney data:');
                    addResult('info', JSON.stringify(data, null, 2));
                }
                
            } catch (error) {
                addResult('error', `❌ Test failed: ${error.message}`);
                console.error('Attorney data error:', error);
            }
        };

        window.testFullQuery = async function() {
            addResult('info', '🚀 Testing full assistant routing query...');
            
            try {
                // Test the exact query that the assistant routing service uses
                const mappingUrl = `${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_SUBDOMAIN}&is_active=eq.true`;
                
                addResult('info', 'Step 1: Query subdomain mapping...');
                
                const mappingResponse = await fetch(mappingUrl, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!mappingResponse.ok) {
                    addResult('error', `❌ Mapping query failed: ${mappingResponse.status}`);
                    return;
                }

                const mappingData = await mappingResponse.json();
                
                if (!mappingData || mappingData.length === 0) {
                    addResult('error', '❌ No mapping found - this is why the subdomain shows fallback content');
                    addResult('info', 'The assistant1test subdomain is not mapped to any assistant in the database');
                    
                    // Let's check if assistant_subdomains table exists
                    addResult('info', 'Checking if assistant_subdomains table exists...');
                    
                    const tableUrl = `${SUPABASE_URL}/rest/v1/assistant_subdomains?limit=1`;
                    const tableResponse = await fetch(tableUrl, {
                        method: 'GET',
                        headers: {
                            'apikey': SUPABASE_KEY,
                            'Authorization': `Bearer ${SUPABASE_KEY}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (tableResponse.ok) {
                        addResult('success', '✅ assistant_subdomains table exists');
                        const tableData = await tableResponse.json();
                        addResult('info', `Table has ${tableData.length} records`);
                        if (tableData.length > 0) {
                            addResult('info', 'Sample records:');
                            addResult('info', JSON.stringify(tableData, null, 2));
                        }
                    } else {
                        addResult('error', '❌ assistant_subdomains table does not exist or is not accessible');
                    }
                    
                    return;
                }

                const mapping = mappingData[0];
                addResult('success', `✅ Found mapping: ${JSON.stringify(mapping, null, 2)}`);
                
                // Step 2: Get attorney data
                addResult('info', 'Step 2: Query attorney data...');
                
                const attorneyUrl = `${SUPABASE_URL}/rest/v1/attorneys?id=eq.${mapping.attorney_id}`;
                
                const attorneyResponse = await fetch(attorneyUrl, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!attorneyResponse.ok) {
                    addResult('error', `❌ Attorney query failed: ${attorneyResponse.status}`);
                    return;
                }

                const attorneyData = await attorneyResponse.json();
                
                if (!attorneyData || attorneyData.length === 0) {
                    addResult('error', `❌ No attorney found with ID: ${mapping.attorney_id}`);
                    return;
                }

                const attorney = attorneyData[0];
                addResult('success', `✅ Found attorney: ${JSON.stringify(attorney, null, 2)}`);
                
                // Step 3: Create assistant config
                addResult('info', 'Step 3: Create assistant config...');
                
                const assistantConfig = {
                    id: attorney.id,
                    assistant_id: mapping.assistant_id,
                    attorney_id: mapping.attorney_id,
                    subdomain: mapping.subdomain,
                    firmName: attorney.firm_name || mapping.firm_name || "LegalScout",
                    name: attorney.name || attorney.firm_name,
                    email: attorney.email,
                    vapi_assistant_id: mapping.assistant_id,
                    current_assistant_id: mapping.assistant_id,
                    is_primary_assistant: mapping.is_primary,
                    welcomeMessage: attorney.welcome_message || "Hello! How can I help you today?",
                    primaryColor: attorney.primary_color || "#3B82F6",
                    logoUrl: attorney.logo_url,
                    vapiInstructions: attorney.vapi_instructions || "You are a helpful legal assistant.",
                    aiModel: attorney.ai_model || "gpt-4o",
                    voiceId: attorney.voice_id || "alloy",
                    isActive: true,
                    loadedVia: 'database_test',
                    vapiSyncStatus: 'assistant_subdomain_mapped'
                };
                
                addResult('success', '✅ Assistant config created successfully:');
                addResult('info', JSON.stringify(assistantConfig, null, 2));
                
            } catch (error) {
                addResult('error', `❌ Full query test failed: ${error.message}`);
                console.error('Full query error:', error);
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
            
            // Auto-scroll to bottom
            div.scrollIntoView({ behavior: 'smooth' });
        }

        // Auto-run basic test on load
        setTimeout(() => {
            addResult('info', '🏁 Database test page loaded. Click buttons above to run tests.');
            addResult('info', `Testing subdomain: ${TEST_SUBDOMAIN}`);
        }, 100);
    </script>
</body>
</html>
