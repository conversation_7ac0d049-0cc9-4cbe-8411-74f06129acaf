/**
 * Surgical Webhook Test
 * 
 * This test cuts straight to the heart of the matter:
 * 1. Tests the direct webhook endpoint
 * 2. Verifies Supabase integration
 * 3. Checks if data appears in dashboard
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '../../.env' });

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

const TEST_CALL_ID = 'surgical-test-' + Date.now();
const ASSISTANT_ID = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
const ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';

const TEST_CALL_DATA = {
  id: TEST_CALL_ID,
  assistant_id: ASSISTANT_ID,
  status: 'completed',
  duration: 180,
  start_time: new Date(Date.now() - 180000).toISOString(),
  end_time: new Date().toISOString(),
  customer: {
    phone_number: '+19145893180'
  },
  transcripts: [
    {
      role: 'assistant',
      message: 'Hello! I\'m Scout, your legal assistant. How can I help you today?',
      timestamp: new Date(Date.now() - 180000).toISOString()
    },
    {
      role: 'user',
      message: 'Hi, I was in a car accident and need legal advice.',
      timestamp: new Date(Date.now() - 160000).toISOString()
    }
  ],
  messages: [],
  tool_executions: [],
  metadata: {
    test: true,
    urgency: 'High'
  }
};

async function testDirectWebhook() {
  console.log('🎯 Testing direct webhook endpoint...');
  
  const url = 'https://legalscout.net/api/vapi-webhook-direct';
  
  try {
    // Test GET first
    const getResponse = await fetch(url, { method: 'GET' });
    console.log(`GET ${url}: ${getResponse.status}`);
    
    if (getResponse.ok) {
      const getResult = await getResponse.json();
      console.log('✅ Direct webhook endpoint is accessible');
      console.log('Response:', getResult.message);
    } else {
      console.log('❌ Direct webhook endpoint not accessible');
      return false;
    }

    // Test POST with call data
    const postResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CALL_DATA)
    });

    console.log(`POST ${url}: ${postResponse.status}`);
    
    const postResult = await postResponse.json();
    console.log('POST Response:', postResult);

    if (postResponse.ok && postResult.success) {
      console.log('✅ Direct webhook processed call successfully');
      return true;
    } else {
      console.log('❌ Direct webhook failed to process call');
      return false;
    }

  } catch (error) {
    console.log('❌ Direct webhook test failed:', error.message);
    return false;
  }
}

async function verifySupabaseData() {
  console.log('\n🔍 Verifying data in Supabase...');
  
  // Check call record
  const { data: callRecord, error: callError } = await supabase
    .from('call_records')
    .select('*')
    .eq('call_id', TEST_CALL_ID)
    .single();

  if (callError) {
    console.log('❌ Call record not found:', callError.message);
  } else {
    console.log('✅ Call record created:', callRecord.call_id);
  }

  // Check consultation
  const { data: consultation, error: consultError } = await supabase
    .from('consultations')
    .select('*')
    .eq('metadata->call_id', TEST_CALL_ID)
    .single();

  if (consultError) {
    console.log('❌ Consultation not found:', consultError.message);
  } else {
    console.log('✅ Consultation created:', consultation.id);
    console.log('  Client:', consultation.client_name);
    console.log('  Phone:', consultation.client_phone);
  }

  return { callRecord: !!callRecord, consultation: !!consultation };
}

async function testAttorneyMapping() {
  console.log('\n🔗 Testing attorney mapping...');
  
  const { data: attorney, error } = await supabase
    .from('attorneys')
    .select('*')
    .eq('vapi_assistant_id', ASSISTANT_ID)
    .single();

  if (error) {
    console.log('❌ Attorney mapping failed:', error.message);
    return false;
  }

  console.log('✅ Attorney mapping works:');
  console.log(`  Assistant ID: ${ASSISTANT_ID}`);
  console.log(`  Attorney ID: ${attorney.id}`);
  console.log(`  Attorney Email: ${attorney.email}`);
  
  return true;
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  await supabase.from('call_records').delete().eq('call_id', TEST_CALL_ID);
  await supabase.from('consultations').delete().eq('metadata->call_id', TEST_CALL_ID);
  
  console.log('✅ Cleanup completed');
}

async function runSurgicalTest() {
  console.log('🔬 SURGICAL WEBHOOK TEST');
  console.log('=' .repeat(50));
  console.log(`Test Call ID: ${TEST_CALL_ID}`);
  console.log(`Assistant ID: ${ASSISTANT_ID}`);
  console.log('');

  // Test attorney mapping first
  const mappingWorks = await testAttorneyMapping();
  if (!mappingWorks) {
    console.log('\n❌ CRITICAL: Attorney mapping is broken - cannot proceed');
    return;
  }

  // Test direct webhook
  const webhookWorks = await testDirectWebhook();
  
  if (webhookWorks) {
    // Wait for processing
    console.log('\n⏳ Waiting for data processing...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Verify data was created
    const { callRecord, consultation } = await verifySupabaseData();

    // Final summary
    console.log('\n🎯 SURGICAL TEST RESULTS:');
    console.log('=' .repeat(50));
    console.log(`Attorney mapping: ✅ Working`);
    console.log(`Direct webhook: ${webhookWorks ? '✅ Working' : '❌ Failed'}`);
    console.log(`Call record created: ${callRecord ? '✅ Yes' : '❌ No'}`);
    console.log(`Consultation created: ${consultation ? '✅ Yes' : '❌ No'}`);

    if (webhookWorks && callRecord && consultation) {
      console.log('\n🎉 SUCCESS: End-to-end flow is working!');
      console.log('💡 Next step: Configure Vapi to use the direct webhook URL');
      console.log('   URL: https://legalscout.net/api/vapi-webhook-direct');
    } else {
      console.log('\n❌ ISSUE: Some part of the flow is broken');
    }
  }

  // Cleanup
  await cleanup();
}

runSurgicalTest().catch(console.error);
