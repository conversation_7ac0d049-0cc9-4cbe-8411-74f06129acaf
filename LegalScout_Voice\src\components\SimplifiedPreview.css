.simplified-preview {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 2rem;
  background-color: var(--background-color, rgba(26, 26, 26, 0.9));
  color: #fff;
  position: relative;
  overflow: hidden;
}

.simplified-preview.light-theme {
  color: #333;
}

.simplified-preview.dark-theme {
  color: #fff;
}

.preview-content {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 2rem;
  z-index: 1;
}

.preview-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  text-align: center;
}

/* Banner and logo containers removed - SimplifiedPreview now matches dashboard preview exactly */

.preview-firm-name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--primary-color, #4B74AA);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.preview-practice-description {
  width: 100%;
  max-width: 800px;
  padding: 2rem;
  border-radius: 10px;
  background-color: var(--text-background-color, rgba(99, 76, 56, 0.1));
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.practice-description-content {
  font-size: 1.1rem;
  line-height: 1.6;
}

.practice-description-content h1,
.practice-description-content h2,
.practice-description-content h3 {
  color: var(--secondary-color, #3498db);
}

.practice-description-content ul,
.practice-description-content ol {
  margin-left: 1.5rem;
}

.practice-description-content blockquote {
  border-left: 4px solid var(--secondary-color, #3498db);
  padding-left: 1rem;
  margin-left: 0;
  font-style: italic;
}

.practice-description-content a {
  color: var(--secondary-color, #3498db);
  text-decoration: underline;
}

/* Custom HTML styling */
.practice-description-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.practice-description-content th,
.practice-description-content td {
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem;
  text-align: left;
}

.practice-description-content th {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--secondary-color, #3498db);
}

.practice-description-content tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.1);
}

.practice-description-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

.practice-description-content hr {
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin: 1.5rem 0;
}

.practice-description-content code {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.practice-description-content pre {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin: 1rem 0;
}

.practice-description-content pre code {
  background-color: transparent;
  padding: 0;
}

/* Custom classes that attorneys might use */
.practice-description-content .highlight {
  background-color: rgba(var(--primary-color-rgb, 75, 116, 170), 0.2);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
}

.practice-description-content .centered {
  text-align: center;
}

.practice-description-content .right-aligned {
  text-align: right;
}

.practice-description-content .box {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  border-left: 4px solid var(--secondary-color, #3498db);
}

/* Practice Areas Section */
.practice-areas-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.practice-areas-section h3 {
  color: var(--secondary-color, #3498db);
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.practice-areas-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  list-style: none;
  padding: 0;
  margin: 0;
}

.practice-areas-list li {
  background-color: var(--primary-color, #4B74AA);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Office Address Section */
.office-address-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.office-address-section h3 {
  color: var(--secondary-color, #3498db);
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.4rem;
}

.office-address-section p {
  margin: 0;
  line-height: 1.5;
}

/* Scheduling Section */
.scheduling-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  text-align: center;
}

.scheduling-section h3 {
  color: var(--secondary-color, #3498db);
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.scheduling-link {
  display: inline-block;
  background-color: var(--button-color, #3498db);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.scheduling-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.preview-button-container {
  margin: 2rem 0;
}

.preview-call-container {
  width: 100%;
  max-width: 800px;
  height: 600px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 768px) {
  .simplified-preview {
    padding: 1rem;
  }

  .preview-firm-name {
    font-size: 2rem;
  }

  .preview-practice-description {
    padding: 1.5rem;
  }

  .practice-description-content {
    font-size: 1rem;
  }

  .preview-call-container {
    height: 500px;
  }
}

@media (max-width: 480px) {
  .preview-firm-name {
    font-size: 1.8rem;
  }

  .preview-practice-description {
    padding: 1rem;
  }

  .preview-call-container {
    height: 400px;
  }
}
