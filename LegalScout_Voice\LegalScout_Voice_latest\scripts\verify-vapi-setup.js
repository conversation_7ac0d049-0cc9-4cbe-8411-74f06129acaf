/**
 * Verify Vapi Setup
 * 
 * This script verifies the Vapi setup by checking environment variables,
 * testing the direct API connection, and testing the MCP server connection.
 */

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fs from 'fs';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
config({ path: resolve(__dirname, '../.env') });

// Check environment variables
function checkEnvironment() {
  console.log('🔍 Checking environment variables...');
  
  const variables = [
    'VITE_VAPI_PUBLIC_KEY',
    'VAPI_PUBLIC_KEY',
    'VAPI_SECRET_KEY',
    'VAPI_TOKEN'
  ];
  
  const results = {};
  let allSet = true;
  
  for (const variable of variables) {
    const value = process.env[variable];
    const isSet = !!value;
    results[variable] = isSet ? `Set (${value.substring(0, 5)}...)` : 'Not set';
    
    if (!isSet) {
      allSet = false;
    }
  }
  
  console.log('Environment variables:');
  console.table(results);
  
  if (allSet) {
    console.log('✅ All required environment variables are set');
  } else {
    console.log('❌ Some required environment variables are not set');
  }
  
  return allSet;
}

// Check .env file
function checkEnvFile() {
  console.log('🔍 Checking .env file...');
  
  const envPath = resolve(__dirname, '../.env');
  
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    const variables = [
      'VITE_VAPI_PUBLIC_KEY',
      'VAPI_PUBLIC_KEY',
      'VAPI_SECRET_KEY',
      'VAPI_TOKEN'
    ];
    
    const results = {};
    let allSet = true;
    
    for (const variable of variables) {
      const line = lines.find(line => line.startsWith(`${variable}=`));
      
      if (line) {
        const value = line.split('=')[1].trim();
        const isPlaceholder = value.includes('REPLACE_WITH_YOUR_ACTUAL_VAPI_API_KEY');
        
        results[variable] = isPlaceholder ? 'Placeholder value' : `Set (${value.substring(0, 5)}...)`;
        
        if (isPlaceholder) {
          allSet = false;
        }
      } else {
        results[variable] = 'Not found';
        allSet = false;
      }
    }
    
    console.log('.env file variables:');
    console.table(results);
    
    if (allSet) {
      console.log('✅ All required variables in .env file are set correctly');
    } else {
      console.log('❌ Some variables in .env file are missing or have placeholder values');
    }
    
    return allSet;
  } catch (error) {
    console.error(`❌ Error reading .env file: ${error.message}`);
    return false;
  }
}

// Check Vapi MCP server setup
function checkVapiMcpServer() {
  console.log('🔍 Checking Vapi MCP server setup...');
  
  const apiPath = resolve(__dirname, '../api/vapi-mcp-server');
  
  try {
    // Check if the API directory exists
    if (!fs.existsSync(apiPath)) {
      console.error('❌ Vapi MCP server API directory not found');
      return false;
    }
    
    // Check if the index.js file exists
    const indexPath = resolve(apiPath, 'index.js');
    if (!fs.existsSync(indexPath)) {
      console.error('❌ Vapi MCP server index.js file not found');
      return false;
    }
    
    // Check if the [path].js file exists
    const pathJsPath = resolve(apiPath, '[path].js');
    if (!fs.existsSync(pathJsPath)) {
      console.error('❌ Vapi MCP server [path].js file not found');
      return false;
    }
    
    console.log('✅ Vapi MCP server files found');
    
    // Check the content of the index.js file
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    
    if (!indexContent.includes('createMcpHandler') || !indexContent.includes('createServer')) {
      console.error('❌ Vapi MCP server index.js file does not contain required functions');
      return false;
    }
    
    if (!indexContent.includes('VAPI_TOKEN')) {
      console.error('❌ Vapi MCP server index.js file does not use VAPI_TOKEN environment variable');
      return false;
    }
    
    console.log('✅ Vapi MCP server index.js file contains required code');
    
    return true;
  } catch (error) {
    console.error(`❌ Error checking Vapi MCP server setup: ${error.message}`);
    return false;
  }
}

// Check vercel.json configuration
function checkVercelConfig() {
  console.log('🔍 Checking vercel.json configuration...');
  
  const vercelPath = resolve(__dirname, '../vercel.json');
  
  try {
    if (!fs.existsSync(vercelPath)) {
      console.error('❌ vercel.json file not found');
      return false;
    }
    
    const vercelContent = fs.readFileSync(vercelPath, 'utf8');
    const vercelConfig = JSON.parse(vercelContent);
    
    // Check functions configuration
    if (!vercelConfig.functions || !vercelConfig.functions['api/vapi-mcp-server/*.js']) {
      console.error('❌ vercel.json does not contain Vapi MCP server functions configuration');
      return false;
    }
    
    // Check routes configuration
    if (!vercelConfig.routes) {
      console.error('❌ vercel.json does not contain routes configuration');
      return false;
    }
    
    const vapiMcpRoutes = vercelConfig.routes.filter(route => 
      route.src && (route.src.includes('/vapi-mcp-server') || route.src.includes('/api/vapi-mcp-server'))
    );
    
    if (vapiMcpRoutes.length === 0) {
      console.error('❌ vercel.json does not contain Vapi MCP server routes configuration');
      return false;
    }
    
    console.log('✅ vercel.json contains Vapi MCP server configuration');
    return true;
  } catch (error) {
    console.error(`❌ Error checking vercel.json configuration: ${error.message}`);
    return false;
  }
}

// Run all checks
function runChecks() {
  console.log('🧪 Running Vapi setup verification...\n');
  
  const envVarsOk = checkEnvironment();
  console.log('');
  
  const envFileOk = checkEnvFile();
  console.log('');
  
  const mcpServerOk = checkVapiMcpServer();
  console.log('');
  
  const vercelConfigOk = checkVercelConfig();
  console.log('');
  
  // Print summary
  console.log('📋 Verification Summary:');
  console.log(`Environment Variables: ${envVarsOk ? '✅ OK' : '❌ Issues found'}`);
  console.log(`.env File: ${envFileOk ? '✅ OK' : '❌ Issues found'}`);
  console.log(`Vapi MCP Server: ${mcpServerOk ? '✅ OK' : '❌ Issues found'}`);
  console.log(`Vercel Configuration: ${vercelConfigOk ? '✅ OK' : '❌ Issues found'}`);
  
  if (envVarsOk && envFileOk && mcpServerOk && vercelConfigOk) {
    console.log('\n✅ All checks passed! Vapi setup looks good.');
  } else {
    console.log('\n❌ Some checks failed. Please fix the issues and run this script again.');
  }
}

// Run the checks
runChecks();
