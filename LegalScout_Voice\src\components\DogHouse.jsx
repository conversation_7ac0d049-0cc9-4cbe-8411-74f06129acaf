import React from 'react';

const DogHouse = ({ isDarkTheme, position, onClick }) => {
  // Theme-based colors
  const houseColor = isDarkTheme ? '#424242' : '#8B4513';
  const roofColor = isDarkTheme ? '#303030' : '#A52A2A';
  const doorColor = isDarkTheme ? '#252525' : '#643B0F';
  const boneColor = isDarkTheme ? '#f0f0f0' : '#E8E8E8';
  const boneOutline = isDarkTheme ? '#303030' : '#555';

  return (
    <div 
      className={`dog-house visible`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-50%, -100%)',
        cursor: 'pointer'
      }}
      onClick={onClick}
    >
      <svg 
        width="100%" 
        height="100%" 
        viewBox="0 0 40 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Roof */}
        <path 
          d="M2 18L20 5L38 18H2Z" 
          fill={roofColor}
          stroke="#000000"
          strokeWidth="1"
        />
        
        {/* House body */}
        <rect 
          x="5" 
          y="18" 
          width="30" 
          height="20" 
          fill={houseColor}
          stroke="#000000"
          strokeWidth="1" 
        />
        
        {/* Door */}
        <rect 
          x="15" 
          y="25" 
          width="10" 
          height="13" 
          rx="2"
          fill={doorColor}
          stroke="#000000"
          strokeWidth="0.5" 
        />
        
        {/* Bone sign */}
        <g transform="translate(16, 12) scale(0.5)">
          <path 
            d="M4 1.5C4 0.67 3.33 0 2.5 0S1 0.67 1 1.5C1 2.02 1.29 2.46 1.7 2.7C1.29 2.94 1 3.38 1 3.9C1 4.73 1.67 5.4 2.5 5.4S4 4.73 4 3.9C4 3.38 3.71 2.94 3.3 2.7C3.71 2.46 4 2.02 4 1.5ZM23 1.5C23 0.67 22.33 0 21.5 0S20 0.67 20 1.5C20 2.02 20.29 2.46 20.7 2.7C20.29 2.94 20 3.38 20 3.9C20 4.73 20.67 5.4 21.5 5.4S23 4.73 23 3.9C23 3.38 22.71 2.94 22.3 2.7C22.71 2.46 23 2.02 23 1.5ZM4 2.7H20V3.7H4V2.7Z" 
            fill={boneColor}
            stroke={boneOutline}
            strokeWidth="0.5"
          />
        </g>
        
        {/* Little paw print next to the door */}
        <circle cx="28" cy="30" r="1" fill={boneColor} />
        <circle cx="26" cy="31" r="0.8" fill={boneColor} />
        <circle cx="30" cy="31" r="0.8" fill={boneColor} />
        <circle cx="27" cy="33" r="1.2" fill={boneColor} />
      </svg>
    </div>
  );
};

export default DogHouse; 