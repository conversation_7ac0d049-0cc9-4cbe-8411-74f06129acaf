home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
home:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
home:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-9522ccf9.js:85 [VapiLoader] Starting Vapi SDK loading process
index-9522ccf9.js:85 [VapiLoader] Attempting to import @vapi-ai/web package
index-9522ccf9.js:172 [VapiMcpService] Created clean fetch from iframe
index-9522ccf9.js:172 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-9522ccf9.js:85 [VapiLoader] Starting Vapi SDK loading process
index-9522ccf9.js:85 [VapiLoader] Attempting to import @vapi-ai/web package
index-9522ccf9.js:172 [VapiMcpService] Created clean fetch from iframe
index-9522ccf9.js:172 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
