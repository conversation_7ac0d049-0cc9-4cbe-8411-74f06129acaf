/**
 * Session Templates for Multi-Agent Multi-Human Workflows
 * Extends the existing practice template system to include orchestration
 */

// Base session template structure
export const SESSION_TEMPLATE_SCHEMA = {
  // Basic template info (extends existing)
  id: "string",
  name: "string", 
  description: "string",
  category: "string", // "business_formation", "litigation", "family_law", etc.
  attorney_id: "string",
  is_public: "boolean",
  
  // Session configuration
  session_config: {
    // Estimated duration and complexity
    estimated_duration: "string", // "30-60 minutes", "2-4 hours"
    complexity_level: "string", // "simple", "medium", "complex"
    max_participants: "number",
    
    // Pricing model
    pricing: {
      type: "string", // "fixed", "hourly", "outcome_based"
      base_price: "number",
      additional_fees: "object"
    }
  },

  // AI Agent definitions
  ai_agents: [
    {
      role: "string", // "intake_specialist", "legal_researcher", "document_generator"
      name: "string",
      description: "string",
      
      // Agent configuration
      config: {
        model: "string", // "gpt-4o", "claude-3-sonnet"
        voice: "string", // "openai/echo", "11labs/sarah"
        temperature: "number",
        tools: ["string"], // MCP tools available to this agent
        instructions: "string", // Role-specific instructions
        handoff_triggers: ["string"] // When to hand off to human/other agent
      },
      
      // When this agent is active
      activation_conditions: {
        phase: "string", // "intake", "analysis", "review"
        triggers: ["string"], // "complexity_high", "legal_issue_detected"
        prerequisites: ["string"] // "client_data_collected"
      }
    }
  ],

  // Human participant roles
  human_roles: [
    {
      role: "string", // "lead_attorney", "specialist", "paralegal", "client"
      name: "string",
      description: "string",
      required: "boolean",
      
      // Participation settings
      participation: {
        mode: "string", // "always_present", "on_demand", "checkpoint_only"
        notification_triggers: ["string"], // "session_start", "complexity_escalation"
        communication_channels: ["string"], // "voice", "text", "video", "document"
        decision_authority: ["string"] // "approve_documents", "authorize_filing"
      },
      
      // Availability requirements
      availability: {
        response_time: "string", // "immediate", "within_5_minutes", "within_1_hour"
        time_commitment: "string", // "full_session", "checkpoints_only"
        expertise_required: ["string"] // "tax_law", "litigation", "real_estate"
      }
    }
  ],

  // Workflow definition
  workflow: {
    phases: [
      {
        name: "string", // "intake", "analysis", "collaboration", "review", "delivery"
        description: "string",
        estimated_duration: "string",
        
        // Phase configuration
        participants: {
          ai_agents: ["string"], // roles that are active
          human_roles: ["string"], // roles that participate
          client_involvement: "boolean"
        },
        
        // Phase workflow
        steps: [
          {
            name: "string",
            type: "string", // "ai_task", "human_task", "collaboration", "checkpoint"
            assigned_to: "string", // role name
            dependencies: ["string"], // previous steps required
            deliverables: ["string"], // outputs expected
            
            // Step configuration
            config: {
              tools: ["string"], // MCP tools for this step
              templates: ["string"], // document templates
              approval_required: "boolean",
              timeout: "string" // "30 minutes", "2 hours"
            }
          }
        ],
        
        // Phase completion criteria
        completion_criteria: {
          required_deliverables: ["string"],
          approval_checkpoints: ["string"],
          quality_gates: ["string"]
        }
      }
    ],
    
    // Handoff rules between phases/participants
    handoff_rules: {
      ai_to_human: [
        {
          trigger: "string", // "complexity_threshold", "legal_issue", "client_request"
          from_agent: "string",
          to_human: "string",
          context_transfer: ["string"], // data to transfer
          briefing_template: "string"
        }
      ],
      human_to_ai: [
        {
          trigger: "string",
          from_human: "string", 
          to_agent: "string",
          instructions: "string"
        }
      ],
      collaboration_triggers: [
        {
          condition: "string", // "multiple_expertise_needed", "client_decision_required"
          participants: ["string"],
          collaboration_type: "string", // "conference", "document_review", "decision_meeting"
          moderator: "string" // "ai_moderator", "lead_attorney"
        }
      ]
    }
  },

  // Data collection (extends existing practice templates)
  data_collection: {
    // Inherit from practice area template
    practice_area_template: "string", // references existing template
    
    // Additional session-specific fields
    additional_fields: [
      {
        name: "string",
        type: "string",
        description: "string",
        required: "boolean",
        collected_by: "string", // "ai_agent", "human_role"
        collection_phase: "string" // "intake", "analysis"
      }
    ],
    
    // Data validation rules
    validation: {
      completeness_check: "boolean",
      quality_gates: ["string"],
      approval_required: "boolean"
    }
  },

  // Communication settings
  communication: {
    // Shared workspace configuration
    workspace: {
      enabled: "boolean",
      features: ["string"], // "live_transcription", "document_sharing", "real_time_editing"
      access_control: "object" // role-based permissions
    },
    
    // Notification settings
    notifications: {
      sms_enabled: "boolean",
      email_enabled: "boolean",
      in_app_enabled: "boolean",
      notification_triggers: ["string"]
    },
    
    // Recording and compliance
    recording: {
      enabled: "boolean",
      consent_required: "boolean",
      retention_period: "string",
      access_permissions: ["string"]
    }
  },

  // Success metrics and evaluation
  success_criteria: {
    // Outcome measurements
    metrics: [
      {
        name: "string", // "client_satisfaction", "time_to_completion", "accuracy"
        measurement: "string", // "survey", "duration", "review_score"
        target: "string" // ">=4.5/5", "<2 hours", ">=95%"
      }
    ],
    
    // Quality assurance
    quality_checks: [
      {
        checkpoint: "string", // "document_review", "legal_compliance"
        reviewer: "string", // role responsible
        criteria: ["string"]
      }
    ]
  }
};

// AI Agent Role Definitions (maps to current workflow actions)
export const AI_AGENT_ROLES = {
  // Pre-Engagement Agents
  intake_specialist: {
    name: "Intake Specialist",
    description: "Handles initial client screening, qualification, and information gathering",
    workflow_actions: ["qualify", "intake", "collect-info"],
    tools: ["vapi_voice", "client_validation", "data_collection", "screening_forms"],
    handoff_triggers: ["complex_case", "conflict_detected", "qualification_failed"]
  },

  conflict_checker: {
    name: "Conflict Check Specialist",
    description: "Performs ethical conflict analysis and verification",
    workflow_actions: ["conflict-check"],
    tools: ["conflict_database", "client_search", "case_analysis", "ethics_validation"],
    handoff_triggers: ["conflict_found", "manual_review_needed"]
  },

  referral_coordinator: {
    name: "Referral Coordinator",
    description: "Manages client referrals and attorney matching",
    workflow_actions: ["refer"],
    tools: ["attorney_network", "practice_area_matching", "credit_system", "referral_tracking"],
    handoff_triggers: ["referral_accepted", "referral_declined"]
  },

  // Client Engagement Agents
  legal_researcher: {
    name: "Legal Research Specialist",
    description: "Conducts comprehensive legal research and case law analysis",
    workflow_actions: ["research"],
    tools: ["legal_databases", "case_law_search", "statute_lookup", "precedent_analysis"],
    handoff_triggers: ["complex_legal_issue", "conflicting_precedents", "jurisdiction_questions"]
  },

  document_generator: {
    name: "Document Generation Specialist",
    description: "Creates, drafts, and prepares legal documents",
    workflow_actions: ["draft", "forms"],
    tools: ["document_templates", "form_automation", "contract_generation", "legal_writing"],
    handoff_triggers: ["custom_clauses_needed", "complex_terms", "client_specific_requirements"]
  },

  document_reviewer: {
    name: "Document Review Specialist",
    description: "Reviews documents for accuracy, completeness, and compliance",
    workflow_actions: ["review"],
    tools: ["document_analysis", "compliance_check", "quality_assurance", "error_detection"],
    handoff_triggers: ["errors_found", "compliance_issues", "attorney_review_required"]
  },

  filing_specialist: {
    name: "Court Filing Specialist",
    description: "Handles court filings, submissions, and procedural requirements",
    workflow_actions: ["file"],
    tools: ["court_systems", "e_filing", "deadline_tracking", "procedural_compliance"],
    handoff_triggers: ["filing_errors", "court_rejection", "deadline_conflicts"]
  }
};

// Human Role Definitions (maps to attorney expertise and responsibilities)
export const HUMAN_ROLES = {
  lead_attorney: {
    name: "Lead Attorney",
    description: "Primary legal counsel with decision-making authority",
    workflow_oversight: ["all"],
    decision_authority: ["case_strategy", "settlement_approval", "client_communication"],
    expertise_areas: ["general_practice"]
  },

  specialist_attorney: {
    name: "Specialist Attorney",
    description: "Subject matter expert for specific practice areas",
    workflow_oversight: ["research", "draft", "review"],
    decision_authority: ["technical_legal_issues", "specialized_documents"],
    expertise_areas: ["tax_law", "intellectual_property", "securities", "immigration"]
  },

  paralegal: {
    name: "Paralegal",
    description: "Legal assistant for document preparation and case management",
    workflow_oversight: ["forms", "file", "collect-info"],
    decision_authority: ["document_formatting", "filing_procedures"],
    expertise_areas: ["document_preparation", "case_management"]
  },

  client: {
    name: "Client",
    description: "The person or entity receiving legal services",
    workflow_oversight: ["intake", "review"],
    decision_authority: ["case_decisions", "settlement_approval", "document_approval"],
    expertise_areas: ["business_knowledge", "case_facts"]
  }
};

// Default session templates for common legal workflows
export const DEFAULT_SESSION_TEMPLATES = {
  // 1. Client Qualification & Intake Session
  client_qualification: {
    name: "Client Qualification & Intake",
    description: "Comprehensive client screening, qualification, and initial information gathering",
    category: "pre_engagement",

    session_config: {
      estimated_duration: "30-60 minutes",
      complexity_level: "simple",
      max_participants: 3,
      pricing: {
        type: "fixed",
        base_price: 199,
        additional_fees: {
          rush_processing: 99,
          specialist_consultation: 299
        }
      }
    },

    ai_agents: [
      {
        role: "intake_specialist",
        name: "Client Intake Specialist",
        description: "Conducts initial client screening and qualification assessment",
        config: {
          model: "gpt-4o",
          voice: "openai/echo",
          temperature: 0.3,
          tools: ["vapi_voice", "client_validation", "screening_forms", "qualification_matrix"],
          instructions: "You are a professional intake specialist. Conduct thorough client screening, assess case viability, and gather comprehensive information. Escalate to attorney for complex cases or ethical concerns.",
          handoff_triggers: ["complex_case", "ethical_concerns", "high_value_case", "specialist_needed"]
        },
        activation_conditions: {
          phase: "intake",
          triggers: ["session_start"],
          prerequisites: []
        }
      },
      {
        role: "conflict_checker",
        name: "Conflict Analysis Agent",
        description: "Performs automated conflict checking and ethical screening",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.1,
          tools: ["conflict_database", "client_search", "ethics_validation"],
          instructions: "Perform comprehensive conflict checking against existing clients and cases. Flag any potential conflicts for attorney review.",
          handoff_triggers: ["conflict_detected", "unclear_relationship"]
        },
        activation_conditions: {
          phase: "conflict_check",
          triggers: ["client_data_collected"],
          prerequisites: ["intake_completed"]
        }
      }
    ],

    human_roles: [
      {
        role: "lead_attorney",
        name: "Supervising Attorney",
        description: "Provides oversight and handles complex qualification decisions",
        required: true,
        participation: {
          mode: "on_demand",
          notification_triggers: ["complex_case", "conflict_detected", "qualification_uncertain"],
          communication_channels: ["sms", "voice", "text"],
          decision_authority: ["accept_case", "decline_case", "set_fees", "conflict_resolution"]
        },
        availability: {
          response_time: "within_15_minutes",
          time_commitment: "checkpoints_only",
          expertise_required: ["general_practice", "ethics"]
        }
      },
      {
        role: "client",
        name: "Prospective Client",
        description: "Person seeking legal representation",
        required: true,
        participation: {
          mode: "always_present",
          notification_triggers: ["session_start"],
          communication_channels: ["voice", "text"],
          decision_authority: ["provide_information", "authorize_representation"]
        }
      }
    ],

    workflow: {
      phases: [
        {
          name: "initial_screening",
          description: "Basic client information and case overview",
          estimated_duration: "10-15 minutes",
          participants: {
            ai_agents: ["intake_specialist"],
            human_roles: ["client"],
            client_involvement: true
          },
          steps: [
            {
              name: "client_consultation",
              type: "ai_task",
              assigned_to: "intake_specialist",
              dependencies: [],
              deliverables: ["client_profile", "case_overview", "urgency_assessment"],
              config: {
                tools: ["vapi_voice", "screening_forms"],
                templates: ["intake_questionnaire"],
                approval_required: false,
                timeout: "20 minutes"
              }
            }
          ]
        },
        {
          name: "conflict_analysis",
          description: "Automated conflict checking and ethical review",
          estimated_duration: "5-10 minutes",
          participants: {
            ai_agents: ["conflict_checker"],
            human_roles: [],
            client_involvement: false
          },
          steps: [
            {
              name: "conflict_check",
              type: "ai_task",
              assigned_to: "conflict_checker",
              dependencies: ["client_consultation"],
              deliverables: ["conflict_report", "ethics_clearance"],
              config: {
                tools: ["conflict_database", "ethics_validation"],
                approval_required: false,
                timeout: "15 minutes"
              }
            }
          ]
        },
        {
          name: "qualification_decision",
          description: "Final qualification and engagement decision",
          estimated_duration: "10-20 minutes",
          participants: {
            ai_agents: ["intake_specialist"],
            human_roles: ["lead_attorney", "client"],
            client_involvement: true
          },
          steps: [
            {
              name: "attorney_review",
              type: "human_task",
              assigned_to: "lead_attorney",
              dependencies: ["conflict_check"],
              deliverables: ["qualification_decision", "fee_estimate", "engagement_terms"],
              config: {
                approval_required: true,
                timeout: "24 hours"
              }
            }
          ]
        }
      ]
    }
  },

  // 2. Legal Research & Analysis Session
  legal_research: {
    name: "Legal Research & Analysis",
    description: "Comprehensive legal research with AI assistance and attorney oversight",
    category: "client_engagement",

    session_config: {
      estimated_duration: "2-4 hours",
      complexity_level: "medium",
      max_participants: 4,
      pricing: {
        type: "hourly",
        base_price: 150,
        additional_fees: {
          specialist_consultation: 250,
          rush_research: 200
        }
      }
    },

    ai_agents: [
      {
        role: "legal_researcher",
        name: "Legal Research Specialist",
        description: "Conducts comprehensive legal research and case analysis",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.2,
          tools: ["legal_databases", "case_law_search", "statute_lookup", "precedent_analysis"],
          instructions: "Conduct thorough legal research using multiple sources. Analyze case law, statutes, and regulations. Provide comprehensive analysis with proper citations.",
          handoff_triggers: ["conflicting_precedents", "jurisdiction_complexity", "novel_legal_issue"]
        },
        activation_conditions: {
          phase: "research",
          triggers: ["research_request"],
          prerequisites: ["case_parameters_defined"]
        }
      }
    ],

    human_roles: [
      {
        role: "lead_attorney",
        name: "Research Attorney",
        description: "Guides research direction and validates findings",
        required: true,
        participation: {
          mode: "checkpoint_only",
          notification_triggers: ["research_complete", "complex_findings", "conflicting_law"],
          communication_channels: ["text", "document", "voice"],
          decision_authority: ["research_scope", "legal_strategy", "precedent_selection"]
        }
      },
      {
        role: "specialist_attorney",
        name: "Subject Matter Expert",
        description: "Provides specialized expertise for complex legal issues",
        required: false,
        participation: {
          mode: "on_demand",
          notification_triggers: ["specialist_needed", "novel_issue"],
          communication_channels: ["voice", "document"],
          decision_authority: ["specialized_analysis", "expert_opinion"]
        }
      }
    ]
  },

  // 3. Document Drafting & Review Session
  document_drafting: {
    name: "Document Drafting & Review",
    description: "AI-assisted document creation with attorney review and client collaboration",
    category: "client_engagement",

    session_config: {
      estimated_duration: "1-3 hours",
      complexity_level: "medium",
      max_participants: 4,
      pricing: {
        type: "fixed",
        base_price: 499,
        additional_fees: {
          complex_clauses: 199,
          multiple_revisions: 99
        }
      }
    },

    ai_agents: [
      {
        role: "document_generator",
        name: "Document Drafting Specialist",
        description: "Creates legal documents using templates and AI assistance",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.2,
          tools: ["document_templates", "contract_generation", "legal_writing", "clause_library"],
          instructions: "Draft comprehensive legal documents based on client requirements. Use appropriate templates and ensure legal compliance. Flag complex clauses for attorney review.",
          handoff_triggers: ["complex_terms", "custom_clauses", "regulatory_requirements"]
        },
        activation_conditions: {
          phase: "drafting",
          triggers: ["document_request"],
          prerequisites: ["requirements_gathered"]
        }
      },
      {
        role: "document_reviewer",
        name: "Document Review Specialist",
        description: "Reviews documents for accuracy and compliance",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.1,
          tools: ["document_analysis", "compliance_check", "error_detection", "quality_assurance"],
          instructions: "Thoroughly review documents for accuracy, completeness, and legal compliance. Identify potential issues and suggest improvements.",
          handoff_triggers: ["compliance_issues", "errors_found", "attorney_review_needed"]
        },
        activation_conditions: {
          phase: "review",
          triggers: ["document_completed"],
          prerequisites: ["initial_draft"]
        }
      }
    ],

    human_roles: [
      {
        role: "lead_attorney",
        name: "Drafting Attorney",
        description: "Oversees document creation and provides legal expertise",
        required: true,
        participation: {
          mode: "checkpoint_only",
          notification_triggers: ["complex_clauses", "compliance_issues", "final_review"],
          communication_channels: ["document", "text", "voice"],
          decision_authority: ["approve_document", "require_changes", "legal_strategy"]
        }
      },
      {
        role: "client",
        name: "Document Recipient",
        description: "Reviews and approves final documents",
        required: true,
        participation: {
          mode: "checkpoint_only",
          notification_triggers: ["document_ready", "revisions_needed"],
          communication_channels: ["document", "text", "voice"],
          decision_authority: ["approve_document", "request_changes"]
        }
      }
    ]
  },

  // 4. Court Filing & Compliance Session
  court_filing: {
    name: "Court Filing & Compliance",
    description: "Automated court filing with compliance checking and deadline management",
    category: "client_engagement",

    session_config: {
      estimated_duration: "30-90 minutes",
      complexity_level: "medium",
      max_participants: 3,
      pricing: {
        type: "fixed",
        base_price: 299,
        additional_fees: {
          expedited_filing: 149,
          multiple_jurisdictions: 199
        }
      }
    },

    ai_agents: [
      {
        role: "filing_specialist",
        name: "Court Filing Specialist",
        description: "Handles automated court filings and procedural compliance",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.1,
          tools: ["court_systems", "e_filing", "deadline_tracking", "procedural_compliance"],
          instructions: "Handle court filings efficiently and accurately. Ensure all procedural requirements are met and deadlines are tracked. Escalate filing issues immediately.",
          handoff_triggers: ["filing_rejection", "deadline_conflict", "procedural_error"]
        },
        activation_conditions: {
          phase: "filing",
          triggers: ["documents_ready"],
          prerequisites: ["attorney_approval"]
        }
      }
    ],

    human_roles: [
      {
        role: "lead_attorney",
        name: "Filing Attorney",
        description: "Authorizes filings and handles complications",
        required: true,
        participation: {
          mode: "checkpoint_only",
          notification_triggers: ["filing_ready", "filing_issues", "deadline_alerts"],
          communication_channels: ["sms", "text", "voice"],
          decision_authority: ["authorize_filing", "handle_rejections", "modify_strategy"]
        }
      },
      {
        role: "paralegal",
        name: "Filing Paralegal",
        description: "Assists with document preparation and filing procedures",
        required: false,
        participation: {
          mode: "on_demand",
          notification_triggers: ["complex_filing", "multiple_documents"],
          communication_channels: ["text", "document"],
          decision_authority: ["document_formatting", "filing_procedures"]
        }
      }
    ]
  },

  // 5. Multi-Party Collaboration Session
  multi_party_collaboration: {
    name: "Multi-Party Legal Collaboration",
    description: "Complex legal matters requiring multiple attorneys and specialists",
    category: "complex_engagement",

    session_config: {
      estimated_duration: "2-6 hours",
      complexity_level: "complex",
      max_participants: 8,
      pricing: {
        type: "hourly",
        base_price: 200,
        additional_fees: {
          specialist_consultation: 350,
          expert_witness: 500
        }
      }
    },

    ai_agents: [
      {
        role: "legal_researcher",
        name: "Research Coordinator",
        description: "Coordinates research across multiple legal areas",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.2,
          tools: ["legal_databases", "multi_jurisdiction_search", "precedent_analysis"],
          instructions: "Coordinate comprehensive legal research across multiple practice areas. Synthesize findings and identify conflicts or synergies.",
          handoff_triggers: ["conflicting_law", "jurisdiction_complexity"]
        }
      },
      {
        role: "document_generator",
        name: "Collaboration Coordinator",
        description: "Manages document sharing and version control",
        config: {
          model: "gpt-4o",
          voice: null,
          temperature: 0.1,
          tools: ["document_management", "version_control", "collaboration_tools"],
          instructions: "Manage document collaboration, track changes, and ensure all parties have current versions. Coordinate review cycles.",
          handoff_triggers: ["version_conflicts", "access_issues"]
        }
      }
    ],

    human_roles: [
      {
        role: "lead_attorney",
        name: "Lead Counsel",
        description: "Primary attorney coordinating the collaboration",
        required: true,
        participation: {
          mode: "always_present",
          notification_triggers: ["session_start", "major_decisions"],
          communication_channels: ["voice", "video", "document"],
          decision_authority: ["strategy_decisions", "settlement_authority", "team_coordination"]
        }
      },
      {
        role: "specialist_attorney",
        name: "Subject Matter Expert",
        description: "Specialist attorney for specific legal areas",
        required: false,
        participation: {
          mode: "on_demand",
          notification_triggers: ["expertise_needed", "complex_issues"],
          communication_channels: ["voice", "video", "document"],
          decision_authority: ["specialized_advice", "technical_decisions"]
        }
      },
      {
        role: "client",
        name: "Client Representative",
        description: "Client or authorized representative",
        required: true,
        participation: {
          mode: "checkpoint_only",
          notification_triggers: ["major_decisions", "strategy_changes"],
          communication_channels: ["voice", "video"],
          decision_authority: ["business_decisions", "settlement_approval"]
        }
      }
    ]
  }
};
