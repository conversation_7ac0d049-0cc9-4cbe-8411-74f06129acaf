/**
 * Vapi MCP Search Service
 * 
 * This service handles the integration with Vapi's MCP tool for web search functionality.
 * It provides methods to configure the MCP tool and process search results.
 */

// Configuration for the Vapi MCP tool
const MCP_TOOL_CONFIG = {
  name: 'web_search',
  description: 'Searches the web for information related to legal questions',
  parameters: {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: 'The search query'
      },
      numResults: {
        type: 'number',
        description: 'Number of results to return',
        default: 5
      },
      type: {
        type: 'string',
        description: 'Type of search (web, legal)',
        enum: ['web', 'legal'],
        default: 'web'
      },
      format: {
        type: 'string',
        description: 'Format of results (cards, timeline, statute, concept-map)',
        enum: ['cards', 'timeline', 'statute', 'concept-map'],
        default: 'cards'
      }
    },
    required: ['query']
  }
};

/**
 * Get the MCP tool configuration for Vapi
 * @returns {Object} MCP tool configuration
 */
export const getMcpToolConfig = () => {
  return MCP_TOOL_CONFIG;
};

/**
 * Process search results from the MCP tool
 * @param {Object} toolResponse - Response from the MCP tool
 * @returns {Object} Processed search results
 */
export const processSearchResults = (toolResponse) => {
  try {
    // Check if the response is valid
    if (!toolResponse || !toolResponse.results) {
      console.error('Invalid search tool response:', toolResponse);
      return null;
    }
    
    // Return the formatted results
    return toolResponse.results;
  } catch (error) {
    console.error('Error processing search results:', error);
    return null;
  }
};

/**
 * Generate system prompt instructions for using the web search tool
 * @returns {string} System prompt instructions
 */
export const getWebSearchSystemPrompt = () => {
  return `
You have access to a web search tool that can help you find information on the internet.

When to use the web search tool:
1. When asked about current events, news, or recent developments
2. When asked about specific legal cases, statutes, or regulations
3. When you need to provide factual information that may have changed since your training data
4. When asked about specific organizations, people, or places that you don't have detailed information about

How to use the web search tool:
1. When a user asks a question that would benefit from web search, use the web_search tool
2. Formulate a clear, specific search query related to the user's question
3. Specify the number of results you want (usually 3-5 is sufficient)
4. For legal questions, set the type parameter to "legal"
5. Choose an appropriate format for the results based on the type of information:
   - "cards" for general information (default)
   - "timeline" for historical or chronological information
   - "statute" for legal statutes or regulations
   - "concept-map" for complex topics with multiple related concepts

Example tool usage:
For a question about "recent changes to California tenant laws":
{
  "query": "California tenant law changes 2023",
  "numResults": 3,
  "type": "legal",
  "format": "cards"
}

After receiving search results:
1. Analyze the information from the search results
2. Synthesize a response that directly addresses the user's question
3. Cite your sources by mentioning the websites or publications you found information from
4. If the search results don't provide adequate information, acknowledge this and provide the best answer you can based on your training

Remember to maintain a professional tone and clarify that you're providing information, not legal advice.
`;
};

export default {
  getMcpToolConfig,
  processSearchResults,
  getWebSearchSystemPrompt
};
