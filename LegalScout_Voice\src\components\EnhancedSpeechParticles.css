.enhanced-speech-particles {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speech-particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bars-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  opacity: 0.7; /* Make it slightly transparent to blend with the mist */
}

.bars-svg {
  width: 100%;
  height: 100%;
}

/* Animation for the bars */
@keyframes pulse {
  0% {
    transform: scaleY(0.8);
  }
  50% {
    transform: scaleY(1.2);
  }
  100% {
    transform: scaleY(0.8);
  }
}

/* Add this to your existing CSS file or create a new one */
.enhanced-speech-particles.assistant-speaking rect {
  fill: rgba(0, 191, 255, 0.8); /* Light blue for assistant */
  animation: pulse 1s infinite;
}

.enhanced-speech-particles.user-speaking rect {
  fill: rgba(0, 200, 100, 0.8); /* Green for user */
  animation: pulse 0.8s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-speech-particles {
    height: 150px;
  }
}
