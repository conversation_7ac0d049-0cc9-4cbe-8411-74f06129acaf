dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
dashboard:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
supabase.js?t=1749947258324:213 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized Object
vapiAssistantService.js?t=1749947258324:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749947258324:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=1749947258324:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749947258324:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: Object
main.jsx:57 🔥 [main.jsx] Critical imports check: Object
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
hook.js:377 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: Object
hook.js:377 🔥 [App.jsx] App component is starting!
hook.js:377 🔥 [App.jsx] Auth state: Object
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=1749947258324:27 🔧 [Supabase] EMERGENCY: Bypassing broken Supabase client, using stub only
supabase.js?t=1749947258324:28 🔧 [Supabase] Headers/fetch environment is corrupted by browser extension or hook
mockSupabase.js:3 🚧 Creating stub Supabase client for fallback
AuthContext.jsx:485 Using real authentication in all environments
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
mockSupabase.js:14 🚧 [Stub] Auth state change listener registered
mockSupabase.js:14 🚧 [Stub] Auth state change listener registered
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:164 🔐 [AuthContext] No session found
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
index.ts:5 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AttorneyProfileManager.js?t=1749947258324:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
mockSupabase.js:19 🚧 [Stub] Querying table: attorneys
mockSupabase.js:24 🚧 [Stub] SELECT * FROM attorneys
mockSupabase.js:28 🚧 [Stub] WHERE id = 87756a2c-a398-43f2-889a-b8815684df71
mockSupabase.js:40 🚧 [Stub] SINGLE result
AttorneyProfileManager.js?t=1749947258324:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
