import React, { useState, useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css'; // Re-add Leaflet CSS import
import './MapDossierView.css';
import './Map.css';
import { withDevTools, createDebugger, trackUserJourney } from '../utils/debugConfig';

// Create debugger for MapDossierView component
const debug = createDebugger('MapDossierView');

// Import marker icons to avoid missing marker issue
import icon from 'leaflet/dist/images/marker-icon.png';
import iconRetina from 'leaflet/dist/images/marker-icon-2x.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

// Fix default icon issue in Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconUrl: icon,
  iconRetinaUrl: iconRetina,
  shadowUrl: iconShadow
});

// Try to import SVG files for custom markers
let attorneyIconUrl, locationIconUrl;
try {
  // Set the dynamic paths for the SVG files
  attorneyIconUrl = new URL('/attorney-marker.svg', import.meta.url).href;
  locationIconUrl = new URL('/location-marker.svg', import.meta.url).href;
} catch (error) {
  console.error('Error loading SVG icons:', error);
  // Use fallback icons (Leaflet default) if SVG loading fails
  attorneyIconUrl = 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png';
  locationIconUrl = 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png';
}

// Define constants for the Continental US view
const DEFAULT_US_CENTER = [39.8283, -98.5795]; // Center of the continental US
const DEFAULT_US_ZOOM = 4; // Zoom level for continental US view

// Define custom icon for attorneys
const attorneyIcon = L.icon({
  iconUrl: attorneyIconUrl,
  iconSize: [32, 32], // size of the icon
  iconAnchor: [16, 32], // point of the icon which will correspond to marker's location
  popupAnchor: [0, -32] // point from which the popup should open relative to the iconAnchor
});

// Define custom icon for client location
const locationIcon = L.icon({
  iconUrl: locationIconUrl,
  iconSize: [32, 32], // size of the icon
  iconAnchor: [16, 32], // point of the icon which will correspond to marker's location
  popupAnchor: [0, -32] // point from which the popup should open relative to the iconAnchor
});

// Add error handler for SVG loading - fallback to PNG
document.addEventListener('DOMContentLoaded', () => {
  // Set up fallback for attorney marker
  const attorneyMarkerElements = document.querySelectorAll('.attorney-marker-icon img');
  attorneyMarkerElements.forEach(img => {
    img.onerror = function() {
      this.src = '/attorney-marker.png';
    };
  });
  
  // Set up fallback for location marker
  const locationMarkerElements = document.querySelectorAll('.location-marker-icon img');
  locationMarkerElements.forEach(img => {
    img.onerror = function() {
      this.src = '/location-marker.png';
    };
  });
});

// Helper function to get a user-friendly location name
const getLocationName = (location) => {
  return location.address || `${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}`;
};

const MapDossierView = ({ isVisible, location, attorneys = [] }) => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef({
    attorneys: [],
    clientLocation: null
  });

  // Initialize map when component becomes visible
  useEffect(() => {
    if (!isVisible || !mapContainerRef.current) return;

    // Initialize map if it doesn't exist yet
    if (!mapInstanceRef.current) {
      // Create map instance
      mapInstanceRef.current = L.map(mapContainerRef.current, {
        // Always initialize with continental US view
        center: DEFAULT_US_CENTER,
        zoom: DEFAULT_US_ZOOM,
        zoomControl: true,
        attributionControl: false
      });

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        className: 'dark-tiles' // Apply dark styling
      }).addTo(mapInstanceRef.current);
    }

    // Update the map view if we have a more specific location
    if (location && location.lat && location.lng) {
      mapInstanceRef.current.setView([location.lat, location.lng], 11);
      
      // Add client location marker if we have location data
      if (!markersRef.current.clientLocation && location.lat && location.lng) {
        markersRef.current.clientLocation = L.marker([location.lat, location.lng], { icon: locationIcon })
          .addTo(mapInstanceRef.current)
          .bindPopup(location.address || 'Your Location');
      }
    }

    // Cleanup function to destroy map when component unmounts
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
        markersRef.current = {
          attorneys: [],
          clientLocation: null
        };
      }
    };
  }, [isVisible, location]);

  // Add attorney markers when attorneys data changes
  useEffect(() => {
    if (!mapInstanceRef.current || !attorneys.length) return;

    // Clear existing attorney markers
    markersRef.current.attorneys.forEach(marker => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.removeLayer(marker);
      }
    });
    markersRef.current.attorneys = [];

    // Add new attorney markers
    attorneys.forEach(attorney => {
      if (attorney.latitude && attorney.longitude) {
        const marker = L.marker([attorney.latitude, attorney.longitude], { icon: attorneyIcon })
          .addTo(mapInstanceRef.current)
          .bindPopup(`<strong>${attorney.name}</strong><br>${attorney.practiceArea || ''}`);
        
        markersRef.current.attorneys.push(marker);
      }
    });
  }, [attorneys]);

  return (
    <div className="map-dossier-view">
      <div ref={mapContainerRef} className="map-container"></div>
    </div>
  );
};

export default MapDossierView;