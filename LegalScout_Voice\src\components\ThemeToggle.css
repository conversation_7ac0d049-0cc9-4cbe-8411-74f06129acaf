.theme-toggle-container {
  margin-left: 1rem;
  display: flex;
  align-items: center;
}

.theme-toggle {
  position: relative;
  width: 56px;
  height: 28px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 14px;
  border: none;
  cursor: pointer;
  padding: 2px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(59, 130, 246, 0.2),
    rgba(59, 130, 246, 0.4)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-toggle:hover::before {
  opacity: 1;
}

.toggle-circle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background-color: #1a1a1a;
  border-radius: 50%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.theme-toggle.dark .toggle-circle {
  transform: translateX(28px);
  background-color: #fff;
}

.toggle-icons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px;
  z-index: 1;
}

.sun-icon,
.moon-icon {
  width: 16px;
  height: 16px;
  color: #fff;
  transition: opacity 0.3s ease;
}

.theme-toggle.dark .sun-icon {
  opacity: 0.3;
}

.theme-toggle.light .moon-icon {
  opacity: 0.3;
}

/* Light mode styles */
.theme-toggle.light {
  background: rgba(59, 130, 246, 0.15);
}

.theme-toggle.light .toggle-circle {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
} 