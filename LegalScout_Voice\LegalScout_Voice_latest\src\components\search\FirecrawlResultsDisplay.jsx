import React from 'react';
import './FirecrawlResultsDisplay.css';

/**
 * Component to display Firecrawl search results
 * 
 * @param {Object} props Component props
 * @param {Object} props.results Formatted search results to display
 * @param {string} props.query Original search query
 * @param {Function} props.onClose Function to close the results panel
 */
const FirecrawlResultsDisplay = ({ results, query, onClose }) => {
  if (!results || !results.items || results.items.length === 0) {
    return (
      <div className="firecrawl-results empty">
        <div className="results-header">
          <h3>Search Results</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="results-empty">
          <p>No results found for "{query}"</p>
        </div>
      </div>
    );
  }

  // Render different layouts based on the result type
  const renderResults = () => {
    switch (results.type) {
      case 'detailed':
        return (
          <div className="results-detailed">
            {results.items.map((item, index) => (
              <div key={index} className="result-card detailed">
                <h4 className="result-title">
                  <a href={item.url} target="_blank" rel="noopener noreferrer">
                    {item.title}
                  </a>
                </h4>
                <div className="result-source">{item.source}</div>
                <div className="result-summary">{item.summary}</div>
                <div 
                  className="result-content" 
                  dangerouslySetInnerHTML={{ __html: item.content }}
                />
              </div>
            ))}
          </div>
        );

      case 'legal':
        return (
          <div className="results-legal">
            {results.items.map((item, index) => (
              <div key={index} className="result-card legal">
                <h4 className="result-title">
                  <a href={item.url} target="_blank" rel="noopener noreferrer">
                    {item.title}
                  </a>
                </h4>
                {item.citation && (
                  <div className="result-citation">{item.citation}</div>
                )}
                <div className="result-source">{item.source}</div>
                <div className="result-summary">{item.summary}</div>
              </div>
            ))}
          </div>
        );

      case 'simple':
      default:
        return (
          <div className="results-simple">
            {results.items.map((item, index) => (
              <div key={index} className="result-card simple">
                <h4 className="result-title">
                  <a href={item.url} target="_blank" rel="noopener noreferrer">
                    {item.title}
                  </a>
                </h4>
                <div className="result-source">{item.source}</div>
                <div className="result-summary">{item.summary}</div>
              </div>
            ))}
          </div>
        );
    }
  };

  return (
    <div className="firecrawl-results">
      <div className="results-header">
        <h3>Search Results: "{query}"</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      <div className="results-content">
        {renderResults()}
      </div>
      <div className="results-footer">
        <div className="results-attribution">
          Results powered by Firecrawl
        </div>
      </div>
    </div>
  );
};

export default FirecrawlResultsDisplay;
