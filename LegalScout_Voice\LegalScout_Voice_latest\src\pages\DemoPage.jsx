import React, { useState, useEffect, useRef } from 'react';
import { PracticeAreasSelect } from '../components/PracticeAreasSelect';
import { mapPreviewToDatabase } from '../utils/configMapping';
import { supabase } from '../lib/supabase';

// Default customization values
const defaultCustomizations = {
  firmName: 'Your Law Firm',
  attorneyName: 'Your Name',
  practiceAreas: [],
  state: '',
  backgroundColor: '#1a1a1a', // Dark background color for dark mode
  templateColors: {
    primary: '#4B74AA',
    secondary: '#2C3E50',
  },
  logoUrl: '',
  welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",
  backgroundOpacity: 0.9
};

// Practice area templates for prompts
const practiceAreaTemplates = {
  'Personal Injury': {
    welcomeMessage: "Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.",
    informationGathering: "I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?"
  },
  'Family Law': {
    welcomeMessage: "Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.",
    informationGathering: "I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?"
  },
  'Criminal Defense': {
    welcomeMessage: "Welcome to our criminal defense consultation. I'm your virtual legal assistant and I'm here to gather preliminary information about your case.",
    informationGathering: "I need to know the charges against you, the date and location of the alleged crime, and the details of the incident as you recall them."
  },
  'Estate Planning': {
    welcomeMessage: "Welcome to our estate planning consultation. I'm your virtual legal assistant and I'm here to help with your estate planning needs.",
    informationGathering: "I'd like to understand your estate planning goals. Are you interested in creating a will, trust, power of attorney, or do you need assistance with probate matters?"
  },
  'Business Law': {
    welcomeMessage: "Welcome to our business law consultation. I'm your virtual legal assistant and I'm here to help with your business legal needs.",
    informationGathering: "Please tell me about your business and the legal issues you're facing. Are you dealing with contracts, formation, employment issues, or other business matters?"
  }
};

// List of US states for dropdown
const usStates = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
  { value: 'DC', label: 'District of Columbia' }
];

const DemoPage = () => {
  // State for customization options
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [customizations, setCustomizations] = useState(defaultCustomizations);
  const [showPreview, setShowPreview] = useState(false);
  const [activeSetting, setActiveSetting] = useState('basic');
  const [isProcessing, setIsProcessing] = useState(false);
  const previewIframeRef = useRef(null);
  const previewContainerRef = useRef(null);
  const [iframeHeight, setIframeHeight] = useState(600); // Add state for iframe height
  const [iframeReady, setIframeReady] = useState(false);
  const [iframeError, setIframeError] = useState(null);
  const [data, setData] = useState({
    companyName: 'Your Law Firm',
    primaryColor: '#2563eb', // Default primary color - blue
    backgroundColor: '#000000', // Default background color - black
    backgroundOpacity: '0.95', // Default background opacity
    messageDelay: '1000', // Default message delay
    theme: 'dark', // Default theme
  });
  const [currentTab, setCurrentTab] = useState('general');

  // Handle message events from iframe (like height updates)
  useEffect(() => {
    const handleIframeMessage = (event) => {
      // Safely check if the message is coming from our iframe by checking origin
      try {
        // Check if the message is coming from our iframe
        if (event.data && typeof event.data === 'object') {
          console.log('[DemoPage] Received message from iframe:', event.data.type);

          if (event.data.type === 'iframeHeight') {
            console.log('[DemoPage] Received iframe height update:', event.data.height);
            // Set minimum height to avoid collapse
            setIframeHeight(Math.max(600, event.data.height));
          }

          if (event.data.type === 'previewReady') {
            console.log('[DemoPage] Preview iframe is ready');
            setIframeReady(true);
          }

          if (event.data.type === 'error') {
            console.error('[DemoPage] Error from iframe:', event.data.message);
            setIframeError(event.data.message);
          }
        }
      } catch (error) {
        console.warn('[DemoPage] Error processing iframe message:', error);
      }
    };

    window.addEventListener('message', handleIframeMessage);
    return () => window.removeEventListener('message', handleIframeMessage);
  }, []);

  // Handle iframe loading
  const handleIframeLoad = () => {
    console.log('[DemoPage] Iframe loaded');
    setIframeError(null);

    // Start checking for iframe ping messages
    startIframePingListener();

    // Handle the JSON error message by injecting a script to suppress it
    try {
      const iframe = document.getElementById('preview-iframe');
      if (iframe && iframe.contentWindow) {
        // Create a script to handle the error
        const script = `
          (function() {
            // Override fetch for configuration requests to handle errors gracefully
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
              return originalFetch(url, options)
                .then(response => {
                  // Check if response is ok
                  if (!response.ok && (url.includes('config') || url.includes('subdomain'))) {
                    console.warn('Config fetch returned not OK, creating mock response');
                    return {
                      ok: true,
                      json: () => Promise.resolve({}),
                      text: () => Promise.resolve("{}")
                    };
                  }
                  return response;
                })
                .then(response => {
                  // Handle non-JSON responses for config endpoints
                  if (url && (url.includes('config') || url.includes('subdomain'))) {
                    const originalJson = response.json;
                    response.json = function() {
                      return originalJson.call(this)
                        .catch(err => {
                          console.warn('Error parsing JSON, returning empty object instead:', err);
                          return {};
                        });
                    };

                    const originalText = response.text;
                    response.text = function() {
                      return originalText.call(this)
                        .then(text => {
                          // If the response starts with HTML tags, return empty JSON
                          if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                            console.warn('Received HTML instead of JSON, returning empty object');
                            return "{}";
                          }
                          return text;
                        })
                        .catch(err => {
                          console.warn('Error getting text response, returning empty JSON:', err);
                          return "{}";
                        });
                    };
                  }
                  return response;
                })
                .catch(error => {
                  // If it's a config error, suppress the visible error
                  if (url && (url.includes('config') || url.includes('subdomain'))) {
                    console.warn('Config fetch error handled silently:', error);
                    // Return empty config instead of error
                    return {
                      ok: true,
                      json: () => Promise.resolve({}),
                      text: () => Promise.resolve("{}")
                    };
                  }
                  // Otherwise, let the error propagate
                  return Promise.reject(error);
                });
            };

            // Override JSON.parse to handle invalid JSON from config endpoints
            const originalJsonParse = JSON.parse;
            JSON.parse = function(text) {
              try {
                return originalJsonParse(text);
              } catch (e) {
                console.warn('JSON parse error, returning empty object:', e);
                const stack = new Error().stack || '';

                // Only handle JSON errors for config/subdomain requests
                if (stack.includes('config') || stack.includes('subdomain')) {
                  console.warn('Suppressing JSON error for config/subdomain');
                  return {};
                }
                throw e;
              }
            };

            // Remove any existing error banners
            const existingBanners = document.querySelectorAll('.error-banner');
            existingBanners.forEach(banner => banner.parentNode.removeChild(banner));

            // Hide and remove error messages with specific content
            setInterval(() => {
              const errorElements = document.querySelectorAll('div, p');
              errorElements.forEach(el => {
                if (el.textContent && (
                    el.textContent.includes('SyntaxError') ||
                    el.textContent.includes('Unexpected token') ||
                    el.textContent.includes('is not valid JSON') ||
                    el.textContent.includes('Error loading subdomain configs')
                  )) {
                  el.style.display = 'none';
                  if (el.parentNode) {
                    el.parentNode.removeChild(el);
                  }
                }
              });
            }, 500);

            // Center the content in the preview pane
            const styleEl = document.createElement('style');
            styleEl.textContent = \`
              body, html {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                display: flex !important;
                justify-content: center !important;
                align-items: flex-start !important;
                overflow-x: hidden !important;
              }
              #preview-content, .preview-interface {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 auto !important;
                overflow-x: hidden !important;
              }
              /* Hide error messages */
              [style*="background-color: red"], [style*="background-color: rgba(255, 0, 0"], [style*="background-color:#ff0000"] {
                display: none !important;
              }
            \`;
            document.head.appendChild(styleEl);
          })();
        `;

        // Inject the script
        setTimeout(() => {
          try {
            const scriptEl = iframe.contentDocument.createElement('script');
            scriptEl.textContent = script;
            iframe.contentDocument.head.appendChild(scriptEl);
          } catch (e) {
            console.warn('Could not inject error handling script:', e);
          }
        }, 200);
      }
    } catch (e) {
      console.warn('Error handling iframe errors:', e);
    }
  };

  // Handle website submission
  const handleWebsiteSubmit = async () => {
    if (!websiteUrl) return;

    setIsProcessing(true);

    // Simulate processing delay
    setTimeout(() => {
      setShowPreview(true);
      setIsProcessing(false);

      // Scroll to preview
      if (previewContainerRef.current) {
        setTimeout(() => {
          previewContainerRef.current.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    }, 1500);
  };

  // Save configuration to Supabase
  const saveToSupabase = async () => {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        alert('You must be logged in to save your configuration');
        return;
      }

      // Get the attorney record for the current user
      const { data: attorneyData, error: attorneyError } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (attorneyError) {
        console.error('Error fetching attorney data:', attorneyError);
        alert('Error fetching your attorney profile. Please try again.');
        return;
      }

      if (!attorneyData) {
        alert('No attorney profile found for your account');
        return;
      }

      // Map the customizations to database fields
      const dbConfig = mapPreviewToDatabase(customizations);

      // Update the attorney record
      const { error: updateError } = await supabase
        .from('attorneys')
        .update(dbConfig)
        .eq('id', attorneyData.id);

      if (updateError) {
        console.error('Error updating attorney data:', updateError);
        alert('Error saving your configuration. Please try again.');
        return;
      }

      alert('Configuration saved successfully!');
    } catch (error) {
      console.error('Error saving configuration:', error);
      alert('An unexpected error occurred. Please try again.');
    }
  };

  // Update preview with new customizations
  const handlePreviewChanges = () => {
    // If we have an iframe reference, update it with new URL
    if (previewIframeRef.current) {
      console.log('[DemoPage] Updating preview with new customizations');

      // Try to update via postMessage first
      const message = {
        type: 'updateCustomizations',
        customizations
      };

      // Use a timeout to ensure the iframe has loaded
      setTimeout(() => {
        try {
          // Check if iframe is accessible
          if (!previewIframeRef.current || !previewIframeRef.current.contentWindow) {
            console.warn('[DemoPage] Iframe or contentWindow not accessible');

            // Try to find the iframe by other means
            const allIframes = document.querySelectorAll('iframe');
            console.log(`[DemoPage] Found ${allIframes.length} iframes on the page`);

            // Try to find the preview iframe by its src
            for (let i = 0; i < allIframes.length; i++) {
              const src = allIframes[i].src || '';
              if (src.includes('preview') || src.includes('enhanced-preview')) {
                console.log('[DemoPage] Found preview iframe by src:', src);
                if (allIframes[i].contentWindow) {
                  allIframes[i].contentWindow.postMessage(message, '*');
                  console.log('[DemoPage] Sent customization update via postMessage to found iframe');
                  return;
                }
              }
            }

            // If we still can't find a suitable iframe, reload
            reloadIframe();
            return;
          }

          // Send message to iframe
          previewIframeRef.current.contentWindow.postMessage(message, '*');
          console.log('[DemoPage] Sent customization update via postMessage');
        } catch (error) {
          console.warn('[DemoPage] Error sending message to iframe:', error);

          // If postMessage fails, reload the iframe as fallback
          reloadIframe();
        }
      }, 500); // Wait 500ms to ensure iframe is loaded
    }
  };

  // Function to reload iframe
  const reloadIframe = () => {
    console.log('[DemoPage] Reloading iframe');
    setIframeError(null);

    // Instead of changing just the src, we'll replace the entire iframe
    // This provides a more thorough reset of the iframe state
    if (previewIframeRef.current && previewIframeRef.current.parentNode) {
      const parent = previewIframeRef.current.parentNode;
      const oldIframe = previewIframeRef.current;

      // Create a new iframe with the same properties
      const newIframe = document.createElement('iframe');
      newIframe.className = 'preview-iframe';
      newIframe.style.width = '100%';
      newIframe.style.height = '100%';
      newIframe.style.border = 'none';
      newIframe.style.visibility = 'visible';
      newIframe.style.display = 'block';
      newIframe.allow = 'fullscreen';
      newIframe.sandbox = 'allow-scripts allow-same-origin allow-forms';
      newIframe.onload = handleIframeLoad;
      newIframe.onerror = handleIframeError;

      // Create URL with production flag
      const previewUrl = new URL(getPreviewUrl() || '/preview-page');
      previewUrl.searchParams.append('production', 'true');

      newIframe.src = previewUrl.toString();

      // Replace the old iframe with the new one
      parent.replaceChild(newIframe, oldIframe);
      previewIframeRef.current = newIframe;
    }
  };

  // Handle file uploads for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCustomizations(prev => ({
          ...prev,
          logoUrl: reader.result,
        }));

        // Show preview when logo is uploaded
        setShowPreview(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Generate preview URL with query parameters
  const getPreviewUrl = () => {
    const params = new URLSearchParams({
      firm: customizations.firmName,
      attorney: customizations.attorneyName,
      primary: customizations.templateColors.primary,
      secondary: customizations.templateColors.secondary,
      showStartButton: 'true',
      responsive: 'true',
      backgroundOpacity: customizations.backgroundOpacity || 0.9,
      backgroundColor: customizations.backgroundColor || '#1a1a1a'
    });

    if (customizations.logoUrl) {
      params.append('logo', customizations.logoUrl);
    }

    if (customizations.welcomeMessage) {
      params.append('welcome', customizations.welcomeMessage);
    }

    if (customizations.informationGathering) {
      params.append('info', customizations.informationGathering);
    }

    if (customizations.state) {
      params.append('state', customizations.state);
    }

    if (customizations.practiceAreas && customizations.practiceAreas.length) {
      params.append('areas', customizations.practiceAreas.join(','));
    }

    console.log('Preview URL params:', {
      backgroundOpacity: customizations.backgroundOpacity,
      backgroundColor: customizations.backgroundColor,
      params: params.toString()
    });

    return `/preview?${params.toString()}`;
  };

  // Handle practice area selection and update prompts
  const handlePracticeAreaChange = (practiceArea) => {
    // Get template for selected practice area
    const template = practiceAreaTemplates[practiceArea];

    if (template) {
      setCustomizations(prev => ({
        ...prev,
        practiceAreas: prev.practiceAreas.includes(practiceArea) ?
          prev.practiceAreas :
          [...prev.practiceAreas, practiceArea],
        welcomeMessage: template.welcomeMessage,
        informationGathering: template.informationGathering
      }));
    }

    // Show preview when practice area is selected
    setShowPreview(true);
  };

  // Render the preview iframe with improved error handling and scrolling support
  const renderPreviewIframe = () => {
    const previewUrl = `/preview?${getPreviewParams()}&production=true&centered=true&fullWidth=true`;

    return (
      <div className="preview-container h-full w-full" style={{
        position: 'relative',
        height: '100%',
        width: '100%',
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start'
      }}>
        {/* Show error message and reload button when iframe fails to load */}
        {iframeError && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            backgroundColor: 'rgba(0,0,0,0.7)',
            padding: '20px',
            borderRadius: '8px',
            color: 'white',
            zIndex: 10
          }}>
            <p>Error loading preview. Please try again.</p>
            <button
              onClick={reloadIframe}
              style={{
                marginTop: '10px',
                padding: '8px 16px',
                backgroundColor: '#4B74AA',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Reload Preview
            </button>
          </div>
        )}

        <iframe
          ref={iframeRef}
          title="Preview"
          src={previewUrl}
          className="w-full h-full border-none"
          style={{
            border: 'none',
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            margin: 0,
            padding: 0,
            display: 'block',
            visibility: 'visible',
            opacity: 1,
            overflow: 'auto'
          }}
          onLoad={handleIframeLoad}
          onError={() => setIframeError(true)}
          allow="fullscreen"
        />
      </div>
    );
  };

  // Handle iframe ping/pong communication
  const startIframePingListener = () => {
    // Remove any existing event listener
    window.removeEventListener('message', handleIframeMessage);

    // Add event listener for iframe messages
    window.addEventListener('message', handleIframeMessage);

    // Set initial visibility check timeout
    setTimeout(() => {
      checkIframeVisibility();
    }, 3000);
  };

  const handleIframeMessage = (event) => {
    // Ensure message is from our iframe
    if (!previewIframeRef.current || event.source !== previewIframeRef.current.contentWindow) {
      return;
    }

    const { type, ...data } = event.data;

    switch (type) {
      case 'IFRAME_READY':
        console.log('[DemoPage] Received IFRAME_READY message', data);
        // Respond with a pong message
        sendMessageToIframe('IFRAME_PONG', { acknowledged: true });
        break;

      case 'IFRAME_PING':
        console.log('[DemoPage] Received IFRAME_PING message', data);
        // Respond with a pong message
        sendMessageToIframe('IFRAME_PONG', {
          acknowledged: true,
          timestamp: Date.now()
        });
        break;

      case 'RESIZE_IFRAME':
        // Handle iframe height adjustment
        if (data && typeof data.height === 'number' && data.height > 0) {
          console.log('[DemoPage] Resizing iframe to height:', data.height);
          setIframeHeight(Math.max(data.height, 600)); // Minimum height of 600px
        }
        break;

      default:
        // Ignore other message types
        break;
    }
  };

  const handleIframeError = (error) => {
    console.error('[DemoPage] Iframe error:', error);
    setIframeError('Failed to load the preview. Please try reloading.');
  };

  // Check iframe visibility and force if needed
  const checkIframeVisibility = () => {
    console.log('[DemoPage] Checking iframe visibility');

    // Tell the iframe to check visibility
    sendMessageToIframe('IFRAME_PONG', {
      checkVisibility: true,
      timestamp: Date.now()
    });

    // Schedule periodic visibility checks
    setTimeout(() => {
      checkIframeVisibility();
    }, 10000); // Check every 10 seconds
  };

  const sendMessageToIframe = (type, data = {}) => {
    // Use a timeout to ensure the iframe has loaded
    setTimeout(() => {
      if (previewIframeRef.current && previewIframeRef.current.contentWindow) {
        try {
          previewIframeRef.current.contentWindow.postMessage({ type, ...data }, '*');
          console.log(`[DemoPage] Sent message to iframe: ${type}`);
        } catch (error) {
          console.error('[DemoPage] Error sending message to iframe:', error);

          // Try to find the iframe by other means
          const allIframes = document.querySelectorAll('iframe');
          console.log(`[DemoPage] Found ${allIframes.length} iframes on the page`);

          // Try to find the preview iframe by its src
          for (let i = 0; i < allIframes.length; i++) {
            const src = allIframes[i].src || '';
            if (src.includes('preview') || src.includes('enhanced-preview')) {
              console.log('[DemoPage] Found preview iframe by src:', src);
              if (allIframes[i].contentWindow) {
                allIframes[i].contentWindow.postMessage({ type, ...data }, '*');
                console.log(`[DemoPage] Sent message to found iframe: ${type}`);
                return;
              }
            }
          }
        }
      } else {
        console.warn('[DemoPage] Iframe or contentWindow not accessible for message:', type);
      }
    }, 300); // Wait 300ms to ensure iframe is loaded
  };

  // Clean up event listeners when component unmounts
  useEffect(() => {
    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, []);

  // Add a bit of CSS for proper scrolling and centering
  useEffect(() => {
    // Create style element
    const style = document.createElement('style');
    style.textContent = `
      .preview-iframe-container {
        height: 800px;
        min-height: 800px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        width: 100%;
        margin: 0 auto;
      }

      .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
        flex: 1 1 auto;
        margin: 0 auto;
      }

      /* Error message styling */
      .error-banner {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: rgba(255, 0, 0, 0.8);
        color: white;
        padding: 5px 10px;
        font-size: 12px;
        z-index: 9999;
        text-align: center;
        display: none;
      }

      @media (max-height: 900px) {
        .preview-iframe-container {
          height: 700px;
          min-height: 700px;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Add the showIframeError function
  const showIframeError = () => {
    const errorElem = document.getElementById('iframe-error');
    if (errorElem) {
      errorElem.style.display = 'block';
    }
    setIframeReady(false);
  };

  // Add useEffect to initialize the iframe on component mount
  useEffect(() => {
    // Initialize with default configuration
    renderPreviewIframe();

    // Set default active setting
    setActiveSetting('basic');
  }, []);

  // Add useEffect to update the iframe whenever customizations are saved
  useEffect(() => {
    if (iframeReady) {
      renderPreviewIframe();
    }
  }, [data.theme]);

  // Add a keyframes animation to the head for pulsating border
  useEffect(() => {
    // Create style element for pulsating border animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes neonPulse {
        0% { box-shadow: 0 0 5px ${customizations.templateColors.primary || '#4B74AA'}, 0 0 10px ${customizations.templateColors.primary || '#4B74AA'} inset; }
        50% { box-shadow: 0 0 8px ${customizations.templateColors.primary || '#4B74AA'}, 0 0 15px ${customizations.templateColors.primary || '#4B74AA'} inset; }
        100% { box-shadow: 0 0 5px ${customizations.templateColors.primary || '#4B74AA'}, 0 0 10px ${customizations.templateColors.primary || '#4B74AA'} inset; }
      }

      #iframe-container {
        animation: neonPulse 3s infinite ease-in-out;
        border: 1px solid ${customizations.templateColors.primary || '#4B74AA'};
        overflow: hidden;
        padding: 3px;
        background: rgba(0,0,0,0.05);
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, [customizations.templateColors.primary]);

  // Enhance JSON error handling globally
  useEffect(() => {
    // Suppress error messages in the DOM
    const suppressErrorMessages = () => {
      // Get all error message elements
      const errorElements = document.querySelectorAll('[style*="background-color: red"], [style*="color: red"]');
      errorElements.forEach(el => {
        if (el.textContent.includes('Error loading')) {
          el.style.display = 'none';
        }
      });

      // Remove the voice connection warning banner
      const warningBanner = document.getElementById('voice-connection-warning');
      if (warningBanner) {
        console.log('[DemoPage] Removing voice connection warning banner');
        warningBanner.remove();
      }
    };

    // Run immediately and set up an interval
    suppressErrorMessages();
    const interval = setInterval(suppressErrorMessages, 1000);

    // Override fetch for subdomain configs to prevent errors
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
      // If it's a subdomain config request, return empty data instead of erroring
      if (url && (typeof url === 'string') &&
          (url.includes('subdomain') || url.includes('config'))) {
        console.log('Intercepting config request to prevent errors:', url);
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({}),
          text: () => Promise.resolve("{}")
        });
      }

      // Otherwise proceed as normal
      return originalFetch(url, options);
    };

    return () => {
      clearInterval(interval);
      window.fetch = originalFetch;
    };
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 h-screen overflow-hidden">
      {/* Config Panel */}
      <div className="bg-white dark:bg-gray-900 overflow-y-auto">
        <div className="w-full mx-auto py-4">
          <div className="px-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">LegalScout Assistant Customizer</h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
              Customize the appearance of your LegalScout assistant.
            </p>
          </div>

          <div className="mt-6 px-4">
            <div className="space-y-6 w-full mx-auto">
              {/* Tabs for different settings categories */}
              <div className="w-full">
                <div className="flex flex-wrap w-full mb-4 gap-2">
                  <button
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      currentTab === 'general' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                    onClick={() => setCurrentTab('general')}
                  >
                    General
                  </button>
                  <button
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      currentTab === 'colors' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                    onClick={() => setCurrentTab('colors')}
                  >
                    Colors
                  </button>
                  <button
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      currentTab === 'content' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                    onClick={() => setCurrentTab('content')}
                  >
                    Content
                  </button>
                  <button
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      currentTab === 'settings' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                    onClick={() => setCurrentTab('settings')}
                  >
                    Settings
                  </button>
                </div>
              </div>

              {/* Settings content based on active tab */}
              {currentTab === 'general' && (
                <div className="space-y-4">
                  <div>
                    <label className="block mb-2 font-medium">Firm Name</label>
                    <input
                      type="text"
                      value={customizations.firmName}
                      onChange={(e) => setCustomizations(prev => ({
                        ...prev,
                        firmName: e.target.value,
                      }))}
                      className="w-full p-2 border rounded"
                    />
                  </div>

                  <div>
                    <label className="block mb-2 font-medium">Attorney Name</label>
                    <input
                      type="text"
                      value={customizations.attorneyName}
                      onChange={(e) => setCustomizations(prev => ({
                        ...prev,
                        attorneyName: e.target.value,
                      }))}
                      className="w-full p-2 border rounded"
                    />
                  </div>

                  <div>
                    <label className="block mb-2 font-medium">State</label>
                    <select
                      value={customizations.state}
                      onChange={(e) => setCustomizations(prev => ({
                        ...prev,
                        state: e.target.value,
                      }))}
                      className="w-full p-2 border rounded"
                    >
                      <option value="">Select a state</option>
                      {usStates.map(state => (
                        <option key={state.value} value={state.value}>
                          {state.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block mb-2 font-medium">Practice Areas</label>
                    <PracticeAreasSelect
                      value={customizations.practiceAreas}
                      onChange={(value) => setCustomizations(prev => ({
                        ...prev,
                        practiceAreas: value,
                      }))}
                      className="w-full"
                    />
                  </div>
                </div>
              )}

              {/* Appearance Settings */}
              {currentTab === 'colors' && (
                <div className="space-y-4">
                  <div>
                    <label className="block mb-2 font-medium">Colors</label>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <label className="block text-sm mb-1">Primary Color</label>
                        <div className="flex items-center gap-2">
                          <input
                            type="color"
                            value={customizations.templateColors.primary}
                            onChange={(e) => setCustomizations(prev => ({
                              ...prev,
                              templateColors: {
                                ...prev.templateColors,
                                primary: e.target.value,
                              },
                            }))}
                            className="w-10 h-10 rounded cursor-pointer border"
                          />
                          <input
                            type="text"
                            value={customizations.templateColors.primary}
                            onChange={(e) => setCustomizations(prev => ({
                              ...prev,
                              templateColors: {
                                ...prev.templateColors,
                                primary: e.target.value,
                              },
                            }))}
                            className="flex-1 p-2 border rounded"
                          />
                        </div>
                      </div>
                      <div className="flex-1">
                        <label className="block text-sm mb-1">Secondary Color</label>
                        <div className="flex items-center gap-2">
                          <input
                            type="color"
                            value={customizations.templateColors.secondary}
                            onChange={(e) => setCustomizations(prev => ({
                              ...prev,
                              templateColors: {
                                ...prev.templateColors,
                                secondary: e.target.value,
                              },
                            }))}
                            className="w-10 h-10 rounded cursor-pointer border"
                          />
                          <input
                            type="text"
                            value={customizations.templateColors.secondary}
                            onChange={(e) => setCustomizations(prev => ({
                              ...prev,
                              templateColors: {
                                ...prev.templateColors,
                                secondary: e.target.value,
                              },
                            }))}
                            className="flex-1 p-2 border rounded"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="backgroundColor" className="block text-sm font-medium mb-1">
                      Background Color
                    </label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        id="backgroundColor"
                        value={customizations.backgroundColor || '#1a1a1a'}
                        onChange={(e) => setCustomizations(prev => ({
                          ...prev,
                          backgroundColor: e.target.value
                        }))}
                        className="w-10 h-10 rounded cursor-pointer border"
                      />
                      <input
                        type="text"
                        value={customizations.backgroundColor || '#1a1a1a'}
                        onChange={(e) => setCustomizations(prev => ({
                          ...prev,
                          backgroundColor: e.target.value
                        }))}
                        className="flex-1 p-2 border rounded"
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="textBackgroundColor" className="block text-sm font-medium mb-1">
                      Text Background Color
                    </label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        id="textBackgroundColor"
                        value={customizations.textBackgroundColor || '#634C38'}
                        onChange={(e) => setCustomizations(prev => ({
                          ...prev,
                          textBackgroundColor: e.target.value
                        }))}
                        className="w-10 h-10 rounded cursor-pointer border"
                      />
                      <input
                        type="text"
                        value={customizations.textBackgroundColor || '#634C38'}
                        onChange={(e) => setCustomizations(prev => ({
                          ...prev,
                          textBackgroundColor: e.target.value
                        }))}
                        className="flex-1 p-2 border rounded"
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="backgroundOpacity" className="block text-sm font-medium mb-1">
                      Background Transparency
                    </label>
                    <div className="flex items-center">
                      <input
                        type="range"
                        id="backgroundOpacity"
                        min="0.1"
                        max="1"
                        step="0.1"
                        value={customizations.backgroundOpacity || 0.9}
                        onChange={(e) => setCustomizations(prev => ({
                          ...prev,
                          backgroundOpacity: parseFloat(e.target.value)
                        }))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <span className="ml-2 text-sm">
                        {Math.round((customizations.backgroundOpacity || 0.9) * 100)}%
                      </span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="theme" className="text-gray-700 font-medium mb-2 block">
                      Interface Theme
                    </label>
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => {
                          setData({ ...data, theme: 'dark' });
                          // If iframe is loaded, reload it to apply changes
                          if (iframeReady) {
                            reloadIframe();
                          }
                        }}
                        className={`px-4 py-2 rounded ${
                          data.theme === 'dark'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700'
                        }`}
                      >
                        Dark Mode
                      </button>
                      <button
                        onClick={() => {
                          setData({ ...data, theme: 'light' });
                          // If iframe is loaded, reload it to apply changes
                          if (iframeReady) {
                            reloadIframe();
                          }
                        }}
                        className={`px-4 py-2 rounded ${
                          data.theme === 'light'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700'
                        }`}
                      >
                        Light Mode
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Content Settings */}
              {currentTab === 'content' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Welcome Message
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      rows="3"
                      placeholder="Custom welcome message"
                      value={customizations.welcomeMessage}
                      onChange={(e) => setCustomizations(prev => ({
                        ...prev,
                        welcomeMessage: e.target.value,
                      }))}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Information Gathering
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      rows="3"
                      placeholder="Information gathering questions"
                      value={customizations.informationGathering}
                      onChange={(e) => setCustomizations(prev => ({
                        ...prev,
                        informationGathering: e.target.value,
                      }))}
                    />
                  </div>
                </div>
              )}

              {/* Settings Tab */}
              {currentTab === 'settings' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Theme
                    </label>
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => setData({ ...data, theme: 'light' })}
                        className={`px-4 py-2 rounded-md ${
                          data.theme === 'light'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                        }`}
                      >
                        Light Mode
                      </button>
                      <button
                        onClick={() => setData({ ...data, theme: 'dark' })}
                        className={`px-4 py-2 rounded-md ${
                          data.theme === 'dark'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                        }`}
                      >
                        Dark Mode
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Display Options
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="showLogo"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          checked={customizations.showLogo}
                          onChange={(e) => setCustomizations(prev => ({
                            ...prev,
                            showLogo: e.target.checked,
                          }))}
                        />
                        <label htmlFor="showLogo" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Show Logo
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Update Preview Button */}
              <div className="pt-4">
                <button
                  onClick={handlePreviewChanges}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Update Preview
                </button>
              </div>

              {/* Save to Supabase Button */}
              <div className="pt-4">
                <button
                  onClick={saveToSupabase}
                  className="w-full px-4 py-3 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Save Configuration
                </button>
              </div>

              {/* Add extra padding at the bottom for scrolling */}
              <div className="h-20"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Panel - Fixed at 50% with full height */}
      <div className="h-screen relative bg-gray-100 dark:bg-gray-800 overflow-hidden">
        <div className="p-4 text-gray-900 dark:text-white flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold">Live Preview</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setData({ ...data, theme: data.theme === 'light' ? 'dark' : 'light' })}
              className="px-3 py-1 rounded-md bg-blue-600 text-white text-sm"
            >
              {data.theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}
            </button>
            <button
              onClick={reloadIframe}
              className="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white text-sm"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Error message container */}
        <div id="iframe-error" className="hidden absolute inset-0 flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-900">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-lg w-full text-center">
            <p className="text-red-700 dark:text-red-400 font-medium">Failed to load the preview</p>
            <p className="text-red-600 dark:text-red-300 mt-1 text-sm">
              There was an error loading the preview. Please try refreshing.
            </p>
            <button
              onClick={reloadIframe}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
            >
              Reload Preview
            </button>
          </div>
        </div>

        {/* Iframe container - takes full height and width */}
        <div
          id="iframe-container"
          className="absolute inset-0 top-[57px] w-full h-[calc(100%-57px)]"
        ></div>
      </div>
    </div>
  );
};

export default DemoPage;