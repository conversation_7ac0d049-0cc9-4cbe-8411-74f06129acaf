-- Create call_records table to store Vapi call data
CREATE TABLE IF NOT EXISTS call_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  call_id TEXT NOT NULL UNIQUE,
  assistant_id TEXT NOT NULL,
  customer_phone TEXT,
  status TEXT NOT NULL,
  duration INTEGER,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  transcripts JSONB DEFAULT '[]'::JSONB,
  messages JSONB DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
  tool_executions JSONB DEFAULT '[]'::JSO<PERSON><PERSON>,
  metadata JSONB DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index on call_id for faster lookups
CREATE INDEX IF NOT EXISTS call_records_call_id_idx ON call_records (call_id);

-- Add index on assistant_id for filtering by assistant
CREATE INDEX IF NOT EXISTS call_records_assistant_id_idx ON call_records (assistant_id);

-- Add RLS policies for call_records table
ALTER TABLE call_records ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view their own call records
CREATE POLICY "Users can view their own call records" ON call_records
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM attorneys
      WHERE attorneys.id = auth.uid()
      AND attorneys.vapi_assistant_id = call_records.assistant_id
    )
  );

-- Create policy to allow service role to insert call records
CREATE POLICY "Service role can insert call records" ON call_records
  FOR INSERT
  WITH CHECK (auth.role() = 'service_role');

-- Create policy to allow service role to update call records
CREATE POLICY "Service role can update call records" ON call_records
  FOR UPDATE
  USING (auth.role() = 'service_role');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_call_records_updated_at
BEFORE UPDATE ON call_records
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
