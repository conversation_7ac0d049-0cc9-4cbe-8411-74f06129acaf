// COMPREHENSIVE DIAGNOSTIC: Data Collection → Briefs Flow
window.diagDataFlow = function() {
  console.log('🔍 COMPREHENSIVE DIAGNOSTIC: Data Collection → Briefs Flow');
  console.log('='.repeat(60));
  
  // Step 1: Check Data Collection tab state
  console.log('📋 STEP 1: Data Collection Tab State...');
  
  const agentTab = Array.from(document.querySelectorAll('button')).find(btn => 
    btn.textContent?.trim() === 'Agent'
  );
  
  if (agentTab) {
    agentTab.click();
    
    setTimeout(() => {
      const dataTab = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('Data Collection')
      );
      
      if (dataTab) {
        dataTab.click();
        
        setTimeout(() => {
          console.log('✅ In Data Collection tab');
          
          // Check current custom fields in the form
          const customFieldsContainer = document.querySelector('.custom-fields-tab');
          if (customFieldsContainer) {
            const customFieldRows = customFieldsContainer.querySelectorAll('.custom-field-row');
            console.log(`📊 CUSTOM FIELDS IN FORM: ${customFieldRows.length}`);
            
            customFieldRows.forEach((row, i) => {
              const nameInput = row.querySelector('input[id*="fieldName"]');
              const descInput = row.querySelector('input[id*="fieldDescription"]');
              const typeSelect = row.querySelector('select[id*="fieldType"]');
              const requiredCheck = row.querySelector('input[id*="fieldRequired"]');
              
              console.log(`Field ${i + 1}:`);
              console.log(`  - Name: "${nameInput?.value || 'EMPTY'}"`);
              console.log(`  - Description: "${descInput?.value || 'EMPTY'}"`);
              console.log(`  - Type: "${typeSelect?.value || 'EMPTY'}"`);
              console.log(`  - Required: ${requiredCheck?.checked || false}`);
            });
          }
          
          // Test template application
          console.log('\n🎯 TESTING TEMPLATE APPLICATION...');
          
          const practiceSelect = document.getElementById('practiceAreaTemplate');
          const applyBtn = Array.from(document.querySelectorAll('button')).find(btn => 
            btn.textContent?.includes('Apply') && btn.textContent?.includes('Template')
          );
          
          if (practiceSelect && applyBtn) {
            console.log('✅ Template controls found');
            
            // Apply Personal Injury template
            practiceSelect.value = 'Personal Injury';
            practiceSelect.dispatchEvent(new Event('change', { bubbles: true }));
            
            setTimeout(() => {
              applyBtn.click();
              console.log('✅ Applied Personal Injury template');
              
              setTimeout(() => {
                // Check fields after template
                const updatedRows = document.querySelectorAll('.custom-field-row');
                console.log(`📊 CUSTOM FIELDS AFTER TEMPLATE: ${updatedRows.length}`);
                
                updatedRows.forEach((row, i) => {
                  const nameInput = row.querySelector('input[id*="fieldName"]');
                  const descInput = row.querySelector('input[id*="fieldDescription"]');
                  
                  console.log(`Field ${i + 1}:`);
                  console.log(`  - Name: "${nameInput?.value || 'EMPTY'}"`);
                  console.log(`  - Description: "${descInput?.value || 'EMPTY'}"`);
                });
                
                // Test save functionality
                console.log('\n💾 TESTING SAVE FUNCTIONALITY...');
                
                const saveBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                  btn.textContent?.includes('Save Changes')
                );
                
                if (saveBtn) {
                  saveBtn.click();
                  console.log('✅ Clicked Save Changes');
                  
                  setTimeout(() => {
                    // Check for success message
                    const successMsg = document.querySelector('.success-message');
                    const errorMsg = document.querySelector('.error-message');
                    
                    if (successMsg) {
                      console.log('✅ Save successful:', successMsg.textContent);
                    } else if (errorMsg) {
                      console.log('❌ Save failed:', errorMsg.textContent);
                    }
                    
                    // Step 2: Check Supabase data after save
                    setTimeout(() => {
                      checkSupabaseAfterSave();
                    }, 1000);
                    
                  }, 2000);
                } else {
                  console.log('❌ Save button not found');
                }
                
              }, 1000);
            }, 500);
          } else {
            console.log('❌ Template controls not found');
          }
          
        }, 1000);
      }
    }, 500);
  }
};

// Function to check Supabase data after save
async function checkSupabaseAfterSave() {
  console.log('\n📋 STEP 2: Checking Supabase After Save...');
  
  const attorney = window.standaloneAttorneyManager?.attorney;
  if (!attorney?.id) {
    console.log('❌ No attorney ID available');
    return;
  }
  
  try {
    const { data, error } = await window.supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorney.id)
      .single();
    
    if (error) {
      console.log('❌ Supabase query error:', error);
      return;
    }
    
    console.log('📊 SUPABASE DATA AFTER SAVE:');
    console.log('- Has custom_fields:', !!data.custom_fields);
    console.log('- custom_fields type:', typeof data.custom_fields);
    
    if (data.custom_fields) {
      try {
        const fields = Array.isArray(data.custom_fields) ? 
          data.custom_fields : 
          JSON.parse(data.custom_fields);
        
        console.log(`- Number of fields: ${fields.length}`);
        fields.forEach((field, i) => {
          console.log(`  ${i + 1}. ${field.name} (${field.type}) - ${field.description}`);
        });
      } catch (parseError) {
        console.log('❌ Error parsing custom fields:', parseError);
        console.log('- Raw custom_fields:', data.custom_fields);
      }
    }
    
    // Step 3: Check Briefs tab
    setTimeout(() => {
      checkBriefsTab(data);
    }, 1000);
    
  } catch (error) {
    console.log('❌ Error checking Supabase:', error);
  }
}

// Function to check Briefs tab
function checkBriefsTab(attorneyData) {
  console.log('\n📋 STEP 3: Checking Briefs Tab...');
  
  const briefsTab = Array.from(document.querySelectorAll('button')).find(btn => 
    btn.textContent?.trim() === 'Briefs'
  );
  
  if (briefsTab) {
    briefsTab.click();
    console.log('✅ Navigated to Briefs tab');
    
    setTimeout(() => {
      // Check table headers
      const table = document.querySelector('table');
      if (table) {
        const headers = table.querySelectorAll('th');
        console.log(`📊 TABLE HEADERS (${headers.length}):`);
        headers.forEach((header, i) => {
          console.log(`${i + 1}. "${header.textContent?.trim()}"`);
        });
        
        // Check for custom field headers specifically
        const customFieldHeaders = Array.from(headers).filter(header => 
          header.querySelector('.field-source-badge') || 
          header.textContent?.includes('Custom Field')
        );
        
        console.log(`📊 CUSTOM FIELD HEADERS: ${customFieldHeaders.length}`);
        customFieldHeaders.forEach((header, i) => {
          console.log(`  ${i + 1}. "${header.textContent?.trim()}"`);
        });
        
        // Check React component props
        const briefsContainer = document.querySelector('.consultations-tab');
        if (briefsContainer) {
          const reactFiberKey = Object.keys(briefsContainer).find(key => 
            key.startsWith('__reactFiber')
          );
          
          if (reactFiberKey) {
            let currentFiber = briefsContainer[reactFiberKey];
            while (currentFiber) {
              if (currentFiber.type?.name === 'ConsultationsTab') {
                const props = currentFiber.memoizedProps;
                console.log('\n📊 CONSULTATIONSTAB PROPS:');
                console.log('- Has attorney prop:', !!props?.attorney);
                console.log('- Attorney ID matches:', props?.attorney?.id === attorneyData.id);
                console.log('- Attorney has custom_fields:', !!props?.attorney?.custom_fields);
                
                if (props?.attorney?.custom_fields) {
                  console.log('- custom_fields in props:', props.attorney.custom_fields);
                  
                  // Compare with Supabase data
                  const propsFields = Array.isArray(props.attorney.custom_fields) ? 
                    props.attorney.custom_fields : 
                    JSON.parse(props.attorney.custom_fields);
                  
                  const supabaseFields = Array.isArray(attorneyData.custom_fields) ? 
                    attorneyData.custom_fields : 
                    JSON.parse(attorneyData.custom_fields);
                  
                  console.log('- Props fields count:', propsFields.length);
                  console.log('- Supabase fields count:', supabaseFields.length);
                  console.log('- Data matches:', JSON.stringify(propsFields) === JSON.stringify(supabaseFields));
                } else {
                  console.log('❌ No custom_fields in ConsultationsTab props');
                  console.log('- This is why custom fields are not showing in table headers');
                }
                break;
              }
              currentFiber = currentFiber.return;
            }
          }
        }
        
        // Test Manage Columns modal
        console.log('\n🔧 TESTING MANAGE COLUMNS MODAL...');
        
        const manageBtn = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent?.includes('Manage Columns')
        );
        
        if (manageBtn) {
          manageBtn.click();
          console.log('✅ Clicked Manage Columns button');
          
          setTimeout(() => {
            const modal = document.querySelector('.manage-columns-modal, [class*="modal"]');
            if (modal) {
              console.log('✅ Modal opened');
              
              // Check modal content
              const columnItems = modal.querySelectorAll('[class*="column"], [class*="item"]');
              console.log(`📊 COLUMNS IN MODAL: ${columnItems.length}`);
              
              columnItems.forEach((item, i) => {
                console.log(`  ${i + 1}. "${item.textContent?.trim().substring(0, 50)}"`);
              });
              
              // Close modal
              const closeBtn = modal.querySelector('button[class*="close"], .close-button');
              if (closeBtn) {
                closeBtn.click();
                console.log('✅ Closed modal');
              }
            } else {
              console.log('❌ Modal did not open');
            }
          }, 1000);
        } else {
          console.log('❌ Manage Columns button not found');
        }
        
      } else {
        console.log('❌ No table found in Briefs tab');
      }
    }, 1500);
  }
}

// Run the comprehensive diagnostic
window.diagDataFlow();
VM5268:3 🔍 COMPREHENSIVE DIAGNOSTIC: Data Collection → Briefs Flow
VM5268:4 ============================================================
VM5268:7 📋 STEP 1: Data Collection Tab State...
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 1 new preview iframes in added content
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
undefined
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiLogger.js:103 [20:07:37] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
vapiLogger.js:103 [20:07:37] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
VM5281 emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
VM5284 critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Vapi keys set globally
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:53 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:64 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:68 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:75 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:86 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:116 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:119 🎉 [EMERGENCY] Emergency fixes complete!
VM5287 robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
VM5287 robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
VM5288 disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
VM5288 disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
VM5288 disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
VM5288 disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
VM5289 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM5289 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM5290 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM5290 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM5290 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM5290 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM5291 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM5291 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM5291 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM5292 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM5292 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM5292 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM5293 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM5293 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM5293 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM5294 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM5294 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM5294 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM5295 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM5295 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM5295 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM5295 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM5296 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM5296 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM5296 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM5296 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM5296 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM5296 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
VM5298 production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
VM5299 clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
VM5299 clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
VM5299 clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
VM5299 clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
VM5299 clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
VM5299 clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
VM5299 clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
VM5299 clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
VM5299 clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
VM5299 clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
VM5299 clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
VM5275 client:229 [vite] connecting...
VM5275 client:325 [vite] connected.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:235 Supabase loaded from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:245 Creating Supabase client from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:249 Supabase client created from CDN
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
VM5297 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
VM5299 clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
VM5299 clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM5018 SyncContext.jsx:136:24)
    at async Object.callback (VM4989 AuthContext.jsx:164:34)
    at async VM5082 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM5082 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM5082 @supabase_supabase-js.js:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM5018 SyncContext.jsx:136:24)
    at async Object.callback (VM4989 AuthContext.jsx:164:34)
    at async VM5082 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM5082 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM5082 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM5018 SyncContext.jsx:136:24)
    at async Object.callback (VM4989 AuthContext.jsx:164:34)
    at async VM5082 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM5082 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM5082 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
DashboardNew.jsx:637 [DashboardNew] Sub-tab changed to: customFields
CustomFieldsTab.jsx:34 [CustomFieldsTab] useEffect triggered with attorney: true
CustomFieldsTab.jsx:38 [CustomFieldsTab] Loading attorney data: {id: '87756a2c-a398-43f2-889a-b8815684df71', hasSummaryPrompt: true, hasStructuredPrompt: true, hasSuccessPrompt: true, hasCustomFields: true}
CustomFieldsTab.jsx:53 [CustomFieldsTab] Set custom fields: 8
CustomFieldsTab.jsx:63 [CustomFieldsTab] Set prompts: {summaryLength: 1349, structuredLength: 927, successLength: 1107}
CustomFieldsTab.jsx:76 [CustomFieldsTab] Set structured data schema
CustomFieldsTab.jsx:34 [CustomFieldsTab] useEffect triggered with attorney: true
CustomFieldsTab.jsx:38 [CustomFieldsTab] Loading attorney data: {id: '87756a2c-a398-43f2-889a-b8815684df71', hasSummaryPrompt: true, hasStructuredPrompt: true, hasSuccessPrompt: true, hasCustomFields: true}
CustomFieldsTab.jsx:53 [CustomFieldsTab] Set custom fields: 8
CustomFieldsTab.jsx:63 [CustomFieldsTab] Set prompts: {summaryLength: 1349, structuredLength: 927, successLength: 1107}
CustomFieldsTab.jsx:76 [CustomFieldsTab] Set structured data schema
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [20:07:38] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [20:07:38] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
vapiLogger.js:103 [20:07:38] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
VapiDirectApiService.js:34 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
vapiLogger.js:103 [20:07:38] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
VapiDirectApiService.js:129 ✅ [VapiDirectApiService] Using direct API data (complete)
AgentTab.jsx:504 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
DashboardNew.jsx:666 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:743 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
standalone-attorney-manager-fixed.js:478 [StandaloneAttorneyManager] Updating attorney
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:531 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
DashboardNew.jsx:746 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:38.934Z', subdomain: 'damon', firm_name: 'LegalScout', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458932}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458932}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458932}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458932}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:38.934Z', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458999}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458999}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458999}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240458999}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459003}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459003}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459003}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459003}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
 [StandaloneAttorneyManager] Updating attorney
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:39.164Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459166}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459166}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459166}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459166}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459169}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459169}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459169}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459169}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:39.164Z', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459221}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459221}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459221}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459221}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459224}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459224}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459224}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240459224}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VM5268:25 ✅ In Data Collection tab
VM5268:31 📊 CUSTOM FIELDS IN FORM: 8
VM5268:39 Field 1:
VM5268:40   - Name: "clientName"
VM5268:41   - Description: "Client's full name"
VM5268:42   - Type: "string"
VM5268:43   - Required: true
VM5268:39 Field 2:
VM5268:40   - Name: "contactPhone"
VM5268:41   - Description: "Client's phone number"
VM5268:42   - Type: "string"
VM5268:43   - Required: true
VM5268:39 Field 3:
VM5268:40   - Name: "contactEmail"
VM5268:41   - Description: "Client's email address"
VM5268:42   - Type: "string"
VM5268:43   - Required: false
VM5268:39 Field 4:
VM5268:40   - Name: "legalIssueDescription"
VM5268:41   - Description: "Brief description of the legal issue"
VM5268:42   - Type: "string"
VM5268:43   - Required: true
VM5268:39 Field 5:
VM5268:40   - Name: "urgencyLevel"
VM5268:41   - Description: "Level of urgency"
VM5268:42   - Type: "string"
VM5268:43   - Required: true
VM5268:39 Field 6:
VM5268:40   - Name: "referralSource"
VM5268:41   - Description: "How the client found your firm"
VM5268:42   - Type: "string"
VM5268:43   - Required: false
VM5268:39 Field 7:
VM5268:40   - Name: "followUpRequested"
VM5268:41   - Description: "Whether follow-up was requested"
VM5268:42   - Type: "boolean"
VM5268:43   - Required: true
VM5268:39 Field 8:
VM5268:40   - Name: "preferredContactMethod"
VM5268:41   - Description: "Client's preferred contact method"
VM5268:42   - Type: "string"
VM5268:43   - Required: false
VM5268:48 
🎯 TESTING TEMPLATE APPLICATION...
VM5268:56 ✅ Template controls found
VM5268:64 ✅ Applied Personal Injury template
VM5268:69 📊 CUSTOM FIELDS AFTER TEMPLATE: 5
VM5268:75 Field 1:
VM5268:76   - Name: "accidentDate"
VM5268:77   - Description: "Date when the accident occurred"
VM5268:75 Field 2:
VM5268:76   - Name: "injuryType"
VM5268:77   - Description: "Type of injury sustained"
VM5268:75 Field 3:
VM5268:76   - Name: "medicalTreatment"
VM5268:77   - Description: "Medical treatment received so far"
VM5268:75 Field 4:
VM5268:76   - Name: "insuranceInfo"
VM5268:77   - Description: "Client's insurance information"
VM5268:75 Field 5:
VM5268:76   - Name: "atFault"
VM5268:77   - Description: "Whether the client was at fault for the accident"
VM5268:81 
💾 TESTING SAVE FUNCTIONALITY...
CustomFieldsTab.jsx:174 Saving custom fields for attorney ID: 87756a2c-a398-43f2-889a-b8815684df71
VM5268:89 ✅ Clicked Save Changes
DashboardNew.jsx:666 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:743 [DashboardNew] Calling updateAttorney with: {theme: 'light'}
standalone-attorney-manager-fixed.js:478 [StandaloneAttorneyManager] Updating attorney
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:746 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:40.931Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240460930}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240460930}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240460930}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240460930}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:40.931Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240460995}
