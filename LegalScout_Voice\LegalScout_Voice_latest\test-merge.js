import fs from 'fs';

console.log('🔍 TESTING MERGE ANALYSIS...');

try {
  const analysis = JSON.parse(fs.readFileSync('codebase-analysis-report.json', 'utf8'));
  
  console.log('✅ Analysis loaded successfully!');
  console.log('📂 Workspace:', analysis.workspace);
  console.log('🌿 Branch:', analysis.branch);
  console.log('✅ Status:', analysis.status);
  console.log('🚀 Port:', analysis.applicationRunning?.port);
  
  console.log('\n🎯 MODERN COMPONENTS:');
  Object.entries(analysis.modernComponents).forEach(([category, components]) => {
    console.log(`\n📁 ${category.toUpperCase()}:`);
    Object.entries(components).forEach(([name, info]) => {
      console.log(`  ✅ ${name} - ${info.recommendation}`);
    });
  });
  
  console.log('\n🏗️ ARCHITECTURAL PATTERNS:');
  analysis.architecturalPatterns?.good?.forEach(pattern => {
    console.log(`  ✅ ${pattern}`);
  });
  
  console.log('\n🚫 ANTI-PATTERNS AVOIDED:');
  analysis.architecturalPatterns?.avoided?.forEach(antiPattern => {
    console.log(`  ❌ ${antiPattern}`);
  });
  
  console.log('\n💎 COMPONENTS TO KEEP:');
  analysis.recommendations?.keep?.forEach(item => {
    console.log(`  🔥 ${item.component} - ${item.reason}`);
  });
  
  console.log('\n🎯 MERGE STRATEGY:', analysis.recommendations?.merge_strategy);
  console.log('📊 CODE QUALITY:', analysis.codeQuality?.overall);
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
