const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Function to update the HTML table with subdomain data
async function updateTable() {
    const configFilePath = path.join(__dirname, 'subdomain_config.json');
    console.log(`Checking if config file exists: ${configFilePath}`);
    if (fs.existsSync(configFilePath)) {
        const fileContent = await readFile(configFilePath, 'utf8');
        const configData = JSON.parse(fileContent);
        console.log('Config data loaded:', configData);

        // Construct an HTML table
        let table = '<table><tr><th>Subdomain</th><th>Firm Name</th><th>Logo URL</th><th>Mascot URL</th><th>VAPI URL</th><th>VAPI Instructions</th><th>VAPI Context</th><th>Interaction Deposit URL</th></tr>';
        for (const subdomain in configData) {
            const data = configData[subdomain];
            table += `<tr>
                        <td>${subdomain}</td>
                        <td>${data.firmName}</td>
                        <td>${data.logo}</td>
                        <td>${data.mascot}</td>
                        <td>${data.vapi_url}</td>
                        <td>${data.vapiInstructions}</td>
                        <td>${data.vapiContext}</td>
                        <td>${data.interactionDepositUrl}</td>
                      </tr>`;
        }
        table += '</table>';

        // Write the table into an HTML file saved in the same directory
        const tableFilePath = path.join(__dirname, 'subdomain_table.html');
        await writeFile(tableFilePath, table, 'utf8');
        console.log(`Table written to: ${tableFilePath}`);
    } else {
        console.log(`Config file does not exist: ${configFilePath}`);
    }
}

module.exports = async function (context, req) {
    try {
        console.log('Received request:', req.body);
        const payload = req.body;

        // Validate required fields
        const {
            subdomain,
            logoUrl,
            mascotUrl,
            vapiUrl,
            firmName,
            vapiInstructions,
            vapiContext,
            interactionDepositUrl
        } = payload;
        if (!subdomain || !logoUrl || !firmName || !vapiInstructions || !vapiContext || !interactionDepositUrl) {
            throw new Error('Missing required attorney data in the payload.');
        }

        // Use a config file saved alongside this function
        const configFilePath = path.join(__dirname, 'subdomain_config.json');
        let configData = {};
        if (fs.existsSync(configFilePath)) {
            const fileContent = await readFile(configFilePath, 'utf8');
            configData = JSON.parse(fileContent);
            console.log('Existing config data:', configData);
        }

        // Update or create configuration for the given subdomain
        configData[subdomain] = {
            logo: logoUrl,
            mascot: mascotUrl || (configData[subdomain] && configData[subdomain].mascot) || null,
            vapi_url: vapiUrl || (configData[subdomain] && configData[subdomain].vapi_url) || null,
            firmName: firmName,
            vapiInstructions: vapiInstructions,
            vapiContext: vapiContext,
            interactionDepositUrl: interactionDepositUrl
        };

        await writeFile(configFilePath, JSON.stringify(configData, null, 2), 'utf8');
        console.log('Updated config data written to file:', configData);

        // Update the table file based on the new configuration
        await updateTable();

        context.res = {
            status: 200,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ message: "Attorney data updated successfully!" })
        };
    } catch (error) {
        console.error('Error updating attorney data:', error);
        context.res = {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ error: `Error updating attorney data: ${error.message}` })
        };
    }
};

module.exports.serveHtml = async function (context, req) {
    try {
        const tableFilePath = path.join(__dirname, 'subdomain_table.html');
        console.log(`Serving HTML file: ${tableFilePath}`);
        if (fs.existsSync(tableFilePath)) {
            const tableContent = await readFile(tableFilePath, 'utf8');
            context.res = {
                status: 200,
                headers: { 'Content-Type': 'text/html' },
                body: tableContent
            };
            console.log('HTML file served successfully');
        } else {
            context.res = {
                status: 404,
                headers: { 'Content-Type': 'text/plain' },
                body: 'HTML file not found'
            };
            console.log('HTML file not found');
        }
    } catch (error) {
        console.error('Error serving HTML file:', error);
        context.res = {
            status: 500,
            headers: { 'Content-Type': 'text/plain' },
            body: `Error serving HTML file: ${error.message}`
        };
    }
};

// Run the updateTable function upon load
updateTable().catch(console.error);
