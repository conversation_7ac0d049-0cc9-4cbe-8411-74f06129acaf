<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Vapi Integration Test</h1>

    <div class="test-section info">
        <h2>Test Environment</h2>
        <p>Hostname: <span id="hostname"></span></p>
        <p>Environment: <span id="environment"></span></p>
        <p>API Key: <span id="apiKey"></span></p>
    </div>

    <div class="test-section">
        <h2>1. Test Vapi MCP Connection</h2>
        <button onclick="testVapiConnection()">Test Connection</button>
        <div id="connectionResult"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Assistant Retrieval</h2>
        <button onclick="testGetAssistant()">Get Real Assistant</button>
        <button onclick="testGetMockAssistant()">Test Mock ID Detection</button>
        <div id="assistantResult"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Assistant Creation</h2>
        <button onclick="testCreateAssistant()">Create Assistant</button>
        <div id="createResult"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Database Update</h2>
        <button onclick="testDatabaseUpdate()">Update Database with Real ID</button>
        <div id="databaseResult"></div>
    </div>

    <div class="test-section">
        <h2>5. Test UI Mock Detection</h2>
        <button onclick="testUILogic()">Test UI Mock ID Detection</button>
        <div id="uiResult"></div>
    </div>

    <script>
        // Display environment info
        document.getElementById('hostname').textContent = window.location.hostname;
        const isProduction = window.location.hostname === 'dashboard.legalscout.net' ||
                            window.location.hostname.endsWith('.legalscout.net');
        document.getElementById('environment').textContent = isProduction ? 'Production' : 'Development';
        document.getElementById('apiKey').textContent = '6734febc-fc65-4669-93b0-929b31ff6564'.substring(0, 8) + '...';

        async function testVapiConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p>Testing connection...</p>';

            try {
                // Test direct API call
                const response = await fetch('https://api.vapi.ai/assistant?limit=1', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Connection Successful</h3>
                            <p>Status: ${response.status}</p>
                            <p>Assistants found: ${Array.isArray(data) ? data.length : 'N/A'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Connection Failed</h3>
                            <p>Status: ${response.status} ${response.statusText}</p>
                            <p>Error: ${errorText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Connection Error</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testGetAssistant() {
            const resultDiv = document.getElementById('assistantResult');
            resultDiv.innerHTML = '<p>Getting assistant...</p>';

            try {
                const assistantId = '4d7f0a46-a3b8-4400-8690-5194958128a4';
                const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Assistant Retrieved</h3>
                            <p>ID: ${data.id}</p>
                            <p>Name: ${data.name}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Failed to Get Assistant</h3>
                            <p>Status: ${response.status} ${response.statusText}</p>
                            <p>Error: ${errorText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error Getting Assistant</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testGetMockAssistant() {
            const resultDiv = document.getElementById('assistantResult');
            resultDiv.innerHTML = '<p>Testing mock ID detection...</p>';

            const mockId = 'mock-1234567890';
            const isProduction = window.location.hostname === 'dashboard.legalscout.net' ||
                                window.location.hostname.endsWith('.legalscout.net');

            if (mockId.startsWith('mock-')) {
                if (isProduction) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Mock ID Detected in Production</h3>
                            <p>Mock assistant ID "${mockId}" would be rejected in production</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="info">
                            <h3>⚠️ Mock ID Detected in Development</h3>
                            <p>Mock assistant ID "${mockId}" detected and would return null</p>
                        </div>
                    `;
                }
            }
        }

        async function testCreateAssistant() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = '<p>Creating test assistant...</p>';

            try {
                const assistantConfig = {
                    name: 'Test Assistant',
                    firstMessage: 'Hello, I am a test assistant.',
                    firstMessageMode: 'assistant-speaks-first',
                    model: {
                        provider: 'openai',
                        model: 'gpt-4o',
                        messages: [
                            {
                                role: 'system',
                                content: 'You are a test assistant.'
                            }
                        ]
                    },
                    voice: {
                        provider: '11labs',
                        voiceId: 'sarah'
                    },
                    transcriber: {
                        provider: 'deepgram',
                        model: 'nova-3'
                    }
                };

                const response = await fetch('https://api.vapi.ai/assistant', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(assistantConfig)
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Assistant Created</h3>
                            <p>ID: ${data.id}</p>
                            <p>Name: ${data.name}</p>
                            <p>Is Mock: ${data.id && data.id.startsWith('mock-') ? 'Yes' : 'No'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Failed to Create Assistant</h3>
                            <p>Status: ${response.status} ${response.statusText}</p>
                            <p>Error: ${errorText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error Creating Assistant</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testDatabaseUpdate() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<p>Testing database update...</p>';

            // This would normally require backend API, showing concept
            resultDiv.innerHTML = `
                <div class="info">
                    <h3>ℹ️ Database Update Test</h3>
                    <p>In a real application, this would:</p>
                    <ul>
                        <li>Update the attorney record with real assistant ID: 4d7f0a46-a3b8-4400-8690-5194958128a4</li>
                        <li>Prevent automatic sync from overriding with mock IDs</li>
                        <li>Ensure UI shows proper assistant configuration</li>
                    </ul>
                    <p><strong>Current Issue:</strong> Realtime sync is overriding manual updates</p>
                    <p><strong>Solution:</strong> Mock ID detection logic prevents saving mock IDs to database</p>
                </div>
            `;
        }

        async function testUILogic() {
            const resultDiv = document.getElementById('uiResult');
            resultDiv.innerHTML = '<p>Testing UI logic...</p>';

            // Test the UI logic for mock ID detection
            const testCases = [
                { id: null, expected: 'Show "Create Assistant"' },
                { id: undefined, expected: 'Show "Create Assistant"' },
                { id: '', expected: 'Show "Create Assistant"' },
                { id: 'mock-1234567890', expected: 'Show "Fix Assistant" (Mock detected)' },
                { id: '4d7f0a46-a3b8-4400-8690-5194958128a4', expected: 'Show assistant configuration' }
            ];

            let results = '<div class="success"><h3>✅ UI Logic Test Results</h3>';

            testCases.forEach(testCase => {
                const isValidAssistantId = (assistantId) => {
                    return assistantId &&
                           assistantId !== 'null' &&
                           assistantId !== 'undefined' &&
                           !assistantId.startsWith('mock-');
                };

                const shouldShowCreate = !isValidAssistantId(testCase.id);
                const isMockId = testCase.id && testCase.id.startsWith('mock-');

                let actualResult;
                if (shouldShowCreate) {
                    actualResult = isMockId ? 'Show "Fix Assistant" (Mock detected)' : 'Show "Create Assistant"';
                } else {
                    actualResult = 'Show assistant configuration';
                }

                const isCorrect = actualResult === testCase.expected;

                results += `
                    <p style="color: ${isCorrect ? 'green' : 'red'}">
                        ${isCorrect ? '✅' : '❌'} ID: "${testCase.id}" → ${actualResult}
                    </p>
                `;
            });

            results += '</div>';
            resultDiv.innerHTML = results;
        }
    </script>
</body>
</html>
