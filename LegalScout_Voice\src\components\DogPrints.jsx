import React, { useEffect, useRef, useState } from 'react';
import './DogPrints.css';
import DogHouse from './DogHouse';
import Bone from './Bone';

const DogPrints = ({ isDarkTheme }) => {
  const containerRef = useRef(null);
  const pawsRef = useRef([]);
  const timeoutRef = useRef(null);
  const animationFrameRef = useRef(null);
  const pawIntervalRef = useRef(null);
  const boneAnimationRef = useRef(null);
  
  // Click interaction states
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [dogHouseVisible, setDogHouseVisible] = useState(false);
  const [dogHousePosition, setDogHousePosition] = useState({ x: 0, y: 0 });
  const [boneActive, setBoneActive] = useState(false);
  const [bonePosition, setBonePosition] = useState({ x: 0, y: 0 });
  const [boneRotation, setBoneRotation] = useState(0);
  const [clickFeedback, setClickFeedback] = useState(false);
  const [boneThrowStrength, setBoneThrowStrength] = useState(0);
  
  // Scout's position and movement variables
  const scoutRef = useRef({
    position: { x: 0, y: 0 },
    startPoint: { x: 0, y: 0 },
    endPoint: { x: 0, y: 0 },
    direction: 'right', // 'left', 'right' - used for orientation
    pathPoints: [], // Array of waypoints for Scout to follow
    currentPathIndex: 0,
    velocity: { x: 0, y: 0 },
    isWalking: false,
    pawStep: 0,
    nextPawTime: 0,
    lastPathChangeTime: 0,
    pawStride: 30, // Distance between paw prints (px)
    walkSpeed: 1.5, // Pixels per animation frame
    turnRadius: 40 // Min radius for direction changes (for smoothness)
  });
  
  // Settings
  const maxVisiblePaws = 4; // Limited paws for clean appearance
  const pawLifetime = 6000; // How long paws remain visible (ms)
  const walkProbability = 0.15; // Chance of Scout starting to walk
  const walkCheckInterval = 7000; // Interval to check if Scout should walk (ms)
  const pathUpdateInterval = 700; // How often to potentially update direction
  const pathUpdateChance = 0.3; // Chance of changing path at each update
  const logoAreaWidth = 300; // Width of the logo area to avoid (increased from 250)
  const logoAreaHeight = 80; // Height of the logo area to avoid (increased from 60)
  const logoAreaMargin = 20; // Additional safety margin around logo area
  
  // Color settings
  const getLightModeColor = () => '#333';
  const getDarkModeColor = () => '#ddd';
  const getPawColor = () => isDarkTheme ? getDarkModeColor() : getLightModeColor();

  // Function to check if an element is in or near the logo area
  const isInLogoArea = (element) => {
    const rect = element.getBoundingClientRect();
    return (rect.left < (logoAreaWidth + logoAreaMargin) && 
            rect.top < (logoAreaHeight + logoAreaMargin));
  };

  // Enhanced cleanup function
  const forceCleanContainer = () => {
    const container = containerRef.current;
    if (!container) return;
    
    // Clean up all paw elements
    const allPaws = document.querySelectorAll('.dog-paw');
    allPaws.forEach(paw => {
      // Force immediate removal if in logo area
      if (isInLogoArea(paw)) {
        if (paw.parentNode) {
          paw.parentNode.removeChild(paw);
        }
      } else {
        // Normal fade out for others
        paw.classList.add('fade-out');
        paw.style.opacity = "0";
        setTimeout(() => {
          if (paw.parentNode) {
            paw.parentNode.removeChild(paw);
          }
        }, 100);
      }
    });
    
    // Clean up any stray toe elements
    const allToes = document.querySelectorAll('[class*="toe"]');
    allToes.forEach(toe => {
      if (toe.parentNode) {
        toe.parentNode.removeChild(toe);
      }
    });

    // Additional cleanup for any elements with specific classes
    const cleanupSelectors = [
      '.dog-paw',
      '.toe',
      '.toe-1',
      '.toe-2',
      '.toe-3',
      '.left-paw',
      '.right-paw'
    ];

    cleanupSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
    });
    
    // Reset the array
    pawsRef.current = [];
  };

  // Track mouse position
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const handleMouseMove = (e) => {
      const rect = container.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // Update throw strength based on mouse distance from doghouse
      if (dogHouseVisible) {
        const dx = e.clientX - rect.left - dogHousePosition.x;
        const dy = e.clientY - rect.top - dogHousePosition.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        setBoneThrowStrength(Math.min(1, distance / 200));
      }
    };
    
    const handleContainerClick = (e) => {
      if (e.target === container && scoutRef.current.isWalking) {
        setClickFeedback(true);
        setTimeout(() => setClickFeedback(false), 300);
        
        setDogHousePosition({
          x: scoutRef.current.position.x,
          y: scoutRef.current.position.y
        });
        setDogHouseVisible(true);
        scoutRef.current.isWalking = false;
      }
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('click', handleContainerClick);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('click', handleContainerClick);
    };
  }, [dogHouseVisible]);
  
  // Enhanced health check interval
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    // Clean up EVERYTHING at component initialization
    forceCleanContainer();
    
    // More frequent health checks
    const healthCheckInterval = setInterval(() => {
      // Check for any elements in the logo area
      const elementsNearLogo = document.querySelectorAll('.dog-paw, [class*="toe"]');
      elementsNearLogo.forEach(element => {
        if (isInLogoArea(element)) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      });

      // Filter out invalid paws from tracking array
      pawsRef.current = pawsRef.current.filter(paw => 
        paw.element && 
        document.body.contains(paw.element) && 
        !isInLogoArea(paw.element)
      );

      // Look for any stray elements
      const allElements = document.querySelectorAll('.dog-paw, [class*="toe"]');
      allElements.forEach(element => {
        const pawId = element.dataset.pawId;
        const isTracked = pawId && pawsRef.current.some(p => p.id === pawId);
        const pawCreationTime = parseInt(pawId?.split('-')[0] || '0');
        const isPawTooOld = Date.now() - pawCreationTime > (pawLifetime * 1.2);
        
        if (!isTracked || isPawTooOld || isInLogoArea(element)) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      });
    }, 500); // More frequent checks (reduced from 1000ms)

    // More frequent emergency cleanup
    const emergencyCleanupInterval = setInterval(() => {
      forceCleanContainer();
    }, 10000); // More frequent (reduced from 20000ms)

    return () => {
      clearInterval(healthCheckInterval);
      clearInterval(emergencyCleanupInterval);
      forceCleanContainer();
    };
  }, [isDarkTheme]);
  
  // Modify startNewWalk to be more cautious around logo area
  const startNewWalk = () => {
    const container = containerRef.current;
    if (!container || scoutRef.current.isWalking || dogHouseVisible) return;
    
    // Aggressive cleanup before starting new walk
    forceCleanContainer();
    
    const { width, height } = container.getBoundingClientRect();
    const { walkSpeed, pawStride } = scoutRef.current;
    
    // Choose entry and exit sides (0=left, 1=right)
    const entrySide = Math.random() > 0.5 ? 0 : 1;
    const direction = entrySide === 0 ? 'right' : 'left';
    
    // Calculate Y position (stay in middle 60% vertically)
    const yPosition = height * (0.2 + Math.random() * 0.6);
    
    // Define start and end points
    const startX = entrySide === 0 ? -10 : width + 10;
    const endX = entrySide === 0 ? width + 10 : -10;
    
    // Generate a nice path with waypoints
    const pathPoints = generatePath(
      { x: startX, y: yPosition },
      { x: endX, y: yPosition },
      width, height
    );
    
    // Reset Scout's state
    scoutRef.current = {
      ...scoutRef.current,
      position: { x: startX, y: yPosition },
      startPoint: { x: startX, y: yPosition },
      endPoint: { x: endX, y: yPosition },
      direction,
      pathPoints,
      currentPathIndex: 0,
      velocity: { 
        x: direction === 'right' ? walkSpeed : -walkSpeed,
        y: 0
      },
      isWalking: true,
      pawStep: 0,
      nextPawTime: 0,
      lastPathChangeTime: 0
    };
    
    // Remove any existing paws
    removeAllPaws();
  };
  
  // Generate a smooth, natural-looking path for Scout
  const generatePath = (start, end, width, height) => {
    const points = [{ ...start }];
    const totalDistance = Math.abs(end.x - start.x);
    const numWaypoints = Math.floor(2 + Math.random() * 3); // 2-4 waypoints
    
    // Add waypoints with smooth ups and downs
    for (let i = 1; i <= numWaypoints; i++) {
      const progressRatio = i / (numWaypoints + 1);
      
      // X position progresses linearly from start to end
      const x = start.x + (end.x - start.x) * progressRatio;
      
      // Y position varies within a band to create a natural path
      // Create alternating up/down pattern
      const yVariation = (i % 2 === 0 ? -1 : 1) * Math.random() * (height * 0.1);
      const y = start.y + yVariation;
      
      points.push({ x, y });
    }
    
    // Add end point
    points.push({ ...end });
    
    return points;
  };
  
  // Update Scout's position and create paw prints
  const updateScoutPosition = (timestamp) => {
    const scout = scoutRef.current;
    if (!scout.isWalking) return;
    
    const container = containerRef.current;
    if (!container) return;
    
    const { width, height } = container.getBoundingClientRect();
    const { walkSpeed, pawStride, turnRadius } = scout;
    
    // Check if we need to move to next waypoint
    if (scout.currentPathIndex < scout.pathPoints.length - 1) {
      const currentTarget = scout.pathPoints[scout.currentPathIndex + 1];
      const dx = currentTarget.x - scout.position.x;
      const dy = currentTarget.y - scout.position.y;
      const distanceToTarget = Math.sqrt(dx * dx + dy * dy);
      
      // If close enough to current waypoint, move to next
      if (distanceToTarget < turnRadius) {
        scout.currentPathIndex++;
      }
    }
    
    // Get current target waypoint
    const targetPoint = scout.pathPoints[Math.min(scout.currentPathIndex + 1, scout.pathPoints.length - 1)];
    
    // Calculate movement vector toward target
    const dx = targetPoint.x - scout.position.x;
    const dy = targetPoint.y - scout.position.y;
    const distanceToTarget = Math.sqrt(dx * dx + dy * dy);
    
    // Normalize vector and apply speed
    let vx = 0, vy = 0;
    if (distanceToTarget > 0) {
      vx = (dx / distanceToTarget) * walkSpeed;
      vy = (dy / distanceToTarget) * walkSpeed;
    }
    
    // Update velocity with some smoothing
    scout.velocity.x = scout.velocity.x * 0.9 + vx * 0.1;
    scout.velocity.y = scout.velocity.y * 0.9 + vy * 0.1;
    
    // Update position
    scout.position.x += scout.velocity.x;
    scout.position.y += scout.velocity.y;
    
    // Update direction based on horizontal velocity
    if (scout.velocity.x > 0.1) {
      scout.direction = 'right';
    } else if (scout.velocity.x < -0.1) {
      scout.direction = 'left';
    }
    
    // Create paw prints with proper timing
    const distanceTraveled = Math.sqrt(scout.velocity.x * scout.velocity.x + scout.velocity.y * scout.velocity.y);
    
    if (timestamp >= scout.nextPawTime && distanceTraveled > 0) {
      // Time for a new paw, alternating left and right
      const isRightPaw = scout.pawStep % 2 === 0;
      createPaw(
        scout.position, 
        { x: scout.velocity.x, y: scout.velocity.y }, 
        isRightPaw
      );
      
      // Schedule next paw print
      scout.pawStep++;
      
      // Time between paws depends on speed
      const pawTimingGap = 300; // ms between paws
      scout.nextPawTime = timestamp + pawTimingGap;
    }
    
    // Check if Scout has reached the end
    const isOffScreen = 
      scout.position.x < -20 || 
      scout.position.x > width + 20 || 
      scout.position.y < -20 || 
      scout.position.y > height + 20;
      
    if (isOffScreen && scout.currentPathIndex >= scout.pathPoints.length - 1) {
      scout.isWalking = false;
    }
  };
  
  // Create a new paw print with proper orientation
  const createPaw = (position, velocity, isRightPaw) => {
    const container = containerRef.current;
    if (!container) return;
    
    // Check if the paw would be in the logo area - if so, don't create it
    if (position.x < logoAreaWidth && position.y < logoAreaHeight) {
      return null;
    }
    
    // Calculate angle based on velocity
    const angle = Math.atan2(velocity.y, velocity.x) * (180 / Math.PI);
    
    // Create paw element
    const pawElement = document.createElement('div');
    pawElement.className = `dog-paw ${isRightPaw ? 'right-paw' : 'left-paw'}`;
    pawElement.style.backgroundColor = getPawColor();
    pawElement.dataset.pawId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    
    // Apply offset based on whether it's a right or left paw
    // This creates a realistic walking pattern with separation between right and left paws
    const perpOffset = 5; // Perpendicular offset for right/left alternation
    
    // Calculate perpendicular vector
    const perpVectorX = -velocity.y;
    const perpVectorY = velocity.x;
    
    // Normalize perpendicular vector
    const perpLength = Math.sqrt(perpVectorX * perpVectorX + perpVectorY * perpVectorY);
    const normalizedPerpX = perpLength > 0 ? perpVectorX / perpLength : 0;
    const normalizedPerpY = perpLength > 0 ? perpVectorY / perpLength : 0;
    
    // Apply offset in perpendicular direction (opposite for right/left)
    const offsetSign = isRightPaw ? 1 : -1;
    const offsetX = normalizedPerpX * perpOffset * offsetSign;
    const offsetY = normalizedPerpY * perpOffset * offsetSign;
    
    // Final position with offset
    const finalX = position.x + offsetX;
    const finalY = position.y + offsetY;
    
    // Double-check again if the final position would be in the logo area - if so, don't create it
    if (finalX < logoAreaWidth && finalY < logoAreaHeight) {
      return null;
    }
    
    pawElement.style.left = `${finalX}px`;
    pawElement.style.top = `${finalY}px`;
    
    // Apply correct rotation based on movement direction
    // Add 90 degrees to point paw in the direction of movement
    pawElement.style.transform = `rotate(${angle + 90}deg)`;
    
    // Add toes to the paw
    for (let i = 1; i <= 3; i++) {
      const toe = document.createElement('div');
      toe.className = `toe toe-${i}`;
      toe.style.backgroundColor = getPawColor();
      pawElement.appendChild(toe);
    }
    
    // Add paw to container
    container.appendChild(pawElement);
    
    // Store reference to the paw
    pawsRef.current.push({
      element: pawElement,
      id: pawElement.dataset.pawId,
      createdAt: Date.now()
    });
    
    // Limit number of visible paws
    if (pawsRef.current.length > maxVisiblePaws) {
      removePaw(0); // Remove oldest paw
    }
    
    // Schedule removal of this paw after lifetime
    const timeoutId = setTimeout(() => {
      const index = pawsRef.current.findIndex(p => p.id === pawElement.dataset.pawId);
      if (index !== -1) {
        removePaw(index);
      } else {
        // Fallback cleanup - direct DOM removal for reliability
        if (pawElement.parentNode) {
          pawElement.parentNode.removeChild(pawElement);
        }
      }
    }, pawLifetime);
    
    // Store the timeout ID for cleanup
    return timeoutId;
  };
  
  // Remove a paw by index - modified for direct DOM removal
  const removePaw = (index) => {
    if (index < 0 || index >= pawsRef.current.length) return;
    
    const paw = pawsRef.current[index];
    if (!paw) return;
    
    // Direct DOM removal for reliability
    if (paw.element && paw.element.parentNode) {
      paw.element.parentNode.removeChild(paw.element);
    }
    
    // Remove reference from array immediately
    pawsRef.current.splice(index, 1);
  };
  
  // Remove all paws - simplified for reliability
  const removeAllPaws = () => {
    forceCleanContainer();
  };

  // Add a safety cleanup function for component unmounting
  useEffect(() => {
    return () => {
      removeAllPaws();
    };
  }, []);

  // Handle bone throw from doghouse
  const handleThrowBone = () => {
    // Cancel any existing bone animation
    if (boneAnimationRef.current) {
      cancelAnimationFrame(boneAnimationRef.current);
    }
    
    // Starting position is at the dog house
    const startPosition = { ...dogHousePosition };
    
    // Calculate direction vector from doghouse to mouse
    const throwVector = {
      x: mousePosition.x - dogHousePosition.x,
      y: mousePosition.y - dogHousePosition.y
    };
    
    // Normalize throw vector
    const throwDistance = Math.sqrt(
      Math.pow(throwVector.x, 2) + Math.pow(throwVector.y, 2)
    );
    
    const normalizedThrowVector = {
      x: throwVector.x / throwDistance,
      y: throwVector.y / throwDistance
    };
    
    // Apply throw strength for longer throw
    const strengthMultiplier = 0.8 + (boneThrowStrength * 0.7);
    const finalDistance = throwDistance * strengthMultiplier;
    
    // Calculate angle for bone rotation
    const angle = Math.atan2(throwVector.y, throwVector.x) * (180 / Math.PI);
    setBoneRotation(angle);
    
    // Set initial bone position
    setBonePosition(startPosition);
    setBoneActive(true);
    
    // Gravity and physics variables
    const gravity = 0.0015;
    const initialVelocityY = -0.02 * (1 + boneThrowStrength);
    
    // Animation variables
    let time = 0;
    let velocityY = initialVelocityY;
    let rotationSpeed = 5 + (boneThrowStrength * 10);
    let totalRotation = 0;
    
    // Animate bone movement with simulated physics
    const animateBone = () => {
      time += 0.016;
      
      // Update vertical velocity with gravity
      velocityY += gravity;
      
      // Calculate horizontal progress
      const horizontalProgress = Math.min(1, time * 0.8);
      
      // Calculate new position with physics
      const newPosition = {
        x: startPosition.x + normalizedThrowVector.x * finalDistance * horizontalProgress,
        y: startPosition.y + normalizedThrowVector.y * finalDistance * horizontalProgress + 
           (velocityY * time * 1000) + (0.5 * gravity * time * time * 10000)
      };
      
      // Update bone rotation
      totalRotation += rotationSpeed;
      setBoneRotation(angle + totalRotation);
      
      // Update bone position
      setBonePosition(newPosition);
      
      // Continue animation until complete
      if (horizontalProgress < 1) {
        boneAnimationRef.current = requestAnimationFrame(animateBone);
      } else {
        // Animation complete - fade out bone
        setTimeout(() => {
          setBoneActive(false);
          setDogHouseVisible(false);
          
          // Wait before allowing Scout to walk again
          setTimeout(() => {
            startNewWalk();
          }, 1500);
        }, 200);
      }
    };
    
    boneAnimationRef.current = requestAnimationFrame(animateBone);
  };

  return (
    <div 
      ref={containerRef} 
      style={{ 
        position: 'absolute', 
        top: 0, 
        left: 0, 
        width: '100%', 
        height: '100%', 
        pointerEvents: 'none',
        overflow: 'hidden',
        zIndex: 5
      }} 
      onClick={(e) => {
        // Make the container receive clicks only if Scout is walking
        if (scoutRef.current.isWalking) {
          e.currentTarget.style.pointerEvents = 'auto';
          setTimeout(() => {
            e.currentTarget.style.pointerEvents = 'none';
          }, 0);
        }
      }}
    >
      {/* Click feedback indicator */}
      {clickFeedback && (
        <div 
          style={{
            position: 'absolute',
            left: `${mousePosition.x}px`,
            top: `${mousePosition.y}px`,
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            backgroundColor: isDarkTheme ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
            transform: 'translate(-50%, -50%)',
            animation: 'ripple 0.3s ease-out',
            pointerEvents: 'none'
          }}
        />
      )}
      
      {dogHouseVisible && (
        <DogHouse 
          isDarkTheme={isDarkTheme} 
          position={dogHousePosition} 
          onClick={handleThrowBone} 
        />
      )}
      
      {boneActive && (
        <Bone 
          isDarkTheme={isDarkTheme} 
          position={bonePosition} 
          rotation={boneRotation}
          isActive={boneActive}
        />
      )}
    </div>
  );
};

export default DogPrints; 