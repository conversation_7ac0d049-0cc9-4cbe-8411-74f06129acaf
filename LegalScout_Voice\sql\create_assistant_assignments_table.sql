-- Create assistant_assignments table for OAuth-based assistant access control
-- This table manages which assistants are assigned to which OAuth users

CREATE TABLE IF NOT EXISTS assistant_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  oauth_email TEXT NOT NULL,
  assistant_id TEXT NOT NULL,
  assigned_by UUID REFERENCES attorneys(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique assignment per user/assistant combination
  UNIQUE(oauth_email, assistant_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assistant_assignments_oauth_email 
ON assistant_assignments(oauth_email);

CREATE INDEX IF NOT EXISTS idx_assistant_assignments_assistant_id 
ON assistant_assignments(assistant_id);

CREATE INDEX IF NOT EXISTS idx_assistant_assignments_active 
ON assistant_assignments(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_assistant_assignments_oauth_active 
ON assistant_assignments(oauth_email, is_active) WHERE is_active = TRUE;

-- Enable RLS
ALTER TABLE assistant_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only see their own assignments
CREATE POLICY "Users can view their own assistant assignments" 
ON assistant_assignments FOR SELECT 
USING (oauth_email = auth.jwt() ->> 'email');

-- RLS Policy: Attorneys can manage assignments for their firm
CREATE POLICY "Attorneys can manage assistant assignments" 
ON assistant_assignments FOR ALL 
USING (
  assigned_by IN (
    SELECT id FROM attorneys 
    WHERE oauth_email = auth.jwt() ->> 'email'
  )
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_assistant_assignments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_assistant_assignments_updated_at
  BEFORE UPDATE ON assistant_assignments
  FOR EACH ROW
  EXECUTE FUNCTION update_assistant_assignments_updated_at();

-- Insert default assignments for existing users
-- Damon's assignments
INSERT INTO assistant_assignments (oauth_email, assistant_id, assigned_by, notes)
VALUES 
  ('<EMAIL>', 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', 
   (SELECT id FROM attorneys WHERE oauth_email = '<EMAIL>' LIMIT 1),
   'Primary assistant for Damon'),
  ('<EMAIL>', 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
   (SELECT id FROM attorneys WHERE oauth_email = '<EMAIL>' LIMIT 1),
   'Primary assistant for Damon (work email)')
ON CONFLICT (oauth_email, assistant_id) DO NOTHING;

-- Robert's assignments (when we know his assistant ID)
-- INSERT INTO assistant_assignments (oauth_email, assistant_id, assigned_by, notes)
-- VALUES 
--   ('<EMAIL>', 'robert-assistant-id-here',
--    (SELECT id FROM attorneys WHERE oauth_email = '<EMAIL>' LIMIT 1),
--    'Primary assistant for Robert')
-- ON CONFLICT (oauth_email, assistant_id) DO NOTHING;

-- Grant permissions
GRANT ALL ON assistant_assignments TO authenticated;
GRANT ALL ON assistant_assignments TO service_role;

-- Comments for documentation
COMMENT ON TABLE assistant_assignments IS 'Manages which Vapi assistants are assigned to which OAuth users';
COMMENT ON COLUMN assistant_assignments.oauth_email IS 'OAuth email address of the user (e.g., <EMAIL>)';
COMMENT ON COLUMN assistant_assignments.assistant_id IS 'Vapi assistant ID that the user has access to';
COMMENT ON COLUMN assistant_assignments.assigned_by IS 'Attorney who assigned this assistant (for audit trail)';
COMMENT ON COLUMN assistant_assignments.is_active IS 'Whether this assignment is currently active';
COMMENT ON COLUMN assistant_assignments.notes IS 'Optional notes about this assignment';
