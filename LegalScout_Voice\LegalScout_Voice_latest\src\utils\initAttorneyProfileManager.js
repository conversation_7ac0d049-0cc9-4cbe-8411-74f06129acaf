/**
 * Attorney Profile Manager Initialization
 *
 * This script initializes the AttorneyProfileManager when the application loads.
 * It ensures that the attorney profile is properly loaded and synchronized across systems.
 */

import { attorneyProfileManager } from '../services/AttorneyProfileManager';
import { vapiServiceManager } from '../services/vapiServiceManager';

/**
 * Initialize the attorney profile manager
 * @returns {Promise<void>}
 */
export const initAttorneyProfileManager = async () => {
  console.log('[initAttorneyProfileManager] Initializing attorney profile manager');

  try {
    // Try to connect to Vapi service
    const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                  (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||
                  localStorage.getItem('vapi_api_key');

    if (apiKey) {
      console.log('[initAttorneyProfileManager] Initializing Vapi service manager');

      try {
        // Determine if we're in attorney dashboard or preview mode
        const isAttorneyDashboard = window.location.pathname.includes('/dashboard') ||
                                   window.location.pathname.includes('/admin');
        const isPreview = window.location.pathname.includes('/preview');

        // Determine if we're in production
        const isProduction = typeof window !== 'undefined' &&
                            (window.location.hostname.includes('.com') ||
                             window.location.hostname.includes('.org') ||
                             window.location.hostname.includes('.net') ||
                             window.location.hostname.includes('.io') ||
                             !window.location.hostname.includes('localhost'));

        // Prioritize direct API connection instead of MCP
        const forceMcpMode = false;

        // Initialize the Vapi service manager with appropriate options
        await vapiServiceManager.initialize(apiKey, false, {
          isAttorneyDashboard,
          isPreview,
          isProduction,
          forceMcpMode
        });

        // Log connection status
        const status = vapiServiceManager.getConnectionStatus();
        console.log('[initAttorneyProfileManager] Vapi service manager initialized:', status);

        // Check for connection warnings
        const connectionWarning = vapiServiceManager.getConnectionWarning();
        if (connectionWarning) {
          console.warn('[initAttorneyProfileManager] Vapi connection warning:', connectionWarning);

          // Only show warning in the dashboard, not in preview or demo pages
          if (isAttorneyDashboard && !window.location.pathname.includes('/demo')) {
            // Use setTimeout to ensure DOM is ready
            setTimeout(() => {
              try {
                // Create or update warning banner
                let warningBanner = document.getElementById('voice-connection-warning');
                if (!warningBanner) {
                  warningBanner = document.createElement('div');
                  warningBanner.id = 'voice-connection-warning';
                  warningBanner.style.backgroundColor = '#FFF3CD';
                  warningBanner.style.color = '#856404';
                  warningBanner.style.padding = '10px';
                  warningBanner.style.margin = '10px 0';
                  warningBanner.style.borderRadius = '4px';
                  warningBanner.style.border = '1px solid #FFEEBA';
                  warningBanner.style.position = 'fixed';
                  warningBanner.style.top = '10px';
                  warningBanner.style.right = '10px';
                  warningBanner.style.zIndex = '9999';
                  warningBanner.style.maxWidth = '400px';
                  document.body.appendChild(warningBanner);
                }
                warningBanner.textContent = connectionWarning;
              } catch (domError) {
                console.error('[initAttorneyProfileManager] Error showing warning banner:', domError);
              }
            }, 1000);
          } else {
            // For demo and preview pages, just log the warning without showing a banner
            console.log('[initAttorneyProfileManager] Voice service warning (hidden from UI):', connectionWarning);
          }
        }

        if (status.useMock) {
          if (isAttorneyDashboard || isPreview) {
            console.warn('[initAttorneyProfileManager] Using mock Vapi service in attorney dashboard/preview mode');
          } else {
            console.log('[initAttorneyProfileManager] Using mock Vapi service');
          }
        } else {
          console.log(`[initAttorneyProfileManager] Connected to Vapi using ${status.connectionMode} mode`);
        }
      } catch (error) {
        console.warn('[initAttorneyProfileManager] Failed to initialize Vapi service manager:', error);
        console.log('[initAttorneyProfileManager] Continuing with initialization despite Vapi connection failure');
      }
    } else {
      console.warn('[initAttorneyProfileManager] No Vapi API key found');
    }

    // Auto-initialize from localStorage if available
    const storedAttorney = localStorage.getItem('attorney');
    if (storedAttorney) {
      try {
        const attorney = JSON.parse(storedAttorney);
        if (attorney && attorney.id) {
          console.log('[initAttorneyProfileManager] Found attorney in localStorage:', attorney.id);

          // The AttorneyProfileManager will handle the rest of the initialization
          // in its constructor via autoInitializeFromLocalStorage()
        }
      } catch (error) {
        console.error('[initAttorneyProfileManager] Error parsing stored attorney:', error);
      }
    }

    console.log('[initAttorneyProfileManager] Initialization complete');
  } catch (error) {
    console.error('[initAttorneyProfileManager] Initialization error:', error);
  }
};

// Auto-initialize when this module is imported
initAttorneyProfileManager();

export default initAttorneyProfileManager;
