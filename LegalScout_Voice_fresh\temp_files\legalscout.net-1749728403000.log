 🚀 [PRODUCTION CSP] Environment: PRODUCTION
 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
 🚀 [PRODUCTION CSP] Final eval status: WORKING
 🔧 [ProductionEvalFix] Starting production eval fix...
 🔧 [ProductionEvalFix] Environment detected: Object
 🧪 [CSP TEST] Running CSP verification tests...
 🧪 [CSP TEST] ✅ Eval test PASSED: 4
 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
 🧪 [CSP TEST] All tests completed
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using default Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: Object
 🔧 [ProductionEvalFix] Initialization complete
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 🧪 [CSP TEST] ✅ setTimeout string test PASSED
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 🔧 [FinalLabelFix] Starting final label accessibility fix...
 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
 ✅ [FinalLabelFix] Final label fix initialized and monitoring
 🧪 [TestAssistantIDPropagation] Starting test...
 ✅ [TestAssistantIDPropagation] Test script loaded
 🧹 [VapiAssistantCleanup] Starting cleanup utility...
 ✅ [VapiAssistantCleanup] Cleanup utility loaded
 💡 Usage:
   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: Object
 🔧 [ProductionEvalFix] Initialization complete
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:146 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:166 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:171 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:188 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:193 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:206 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:250 [ProductionCorsFix] 🎉 All production fixes initialized successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
index.ts:5 Loaded contentScript
ActiveCheckHelper.ts:21 received intentional event
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... Object
production-cors-fix.js:211 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: Array(1)
legalscout.net/:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
production-cors-fix.js:224 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
ActiveCheckHelper.ts:8 updating page active status
test-assistant-id-propagation.js:157 🔄 Auto-running tests...
test-assistant-id-propagation.js:122 🚀 Running all tests...
test-assistant-id-propagation.js:9 🔍 Testing assistant ID propagation...
hook.js:608 ⚠️ Assistant dropdown not found
overrideMethod @ hook.js:608
test-assistant-id-propagation.js:71 🛡️ Testing CSP fix...
test-assistant-id-propagation.js:81 ✅ CSP includes vercel.live domain
test-assistant-id-propagation.js:91 🌐 Testing Vapi API endpoints...
hook.js:608 ⚠️ Vapi Direct API service not found
overrideMethod @ hook.js:608
test-assistant-id-propagation.js:105 ♿ Testing form accessibility...
test-assistant-id-propagation.js:115 ✅ No problematic labels found
test-assistant-id-propagation.js:131 📊 Test Results: Object
test-assistant-id-propagation.js:136 🎯 Tests passed: 2/4
hook.js:608 ⚠️ Some tests failed. Check the logs above for details.
overrideMethod @ hook.js:608
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
