-- Fix Storage Policies for legalscout_bucket1
-- This migration creates the necessary storage policies to allow authenticated users to upload files

-- Create the storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'legalscout_bucket1',
  'legalscout_bucket1',
  true,
  52428800, -- 50MB in bytes
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'audio/mpeg', 'audio/wav', 'audio/mp3', 'application/pdf', 'text/plain']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'audio/mpeg', 'audio/wav', 'audio/mp3', 'application/pdf', 'text/plain'];

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Authenticated users can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public can view files in public buckets" ON storage.objects;

-- Create storage policies for legalscout_bucket1

-- Policy 1: Allow authenticated users to upload files to their own folders
CREATE POLICY "Authenticated users can upload files" ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'legalscout_bucket1' 
    AND auth.role() = 'authenticated'
    AND (
      -- Allow uploads to assistant folders (assistant_id/filename)
      name ~ '^[a-f0-9-]{36}/' 
      OR 
      -- Allow uploads to attorney folders (attorney_id/filename)
      name ~ '^[a-f0-9-]{36}/' 
      OR
      -- Allow uploads with attorney identifier in filename
      name ~ 'knowledge-[a-f0-9-]{36}-'
      OR
      name ~ 'voice-clone-[a-f0-9-]{36}-'
      OR
      name ~ 'logo_[0-9]+'
      OR
      name ~ 'assistant_image_[0-9]+'
    )
  );

-- Policy 2: Allow authenticated users to view all files in the bucket
CREATE POLICY "Authenticated users can view files" ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'legalscout_bucket1' 
    AND auth.role() = 'authenticated'
  );

-- Policy 3: Allow public access to view files (since bucket is public)
CREATE POLICY "Public can view files in public buckets" ON storage.objects
  FOR SELECT
  USING (bucket_id = 'legalscout_bucket1');

-- Policy 4: Allow authenticated users to delete their own files
CREATE POLICY "Authenticated users can delete their own files" ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'legalscout_bucket1' 
    AND auth.role() = 'authenticated'
    AND (
      -- Allow deletion of files in assistant folders
      name ~ '^[a-f0-9-]{36}/' 
      OR 
      -- Allow deletion of files with attorney identifier
      name ~ 'knowledge-[a-f0-9-]{36}-'
      OR
      name ~ 'voice-clone-[a-f0-9-]{36}-'
      OR
      name ~ 'logo_[0-9]+'
      OR
      name ~ 'assistant_image_[0-9]+'
    )
  );

-- Policy 5: Allow authenticated users to update file metadata
CREATE POLICY "Authenticated users can update files" ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'legalscout_bucket1' 
    AND auth.role() = 'authenticated'
    AND (
      -- Allow updates to files in assistant folders
      name ~ '^[a-f0-9-]{36}/' 
      OR 
      -- Allow updates to files with attorney identifier
      name ~ 'knowledge-[a-f0-9-]{36}-'
      OR
      name ~ 'voice-clone-[a-f0-9-]{36}-'
      OR
      name ~ 'logo_[0-9]+'
      OR
      name ~ 'assistant_image_[0-9]+'
    )
  );

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create a function to help with debugging storage issues
CREATE OR REPLACE FUNCTION debug_storage_access(file_path TEXT)
RETURNS TABLE (
  user_id UUID,
  user_role TEXT,
  bucket_id TEXT,
  file_name TEXT,
  can_insert BOOLEAN,
  can_select BOOLEAN,
  can_delete BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    auth.uid() as user_id,
    auth.role() as user_role,
    'legalscout_bucket1'::TEXT as bucket_id,
    file_path as file_name,
    -- Check insert permission
    (auth.role() = 'authenticated' AND (
      file_path ~ '^[a-f0-9-]{36}/' OR 
      file_path ~ 'knowledge-[a-f0-9-]{36}-' OR
      file_path ~ 'voice-clone-[a-f0-9-]{36}-' OR
      file_path ~ 'logo_[0-9]+' OR
      file_path ~ 'assistant_image_[0-9]+'
    )) as can_insert,
    -- Check select permission
    (auth.role() = 'authenticated') as can_select,
    -- Check delete permission
    (auth.role() = 'authenticated' AND (
      file_path ~ '^[a-f0-9-]{36}/' OR 
      file_path ~ 'knowledge-[a-f0-9-]{36}-' OR
      file_path ~ 'voice-clone-[a-f0-9-]{36}-' OR
      file_path ~ 'logo_[0-9]+' OR
      file_path ~ 'assistant_image_[0-9]+'
    )) as can_delete;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the debug function
GRANT EXECUTE ON FUNCTION debug_storage_access(TEXT) TO authenticated;

-- Add a comment explaining the storage setup
COMMENT ON TABLE storage.objects IS 'Storage objects with RLS policies for legalscout_bucket1. Authenticated users can upload/view/delete files with proper naming patterns.';
