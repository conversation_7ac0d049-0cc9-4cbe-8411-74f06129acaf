import React, { useState } from 'react';

const PRACTICE_AREAS = [
  'Administrative Law',
  'Bankruptcy',
  'Business Law',
  'Civil Rights',
  'Constitutional Law',
  'Consumer Law',
  'Contract Law',
  'Corporate Law',
  'Criminal Law',
  'Employment Law',
  'Environmental Law',
  'Family Law',
  'Health Law',
  'Immigration Law',
  'Insurance Law',
  'Intellectual Property',
  'International Law',
  'Labor Law',
  'Personal Injury',
  'Real Estate Law',
  'Securities Law',
  'Tax Law',
  'Trust and Estate Law',
];

export const PracticeAreasSelect = ({ value, onChange, className }) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const toggleArea = (area) => {
    if (value.includes(area)) {
      onChange(value.filter(a => a !== area));
    } else {
      onChange([...value, area]);
    }
  };
  
  const clearAll = () => {
    onChange([]);
  };
  
  const filteredAreas = searchTerm 
    ? PRACTICE_AREAS.filter(area => 
        area.toLowerCase().includes(searchTerm.toLowerCase()))
    : PRACTICE_AREAS;
  
  return (
    <div className={`practice-areas-select ${className || ''}`}>
      <div className="flex items-center mb-2">
        <input
          type="text"
          placeholder="Search practice areas..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="p-2 border rounded flex-grow"
        />
        {value.length > 0 && (
          <button 
            onClick={clearAll}
            className="ml-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
          >
            Clear All
          </button>
        )}
      </div>
      
      <div className="selected-areas mb-2">
        {value.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.map(area => (
              <div 
                key={area} 
                className="bg-blue-100 px-2 py-1 rounded-full text-sm flex items-center"
              >
                <span>{area}</span>
                <button 
                  onClick={() => toggleArea(area)}
                  className="ml-1 text-gray-500 hover:text-gray-800"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="area-options max-h-60 overflow-y-auto border rounded">
        {filteredAreas.length > 0 ? (
          filteredAreas.map(area => (
            <div 
              key={area}
              onClick={() => toggleArea(area)}
              className={`p-2 cursor-pointer hover:bg-gray-100 ${
                value.includes(area) ? 'bg-blue-50' : ''
              }`}
            >
              <input
                type="checkbox"
                checked={value.includes(area)}
                onChange={() => {}}
                className="mr-2"
              />
              {area}
            </div>
          ))
        ) : (
          <div className="p-2 text-gray-500">No matching practice areas</div>
        )}
      </div>
    </div>
  );
}; 