/**
 * Critical Issues Test Suite
 * 
 * Comprehensive testing for the issues identified in legalscout.net-1749693550984.log
 */

class CriticalIssuesTestSuite {
  constructor() {
    this.testResults = [];
    this.errors = [];
    this.warnings = [];
  }

  async runAllTests() {
    console.log('🧪 Starting Critical Issues Test Suite');
    console.log('=====================================');

    try {
      await this.testReactContextIssues();
      await this.testVapiSDKLoading();
      await this.testCSPViolations();
      await this.testAssistantCleanup();
      await this.testModuleImports();
      await this.testSupabaseConnection();
      await this.testAssistantMapping();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.errors.push(`Test suite failure: ${error.message}`);
    }
  }

  async testReactContextIssues() {
    console.log('\n🔍 Testing React Context Issues...');
    
    try {
      // Test if React is properly loaded
      if (typeof React === 'undefined') {
        this.errors.push('React is not defined globally');
        return;
      }

      // Test createContext availability
      if (!React.createContext) {
        this.errors.push('React.createContext is not available');
        return;
      }

      // Test AssistantAwareContext
      const testContext = React.createContext(null);
      if (!testContext) {
        this.errors.push('Failed to create test context');
        return;
      }

      console.log('✅ React Context tests passed');
      this.testResults.push('React Context: PASS');
    } catch (error) {
      console.error('❌ React Context test failed:', error);
      this.errors.push(`React Context: ${error.message}`);
    }
  }

  async testVapiSDKLoading() {
    console.log('\n🔍 Testing Vapi SDK Loading...');
    
    try {
      // Test if Vapi is available
      if (typeof window.Vapi !== 'undefined') {
        console.log('✅ Vapi SDK already loaded');
        this.testResults.push('Vapi SDK: LOADED');
        return;
      }

      // Test CDN accessibility
      const cdnTest = await this.testCDNAccess('https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js');
      if (!cdnTest) {
        this.warnings.push('Vapi CDN not accessible - may need fallback');
      }

      // Test alternative loading methods
      const npmTest = await this.testNPMPackageAccess('@vapi-ai/web');
      if (!npmTest) {
        this.warnings.push('NPM package not accessible');
      }

      console.log('✅ Vapi SDK loading tests completed');
      this.testResults.push('Vapi SDK Loading: TESTED');
    } catch (error) {
      console.error('❌ Vapi SDK loading test failed:', error);
      this.errors.push(`Vapi SDK Loading: ${error.message}`);
    }
  }

  async testCSPViolations() {
    console.log('\n🔍 Testing CSP Violations...');
    
    try {
      // Test frame-src violations
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (cspMeta) {
        const cspContent = cspMeta.getAttribute('content');
        
        // Check for vercel.live in frame-src
        if (!cspContent.includes('https://vercel.live')) {
          this.warnings.push('vercel.live not in frame-src CSP directive');
        }
        
        // Check for *.vercel.live in frame-src
        if (!cspContent.includes('https://*.vercel.live')) {
          this.warnings.push('*.vercel.live not in frame-src CSP directive');
        }
      }

      console.log('✅ CSP violation tests completed');
      this.testResults.push('CSP Violations: CHECKED');
    } catch (error) {
      console.error('❌ CSP violation test failed:', error);
      this.errors.push(`CSP Violations: ${error.message}`);
    }
  }

  async testAssistantCleanup() {
    console.log('\n🔍 Testing Assistant Cleanup Issues...');
    
    try {
      // Test if cleanup utilities are available
      if (typeof window.vapiAssistantCleanup !== 'undefined') {
        console.log('✅ Assistant cleanup utility available');
        
        // Test dry run functionality
        if (window.vapiAssistantCleanup.analyzeAssistants) {
          const analysis = await window.vapiAssistantCleanup.analyzeAssistants();
          console.log('📊 Assistant analysis:', analysis);
        }
      } else {
        this.warnings.push('Assistant cleanup utility not loaded');
      }

      console.log('✅ Assistant cleanup tests completed');
      this.testResults.push('Assistant Cleanup: TESTED');
    } catch (error) {
      console.error('❌ Assistant cleanup test failed:', error);
      this.errors.push(`Assistant Cleanup: ${error.message}`);
    }
  }

  async testModuleImports() {
    console.log('\n🔍 Testing Module Import Issues...');
    
    try {
      // Test for ES6 module syntax in non-module scripts
      const scripts = document.querySelectorAll('script:not([type="module"])');
      let moduleIssues = 0;
      
      scripts.forEach(script => {
        if (script.src && script.src.includes('production-debug-vapi.js')) {
          this.warnings.push('production-debug-vapi.js may have ES6 import issues');
          moduleIssues++;
        }
      });

      if (moduleIssues === 0) {
        console.log('✅ No obvious module import issues detected');
      }

      console.log('✅ Module import tests completed');
      this.testResults.push('Module Imports: CHECKED');
    } catch (error) {
      console.error('❌ Module import test failed:', error);
      this.errors.push(`Module Imports: ${error.message}`);
    }
  }

  async testSupabaseConnection() {
    console.log('\n🔍 Testing Supabase Connection...');
    
    try {
      if (typeof window.supabase !== 'undefined') {
        // Test basic connection
        const { data, error } = await window.supabase
          .from('attorneys')
          .select('id')
          .limit(1);
        
        if (error) {
          this.errors.push(`Supabase connection error: ${error.message}`);
        } else {
          console.log('✅ Supabase connection successful');
          this.testResults.push('Supabase Connection: PASS');
        }
      } else {
        this.warnings.push('Supabase client not available');
      }
    } catch (error) {
      console.error('❌ Supabase connection test failed:', error);
      this.errors.push(`Supabase Connection: ${error.message}`);
    }
  }

  async testAssistantMapping() {
    console.log('\n🔍 Testing Assistant Mapping...');
    
    try {
      if (typeof window.supabase !== 'undefined') {
        // Test assistant_subdomains table
        const { data: subdomains, error: subdomainError } = await window.supabase
          .from('assistant_subdomains')
          .select('*')
          .limit(5);
        
        if (subdomainError) {
          this.errors.push(`Assistant subdomains error: ${subdomainError.message}`);
        } else {
          console.log('✅ Assistant subdomains accessible');
          console.log('📊 Sample subdomains:', subdomains);
        }

        // Test attorneys table
        const { data: attorneys, error: attorneyError } = await window.supabase
          .from('attorneys')
          .select('id, email, current_assistant_id')
          .limit(5);
        
        if (attorneyError) {
          this.errors.push(`Attorneys table error: ${attorneyError.message}`);
        } else {
          console.log('✅ Attorneys table accessible');
          console.log('📊 Sample attorneys:', attorneys);
        }
      }

      console.log('✅ Assistant mapping tests completed');
      this.testResults.push('Assistant Mapping: TESTED');
    } catch (error) {
      console.error('❌ Assistant mapping test failed:', error);
      this.errors.push(`Assistant Mapping: ${error.message}`);
    }
  }

  async testCDNAccess(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async testNPMPackageAccess(packageName) {
    try {
      const module = await import(packageName);
      return !!module;
    } catch (error) {
      return false;
    }
  }

  generateReport() {
    console.log('\n📊 Test Suite Report');
    console.log('===================');
    
    console.log('\n✅ Passed Tests:');
    this.testResults.forEach(result => console.log(`  - ${result}`));
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    const totalTests = this.testResults.length;
    const totalIssues = this.errors.length + this.warnings.length;
    
    console.log(`\n📈 Summary: ${totalTests} tests completed, ${this.errors.length} errors, ${this.warnings.length} warnings`);
    
    if (this.errors.length === 0) {
      console.log('🎉 All critical tests passed!');
    } else {
      console.log('🚨 Critical issues detected - see errors above');
    }
  }
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  window.CriticalIssuesTestSuite = CriticalIssuesTestSuite;
  
  // Auto-run after page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const testSuite = new CriticalIssuesTestSuite();
        testSuite.runAllTests();
      }, 2000); // Wait 2 seconds for other scripts to load
    });
  } else {
    setTimeout(() => {
      const testSuite = new CriticalIssuesTestSuite();
      testSuite.runAllTests();
    }, 2000);
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CriticalIssuesTestSuite;
}
