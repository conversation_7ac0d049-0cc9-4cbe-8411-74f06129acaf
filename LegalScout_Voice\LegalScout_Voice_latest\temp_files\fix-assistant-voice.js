/**
 * Fix Assistant Voice Configuration using MCP
 * Adds the missing firstMessage and firstMessageMode to make the assistant speak first
 */

// Use MCP to update the assistant directly
const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

// Configuration to add the missing fields
const assistantConfig = {
  firstMessage: "Hello! How can I help you today?",
  firstMessageMode: "assistant-speaks-first",
  instructions: "You are a legal assistant helping clients with their legal needs. Be professional, helpful, and gather relevant information for consultation.",
  // Keep existing configurations
  name: "LegalScout Assistant",
  llm: {
    provider: "openai",
    model: "gpt-4o"
  },
  voice: {
    provider: "11labs",
    voiceId: "sarah",
    model: "eleven_turbo_v2_5"
  },
  transcriber: {
    provider: "deepgram",
    model: "nova-3"
  }
};

console.log('🔧 [FixAssistantVoice] Updating assistant with MCP...');
console.log('🎯 [FixAssistantVoice] Config:', assistantConfig);

console.log('🔧 Fix Assistant Voice script loaded');
