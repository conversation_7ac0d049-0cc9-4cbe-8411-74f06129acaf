callback:21 ✅ Vapi public key set globally
callback:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
callback:52 🚀 [EMERGENCY] Starting emergency critical fixes...
callback:56 🔧 [EMERGENCY] Adding process polyfill
callback:63 ✅ [EMERGENCY] Process polyfill added
callback:74 🔧 [EMERGENCY] Development mode: false (forced production)
callback:101 ✅ [EMERGENCY] Fetch patched
callback:104 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
callback:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
overrideMethod @ hook.js:608
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
callback:209 Supabase loaded from CDN
callback:219 Creating Supabase client from CDN
callback:223 Supabase client created from CDN
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:100 Supabase client initialized successfully with proper headers
supabase.js:103 Testing Supabase connection...
supabase.js:148 Running in development mode
supabase.js:216 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
index.ts:5 Loaded contentScript
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
(anonymous) @ supabase.js:108
callback#:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
supabase.js:114 Supabase query error: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabase.js:114
Promise.then
then @ @supabase_supabase-js.js?v=fb518282:192
(anonymous) @ supabase.js:108
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
App.jsx:816 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
fetchSubdomains @ App.jsx:816
await in fetchSubdomains
(anonymous) @ App.jsx:832
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
AuthCallback.jsx:105 Auth callback error: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
handleCallback @ AuthCallback.jsx:105
await in handleCallback
(anonymous) @ AuthCallback.jsx:112
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
query.then @ supabase.js:195
AuthCallback.jsx:105 Auth callback error: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
handleCallback @ AuthCallback.jsx:105
await in handleCallback
(anonymous) @ AuthCallback.jsx:112
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
