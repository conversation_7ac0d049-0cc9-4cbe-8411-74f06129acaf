/**
 * Complete test of assistant dropdown and switching functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;
const VAPI_PRIVATE_KEY = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

console.log('🎯 Complete Assistant Functionality Test');
console.log('========================================');

async function testCompleteAssistantFunctionality() {
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    
    // Test data
    const attorneyId = '87756a2c-a398-43f2-889a-b8815684df71';
    const originalAssistantId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
    const testAssistantId = '4e899c0a-b435-45c4-abcd-8abd3ff13ec3';
    
    console.log('👤 Attorney ID:', attorneyId);
    console.log('🤖 Original Assistant ID:', originalAssistantId);
    console.log('🔄 Test Assistant ID:', testAssistantId);
    
    // Test 1: Verify Vapi API access
    console.log('\n📋 Test 1: Vapi API Access');
    console.log('==========================');
    
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Vapi API error: ${response.status}`);
    }

    const allAssistants = await response.json();
    console.log(`✅ Vapi API: Found ${allAssistants.length} assistants`);
    
    // Filter relevant assistants
    const relevantAssistants = allAssistants.filter(assistant => {
      const name = assistant.name?.toLowerCase() || '';
      return name.includes('legalscout') || name.includes('kost') || name.includes('damon');
    });
    
    console.log(`✅ Filtering: ${relevantAssistants.length} relevant assistants`);
    
    // Test 2: Verify attorney data
    console.log('\n📋 Test 2: Attorney Data');
    console.log('========================');
    
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();

    if (attorneyError) {
      throw new Error(`Attorney lookup failed: ${attorneyError.message}`);
    }
    
    console.log(`✅ Attorney: ${attorney.firm_name} (${attorney.email})`);
    console.log(`✅ Subdomain: ${attorney.subdomain}`);
    console.log(`✅ Current Assistant: ${attorney.vapi_assistant_id}`);
    
    // Test 3: Test assistant switching
    console.log('\n📋 Test 3: Assistant Switching');
    console.log('==============================');
    
    // Switch to test assistant
    console.log('🔄 Switching to test assistant...');
    const { error: switchError } = await supabase
      .from('attorneys')
      .update({ 
        current_assistant_id: testAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorneyId);

    if (switchError) {
      throw new Error(`Switch failed: ${switchError.message}`);
    }
    
    console.log('✅ Successfully switched current_assistant_id');
    
    // Create/update assistant config
    console.log('📝 Creating assistant config...');
    const configData = {
      attorney_id: attorneyId,
      assistant_id: testAssistantId,
      firm_name: attorney.firm_name,
      primary_color: '#2563eb',
      welcome_message: 'Hello from the test assistant!',
      vapi_instructions: 'You are a test legal assistant.',
      voice_provider: '11labs',
      voice_id: 'sarah',
      ai_model: 'gpt-4o'
    };

    const { data: config, error: configError } = await supabase
      .from('assistant_ui_configs')
      .upsert(configData)
      .select()
      .single();

    if (configError) {
      throw new Error(`Config creation failed: ${configError.message}`);
    }
    
    console.log('✅ Successfully created assistant config');
    
    // Test 4: Test config retrieval
    console.log('\n📋 Test 4: Config Retrieval');
    console.log('===========================');
    
    const { data: retrievedConfig, error: retrieveError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('attorney_id', attorneyId)
      .eq('assistant_id', testAssistantId)
      .single();

    if (retrieveError) {
      throw new Error(`Config retrieval failed: ${retrieveError.message}`);
    }
    
    console.log('✅ Successfully retrieved config');
    console.log(`   Welcome message: ${retrievedConfig.welcome_message}`);
    console.log(`   Voice provider: ${retrievedConfig.voice_provider}`);
    
    // Test 5: Switch back to original
    console.log('\n📋 Test 5: Switch Back');
    console.log('======================');
    
    const { error: switchBackError } = await supabase
      .from('attorneys')
      .update({ 
        current_assistant_id: originalAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorneyId);

    if (switchBackError) {
      throw new Error(`Switch back failed: ${switchBackError.message}`);
    }
    
    console.log('✅ Successfully switched back to original assistant');
    
    // Final verification
    const { data: finalAttorney } = await supabase
      .from('attorneys')
      .select('current_assistant_id, vapi_assistant_id')
      .eq('id', attorneyId)
      .single();
    
    console.log(`✅ Final state: current=${finalAttorney.current_assistant_id}, vapi=${finalAttorney.vapi_assistant_id}`);
    
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('====================');
    console.log('✅ Vapi API access working');
    console.log('✅ Assistant filtering working');
    console.log('✅ Attorney data retrieval working');
    console.log('✅ Assistant switching working');
    console.log('✅ Config creation/retrieval working');
    console.log('✅ RLS policy working');
    
    console.log('\n📝 READY FOR DASHBOARD TESTING:');
    console.log('1. The assistant dropdown should now populate with assistants');
    console.log('2. You should be able to switch between assistants');
    console.log('3. Each assistant can have its own UI configuration');
    console.log('4. The current assistant should be properly selected');
    
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
    console.log(error.stack);
  }
}

// Run the test
testCompleteAssistantFunctionality();
