// FIX SCRIPT: Force Attorney Data Refresh in Briefs
window.fixAttorneyDataInBriefs = function() {
  console.log('🔧 FIX SCRIPT: Force Attorney Data Refresh in Briefs');
  console.log('='.repeat(50));
  
  // Step 1: Get fresh attorney data from Supabase
  console.log('📋 STEP 1: Fetching fresh attorney data...');
  
  refreshAttorneyData();
};

async function refreshAttorneyData() {
  const attorney = window.standaloneAttorneyManager?.attorney;
  
  if (!attorney?.id) {
    console.log('❌ No attorney ID available');
    return;
  }
  
  try {
    console.log('🔄 Fetching fresh data from Supabase...');
    
    const { data: freshData, error } = await window.supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorney.id)
      .single();
    
    if (error) {
      console.log('❌ Error fetching fresh data:', error);
      return;
    }
    
    console.log('✅ Fresh data retrieved');
    console.log('- Has custom_fields:', !!freshData.custom_fields);
    console.log('- Fields count:', Array.isArray(freshData.custom_fields) ? 
      freshData.custom_fields.length : 
      JSON.parse(freshData.custom_fields || '[]').length);
    
    // Step 2: Update the attorney manager
    console.log('\n📋 STEP 2: Updating attorney manager...');
    
    if (window.standaloneAttorneyManager) {
      // Method 1: Try updateAttorney if it exists
      if (typeof window.standaloneAttorneyManager.updateAttorney === 'function') {
        window.standaloneAttorneyManager.updateAttorney(freshData);
        console.log('✅ Used updateAttorney method');
      } else {
        // Method 2: Direct assignment
        window.standaloneAttorneyManager.attorney = freshData;
        console.log('✅ Direct assignment to attorney property');
      }
      
      // Method 3: Trigger any update callbacks
      if (window.standaloneAttorneyManager.onUpdate) {
        window.standaloneAttorneyManager.onUpdate(freshData);
        console.log('✅ Triggered onUpdate callback');
      }
    }
    
    // Step 3: Force React re-render by navigating away and back
    console.log('\n📋 STEP 3: Force React re-render...');
    
    const profileTab = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent?.trim() === 'Profile'
    );
    
    if (profileTab) {
      profileTab.click();
      console.log('✅ Navigated to Profile');
      
      setTimeout(() => {
        const briefsTab = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent?.trim() === 'Briefs'
        );
        
        if (briefsTab) {
          briefsTab.click();
          console.log('✅ Navigated back to Briefs');
          
          setTimeout(() => {
            // Step 4: Verify the fix
            console.log('\n📋 STEP 4: Verifying the fix...');
            
            const briefsContainer = document.querySelector('.consultations-tab');
            if (briefsContainer) {
              const reactFiberKey = Object.keys(briefsContainer).find(key => 
                key.startsWith('__reactFiber')
              );
              
              if (reactFiberKey) {
                let currentFiber = briefsContainer[reactFiberKey];
                while (currentFiber) {
                  if (currentFiber.type?.name === 'ConsultationsTab') {
                    const props = currentFiber.memoizedProps;
                    console.log('📊 UPDATED CONSULTATIONSTAB PROPS:');
                    console.log('- Has attorney prop:', !!props?.attorney);
                    console.log('- Attorney has custom_fields:', !!props?.attorney?.custom_fields);
                    
                    if (props?.attorney?.custom_fields) {
                      const fields = Array.isArray(props.attorney.custom_fields) ? 
                        props.attorney.custom_fields : 
                        JSON.parse(props.attorney.custom_fields);
                      
                      console.log('- Fields count in props:', fields.length);
                      console.log('✅ SUCCESS! Custom fields now in props');
                      
                      // Check table headers
                      setTimeout(() => {
                        const table = document.querySelector('table');
                        if (table) {
                          const headers = table.querySelectorAll('th');
                          console.log(`\n📊 UPDATED TABLE HEADERS (${headers.length}):`);
                          headers.forEach((header, i) => {
                            console.log(`${i + 1}. "${header.textContent?.trim()}"`);
                          });
                          
                          const customHeaders = Array.from(headers).filter(header => 
                            header.querySelector('.field-source-badge')
                          );
                          
                          console.log(`✅ Custom field headers: ${customHeaders.length}`);
                          
                          if (customHeaders.length > 0) {
                            console.log('🎉 SUCCESS! Custom fields now showing in table!');
                          } else {
                            console.log('⚠️ Custom fields in props but not in table headers');
                          }
                        }
                      }, 500);
                      
                    } else {
                      console.log('❌ Still no custom_fields in props');
                      console.log('- Attorney ID in props:', props?.attorney?.id);
                      console.log('- Fresh data ID:', freshData.id);
                    }
                    break;
                  }
                  currentFiber = currentFiber.return;
                }
              }
            }
            
            // Step 5: Test Manage Columns modal
            setTimeout(() => {
              console.log('\n📋 STEP 5: Testing Manage Columns modal...');
              
              const manageBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                btn.textContent?.includes('Manage Columns')
              );
              
              if (manageBtn) {
                manageBtn.click();
                console.log('✅ Clicked Manage Columns');
                
                setTimeout(() => {
                  const modal = document.querySelector('.manage-columns-modal, [class*="modal"]');
                  if (modal && modal.style.display !== 'none') {
                    console.log('✅ Modal opened');
                    
                    const columnItems = modal.querySelectorAll('[class*="column"], [class*="item"], li, .column-item');
                    console.log(`📊 COLUMNS IN MODAL: ${columnItems.length}`);
                    
                    if (columnItems.length > 0) {
                      console.log('🎉 SUCCESS! Manage Columns now shows data!');
                      columnItems.forEach((item, i) => {
                        console.log(`  ${i + 1}. "${item.textContent?.trim().substring(0, 50)}"`);
                      });
                    } else {
                      console.log('⚠️ Modal opened but no columns found');
                    }
                    
                    // Close modal
                    const closeBtn = modal.querySelector('button[class*="close"], .close-button, [aria-label*="close"]');
                    if (closeBtn) {
                      closeBtn.click();
                    }
                  } else {
                    console.log('❌ Modal did not open properly');
                  }
                }, 1000);
              }
            }, 1500);
            
          }, 1000);
        }
      }, 500);
    }
    
  } catch (error) {
    console.log('❌ Error in refresh process:', error);
  }
}

// Run the fix
window.fixAttorneyDataInBriefs();
VM5399:3 🔧 FIX SCRIPT: Force Attorney Data Refresh in Briefs
VM5399:4 ==================================================
VM5399:7 📋 STEP 1: Fetching fresh attorney data...
VM5399:21 🔄 Fetching fresh data from Supabase...
undefined
VM5399:34 ✅ Fresh data retrieved
VM5399:35 - Has custom_fields: true
VM5399:36 - Fields count: 5
VM5399:41 
📋 STEP 2: Updating attorney manager...
standalone-attorney-manager-fixed.js:478 [StandaloneAttorneyManager] Updating attorney
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM5399:47 ✅ Used updateAttorney method
VM5399:62 
📋 STEP 3: Force React re-render...
DashboardNew.jsx:624 [DashboardNew] Tab changed to: profile
VM5399:70 ✅ Navigated to Profile
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:40.931Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: '<EMAIL>'}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:40.931Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: '<EMAIL>'}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: '<EMAIL>'}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:10:12.655Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:10:12.655Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: '<EMAIL>'}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: '<EMAIL>'}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612715}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612715}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612715}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612715}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612717}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612717}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612717}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749240612717}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:624 [DashboardNew] Tab changed to: consultations
VM5399:79 ✅ Navigated back to Briefs
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
VM5399:83 
📋 STEP 4: Verifying the fix...
VM5399:96 📊 UPDATED CONSULTATIONSTAB PROPS:
VM5399:97 - Has attorney prop: true
VM5399:98 - Attorney has custom_fields: true
VM5399:105 - Fields count in props: 5
VM5399:106 ✅ SUCCESS! Custom fields now in props
VM5399:113 
📊 UPDATED TABLE HEADERS (10):
VM5399:115 1. "Date"
VM5399:115 2. "Client"
VM5399:115 3. "Contact"
VM5399:115 4. "Summary"
VM5399:115 5. "accidentDateCustom Field"
VM5399:115 6. "injuryTypeCustom Field"
VM5399:115 7. "medicalTreatmentCustom Field"
VM5399:115 8. "insuranceInfoCustom Field"
VM5399:115 9. "atFaultCustom Field"
VM5399:115 10. "Actions"
VM5399:122 ✅ Custom field headers: 5
VM5399:125 🎉 SUCCESS! Custom fields now showing in table!
VM5399:146 
📋 STEP 5: Testing Manage Columns modal...
VM5399:154 ✅ Clicked Manage Columns
ManageColumnsModal.jsx:61 Filtered columns: []
ManageColumnsModal.jsx:62 Filter: all SortBy: order
ManageColumnsModal.jsx:61 Filtered columns: []
ManageColumnsModal.jsx:62 Filter: all SortBy: order
ManageColumnsModal.jsx:29 ManageColumnsModal opened with columns: []
ManageColumnsModal.jsx:30 Columns length: 0
ManageColumnsModal.jsx:31 Columns data: []
ManageColumnsModal.jsx:29 ManageColumnsModal opened with columns: []
ManageColumnsModal.jsx:30 Columns length: 0
ManageColumnsModal.jsx:31 Columns data: []
ManageColumnsModal.jsx:61 Filtered columns: []
ManageColumnsModal.jsx:62 Filter: all SortBy: order
ManageColumnsModal.jsx:61 Filtered columns: []
ManageColumnsModal.jsx:62 Filter: all SortBy: order
VM5399:159 ✅ Modal opened
VM5399:162 📊 COLUMNS IN MODAL: 7
VM5399:165 🎉 SUCCESS! Manage Columns now shows data!
VM5399:167   1. "Manage ColumnsShow:All ColumnsVisible OnlyHidden O"
VM5399:167   2. "Show:All ColumnsVisible OnlyHidden OnlySort by:Dis"
VM5399:167   3. "Total Columns:0Visible:0Hidden:0"
VM5399:167   4. "Total Columns:0"
VM5399:167   5. "Visible:0"
VM5399:167   6. "Hidden:0"
VM5399:167   7. "Select AllColumnTypeStatusCreatedActionsNo columns"
