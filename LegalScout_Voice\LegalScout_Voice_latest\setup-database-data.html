<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Database Data</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #2d5a2d; border: 1px solid #4a8a4a; }
        .error { background: #5a2d2d; border: 1px solid #8a4a4a; }
        .info { background: #2d2d5a; border: 1px solid #4a4a8a; }
        .warning { background: #5a5a2d; border: 1px solid #8a8a4a; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #4a8a4a; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #5a9a5a; }
        .danger { background: #8a4a4a; }
        .danger:hover { background: #9a5a5a; }
    </style>
</head>
<body>
    <h1>🔧 Setup Database Data for assistant1test</h1>
    <p>This tool will create the required database records for the assistant1test subdomain.</p>
    
    <div class="test-result warning">
        <strong>⚠️ Warning:</strong> This will modify the production database. Only proceed if you understand the implications.
    </div>
    
    <button onclick="checkExistingData()">🔍 Check Existing Data</button>
    <button onclick="setupAttorneyData()" class="danger">👤 Setup Attorney Record</button>
    <button onclick="setupSubdomainMapping()" class="danger">🔗 Setup Subdomain Mapping</button>
    <button onclick="verifySetup()">✅ Verify Complete Setup</button>
    <button onclick="clearResults()">🧹 Clear Results</button>
    
    <div id="results"></div>

    <script>
        const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
        
        // Test data from the diagnostics
        const TEST_DATA = {
            subdomain: 'assistant1test',
            assistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d',
            attorneyId: '87756a2c-a398-43f2-889a-b8815684df71',
            firmName: 'LegalScout'
        };

        window.checkExistingData = async function() {
            addResult('info', '🔍 Checking existing data...');
            
            try {
                // Check attorney
                const attorneyUrl = `${SUPABASE_URL}/rest/v1/attorneys?id=eq.${TEST_DATA.attorneyId}`;
                const attorneyResponse = await fetch(attorneyUrl, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (attorneyResponse.ok) {
                    const attorneyData = await attorneyResponse.json();
                    if (attorneyData.length > 0) {
                        addResult('success', '✅ Attorney record exists');
                        addResult('info', JSON.stringify(attorneyData[0], null, 2));
                    } else {
                        addResult('warning', '⚠️ Attorney record does not exist');
                    }
                } else {
                    addResult('error', `❌ Failed to check attorney: ${attorneyResponse.status}`);
                }

                // Check subdomain mapping
                const mappingUrl = `${SUPABASE_URL}/rest/v1/assistant_subdomains?subdomain=eq.${TEST_DATA.subdomain}`;
                const mappingResponse = await fetch(mappingUrl, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (mappingResponse.ok) {
                    const mappingData = await mappingResponse.json();
                    if (mappingData.length > 0) {
                        addResult('success', '✅ Subdomain mapping exists');
                        addResult('info', JSON.stringify(mappingData[0], null, 2));
                    } else {
                        addResult('warning', '⚠️ Subdomain mapping does not exist');
                    }
                } else {
                    addResult('error', `❌ Failed to check subdomain mapping: ${mappingResponse.status}`);
                }

                // Check view
                const viewUrl = `${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_DATA.subdomain}`;
                const viewResponse = await fetch(viewUrl, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (viewResponse.ok) {
                    const viewData = await viewResponse.json();
                    if (viewData.length > 0) {
                        addResult('success', '✅ View query returns data');
                        addResult('info', JSON.stringify(viewData[0], null, 2));
                    } else {
                        addResult('warning', '⚠️ View query returns no data');
                    }
                } else {
                    addResult('error', `❌ Failed to query view: ${viewResponse.status}`);
                }
                
            } catch (error) {
                addResult('error', `❌ Check failed: ${error.message}`);
            }
        };

        window.setupAttorneyData = async function() {
            addResult('info', '👤 Setting up attorney record...');
            
            try {
                const attorneyData = {
                    id: TEST_DATA.attorneyId,
                    firm_name: TEST_DATA.firmName,
                    name: 'LegalScout Assistant',
                    email: '<EMAIL>',
                    phone: '(*************',
                    logo_url: 'https://legalscout.net/logo.png',
                    vapi_instructions: 'You are a helpful legal assistant for LegalScout. You help potential clients understand their legal options and connect them with qualified attorneys.',
                    ai_model: 'gpt-4o',
                    voice_id: 'alloy',
                    welcome_message: 'Hello! I\'m your LegalScout AI assistant. How can I help you with your legal questions today?',
                    primary_color: '#3B82F6',
                    is_active: true
                };

                const response = await fetch(`${SUPABASE_URL}/rest/v1/attorneys`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(attorneyData)
                });

                if (response.ok) {
                    const result = await response.json();
                    addResult('success', '✅ Attorney record created successfully');
                    addResult('info', JSON.stringify(result, null, 2));
                } else {
                    const errorText = await response.text();
                    if (response.status === 409) {
                        addResult('warning', '⚠️ Attorney record already exists (conflict)');
                    } else {
                        addResult('error', `❌ Failed to create attorney: ${response.status} - ${errorText}`);
                    }
                }
                
            } catch (error) {
                addResult('error', `❌ Setup attorney failed: ${error.message}`);
            }
        };

        window.setupSubdomainMapping = async function() {
            addResult('info', '🔗 Setting up subdomain mapping...');
            
            try {
                const mappingData = {
                    assistant_id: TEST_DATA.assistantId,
                    subdomain: TEST_DATA.subdomain,
                    attorney_id: TEST_DATA.attorneyId,
                    is_primary: true,
                    is_active: true
                };

                const response = await fetch(`${SUPABASE_URL}/rest/v1/assistant_subdomains`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(mappingData)
                });

                if (response.ok) {
                    const result = await response.json();
                    addResult('success', '✅ Subdomain mapping created successfully');
                    addResult('info', JSON.stringify(result, null, 2));
                } else {
                    const errorText = await response.text();
                    if (response.status === 409) {
                        addResult('warning', '⚠️ Subdomain mapping already exists (conflict)');
                    } else {
                        addResult('error', `❌ Failed to create mapping: ${response.status} - ${errorText}`);
                    }
                }
                
            } catch (error) {
                addResult('error', `❌ Setup mapping failed: ${error.message}`);
            }
        };

        window.verifySetup = async function() {
            addResult('info', '✅ Verifying complete setup...');
            
            try {
                // Test the exact query the assistant routing service uses
                const url = `${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_DATA.subdomain}&is_active=eq.true`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    addResult('error', `❌ Verification query failed: ${response.status}`);
                    return;
                }

                const data = await response.json();
                
                if (!data || data.length === 0) {
                    addResult('error', '❌ Verification failed: No data returned from view');
                    addResult('info', 'The assistant routing service will not find this subdomain');
                } else {
                    addResult('success', '✅ Verification successful! Data is properly configured');
                    addResult('info', 'Assistant routing service should now work for assistant1test');
                    addResult('info', 'Complete data:');
                    addResult('info', JSON.stringify(data[0], null, 2));
                    
                    // Show what the assistant config would look like
                    const mapping = data[0];
                    const assistantConfig = {
                        id: mapping.attorney_id,
                        assistant_id: mapping.assistant_id,
                        attorney_id: mapping.attorney_id,
                        subdomain: mapping.subdomain,
                        firmName: mapping.firm_name || "LegalScout",
                        name: mapping.firm_name,
                        email: mapping.email,
                        vapi_assistant_id: mapping.assistant_id,
                        current_assistant_id: mapping.assistant_id,
                        is_primary_assistant: mapping.is_primary,
                        isActive: true,
                        loadedVia: 'assistant_routing_service',
                        vapiSyncStatus: 'assistant_subdomain_mapped'
                    };
                    
                    addResult('success', '🎯 Expected assistant config:');
                    addResult('info', JSON.stringify(assistantConfig, null, 2));
                }
                
            } catch (error) {
                addResult('error', `❌ Verification failed: ${error.message}`);
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
            
            // Auto-scroll to bottom
            div.scrollIntoView({ behavior: 'smooth' });
        }

        // Auto-run check on load
        setTimeout(() => {
            addResult('info', '🏁 Database setup tool loaded.');
            addResult('info', `Target subdomain: ${TEST_DATA.subdomain}`);
            addResult('info', `Target assistant ID: ${TEST_DATA.assistantId}`);
            addResult('info', `Target attorney ID: ${TEST_DATA.attorneyId}`);
            addResult('warning', '⚠️ Click "Check Existing Data" first to see what needs to be created');
        }, 100);
    </script>
</body>
</html>
