import React from 'react';

const VapiStatusSummary = ({ logs }) => {
  // Analyze logs to determine status
  const analyzeStatus = () => {
    if (!logs || logs.length === 0) {
      return { status: 'unknown', message: 'No logs available' };
    }

    const hasConnectionAttempt = logs.some(log => 
      log.message.includes('Attempting MCP connection')
    );
    
    const hasMcpFailure = logs.some(log => 
      log.message.includes('Vapi connection failed') && 
      log.data?.error === 'An invalid or illegal string was specified'
    );
    
    const hasAssistantCreation = logs.some(log => 
      log.message.includes('Creating new assistant')
    );
    
    const hasAssistantSuccess = logs.some(log => 
      log.message.includes('Assistant created successfully') ||
      log.message.includes('Assistant verified in Vapi')
    );

    if (hasAssistantSuccess) {
      return {
        status: 'success',
        message: 'Vapi integration working correctly via Direct API',
        details: [
          '✅ Assistant creation/verification successful',
          '✅ Direct API connection working',
          hasMcpFailure ? '⚠️ MCP connection failed (expected in many environments)' : '✅ MCP connection working',
          '✅ Field synchronization operational'
        ]
      };
    } else if (hasAssistantCreation) {
      return {
        status: 'pending',
        message: 'Assistant creation in progress',
        details: ['🔄 Creating assistant...']
      };
    } else if (hasConnectionAttempt) {
      return {
        status: 'connecting',
        message: 'Attempting to connect to Vapi',
        details: ['🔄 Connecting to Vapi services...']
      };
    } else {
      return {
        status: 'unknown',
        message: 'No Vapi activity detected',
        details: ['❓ No Vapi operations found in logs']
      };
    }
  };

  const { status, message, details } = analyzeStatus();

  const getStatusColor = () => {
    switch (status) {
      case 'success': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'connecting': return '#3b82f6';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success': return '✅';
      case 'pending': return '🔄';
      case 'connecting': return '🔗';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  return (
    <div style={{
      padding: '20px',
      border: `2px solid ${getStatusColor()}`,
      borderRadius: '8px',
      backgroundColor: '#f9fafb',
      marginBottom: '20px'
    }}>
      <h3 style={{ 
        margin: '0 0 10px 0', 
        color: getStatusColor(),
        display: 'flex',
        alignItems: 'center',
        gap: '10px'
      }}>
        <span style={{ fontSize: '24px' }}>{getStatusIcon()}</span>
        Vapi Integration Status
      </h3>
      
      <p style={{ 
        margin: '0 0 15px 0', 
        fontSize: '16px',
        fontWeight: 'bold',
        color: getStatusColor()
      }}>
        {message}
      </p>

      {details && details.length > 0 && (
        <ul style={{ 
          margin: '0', 
          paddingLeft: '20px',
          color: '#374151'
        }}>
          {details.map((detail, index) => (
            <li key={index} style={{ marginBottom: '5px' }}>
              {detail}
            </li>
          ))}
        </ul>
      )}

      {status === 'success' && (
        <div style={{
          marginTop: '15px',
          padding: '10px',
          backgroundColor: '#ecfdf5',
          borderRadius: '6px',
          border: '1px solid #10b981'
        }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#065f46' }}>
            🎉 Integration Working Correctly
          </h4>
          <p style={{ margin: '0', fontSize: '14px', color: '#047857' }}>
            Your Vapi integration is functioning properly. The MCP connection failure is normal 
            and expected in browser environments. The system automatically falls back to the 
            Direct API which provides full functionality.
          </p>
        </div>
      )}

      {status === 'unknown' && (
        <div style={{
          marginTop: '15px',
          padding: '10px',
          backgroundColor: '#f3f4f6',
          borderRadius: '6px',
          border: '1px solid #6b7280'
        }}>
          <p style={{ margin: '0', fontSize: '14px', color: '#374151' }}>
            Run the comprehensive test to check your Vapi integration status.
          </p>
        </div>
      )}
    </div>
  );
};

export default VapiStatusSummary;
