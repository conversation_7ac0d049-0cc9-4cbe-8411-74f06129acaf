.meeting-scheduler {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 0.75rem;
  background-color: var(--card-background, #ffffff);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Header */
.scheduler-header {
  margin-bottom: 2rem;
  text-align: center;
}

.scheduler-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #1f2937);
  margin-bottom: 0.5rem;
}

.scheduler-description {
  font-size: 1rem;
  color: var(--text-secondary, #4b5563);
  max-width: 600px;
  margin: 0 auto;
}

/* Content */
.scheduler-content {
  flex: 1;
  margin-bottom: 2rem;
}

/* Call Controller Container */
.call-controller-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Instructions */
.instructions {
  padding: 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--instructions-background, rgba(0, 0, 0, 0.02));
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.instructions-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin-bottom: 1rem;
}

.instructions-list {
  padding-left: 1.5rem;
  color: var(--text-secondary, #4b5563);
}

.instructions-list li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Meeting Confirmation */
.meeting-confirmation {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border-radius: 0.5rem;
  background-color: var(--confirmation-background, rgba(16, 185, 129, 0.05));
  border: 1px solid var(--confirmation-border, rgba(16, 185, 129, 0.2));
}

.confirmation-icon {
  color: var(--success-color, #10b981);
  margin-bottom: 1rem;
}

.confirmation-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary, #1f2937);
  margin-bottom: 1.5rem;
}

.confirmation-details {
  margin-bottom: 1.5rem;
  text-align: left;
}

.confirmation-details p {
  margin-bottom: 0.5rem;
  color: var(--text-secondary, #4b5563);
}

.confirmation-message {
  margin-bottom: 1.5rem;
  color: var(--text-secondary, #4b5563);
  font-style: italic;
}

.reset-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--primary-color, #3b82f6);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background-color: var(--primary-color-dark, #2563eb);
}

/* Error */
.scheduling-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border-radius: 0.5rem;
  background-color: var(--error-background, rgba(239, 68, 68, 0.05));
  border: 1px solid var(--error-border, rgba(239, 68, 68, 0.2));
}

.error-icon {
  color: var(--error-color, #ef4444);
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary, #1f2937);
  margin-bottom: 1rem;
}

.error-message {
  margin-bottom: 1.5rem;
  color: var(--text-secondary, #4b5563);
}

.retry-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--primary-color, #3b82f6);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: var(--primary-color-dark, #2563eb);
}

/* Footer */
.scheduler-footer {
  text-align: center;
}

.privacy-note {
  font-size: 0.875rem;
  color: var(--text-tertiary, #6b7280);
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .meeting-scheduler {
    padding: 1.5rem;
  }
  
  .scheduler-title {
    font-size: 1.25rem;
  }
  
  .scheduler-description {
    font-size: 0.875rem;
  }
  
  .instructions {
    padding: 1rem;
  }
  
  .instructions-title {
    font-size: 1rem;
  }
  
  .meeting-confirmation,
  .scheduling-error {
    padding: 1.5rem;
  }
  
  .confirmation-title,
  .error-title {
    font-size: 1.125rem;
  }
}
