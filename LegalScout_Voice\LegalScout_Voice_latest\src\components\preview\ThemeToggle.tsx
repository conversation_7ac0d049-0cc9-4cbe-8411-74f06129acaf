import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ThemeToggleProps {
  isDark: boolean;
  onToggle: () => void;
  primaryColor: string;
}

export const ThemeToggle = ({ isDark, onToggle, primaryColor }: ThemeToggleProps) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onToggle}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="fixed top-2 right-2 p-1 rounded-full shadow-lg z-50 transition-colors"
        style={{
          backgroundColor: isDark ? '#374151' : 'white',
          border: `2px solid ${primaryColor}20`,
          width: '28px',
          height: '28px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <motion.div
          initial={false}
          animate={{ rotate: isDark ? 40 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {isDark ? (
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="white"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
              />
            </svg>
          ) : (
            <svg
              className="w-4 h-4"
              fill="none"
              stroke={primaryColor}
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"
              />
            </svg>
          )}
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="fixed top-10 right-2 px-2 py-1 rounded-lg text-xs shadow-lg z-50 whitespace-nowrap"
            style={{
              backgroundColor: isDark ? '#374151' : 'white',
              color: isDark ? 'white' : 'black',
              border: `1px solid ${primaryColor}20`,
            }}
          >
            Toggle theme
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}; 