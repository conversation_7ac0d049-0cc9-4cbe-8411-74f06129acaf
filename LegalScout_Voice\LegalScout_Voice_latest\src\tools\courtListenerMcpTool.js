/**
 * CourtListener Citation Lookup MCP Tool
 * 
 * This file provides a wrapper for the CourtListener Citation Lookup API
 * to be used with the Vapi MCP server.
 */

const { lookupCitations, formatCitationResults } = require('./courtListenerTool');

/**
 * Register the CourtListener citation lookup tool with an MCP server
 * 
 * @param {object} server - The MCP server instance
 * @param {string} apiKey - Optional CourtListener API key
 */
function registerCourtListenerTool(server, apiKey = null) {
  // Register the citation lookup tool
  server.tool(
    'lookup_legal_citations',
    'Verify and lookup legal citations in text using the CourtListener API',
    {
      text: {
        type: 'string',
        description: 'The text containing legal citations to verify (up to 64,000 characters)'
      },
      format_results: {
        type: 'boolean',
        description: 'Whether to format the results for human readability',
        default: true
      }
    },
    async ({ text, format_results = true }) => {
      try {
        // Call the citation lookup API
        const results = await lookupCitations(text, apiKey);
        
        // Format the results if requested
        const content = format_results 
          ? formatCitationResults(results)
          : JSON.stringify(results, null, 2);
        
        return {
          content: [
            {
              type: 'text',
              text: content
            }
          ]
        };
      } catch (error) {
        console.error('Error in lookup_legal_citations tool:', error);
        return {
          content: [
            {
              type: 'text',
              text: `Error looking up citations: ${error.message}`
            }
          ]
        };
      }
    }
  );
  
  // Register a simplified version for quick citation verification
  server.tool(
    'verify_citation',
    'Quickly verify if a specific legal citation is valid',
    {
      citation: {
        type: 'string',
        description: 'The legal citation to verify (e.g., "410 U.S. 113")'
      }
    },
    async ({ citation }) => {
      try {
        // Call the citation lookup API with just the citation
        const results = await lookupCitations(citation, apiKey);
        
        // Check if the citation is valid
        const isValid = Array.isArray(results) && 
                        results.length > 0 && 
                        results[0].status === 200;
        
        let responseText = '';
        
        if (isValid) {
          const citation = results[0];
          const cluster = citation.clusters && citation.clusters.length > 0 
            ? citation.clusters[0] 
            : null;
          
          responseText = `✓ Valid citation: "${citation.citation}"\n`;
          
          if (cluster) {
            responseText += `Case: ${cluster.case_name || 'Unknown'}\n`;
            responseText += `Court: ${cluster.court_name || 'Unknown'}\n`;
            responseText += `Date: ${cluster.date_filed || 'Unknown'}\n`;
            
            if (cluster.absolute_url) {
              responseText += `Link: https://www.courtlistener.com${cluster.absolute_url}`;
            }
          }
        } else {
          const errorMessage = Array.isArray(results) && results.length > 0
            ? results[0].error_message || 'Invalid citation'
            : 'Invalid citation';
          
          responseText = `✗ ${errorMessage}: "${citation}"`;
        }
        
        return {
          content: [
            {
              type: 'text',
              text: responseText
            }
          ]
        };
      } catch (error) {
        console.error('Error in verify_citation tool:', error);
        return {
          content: [
            {
              type: 'text',
              text: `Error verifying citation: ${error.message}`
            }
          ]
        };
      }
    }
  );
}

module.exports = {
  registerCourtListenerTool
};
