/**
 * Production Routing Fix
 * 
 * This utility ensures that routing works correctly in production by handling
 * the differences between development and production environments.
 */

// Production-safe route detection
const detectCurrentRoute = () => {
  if (typeof window === 'undefined') return '/';
  
  const pathname = window.location.pathname;
  const search = window.location.search;
  
  return pathname + search;
};

// Check if we're in production
const isProduction = () => {
  if (typeof window === 'undefined') return true;
  
  const hostname = window.location.hostname;
  return !hostname.includes('localhost') && !hostname.includes('127.0.0.1');
};

// Production route mapping
const PRODUCTION_ROUTES = {
  '/': 'home',
  '/dashboard': 'dashboard',
  '/about': 'about',
  '/contact': 'contact',
  '/preview': 'preview',
  '/demo': 'demo',
  '/simple-demo': 'simple-demo',
  '/auth': 'auth',
  '/auth/callback': 'auth-callback',
  '/preview-frame': 'preview-frame',
  '/preview-frame-test': 'preview-frame-test',
  '/simple-preview': 'simple-preview',
  '/test-route': 'test-route'
};

// Check if route is valid
const isValidRoute = (pathname) => {
  return Object.keys(PRODUCTION_ROUTES).includes(pathname) || 
         pathname.startsWith('/dashboard/') ||
         pathname.startsWith('/preview/') ||
         pathname.startsWith('/auth/');
};

// Handle production routing
export const handleProductionRouting = () => {
  if (!isProduction()) {
    console.log('[ProductionRoutingFix] Development environment detected, skipping production routing fixes');
    return;
  }

  console.log('[ProductionRoutingFix] Applying production routing fixes...');

  const currentRoute = detectCurrentRoute();
  const pathname = window.location.pathname;

  console.log('[ProductionRoutingFix] Current route:', currentRoute);
  console.log('[ProductionRoutingFix] Pathname:', pathname);

  // Check if current route is valid
  if (!isValidRoute(pathname)) {
    console.warn('[ProductionRoutingFix] Invalid route detected:', pathname);
    console.log('[ProductionRoutingFix] Redirecting to home page...');
    
    // Redirect to home page for invalid routes
    window.history.replaceState({}, '', '/');
    return;
  }

  // Handle specific route fixes
  if (pathname === '/dashboard' && !window.location.search) {
    console.log('[ProductionRoutingFix] Dashboard route detected, ensuring proper initialization...');
    
    // Ensure dashboard loads correctly
    if (typeof window.initializeDashboard === 'function') {
      window.initializeDashboard();
    }
  }

  // Handle preview routes
  if (pathname.startsWith('/preview') || pathname.startsWith('/simple-preview')) {
    console.log('[ProductionRoutingFix] Preview route detected, ensuring proper preview initialization...');
    
    // Ensure preview components load correctly
    if (typeof window.initializePreview === 'function') {
      window.initializePreview();
    }
  }

  console.log('[ProductionRoutingFix] Production routing fixes applied successfully');
};

// Initialize production routing fixes
export const initProductionRouting = () => {
  try {
    // Apply routing fixes immediately
    handleProductionRouting();

    // Also apply on popstate events (back/forward navigation)
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handleProductionRouting);
      
      // Apply on hash changes as well
      window.addEventListener('hashchange', handleProductionRouting);
    }

    console.log('✅ [ProductionRoutingFix] Production routing initialized successfully');
  } catch (error) {
    console.error('🚨 [ProductionRoutingFix] Error initializing production routing:', error);
  }
};

// Export utilities
export { detectCurrentRoute, isProduction, isValidRoute, PRODUCTION_ROUTES };

export default {
  handleProductionRouting,
  initProductionRouting,
  detectCurrentRoute,
  isProduction,
  isValidRoute
};
