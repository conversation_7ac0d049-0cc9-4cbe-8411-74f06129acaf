-- Create forwarding_rules table to store call forwarding configuration
CREATE TABLE IF NOT EXISTS public.forwarding_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationships
  attorney_id UUID REFERENCES public.attorneys(id) NOT NULL,
  
  -- Basic Information
  name TEXT NOT NULL,
  forward_to_name TEXT NOT NULL,
  forward_to_phone TEXT NOT NULL,
  forward_to_title TEXT,
  
  -- Condition Configuration
  condition_type TEXT NOT NULL, -- 'time-based', 'keyword-based', 'always'
  
  -- Time-based Condition Fields
  days TEXT[], -- Array of days: ['monday', 'tuesday', etc.]
  start_time TIME,
  end_time TIME,
  timezone TEXT,
  
  -- Keyword-based Condition Fields
  keywords TEXT[], -- Array of keywords to trigger forwarding
  
  -- Status
  is_active BOOLEAN DEFAULT true
);

-- Add index for attorney_id for faster lookups
CREATE INDEX IF NOT EXISTS forwarding_rules_attorney_id_idx ON public.forwarding_rules (attorney_id);

-- Add RLS policies for forwarding_rules
ALTER TABLE public.forwarding_rules ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view their own forwarding rules
CREATE POLICY "Users can view their own forwarding rules" ON public.forwarding_rules
  FOR SELECT
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to insert their own forwarding rules
CREATE POLICY "Users can insert their own forwarding rules" ON public.forwarding_rules
  FOR INSERT
  WITH CHECK (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to update their own forwarding rules
CREATE POLICY "Users can update their own forwarding rules" ON public.forwarding_rules
  FOR UPDATE
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to delete their own forwarding rules
CREATE POLICY "Users can delete their own forwarding rules" ON public.forwarding_rules
  FOR DELETE
  USING (auth.uid() = attorney_id);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_forwarding_rules_updated_at
BEFORE UPDATE ON public.forwarding_rules
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add comment to the table for documentation
COMMENT ON TABLE public.forwarding_rules IS 'Stores call forwarding rules for attorneys';
