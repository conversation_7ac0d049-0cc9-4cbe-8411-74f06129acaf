/**
 * Utility functions for mapping between different configuration formats
 * This ensures consistent field naming between the UI, preview, and database
 */

/**
 * Maps preview component properties to database fields
 * @param {Object} previewConfig - Configuration from the preview component
 * @returns {Object} - Configuration formatted for database storage
 */
export const mapPreviewToDatabase = (previewConfig) => {
  // Create a base object with all fields set to null or empty arrays
  // This ensures we're only sending fields that match the database schema
  const baseConfig = {
    // Basic information
    firm_name: null,
    title_text: null,
    name: null,
    subdomain: null,

    // Practice information
    practice_areas: [],
    practice_description: null,

    // Visual customization
    primary_color: null,
    secondary_color: null,
    button_color: null,
    background_color: null,
    background_opacity: null,
    button_text: null,
    button_opacity: null,
    practice_area_background_opacity: null,
    text_background_color: null,

    // Media URLs
    logo_url: null,
    profile_image: null,
    button_image: null,

    // Vapi configuration
    welcome_message: null,
    information_gathering: null,
    vapi_instructions: null,
    vapi_context: null,
    vapi_assistant_id: null,

    // Contact information
    office_address: null,
    phone: null,
    scheduling_link: null,
    email: null,

    // Other fields
    voice_id: null,
    ai_model: null,
    is_active: true,

    // Custom fields configuration
    custom_fields: [],
    summary_prompt: null,
    structured_data_prompt: null,
    structured_data_schema: null
  };

  // Map the preview config to the database fields
  return {
    ...baseConfig,
    // Basic information
    firm_name: previewConfig.firmName,
    title_text: previewConfig.titleText,
    name: previewConfig.attorneyName,
    subdomain: previewConfig.subdomain,

    // Practice information
    practice_areas: previewConfig.practiceAreas || [],
    practice_description: previewConfig.practiceDescription,

    // Visual customization
    primary_color: previewConfig.primaryColor,
    secondary_color: previewConfig.secondaryColor,
    button_color: previewConfig.buttonColor,
    background_color: previewConfig.backgroundColor,
    background_opacity: previewConfig.backgroundOpacity,
    button_text: previewConfig.buttonText,
    button_opacity: previewConfig.buttonOpacity,
    practice_area_background_opacity: previewConfig.practiceAreaBackgroundOpacity,
    text_background_color: previewConfig.textBackgroundColor,

    // Media URLs
    logo_url: previewConfig.logoUrl,
    profile_image: previewConfig.mascot,
    button_image: previewConfig.buttonImageUrl,

    // Vapi configuration
    welcome_message: previewConfig.welcomeMessage,
    information_gathering: previewConfig.informationGathering,
    vapi_instructions: previewConfig.vapiInstructions,
    vapi_context: previewConfig.vapiContext,

    // Contact information (if provided)
    office_address: previewConfig.address,
    phone: previewConfig.phone,
    scheduling_link: previewConfig.schedulingLink,
    email: previewConfig.email,

    // Custom fields configuration
    custom_fields: previewConfig.customFields,
    summary_prompt: previewConfig.summaryPrompt,
    structured_data_prompt: previewConfig.structuredDataPrompt,
    structured_data_schema: previewConfig.structuredDataSchema
  };
};

/**
 * Maps database fields to preview component properties
 * @param {Object} dbConfig - Configuration from the database
 * @returns {Object} - Configuration formatted for the preview component
 */
export const mapDatabaseToPreview = (dbConfig) => {
  return {
    // CORRECTED MAPPING: Agent name vs Firm name
    // Agent Name (what shows in preview) - prioritize assistant_name, then title_text, then firm_name
    firmName: dbConfig.assistant_name || dbConfig.titleText || dbConfig.title_text || dbConfig.firmName || dbConfig.firm_name || 'Your Law Firm',
    titleText: dbConfig.assistant_name || dbConfig.titleText || dbConfig.title_text || dbConfig.firmName || dbConfig.firm_name || '',

    // Actual firm name (for reference)
    actualFirmName: dbConfig.firmName || dbConfig.firm_name || 'Your Law Firm',
    attorneyName: dbConfig.attorneyName || dbConfig.name || 'Your Name',

    // Practice information
    practiceAreas: dbConfig.practiceAreas || dbConfig.practice_areas || [],
    practiceDescription: dbConfig.practiceDescription || dbConfig.practice_description || 'Your AI legal assistant is ready to help',

    // Visual customization
    primaryColor: dbConfig.primaryColor || dbConfig.primary_color || '#4B74AA',
    secondaryColor: dbConfig.secondaryColor || dbConfig.secondary_color || '#2C3E50',
    buttonColor: dbConfig.buttonColor || dbConfig.button_color || '#D85722',
    backgroundColor: dbConfig.backgroundColor || dbConfig.background_color || '#1a1a1a',
    backgroundOpacity: dbConfig.backgroundOpacity !== undefined ? dbConfig.backgroundOpacity : (dbConfig.background_opacity !== undefined ? dbConfig.background_opacity : 0.9),
    buttonText: dbConfig.buttonText || dbConfig.button_text || 'Start Consultation',
    buttonOpacity: dbConfig.buttonOpacity !== undefined ? dbConfig.buttonOpacity : (dbConfig.button_opacity !== undefined ? dbConfig.button_opacity : 1),
    practiceAreaBackgroundOpacity: dbConfig.practiceAreaBackgroundOpacity !== undefined ? dbConfig.practiceAreaBackgroundOpacity : (dbConfig.practice_area_background_opacity !== undefined ? dbConfig.practice_area_background_opacity : 0.1),
    textBackgroundColor: dbConfig.textBackgroundColor || dbConfig.text_background_color || '#634C38',
    theme: dbConfig.theme || 'dark',

    // Media URLs
    logoUrl: dbConfig.logoUrl || dbConfig.logo_url || '',
    mascot: dbConfig.mascot || dbConfig.profile_image || '',
    buttonImageUrl: dbConfig.buttonImageUrl || dbConfig.button_image || '',

    // Vapi configuration
    welcomeMessage: dbConfig.welcomeMessage || dbConfig.welcome_message || "Hello! I'm Scout, your legal assistant. How can I help you today?",
    informationGathering: dbConfig.informationGathering || dbConfig.information_gathering || "Tell me about your situation, and I'll help find the right solution for you.",
    vapiInstructions: dbConfig.vapiInstructions || dbConfig.vapi_instructions || '',
    vapiContext: dbConfig.vapiContext || dbConfig.vapi_context || '',
    vapiAssistantId: dbConfig.vapiAssistantId || dbConfig.vapi_assistant_id || null,
    vapi_assistant_id: dbConfig.vapiAssistantId || dbConfig.vapi_assistant_id || null, // Include both formats for compatibility

    // Contact information
    address: dbConfig.address || dbConfig.office_address || '',
    phone: dbConfig.phone || '',
    schedulingLink: dbConfig.schedulingLink || dbConfig.scheduling_link || '',

    // Custom fields configuration
    customFields: dbConfig.customFields || dbConfig.custom_fields || [],
    summaryPrompt: dbConfig.summaryPrompt || dbConfig.summary_prompt || '',
    structuredDataPrompt: dbConfig.structuredDataPrompt || dbConfig.structured_data_prompt || '',
    structuredDataSchema: dbConfig.structuredDataSchema || dbConfig.structured_data_schema || null,
  };
};

/**
 * Maps database fields to a format suitable for the Vapi assistant
 * @param {Object} dbConfig - Configuration from the database
 * @returns {Object} - Configuration formatted for Vapi
 */
export const mapDatabaseToVapi = (dbConfig) => {
  // Generate structured data schema from custom fields if available
  let structuredDataSchema = null;
  if (dbConfig.custom_fields) {
    try {
      // If stored as string, parse it
      const customFields = typeof dbConfig.custom_fields === 'string'
        ? JSON.parse(dbConfig.custom_fields)
        : dbConfig.custom_fields;

      // Use schema generator if available, otherwise create a basic schema
      if (typeof window.generateStructuredDataSchema === 'function') {
        structuredDataSchema = window.generateStructuredDataSchema(customFields);
      } else {
        // Basic schema with standard client information
        structuredDataSchema = {
          type: "object",
          properties: {
            clientName: {
              type: "string",
              description: "The client's full name"
            },
            contactInfo: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  description: "Client's email address"
                },
                phone: {
                  type: "string",
                  description: "Client's phone number"
                }
              },
              description: "Client's contact information"
            },
            legalIssue: {
              type: "string",
              description: "The primary legal issue or concern"
            },
            practiceArea: {
              type: "string",
              description: "The relevant practice area (e.g., Personal Injury, Family Law)"
            }
          },
          required: ["legalIssue", "practiceArea"]
        };

        // Add custom fields to the schema
        if (Array.isArray(customFields)) {
          customFields.forEach(field => {
            if (!field.name) return; // Skip fields without names

            structuredDataSchema.properties[field.name] = {
              type: field.type || "string",
              description: field.description || field.name
            };

            // Add enum values if applicable
            if (field.type === 'enum' && field.options?.length > 0) {
              structuredDataSchema.properties[field.name].enum = field.options;
            }

            // Add to required fields if marked as required
            if (field.required) {
              if (!structuredDataSchema.required) {
                structuredDataSchema.required = [];
              }
              structuredDataSchema.required.push(field.name);
            }
          });
        }
      }
    } catch (error) {
      console.error('Error generating schema from custom fields:', error);
    }
  }

  // Create the Vapi configuration
  const config = {
    assistantName: `${dbConfig.firm_name} Legal Assistant`,
    firstMessage: dbConfig.welcome_message || "Hello, I'm your legal assistant. How can I help you today?",
    instructions: dbConfig.vapi_instructions || `You are a legal assistant for ${dbConfig.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
    voiceId: dbConfig.voice_id || "sarah",
    model: dbConfig.ai_model || "gpt-4o",
  };

  // Add analysis configuration if available
  if (dbConfig.summary_prompt || dbConfig.structured_data_prompt || structuredDataSchema) {
    config.analysis = {};

    // Add summary configuration
    if (dbConfig.summary_prompt) {
      config.analysis.summary = {
        prompt: dbConfig.summary_prompt
      };
    }

    // Add structured data configuration
    if (dbConfig.structured_data_prompt || structuredDataSchema) {
      config.analysis.structuredData = {};

      if (dbConfig.structured_data_prompt) {
        config.analysis.structuredData.prompt = dbConfig.structured_data_prompt;
      }

      if (structuredDataSchema) {
        config.analysis.structuredData.schema = structuredDataSchema;
      }
    }
  }

  return config;
};
