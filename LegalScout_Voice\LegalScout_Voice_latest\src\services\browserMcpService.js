/**
 * Browser MCP Service
 * 
 * This service provides integration with the browser MCP server running at docs.browsermcp.io,
 * allowing programmatic control of browser actions through the Model Context Protocol.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { createDebugger } from '../utils/debugConfig';

// Create debugger for this service
const debug = createDebugger('browserMcpService');

class BrowserMcpService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.serverUrl = 'https://docs.browsermcp.io/sse';
  }

  /**
   * Initialize the MCP client and connect to the browser MCP server
   * @returns {Promise<boolean>} - Connection success status
   */
  async initialize() {
    try {
      // Initialize MCP client
      this.client = new Client({
        name: 'legalscout-browser-client',
        version: '1.0.0',
      });
      
      // Create SSE transport for connection to browser MCP server
      const transport = new SSEClientTransport({
        url: this.serverUrl
      });
      
      debug.log('Connecting to Browser MCP server at', this.serverUrl);
      await this.client.connect(transport);
      
      this.connected = true;
      debug.log('Connected successfully to Browser MCP server');
      
      return true;
    } catch (error) {
      debug.error('Error connecting to Browser MCP server:', error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Disconnect from the browser MCP server
   */
  async disconnect() {
    if (this.client && this.connected) {
      try {
        await this.client.close();
        debug.log('Disconnected from Browser MCP server');
      } catch (error) {
        debug.error('Error disconnecting from Browser MCP server:', error);
      } finally {
        this.connected = false;
        this.client = null;
      }
    }
  }

  /**
   * Check if the client is connected
   * @returns {boolean} - Connection status
   */
  isClientConnected() {
    return this.connected && this.client !== null;
  }

  /**
   * Check connection status and reconnect if necessary
   * @returns {Promise<boolean>} - Connection status
   */
  async ensureConnection() {
    if (!this.connected) {
      return await this.initialize();
    }
    return this.connected;
  }

  /**
   * Navigate to a URL
   * @param {string} url - URL to navigate to
   * @returns {Promise<Object>} - Response from the browser MCP server
   */
  async navigate(url) {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Browser MCP server');
    }
    
    try {
      const response = await this.client.callTool({
        name: 'browser_navigate_browsermcp',
        arguments: {
          url
        },
      });
      
      return response.content;
    } catch (error) {
      debug.error('Error navigating to URL:', error);
      throw error;
    }
  }

  /**
   * Take a snapshot of the current page
   * @returns {Promise<Object>} - Snapshot data
   */
  async takeSnapshot() {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Browser MCP server');
    }
    
    try {
      const response = await this.client.callTool({
        name: 'browser_snapshot_browsermcp',
        arguments: {},
      });
      
      return response.content;
    } catch (error) {
      debug.error('Error taking snapshot:', error);
      throw error;
    }
  }

  /**
   * Click on an element
   * @param {string} element - Human-readable element description
   * @param {string} ref - Element reference from snapshot
   * @returns {Promise<Object>} - Response from the browser MCP server
   */
  async clickElement(element, ref) {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Browser MCP server');
    }
    
    try {
      const response = await this.client.callTool({
        name: 'browser_click_browsermcp',
        arguments: {
          element,
          ref
        },
      });
      
      return response.content;
    } catch (error) {
      debug.error('Error clicking element:', error);
      throw error;
    }
  }

  /**
   * Type text into an element
   * @param {string} element - Human-readable element description
   * @param {string} ref - Element reference from snapshot
   * @param {string} text - Text to type
   * @param {boolean} submit - Whether to submit after typing
   * @returns {Promise<Object>} - Response from the browser MCP server
   */
  async typeText(element, ref, text, submit = false) {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Browser MCP server');
    }
    
    try {
      const response = await this.client.callTool({
        name: 'browser_type_browsermcp',
        arguments: {
          element,
          ref,
          text,
          submit
        },
      });
      
      return response.content;
    } catch (error) {
      debug.error('Error typing text:', error);
      throw error;
    }
  }

  /**
   * Take a screenshot of the current page
   * @returns {Promise<Object>} - Screenshot data
   */
  async takeScreenshot() {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Browser MCP server');
    }
    
    try {
      const response = await this.client.callTool({
        name: 'browser_screenshot_browsermcp',
        arguments: {},
      });
      
      return response.content;
    } catch (error) {
      debug.error('Error taking screenshot:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const browserMcpService = new BrowserMcpService();

// Export the class for testing or custom instantiation
export default BrowserMcpService;
