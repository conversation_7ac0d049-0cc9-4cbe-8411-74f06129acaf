/**
 * Bulk Delete Vapi Assistants
 * 
 * This script uses the Vapi API directly to delete orphaned assistants
 */

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// The one assistant that should be kept (linked to "damon" attorney)
const KEEP_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

async function makeVapiRequest(endpoint, method = 'GET', body = null) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json'
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  console.log(`Making ${method} request to: ${endpoint}`);
  
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  if (response.status === 204) {
    return null; // No content for successful delete
  }

  return await response.json();
}

async function listAllAssistants() {
  try {
    console.log('📋 Fetching ALL assistants from Vapi with pagination...');

    let allAssistants = [];
    let hasMore = true;
    let batchCount = 0;
    const limit = 1000; // Maximum allowed by API

    while (hasMore) {
      batchCount++;
      console.log(`   Fetching batch ${batchCount} (limit: ${limit})...`);

      let endpoint = `/assistant?limit=${limit}`;

      // If we have assistants already, use the oldest one's createdAt as a filter
      if (allAssistants.length > 0) {
        const oldestAssistant = allAssistants[allAssistants.length - 1];
        const createdAtFilter = encodeURIComponent(oldestAssistant.createdAt);
        endpoint = `/assistant?limit=${limit}&createdAtLt=${createdAtFilter}`;
      }

      const batch = await makeVapiRequest(endpoint);
      console.log(`   Retrieved ${batch.length} assistants in batch ${batchCount}`);

      if (batch.length === 0) {
        hasMore = false;
      } else {
        allAssistants = allAssistants.concat(batch);

        // If we got fewer than the limit, we've reached the end
        if (batch.length < limit) {
          hasMore = false;
        }
      }

      // Small delay between requests
      if (hasMore) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    console.log(`Found ${allAssistants.length} total assistants in Vapi`);
    return allAssistants;

  } catch (error) {
    console.error('Error listing assistants:', error);
    throw error;
  }
}

async function deleteAssistant(assistantId) {
  try {
    console.log(`🗑️  Deleting assistant: ${assistantId}`);
    const result = await makeVapiRequest(`/assistant/${assistantId}`, 'DELETE');
    console.log(`✅ Successfully deleted assistant: ${assistantId}`);
    return { success: true, result };
  } catch (error) {
    console.error(`❌ Error deleting assistant ${assistantId}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function bulkDeleteOrphanedAssistants(dryRun = true) {
  console.log(`🧹 ${dryRun ? 'DRY RUN - ' : ''}Bulk deleting orphaned Vapi assistants...\n`);
  
  try {
    // Get all assistants
    const allAssistants = await listAllAssistants();
    
    // Identify orphaned assistants
    const orphanedAssistants = allAssistants.filter(assistant => 
      assistant.id !== KEEP_ASSISTANT_ID
    );
    
    const keptAssistants = allAssistants.filter(assistant => 
      assistant.id === KEEP_ASSISTANT_ID
    );
    
    console.log('\n📊 Analysis:');
    console.log(`   Total assistants: ${allAssistants.length}`);
    console.log(`   ✅ Keep (linked): ${keptAssistants.length}`);
    console.log(`   🗑️  Delete (orphaned): ${orphanedAssistants.length}`);
    
    if (keptAssistants.length > 0) {
      console.log('\n✅ Assistants to KEEP:');
      keptAssistants.forEach(assistant => {
        console.log(`   ${assistant.id} - ${assistant.name} (Created: ${assistant.createdAt})`);
      });
    }
    
    if (orphanedAssistants.length === 0) {
      console.log('\n🎉 No orphaned assistants found!');
      return;
    }
    
    console.log('\n🗑️  Orphaned assistants to DELETE:');
    orphanedAssistants.slice(0, 5).forEach(assistant => {
      console.log(`   ${assistant.id} - ${assistant.name} (Created: ${assistant.createdAt})`);
    });
    if (orphanedAssistants.length > 5) {
      console.log(`   ... and ${orphanedAssistants.length - 5} more`);
    }
    
    if (dryRun) {
      console.log('\n🔍 DRY RUN - No actual deletions performed');
      console.log('   Run with --delete flag to perform actual cleanup');
      return;
    }
    
    // Perform actual deletions
    console.log('\n🚨 PERFORMING ACTUAL DELETIONS...\n');
    
    const results = {
      deleted: [],
      failed: []
    };
    
    for (let i = 0; i < orphanedAssistants.length; i++) {
      const assistant = orphanedAssistants[i];
      console.log(`\nProgress: ${i + 1}/${orphanedAssistants.length}`);
      
      const result = await deleteAssistant(assistant.id);
      
      if (result.success) {
        results.deleted.push(assistant.id);
      } else {
        results.failed.push({ id: assistant.id, error: result.error });
      }
      
      // Small delay between deletions
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Summary
    console.log('\n📊 Cleanup Summary:');
    console.log(`   ✅ Successfully deleted: ${results.deleted.length}`);
    console.log(`   ❌ Failed to delete: ${results.failed.length}`);
    
    if (results.failed.length > 0) {
      console.log('\n❌ Failed deletions:');
      results.failed.forEach(item => console.log(`   - ${item.id}: ${item.error}`));
    }
    
    // Verify final state
    console.log('\n🔍 Verifying final state...');
    const finalAssistants = await listAllAssistants();
    
    const remainingOrphaned = finalAssistants.filter(a => a.id !== KEEP_ASSISTANT_ID);
    
    if (remainingOrphaned.length === 0) {
      console.log('\n🎉 SUCCESS: All orphaned assistants have been cleaned up!');
      console.log(`   Only the linked assistant remains: ${KEEP_ASSISTANT_ID}`);
    } else {
      console.log(`\n⚠️  WARNING: ${remainingOrphaned.length} orphaned assistants still remain`);
      console.log('   This might indicate new assistants were created during cleanup');
    }
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  }
}

// Main execution
async function main() {
  console.log('🚀 Vapi Bulk Assistant Cleanup Tool\n');
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  const shouldDelete = args.includes('--delete');
  
  if (shouldDelete) {
    console.log('⚠️  DELETE MODE ENABLED - This will permanently delete orphaned assistants!\n');
  }
  
  await bulkDeleteOrphanedAssistants(!shouldDelete);
  
  if (!shouldDelete) {
    console.log('\n💡 To perform actual cleanup, run: node scripts/bulk-delete-vapi.js --delete');
  }
}

// Handle fetch for Node.js
async function initializeFetch() {
  if (typeof fetch === 'undefined') {
    const { default: fetch } = await import('node-fetch');
    global.fetch = fetch;
  }
}

async function runScript() {
  await initializeFetch();
  await main();
}

runScript().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
