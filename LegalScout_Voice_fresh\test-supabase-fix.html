<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Fix</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #2d5a2d; border: 1px solid #4a8a4a; }
        .error { background: #5a2d2d; border: 1px solid #8a4a4a; }
        .info { background: #2d2d5a; border: 1px solid #4a4a8a; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #4a8a4a; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #5a9a5a; }
    </style>
</head>
<body>
    <h1>🧪 Supabase Fix Verification Test</h1>
    <p>This test verifies that our Supabase client fix works correctly.</p>
    
    <button onclick="testSupabaseClient()">🔧 Test Supabase Client</button>
    <button onclick="testAssistantRouting()">🎯 Test Assistant Routing</button>
    <button onclick="testFullFlow()">🚀 Test Full Flow</button>
    <button onclick="clearResults()">🧹 Clear Results</button>
    
    <div id="results"></div>

    <script type="module">
        window.testSupabaseClient = async function() {
            addResult('info', '🔧 Testing Supabase Client Creation...');
            
            try {
                // Import our fixed Supabase client
                const { supabase } = await import('./src/lib/supabase.js');
                
                addResult('success', '✅ Supabase client imported successfully');
                
                // Test basic client functionality
                if (supabase && typeof supabase.from === 'function') {
                    addResult('success', '✅ Supabase client has expected methods');
                    
                    // Test a simple query (should not crash)
                    try {
                        const { data, error } = await supabase
                            .from('attorneys')
                            .select('id')
                            .limit(1);
                        
                        if (error) {
                            addResult('info', `ℹ️ Query returned error (expected): ${error.message}`);
                        } else {
                            addResult('success', '✅ Query executed successfully');
                        }
                    } catch (queryError) {
                        addResult('error', `❌ Query failed: ${queryError.message}`);
                    }
                } else {
                    addResult('error', '❌ Supabase client missing expected methods');
                }
                
            } catch (error) {
                addResult('error', `❌ Supabase client test failed: ${error.message}`);
                console.error('Supabase test error:', error);
            }
        };

        window.testAssistantRouting = async function() {
            addResult('info', '🎯 Testing Assistant Routing Service...');
            
            try {
                // Import assistant routing service
                const { assistantRoutingService } = await import('./src/services/assistantRoutingService.js');
                
                addResult('success', '✅ Assistant routing service imported');
                
                // Test getting config for assistant1test
                const config = await assistantRoutingService.getAssistantConfig('assistant1test');
                
                if (config) {
                    addResult('success', `✅ Found assistant config: ${JSON.stringify(config, null, 2)}`);
                } else {
                    addResult('info', 'ℹ️ No assistant config found (may be expected)');
                }
                
            } catch (error) {
                addResult('error', `❌ Assistant routing test failed: ${error.message}`);
                console.error('Assistant routing error:', error);
            }
        };

        window.testFullFlow = async function() {
            addResult('info', '🚀 Testing Full Subdomain Flow...');
            
            try {
                // Simulate the full App.jsx flow
                const subdomainValue = 'assistant1test';
                
                addResult('info', `Testing subdomain: ${subdomainValue}`);
                
                // Test assistant routing service
                const { assistantRoutingService } = await import('./src/services/assistantRoutingService.js');
                const assistantConfig = await assistantRoutingService.getAssistantConfig(subdomainValue);
                
                if (assistantConfig) {
                    addResult('success', `✅ Assistant found via routing service`);
                    addResult('info', `Firm: ${assistantConfig.firmName || 'Unknown'}`);
                    addResult('info', `Assistant ID: ${assistantConfig.assistant_id || 'Unknown'}`);
                } else {
                    addResult('info', 'ℹ️ No assistant found via routing service');
                    
                    // Test fallback method
                    try {
                        const { getAssistantConfigAsync } = await import('./src/utils/previewConfigHandler.js');
                        const fallbackConfig = await getAssistantConfigAsync(subdomainValue);
                        
                        if (fallbackConfig) {
                            addResult('success', '✅ Assistant found via fallback method');
                        } else {
                            addResult('info', 'ℹ️ No assistant found via fallback either');
                        }
                    } catch (fallbackError) {
                        addResult('error', `❌ Fallback method failed: ${fallbackError.message}`);
                    }
                }
                
            } catch (error) {
                addResult('error', `❌ Full flow test failed: ${error.message}`);
                console.error('Full flow error:', error);
            }
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
            
            // Auto-scroll to bottom
            div.scrollIntoView({ behavior: 'smooth' });
        }

        // Auto-run basic test on load
        setTimeout(() => {
            addResult('info', '🏁 Test page loaded. Click buttons above to run tests.');
        }, 100);
    </script>
</body>
</html>
