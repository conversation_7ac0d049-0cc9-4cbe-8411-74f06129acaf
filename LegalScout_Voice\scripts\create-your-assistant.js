#!/usr/bin/env node

/**
 * Create Your Own Vapi Assistant
 * 
 * Creates a new assistant under your Vapi account to replace the one
 * that belongs to scout@legalscout
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🤖 Creating Your Own Vapi Assistant');
console.log('===================================\n');

async function createNewAssistant() {
  console.log('📋 Creating new assistant for your account...');
  
  const assistantConfig = {
    name: "LegalScout Assistant - Damon",
    firstMessage: "Hello! I'm Scout from LegalScout. How can I help you today?",
    voice: {
      provider: "openai",
      voiceId: "alloy"
    },
    model: {
      provider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 500,
      messages: [
        {
          role: "system",
          content: "You are Scout, a helpful legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with appropriate legal services. Be professional, empathetic, and helpful. Ask relevant questions to understand their situation and gather information that would be useful for an attorney consultation."
        }
      ]
    },
    // Add web call configuration
    endCallMessage: "Thank you for contacting LegalScout. We'll be in touch soon!",
    endCallPhrases: ["goodbye", "thank you", "that's all", "end call"],
    recordingEnabled: true,
    hipaaEnabled: false,
    clientMessages: [
      "transcript",
      "hang",
      "function-call"
    ],
    serverMessages: [
      "conversation-update",
      "function-call",
      "hang",
      "speech-update"
    ]
  };
  
  try {
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Failed to create assistant: ${response.status} ${response.statusText}`);
      console.log(`Error details: ${errorText}`);
      return null;
    }
    
    const newAssistant = await response.json();
    
    console.log('✅ Assistant created successfully!');
    console.log(`   Name: ${newAssistant.name}`);
    console.log(`   ID: ${newAssistant.id}`);
    console.log(`   Organization: ${newAssistant.orgId}`);
    
    return newAssistant;
    
  } catch (error) {
    console.log(`❌ Error creating assistant: ${error.message}`);
    return null;
  }
}

async function updateAttorneyRecord(newAssistantId) {
  console.log('\n📝 Updating attorney record with new assistant ID...');
  
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .update({ 
        vapi_assistant_id: newAssistantId,
        updated_at: new Date().toISOString()
      })
      .eq('id', '695b5caf-4884-456d-a3b1-7765427b6095')
      .select();
    
    if (error) {
      console.log(`❌ Failed to update attorney record: ${error.message}`);
      return false;
    }
    
    console.log('✅ Attorney record updated successfully');
    console.log(`   New assistant ID: ${newAssistantId}`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Error updating attorney record: ${error.message}`);
    return false;
  }
}

async function testNewAssistant(assistantId) {
  console.log('\n🧪 Testing new assistant configuration...');
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to fetch assistant: ${response.status} ${response.statusText}`);
      return false;
    }
    
    const assistant = await response.json();
    
    console.log('✅ Assistant configuration verified:');
    console.log(`   Voice: ${assistant.voice?.provider} - ${assistant.voice?.voiceId}`);
    console.log(`   Model: ${assistant.model?.provider} - ${assistant.model?.model}`);
    console.log(`   First Message: "${assistant.firstMessage}"`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Error testing assistant: ${error.message}`);
    return false;
  }
}

async function runAssistantCreation() {
  console.log('🚀 Starting assistant creation process...\n');
  
  // Step 1: Create new assistant
  const newAssistant = await createNewAssistant();
  
  if (!newAssistant) {
    console.log('❌ Failed to create assistant. Cannot proceed.');
    return;
  }
  
  // Step 2: Update attorney record
  const updateSuccess = await updateAttorneyRecord(newAssistant.id);
  
  if (!updateSuccess) {
    console.log('⚠️  Assistant created but failed to update attorney record.');
    console.log(`   Manually update attorney record with assistant ID: ${newAssistant.id}`);
  }
  
  // Step 3: Test the new assistant
  await testNewAssistant(newAssistant.id);
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Clear your browser cache and localStorage');
  console.log('2. Refresh your localhost application');
  console.log('3. Try making a voice call again');
  console.log('4. The assistant should now speak and respond properly');
  
  console.log('\n✅ Assistant creation complete!');
  console.log(`Your new assistant ID: ${newAssistant.id}`);
}

runAssistantCreation().catch(console.error);
