/**
 * Subdomain Manager Component
 * 
 * Allows attorneys to change their subdomain and automatically syncs
 * all assistant configurations and domain-specific settings.
 */

import React, { useState, useEffect } from 'react';
import { useDomainSync } from '../../hooks/useDomainSync';
import './SubdomainManager.css';

const SubdomainManager = ({ attorney, onSubdomainChanged }) => {
  const [newSubdomain, setNewSubdomain] = useState('');
  const [validation, setValidation] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  
  const {
    isUpdating,
    error,
    lastUpdate,
    updateSubdomain,
    syncAssistantForCurrentDomain,
    validateSubdomain,
    clearError
  } = useDomainSync();

  // Initialize with current subdomain
  useEffect(() => {
    if (attorney?.subdomain) {
      setNewSubdomain(attorney.subdomain);
    }
  }, [attorney?.subdomain]);

  // Validate subdomain as user types
  useEffect(() => {
    const validateAsync = async () => {
      if (newSubdomain && newSubdomain !== attorney?.subdomain) {
        const result = await validateSubdomain(newSubdomain, attorney?.id);
        setValidation(result);
      } else {
        setValidation(null);
      }
    };

    const timeoutId = setTimeout(validateAsync, 500);
    return () => clearTimeout(timeoutId);
  }, [newSubdomain, attorney?.subdomain, attorney?.id, validateSubdomain]);

  const handleSubdomainChange = (e) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setNewSubdomain(value);
    clearError();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validation?.available && newSubdomain !== attorney?.subdomain) {
      setShowConfirmation(true);
    }
  };

  const confirmSubdomainChange = async () => {
    try {
      const result = await updateSubdomain(attorney.id, newSubdomain);
      
      if (result.success) {
        setShowConfirmation(false);
        if (onSubdomainChanged) {
          onSubdomainChanged(newSubdomain, result.oldSubdomain);
        }
      }
    } catch (error) {
      console.error('Error updating subdomain:', error);
    }
  };

  const handleSyncAssistant = async () => {
    try {
      await syncAssistantForCurrentDomain(attorney.id);
    } catch (error) {
      console.error('Error syncing assistant:', error);
    }
  };

  const getValidationMessage = () => {
    if (!validation) return null;
    
    if (validation.available) {
      return (
        <div className="validation-message success">
          ✅ Subdomain is available
        </div>
      );
    } else {
      return (
        <div className="validation-message error">
          ❌ {validation.error}
        </div>
      );
    }
  };

  const getPreviewUrl = () => {
    return `https://${newSubdomain || 'your-subdomain'}.legalscout.net`;
  };

  return (
    <div className="subdomain-manager">
      <div className="subdomain-manager-header">
        <h3>Domain Settings</h3>
        <p>Manage your custom subdomain and domain-specific configurations</p>
      </div>

      <form onSubmit={handleSubmit} className="subdomain-form">
        <div className="form-group">
          <label htmlFor="subdomain">Your Subdomain</label>
          <div className="subdomain-input-group">
            <input
              type="text"
              id="subdomain"
              value={newSubdomain}
              onChange={handleSubdomainChange}
              placeholder="yourfirm"
              disabled={isUpdating}
              className={validation && !validation.available ? 'error' : ''}
            />
            <span className="domain-suffix">.legalscout.net</span>
          </div>
          {getValidationMessage()}
        </div>

        <div className="preview-section">
          <label>Preview URL</label>
          <div className="preview-url">
            <a 
              href={getPreviewUrl()} 
              target="_blank" 
              rel="noopener noreferrer"
              className={newSubdomain ? '' : 'disabled'}
            >
              {getPreviewUrl()}
            </a>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            disabled={
              isUpdating || 
              !validation?.available || 
              newSubdomain === attorney?.subdomain
            }
            className="btn-primary"
          >
            {isUpdating ? 'Updating...' : 'Update Subdomain'}
          </button>

          <button
            type="button"
            onClick={handleSyncAssistant}
            disabled={isUpdating || !attorney?.vapi_assistant_id}
            className="btn-secondary"
          >
            {isUpdating ? 'Syncing...' : 'Sync Assistant Config'}
          </button>
        </div>
      </form>

      {error && (
        <div className="error-message">
          <strong>Error:</strong> {error}
          <button onClick={clearError} className="btn-close">×</button>
        </div>
      )}

      {lastUpdate && (
        <div className="success-message">
          <strong>Success!</strong> Subdomain changed from "{lastUpdate.oldSubdomain}" to "{lastUpdate.newSubdomain}"
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="modal-overlay">
          <div className="confirmation-modal">
            <h4>Confirm Subdomain Change</h4>
            <p>
              Are you sure you want to change your subdomain from 
              <strong> {attorney?.subdomain} </strong> to 
              <strong> {newSubdomain}</strong>?
            </p>
            <p className="warning">
              This will update your assistant configuration and may temporarily 
              affect your live site until the changes propagate.
            </p>
            <div className="modal-actions">
              <button 
                onClick={() => setShowConfirmation(false)}
                className="btn-secondary"
                disabled={isUpdating}
              >
                Cancel
              </button>
              <button 
                onClick={confirmSubdomainChange}
                className="btn-primary"
                disabled={isUpdating}
              >
                {isUpdating ? 'Updating...' : 'Confirm Change'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubdomainManager;
