import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CallManagementSection from '../CallManagementSection';
import { vapiMcpService } from '../../../services/vapiMcpService';
import { supabase } from '../../../lib/supabase';

// Mock the vapiMcpService
jest.mock('../../../services/vapiMcpService', () => ({
  vapiMcpService: {
    ensureConnection: jest.fn().mockResolvedValue(true),
    createAssistant: jest.fn().mockResolvedValue({ id: 'test-assistant-id' }),
    listPhoneNumbers: jest.fn().mockResolvedValue([
      { id: 'phone-1', phone_number: '+**********', friendly_name: 'Test Phone' }
    ]),
    createCall: jest.fn().mockResolvedValue({ id: 'test-call-id' })
  }
}));

// Mock the supabase client
jest.mock('../../../lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: { id: 'test-attorney-id' }, error: null })
  }
}));

// Mock the CallHistoryPanel component
jest.mock('../CallHistoryPanel', () => {
  return function MockCallHistoryPanel(props) {
    return (
      <div data-testid="call-history-panel">
        <button 
          data-testid="view-details-button" 
          onClick={() => props.onViewDetails('test-call-id')}
        >
          View Details
        </button>
      </div>
    );
  };
});

// Mock the CallDetailsModal component
jest.mock('../CallDetailsModal', () => {
  return function MockCallDetailsModal(props) {
    return (
      props.isOpen && (
        <div data-testid="call-details-modal">
          <button 
            data-testid="close-modal-button" 
            onClick={props.onClose}
          >
            Close
          </button>
          <div>Call ID: {props.callId}</div>
        </div>
      )
    );
  };
});

describe('CallManagementSection', () => {
  const mockAttorney = {
    id: 'test-attorney-id',
    firm_name: 'Test Law Firm',
    vapi_assistant_id: 'test-assistant-id',
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'Test instructions',
    voice_provider: '11labs',
    voice_id: 'test-voice',
    ai_model: 'gpt-4o'
  };

  const mockAttorneyWithoutAssistant = {
    id: 'test-attorney-id',
    firm_name: 'Test Law Firm',
    vapi_assistant_id: null,
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'Test instructions',
    voice_provider: '11labs',
    voice_id: 'test-voice',
    ai_model: 'gpt-4o'
  };

  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly with attorney data', async () => {
    render(<CallManagementSection attorney={mockAttorney} onError={mockOnError} />);
    
    // Check if the component renders correctly
    expect(screen.getByText('Call Management')).toBeInTheDocument();
    expect(screen.getByText('Make Outbound Call')).toBeInTheDocument();
    expect(screen.getByLabelText('Client Phone Number:')).toBeInTheDocument();
    
    // Wait for phone numbers to load
    await waitFor(() => {
      expect(vapiMcpService.listPhoneNumbers).toHaveBeenCalled();
    });
    
    // Check if the phone number dropdown is rendered
    await waitFor(() => {
      expect(screen.getByLabelText('Caller ID:')).toBeInTheDocument();
      expect(screen.getByText('+********** (Test Phone)')).toBeInTheDocument();
    });
  });

  test('creates a new assistant if attorney does not have one', async () => {
    render(<CallManagementSection attorney={mockAttorneyWithoutAssistant} onError={mockOnError} />);
    
    // Wait for assistant creation
    await waitFor(() => {
      expect(vapiMcpService.createAssistant).toHaveBeenCalled();
    });
    
    // Check if Supabase was updated with the new assistant ID
    await waitFor(() => {
      expect(supabase.from).toHaveBeenCalledWith('attorneys');
      expect(supabase.update).toHaveBeenCalled();
      expect(supabase.eq).toHaveBeenCalledWith('id', 'test-attorney-id');
    });
  });

  test('makes a call when the Call Now button is clicked', async () => {
    render(<CallManagementSection attorney={mockAttorney} onError={mockOnError} />);
    
    // Enter a phone number
    const phoneNumberInput = screen.getByLabelText('Client Phone Number:');
    fireEvent.change(phoneNumberInput, { target: { value: '+9876543210' } });
    
    // Wait for phone numbers to load
    await waitFor(() => {
      expect(vapiMcpService.listPhoneNumbers).toHaveBeenCalled();
    });
    
    // Click the Call Now button
    const callButton = screen.getByText('Call Now');
    fireEvent.click(callButton);
    
    // Check if the call was created
    await waitFor(() => {
      expect(vapiMcpService.createCall).toHaveBeenCalledWith(
        'test-assistant-id',
        '+9876543210',
        { phoneNumberId: 'phone-1' }
      );
    });
    
    // Check if success message is displayed
    await waitFor(() => {
      expect(screen.getByText(/Call initiated successfully/)).toBeInTheDocument();
      expect(screen.getByText(/test-call-id/)).toBeInTheDocument();
    });
  });

  test('schedules a call when the Schedule Call button is clicked', async () => {
    render(<CallManagementSection attorney={mockAttorney} onError={mockOnError} />);
    
    // Enter a phone number
    const phoneNumberInput = screen.getByLabelText('Client Phone Number:');
    fireEvent.change(phoneNumberInput, { target: { value: '+9876543210' } });
    
    // Wait for phone numbers to load
    await waitFor(() => {
      expect(vapiMcpService.listPhoneNumbers).toHaveBeenCalled();
    });
    
    // Click the Schedule Call button
    const scheduleButton = screen.getByText('Schedule Call');
    fireEvent.click(scheduleButton);
    
    // Check if the call was scheduled
    await waitFor(() => {
      expect(vapiMcpService.createCall).toHaveBeenCalled();
      // Check that the third argument contains scheduledAt
      const callArgs = vapiMcpService.createCall.mock.calls[0][2];
      expect(callArgs).toHaveProperty('scheduledAt');
      expect(callArgs).toHaveProperty('phoneNumberId', 'phone-1');
    });
    
    // Check if success message is displayed
    await waitFor(() => {
      expect(screen.getByText(/Call scheduled successfully/)).toBeInTheDocument();
    });
  });

  test('shows error message when call fails', async () => {
    // Mock the createCall function to reject
    vapiMcpService.createCall.mockRejectedValueOnce(new Error('Call failed'));
    
    render(<CallManagementSection attorney={mockAttorney} onError={mockOnError} />);
    
    // Enter a phone number
    const phoneNumberInput = screen.getByLabelText('Client Phone Number:');
    fireEvent.change(phoneNumberInput, { target: { value: '+9876543210' } });
    
    // Wait for phone numbers to load
    await waitFor(() => {
      expect(vapiMcpService.listPhoneNumbers).toHaveBeenCalled();
    });
    
    // Click the Call Now button
    const callButton = screen.getByText('Call Now');
    fireEvent.click(callButton);
    
    // Check if error message is displayed
    await waitFor(() => {
      expect(screen.getByText(/Failed to make call/)).toBeInTheDocument();
      expect(mockOnError).toHaveBeenCalledWith('Failed to make call: Call failed');
    });
  });

  test('opens and closes call details modal', async () => {
    render(<CallManagementSection attorney={mockAttorney} onError={mockOnError} />);
    
    // Click the View Details button in the call history panel
    const viewDetailsButton = screen.getByTestId('view-details-button');
    fireEvent.click(viewDetailsButton);
    
    // Check if the modal is open
    expect(screen.getByTestId('call-details-modal')).toBeInTheDocument();
    expect(screen.getByText('Call ID: test-call-id')).toBeInTheDocument();
    
    // Close the modal
    const closeButton = screen.getByTestId('close-modal-button');
    fireEvent.click(closeButton);
    
    // Check if the modal is closed
    expect(screen.queryByTestId('call-details-modal')).not.toBeInTheDocument();
  });
});
