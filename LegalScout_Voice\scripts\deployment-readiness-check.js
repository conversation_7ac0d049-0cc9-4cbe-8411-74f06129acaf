/**
 * Deployment Readiness Check
 * 
 * This script performs a comprehensive check of deployment readiness
 * for LegalScout Voice application.
 */

import fs from 'fs';
import path from 'path';

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Check results storage
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  checks: []
};

function addCheck(name, status, message, type = 'info') {
  results.checks.push({ name, status, message, type });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

async function checkVapiAssistant() {
  console.log('\n🔍 Checking Vapi Assistant Configuration...');
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      addCheck('Vapi Assistant', 'FAIL', `HTTP ${response.status}: ${response.statusText}`, 'error');
      return;
    }

    const assistant = await response.json();
    
    // Check required fields
    const hasFirstMessage = !!assistant.firstMessage;
    const hasInstructions = !!assistant.model?.messages?.[0]?.content;
    const hasVoice = !!assistant.voice?.voiceId;
    const hasModel = !!assistant.model?.model;
    
    if (hasFirstMessage && hasInstructions && hasVoice && hasModel) {
      addCheck('Vapi Assistant', 'PASS', 'All required fields present');
    } else {
      const missing = [];
      if (!hasFirstMessage) missing.push('firstMessage');
      if (!hasInstructions) missing.push('instructions');
      if (!hasVoice) missing.push('voice');
      if (!hasModel) missing.push('model');
      addCheck('Vapi Assistant', 'FAIL', `Missing: ${missing.join(', ')}`, 'error');
    }
    
  } catch (error) {
    addCheck('Vapi Assistant', 'FAIL', `Error: ${error.message}`, 'error');
  }
}

function checkRequiredFiles() {
  console.log('\n📁 Checking Required Files...');
  
  const requiredFiles = [
    'package.json',
    'index.html',
    'vite.config.js',
    'src/main.jsx',
    'src/services/VapiDirectApiService.js',
    'public/disable-automatic-assistant-creation.js'
  ];
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      addCheck(`File: ${file}`, 'PASS', 'File exists');
    } else {
      addCheck(`File: ${file}`, 'FAIL', 'File missing', 'error');
    }
  }
}

function checkBuildOutput() {
  console.log('\n🏗️ Checking Build Output...');
  
  const distPath = 'dist';
  const requiredDistFiles = [
    'dist/index.html',
    'dist/assets'
  ];
  
  if (!fs.existsSync(distPath)) {
    addCheck('Build Output', 'FAIL', 'dist directory not found - run npm run build', 'error');
    return;
  }
  
  for (const file of requiredDistFiles) {
    if (fs.existsSync(file)) {
      addCheck(`Build: ${file}`, 'PASS', 'Build artifact exists');
    } else {
      addCheck(`Build: ${file}`, 'FAIL', 'Build artifact missing', 'error');
    }
  }
  
  // Check if fix scripts were copied
  const fixScriptsInDist = fs.readdirSync(distPath).filter(file => 
    file.startsWith('fix-') || file.includes('-fix.')
  );
  
  if (fixScriptsInDist.length > 0) {
    addCheck('Fix Scripts', 'PASS', `${fixScriptsInDist.length} fix scripts copied to dist`);
  } else {
    addCheck('Fix Scripts', 'WARN', 'No fix scripts found in dist', 'warning');
  }
}

function checkEnvironmentVariables() {
  console.log('\n🔧 Checking Environment Variables...');
  
  const requiredEnvVars = [
    'VITE_VAPI_PRIVATE_KEY',
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  // Check if .env file exists
  if (fs.existsSync('.env')) {
    addCheck('Environment File', 'PASS', '.env file exists');
    
    const envContent = fs.readFileSync('.env', 'utf8');
    for (const envVar of requiredEnvVars) {
      if (envContent.includes(envVar)) {
        addCheck(`Env Var: ${envVar}`, 'PASS', 'Variable defined in .env');
      } else {
        addCheck(`Env Var: ${envVar}`, 'WARN', 'Variable not found in .env', 'warning');
      }
    }
  } else {
    addCheck('Environment File', 'WARN', '.env file not found', 'warning');
  }
}

function checkPackageJson() {
  console.log('\n📦 Checking Package Configuration...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check required scripts
    const requiredScripts = ['build', 'dev', 'preview'];
    for (const script of requiredScripts) {
      if (packageJson.scripts && packageJson.scripts[script]) {
        addCheck(`Script: ${script}`, 'PASS', 'Script defined');
      } else {
        addCheck(`Script: ${script}`, 'FAIL', 'Script missing', 'error');
      }
    }
    
    // Check dependencies
    const criticalDeps = ['react', 'vite', '@vapi-ai/web'];
    for (const dep of criticalDeps) {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        addCheck(`Dependency: ${dep}`, 'PASS', 'Dependency installed');
      } else {
        addCheck(`Dependency: ${dep}`, 'WARN', 'Dependency not found', 'warning');
      }
    }
    
  } catch (error) {
    addCheck('Package.json', 'FAIL', `Error reading package.json: ${error.message}`, 'error');
  }
}

function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 DEPLOYMENT READINESS REPORT');
  console.log('='.repeat(60));
  
  console.log(`\n📊 Summary:`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Warnings: ${results.warnings}`);
  
  const total = results.passed + results.failed + results.warnings;
  const score = Math.round((results.passed / total) * 10);
  
  console.log(`\n🎯 Deployment Readiness Score: ${score}/10`);
  
  if (results.failed === 0) {
    console.log('\n🎉 STATUS: READY FOR DEPLOYMENT!');
  } else if (results.failed <= 2) {
    console.log('\n⚠️  STATUS: MOSTLY READY - Fix critical issues');
  } else {
    console.log('\n❌ STATUS: NOT READY - Multiple issues need fixing');
  }
  
  // Show detailed results
  console.log('\n📋 Detailed Results:');
  for (const check of results.checks) {
    const icon = check.status === 'PASS' ? '✅' : check.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} ${check.name}: ${check.message}`);
  }
  
  // Recommendations
  console.log('\n💡 Next Steps:');
  if (results.failed > 0) {
    console.log('1. Fix all failed checks before deploying');
  }
  if (results.warnings > 0) {
    console.log('2. Review warnings and fix if necessary');
  }
  console.log('3. Test the application locally with npm run preview');
  console.log('4. Deploy to staging environment first');
  console.log('5. Run full integration tests');
  
  return score >= 8 && results.failed === 0;
}

async function main() {
  console.log('🔍 Starting Deployment Readiness Check...');
  
  // Run all checks
  checkRequiredFiles();
  checkBuildOutput();
  checkPackageJson();
  checkEnvironmentVariables();
  await checkVapiAssistant();
  
  // Generate report
  const isReady = generateReport();
  
  // Exit with appropriate code
  process.exit(isReady ? 0 : 1);
}

// Run the check
main().catch(error => {
  console.error('❌ Deployment readiness check failed:', error);
  process.exit(1);
});
