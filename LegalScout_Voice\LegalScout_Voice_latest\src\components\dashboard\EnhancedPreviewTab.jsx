import React, { useState, useEffect } from 'react';
import EnhancedAgentPreview from '../EnhancedAgentPreview';
import { syncAttorneyProfile } from '../../services/EnhancedSyncTools';
import './EnhancedPreviewTab.css';

/**
 * Enhanced Preview Tab Component
 *
 * This component provides a tab for the dashboard that contains the enhanced
 * agent preview. It can be used as a drop-in replacement for the existing
 * preview tab in the dashboard.
 *
 * @param {Object} props
 * @param {Object} props.attorney - The attorney object from Supabase
 * @param {boolean} props.isDarkTheme - Whether to use dark theme
 * @param {Function} props.onToggleTheme - Callback to toggle theme
 */
const EnhancedPreviewTab = ({
  attorney,
  isDarkTheme = false,
  onToggleTheme
}) => {
  // State for error message
  const [error, setError] = useState(null);
  // State for showing preview
  const [showPreview, setShowPreview] = useState(true);
  // State for sync status
  const [syncStatus, setSyncStatus] = useState('idle');

  // Sync attorney profile when component mounts or attorney changes
  useEffect(() => {
    if (!attorney || !attorney.id) {
      return;
    }

    const syncProfile = async () => {
      try {
        setSyncStatus('syncing');
        console.log('[EnhancedPreviewTab] Syncing attorney profile:', attorney.id);

        const syncResult = await syncAttorneyProfile({
          attorneyId: attorney.id
        });

        console.log('[EnhancedPreviewTab] Sync result:', syncResult);

        if (syncResult.success) {
          setSyncStatus('success');

          if (syncResult.action === 'created') {
            console.log('[EnhancedPreviewTab] Created new assistant:', syncResult.assistantId);
          } else if (syncResult.action === 'updated') {
            console.log('[EnhancedPreviewTab] Updated assistant:', syncResult.assistantId);
          }
        } else {
          setSyncStatus('error');
          setError(`Sync error: ${syncResult.message}`);
        }
      } catch (error) {
        console.error('[EnhancedPreviewTab] Error syncing attorney profile:', error);
        setSyncStatus('error');
        setError(`Sync error: ${error.message}`);
      }
    };

    syncProfile();
  }, [attorney]);

  // Handle error
  const handleError = (error) => {
    console.error('Preview error:', error);
    setError(typeof error === 'string' ? error : error.message);
  };

  // Handle toggle preview
  const handleTogglePreview = () => {
    setShowPreview(prev => !prev);
  };

  return (
    <div className="enhanced-preview-tab">
      <div className="preview-tab-header">
        <h2 className="tab-title">Agent Preview</h2>
        <p className="tab-description">
          Preview how your AI assistant will appear and function for potential clients.
        </p>

        {/* Sync status indicator */}
        <div className={`sync-status ${syncStatus}`}>
          {syncStatus === 'syncing' && <span>Syncing assistant configuration...</span>}
          {syncStatus === 'success' && <span>Assistant configuration synced successfully</span>}
          {syncStatus === 'error' && <span>Error syncing assistant configuration</span>}
        </div>

        {error && (
          <div className="preview-error">
            <p>{error}</p>
          </div>
        )}

        <div className="preview-controls">
          <button
            className="theme-toggle-button"
            onClick={onToggleTheme}
          >
            {isDarkTheme ? 'Switch to Light Theme' : 'Switch to Dark Theme'}
          </button>
          <button
            className="preview-toggle-button"
            onClick={handleTogglePreview}
          >
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          <button
            className="sync-button"
            onClick={() => {
              if (attorney && attorney.id) {
                syncAttorneyProfile({
                  attorneyId: attorney.id,
                  forceUpdate: true
                }).then(result => {
                  console.log('[EnhancedPreviewTab] Manual sync result:', result);
                  if (result.success) {
                    setSyncStatus('success');
                  } else {
                    setSyncStatus('error');
                    setError(`Sync error: ${result.message}`);
                  }
                }).catch(error => {
                  console.error('[EnhancedPreviewTab] Manual sync error:', error);
                  setSyncStatus('error');
                  setError(`Sync error: ${error.message}`);
                });
                setSyncStatus('syncing');
              }
            }}
            disabled={syncStatus === 'syncing' || !attorney || !attorney.id}
          >
            {syncStatus === 'syncing' ? 'Syncing...' : 'Sync Assistant'}
          </button>
        </div>
      </div>

      {showPreview && (
        <div className="preview-container">
          <EnhancedAgentPreview
            attorney={attorney}
            isDarkTheme={isDarkTheme}
            onError={handleError}
            showDebugPanel={false}
          />
        </div>
      )}

      <div className="preview-tab-footer">
        <h3>About This Preview</h3>
        <p>
          This preview shows how your AI assistant will appear and function on your website.
          It uses your actual Vapi assistant configuration, so any changes you make to your
          assistant settings will be reflected here.
        </p>
        <p>
          <strong>Note:</strong> The preview uses your browser's microphone and speakers.
          Make sure your browser has permission to access these devices.
        </p>
        <p>
          <strong>Sync Status:</strong> {syncStatus === 'syncing' ? 'Syncing assistant configuration...' :
                                        syncStatus === 'success' ? 'Assistant configuration synced successfully' :
                                        syncStatus === 'error' ? 'Error syncing assistant configuration' :
                                        'Assistant configuration not synced'}
        </p>
        {attorney?.vapi_assistant_id && (
          <p>
            <strong>Assistant ID:</strong> {attorney.vapi_assistant_id}
          </p>
        )}
      </div>
    </div>
  );
};

export default EnhancedPreviewTab;
