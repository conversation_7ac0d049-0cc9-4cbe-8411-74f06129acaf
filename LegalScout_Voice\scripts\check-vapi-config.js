#!/usr/bin/env node

/**
 * Vapi Configuration Checker
 * 
 * Checks the current Vapi configuration to identify webhook setup issues.
 */

import { spawn, execSync } from 'child_process';
import { writeFileSync } from 'fs';
import { platform } from 'os';

console.log('🔍 VAPI CONFIGURATION CHECKER');
console.log('='.repeat(50));

// Check if MCP server is available
function checkMCPServer() {
  console.log('\n📡 Checking MCP server availability...');

  try {
    // Use execSync for simpler cross-platform compatibility
    const isWindows = platform() === 'win32';
    const npxCommand = isWindows ? 'npx.cmd' : 'npx';

    const output = execSync(`${npxCommand} -y @vapi-ai/mcp-server --help`, {
      env: {
        ...process.env,
        VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564'
      },
      timeout: 10000,
      encoding: 'utf8'
    });

    console.log('✅ MCP server is available');
    return true;
  } catch (error) {
    console.log('❌ MCP server not available:', error.message);
    return false;
  }
}

// Check assistant configuration
async function checkAssistantConfig() {
  console.log('\n🤖 Checking assistant configuration...');

  const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

  console.log(`Assistant ID: ${assistantId}`);
  console.log('✅ Assistant configuration noted (MCP check skipped for now)');
}

// Check webhook configuration
function checkWebhookConfig() {
  console.log('\n🔗 Checking webhook configuration...');
  
  const expectedWebhookUrl = process.env.VERCEL_URL 
    ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call`
    : 'http://localhost:5175/api/webhook/vapi-call';
  
  console.log('Expected webhook URL:', expectedWebhookUrl);
  
  // Check if webhook URL is accessible
  fetch(expectedWebhookUrl, { method: 'GET' })
    .then(response => {
      if (response.status === 405) {
        console.log('✅ Webhook endpoint is accessible (returns 405 for GET as expected)');
      } else {
        console.log(`⚠️  Webhook endpoint returned status: ${response.status}`);
      }
    })
    .catch(error => {
      console.log('❌ Webhook endpoint not accessible:', error.message);
    });
}

// Generate configuration report
function generateConfigReport() {
  console.log('\n📊 Generating configuration report...');
  
  const report = `
# Vapi Configuration Report
Generated: ${new Date().toISOString()}

## Current Configuration

### Environment Variables
- VAPI_PRIVATE_KEY: ${process.env.VAPI_PRIVATE_KEY ? '✅ Set' : '❌ Missing'}
- VAPI_PUBLIC_KEY: ${process.env.VAPI_PUBLIC_KEY ? '✅ Set' : '❌ Missing'}
- SUPABASE_URL: ${process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing'}
- SUPABASE_ANON_KEY: ${process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}

### Assistant Configuration
- Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
- Attorney Email: <EMAIL>

### Webhook Configuration
- Expected URL: ${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call` : 'http://localhost:5175/api/webhook/vapi-call'}
- Handler Location: api/webhook/vapi-call/index.js

## Potential Issues

### 1. Webhook Not Configured in Vapi Dashboard
The most likely cause of missing call records is that the webhook URL is not configured in the Vapi dashboard.

**To Fix:**
1. Go to Vapi dashboard (https://dashboard.vapi.ai)
2. Navigate to Settings > Webhooks
3. Add webhook URL: ${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call` : 'http://localhost:5175/api/webhook/vapi-call'}
4. Enable events: call.started, call.ended, call.completed

### 2. Assistant Not Linked to Attorney
The assistant may not be properly linked to the attorney in the database.

**To Fix:**
1. Check attorneys table for assistant_id field
2. Ensure assistant_id matches Vapi assistant ID
3. Update attorney record if needed

### 3. Webhook Handler Issues
The webhook handler may have issues processing incoming calls.

**To Fix:**
1. Check webhook handler logs
2. Test webhook endpoint manually
3. Verify error handling

## Next Steps

1. **Configure Vapi Webhook**
   - Log into Vapi dashboard
   - Set webhook URL and events
   - Test webhook delivery

2. **Test Webhook Manually**
   - Send test POST request to webhook endpoint
   - Verify data processing and storage

3. **Monitor Call Events**
   - Make a test call
   - Check webhook logs
   - Verify data appears in Supabase

## Manual Test Commands

\`\`\`bash
# Test webhook endpoint
curl -X POST ${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call` : 'http://localhost:5175/api/webhook/vapi-call'} \\
  -H "Content-Type: application/json" \\
  -d '{
    "id": "test-call-123",
    "assistant_id": "f9b97d13-f9c4-40af-a660-62ba5925ff2a",
    "status": "completed",
    "duration": 60
  }'

# Check MCP server
npx -y @vapi-ai/mcp-server

# Run diagnostic tests
npm run test:call-sync
\`\`\`
`;

  writeFileSync('VAPI_CONFIG_REPORT.md', report);
  console.log('✅ Configuration report saved to VAPI_CONFIG_REPORT.md');
}

// Main execution
async function main() {
  try {
    const mcpAvailable = checkMCPServer();

    if (mcpAvailable) {
      await checkAssistantConfig();
    }

    checkWebhookConfig();
    generateConfigReport();

    console.log('\n🎉 CONFIGURATION CHECK COMPLETE');
    console.log('='.repeat(50));
    console.log('📋 Check VAPI_CONFIG_REPORT.md for detailed results');
    console.log('🔧 Most likely issue: Webhook not configured in Vapi dashboard');
    console.log('🌐 Go to https://dashboard.vapi.ai to configure webhooks');

  } catch (error) {
    console.error('❌ Configuration check failed:', error.message);
    process.exit(1);
  }
}

main();
