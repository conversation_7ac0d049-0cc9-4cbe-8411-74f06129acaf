 🚀 [PRODUCTION CSP] Environment: DEVELOPMENT
 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
 🚀 [PRODUCTION CSP] Final eval status: WORKING
 🚀 [PRODUCTION] Starting environment validation...
 ✅ [PRODUCTION] Set VITE_SUPABASE_URL
 ✅ [PRODUCTION] Set VITE_SUPABASE_KEY
 ✅ [PRODUCTION] Set VITE_VAPI_PUBLIC_KEY
 ✅ [PRODUCTION] Set VITE_VAPI_SECRET_KEY
 ✅ [PRODUCTION] Environment validation complete
 🧪 [CSP TEST] Running CSP verification tests...
 🧪 [CSP TEST] ✅ Eval test PASSED: 4
 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
 🧪 [CSP TEST] All tests completed
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🚀 [ESSENTIAL] Loading core functionality...
 ✅ [ESSENTIAL] Core functionality loaded
 🚀 [UI] Loading essential UI fixes...
localhost/:334 Uncaught 
(anonymous) @ localhost/:334
(anonymous) @ localhost/:337
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 🚀 Call Sync Diagnostic loaded. Run window.diagnoseCallSync() to start.
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
system-test-integration.js:110 Uncaught 
 [vite] connecting...
 [vite] connected.
 🧪 [CSP TEST] ✅ setTimeout string test PASSED
 [DashboardIframeManager] Iframe observer set up successfully
 ✅ [CleanAuthSolution] Auth state monitoring set up
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
chunk-Q72EVS5P.js?v=14f00103:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
supabase.js:29 Environment variables: Object
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 [Supabase] Created clean fetch from iframe to bypass interceptors
supabase.js:130 Supabase client initialized successfully with proper headers
supabase.js:133 Testing Supabase connection...
supabase.js:168 Supabase client ready for use
supabase.js:176 Attaching Supabase client to window.supabase
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized Object
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1420 [AttorneyProfileManager] Loaded attorney from localStorage: 2fac58aa-5504-466e-8975-c9d8b0ae1c3e
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:410 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 2fac58aa-5504-466e-8975-c9d8b0ae1c3e
AttorneyProfileManager.js:443 [AttorneyProfileManager] Realtime subscription set up using channel API
productionEnvironment.js:156 Uncaught ReferenceError: ENVIRONMENT_CONFIG is not defined
    at productionEnvironment.js:156:16
(anonymous) @ productionEnvironment.js:156
production-cors-fix.js:238 [ProductionCorsFix] 🚀 Initializing all production fixes...
production-cors-fix.js:116 [ProductionCorsFix] 🌍 Ensuring production environment variables...
production-cors-fix.js:141 [ProductionCorsFix] ✅ Environment variables configured
production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
production-cors-fix.js:111 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:146 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:166 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:171 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:188 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:193 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:206 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:250 [ProductionCorsFix] 🎉 All production fixes initialized successfully
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
supabase.js:147 Supabase connection test successful!
production-cors-fix.js:211 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
production-cors-fix.js:224 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
AttorneyProfileManager.js:385 [AttorneyProfileManager] Loading attorney by id: 2fac58aa-5504-466e-8975-c9d8b0ae1c3e
AttorneyProfileManager.js:57 [AttorneyProfileManager] Refreshed attorney data for auto-sync: Object
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 2fac58aa-5504-466e-8975-c9d8b0ae1c3e
AttorneyProfileManager.js:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
