-- Create assistant_subdomains table for mapping subdomains to specific assistants
-- This enables each assistant to have its own subdomain and branding

CREATE TABLE IF NOT EXISTS assistant_subdomains (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assistant_id TEXT NOT NULL UNIQUE,
  subdomain TEXT UNIQUE NOT NULL,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  
  -- Assistant-specific configuration
  is_primary BOOLEAN DEFAULT FALSE, -- Mark one assistant as primary for the attorney
  is_active BOOLEAN DEFAULT TRUE,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one primary assistant per attorney
  CONSTRAINT unique_primary_per_attorney UNIQUE (attorney_id, is_primary) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_subdomain ON assistant_subdomains (subdomain);
CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_assistant_id ON assistant_subdomains (assistant_id);
CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_attorney_id ON assistant_subdomains (attorney_id);

-- Add comments
COMMENT ON TABLE assistant_subdomains IS 'Maps subdomains to specific Vapi assistants, enabling assistant-level routing';
COMMENT ON COLUMN assistant_subdomains.assistant_id IS 'Vapi assistant ID that this subdomain routes to';
COMMENT ON COLUMN assistant_subdomains.subdomain IS 'Subdomain (e.g., "assistant1" for assistant1.legalscout.net)';
COMMENT ON COLUMN assistant_subdomains.attorney_id IS 'Attorney who owns this assistant';
COMMENT ON COLUMN assistant_subdomains.is_primary IS 'Whether this is the primary assistant for the attorney';

-- Function to ensure only one primary assistant per attorney
CREATE OR REPLACE FUNCTION ensure_single_primary_assistant()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting this as primary, unset all other primary assistants for this attorney
  IF NEW.is_primary = TRUE THEN
    UPDATE assistant_subdomains 
    SET is_primary = FALSE, updated_at = NOW()
    WHERE attorney_id = NEW.attorney_id 
      AND assistant_id != NEW.assistant_id 
      AND is_primary = TRUE;
  END IF;
  
  -- Ensure at least one assistant is primary for each attorney
  IF NEW.is_primary = FALSE THEN
    -- Check if this was the only primary assistant
    IF NOT EXISTS (
      SELECT 1 FROM assistant_subdomains 
      WHERE attorney_id = NEW.attorney_id 
        AND assistant_id != NEW.assistant_id 
        AND is_primary = TRUE
    ) THEN
      -- If no other primary exists, keep this one as primary
      NEW.is_primary = TRUE;
    END IF;
  END IF;
  
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for primary assistant management
CREATE OR REPLACE TRIGGER ensure_single_primary_assistant_trigger
  BEFORE INSERT OR UPDATE ON assistant_subdomains
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_primary_assistant();

-- Function to migrate existing attorney subdomains to assistant subdomains
CREATE OR REPLACE FUNCTION migrate_attorney_subdomains_to_assistants()
RETURNS TEXT AS $$
DECLARE
  attorney_record RECORD;
  result_text TEXT := '';
  migrated_count INTEGER := 0;
BEGIN
  -- Loop through all attorneys with subdomains and assistant IDs
  FOR attorney_record IN 
    SELECT id, subdomain, vapi_assistant_id, current_assistant_id, firm_name
    FROM attorneys 
    WHERE subdomain IS NOT NULL 
      AND (vapi_assistant_id IS NOT NULL OR current_assistant_id IS NOT NULL)
  LOOP
    -- Migrate vapi_assistant_id if it exists
    IF attorney_record.vapi_assistant_id IS NOT NULL THEN
      INSERT INTO assistant_subdomains (
        assistant_id, 
        subdomain, 
        attorney_id, 
        is_primary,
        is_active
      ) VALUES (
        attorney_record.vapi_assistant_id,
        attorney_record.subdomain,
        attorney_record.id,
        TRUE, -- Make vapi_assistant_id the primary
        TRUE
      )
      ON CONFLICT (assistant_id) DO NOTHING;
      
      migrated_count := migrated_count + 1;
      result_text := result_text || format('Migrated %s: %s -> %s (primary)' || chr(10), 
        attorney_record.firm_name, 
        attorney_record.subdomain, 
        attorney_record.vapi_assistant_id);
    END IF;
    
    -- Migrate current_assistant_id if it's different from vapi_assistant_id
    IF attorney_record.current_assistant_id IS NOT NULL 
       AND attorney_record.current_assistant_id != attorney_record.vapi_assistant_id THEN
      
      -- Create a unique subdomain for the current assistant
      INSERT INTO assistant_subdomains (
        assistant_id, 
        subdomain, 
        attorney_id, 
        is_primary,
        is_active
      ) VALUES (
        attorney_record.current_assistant_id,
        attorney_record.subdomain || '-alt', -- Add suffix to avoid conflicts
        attorney_record.id,
        FALSE, -- Not primary
        TRUE
      )
      ON CONFLICT (assistant_id) DO NOTHING;
      
      migrated_count := migrated_count + 1;
      result_text := result_text || format('Migrated %s: %s-alt -> %s (secondary)' || chr(10), 
        attorney_record.firm_name, 
        attorney_record.subdomain, 
        attorney_record.current_assistant_id);
    END IF;
  END LOOP;
  
  result_text := format('Migration completed. Migrated %s assistant-subdomain mappings:' || chr(10) || '%s', 
    migrated_count, result_text);
  
  RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Run the migration (comment out after first run)
-- SELECT migrate_attorney_subdomains_to_assistants();

-- Create a view for easy subdomain-to-assistant lookup
CREATE OR REPLACE VIEW v_subdomain_assistant_lookup AS
SELECT 
  asd.subdomain,
  asd.assistant_id,
  asd.is_primary,
  asd.is_active,
  a.id as attorney_id,
  a.firm_name,
  a.email,
  asd.created_at,
  asd.updated_at
FROM assistant_subdomains asd
JOIN attorneys a ON asd.attorney_id = a.id
WHERE asd.is_active = TRUE;

COMMENT ON VIEW v_subdomain_assistant_lookup IS 'Easy lookup view for routing subdomains to assistants';

-- Grant permissions
GRANT SELECT ON v_subdomain_assistant_lookup TO anon, authenticated;
GRANT ALL ON assistant_subdomains TO authenticated;
GRANT USAGE ON SEQUENCE assistant_subdomains_id_seq TO authenticated;
