/**
 * Diagnostic Script for Consultations Issue
 * 
 * This script helps diagnose why consultations aren't showing up in the Briefs page
 * by checking the data flow from Supabase to the frontend.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  console.log('VITE_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('VITE_SUPABASE_KEY:', supabaseKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function diagnoseConsultations() {
  console.log('🔍 Starting Consultations Diagnostic...\n');

  try {
    // 1. Check all attorney records
    console.log('1️⃣ Checking Attorney Records:');
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, email, name, firm_name')
      .order('created_at', { ascending: false });

    if (attorneyError) {
      console.error('❌ Error fetching attorneys:', attorneyError);
      return;
    }

    console.log(`Found ${attorneys.length} attorneys:`);
    attorneys.forEach(attorney => {
      console.log(`  - ${attorney.name} (${attorney.email}) - ID: ${attorney.id}`);
    });
    console.log('');

    // 2. Check all consultations
    console.log('2️⃣ Checking All Consultations:');
    const { data: allConsultations, error: consultationError } = await supabase
      .from('consultations')
      .select('id, attorney_id, client_name, summary, created_at, status')
      .order('created_at', { ascending: false });

    if (consultationError) {
      console.error('❌ Error fetching consultations:', consultationError);
      return;
    }

    console.log(`Found ${allConsultations.length} total consultations:`);
    allConsultations.forEach(consultation => {
      const attorney = attorneys.find(a => a.id === consultation.attorney_id);
      const attorneyName = attorney ? attorney.name : 'Unknown Attorney';
      console.log(`  - ${consultation.client_name} for ${attorneyName} (${consultation.created_at}) - Status: ${consultation.status}`);
    });
    console.log('');

    // 3. Check consultations by attorney
    console.log('3️⃣ Consultations by Attorney:');
    for (const attorney of attorneys) {
      const { data: attorneyConsultations } = await supabase
        .from('consultations')
        .select('*')
        .eq('attorney_id', attorney.id)
        .order('created_at', { ascending: false });

      console.log(`${attorney.name} (${attorney.email}): ${attorneyConsultations?.length || 0} consultations`);
      if (attorneyConsultations && attorneyConsultations.length > 0) {
        attorneyConsultations.forEach(c => {
          console.log(`    - ${c.client_name} (${c.created_at}) - ${c.summary?.substring(0, 50)}...`);
        });
      }
    }
    console.log('');

    // 4. Check call_records table
    console.log('4️⃣ Checking Call Records:');
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('call_id, attorney_id, status, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (callError) {
      console.error('❌ Error fetching call records:', callError);
    } else {
      console.log(`Found ${callRecords.length} recent call records:`);
      callRecords.forEach(record => {
        const attorney = attorneys.find(a => a.id === record.attorney_id);
        const attorneyName = attorney ? attorney.name : 'Unknown Attorney';
        console.log(`  - Call ${record.call_id} for ${attorneyName} - Status: ${record.status} (${record.created_at})`);
      });
    }
    console.log('');

    // 5. Check for authentication issues
    console.log('5️⃣ Checking Authentication Context:');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.log('❌ No authenticated user (this is expected for server-side script)');
    } else {
      console.log('✅ Authenticated user:', user?.email);
    }
    console.log('');

    // 6. Test direct query that the frontend uses
    console.log('6️⃣ Testing Frontend Query Pattern:');
    const damonEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    
    for (const email of damonEmails) {
      const attorney = attorneys.find(a => a.email === email);
      if (attorney) {
        console.log(`Testing query for ${email} (ID: ${attorney.id}):`);
        
        const { data: testConsultations, error: testError } = await supabase
          .from('consultations')
          .select('*')
          .eq('attorney_id', attorney.id)
          .order('created_at', { ascending: false });

        if (testError) {
          console.error(`  ❌ Error: ${testError.message}`);
        } else {
          console.log(`  ✅ Found ${testConsultations.length} consultations`);
          if (testConsultations.length > 0) {
            console.log(`  📋 Latest: ${testConsultations[0].client_name} - ${testConsultations[0].created_at}`);
          }
        }
      } else {
        console.log(`❌ No attorney found for email: ${email}`);
      }
    }

    // 7. Recommendations
    console.log('\n📋 DIAGNOSTIC SUMMARY:');
    console.log('='.repeat(50));
    
    const totalConsultations = allConsultations.length;
    const damonAttorneys = attorneys.filter(a => damonEmails.includes(a.email));
    const damonConsultations = allConsultations.filter(c => 
      damonAttorneys.some(a => a.id === c.attorney_id)
    );

    console.log(`Total Consultations in DB: ${totalConsultations}`);
    console.log(`Damon's Attorney Records: ${damonAttorneys.length}`);
    console.log(`Consultations for Damon: ${damonConsultations.length}`);

    if (damonConsultations.length === 0) {
      console.log('\n❌ ISSUE: No consultations found for any of Damon\'s attorney records');
      console.log('💡 SOLUTION: Check webhook configuration and ensure calls are being processed correctly');
    } else if (damonConsultations.length > 0) {
      console.log('\n✅ CONSULTATIONS FOUND: Data exists in database');
      console.log('💡 ISSUE: Frontend authentication or query logic problem');
      console.log('🔧 CHECK: Ensure the frontend is using the correct attorney ID');
      
      // Show which attorney ID has the data
      const consultationsByAttorney = {};
      damonConsultations.forEach(c => {
        if (!consultationsByAttorney[c.attorney_id]) {
          consultationsByAttorney[c.attorney_id] = 0;
        }
        consultationsByAttorney[c.attorney_id]++;
      });

      console.log('\n📊 Consultations by Attorney ID:');
      Object.entries(consultationsByAttorney).forEach(([attorneyId, count]) => {
        const attorney = attorneys.find(a => a.id === attorneyId);
        console.log(`  ${attorney?.email}: ${count} consultations (ID: ${attorneyId})`);
      });
    }

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic
diagnoseConsultations()
  .then(() => {
    console.log('\n✅ Diagnostic complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Diagnostic failed:', error);
    process.exit(1);
  });
