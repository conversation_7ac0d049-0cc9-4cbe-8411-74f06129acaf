.practice-area-selector {
  margin-bottom: 20px;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
  font-size: 1rem;
}

.label-icon {
  color: var(--primary-color);
  font-size: 1.1rem;
}

.selector-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.practice-area-select {
  flex: 1;
  min-width: 250px;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.practice-area-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

.practice-area-select:disabled {
  background-color: var(--disabled-bg, #f5f5f5);
  color: var(--disabled-text, #999);
  cursor: not-allowed;
}

.apply-template-btn {
  padding: 12px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.apply-template-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(75, 116, 170, 0.3);
}

.apply-template-btn:disabled {
  background-color: var(--disabled-bg, #ccc);
  color: var(--disabled-text, #999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.practice-area-info {
  margin-top: 12px;
  padding: 12px 16px;
  background-color: var(--info-bg, rgba(75, 116, 170, 0.1));
  border: 1px solid var(--info-border, rgba(75, 116, 170, 0.2));
  border-radius: 8px;
}

.info-text {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .selector-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .practice-area-select {
    min-width: auto;
  }
  
  .apply-template-btn {
    justify-content: center;
  }
}

/* Dark mode support */
[data-theme="dark"] .practice-area-select {
  background-color: var(--input-bg-dark, #2d3748);
  border-color: var(--border-color-dark, #4a5568);
  color: var(--text-primary-dark, #e2e8f0);
}

[data-theme="dark"] .practice-area-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.2);
}

[data-theme="dark"] .practice-area-info {
  background-color: var(--info-bg-dark, rgba(75, 116, 170, 0.15));
  border-color: var(--info-border-dark, rgba(75, 116, 170, 0.3));
}

[data-theme="dark"] .practice-area-select:disabled {
  background-color: var(--disabled-bg-dark, #1a202c);
  color: var(--disabled-text-dark, #718096);
}

/* Integration with existing form styles */
.practice-area-selector.form-group {
  margin-bottom: 24px;
}

.practice-area-selector.compact {
  margin-bottom: 16px;
}

.practice-area-selector.compact .selector-group {
  gap: 8px;
}

.practice-area-selector.compact .practice-area-info {
  margin-top: 8px;
  padding: 8px 12px;
}

/* Template selector styling for consistency */
.practice-area-selector.template-style {
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  margin-bottom: 20px;
}

.practice-area-selector.template-style .selector-controls {
  gap: 10px;
}

[data-theme="dark"] .practice-area-selector.template-style {
  background-color: rgba(255, 255, 255, 0.05);
}
