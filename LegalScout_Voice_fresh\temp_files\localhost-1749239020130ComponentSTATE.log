// FIX SCRIPT 6: Deep Component State Analysis
window.deepComponentAnalysis = function() {
  console.log('🔍 FIX SCRIPT 6: Deep Component State Analysis');
  console.log('='.repeat(55));
  
  // Navigate to Data Collection
  const agentTab = Array.from(document.querySelectorAll('button')).find(btn => 
    btn.textContent?.trim() === 'Agent'
  );
  
  if (agentTab) {
    agentTab.click();
    
    setTimeout(() => {
      const dataTab = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('Data Collection')
      );
      
      if (dataTab) {
        dataTab.click();
        
        setTimeout(() => {
          console.log('📋 ANALYZING COMPONENT RENDERING...');
          
          // Check if CustomFieldsTab is actually rendering
          const customFieldsContainer = document.querySelector('.custom-fields-tab');
          
          if (customFieldsContainer) {
            console.log('✅ CustomFieldsTab container found');
            
            // Get React Fiber to inspect component state
            const reactFiberKey = Object.keys(customFieldsContainer).find(key => 
              key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
            );
            
            if (reactFiberKey) {
              const fiber = customFieldsContainer[reactFiberKey];
              console.log('✅ React Fiber found');
              
              // Walk up to find CustomFieldsTab component
              let currentFiber = fiber;
              let customFieldsComponent = null;
              
              while (currentFiber && !customFieldsComponent) {
                if (currentFiber.type?.name === 'CustomFieldsTab' || 
                    currentFiber.elementType?.name === 'CustomFieldsTab') {
                  customFieldsComponent = currentFiber;
                  break;
                }
                currentFiber = currentFiber.return;
              }
              
              if (customFieldsComponent) {
                console.log('✅ CustomFieldsTab React component found');
                
                // Analyze props
                console.log('\n📋 COMPONENT PROPS:');
                const props = customFieldsComponent.memoizedProps || customFieldsComponent.pendingProps;
                if (props) {
                  console.log('- attorney prop:', !!props.attorney);
                  console.log('- onUpdate prop:', !!props.onUpdate);
                  
                  if (props.attorney) {
                    console.log('- attorney.id:', props.attorney.id);
                    console.log('- attorney.summary_prompt:', !!props.attorney.summary_prompt);
                    console.log('- attorney.structured_data_prompt:', !!props.attorney.structured_data_prompt);
                    console.log('- attorney.success_evaluation_prompt:', !!props.attorney.success_evaluation_prompt);
                  }
                } else {
                  console.log('❌ No props found');
                }
                
                // Analyze state hooks
                console.log('\n📋 COMPONENT STATE HOOKS:');
                let currentHook = customFieldsComponent.memoizedState;
                let hookIndex = 0;
                const stateValues = [];
                
                while (currentHook && hookIndex < 15) {
                  const state = currentHook.memoizedState;
                  stateValues.push(state);
                  
                  console.log(`Hook ${hookIndex}:`, typeof state, 
                    Array.isArray(state) ? `Array(${state.length})` : 
                    typeof state === 'string' ? `String(${state.length})` :
                    typeof state === 'object' && state !== null ? 'Object' : state);
                  
                  // Look for our specific state values
                  if (typeof state === 'string' && state.length > 20) {
                    if (state.includes('summary') || state.includes('Summary')) {
                      console.log(`  ⭐ LIKELY SUMMARY PROMPT: "${state.substring(0, 100)}..."`);
                    }
                    if (state.includes('structured') || state.includes('Structured')) {
                      console.log(`  ⭐ LIKELY STRUCTURED PROMPT: "${state.substring(0, 100)}..."`);
                    }
                    if (state.includes('success') || state.includes('Success')) {
                      console.log(`  ⭐ LIKELY SUCCESS PROMPT: "${state.substring(0, 100)}..."`);
                    }
                  }
                  
                  currentHook = currentHook.next;
                  hookIndex++;
                }
                
                // Check for loading/error states
                console.log('\n📋 CHECKING FOR LOADING/ERROR STATES:');
                const loadingStates = stateValues.filter(state => 
                  state === true || state === false || 
                  (typeof state === 'string' && (state.includes('loading') || state.includes('error')))
                );
                console.log('Potential loading/error states:', loadingStates);
                
                // Check if useEffect has run
                console.log('\n📋 CHECKING COMPONENT LIFECYCLE:');
                if (customFieldsComponent.updateQueue) {
                  console.log('- Has pending updates:', !!customFieldsComponent.updateQueue.pending);
                }
                
                // Force a re-render by updating a prop
                console.log('\n🔄 ATTEMPTING FORCED RE-RENDER:');
                try {
                  if (props && props.onUpdate) {
                    // Trigger onUpdate to force parent re-render
                    props.onUpdate({ forceUpdate: Date.now() });
                    console.log('✅ Triggered onUpdate prop');
                  }
                  
                  // Also dispatch a custom event
                  customFieldsContainer.dispatchEvent(new CustomEvent('forceRerender', {
                    detail: { timestamp: Date.now() }
                  }));
                  console.log('✅ Dispatched forceRerender event');
                  
                } catch (error) {
                  console.log('⚠️ Error forcing re-render:', error.message);
                }
                
              } else {
                console.log('❌ CustomFieldsTab React component not found in fiber tree');
              }
            } else {
              console.log('❌ React Fiber not found');
            }
            
            // Check for any error boundaries or suspense
            console.log('\n📋 CHECKING FOR ERROR BOUNDARIES:');
            const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
            console.log(`Found ${errorElements.length} potential error elements`);
            errorElements.forEach((el, i) => {
              console.log(`${i + 1}. ${el.className}: "${el.textContent?.trim().substring(0, 100)}"`);
            });
            
          } else {
            console.log('❌ CustomFieldsTab container (.custom-fields-tab) not found');
            
            // Check what IS being rendered
            console.log('\n📋 CHECKING WHAT IS ACTUALLY RENDERED:');
            const tabContent = document.querySelector('.tab-content');
            if (tabContent) {
              console.log('Tab content classes:', tabContent.className);
              console.log('Tab content children:', tabContent.children.length);
              
              Array.from(tabContent.children).forEach((child, i) => {
                console.log(`Child ${i + 1}: ${child.tagName} .${child.className}`);
              });
              
              // Look for any component that might be rendering instead
              const allComponents = tabContent.querySelectorAll('[class*="tab"], [class*="Tab"], [class*="component"], [class*="Component"]');
              console.log(`\nFound ${allComponents.length} potential components:`);
              allComponents.forEach((comp, i) => {
                console.log(`${i + 1}. ${comp.tagName} .${comp.className}`);
              });
            }
          }
          
          // Final check: Look for any textareas or form fields anywhere
          console.log('\n📋 GLOBAL FORM FIELD SEARCH:');
          const allTextareas = document.querySelectorAll('textarea');
          const allSelects = document.querySelectorAll('select');
          
          console.log(`Found ${allTextareas.length} textareas globally:`);
          allTextareas.forEach((ta, i) => {
            console.log(`${i + 1}. id="${ta.id}" name="${ta.name}" placeholder="${ta.placeholder?.substring(0, 30)}"`);
          });
          
          console.log(`\nFound ${allSelects.length} selects globally:`);
          allSelects.forEach((sel, i) => {
            console.log(`${i + 1}. id="${sel.id}" name="${sel.name}"`);
          });
          
        }, 1500);
      }
    }, 500);
  }
};

// Run the analysis
window.deepComponentAnalysis();
VM13048:3 🔍 FIX SCRIPT 6: Deep Component State Analysis
VM13048:4 =======================================================
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:579 [AgentTab] Logo loaded from attorney data: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwA...
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:579 [AgentTab] Logo loaded from attorney data: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwA...
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 1 new preview iframes in added content
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
undefined
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiLogger.js:103 [19:43:15] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
vapiLogger.js:103 [19:43:15] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
VM13058 emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
VM13060 critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Vapi keys set globally
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:53 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:64 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:68 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:75 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:86 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:116 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:119 🎉 [EMERGENCY] Emergency fixes complete!
VM13063 robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
VM13063 robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
VM13064 disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
VM13064 disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
VM13064 disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
VM13064 disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
VM13065 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM13065 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM13066 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM13066 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM13066 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM13066 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM13067 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM13067 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM13067 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM13068 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM13068 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM13068 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM13069 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM13069 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM13069 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM13070 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM13070 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM13070 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM13071 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM13071 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM13071 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM13071 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM13072 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM13072 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM13072 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM13072 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM13072 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM13072 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
VM13074 production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
VM13075 clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
VM13075 clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
VM13075 clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
VM13075 clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
VM13075 clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
VM13075 clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
VM13075 clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
VM13075 clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
VM13075 clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
VM13075 clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
VM13075 clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
VM13050 client:229 [vite] connecting...
VM13050 client:325 [vite] connected.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:235 Supabase loaded from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:245 Creating Supabase client from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:249 Supabase client created from CDN
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
VM13073 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM12142 SyncContext.jsx:136:24)
    at async Object.callback (VM12110 AuthContext.jsx:164:34)
    at async VM12217 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM12217 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM12217 @supabase_supabase-js.js:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
VM13075 clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
VM13075 clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM12142 SyncContext.jsx:136:24)
    at async Object.callback (VM12110 AuthContext.jsx:164:34)
    at async VM12217 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM12217 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM12217 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM12142 SyncContext.jsx:136:24)
    at async Object.callback (VM12110 AuthContext.jsx:164:34)
    at async VM12217 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM12217 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM12217 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
DashboardNew.jsx:637 [DashboardNew] Sub-tab changed to: customFields
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [19:43:15] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [19:43:15] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiDirectApiService.js:34 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
vapiLogger.js:103 [19:43:16] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
VapiDirectApiService.js:129 ✅ [VapiDirectApiService] Using direct API data (complete)
AgentTab.jsx:504 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
DashboardNew.jsx:666 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:743 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
standalone-attorney-manager-fixed.js:478 [StandaloneAttorneyManager] Updating attorney
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(45)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:531 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
DashboardNew.jsx:746 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:43:16.112Z', subdomain: 'damon', firm_name: 'LegalScout', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996111}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996111}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996111}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996111}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[76660 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:43:16.112Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996168}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996168}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996168}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996168}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996171}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996171}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996171}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996171}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
VapiDirectApiService.js:34 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 [19:43:16] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
 [StandaloneAttorneyManager] Updating attorney
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(45)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:43:16.344Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996347}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996347}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996347}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996347}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996351}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996351}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996351}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996351}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[76660 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[76660 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:43:16.344Z', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996402}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996402}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996402}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996402}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996406}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996406}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996406}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238996406}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
VM13048:23 📋 ANALYZING COMPONENT RENDERING...
VM13048:29 ✅ CustomFieldsTab container found
 ✅ React Fiber found
 ✅ CustomFieldsTab React component found
 
📋 COMPONENT PROPS:
 - attorney prop: true
 - onUpdate prop: true
 - attorney.id: 87756a2c-a398-43f2-889a-b8815684df71
 - attorney.summary_prompt: false
 - attorney.structured_data_prompt: false
 - attorney.success_evaluation_prompt: false
 
📋 COMPONENT STATE HOOKS:
VM13048:83 Hook 0: object Array(0)
VM13048:83 Hook 1: string String(1349)
VM13048:91   ⭐ LIKELY SUMMARY PROMPT: "
Create a concise yet comprehensive legal intake summary that would be valuable for an attorney revi..."
VM13048:83 Hook 2: string String(927)
VM13048:83 Hook 3: string String(1107)
VM13048:97   ⭐ LIKELY SUCCESS PROMPT: "
Evaluate whether this call was successful as a legal intake by determining if the following critica..."
VM13048:83 Hook 4: object Object
VM13048:83 Hook 5: boolean false
VM13048:83 Hook 6: boolean false
VM13048:83 Hook 7: object null
VM13048:83 Hook 8: string String(0)
VM13048:83 Hook 9: boolean false
VM13048:83 Hook 10: object Object
VM13048:106 
📋 CHECKING FOR LOADING/ERROR STATES:
VM13048:111 Potential loading/error states: (3) [false, false, false]
VM13048:114 
📋 CHECKING COMPONENT LIFECYCLE:
VM13048:116 - Has pending updates: false
VM13048:120 
🔄 ATTEMPTING FORCED RE-RENDER:
DashboardNew.jsx:666 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
VM13048:125 ✅ Triggered onUpdate prop
VM13048:132 ✅ Dispatched forceRerender event
VM13048:146 
📋 CHECKING FOR ERROR BOUNDARIES:
VM13048:148 Found 0 potential error elements
VM13048:177 
📋 GLOBAL FORM FIELD SEARCH:
VM13048:181 Found 3 textareas globally:
VM13048:183 1. id="summaryPrompt" name="" placeholder="Provide a concise summary of t"
VM13048:183 2. id="successEvaluationPrompt" name="" placeholder="Evaluate whether this call was"
VM13048:183 3. id="structuredDataPrompt" name="" placeholder="Extract key information from t"
VM13048:186 
Found 1 selects globally:
VM13048:188 1. id="practiceAreaTemplate" name=""
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238997021}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238997021}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238997021}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749238997021}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, mascot, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:512 Using custom logoUrl: data:image/png;base64,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
Button.jsx:209 Button component received mascot URL: data:image/png;base64,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
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
Button.jsx:209 Button component received mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAAB0CAYAAAA4j276AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsEAAA7BAbiRa+0AAAASdEVYdFNvZnR3YXJlAEdyZWVuc2hvdF5VCAUAAOAOSURBVHhe7L0FoGVXdf+/rj8d90nGM/FMPMSIoAkQgrSEUgoULS2lLVShP+i/hQKlpdShtJQKUrS4QwQIceLuyUjG58n1+/9+1r7rzcnLTJiZzCSZcNd9+51ztq69tqzv2XZyZtaR6VGPetSjHvWoRz3q0ZOU8t1rj3rUox71qEc96lGPnqTUA2w96lGPetSjHvWoR09y6gG2HvWoRz3qUY961KMnOfUAW4961KMe9ahHPerRk5x6gK1HPepRj3rUox716ElOPcDWox71qEc96lGPevQkpx5g61GPetSjHvWoRz16klMPsPWoRz3qUY961KMePcmpB9h61KMe9ahHPepRj57k1PvSQY961KMe9ahHPdpvKZcDyiTqdJ66kKY3wtajHvWoRz3qUY/2S8qCNWjy81OJeiNsPerRLyzxvtZOtzuj6Ps6k9/totsID4/sJPMed/LH/0fvaAgfaeAzy1dRppBuJ9xa3fse9egpRI9sRttpB9Ud79tbYE5ekqfw+lRtIYAyRtLims/nbeHChVYul2316tU2NjbW9bl3KNLZFZrM294kyvqpWqY96lGPdkjZbv5RSB1OusoA2NRTJJv2RAzRebQdbHX966quykqyRYGo63J/Lf9PJBFK5B1axFbya86aiq2pe4XJAdTKspZbBz8Cap2m/NRkOg+DdT3q0f5K3nL0z6u4Gx667SSaiyp7vNJEvec5tbycXmkK3TZGkPTDn8fp1hHR/k8ANMDQjBkz7Jd+6Zds8eLFtmrVKuvv77dbb73V/vVf/9WuueYaa7V4sVPO9wA4AbhIp91uW6FQsGc84xl26qmnWrFY9Gfi3rBhgwPEG264we6//373SziIsG9605ts8+bNNnfuXA8TdOWVV9rnP//57tOu00RR9qhHPfpFplAFqbOBUAJ0D95BuLWglzq+gq7bFUXqQFAMwDgf94oOS5oCsJZ8qYN1n8l0ffpTIvzQoeWs6IAtubTcp8BarptaB2jYkEkqK8G6HvVo/6XUWhJNALZoXVioKWCV139seQ1q5VTzscTNm0ref9tblHx1X4wmAFs4PQWor6/PGo2GHXPMMfaJT3zCDj744IkRLcxll11mr3vd6+ymm26asN8dCrCGIZ2jjjrK/vEf/9GOP/54B2xBjOThvmXLFhsfH+/apvCAt2XLlvmVMNgB2ngG3C1fvrzre9cpirJHPerRLwjR6DHxlp4IVRBXAJXeKrvdAwqi3XVGBxRkeMRkdQHxAcPCb9IU3bdKn1Ld7nv7mFuCdDx5jOrUip2WT4JCALLEDQRYfHia6f25Rz3af6mgep/GpFMb8ragdpBuElHnHwbY8tlXFdl6W4uGp1hyEzEl4jbzuD9TgKlDDjnE/uu//suOOOIIB0HYBSCq1+v2nve8xz74wQ/6c7O5+692pAMR75lnnmkf+9jHbNGiRRNpAQIBa8EPoIx04QE37CI8o3FcCYs7/DB9u7sUJdyjHvXoF4Ro9AAfQFHqkqKzj+4AGEXvzphZGk1z6nb46noEpIrWsLLVZOoyDcXo414pwq5foBYdJaMBGMGreOt3AyTsAjpnQf/U+WVVTYoOHraDte1J9LqvHj0VKLU94FiY1AJoCbySNPXU1F3Dfy3akzcgtQQHajLRKPye8LQWTIo74+EpQQAggNrKlSsnRrwCROFWqVR8CpOpSEDSnhBxAaoIv3HjRrvvvvs87iwoBKzF6Blu+A+wxhV/2OOXe+wBebfffns3ld0jSjH6xh71qEe/AMR7HV05DR84xbRjgkNBdHDtCUCHv5ZuUkeBTVcJdLphXBekKc4UllvuZXBz9yA9tAmXxhQAYokD8cIbrf7y7QTF0shaUTopxeujft0ot9tw16Me7b/EIoCcQBYKfqI2M0IWbcjp4fU8l1O7iI1ANAgIL34fLaQbxi8B/vZ/AjANDAzY5z73OTvrrLMmRrQCTMVIFiDrjW98o33lK1/Z4xG2AGdMwZ5yyil2xhln+PP69ett27ZttnTpUp96nT17djeUxC1eIlytVrP//u//tquvvtr5Ys0dvF966aX21a9+tRti9yhKtmd6pmee4kZdeaci0y/TJ5OW8oOIijKl7jXvfgXYJoz6LuEn+cvJLSfY5Eb+H2EUHrc0cap7hUvDY9uN4iOdvExR/gQg3cAbaQAF/Z44cv0KP6BrWSbvvMAzpgA/7q9nemY/NmozOa/fmG478vYz2dAuMQX579MVo/YxYSpdg31ZbaisV6qSDG0ttemnghHw6Rx++OEdAbJOtVrtCBS5GR8f7zQajY6Am9vz/OEPf7gzODi4w3gezQiseTpcw04grFOpVDrlsuSqewxxX3DBBZ3Nmzd3BNDckH6z2fT0P/KRj3SmTZvmfomrVCp5HMSdTW9XTReibydQZZZAhWHHNZ5BkJMp3HHDxHM2jiyFe1AMLfaoR08Voh3EkH32GvU+6jtXNWa/7shEe8re4z/bfnaFaPVpVG37FbtE3MEPy5f1pqq3fmKvFPzJNxwUlW5RfY3eH63IyFinqWeFyLGwttvW88onuzvzJSuU+61QEkRsi/du7MUi/Ke1HCW5ta2smMpWLg+KBdzoQ+gL5Nm5FF+Kr1jok988qVorLw58B2mS8WQZ9ahH+w3RrgSvKtRxXUu8yuiXt5KV8n1qa2XVeNpDWXV8wK954QjaHO2opPbEO1FJ7U4vMr47u6znstpOyRoyTW+v+xvRjulPsn1c2C1ZssSmTJnifSD9KaNaH//4x210dHRiahJ/J554oo9qRb8QcTFl+mgU05lcg4iXETOmN7EnLtK78MILfcRMIM394Rbu73znO21kZMTDYsd0KHEQ955QPtu57UgpRMQkToLZhCLzEH5x5wpz+Av3yHjEHQaKjPEc/rLx9qhH+zPRFiA6FtrU5LodoAuTbVuTKdt+uMdEO9tdAvA0pBxa3o0DerbzVBA4o/PvKzGWpbYou1arbXp3VNoJJLXbUgtFgaviFMuXZ1ozN2wzDjjM5iw50g445Hibf/AxNmfFETZ90QorDM+zVm6KMjpT0U2RGbBmi2E2gFfb6uSBNTd5qZcGikV5lOIBmHV0zRVJi2M9BOo6UkW6WqFfr8CoJIVT2OgMe9Sj/ZF4jStKHeba6TibggwvQ+yWLnTS6tAKAA3Q1a6rXTbUHmsKVdcLU90K7ZqHySu8D4R7rCwWULvVXUvNZGJt6X5E0aYnt20W/n/0ox91e/o/DLsuOSaD4zIg7OgrWeN2+OGHux19LGHohwFNe4ozIhzgCwKQkS7xxiYEzM033+zTsgHk9gZRjAzVeQJZwfAcmQ63cI+Mc6WzDITLPX6h8Esm4h7KKpgduRFn1k+PerS/U4ysUdcxrIdgMezrX/96P+yRzoNt4SxE/cIXvuB+J9Ov//qv22GHHeZtgze8T3/603bxxRf78+61F9onAAieaHsyHUAlK8Jo03JXx18WYGsIQNE14dpi1Aw4VR4SthJAm32gHXbU8VYZmmbbqnU7cNlSRZm3cl9J+alas91Qf9C08dGaNbaO2rYNG+2+O26zjWvusUZ1k1lzVOlJwcB7R2kyssCz+GgorVahbLnKgOULFcsVBNTEBCN8bfHWaUlpobhQVFJeHaWFXKPvyPYpPerRk50K0plMeAKxaJ0O3nSlGmPjK9vQwQAv2gCBaDJY66GEf19PyosQP1qRXmJyehlSGJoXUXhD3s8osEa0aTDDW9/6Vnvve9/r90Gf/OQn7Td/8zftT/7kT+x3f/d33S3CcibbW97yFscn2OEWwGpP+4qIGxocHLTf+73fs3e84x0eJ/09/dDXv/51O++889zP3qIc86koDBTH8573PJs6daozAkNkEOKeM0YeeOABu+uuu+zGG290pRFnoUAhjBBCCAVioR5njhB37LoIJYOfa6+91ocVIxyG+HvUo6cC0S6o71yHh4ft3//9332b+NDQkNd12gxvYdT57Fk+WcIvxIgcfmmHtKuHHnrI7Xed6L37dQGAAZbodNR56Z43+CJArckbfE5gqeSdfyevzq9vmh248ghbuORgO+yY0xSi33Kya+QrNt5sW6lStpaAE9i0Vh+zQhHlUbBivmT5lvqThl7mGmNWH3nI7r31Krvn5qtszZ03Wmdss9JtWZ9kkxcAYz9cozTD+mYvsVPOebEtO+hQG6sJOOYEBksFG9u22VbffZPdeOVPbM0d16vjGTEl5YRcHksn3KMePSHEWxHNkouDLzVLNc28HjCoSpYO1BocGcFrDY4yjEx781Xb1ZVopIGJxdtR2y1lsGrr2lKY/Yhoyxj6TvpJrmCIf/iHf7CXv/zl/kybp0989rOf7S+w559/vv3t3/6tzZ8/393ocxkBO/TQQyf6SrDKY+kjoj+PvgaABkh897vf7Qf3Yofh5ftXfuVXHnN6WcopcRbF2Qc+8AG74IILfG4XRtyxeyVBMo9CAdyhVHDj+cc//rGfT4JQ8INg2QXBIXOzZs1yRlFSZATBRpxBZLxarToC/p//+R9PC9OjHu3vRMOGsu2IlyKG7nHjmcaOe3RKk9tHEH5xi86AETniuvzyy3ezMyD+sgwohwM6ElDDNk2hAN2EurrTkP2DwzYwZbYNrzjaznruC2U/aLnSkI02FKrQp+c+30HaFGjrdGj/bGOvu4JpywEdURDgAwSWpTwquYb1tcessXWN3XbVJXbjpd+1sQ0PCtCNWinXtrqA5KJVZ9sZL36N5abMs3qbl0DWxaW+goncYnPUahvX2A++8ilbfePFckj9Ee5QyKlHPdoviIVnqatIAMxn0NQG9TKUY4S5xPYg6dVpM2
imageStorage.js:121 Processing image URL or ID: data:image/png;base64,[76660 chars]
imageStorage.js:133 Image is a data URL
Button.jsx:231 Processed mascot URL: data:image/png;base64,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
