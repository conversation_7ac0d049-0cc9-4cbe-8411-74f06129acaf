/**
 * <PERSON><PERSON><PERSON> to sync recent Vapi calls and create consultation records
 * 
 * This script fetches recent calls from Vapi and creates consultation records
 * for them in the Supabase database.
 */

import { syncVapiCallsToConsultations } from '../services/consultationSyncService.js';

// Mock Vapi calls data based on what we saw from the MCP server
const recentVapiCalls = [
  {
    id: "70d970de-c386-4f87-92db-d4818ff495eb",
    createdAt: "2025-05-25T02:40:30.492Z",
    updatedAt: "2025-05-25T02:40:48.737Z",
    status: "ended",
    endedReason: "call.in-progress.error-assistant-did-not-receive-customer-audio",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 18 // calculated from timestamps
  },
  {
    id: "6b7a77b0-35fa-4102-a419-28304d420983",
    createdAt: "2025-05-25T02:40:28.788Z",
    updatedAt: "2025-05-25T02:42:52.852Z",
    status: "ended",
    endedReason: "silence-timed-out",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 144 // calculated from timestamps
  },
  {
    id: "f5da271d-4eb5-412f-9254-fbc0827b1744",
    createdAt: "2025-05-25T02:39:47.677Z",
    updatedAt: "2025-05-25T02:40:21.245Z",
    status: "ended",
    endedReason: "customer-ended-call",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 34 // calculated from timestamps
  },
  {
    id: "b127606c-ec11-4808-86d1-7d67c7f91cdf",
    createdAt: "2025-05-25T02:38:40.858Z",
    updatedAt: "2025-05-25T02:40:25.266Z",
    status: "ended",
    endedReason: "customer-ended-call",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 104 // calculated from timestamps
  },
  {
    id: "1398ffc0-0da9-42a3-9e28-47bf74e9e89c",
    createdAt: "2025-05-25T02:12:40.663Z",
    updatedAt: "2025-05-25T02:12:58.871Z",
    status: "ended",
    endedReason: "call.in-progress.error-assistant-did-not-receive-customer-audio",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 18 // calculated from timestamps
  },
  {
    id: "108d03f2-076f-46eb-b767-ed0d4431541f",
    createdAt: "2025-05-25T02:12:38.808Z",
    updatedAt: "2025-05-25T02:13:41.989Z",
    status: "ended",
    endedReason: "customer-ended-call",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 63 // calculated from timestamps
  },
  {
    id: "5c360898-eeb2-4511-9fc3-35f5e9451808",
    createdAt: "2025-05-25T01:46:42.458Z",
    updatedAt: "2025-05-25T01:48:31.008Z",
    status: "ended",
    endedReason: "customer-ended-call",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 109 // calculated from timestamps
  },
  {
    id: "d6dda429-5f1a-47d5-bfad-15110ffc8183",
    createdAt: "2025-05-25T01:44:26.789Z",
    updatedAt: "2025-05-25T01:45:06.086Z",
    status: "ended",
    endedReason: "silence-timed-out",
    assistantId: "d158780b-18df-4ef3-b3b9-d4ea58d2c6b8",
    duration: 39 // calculated from timestamps
  },
  {
    id: "3bb62c3b-259e-4f61-a6f2-535a28a80d66",
    createdAt: "2025-05-25T01:41:45.068Z",
    updatedAt: "2025-05-25T01:42:00.060Z",
    status: "ended",
    endedReason: "pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade",
    assistantId: "25da5d63-1c6e-4d66-9216-df134ec04c54",
    duration: 15 // calculated from timestamps
  },
  {
    id: "009f6035-b529-496a-b961-f26057dc7bb2",
    createdAt: "2025-05-25T01:38:31.906Z",
    updatedAt: "2025-05-25T01:38:47.598Z",
    status: "ended",
    endedReason: "customer-ended-call",
    assistantId: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865",
    duration: 16 // calculated from timestamps
  }
];

/**
 * Main function to run the sync
 */
async function main() {
  console.log('Starting Vapi calls sync...');
  console.log(`Found ${recentVapiCalls.length} recent calls to sync`);

  try {
    const result = await syncVapiCallsToConsultations(recentVapiCalls);

    if (result.success) {
      console.log('\n✅ Sync completed successfully!');
      console.log(`✅ Successfully created: ${result.results.success} consultations`);
      console.log(`❌ Errors: ${result.results.errors}`);
      
      if (result.results.details.length > 0) {
        console.log('\nDetails:');
        result.results.details.forEach(detail => {
          if (detail.status === 'success') {
            console.log(`  ✅ ${detail.callId} -> Consultation ${detail.consultationId}`);
          } else {
            console.log(`  ❌ ${detail.callId} -> Error: ${detail.error}`);
          }
        });
      }
    } else {
      console.error('❌ Sync failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Unexpected error during sync:', error);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as syncVapiCalls };
