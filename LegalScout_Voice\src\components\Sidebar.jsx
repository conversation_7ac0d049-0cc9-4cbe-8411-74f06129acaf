// Update navigation links
<NavLink to="/" end className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Dashboard
</NavLink>
<NavLink to="/profile" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Profile
</NavLink>
<NavLink to="/agent" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Agent
</NavLink>
<NavLink to="/fields" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Custom Fields
</NavLink>
<NavLink to="/automation" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Automation
</NavLink>
<NavLink to="/consultations" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Consultations
</NavLink>
<NavLink to="/integrations" className={({ isActive }) => isActive ? "active-nav-link" : "nav-link"}>
  Integrations
</NavLink>