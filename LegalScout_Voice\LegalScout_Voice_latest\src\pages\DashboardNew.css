/* DashboardNew.css */

/* Main layout */
.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background-color, #f5f7fa);
}

/* Dashboard header - Minimal & Sleek */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 1.25rem;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 99997;
  min-height: 52px;
}

/* Attorney Selector - Minimal */
.attorney-selector-wrapper {
  flex: 0;
  max-width: 260px;
  margin: 0 0.75rem;
  z-index: 99997;
  position: relative;
}

/* Header Assistant Selector - Ultra Compact */
.header-assistant-selector-wrapper {
  flex: 0;
  max-width: 280px;
  margin-left: 1rem;
  z-index: 99997;
  position: relative;
}

.header-assistant-selector-wrapper .enhanced-assistant-dropdown {
  background: transparent;
  border: none;
  border-radius: 6px;
  padding: 0;
  box-shadow: none;
}

/* Hide title and description in header */
.header-assistant-selector-wrapper .enhanced-assistant-dropdown h3,
.header-assistant-selector-wrapper .enhanced-assistant-dropdown .card-description,
.header-assistant-selector-wrapper .enhanced-assistant-dropdown .dropdown-header,
.header-assistant-selector-wrapper .enhanced-assistant-dropdown .assistant-details,
.header-assistant-selector-wrapper .enhanced-assistant-dropdown .dropdown-actions {
  display: none;
}

/* Header dropdown trigger - Compact */
.header-assistant-selector-wrapper .dropdown-trigger {
  min-width: 240px;
  min-height: 26px !important;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(75, 116, 170, 0.15);
  border-radius: 6px;
  padding: 0.25rem 0.5rem !important;
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.header-assistant-selector-wrapper .dropdown-trigger:hover {
  background-color: rgba(255, 255, 255, 1);
  border-color: rgba(75, 116, 170, 0.25);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* Dark theme for header dropdown */
.dashboard-container.dark .header-assistant-selector-wrapper .dropdown-trigger {
  background-color: rgba(24, 24, 28, 0.95);
  border-color: rgba(100, 181, 246, 0.2);
  color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dashboard-container.dark .header-assistant-selector-wrapper .dropdown-trigger:hover {
  background-color: rgba(24, 24, 28, 1);
  border-color: rgba(100, 181, 246, 0.35);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.dashboard-container.dark .header-assistant-selector-wrapper .dropdown-menu {
  background-color: rgba(18, 18, 20, 0.98);
  border-color: rgba(100, 181, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* Header compact assistant info */
.header-assistant-selector-wrapper .assistant-image-mini {
  width: 20px !important;
  height: 20px !important;
  border-width: 1px;
}

.header-assistant-selector-wrapper .status-dot-mini {
  width: 6px !important;
  height: 6px !important;
  border-width: 1px;
  bottom: -1px;
  right: -1px;
}

.header-assistant-selector-wrapper .assistant-info .assistant-name {
  font-size: 0.8rem !important;
  font-weight: 500;
  line-height: 1.2;
}

.header-assistant-selector-wrapper .assistant-info .assistant-subdomain {
  font-size: 0.65rem !important;
  line-height: 1.1;
  opacity: 0.7;
}

.header-assistant-selector-wrapper .dropdown-arrow {
  font-size: 0.7rem;
}

/* Header dropdown menu positioning - Ultra Compact (40% less height) */
.header-assistant-selector-wrapper .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 99999;
  min-width: 280px;
  max-width: 400px;
  margin-top: 4px;
  max-height: 144px !important; /* 40% less than 240px */
  overflow-y: auto;
}

/* Header dropdown items - Compact */
.header-assistant-selector-wrapper .dropdown-item {
  padding: 0.375rem 0.5rem !important;
  min-height: 1.75rem !important;
  gap: 0.5rem !important;
}

.header-assistant-selector-wrapper .assistant-image {
  width: 24px !important;
  height: 24px !important;
  border-width: 1px;
}

.header-assistant-selector-wrapper .status-dot {
  width: 8px !important;
  height: 8px !important;
  border-width: 1px;
  bottom: -1px;
  right: -1px;
}

.header-assistant-selector-wrapper .dropdown-item .assistant-name {
  font-size: 0.8rem !important;
  font-weight: 500;
}

.header-assistant-selector-wrapper .dropdown-item .assistant-subdomain {
  font-size: 0.65rem !important;
  opacity: 0.7;
}

.dashboard-container.dark .header-assistant-selector-wrapper .enhanced-assistant-dropdown {
  background: transparent;
  border: none;
}

/* Dashboard logo section - Horizontal layout with compact assistant selector */
.dashboard-logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
  min-width: 0;
}

.dashboard-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 0;
  flex-shrink: 0;
}

/* Polished assistant selector positioned next to logo/firm name */
.header-assistant-selector-compact {
  flex-shrink: 0;
  margin-left: 0;
  max-width: 180px;
}

/* Override dropdown styles for ultra-compact header integration */
.header-assistant-selector-compact .enhanced-assistant-dropdown {
  background: transparent;
  border: none;
  padding: 0;
  box-shadow: none;
}

.header-assistant-selector-compact .dropdown-header {
  display: none; /* Hide the header in compact mode */
}

.header-assistant-selector-compact .dropdown-trigger {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb, 16, 185, 129), 0.08), rgba(var(--primary-color-rgb, 16, 185, 129), 0.04));
  border: 1px solid rgba(var(--primary-color-rgb, 16, 185, 129), 0.2);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  height: 36px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 160px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
}

.header-assistant-selector-compact .dropdown-trigger:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb, 16, 185, 129), 0.12), rgba(var(--primary-color-rgb, 16, 185, 129), 0.06));
  border-color: rgba(var(--primary-color-rgb, 16, 185, 129), 0.3);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb, 16, 185, 129), 0.15);
  transform: translateY(-1px);
}

.header-assistant-selector-compact .assistant-info {
  flex: 1;
  min-width: 0;
}

.header-assistant-selector-compact .assistant-info .assistant-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary-color, #10b981);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.header-assistant-selector-compact .assistant-info .assistant-subdomain {
  display: none; /* Hide subdomain in compact mode */
}

.header-assistant-selector-compact .dropdown-chevron {
  font-size: 0.7rem;
  margin-left: 0;
  flex-shrink: 0;
  color: var(--primary-color, #10b981);
  opacity: 0.8;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.header-assistant-selector-compact .dropdown-trigger:hover .dropdown-chevron {
  opacity: 1;
  transform: translateY(-0.5px);
}

/* Polished dropdown menu */
.header-assistant-selector-compact .dropdown-menu {
  min-width: 240px;
  max-width: 280px;
  padding: 0.5rem;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(var(--primary-color-rgb, 16, 185, 129), 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(var(--primary-color-rgb, 16, 185, 129), 0.08);
  margin-top: 0.5rem;
}

.header-assistant-selector-compact .dropdown-item {
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.header-assistant-selector-compact .dropdown-item:hover {
  background: rgba(var(--primary-color-rgb, 16, 185, 129), 0.08);
  transform: translateX(2px);
}

.header-assistant-selector-compact .dropdown-item .assistant-info .assistant-name {
  font-size: 0.8rem;
  font-weight: 500;
}

.header-assistant-selector-compact .create-assistant-item {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  border: 1px dashed rgba(var(--primary-color-rgb, 16, 185, 129), 0.3);
  border-radius: 6px;
  margin-top: 0.25rem;
}

/* Header sync status - Minimal */
.header-sync-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  background-color: rgba(0, 0, 0, 0.015);
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.header-sync-status:hover {
  background-color: rgba(0, 0, 0, 0.025);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* Dark theme adjustments for header sync status */
.dashboard-container.dark .header-sync-status {
  background-color: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.06);
}

.dashboard-container.dark .header-sync-status:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dashboard-logo img {
  height: 32px;
  object-fit: contain;
  max-width: 160px;
}

.dashboard-logo .attorney-logo {
  border-radius: 3px;
  transition: all 0.2s ease;
}

.dashboard-logo h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.75rem;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 1.1rem;
  color: #666;
  cursor: pointer;
  padding: 0.375rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: #333;
}

.sign-out-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  color: #666;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 0.85rem;
}

.sign-out-button:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.12);
  color: #333;
}

/* Dashboard content */
.dashboard-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 73px);
}

/* Sidebar */
.dashboard-sidebar {
  width: 204px; /* Increased by 70% from 120px */
  background-color: white;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem 0;
  position: relative;
  transition: width 0.3s ease, padding 0.3s ease;
  overflow: hidden;
}

/* Collapsed sidebar */
.dashboard-sidebar.collapsed {
  width: 60px;
  padding: 1.5rem 0;
}

/* Sidebar controls */
.sidebar-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.sidebar-toggle,
.sidebar-pin {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover,
.sidebar-pin:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-pin.active {
  background-color: rgba(216, 87, 34, 0.2);
  color: #D85722;
}

.dashboard-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 0.25rem; /* Further reduced to give more space to menu items */
  margin-top: 20px;
  width: 100%; /* Ensure nav takes full width of the sidebar */
}

.dashboard-nav button {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* Restored to original value */
  padding: 0.75rem 0.5rem; /* Increased horizontal padding */
  border: none;
  background-color: transparent;
  border-radius: 4px;
  color: #666;
  font-size: 0.9rem; /* Increased from 0.8rem */
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  justify-content: flex-start;
  width: 100%; /* Ensure buttons take full width of the sidebar */
}

.dashboard-nav button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dashboard-nav button.active {
  background-color: rgba(216, 87, 34, 0.1);
  color: #D85722;
  font-weight: 500;
}

/* Navigation call-to-action styling */
.nav-cta {
  margin-left: auto;
  display: flex;
  align-items: center;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.cta-hint {
  font-size: 0.7rem;
  color: rgba(75, 156, 211, 0.7);
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  padding: 2px 6px;
  border-radius: 10px;
  background: rgba(75, 156, 211, 0.08);
  border: 1px solid rgba(75, 156, 211, 0.15);
}

.cta-dot {
  font-size: 1.2rem;
  line-height: 1;
  margin-right: 2px;
}

.cta-dot.new {
  color: #22c55e;
  animation: pulse-gentle 2s infinite;
}

.cta-dot.incomplete {
  color: #f59e0b;
  animation: pulse-gentle 2s infinite;
}

@keyframes pulse-gentle {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.dashboard-nav button:hover .nav-cta {
  opacity: 1;
}

.dashboard-nav button:hover .cta-hint {
  background: rgba(75, 156, 211, 0.12);
  color: rgba(75, 156, 211, 0.9);
}

.dashboard-nav button.active .nav-cta {
  opacity: 0.8;
}

.dashboard-nav button.active .cta-hint {
  background: rgba(216, 87, 34, 0.12);
  color: rgba(216, 87, 34, 0.8);
  border-color: rgba(216, 87, 34, 0.2);
}

/* Sub-tabs styling */
.sub-tabs {
  margin-left: 1rem;
  border-left: 2px solid rgba(216, 87, 34, 0.2);
  padding-left: 0.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.sub-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background-color: transparent;
  border-radius: 4px;
  color: #888;
  font-size: 0.85rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 0.25rem;
}

.sub-tab:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
}

.sub-tab.active {
  background-color: rgba(216, 87, 34, 0.15);
  color: #D85722;
  font-weight: 500;
}

/* Sub-tab call-to-action styling */
.sub-tab .nav-cta {
  margin-left: auto;
  opacity: 0.5;
}

.sub-tab .cta-hint {
  font-size: 0.65rem;
  padding: 1px 4px;
  border-radius: 8px;
}

.sub-tab:hover .nav-cta {
  opacity: 0.8;
}

.sub-tab.active .nav-cta {
  opacity: 0.7;
}

/* Collapsed sidebar nav buttons */
.dashboard-sidebar.collapsed .dashboard-nav button {
  justify-content: flex-start;
  padding: 0.75rem 0.5rem;
}

.dashboard-sidebar.collapsed .dashboard-nav button span {
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* Hide call-to-action elements when sidebar is collapsed */
.dashboard-sidebar.collapsed .nav-cta {
  display: none;
}

/* Hide sub-tabs when sidebar is collapsed */
.dashboard-sidebar.collapsed .sub-tabs {
  display: none;
}

/* Hover behavior for collapsed sidebar when not pinned */
.dashboard-sidebar.collapsed:not(.pinned):hover {
  width: 204px; /* Updated to match the new sidebar width */
}

.dashboard-sidebar.collapsed:not(.pinned):hover .dashboard-nav button {
  justify-content: flex-start;
  padding: 0.75rem 1rem;
}

.dashboard-sidebar.collapsed:not(.pinned):hover .dashboard-nav button span {
  opacity: 1;
  width: auto;
  height: auto;
  margin-left: 0.75rem;
}

/* Show call-to-action elements when hovering over collapsed sidebar */
.dashboard-sidebar.collapsed:not(.pinned):hover .nav-cta {
  display: flex;
}

.dashboard-nav button .coming-soon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7rem;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  color: #666;
}

/* Main panel */
.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  overflow: visible;
}

.tab-content {
  flex: 0.85; /* Increased by 70% from 0.5 */
  padding: 1.5rem;
  overflow-y: auto;
  min-width: 250px;
  max-width: 510px; /* Increased by 70% from 300px */
}

.tab-content.hidden {
  display: none;
}

.tab-content.full-width {
  flex: 1;
  max-width: none;
  width: 100%;
}

/* Preview panel */
.preview-panel {
  flex: 2.15; /* Adjusted to balance with the wider tab content */
  background-color: #f0f2f5;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  min-width: 300px;
  max-width: none; /* Removed max-width constraint to allow it to fill available space */
  left: auto !important; /* Override any inline styles */
  right: auto !important; /* Override any inline styles */
  transform: none !important; /* Override any transforms */
}

.preview-panel.expanded {
  flex: 3; /* Increased from 2 to 3 for even more space in expanded view */
}

.preview-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 100; /* Increased z-index to ensure visibility */
  display: flex;
  gap: 0.5rem;
  align-items: center;
  opacity: 1 !important; /* Force visibility */
  visibility: visible !important; /* Force visibility */
}

.preview-controls button {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  opacity: 1 !important;
  visibility: visible !important;
}

.preview-updating-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  color: #666;
  backdrop-filter: blur(5px);
  animation: pulse 1.5s infinite;
}

.spinner-small {
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 2px solid #D85722;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.preview-controls button:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.preview-container {
  height: 100%;
  min-height: 500px; /* Ensure minimum height */
  overflow: hidden;
  position: relative; /* Keep it in the document flow */
  left: auto !important; /* Override any inline styles */
  top: auto !important; /* Override any inline styles */
  transform: none !important; /* Override any transforms */
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03); /* Light background to make it visible */
  border: 1px solid rgba(0, 0, 0, 0.1); /* Add border to make it visible */
  display: flex; /* Use flexbox to ensure iframe fills container */
  flex-direction: column;
  width: 100% !important; /* Ensure it fills the parent container */
}

.preview-iframe {
  flex: 1; /* Make iframe fill the container */
  width: 100% !important;
  height: 100% !important;
  min-height: 500px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  z-index: 2; /* Ensure iframe is above loading message */
  position: relative; /* Ensure z-index works */
  left: auto !important; /* Override any inline styles */
  top: auto !important; /* Override any inline styles */
  transform: none !important; /* Override any transforms */
}

.preview-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1rem 2rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 8px;
  font-size: 1rem;
  z-index: 1;
}

/* Loading and error states */
.dashboard-loading,
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 2rem;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4B9CD3; /* Light blue color */
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.dashboard-loading .loading-message {
  font-size: 0.9rem;
  opacity: 0.7;
  margin-top: 0.5rem;
  max-width: 300px;
  text-align: center;
}

.dashboard-container.dark .dashboard-loading .loading-message {
  opacity: 0.5;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-error h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.dashboard-error button {
  padding: 0.75rem 1.5rem;
  background-color: #D85722;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 1rem;
}

.dashboard-error button:hover {
  background-color: #c04d1e;
}

/* Dark theme */
.dashboard-container.dark {
  --background-color: #121212;
  color: rgba(255, 255, 255, 0.87);
}

.dashboard-container.dark .dashboard-header {
  background-color: #1e1e1e;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .dashboard-logo h1 {
  color: rgba(255, 255, 255, 0.87);
}

.dashboard-container.dark .dashboard-logo .attorney-logo {
  /* Add a subtle glow effect for dark mode */
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
}

.dashboard-container.dark .theme-toggle,
.dashboard-container.dark .sign-out-button {
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .dashboard-sidebar {
  background-color: #1e1e1e;
  border-right-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .sidebar-toggle,
.dashboard-container.dark .sidebar-pin {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .sidebar-toggle:hover,
.dashboard-container.dark .sidebar-pin:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .sidebar-pin.active {
  background-color: rgba(216, 87, 34, 0.3);
  color: #ff7d4d;
}

.dashboard-container.dark .dashboard-nav button {
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .dashboard-nav button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dark theme call-to-action styling */
.dashboard-container.dark .cta-hint {
  color: rgba(100, 181, 246, 0.8);
  background: rgba(100, 181, 246, 0.1);
  border-color: rgba(100, 181, 246, 0.2);
}

.dashboard-container.dark .dashboard-nav button:hover .cta-hint {
  background: rgba(100, 181, 246, 0.15);
  color: rgba(100, 181, 246, 1);
}

.dashboard-container.dark .dashboard-nav button.active .cta-hint {
  background: rgba(255, 125, 77, 0.15);
  color: rgba(255, 125, 77, 0.9);
  border-color: rgba(255, 125, 77, 0.25);
}

.dashboard-container.dark .cta-dot.new {
  color: #4ade80;
}

.dashboard-container.dark .cta-dot.incomplete {
  color: #fbbf24;
}

.dashboard-container.dark .preview-panel {
  background-color: #121212;
}

.dashboard-container.dark .preview-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .preview-controls button {
  background-color: rgba(40, 40, 40, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.dashboard-container.dark .preview-controls button:hover {
  background-color: rgba(50, 50, 50, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-container.dark .preview-updating-indicator {
  background-color: rgba(30, 30, 30, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .spinner-small {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: #ff7d4d;
}

/* Sidebar share section */
.sidebar-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
  transition: opacity 0.3s ease;
}

.sidebar-share-section {
  padding: 0.5rem 0;
  position: relative;
  transition: opacity 0.3s ease;
}

/* Integration with AssistantAwareShare component */
.sidebar-share-section .sidebar-share-component {
  background: transparent;
  box-shadow: none;
  padding: 0;
  margin: 0;
}

.sidebar-share-section .sidebar-share-component .compact-content {
  gap: 8px;
}

.sidebar-share-section .copy-success {
  background: rgba(72, 216, 105, 0.1);
  color: #48d869;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
}

.share-header {
  cursor: pointer;
  margin-top: 1rem;
}

.sidebar-share-section h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  padding: 0 1rem;
  white-space: nowrap;
}

.copy-success {
  font-size: 0.8rem;
  color: #28a745;
  margin: 0.25rem 0;
  padding: 0 1rem;
}

.share-buttons-container {
  position: relative;
}

.primary-share-buttons {
  display: block;
}

.sidebar-share-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem 1rem;
  margin-bottom: 0.5rem;
  border: none;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  color: #666;
  font-size: 0.85rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-share-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.sidebar-social-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 0.5rem 1rem;
  margin-top: 0.5rem;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.share-header:hover + .sidebar-social-buttons,
.sidebar-social-buttons:hover {
  max-height: 300px;
  opacity: 1;
}

.sidebar-social-buttons button {
  height: 36px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1rem;
  background-color: rgba(0, 0, 0, 0.03);
  color: #666;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
  white-space: nowrap;
}

.sidebar-social-buttons button svg {
  margin-right: 10px;
  font-size: 1.1rem;
}

.sidebar-social-buttons button:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* Hide share section in collapsed state */
.dashboard-sidebar.collapsed .sidebar-divider,
.dashboard-sidebar.collapsed .sidebar-share-section {
  opacity: 0;
  pointer-events: none;
}

/* Show share section when hovering over collapsed sidebar */
.dashboard-sidebar.collapsed:not(.pinned):hover .sidebar-divider,
.dashboard-sidebar.collapsed:not(.pinned):hover .sidebar-share-section {
  opacity: 1;
  pointer-events: auto;
}

/* Integrations panel styling */
.integrations-panel {
  padding: 1rem;
  height: 100%;
  overflow-y: auto;
  background-color: var(--background-color, #f5f7fa);
}

.integrations-panel .integrations-tab {
  padding: 0;
}

.integrations-panel .dashboard-card {
  margin-bottom: 1rem;
}

/* Dark theme for share section */
.dashboard-container.dark .sidebar-divider {
  background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .sidebar-share-section h4 {
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .copy-success {
  color: #48d869;
}

.dashboard-container.dark .sidebar-share-button {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .sidebar-share-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Dark theme for integrations panel */
.dashboard-container.dark .integrations-panel {
  background-color: var(--background-color-dark, #1a1a1a);
}

.dashboard-container.dark .sidebar-social-buttons button {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .sidebar-social-buttons button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.dashboard-container.dark .share-header {
  color: rgba(255, 255, 255, 0.6);
}

/* ===== MOBILE RESPONSIVE DESIGN ===== */

/* Mobile hamburger menu button */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  z-index: 99999 !important;
  position: relative !important;
  min-width: 44px;
  min-height: 44px;
  touch-action: manipulation;
  pointer-events: auto !important;
}

.mobile-menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: currentColor;
  margin: 5px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mobile-overlay.active {
  opacity: 1;
  pointer-events: auto;
}

/* Tablet breakpoint (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  .dashboard-header {
    padding: 0.75rem 1.5rem;
  }

  .dashboard-sidebar {
    width: 200px;
  }

  .tab-content {
    padding: 1rem;
    max-width: 400px;
  }

  .preview-panel {
    min-width: 250px;
  }
}

/* Desktop breakpoint (769px and above) - Ensure sidebar is always visible */
@media (min-width: 769px) {
  .dashboard-sidebar {
    position: relative !important;
    width: 204px !important;
    left: auto !important;
    top: auto !important;
    z-index: auto !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 1.5rem 0 !important;
    overflow: hidden !important;
    transition: width 0.3s ease, padding 0.3s ease !important;
  }

  .dashboard-main {
    margin-left: 0 !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 20px !important;
    overflow: visible !important;
  }

  .mobile-menu-toggle {
    display: none !important;
  }

  .mobile-overlay {
    display: none !important;
  }

  .sidebar-toggle,
  .sidebar-pin {
    display: flex !important;
  }

  .dashboard-nav {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    padding: 0 0.25rem !important;
    margin-top: 20px !important;
    width: 100% !important;
  }

  .dashboard-nav button {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.75rem 0.5rem !important;
    border: none !important;
    background-color: transparent !important;
    border-radius: 4px !important;
    color: #666 !important;
    font-size: 0.9rem !important;
    text-align: left !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    position: relative !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    justify-content: flex-start !important;
    width: 100% !important;
  }

  .dashboard-nav button span {
    display: inline !important;
    opacity: 1 !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Desktop active button styling */
  .dashboard-nav button.active {
    background-color: rgba(216, 87, 34, 0.1) !important;
    color: #D85722 !important;
    font-weight: 500 !important;
  }

  .dashboard-nav button:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }

  /* Dark theme desktop active button styling */
  .dashboard-container.dark .dashboard-nav button.active {
    background-color: rgba(216, 87, 34, 0.2) !important;
    color: #ff7d4d !important;
    font-weight: 500 !important;
  }

  .dashboard-container.dark .dashboard-nav button:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .dashboard-container.dark .dashboard-nav button {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .sub-tabs {
    display: block !important;
    margin-left: 1rem !important;
    border-left: 2px solid rgba(216, 87, 34, 0.2) !important;
    padding-left: 0.5rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .sub-tab {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.5rem 0.75rem !important;
    border: none !important;
    background-color: transparent !important;
    border-radius: 4px !important;
    color: #888 !important;
    font-size: 0.85rem !important;
    text-align: left !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    position: relative !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    justify-content: flex-start !important;
    width: 100% !important;
    margin-bottom: 0.25rem !important;
  }

  .sub-tab span {
    display: inline !important;
  }

  .sidebar-share-section {
    display: block !important;
  }

  .sidebar-divider {
    display: block !important;
  }

  /* Profile button - positioned halfway down */
  .profile-button-bottom {
    margin-top: 3rem !important; /* Fixed spacing instead of auto */
    margin-bottom: 0.5rem !important; /* Less space from bottom */
  }

  .profile-button-bottom span {
    display: inline !important; /* Show text */
  }

  /* Make the navigation container flex for profile button positioning */
  .dashboard-nav {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    padding: 0 0.25rem !important;
    margin-top: 20px !important;
    width: 100% !important;
  }

  /* Clickable logo and firm name in header */
  .dashboard-logo {
    transition: opacity 0.2s ease !important;
  }

  .dashboard-logo:hover {
    opacity: 0.8 !important;
  }

  .dashboard-logo:active {
    opacity: 0.6 !important;
  }
}

/* Mobile breakpoint (768px and below) */
@media (max-width: 768px) {
  /* Header adjustments - Minimal */
  .dashboard-header {
    padding: 0.5rem 0.75rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 64px; /* Reduced height for minimal design */
    z-index: 1001; /* Higher than sidebar to ensure hamburger is clickable */
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: inherit; /* Inherit from dashboard container */
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }

  /* Mobile header assistant selector adjustments */
  .header-assistant-selector-wrapper {
    max-width: 200px;
    margin-left: 0.5rem;
    display: block !important; /* Force visibility on mobile */
    visibility: visible !important;
    opacity: 1 !important;
  }

  .header-assistant-selector-wrapper .dropdown-trigger {
    min-width: 180px;
    min-height: 32px !important;
    padding: 0.375rem 0.5rem !important;
    display: flex !important; /* Force visibility */
    visibility: visible !important;
  }

  .header-assistant-selector-wrapper .assistant-info .assistant-name {
    font-size: 0.75rem !important;
  }

  .header-assistant-selector-wrapper .assistant-info .assistant-subdomain {
    font-size: 0.6rem !important;
  }

  .dashboard-logo h1 {
    font-size: 1rem;
    font-weight: 500;
  }

  .attorney-logo {
    width: 28px;
    height: 28px;
  }

  /* Ensure dashboard logo section doesn't hide assistant selector */
  .dashboard-logo-section {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex-wrap: nowrap !important;
    overflow: visible !important;
  }

  /* Compact assistant selector responsive adjustments */
  .header-assistant-selector-compact {
    max-width: 150px;
    display: block !important; /* Force visibility on mobile */
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 99998 !important; /* Ensure it's above other elements */
  }

  .header-assistant-selector-compact .dropdown-trigger {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
    min-height: 26px;
    max-width: 140px;
    display: flex !important; /* Force visibility */
    visibility: visible !important;
  }

  .header-assistant-selector-compact .assistant-info .assistant-name {
    font-size: 0.7rem;
  }

  /* Ensure dropdown menu is visible on mobile */
  .header-assistant-selector-compact .dropdown-menu,
  .header-assistant-selector-wrapper .dropdown-menu {
    z-index: 99999 !important;
    position: fixed !important; /* Use fixed positioning on mobile */
    max-height: 50vh !important; /* Limit height on mobile */
    overflow-y: auto !important;
    display: block !important;
    visibility: visible !important;
  }

  /* Ensure dropdown items are visible and touchable */
  .header-assistant-selector-compact .dropdown-item,
  .header-assistant-selector-wrapper .dropdown-item {
    min-height: 44px !important; /* Touch-friendly size */
    padding: 0.75rem !important;
    display: flex !important;
    visibility: visible !important;
  }

  /* Show mobile menu toggle */
  .mobile-menu-toggle {
    display: block !important;
    order: -1;
    z-index: 99999 !important;
    position: relative !important;
    pointer-events: auto !important;
  }

  /* Hide desktop sidebar controls */
  .sidebar-toggle,
  .sidebar-pin {
    display: none;
  }

  /* Compact vertical navigation - always visible */
  .dashboard-sidebar {
    position: fixed;
    top: 80px; /* Below header - increased to ensure proper spacing */
    left: 0;
    width: 60px;
    height: calc(100vh - 80px);
    z-index: 998; /* Lower than header but higher than content */
    background-color: white !important;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    overflow-y: auto;
    transition: width 0.3s ease, left 0.3s ease;
    pointer-events: auto !important;
  }

  /* Expanded mobile sidebar when hamburger is clicked */
  .dashboard-sidebar.mobile-open {
    width: 280px;
    padding: 1rem 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 999; /* Higher when expanded, but still below header */
  }

  /* Show mobile overlay when sidebar is expanded */
  .mobile-overlay {
    display: block;
  }

  /* Compact navigation styles */
  .dashboard-nav {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0 0.5rem;
    margin-top: 1rem; /* Push navigation items down to avoid hamburger overlap */
  }

  .dashboard-nav > button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    padding: 0;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    margin: 0 auto;
  }

  .dashboard-nav > button svg {
    width: 20px;
    height: 20px;
    color: #666;
    transition: color 0.2s ease;
  }

  .dashboard-nav > button span {
    display: none; /* Hide text in compact mode */
  }

  .dashboard-nav > button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dashboard-nav > button.active {
    background-color: #4B9CD3;
    color: white;
  }

  .dashboard-nav > button.active svg {
    color: white;
  }

  /* Sub-tabs in compact mode */
  .sub-tabs {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-left: 0;
    padding-left: 0;
  }

  .sub-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 auto;
  }

  .sub-tab svg {
    width: 16px;
    height: 16px;
    color: #888;
    transition: color 0.2s ease;
  }

  .sub-tab span {
    display: none; /* Hide text in compact mode */
  }

  .sub-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .sub-tab.active {
    background-color: #4B9CD3;
    color: white;
  }

  .sub-tab.active svg {
    color: white;
  }

  /* Expanded sidebar styles */
  .dashboard-sidebar.mobile-open .dashboard-nav > button {
    width: auto;
    height: auto;
    padding: 0.75rem 1rem;
    justify-content: flex-start;
    gap: 0.75rem;
  }

  .dashboard-sidebar.mobile-open .dashboard-nav > button span {
    display: inline; /* Show text when expanded */
  }

  .dashboard-sidebar.mobile-open .sub-tab {
    width: auto;
    height: auto;
    padding: 0.5rem 1rem 0.5rem 2rem;
    justify-content: flex-start;
    gap: 0.5rem;
  }

  .dashboard-sidebar.mobile-open .sub-tab span {
    display: inline; /* Show text when expanded */
  }

  /* Share section in compact mode */
  .sidebar-share-section {
    display: none; /* Hide in compact mode */
  }

  .dashboard-sidebar.mobile-open .sidebar-share-section {
    display: block; /* Show when expanded */
  }

  /* Sidebar divider */
  .sidebar-divider {
    display: none; /* Hide in compact mode */
  }

  .dashboard-sidebar.mobile-open .sidebar-divider {
    display: block; /* Show when expanded */
  }

  /* Main content adjustments */
  .dashboard-main {
    flex-direction: column;
    padding: 0;
    gap: 0;
    margin-left: 60px; /* Account for compact sidebar */
    transition: margin-left 0.3s ease;
  }

  /* When mobile menu is open, adjust main content */
  .dashboard-container:has(.dashboard-sidebar.mobile-open) .dashboard-main {
    margin-left: 0; /* Remove margin when sidebar is expanded */
  }

  /* Fallback for browsers that don't support :has() */
  .mobile-menu-open .dashboard-main {
    margin-left: 0;
  }

  /* Tab content full width on mobile */
  .tab-content {
    flex: 1;
    max-width: none;
    width: 100%;
    padding: 1rem;
    min-width: auto;
    position: relative;
    z-index: 1;
    pointer-events: auto;
  }

  .tab-content.full-width {
    padding: 1rem;
  }

  /* Ensure all content inside tab-content is interactive */
  .tab-content * {
    pointer-events: auto;
  }

  /* Preview panel mobile handling */
  .preview-panel {
    flex: none;
    width: 100%;
    min-width: auto;
    max-width: none;
    min-height: 400px;
    margin-top: 1rem;
    border-radius: 8px;
    margin: 1rem;
    margin-top: 0;
  }

  .preview-panel.expanded {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: calc(100vh - 80px);
    z-index: 999;
    margin: 0;
    border-radius: 0;
  }

  /* Preview controls mobile positioning */
  .preview-controls {
    top: 0.5rem;
    right: 0.5rem;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .preview-controls button {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    min-width: auto;
  }

  /* Attorney selector mobile */
  .attorney-selector-wrapper {
    max-width: 200px;
    margin: 0 0.5rem;
  }

  /* Navigation buttons mobile */
  .dashboard-nav button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    justify-content: flex-start;
  }

  .dashboard-nav button span {
    margin-left: 0.75rem;
  }

  /* Share section mobile */
  .sidebar-share-section {
    padding: 1rem;
  }

  .sidebar-share-button,
  .sidebar-social-buttons button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-height: 44px; /* Touch-friendly minimum */
  }

  /* Dark theme mobile adjustments */
  .dashboard-container.dark .mobile-menu-toggle {
    color: rgba(255, 255, 255, 0.87);
  }

  .dashboard-container.dark .mobile-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .dashboard-container.dark .dashboard-sidebar {
    background-color: #1e1e1e !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Dark mode support for compact navigation */
  .dashboard-container.dark .dashboard-nav > button svg {
    color: #ccc;
  }

  .dashboard-container.dark .dashboard-nav > button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dashboard-container.dark .dashboard-nav > button.active {
    background-color: #4B9CD3;
  }

  .dashboard-container.dark .sub-tab svg {
    color: #aaa;
  }

  .dashboard-container.dark .sub-tab:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dashboard-container.dark .sub-tab.active {
    background-color: #4B9CD3;
  }
}

/* Small mobile breakpoint (480px and below) */
@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.375rem 0.5rem;
    height: 56px; /* Even more minimal */
  }

  .dashboard-logo h1 {
    font-size: 0.9rem;
    display: none; /* Hide title on very small screens */
  }

  .attorney-logo {
    width: 24px;
    height: 24px;
  }

  /* Ultra compact assistant selector for very small screens */
  .header-assistant-selector-compact {
    max-width: 120px;
  }

  .header-assistant-selector-compact .dropdown-trigger {
    padding: 0.15rem 0.3rem;
    font-size: 0.65rem;
    min-height: 24px;
    max-width: 110px;
  }

  .header-assistant-selector-compact .assistant-info .assistant-name {
    font-size: 0.65rem;
  }

  .header-assistant-selector-compact .dropdown-chevron {
    font-size: 0.6rem;
    margin-left: 0.25rem;
  }

  /* Compact sidebar adjustments for very small screens */
  .dashboard-sidebar {
    width: 50px; /* Even more compact */
  }

  .dashboard-sidebar.mobile-open {
    width: 100%;
    left: 0;
  }

  .dashboard-main {
    margin-left: 50px; /* Account for smaller compact sidebar */
  }

  /* When mobile menu is open on small screens */
  .mobile-menu-open .dashboard-main {
    margin-left: 0;
  }

  .dashboard-nav > button {
    width: 40px;
    height: 40px;
  }

  .dashboard-nav > button svg {
    width: 18px;
    height: 18px;
  }

  .sub-tab {
    width: 32px;
    height: 32px;
  }

  .sub-tab svg {
    width: 14px;
    height: 14px;
  }

  .tab-content {
    padding: 0.75rem;
  }

  .preview-panel {
    margin: 0.75rem;
    min-height: 300px;
  }

  .preview-controls {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 0.5rem;
    justify-content: center;
  }

  .preview-controls button {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .attorney-selector-wrapper {
    max-width: 150px;
    margin: 0 0.25rem;
  }
}

/* Extra small mobile breakpoint (360px and below) */
@media (max-width: 360px) {
  .dashboard-header {
    padding: 0.25rem 0.375rem;
    height: 52px; /* Ultra minimal */
  }

  .tab-content {
    padding: 0.5rem;
  }

  .preview-panel {
    margin: 0.5rem;
    min-height: 250px;
  }

  .attorney-selector-wrapper {
    display: none; /* Hide on very small screens */
  }

  .dashboard-nav button {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
  }
}

/* ===== MOBILE PREVIEW SOLUTIONS ===== */

/* Swipe-up Preview Indicator */
.mobile-preview-swipe-indicator {
  display: none; /* Hidden by default on all screen sizes */
  position: fixed;
  bottom: 20px; /* Float above the bottom edge */
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 25px; /* Fully rounded for floating effect */
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.4), 0 0 30px rgba(0, 255, 255, 0.2);
  cursor: pointer;
  z-index: 1000; /* Higher z-index to float above content */
  transition: all 0.3s ease;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid #00FFFF;
}

.mobile-preview-swipe-indicator:hover {
  transform: translateX(-50%) translateY(-6px);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.6), 0 0 40px rgba(0, 255, 255, 0.3);
  background-color: rgba(0, 0, 0, 0.95);
  border-color: #00FFFF;
  scale: 1.05;
}

.mobile-preview-swipe-indicator.hidden {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}

.swipe-indicator-handle {
  width: 32px;
  height: 3px;
  background-color: #00FFFF;
  border-radius: 2px;
  opacity: 1;
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.6);
}

.swipe-indicator-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: #FFFFFF;
  opacity: 1;
  margin: 0;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

.swipe-indicator-arrow {
  color: #00FFFF;
  opacity: 1;
  animation: bounce-up 2s ease-in-out infinite;
  filter: drop-shadow(0 0 4px rgba(0, 255, 255, 0.8));
}

@keyframes bounce-up {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Mobile Preview Modal */
.mobile-preview-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1001;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mobile-preview-modal.active {
  opacity: 1;
  pointer-events: auto;
}

.mobile-preview-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 400px;
  height: 80%;
  background-color: rgba(0, 0, 0, 0.95);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3), 0 0 40px rgba(0, 255, 255, 0.1);
  border: 1px solid #00FFFF;
}

.mobile-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.9);
  color: #FFFFFF;
  border-bottom: 1px solid #00FFFF;
}

.mobile-preview-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.mobile-preview-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.mobile-preview-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.mobile-preview-iframe {
  width: 100%;
  height: calc(100% - 60px);
  border: none;
}

/* Slide-up Preview Panel */
.mobile-preview-panel {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background-color: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  pointer-events: none;
}

.mobile-preview-panel.active {
  transform: translateY(0);
  pointer-events: auto;
}

.mobile-preview-panel-handle {
  width: 40px;
  height: 4px;
  background-color: #ddd;
  border-radius: 2px;
  margin: 8px auto;
  cursor: pointer;
}

.mobile-preview-panel-content {
  height: calc(100% - 20px);
  overflow: hidden;
}

.mobile-preview-panel-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Quick Preview Cards */
.mobile-preview-cards {
  display: none;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1rem 0;
}

.mobile-preview-card {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mobile-preview-card-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.mobile-preview-card-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.mobile-preview-card-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0.25rem 0 0 0;
}

/* Header Preview Toggle */
.mobile-preview-toggle {
  display: none;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.2rem;
}

.mobile-preview-toggle:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

/* Mobile breakpoint styles */
@media (max-width: 768px) {
  /* Show mobile preview elements */
  .mobile-preview-swipe-indicator {
    display: flex;
  }

  .mobile-preview-modal {
    display: block;
  }

  .mobile-preview-panel {
    display: block;
  }

  .mobile-preview-cards {
    display: grid;
  }

  /* Hide desktop preview panel on mobile */
  .preview-panel {
    display: none !important;
  }

  /* Add bottom padding to main content areas to prevent overlap */
  .tab-content {
    padding-bottom: 100px !important; /* Extra space for floating preview button */
  }

  .dashboard-main {
    padding-bottom: 100px !important;
  }

  /* Adjust swipe indicator position when mobile menu is open */
  .mobile-menu-open .mobile-preview-swipe-indicator {
    bottom: -60px; /* Hide completely when mobile menu is open */
    opacity: 0;
    transition: all 0.3s ease;
  }
}

/* Dark theme support */
.dashboard-container.dark .mobile-preview-content {
  background-color: #1e1e1e;
  color: rgba(255, 255, 255, 0.87);
}

.dashboard-container.dark .mobile-preview-panel {
  background-color: #1e1e1e;
  color: rgba(255, 255, 255, 0.87);
}

.dashboard-container.dark .mobile-preview-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .mobile-preview-toggle {
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.87);
}

.dashboard-container.dark .mobile-preview-panel-handle {
  background-color: #555;
}

.dashboard-container.dark .mobile-preview-swipe-indicator {
  background-color: rgba(30, 30, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .mobile-preview-swipe-indicator:hover {
  background-color: rgba(30, 30, 30, 0.98);
}

.dashboard-container.dark .swipe-indicator-text {
  color: rgba(255, 255, 255, 0.87);
}
