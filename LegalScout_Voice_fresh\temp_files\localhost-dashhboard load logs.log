 [UnifiedBannerFix] Unified banner fix script loaded
 [VapiSDKLoader] Starting Vapi SDK loading process
 [VapiSDKLoader] Trying CDN source 1/3: https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
 [vite] connecting...
 [vite] connected.
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: Object
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: Object
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: Object
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
 API error, falling back to client-side implementation: 
console.error @ vapi-official-pattern-fix.js:96
 Using client-side fallback for auth state management
 SyncContext: Auth state result: Object
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
 API error, falling back to client-side implementation: 
console.error @ vapi-official-pattern-fix.js:96
 Using client-side fallback for auth state management
 SyncContext: Auth state result: Object
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
 API error, falling back to client-side implementation: 
console.error @ vapi-official-pattern-fix.js:96
 Using client-side fallback for auth state management
 SyncContext: Auth state result: Object
 Development mode: Using mock consistency check result
 [VapiOfficialPatternFix] ⚠️ Failed to load from https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js?t=1748978786767, trying next...
script.onerror @ vapi-official-pattern-fix.js:212
api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1  Failed to load resource: the server responded with a status of 404 (Not Found)
 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
getAssistant @ vapiMcpService.js:424
 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
 [19:26:26] [VapiMcpService] Retrieving assistant Object
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 Environment variables: Object
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1748978735712
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1748978735712
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: Object
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: dev-1748978735712
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js:1  Failed to load resource: net::ERR_NAME_NOT_RESOLVED
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [VapiSDKLoader] CDN source failed: Failed to load https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js
log @ vapi-sdk-loader.js:33
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: Object
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: Object
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: Object
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: Object
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: Object
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
dashboard:67 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): Object
AuthContext.jsx:168 OAuth user data details (auth change): Object
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: Object
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:67 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): Object
AuthContext.jsx:168 OAuth user data details (auth change): Object
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: Object
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:67 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [19:26:27] [VapiMcpService] Retrieving assistant Object
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=158b513d:5023:7)
    at async @supabase_supabase-js.js?v=158b513d:4977:16
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)
vapiMcpService.js:424 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
getAssistant @ vapiMcpService.js:424
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
vapiMcpDebugger.js:180 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
getAssistant @ vapiMcpService.js:424
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: Array(1)
api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
vapiMcpDebugger.js:180 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:100 [19:26:27] [VapiMcpService] Assistant not found in Vapi Object
log @ vapiLogger.js:100
AgentTab.jsx:541 ⚠️ [AgentTab] No voice settings found in Vapi assistant
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: net::ERR_FAILED
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (vapi-official-pattern-fix.js:74:28)
    at window.fetch (dashboard:71:18)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (vapiNetworkInterceptor.js:34:28)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at VapiMcpService.getAssistant (vapiMcpService.js:409:30)
    at async loadAssistantData (AssistantInfoSection.jsx:50:25)
(anonymous) @ consolidated-dashboard-fix.js:109
vapiMcpService.js:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
getAssistant @ vapiMcpService.js:427
vapiMcpService.js:432 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
getAssistant @ vapiMcpService.js:432
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
vapiMcpDebugger.js:180 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:100 [19:26:27] [VapiMcpService] Assistant not found in Vapi Object
log @ vapiLogger.js:100
AgentTab.jsx:541 ⚠️ [AgentTab] No voice settings found in Vapi assistant
api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
vapiMcpDebugger.js:180 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
getAssistant @ vapiMcpService.js:424
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: Object
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c:1 
            
            
           Failed to load resource: net::ERR_FAILED
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (vapi-official-pattern-fix.js:74:28)
    at window.fetch (dashboard:71:18)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (vapiNetworkInterceptor.js:34:28)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at window.fetch (disable-automatic-assistant-creation.js:210:28)
    at VapiMcpService.getAssistant (vapiMcpService.js:409:30)
    at async loadAssistantData (AssistantInfoSection.jsx:50:25)
(anonymous) @ consolidated-dashboard-fix.js:109
vapiMcpService.js:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
getAssistant @ vapiMcpService.js:427
vapiMcpService.js:432 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
getAssistant @ vapiMcpService.js:432
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: Object
AuthContext.jsx:85 OAuth user data: Object
AuthContext.jsx:88 OAuth user data details: Object
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: Object
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:67 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:85 OAuth user data: Object
AuthContext.jsx:88 OAuth user data details: Object
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: Object
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:67 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): Object
AuthContext.jsx:168 OAuth user data details (auth change): Object
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: Object
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: Object
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 500 (Internal Server Error)
vapi-official-pattern-fix.js:96 Auth state error details: Empty response from server
console.error @ vapi-official-pattern-fix.js:96
vapi-official-pattern-fix.js:96 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
console.error @ vapi-official-pattern-fix.js:96
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: Object
useSyncTools.js:516 Development mode: Using mock consistency check result
supabase.js:115 Supabase connection test successful!
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: Array(3)
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:682 Supabase is properly configured and connected! Array(1)
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: Object
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: Array(0)
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: Object
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: Object
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: Object
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: Object
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: Object
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:390 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:450 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:451 firmName: Your Law Firm
EnhancedPreviewNew.jsx:452 titleText: Your Law Firm
EnhancedPreviewNew.jsx:453 logoUrl: 
EnhancedPreviewNew.jsx:454 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:455 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:456 vapiInstructions: 
EnhancedPreviewNew.jsx:457 vapiAssistantId: null
EnhancedPreviewNew.jsx:458 voiceId: sarah
EnhancedPreviewNew.jsx:459 voiceProvider: playht
EnhancedPreviewNew.jsx:460 chatActive: false
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:390 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:450 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:451 firmName: Your Law Firm
EnhancedPreviewNew.jsx:452 titleText: Your Law Firm
EnhancedPreviewNew.jsx:453 logoUrl: 
EnhancedPreviewNew.jsx:454 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:455 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:456 vapiInstructions: 
EnhancedPreviewNew.jsx:457 vapiAssistantId: null
EnhancedPreviewNew.jsx:458 voiceId: sarah
EnhancedPreviewNew.jsx:459 voiceProvider: playht
EnhancedPreviewNew.jsx:460 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:636 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: Object
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: Array(0)
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: Object
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: Object
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: Object
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: Object
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: Object
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:682 Supabase is properly configured and connected! Array(1)
vapi-sdk-loader.js:33 [VapiSDKLoader] Trying CDN source 2/3: https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: Object
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: Object
simple-preview:1 Access to script at 'https://unpkg.com/@vapi-ai/web@2.3.1/dist/index.umd.js' (redirected from 'https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js') from origin 'http://localhost:5175' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
unpkg.com/@vapi-ai/web@2.3.1/dist/index.umd.js:1 
            
            
           Failed to load resource: net::ERR_FAILED
vapi-sdk-loader.js:33 [VapiSDKLoader] CDN source failed: Failed to load https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js
log @ vapi-sdk-loader.js:33
vapi-official-pattern-fix.js:212 [VapiOfficialPatternFix] ⚠️ Failed to load from https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js?t=1748978786767, trying next...
script.onerror @ vapi-official-pattern-fix.js:212
vapi-official-pattern-fix.js:96 [VapiOfficialPatternFix] ❌ Failed to load Vapi SDK from all CDN sources
console.error @ vapi-official-pattern-fix.js:96
DashboardNew.jsx:1288 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
AttorneyProfileManager.js:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
vapi-sdk-loader.js:33 [VapiSDKLoader] Trying CDN source 3/3: https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js
index.umd.js:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
simple-preview:1 Refused to execute script from 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js' because its MIME type ('text/plain') is not executable, and strict MIME type checking is enabled.
vapi-sdk-loader.js:33 [VapiSDKLoader] CDN source failed: Failed to load https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js
log @ vapi-sdk-loader.js:33
vapi-sdk-loader.js:33 [VapiSDKLoader] All CDN sources failed, trying dynamic import
vapi-sdk-loader.js:33 [VapiSDKLoader] Attempting dynamic import of @vapi-ai/web
vapi-sdk-loader.js:33 [VapiSDKLoader] Dynamic import failed: Failed to resolve module specifier '@vapi-ai/web'
log @ vapi-sdk-loader.js:33
vapi-official-pattern-fix.js:96 [VapiSDKLoader] ❌ Failed to load Vapi SDK from all sources
console.error @ vapi-official-pattern-fix.js:96
vapi-sdk-loader.js:33 [VapiSDKLoader] 💥 Vapi SDK setup failed
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787220}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787223}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787618}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787619}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787894}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (2/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:83
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:82
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1748978787127}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
