/**
 * Simple Webhook Test Script
 * 
 * Tests the webhook endpoint directly without complex dependencies
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '../../.env' });

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;
const WEBHOOK_URL = 'https://legalscout.app/api/webhook/vapi-call';
const LOCAL_WEBHOOK_URL = 'http://localhost:5175/api/webhook/vapi-call';

// Initialize Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Test call data
const TEST_CALL_DATA = {
  id: 'simple-test-' + Date.now(),
  assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
  status: 'completed',
  duration: 120,
  start_time: new Date(Date.now() - 120000).toISOString(),
  end_time: new Date().toISOString(),
  customer: {
    phone_number: '+19145893180'
  },
  transcripts: [
    {
      role: 'assistant',
      message: 'Hello! How can I help you today?',
      timestamp: new Date(Date.now() - 120000).toISOString()
    },
    {
      role: 'user',
      message: 'I need help with a legal matter.',
      timestamp: new Date(Date.now() - 100000).toISOString()
    }
  ],
  messages: [],
  tool_executions: [],
  metadata: {
    urgency: 'Medium',
    practice_area: 'General'
  }
};

/**
 * Test webhook endpoint
 */
async function testWebhook(url) {
  console.log(`\n🔍 Testing webhook: ${url}`);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'LegalScout-Test/1.0'
      },
      body: JSON.stringify(TEST_CALL_DATA)
    });

    console.log(`Response status: ${response.status}`);
    
    const responseText = await response.text();
    console.log(`Response body: ${responseText}`);

    if (response.ok) {
      console.log('✅ Webhook test successful');
      return true;
    } else {
      console.log('❌ Webhook test failed');
      return false;
    }
  } catch (error) {
    console.log(`❌ Webhook test error: ${error.message}`);
    return false;
  }
}

/**
 * Check Supabase connection
 */
async function testSupabase() {
  console.log('\n🔍 Testing Supabase connection...');
  
  try {
    // Test connection by querying attorneys
    const { data, error } = await supabase
      .from('attorneys')
      .select('id, email, vapi_assistant_id')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      console.log(`❌ Supabase error: ${error.message}`);
      return false;
    }

    console.log('✅ Supabase connection successful');
    console.log(`Attorney ID: ${data.id}`);
    console.log(`Assistant ID: ${data.vapi_assistant_id}`);
    return data;
  } catch (error) {
    console.log(`❌ Supabase test error: ${error.message}`);
    return false;
  }
}

/**
 * Check for call record
 */
async function checkCallRecord() {
  console.log('\n🔍 Checking for call record...');
  
  try {
    const { data, error } = await supabase
      .from('call_records')
      .select('*')
      .eq('call_id', TEST_CALL_DATA.id)
      .single();

    if (error) {
      console.log(`❌ No call record found: ${error.message}`);
      return false;
    }

    console.log('✅ Call record found');
    console.log(`Call ID: ${data.call_id}`);
    console.log(`Status: ${data.status}`);
    return data;
  } catch (error) {
    console.log(`❌ Error checking call record: ${error.message}`);
    return false;
  }
}

/**
 * Check for consultation
 */
async function checkConsultation() {
  console.log('\n🔍 Checking for consultation...');
  
  try {
    const { data, error } = await supabase
      .from('consultations')
      .select('*')
      .eq('metadata->call_id', TEST_CALL_DATA.id)
      .single();

    if (error) {
      console.log(`❌ No consultation found: ${error.message}`);
      return false;
    }

    console.log('✅ Consultation found');
    console.log(`Client: ${data.client_name}`);
    console.log(`Practice Area: ${data.practice_area}`);
    return data;
  } catch (error) {
    console.log(`❌ Error checking consultation: ${error.message}`);
    return false;
  }
}

/**
 * Clean up test data
 */
async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    await supabase
      .from('call_records')
      .delete()
      .eq('call_id', TEST_CALL_DATA.id);

    await supabase
      .from('consultations')
      .delete()
      .eq('metadata->call_id', TEST_CALL_DATA.id);

    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log(`❌ Cleanup error: ${error.message}`);
  }
}

/**
 * Check existing data
 */
async function checkExistingData() {
  console.log('\n🔍 Checking existing data...');
  
  try {
    // Check consultations
    const { data: consultations, error: consultError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', '87756a2c-a398-43f2-889a-b8815684df71')
      .order('created_at', { ascending: false })
      .limit(5);

    if (consultError) {
      console.log(`❌ Error fetching consultations: ${consultError.message}`);
    } else {
      console.log(`✅ Found ${consultations.length} existing consultations`);
      consultations.forEach((c, i) => {
        console.log(`  ${i + 1}. ${c.client_name} - ${c.practice_area} (${c.created_at})`);
      });
    }

    // Check call records
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('*')
      .eq('attorney_id', '87756a2c-a398-43f2-889a-b8815684df71')
      .order('start_time', { ascending: false })
      .limit(5);

    if (callError) {
      console.log(`❌ Error fetching call records: ${callError.message}`);
    } else {
      console.log(`✅ Found ${callRecords.length} existing call records`);
      callRecords.forEach((c, i) => {
        console.log(`  ${i + 1}. ${c.call_id} - ${c.status} (${c.start_time})`);
      });
    }

  } catch (error) {
    console.log(`❌ Error checking existing data: ${error.message}`);
  }
}

/**
 * Main test function
 */
async function runTest() {
  console.log('🚀 Starting Simple Webhook Test\n');
  console.log('Configuration:');
  console.log(`  Supabase URL: ${SUPABASE_URL}`);
  console.log(`  Test Call ID: ${TEST_CALL_DATA.id}`);
  console.log(`  Assistant ID: ${TEST_CALL_DATA.assistant_id}`);

  // Test Supabase connection
  const attorney = await testSupabase();
  if (!attorney) {
    console.log('❌ Cannot proceed without Supabase connection');
    return;
  }

  // Check existing data
  await checkExistingData();

  // Test production webhook
  console.log('\n📡 Testing Production Webhook');
  const prodSuccess = await testWebhook(WEBHOOK_URL);

  // Test local webhook if production fails
  if (!prodSuccess) {
    console.log('\n📡 Testing Local Webhook');
    await testWebhook(LOCAL_WEBHOOK_URL);
  }

  // Wait for processing
  console.log('\n⏳ Waiting for data processing...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Check results
  const callRecord = await checkCallRecord();
  const consultation = await checkConsultation();

  // Summary
  console.log('\n📊 TEST SUMMARY:');
  console.log(`  Supabase connection: ✅ Working`);
  console.log(`  Webhook response: ${prodSuccess ? '✅ Success' : '❌ Failed'}`);
  console.log(`  Call record created: ${callRecord ? '✅ Yes' : '❌ No'}`);
  console.log(`  Consultation created: ${consultation ? '✅ Yes' : '❌ No'}`);

  // Cleanup
  await cleanup();

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  if (!prodSuccess) {
    console.log('  ⚠️  Webhook endpoint is not responding - check deployment');
  }
  if (!callRecord) {
    console.log('  ⚠️  Call records are not being created - check webhook handler');
  }
  if (!consultation) {
    console.log('  ⚠️  Consultations are not being created - check consultation logic');
  }
}

// Run test
runTest().catch(console.error);
