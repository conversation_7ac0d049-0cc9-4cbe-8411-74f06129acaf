// This file is deprecated - environment variables are now handled by Vite
// Use import.meta.env.VITE_* variables instead

// Debug function for development
export const debugEnvironment = () => {
  if (import.meta.env.MODE === 'development') {
    console.log("VAPI_PUBLIC_KEY:", import.meta.env.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set');
    console.log("Environment mode:", import.meta.env.MODE);
  }
};