/**
 * <PERSON><PERSON><PERSON> to manually create consultation record for missing Vapi call
 * This fixes the issue where webhooks didn't trigger properly
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Create consultation record for a specific call
 */
async function createConsultationForCall(callId, assistantId, attorneyId) {
  try {
    // Check if consultation already exists
    const { data: existing } = await supabase
      .from('consultations')
      .select('id')
      .eq('metadata->call_id', callId)
      .single();

    if (existing) {
      console.log(`✅ Consultation already exists for call ${callId}`);
      return existing;
    }

    // Create consultation record
    const consultationRecord = {
      attorney_id: attorneyId,
      client_name: 'Anonymous Client',
      client_email: null,
      client_phone: null,
      summary: `Call ended with reason: customer-ended-call. Duration: 5 minutes. Status: ended.`,
      transcript: 'Transcript not available - call ended before completion or transcript not captured.',
      duration: 321, // 5 minutes 21 seconds (approximate from call timing)
      practice_area: null,
      location: null,
      location_data: {},
      metadata: {
        call_id: callId,
        assistant_id: assistantId,
        vapi_status: 'ended',
        vapi_end_reason: 'customer-ended-call',
        created_at: '2025-05-30T17:27:12.279Z',
        updated_at: '2025-05-30T17:32:33.432Z',
        manual_creation: true,
        manual_creation_reason: 'Webhook failed to trigger'
      },
      status: 'new'
    };

    // Insert consultation record
    const { data, error } = await supabase
      .from('consultations')
      .insert(consultationRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    console.log(`✅ Created consultation record for call ${callId}`);
    console.log(`   Consultation ID: ${data.id}`);
    return data;

  } catch (error) {
    console.error(`❌ Error creating consultation for call ${callId}:`, error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🔧 Creating missing consultation record...');

    // The specific call that's missing
    const callId = 'a8e52521-000e-4c48-9e04-89ad61841e00';
    const assistantId = '9621936c-cb51-489c-a869-0c33052f0e42';
    const attorneyId = '571390ac-5a83-46b2-ad3a-18b9cf39d701'; // damon's attorney ID

    const consultation = await createConsultationForCall(callId, assistantId, attorneyId);

    console.log('✅ Successfully created consultation record!');
    console.log('   You should now see this call in your Briefs/Consultations tab.');

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
