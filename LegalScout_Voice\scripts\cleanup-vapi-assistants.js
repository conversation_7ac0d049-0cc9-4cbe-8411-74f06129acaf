/**
 * Vapi Assistant <PERSON><PERSON>t
 *
 * This script analyzes and cleans up the Vapi assistant creation issues:
 * 1. Identifies orphaned assistants in Vapi
 * 2. Finds invalid assistant IDs in the database
 * 3. Cleans up mock IDs and API key confusion
 * 4. Prevents future duplicate creation
 */

// Use direct API calls instead of complex imports for now
import fetch from 'node-fetch';

// Known valid assistant IDs from Vapi
const VALID_VAPI_ASSISTANTS = [
  'eb8533fa-902e-46be-8ce9-df20f5c550d7',
  '368e963b-761c-45bb-91e9-8f96b8483f4d',
  'efcadef8-7d6e-49b1-91fb-e74d4223e695',
  'e0705a0d-0511-4d91-b185-56feba033b76',
  'edd3008b-ac5e-4026-97fb-d556cc1def1e',
  'a53344e3-1edb-4917-9b2d-fbd28f4e5dbf',
  'd1dae707-d4b9-4728-9a29-210ccc3b4f5e',
  '5b264951-c02d-43a0-99e0-902578192706',
  'fd273605-12af-438b-9fa4-31cc0dfb4af4',
  '2ecce4a5-a2ca-4a9a-a75d-8821b8294589'
];

// Known invalid/problematic IDs
const INVALID_IDS = {
  API_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  MOCK_PATTERN: /mock|MOCK|DO-NOT-SAVE/,
  ORPHANED: [
    '8d962209-530e-45d2-b2d6-17ed1ef55b3c',
    '7e9e3f5a-213b-4f7f-8ab7-030701a338c2',
    '36ad37ef-77b3-4941-96aa-71907ef60f42',
    '17b3d09a-a8be-4cae-94be-5c03de427f69',
    'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865'
  ]
};

async function analyzeAssistantIssues() {
  console.log('🔍 Starting Vapi Assistant Analysis...\n');

  try {
    // For now, let's just analyze the data we already have
    console.log('📊 Analysis based on known data:');

    console.log('📊 Analysis Results:');
    console.log(`   Orphaned Assistants in Vapi: ${analysis.orphanedAssistants.length}`);
    console.log(`   Invalid Database Entries: ${analysis.invalidDatabaseEntries.length}\n`);

    // Get detailed database analysis
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('id, subdomain, firm_name, email, vapi_assistant_id, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    console.log('🏢 Attorney Database Analysis:');
    console.log(`   Total Attorneys: ${attorneys.length}`);

    const categories = {
      validAssistants: [],
      orphanedIds: [],
      mockIds: [],
      apiKeyError: [],
      noAssistant: []
    };

    // Categorize attorneys by their assistant status
    for (const attorney of attorneys) {
      if (!attorney.vapi_assistant_id) {
        categories.noAssistant.push(attorney);
      } else if (attorney.vapi_assistant_id === INVALID_IDS.API_KEY) {
        categories.apiKeyError.push(attorney);
      } else if (INVALID_IDS.MOCK_PATTERN.test(attorney.vapi_assistant_id)) {
        categories.mockIds.push(attorney);
      } else if (VALID_VAPI_ASSISTANTS.includes(attorney.vapi_assistant_id)) {
        categories.validAssistants.push(attorney);
      } else {
        categories.orphanedIds.push(attorney);
      }
    }

    console.log(`   ✅ Valid Assistants: ${categories.validAssistants.length}`);
    console.log(`   🔗 Orphaned IDs: ${categories.orphanedIds.length}`);
    console.log(`   🎭 Mock IDs: ${categories.mockIds.length}`);
    console.log(`   🔑 API Key Error: ${categories.apiKeyError.length}`);
    console.log(`   ❌ No Assistant: ${categories.noAssistant.length}\n`);

    // Show detailed breakdown
    if (categories.validAssistants.length > 0) {
      console.log('✅ Attorneys with Valid Assistants:');
      categories.validAssistants.forEach(att => {
        console.log(`   ${att.subdomain} (${att.firm_name}) -> ${att.vapi_assistant_id}`);
      });
      console.log();
    }

    if (categories.orphanedIds.length > 0) {
      console.log('🔗 Attorneys with Orphaned Assistant IDs:');
      categories.orphanedIds.forEach(att => {
        console.log(`   ${att.subdomain} (${att.firm_name}) -> ${att.vapi_assistant_id}`);
      });
      console.log();
    }

    if (categories.mockIds.length > 0) {
      console.log('🎭 Attorneys with Mock IDs (CRITICAL - should be cleaned):');
      categories.mockIds.forEach(att => {
        console.log(`   ${att.subdomain} (${att.firm_name}) -> ${att.vapi_assistant_id}`);
      });
      console.log();
    }

    if (categories.apiKeyError.length > 0) {
      console.log('🔑 Attorneys with API Key as Assistant ID (CRITICAL):');
      categories.apiKeyError.forEach(att => {
        console.log(`   ${att.subdomain} (${att.firm_name}) -> ${att.vapi_assistant_id}`);
      });
      console.log();
    }

    console.log('🔍 Orphaned Assistants in Vapi (not linked to any attorney):');
    analysis.orphanedAssistants.forEach(assistant => {
      console.log(`   ${assistant.name} (${assistant.id}) - Created: ${assistant.createdAt}`);
    });

    return {
      analysis,
      categories,
      attorneys
    };

  } catch (error) {
    console.error('❌ Error during analysis:', error);
    throw error;
  }
}

async function cleanupInvalidDatabaseEntries(dryRun = true) {
  console.log(`\n🧹 ${dryRun ? 'DRY RUN - ' : ''}Cleaning up invalid database entries...\n`);

  try {
    // Get attorneys with invalid assistant IDs
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('id, subdomain, firm_name, vapi_assistant_id')
      .not('vapi_assistant_id', 'is', null);

    if (error) {
      throw error;
    }

    const toCleanup = [];

    for (const attorney of attorneys) {
      const assistantId = attorney.vapi_assistant_id;

      // Check for various invalid conditions
      if (assistantId === INVALID_IDS.API_KEY) {
        toCleanup.push({ attorney, reason: 'API_KEY_ERROR', action: 'SET_NULL' });
      } else if (INVALID_IDS.MOCK_PATTERN.test(assistantId)) {
        toCleanup.push({ attorney, reason: 'MOCK_ID', action: 'SET_NULL' });
      } else if (INVALID_IDS.ORPHANED.includes(assistantId)) {
        toCleanup.push({ attorney, reason: 'ORPHANED_ID', action: 'SET_NULL' });
      }
    }

    console.log(`Found ${toCleanup.length} entries to clean up:`);

    for (const item of toCleanup) {
      console.log(`   ${item.attorney.subdomain}: ${item.reason} -> ${item.action}`);

      if (!dryRun) {
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({ vapi_assistant_id: null })
          .eq('id', item.attorney.id);

        if (updateError) {
          console.error(`   ❌ Error updating ${item.attorney.subdomain}:`, updateError);
        } else {
          console.log(`   ✅ Cleaned up ${item.attorney.subdomain}`);
        }
      }
    }

    return toCleanup;

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Vapi Assistant Cleanup Tool\n');

    // Run analysis
    const results = await analyzeAssistantIssues();

    // Run cleanup in dry-run mode first
    await cleanupInvalidDatabaseEntries(true);

    console.log('\n📋 Summary:');
    console.log('   - Analysis complete');
    console.log('   - Run with --cleanup flag to perform actual cleanup');
    console.log('   - Enhanced manager now prevents duplicate creation');

  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Check for command line arguments
const args = process.argv.slice(2);
const shouldCleanup = args.includes('--cleanup');

if (shouldCleanup) {
  console.log('⚠️  CLEANUP MODE ENABLED - This will modify the database!\n');
}

main();
