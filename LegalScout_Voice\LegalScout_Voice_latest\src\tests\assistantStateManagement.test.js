/**
 * Comprehensive test suite for assistant state management
 * Tests that all assistant-specific variables are properly isolated and preserved
 */

import { assistantUIConfigService } from '../services/assistantUIConfigService';

// Mock data for testing
const mockAttorneyId = 'test-attorney-123';
const mockAssistant1 = {
  id: 'assistant-1',
  name: 'Legal Assistant 1',
  config: {
    firm_name: 'Law Firm Alpha',
    primary_color: '#ff0000',
    secondary_color: '#ff4444',
    button_color: '#ff8888',
    background_color: '#ffffff',
    logo_url: 'https://example.com/logo1.png',
    assistant_image_url: 'https://example.com/assistant1.png',
    voice_provider: '11labs',
    voice_id: 'sarah',
    ai_model: 'gpt-4o',
    welcome_message: 'Welcome to Law Firm Alpha',
    practice_description: 'We specialize in corporate law',
    vapi_instructions: 'You are a corporate law assistant',
    practice_areas: ['Corporate Law', 'Business Law'],
    office_address: '123 Main St, City A',
    scheduling_link: 'https://calendly.com/firm-alpha'
  }
};

const mockAssistant2 = {
  id: 'assistant-2',
  name: 'Legal Assistant 2',
  config: {
    firm_name: 'Law Firm Beta',
    primary_color: '#0000ff',
    secondary_color: '#4444ff',
    button_color: '#8888ff',
    background_color: '#f0f0f0',
    logo_url: 'https://example.com/logo2.png',
    assistant_image_url: 'https://example.com/assistant2.png',
    voice_provider: 'openai',
    voice_id: 'echo',
    ai_model: 'gpt-3.5-turbo',
    welcome_message: 'Welcome to Law Firm Beta',
    practice_description: 'We specialize in family law',
    vapi_instructions: 'You are a family law assistant',
    practice_areas: ['Family Law', 'Divorce Law'],
    office_address: '456 Oak Ave, City B',
    scheduling_link: 'https://calendly.com/firm-beta'
  }
};

/**
 * Test class for assistant state management
 */
export class AssistantStateTest {
  constructor() {
    this.testResults = [];
    this.currentTest = null;
  }

  /**
   * Log test result
   */
  logResult(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} [AssistantStateTest] ${testName}: ${message}`);
  }

  /**
   * Test that assistant configurations are properly isolated
   */
  async testAssistantIsolation() {
    this.currentTest = 'Assistant Configuration Isolation';
    
    try {
      // Save configuration for assistant 1
      await assistantUIConfigService.saveAssistantConfig(
        mockAttorneyId,
        mockAssistant1.id,
        mockAssistant1.config
      );

      // Save configuration for assistant 2
      await assistantUIConfigService.saveAssistantConfig(
        mockAttorneyId,
        mockAssistant2.id,
        mockAssistant2.config
      );

      // Retrieve assistant 1 config
      const config1 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant1.id
      );

      // Retrieve assistant 2 config
      const config2 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant2.id
      );

      // Verify isolation - assistant 1 should have its own values
      const isolation1Passed = 
        config1.firm_name === mockAssistant1.config.firm_name &&
        config1.primary_color === mockAssistant1.config.primary_color &&
        config1.voice_id === mockAssistant1.config.voice_id;

      // Verify isolation - assistant 2 should have its own values
      const isolation2Passed = 
        config2.firm_name === mockAssistant2.config.firm_name &&
        config2.primary_color === mockAssistant2.config.primary_color &&
        config2.voice_id === mockAssistant2.config.voice_id;

      // Verify no cross-contamination
      const noCrossContamination = 
        config1.firm_name !== config2.firm_name &&
        config1.primary_color !== config2.primary_color &&
        config1.voice_id !== config2.voice_id;

      const allPassed = isolation1Passed && isolation2Passed && noCrossContamination;
      
      this.logResult(
        this.currentTest,
        allPassed,
        allPassed ? 'Configurations properly isolated' : 'Configuration isolation failed'
      );

      return allPassed;
    } catch (error) {
      this.logResult(this.currentTest, false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test assistant switching preserves individual configurations
   */
  async testAssistantSwitching() {
    this.currentTest = 'Assistant Switching Preservation';
    
    try {
      // Switch to assistant 1
      const config1 = await assistantUIConfigService.switchToAssistant(
        mockAttorneyId,
        mockAssistant1.id
      );

      // Verify assistant 1 configuration is loaded
      const switch1Correct = 
        config1.firm_name === mockAssistant1.config.firm_name &&
        config1.primary_color === mockAssistant1.config.primary_color;

      // Switch to assistant 2
      const config2 = await assistantUIConfigService.switchToAssistant(
        mockAttorneyId,
        mockAssistant2.id
      );

      // Verify assistant 2 configuration is loaded
      const switch2Correct = 
        config2.firm_name === mockAssistant2.config.firm_name &&
        config2.primary_color === mockAssistant2.config.primary_color;

      // Switch back to assistant 1
      const config1Again = await assistantUIConfigService.switchToAssistant(
        mockAttorneyId,
        mockAssistant1.id
      );

      // Verify assistant 1 configuration is still preserved
      const preservationCorrect = 
        config1Again.firm_name === mockAssistant1.config.firm_name &&
        config1Again.primary_color === mockAssistant1.config.primary_color;

      const allPassed = switch1Correct && switch2Correct && preservationCorrect;
      
      this.logResult(
        this.currentTest,
        allPassed,
        allPassed ? 'Assistant switching preserves configurations' : 'Assistant switching failed'
      );

      return allPassed;
    } catch (error) {
      this.logResult(this.currentTest, false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test all visual variables are assistant-specific
   */
  async testVisualVariableIsolation() {
    this.currentTest = 'Visual Variable Isolation';
    
    const visualVariables = [
      'firm_name',
      'primary_color',
      'secondary_color', 
      'button_color',
      'background_color',
      'logo_url',
      'assistant_image_url'
    ];

    try {
      const config1 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant1.id
      );

      const config2 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant2.id
      );

      const isolationResults = visualVariables.map(variable => {
        const isolated = config1[variable] !== config2[variable];
        if (!isolated) {
          console.warn(`⚠️ Visual variable '${variable}' not properly isolated: ${config1[variable]} === ${config2[variable]}`);
        }
        return isolated;
      });

      const allIsolated = isolationResults.every(result => result);
      
      this.logResult(
        this.currentTest,
        allIsolated,
        allIsolated ? 'All visual variables properly isolated' : 'Some visual variables not isolated'
      );

      return allIsolated;
    } catch (error) {
      this.logResult(this.currentTest, false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test Vapi-specific variables are assistant-specific
   */
  async testVapiVariableIsolation() {
    this.currentTest = 'Vapi Variable Isolation';
    
    const vapiVariables = [
      'voice_provider',
      'voice_id',
      'ai_model',
      'vapi_instructions',
      'welcome_message'
    ];

    try {
      const config1 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant1.id
      );

      const config2 = await assistantUIConfigService.getAssistantConfig(
        mockAttorneyId,
        mockAssistant2.id
      );

      const isolationResults = vapiVariables.map(variable => {
        const isolated = config1[variable] !== config2[variable];
        if (!isolated) {
          console.warn(`⚠️ Vapi variable '${variable}' not properly isolated: ${config1[variable]} === ${config2[variable]}`);
        }
        return isolated;
      });

      const allIsolated = isolationResults.every(result => result);
      
      this.logResult(
        this.currentTest,
        allIsolated,
        allIsolated ? 'All Vapi variables properly isolated' : 'Some Vapi variables not isolated'
      );

      return allIsolated;
    } catch (error) {
      this.logResult(this.currentTest, false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 [AssistantStateTest] Starting comprehensive assistant state management tests...');
    
    const tests = [
      () => this.testAssistantIsolation(),
      () => this.testAssistantSwitching(),
      () => this.testVisualVariableIsolation(),
      () => this.testVapiVariableIsolation()
    ];

    const results = [];
    for (const test of tests) {
      const result = await test();
      results.push(result);
    }

    const allPassed = results.every(result => result);
    const passedCount = results.filter(result => result).length;
    
    console.log(`\n📊 [AssistantStateTest] Test Summary: ${passedCount}/${results.length} tests passed`);
    
    if (allPassed) {
      console.log('✅ [AssistantStateTest] All tests passed! Assistant state management is working correctly.');
    } else {
      console.log('❌ [AssistantStateTest] Some tests failed. Assistant state management needs attention.');
    }

    return {
      allPassed,
      passedCount,
      totalCount: results.length,
      results: this.testResults
    };
  }

  /**
   * Clean up test data
   */
  async cleanup() {
    try {
      await assistantUIConfigService.deleteAssistantConfig(mockAttorneyId, mockAssistant1.id);
      await assistantUIConfigService.deleteAssistantConfig(mockAttorneyId, mockAssistant2.id);
      console.log('🧹 [AssistantStateTest] Test data cleaned up');
    } catch (error) {
      console.warn('⚠️ [AssistantStateTest] Error during cleanup:', error.message);
    }
  }
}

// Export for use in other modules
export const assistantStateTest = new AssistantStateTest();
