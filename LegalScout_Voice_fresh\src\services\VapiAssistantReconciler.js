/**
 * Vapi Assistant Reconciler
 * 
 * This service handles assistant ID conflicts and ensures data consistency
 * between Supabase and Vapi using the MCP server for robust operations.
 * 
 * Features:
 * - Detects assistant ID mismatches
 * - Validates assistants exist in Vapi
 * - Automatically reconciles conflicts
 * - Prevents duplicate assistant creation
 * - Provides detailed diagnostics
 */

import { supabase } from '../lib/supabase';

class VapiAssistantReconciler {
  constructor() {
    this.mcpService = null;
    this.diagnostics = [];
  }

  /**
   * Initialize the MCP service connection
   */
  async initializeMcp() {
    try {
      // Import the Vapi MCP service
      const { vapiMcpService } = await import('./vapiMcpService');
      await vapiMcpService.ensureConnection();
      this.mcpService = vapiMcpService;
      
      this.log('✅ MCP service initialized successfully');
      return true;
    } catch (error) {
      this.log(`❌ Failed to initialize MCP service: ${error.message}`);
      return false;
    }
  }

  /**
   * Log diagnostic information
   */
  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(`[VapiAssistantReconciler] ${message}`);
    this.diagnostics.push(logEntry);
  }

  /**
   * Get all assistants for an attorney from Vapi
   */
  async getVapiAssistants() {
    try {
      if (!this.mcpService) {
        throw new Error('MCP service not initialized');
      }

      // List all assistants from Vapi
      const assistants = await this.mcpService.listAssistants();
      this.log(`Found ${assistants.length} assistants in Vapi`);
      
      return assistants;
    } catch (error) {
      this.log(`❌ Error fetching Vapi assistants: ${error.message}`);
      return [];
    }
  }

  /**
   * Validate if an assistant ID exists in Vapi
   */
  async validateAssistantInVapi(assistantId) {
    try {
      if (!assistantId || assistantId.startsWith('mock-')) {
        return { valid: false, reason: 'Invalid or mock assistant ID' };
      }

      const assistant = await this.mcpService.getAssistant(assistantId);
      
      if (assistant) {
        this.log(`✅ Assistant ${assistantId} exists in Vapi: "${assistant.name}"`);
        return { 
          valid: true, 
          assistant,
          name: assistant.name,
          createdAt: assistant.createdAt,
          updatedAt: assistant.updatedAt
        };
      } else {
        this.log(`❌ Assistant ${assistantId} not found in Vapi`);
        return { valid: false, reason: 'Assistant not found in Vapi' };
      }
    } catch (error) {
      this.log(`❌ Error validating assistant ${assistantId}: ${error.message}`);
      return { valid: false, reason: error.message };
    }
  }

  /**
   * Find assistants that might belong to an attorney
   */
  async findAttorneyAssistants(attorney) {
    const vapiAssistants = await this.getVapiAssistants();
    const potentialMatches = [];

    // Look for assistants with matching firm name or similar patterns
    const firmName = attorney.firm_name?.toLowerCase() || '';
    const email = attorney.email?.toLowerCase() || '';
    
    for (const assistant of vapiAssistants) {
      const assistantName = assistant.name?.toLowerCase() || '';
      
      // Check for firm name matches
      if (firmName && assistantName.includes(firmName)) {
        potentialMatches.push({
          ...assistant,
          matchReason: `Firm name match: "${assistant.name}" contains "${attorney.firm_name}"`
        });
      }
      
      // Check for "LegalScout" pattern (specific to your case)
      if (assistantName.includes('legalscout')) {
        potentialMatches.push({
          ...assistant,
          matchReason: `LegalScout pattern match: "${assistant.name}"`
        });
      }
    }

    this.log(`Found ${potentialMatches.length} potential assistant matches for ${attorney.email}`);
    return potentialMatches;
  }

  /**
   * Comprehensive assistant reconciliation for an attorney
   */
  async reconcileAttorneyAssistant(attorney) {
    this.log(`🔍 Starting reconciliation for attorney: ${attorney.email}`);

    const result = {
      success: false,
      action: null,
      assistantId: null,
      message: '',
      diagnostics: [],
      conflicts: []
    };

    try {
      // Step 1: Check current assistant ID in database
      const currentAssistantId = attorney.vapi_assistant_id;
      this.log(`Current assistant ID in database: ${currentAssistantId || 'None'}`);

      // Step 2: Validate current assistant in Vapi
      let currentAssistantValid = false;
      let currentAssistantInfo = null;

      if (currentAssistantId) {
        const validation = await this.validateAssistantInVapi(currentAssistantId);
        currentAssistantValid = validation.valid;
        currentAssistantInfo = validation.assistant;

        if (!currentAssistantValid) {
          result.conflicts.push({
            type: 'invalid_assistant',
            assistantId: currentAssistantId,
            reason: validation.reason
          });
        }
      }

      // Step 3: Find all potential assistants for this attorney
      const potentialAssistants = await this.findAttorneyAssistants(attorney);

      // Step 4: ULTRA-THINKING LOGIC - Prioritize by creation date and name match
      if (currentAssistantValid && currentAssistantInfo) {
        // Current assistant is valid, no action needed
        result.success = true;
        result.action = 'validated';
        result.assistantId = currentAssistantId;
        result.message = `Assistant "${currentAssistantInfo.name}" is valid and connected`;

      } else if (potentialAssistants.length >= 1) {
        // SMART SELECTION: Choose the best match based on multiple criteria
        const bestMatch = this.selectBestAssistant(potentialAssistants, attorney);

        result.success = true;
        result.action = 'auto_resolved';
        result.assistantId = bestMatch.id;
        result.message = `Auto-resolved to: "${bestMatch.name}" (${bestMatch.selectionReason})`;

        // Update database immediately
        await this.updateAttorneyAssistantId(attorney.id, bestMatch.id);

        // Log other potential matches as conflicts for transparency
        if (potentialAssistants.length > 1) {
          result.conflicts.push({
            type: 'multiple_matches_resolved',
            selected: bestMatch,
            alternatives: potentialAssistants.filter(a => a.id !== bestMatch.id)
          });
        }

      } else {
        // No matches found, suggest creating new assistant
        result.success = false;
        result.action = 'create_needed';
        result.message = 'No matching assistants found. New assistant creation recommended.';
      }

      result.diagnostics = this.diagnostics;
      this.log(`🏁 Reconciliation complete: ${result.action} - ${result.message}`);

      return result;

    } catch (error) {
      this.log(`❌ Reconciliation failed: ${error.message}`);
      result.success = false;
      result.action = 'error';
      result.message = error.message;
      result.diagnostics = this.diagnostics;
      return result;
    }
  }

  /**
   * ULTRA-THINKING: Select the best assistant from multiple matches
   * Priority: 1) Exact firm name match, 2) Older creation date, 3) More complete name
   */
  selectBestAssistant(assistants, attorney) {
    const firmName = attorney.firm_name?.toLowerCase() || '';

    // Score each assistant
    const scoredAssistants = assistants.map(assistant => {
      let score = 0;
      let reasons = [];

      const assistantName = assistant.name?.toLowerCase() || '';

      // Exact firm name match gets highest priority
      if (firmName && assistantName === firmName) {
        score += 100;
        reasons.push('exact firm name match');
      } else if (firmName && assistantName.includes(firmName)) {
        score += 50;
        reasons.push('firm name contained');
      }

      // Prefer "Assistant" in name (more complete)
      if (assistantName.includes('assistant')) {
        score += 20;
        reasons.push('has "assistant" in name');
      }

      // Prefer older assistants (more established)
      const createdAt = new Date(assistant.createdAt);
      const daysSinceCreation = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreation > 1) {
        score += Math.min(daysSinceCreation, 30); // Cap at 30 days bonus
        reasons.push(`${Math.floor(daysSinceCreation)} days old`);
      }

      return {
        ...assistant,
        score,
        selectionReason: reasons.join(', ') || 'default match'
      };
    });

    // Sort by score (highest first)
    scoredAssistants.sort((a, b) => b.score - a.score);

    const selected = scoredAssistants[0];
    this.log(`🎯 Selected assistant "${selected.name}" (score: ${selected.score}) from ${assistants.length} options`);

    return selected;
  }

  /**
   * Update attorney's assistant ID in Supabase
   */
  async updateAttorneyAssistantId(attorneyId, assistantId) {
    try {
      const { error } = await supabase
        .from('attorneys')
        .update({ 
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId);

      if (error) {
        throw error;
      }

      this.log(`✅ Updated attorney ${attorneyId} with assistant ID ${assistantId}`);
      return true;
    } catch (error) {
      this.log(`❌ Failed to update attorney assistant ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Run diagnostics for all attorneys
   */
  async runFullDiagnostics() {
    this.log('🚀 Starting full assistant diagnostics');
    
    try {
      // Initialize MCP service
      const mcpReady = await this.initializeMcp();
      if (!mcpReady) {
        throw new Error('Could not initialize MCP service');
      }

      // Get all attorneys from database
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('*');

      if (error) {
        throw error;
      }

      this.log(`Found ${attorneys.length} attorneys to check`);

      const results = [];
      
      for (const attorney of attorneys) {
        this.log(`\n--- Checking attorney: ${attorney.email} ---`);
        const result = await this.reconcileAttorneyAssistant(attorney);
        results.push({
          attorney: attorney.email,
          ...result
        });
      }

      return {
        success: true,
        results,
        summary: this.generateSummary(results)
      };
      
    } catch (error) {
      this.log(`❌ Full diagnostics failed: ${error.message}`);
      return {
        success: false,
        error: error.message,
        diagnostics: this.diagnostics
      };
    }
  }

  /**
   * Generate summary of diagnostics results
   */
  generateSummary(results) {
    const summary = {
      total: results.length,
      validated: 0,
      matched: 0,
      conflicts: 0,
      createNeeded: 0,
      errors: 0
    };

    results.forEach(result => {
      switch (result.action) {
        case 'validated':
          summary.validated++;
          break;
        case 'matched':
          summary.matched++;
          break;
        case 'conflict':
          summary.conflicts++;
          break;
        case 'create_needed':
          summary.createNeeded++;
          break;
        case 'error':
          summary.errors++;
          break;
      }
    });

    return summary;
  }

  /**
   * Get diagnostics log
   */
  getDiagnostics() {
    return this.diagnostics;
  }

  /**
   * Clear diagnostics log
   */
  clearDiagnostics() {
    this.diagnostics = [];
  }
}

// Export singleton instance
export const vapiAssistantReconciler = new VapiAssistantReconciler();
export default VapiAssistantReconciler;
