/**
 * useCallMonitoring Hook
 * 
 * This hook provides real-time monitoring of Vapi calls, including status updates,
 * transcripts, and control functions.
 */

import { useState, useEffect, useCallback } from 'react';
import { enhancedVapiCallService } from '../services/EnhancedVapiCallService';

/**
 * Hook for monitoring a call
 * @param {string} callId - Call ID to monitor
 * @returns {Object} - Call monitoring state and functions
 */
export const useCallMonitoring = (callId) => {
  const [call, setCall] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transcripts, setTranscripts] = useState([]);
  const [status, setStatus] = useState('unknown');
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  
  // Load call data and set up WebSocket monitoring
  useEffect(() => {
    let mounted = true;
    let pollingInterval;
    let websocket = null;

    const loadCall = async () => {
      try {
        if (!callId) return;

        setLoading(true);

        // Get call data using direct API to avoid CORS issues
        // Use the private key for server-side operations
        const apiKey = import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

        console.log('[useCallMonitoring] Using API key:', apiKey?.substring(0, 8) + '...');

        const response = await fetch(`https://api.vapi.ai/call/${callId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('[useCallMonitoring] API Error Response:', {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });
          throw new Error(`Failed to get call: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const callData = await response.json();
        console.log('[useCallMonitoring] Call data received:', callData);

        if (mounted) {
          setCall(callData);
          setStatus(callData.status);
          setLoading(false);

          console.log('[useCallMonitoring] Call data:', {
            id: callData.id,
            status: callData.status,
            hasMonitor: !!callData.monitor,
            listenUrl: callData.monitor?.listenUrl,
            controlUrl: callData.monitor?.controlUrl
          });

          // Set up WebSocket connection for real-time monitoring if available
          if (callData.monitor?.listenUrl && !websocket) {
            console.log('[useCallMonitoring] Setting up WebSocket monitoring');
            setupWebSocketMonitoring(callData.monitor.listenUrl);
          } else {
            console.log('[useCallMonitoring] No monitor URLs available - call may not be active yet');
          }
        }
      } catch (err) {
        if (mounted) {
          console.error('[useCallMonitoring] Error loading call:', err);
          setError(err.message);
          setLoading(false);
        }
      }
    };

    const setupWebSocketMonitoring = (listenUrl) => {
      try {
        console.log('[useCallMonitoring] Setting up WebSocket monitoring:', listenUrl);

        websocket = new WebSocket(listenUrl);

        websocket.onopen = () => {
          console.log('[useCallMonitoring] WebSocket connected');
        };

        websocket.onmessage = (event) => {
          try {
            if (event.data instanceof Blob) {
              // Handle binary audio data if needed
              console.log('[useCallMonitoring] Received audio data');
              return;
            }

            const data = JSON.parse(event.data);
            console.log('[useCallMonitoring] WebSocket message:', data);

            // Handle different message types
            if (data.type === 'transcript') {
              setTranscripts(prev => [...prev, {
                role: data.role || 'user',
                message: data.transcript || data.text,
                timestamp: data.timestamp || Date.now()
              }]);
            } else if (data.type === 'status') {
              setStatus(data.status);
            } else if (data.type === 'speaking') {
              setAssistantIsSpeaking(data.isSpeaking);
            } else if (data.type === 'volume') {
              setVolumeLevel(data.level);
            }
          } catch (parseError) {
            console.warn('[useCallMonitoring] Error parsing WebSocket message:', parseError);
          }
        };

        websocket.onerror = (error) => {
          console.error('[useCallMonitoring] WebSocket error:', error);
        };

        websocket.onclose = () => {
          console.log('[useCallMonitoring] WebSocket disconnected');
          websocket = null;
        };
      } catch (wsError) {
        console.error('[useCallMonitoring] Error setting up WebSocket:', wsError);
      }
    };

    loadCall();

    // Set up polling for updates (less frequent since we have WebSocket)
    pollingInterval = setInterval(loadCall, 10000);

    return () => {
      mounted = false;
      clearInterval(pollingInterval);
      if (websocket) {
        websocket.close();
      }
    };
  }, [callId]);
  
  // Note: Removed old event listeners that used enhancedVapiCallService
  // Now using WebSocket connections directly for real-time updates
  
  // End call function
  const endCall = useCallback(async () => {
    try {
      if (!callId || !call) return false;

      // Use the control URL if available
      if (call.monitor?.controlUrl) {
        const response = await fetch(call.monitor.controlUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'end-call'
          })
        });

        if (response.ok) {
          setStatus('completed');
          return true;
        }
      }

      // Fallback to direct API
      const response = await fetch(`https://api.vapi.ai/call/${callId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_VAPI_SECRET_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: 'ended'
        })
      });

      if (response.ok) {
        setStatus('completed');
        return true;
      }

      return false;
    } catch (err) {
      console.error('[useCallMonitoring] Error ending call:', err);
      setError(err.message);
      return false;
    }
  }, [callId, call]);
  
  // Send message to call
  const sendMessage = useCallback(async (message) => {
    try {
      if (!callId || !message || !call) return false;

      // Use the control URL if available
      if (call.monitor?.controlUrl) {
        const response = await fetch(call.monitor.controlUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'say',
            content: message,
            endCallAfterSpoken: false
          })
        });

        return response.ok;
      }

      return false;
    } catch (err) {
      console.error('[useCallMonitoring] Error sending message:', err);
      setError(err.message);
      return false;
    }
  }, [callId, call]);
  
  return {
    call,
    loading,
    error,
    transcripts,
    status,
    assistantIsSpeaking,
    volumeLevel,
    endCall,
    sendMessage
  };
};

export default useCallMonitoring;
