<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subdomain Consistency</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .comparison-item h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Subdomain Consistency Test</h1>
        
        <div class="info">
            <strong>Issue:</strong> Dashboard preview shows "Testing lawform Firm" and "Welcome to Camp lackbottom trivia" 
            but subdomain shows "Smith & Associates, LLP" and "Welcome to our legal practice"
        </div>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="setTestSubdomain()">Set Test Subdomain to 'damonkost'</button>
            <button onclick="clearTestSubdomain()">Clear Test Subdomain</button>
            <button onclick="reloadFrames()">Reload Both Frames</button>
            <button onclick="checkDatabase()">Check Database Data</button>
        </div>

        <div class="comparison">
            <div class="comparison-item">
                <h3>Dashboard Preview (Should match subdomain)</h3>
                <div class="iframe-container">
                    <iframe id="dashboardPreview" src="http://localhost:5173/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true"></iframe>
                </div>
            </div>

            <div class="comparison-item">
                <h3>Simulated Subdomain Page (Should match preview)</h3>
                <div class="iframe-container">
                    <iframe id="subdomainPage" src="http://localhost:5173/"></iframe>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Debug Information</h2>
            <div id="debugInfo">
                <p>Open browser console to see debug logs</p>
            </div>
        </div>
    </div>

    <script>
        function setTestSubdomain() {
            localStorage.setItem('legalscout_test_subdomain', 'damonkost');
            console.log('Set test subdomain to: damonkost');
            document.getElementById('debugInfo').innerHTML += '<p>✓ Set test subdomain to: damonkost</p>';
            reloadFrames();
        }

        function clearTestSubdomain() {
            localStorage.removeItem('legalscout_test_subdomain');
            console.log('Cleared test subdomain');
            document.getElementById('debugInfo').innerHTML += '<p>✓ Cleared test subdomain</p>';
            reloadFrames();
        }

        function reloadFrames() {
            const dashboardFrame = document.getElementById('dashboardPreview');
            const subdomainFrame = document.getElementById('subdomainPage');
            
            // Add timestamp to force reload
            const timestamp = Date.now();
            dashboardFrame.src = `http://localhost:5173/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&t=${timestamp}`;
            subdomainFrame.src = `http://localhost:5173/?t=${timestamp}`;
            
            console.log('Reloaded both frames');
            document.getElementById('debugInfo').innerHTML += '<p>✓ Reloaded both frames</p>';
        }

        async function checkDatabase() {
            try {
                // This would need to be implemented with proper API calls
                console.log('Checking database for damonkost subdomain...');
                document.getElementById('debugInfo').innerHTML += '<p>✓ Check browser console for database query results</p>';
                
                // Log what we expect to see
                console.log('Expected from database:');
                console.log('- firm_name: "Testing lawform"');
                console.log('- title_text: "Camp Lakebottom"');
                console.log('- welcome_message: "Welcome to Camp lackbottom trivia."');
            } catch (error) {
                console.error('Error checking database:', error);
            }
        }

        // Auto-set test subdomain on load
        window.addEventListener('load', () => {
            setTestSubdomain();
        });
    </script>
</body>
</html>
