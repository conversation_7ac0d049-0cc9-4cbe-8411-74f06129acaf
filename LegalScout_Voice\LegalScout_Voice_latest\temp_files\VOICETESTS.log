// Test Real Voice Updates
(function() {
  'use strict';

  console.log('🧪 TESTING REAL VOICE UPDATES');
  console.log('='.repeat(40));

  // Test with voices we know work from your assistants
  const workingVoices = [
    { id: 'sarah', provider: '11labs', note: 'Used by Kost Family assistants' },
    { id: 'alloy', provider: 'openai', note: 'Used by multiple assistants' },
    { id: 'echo', provider: 'openai', note: 'Your current voice' }
  ];

  let currentTest = 0;

  function testNextVoice() {
    if (currentTest >= workingVoices.length) {
      console.log('\n🎉 All voice tests completed!');
      return;
    }

    const voice = workingVoices[currentTest];
    console.log(`\n🔄 Test ${currentTest + 1}: Updating to ${voice.id} (${voice.provider})`);
    console.log(`   Note: ${voice.note}`);

    window.FixedVoiceManager.updateVoice(voice.id).then(success => {
      if (success) {
        console.log(`✅ SUCCESS: ${voice.id} voice update worked!`);
        
        // Verify the change propagated
        setTimeout(() => {
          const attorney = window.standaloneAttorneyManager?.attorney;
          const currentVoice = attorney?.voice_id;
          const currentProvider = attorney?.voice_provider;
          
          console.log(`📊 Verification:`);
          console.log(`   - Voice ID in state: ${currentVoice}`);
          console.log(`   - Provider in state: ${currentProvider}`);
          console.log(`   - Update successful: ${currentVoice === voice.id ? '✅' : '❌'}`);
          
          currentTest++;
          setTimeout(testNextVoice, 3000); // Wait 3 seconds between tests
        }, 1500);
      } else {
        console.log(`❌ FAILED: ${voice.id} voice update failed`);
        currentTest++;
        setTimeout(testNextVoice, 2000);
      }
    });
  }

  // Start the test sequence
  console.log('🚀 Starting voice update tests with known working configurations...');
  testNextVoice();

})();
VM3831:5 🧪 TESTING REAL VOICE UPDATES
VM3831:6 ========================================
VM3831:54 🚀 Starting voice update tests with known working configurations...
VM3831:24 
🔄 Test 1: Updating to sarah (11labs)
VM3831:25    Note: Used by Kost Family assistants
VM3823:66 🎤 Updating voice to: sarah
VM3823:80 📋 Using voice config: {provider: '11labs', voiceId: 'sarah'}
VM3823:88 📤 Sending update to Vapi: {voice: {…}}
critical-production-fix.js:48 [CriticalProductionFix] Using PUBLIC key for client operations: client
critical-production-fix.js:49 [CriticalProductionFix] PUBLIC key value: 310f0d43...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:80 [EmergencyApiKeyFix] Using SECRET key for assistant creation/update
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
undefined
VM3823:101 ✅ Voice update successful!
VM3823:102 📋 New voice: {voiceId: 'sarah', provider: '11labs'}
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(45)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:29 ✅ SUCCESS: sarah voice update worked!
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:03.286Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:03.286Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143363}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143363}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143363}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143363}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143378}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143378}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143378}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232143378}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VM3823:148 ✅ Local state updated successfully
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:03.626Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:03.626Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:03.626Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:37 📊 Verification:
VM3831:38    - Voice ID in state: sarah
VM3831:39    - Provider in state: 11labs
VM3831:40    - Update successful: ✅
VM3831:24 
🔄 Test 2: Updating to alloy (openai)
VM3831:25    Note: Used by multiple assistants
VM3823:66 🎤 Updating voice to: alloy
VM3823:80 📋 Using voice config: {provider: 'openai', voiceId: 'alloy'}
VM3823:88 📤 Sending update to Vapi: {voice: {…}}
critical-production-fix.js:48 [CriticalProductionFix] Using PUBLIC key for client operations: client
critical-production-fix.js:49 [CriticalProductionFix] PUBLIC key value: 310f0d43...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:80 [EmergencyApiKeyFix] Using SECRET key for assistant creation/update
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
VM3823:101 ✅ Voice update successful!
VM3823:102 📋 New voice: {voiceId: 'alloy', provider: 'openai'}
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(45)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:29 ✅ SUCCESS: alloy voice update worked!
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:07.942Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:07.942Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148007}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148007}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148007}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148007}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148011}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148011}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148011}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232148011}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VM3823:148 ✅ Local state updated successfully
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:08.205Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:08.205Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:08.205Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:37 📊 Verification:
VM3831:38    - Voice ID in state: alloy
VM3831:39    - Provider in state: openai
VM3831:40    - Update successful: ✅
VM3831:24 
🔄 Test 3: Updating to echo (openai)
VM3831:25    Note: Your current voice
VM3823:66 🎤 Updating voice to: echo
VM3823:80 📋 Using voice config: {provider: 'openai', voiceId: 'echo'}
VM3823:88 📤 Sending update to Vapi: {voice: {…}}
critical-production-fix.js:48 [CriticalProductionFix] Using PUBLIC key for client operations: client
critical-production-fix.js:49 [CriticalProductionFix] PUBLIC key value: 310f0d43...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:80 [EmergencyApiKeyFix] Using SECRET key for assistant creation/update
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
VM3823:101 ✅ Voice update successful!
VM3823:102 📋 New voice: {voiceId: 'echo', provider: 'openai'}
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(45)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:29 ✅ SUCCESS: echo voice update worked!
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:12.719Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T17:49:12.719Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152791}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152791}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152791}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152791}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152799}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152799}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152799}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749232152799}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
VM3823:148 ✅ Local state updated successfully
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:12.973Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:12.973Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:509 [AttorneyProfileManager] Received attorney update from Supabase: {schema: 'public', table: 'attorneys', commit_timestamp: '2025-06-06T17:49:12.973Z', eventType: 'UPDATE', new: {…}, …}
AttorneyProfileManager.js:521 [AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3831:37 📊 Verification:
VM3831:38    - Voice ID in state: echo
VM3831:39    - Provider in state: openai
VM3831:40    - Update successful: ✅
VM3831:19 
🎉 All voice tests completed!
