-- Migration to add subdomain webhook URL sync functionality
-- This ensures that when an attorney's subdomain changes, their Vapi assistant webhook URL is updated

-- Create a function to update Vapi assistant webhook URL when subdomain changes
CREATE OR REPLACE FUNCTION update_assistant_webhook_url()
RETURNS TRIGGER AS $$
DECLARE
  result RECORD;
  webhook_url TEXT;
  base_domain TEXT := 'legalscout.net';
BEGIN
  -- Only proceed if subdomain actually changed and assistant exists
  IF OLD.subdomain IS DISTINCT FROM NEW.subdomain AND NEW.vapi_assistant_id IS NOT NULL THEN
    
    -- Generate the new webhook URL
    IF NEW.subdomain IS NOT NULL AND NEW.subdomain != '' THEN
      webhook_url := 'https://' || NEW.subdomain || '.' || base_domain || '/api/vapi-webhook-direct';
    ELSE
      webhook_url := 'https://' || base_domain || '/api/vapi-webhook-direct';
    END IF;
    
    -- Log the webhook URL update
    RAISE NOTICE 'Updating webhook URL for attorney % (%) from subdomain % to %: %', 
      NEW.firm_name, NEW.id, OLD.subdomain, NEW.subdomain, webhook_url;
    
    -- Call the edge function to update the assistant webhook URL
    SELECT
      content INTO result
    FROM
      http((
        'POST',
        CONCAT(current_setting('app.settings.supabase_functions_endpoint', TRUE), '/functions/v1/update-assistant-webhook'),
        ARRAY[
          http_header('Authorization', CONCAT('Bearer ', current_setting('app.settings.service_role_key', TRUE))),
          http_header('Content-Type', 'application/json')
        ],
        'application/json',
        json_build_object(
          'assistantId', NEW.vapi_assistant_id,
          'webhookUrl', webhook_url,
          'attorneyId', NEW.id,
          'subdomain', NEW.subdomain
        )::text
      )::http_request);
    
    -- Log the result
    RAISE NOTICE 'Webhook URL update result: %', result.content;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger specifically for subdomain changes
CREATE OR REPLACE TRIGGER update_assistant_webhook_url_trigger
AFTER UPDATE OF subdomain ON attorneys
FOR EACH ROW
WHEN (OLD.subdomain IS DISTINCT FROM NEW.subdomain AND NEW.vapi_assistant_id IS NOT NULL)
EXECUTE FUNCTION update_assistant_webhook_url();

-- Create an index on subdomain for faster lookups
CREATE INDEX IF NOT EXISTS attorneys_subdomain_idx ON attorneys (subdomain);

-- Create an index on vapi_assistant_id for faster lookups
CREATE INDEX IF NOT EXISTS attorneys_vapi_assistant_id_idx ON attorneys (vapi_assistant_id);

-- Add a comment explaining the webhook URL pattern
COMMENT ON TRIGGER update_assistant_webhook_url_trigger ON attorneys IS 
'Automatically updates Vapi assistant webhook URL when attorney subdomain changes. 
Webhook URL pattern: https://{subdomain}.legalscout.net/api/vapi-webhook-direct';

-- Add a comment explaining the function
COMMENT ON FUNCTION update_assistant_webhook_url() IS
'Updates Vapi assistant webhook URL when attorney subdomain changes.
Called by update_assistant_webhook_url_trigger.
Generates webhook URL in format: https://{subdomain}.legalscout.net/api/vapi-webhook-direct';

-- Create assistant_ui_configs table for per-assistant UI settings
CREATE TABLE IF NOT EXISTS assistant_ui_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT NOT NULL,

  -- Basic Info
  firm_name TEXT,
  logo_url TEXT,
  mascot_url TEXT,

  -- Colors and Styling
  primary_color TEXT DEFAULT '#2563eb',
  secondary_color TEXT DEFAULT '#1e40af',
  button_color TEXT DEFAULT '#3b82f6',
  background_color TEXT DEFAULT '#ffffff',
  background_opacity DECIMAL(3,2) DEFAULT 1.0,
  button_opacity DECIMAL(3,2) DEFAULT 1.0,
  practice_area_background_opacity DECIMAL(3,2) DEFAULT 0.1,
  text_background_color TEXT DEFAULT '#ffffff',

  -- Content
  practice_description TEXT,
  welcome_message TEXT,
  information_gathering TEXT,
  office_address TEXT,
  scheduling_link TEXT,
  practice_areas TEXT[], -- Array of practice areas

  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,
  voice_provider TEXT DEFAULT '11labs',
  voice_id TEXT DEFAULT 'sarah',
  ai_model TEXT DEFAULT 'gpt-4o',

  -- Custom Fields (JSON for flexibility)
  custom_fields JSONB DEFAULT '{}',

  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one config per attorney-assistant pair
  UNIQUE(attorney_id, assistant_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS assistant_ui_configs_attorney_id_idx ON assistant_ui_configs (attorney_id);
CREATE INDEX IF NOT EXISTS assistant_ui_configs_assistant_id_idx ON assistant_ui_configs (assistant_id);
CREATE INDEX IF NOT EXISTS assistant_ui_configs_attorney_assistant_idx ON assistant_ui_configs (attorney_id, assistant_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_assistant_ui_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_assistant_ui_configs_updated_at_trigger
  BEFORE UPDATE ON assistant_ui_configs
  FOR EACH ROW
  EXECUTE FUNCTION update_assistant_ui_configs_updated_at();

-- Add RLS policies
ALTER TABLE assistant_ui_configs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access configs for their own attorney records
CREATE POLICY "Users can access their own assistant configs" ON assistant_ui_configs
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );

-- Add comments
COMMENT ON TABLE assistant_ui_configs IS 'Stores UI configuration settings per assistant for each attorney';
COMMENT ON COLUMN assistant_ui_configs.attorney_id IS 'Reference to the attorney who owns this configuration';
COMMENT ON COLUMN assistant_ui_configs.assistant_id IS 'Vapi assistant ID this configuration applies to';
COMMENT ON COLUMN assistant_ui_configs.custom_fields IS 'Flexible JSON storage for additional custom fields and settings';
