/**
 * Schema Generator Utilities
 *
 * This file contains utilities for generating structured data schemas
 * from attorney-defined custom fields.
 */

/**
 * Generates a structured data schema from custom fields
 * @param {Array} customFields - Array of custom field definitions
 * @returns {Object} - JSON Schema for structured data extraction
 */
export const generateStructuredDataSchema = (customFields) => {
  // Start with base client information
  const schema = {
    type: "object",
    properties: {
      clientName: {
        type: "string",
        description: "The client's full name"
      },
      contactInfo: {
        type: "object",
        properties: {
          email: {
            type: "string",
            description: "<PERSON><PERSON>'s email address"
          },
          phone: {
            type: "string",
            description: "<PERSON><PERSON>'s phone number"
          }
        },
        description: "Client's contact information"
      },
      legalIssue: {
        type: "string",
        description: "The primary legal issue or concern"
      },
      practiceArea: {
        type: "string",
        description: "The relevant practice area (e.g., Personal Injury, Family Law)"
      }
    },
    required: ["legalIssue", "practiceArea"]
  };

  // Add custom fields to the schema
  if (customFields && Array.isArray(customFields)) {
    customFields.forEach(field => {
      if (!field.name) return; // Skip fields without names

      schema.properties[field.name] = {
        type: field.type || "string",
        description: field.description || field.name
      };

      // Add enum values if applicable
      if (field.type === 'enum' && field.options?.length > 0) {
        schema.properties[field.name].enum = field.options;
      }

      // Add to required fields if marked as required
      if (field.required) {
        if (!schema.required) {
          schema.required = [];
        }
        schema.required.push(field.name);
      }
    });
  }

  return schema;
};

/**
 * Default summary prompt for legal consultations
 */
export const DEFAULT_SUMMARY_PROMPT =
  "Provide a concise summary of this legal consultation, highlighting the client's main concerns, " +
  "legal issues identified, and next steps recommended.";

/**
 * Default structured data prompt for legal consultations
 */
export const DEFAULT_STRUCTURED_DATA_PROMPT =
  "Extract key information from this legal consultation, including all custom fields defined by the attorney.";

/**
 * Practice area templates with predefined custom fields
 */
export const PRACTICE_AREA_TEMPLATES = {
  "General Purpose": {
    summary_prompt: "Summarize this legal consultation, highlighting the client's legal issue, key facts, timeline, and recommended next steps.",
    structured_data_prompt: "Extract key information from this legal consultation, including client details, legal issue, and follow-up requirements.",
    custom_fields: [
      {
        name: "clientName",
        description: "Client's full name",
        type: "string",
        required: true
      },
      {
        name: "contactPhone",
        description: "Client's phone number",
        type: "string",
        required: true
      },
      {
        name: "contactEmail",
        description: "Client's email address",
        type: "string",
        required: false
      },
      {
        name: "legalIssueDescription",
        description: "Brief description of the legal issue",
        type: "string",
        required: true
      },
      {
        name: "urgencyLevel",
        description: "Level of urgency",
        type: "string",
        options: ["Immediate", "Within 1 week", "Within 1 month", "No urgency"],
        required: true
      },
      {
        name: "referralSource",
        description: "How the client found your firm",
        type: "string",
        required: false
      },
      {
        name: "followUpRequested",
        description: "Whether follow-up was requested",
        type: "boolean",
        required: true
      },
      {
        name: "preferredContactMethod",
        description: "Client's preferred contact method",
        type: "string",
        options: ["Phone", "Email", "Text", "No preference"],
        required: false
      }
    ]
  },

  "Personal Injury": {
    summary_prompt: "Summarize this personal injury consultation, highlighting the accident details, injuries sustained, treatment received, and potential liability issues.",
    structured_data_prompt: "Extract key information from this personal injury consultation, focusing on accident details, injuries, and treatment.",
    custom_fields: [
      {
        name: "accidentDate",
        description: "Date when the accident occurred",
        type: "string",
        required: true
      },
      {
        name: "injuryType",
        description: "Type of injury sustained",
        type: "string",
        required: true
      },
      {
        name: "medicalTreatment",
        description: "Medical treatment received so far",
        type: "string",
        required: true
      },
      {
        name: "insuranceInfo",
        description: "Client's insurance information",
        type: "string",
        required: false
      },
      {
        name: "atFault",
        description: "Whether the client was at fault for the accident",
        type: "boolean",
        required: true
      }
    ]
  },
  "Family Law": {
    summary_prompt: "Summarize this family law consultation, highlighting the marital status, children involved, assets, and primary concerns.",
    structured_data_prompt: "Extract key information from this family law consultation, focusing on marital status, children, and primary concerns.",
    custom_fields: [
      {
        name: "maritalStatus",
        description: "Current marital status",
        type: "string",
        options: ["Married", "Separated", "Divorced", "Never Married"],
        required: true
      },
      {
        name: "marriageLength",
        description: "Length of marriage in years",
        type: "number",
        required: false
      },
      {
        name: "childrenCount",
        description: "Number of children involved",
        type: "number",
        required: true
      },
      {
        name: "childrenAges",
        description: "Ages of children involved",
        type: "string",
        required: false
      },
      {
        name: "primaryConcern",
        description: "Primary family law concern",
        type: "string",
        options: ["Divorce", "Child Custody", "Child Support", "Alimony", "Adoption", "Prenuptial Agreement"],
        required: true
      }
    ]
  },
  "Estate Planning": {
    summary_prompt: "Summarize this estate planning consultation, highlighting the client's assets, family situation, and estate planning goals.",
    structured_data_prompt: "Extract key information from this estate planning consultation, focusing on assets, family structure, and planning goals.",
    custom_fields: [
      {
        name: "hasExistingWill",
        description: "Whether the client has an existing will",
        type: "boolean",
        required: true
      },
      {
        name: "hasChildren",
        description: "Whether the client has children",
        type: "boolean",
        required: true
      },
      {
        name: "estateValue",
        description: "Approximate value of the client's estate",
        type: "string",
        required: false
      },
      {
        name: "primaryConcern",
        description: "Primary estate planning concern",
        type: "string",
        options: ["Will Creation", "Trust Establishment", "Asset Protection", "Business Succession", "Tax Planning"],
        required: true
      }
    ]
  },
  "Criminal Defense": {
    summary_prompt: "Summarize this criminal defense consultation, highlighting the charges, circumstances, and potential defense strategies.",
    structured_data_prompt: "Extract key information from this criminal defense consultation, focusing on charges, circumstances, and prior history.",
    custom_fields: [
      {
        name: "chargeType",
        description: "Type of criminal charge",
        type: "string",
        required: true
      },
      {
        name: "arrestDate",
        description: "Date of arrest",
        type: "string",
        required: true
      },
      {
        name: "courtDate",
        description: "Next scheduled court date",
        type: "string",
        required: false
      },
      {
        name: "priorConvictions",
        description: "Whether the client has prior convictions",
        type: "boolean",
        required: true
      },
      {
        name: "bailStatus",
        description: "Current bail status",
        type: "string",
        options: ["Released", "Bail Set", "Detained", "Not Applicable"],
        required: true
      }
    ]
  },
  "Intellectual Property": {
    summary_prompt: "Summarize this intellectual property consultation, highlighting the type of IP, current protection status, potential infringement issues, and recommended next steps.",
    structured_data_prompt: "Extract key information from this intellectual property consultation, focusing on IP type, protection status, and infringement concerns.",
    custom_fields: [
      {
        name: "ipType",
        description: "Type of intellectual property",
        type: "string",
        options: ["Patent", "Trademark", "Copyright", "Trade Secret", "Other"],
        required: true
      },
      {
        name: "currentProtection",
        description: "Current protection status",
        type: "string",
        options: ["None", "Pending Application", "Registered/Granted", "Expired", "Abandoned"],
        required: true
      },
      {
        name: "assetDescription",
        description: "Brief description of the intellectual property asset",
        type: "string",
        required: true
      },
      {
        name: "infringementConcern",
        description: "Whether there is a potential infringement concern",
        type: "boolean",
        required: true
      },
      {
        name: "commercialUse",
        description: "Whether the IP is being used commercially",
        type: "boolean",
        required: true
      },
      {
        name: "jurisdictions",
        description: "Jurisdictions where protection is sought",
        type: "string",
        required: false
      }
    ]
  },
  "Immigration Law": {
    summary_prompt: "Summarize this immigration consultation, highlighting the client's current status, desired immigration outcome, timeline, and any potential complications.",
    structured_data_prompt: "Extract key information from this immigration consultation, focusing on current status, desired outcome, and timeline.",
    custom_fields: [
      {
        name: "currentStatus",
        description: "Current immigration status",
        type: "string",
        options: ["US Citizen", "Permanent Resident", "Visa Holder", "Asylum Seeker", "Undocumented", "Outside US"],
        required: true
      },
      {
        name: "visaType",
        description: "Current visa type (if applicable)",
        type: "string",
        required: false
      },
      {
        name: "countryOfOrigin",
        description: "Country of citizenship",
        type: "string",
        required: true
      },
      {
        name: "desiredOutcome",
        description: "Desired immigration outcome",
        type: "string",
        options: ["Citizenship", "Green Card", "Work Visa", "Student Visa", "Family Visa", "Asylum", "Other"],
        required: true
      },
      {
        name: "familySponsorship",
        description: "Whether family sponsorship is available",
        type: "boolean",
        required: true
      },
      {
        name: "employmentSponsorship",
        description: "Whether employment sponsorship is available",
        type: "boolean",
        required: true
      },
      {
        name: "urgencyLevel",
        description: "Level of urgency",
        type: "string",
        options: ["Immediate", "Within 3 months", "Within 6 months", "Within 1 year", "No urgency"],
        required: true
      }
    ]
  },
  "Employment Law": {
    summary_prompt: "Summarize this employment law consultation, highlighting the employment issue, relevant timeline, employer details, and potential claims.",
    structured_data_prompt: "Extract key information from this employment law consultation, focusing on the issue type, timeline, and employer details.",
    custom_fields: [
      {
        name: "employmentIssue",
        description: "Type of employment issue",
        type: "string",
        options: ["Wrongful Termination", "Discrimination", "Harassment", "Wage Dispute", "Contract Dispute", "Workplace Safety", "Other"],
        required: true
      },
      {
        name: "employmentStatus",
        description: "Current employment status",
        type: "string",
        options: ["Currently Employed", "Terminated", "Resigned", "On Leave"],
        required: true
      },
      {
        name: "employerSize",
        description: "Size of employer (number of employees)",
        type: "string",
        options: ["1-14", "15-49", "50-99", "100-499", "500+"],
        required: true
      },
      {
        name: "incidentDate",
        description: "Date of the incident or issue",
        type: "string",
        required: true
      },
      {
        name: "reportedToEmployer",
        description: "Whether the issue was reported to employer",
        type: "boolean",
        required: true
      },
      {
        name: "employmentLength",
        description: "Length of employment in years",
        type: "number",
        required: true
      },
      {
        name: "documentsAvailable",
        description: "Whether supporting documents are available",
        type: "boolean",
        required: true
      }
    ]
  },
  "Real Estate Law": {
    summary_prompt: "Summarize this real estate consultation, highlighting the property details, transaction type, current status, and legal concerns.",
    structured_data_prompt: "Extract key information from this real estate consultation, focusing on property details, transaction type, and legal concerns.",
    custom_fields: [
      {
        name: "transactionType",
        description: "Type of real estate transaction",
        type: "string",
        options: ["Purchase", "Sale", "Lease", "Foreclosure", "Boundary Dispute", "Zoning Issue", "Other"],
        required: true
      },
      {
        name: "propertyType",
        description: "Type of property",
        type: "string",
        options: ["Residential", "Commercial", "Industrial", "Land", "Mixed-Use"],
        required: true
      },
      {
        name: "propertyAddress",
        description: "Property address",
        type: "string",
        required: true
      },
      {
        name: "transactionStage",
        description: "Current stage of transaction",
        type: "string",
        options: ["Pre-Contract", "Under Contract", "Closing", "Post-Closing", "Dispute"],
        required: true
      },
      {
        name: "hasRealtor",
        description: "Whether client is working with a realtor",
        type: "boolean",
        required: true
      },
      {
        name: "contractSigned",
        description: "Whether a contract has been signed",
        type: "boolean",
        required: true
      },
      {
        name: "closingDate",
        description: "Expected or actual closing date",
        type: "string",
        required: false
      }
    ]
  },
  "Business Law": {
    summary_prompt: "Summarize this business law consultation, highlighting the business structure, legal issue, timeline, and recommended next steps.",
    structured_data_prompt: "Extract key information from this business law consultation, focusing on business structure, legal issue, and timeline.",
    custom_fields: [
      {
        name: "businessType",
        description: "Type of business entity",
        type: "string",
        options: ["Sole Proprietorship", "Partnership", "LLC", "Corporation", "Non-Profit", "Not Yet Formed"],
        required: true
      },
      {
        name: "businessStage",
        description: "Stage of business",
        type: "string",
        options: ["Pre-Formation", "Startup", "Established", "Expanding", "Selling/Closing"],
        required: true
      },
      {
        name: "legalIssueType",
        description: "Type of legal issue",
        type: "string",
        options: ["Formation", "Contracts", "Employment", "Intellectual Property", "Regulatory Compliance", "Dispute/Litigation", "Merger/Acquisition"],
        required: true
      },
      {
        name: "employeeCount",
        description: "Number of employees",
        type: "string",
        options: ["0", "1-5", "6-20", "21-50", "51-100", "100+"],
        required: true
      },
      {
        name: "annualRevenue",
        description: "Annual revenue range",
        type: "string",
        options: ["Pre-Revenue", "Under $100K", "$100K-$500K", "$500K-$1M", "$1M-$5M", "$5M+"],
        required: false
      },
      {
        name: "hasExistingCounsel",
        description: "Whether the business has existing legal counsel",
        type: "boolean",
        required: true
      },
      {
        name: "urgencyLevel",
        description: "Level of urgency",
        type: "string",
        options: ["Immediate", "Within 1 week", "Within 1 month", "No urgency"],
        required: true
      }
    ]
  }
};

/**
 * Gets a template for a specific practice area or returns a default template
 * @param {string} practiceArea - The practice area
 * @returns {Object} - Template with custom fields and prompts
 */
export const getPracticeAreaTemplate = (practiceArea) => {
  return PRACTICE_AREA_TEMPLATES[practiceArea] || {
    summary_prompt: DEFAULT_SUMMARY_PROMPT,
    structured_data_prompt: DEFAULT_STRUCTURED_DATA_PROMPT,
    custom_fields: []
  };
};

// Make the generateStructuredDataSchema function available globally
if (typeof window !== 'undefined') {
  window.generateStructuredDataSchema = generateStructuredDataSchema;
}
