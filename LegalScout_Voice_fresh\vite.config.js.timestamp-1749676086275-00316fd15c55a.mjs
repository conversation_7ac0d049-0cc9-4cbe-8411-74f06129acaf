// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Scout_Finalize/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Scout_Finalize/node_modules/@vitejs/plugin-react/dist/index.mjs";

// vite-plugin-exclude-framer-motion.js
function excludeFramerMotion() {
  const mockFramerMotionCode = `
    console.log('[ExcludeFramerMotion] Loading mock Framer Motion implementation');

    // Create empty mock components and functions
    const noop = () => {};
    const Component = (props) => props.children || null;

    // Create a mock context
    const createContext = (defaultValue) => ({
      Provider: Component,
      Consumer: Component,
      displayName: 'MockContext',
      _currentValue: defaultValue,
      _currentValue2: defaultValue,
      _threadCount: 0,
      _defaultValue: defaultValue
    });

    // Create mock contexts
    export const LayoutGroupContext = createContext({});
    export const MotionConfigContext = createContext({
      transformPagePoint: undefined,
      isStatic: false,
      reducedMotion: "never"
    });

    // Make contexts available globally
    if (typeof window !== 'undefined') {
      window.LayoutGroupContext = LayoutGroupContext;
      window.MotionConfigContext = MotionConfigContext;
      window.React = window.React || {};
      window.React.createContext = createContext;
    }

    // Export common components and functions
    export const motion = new Proxy({}, {
      get: (target, prop) => Component
    });

    export const AnimatePresence = Component;
    export const MotionConfig = Component;
    export const LazyMotion = Component;
    export const m = motion;

    // Export common hooks and utilities
    export const useAnimation = () => ({ start: noop, stop: noop });
    export const useMotionValue = () => ({ get: () => 0, set: noop });
    export const useTransform = () => 0;
    export const useSpring = () => ({ get: () => 0, set: noop });
    export const useCycle = () => [0, noop];
    export const useMotionTemplate = () => '';
    export const useInView = () => ({ inView: false, ref: {} });
    export const useScroll = () => ({
      scrollY: { get: () => 0, onChange: noop },
      scrollYProgress: { get: () => 0, onChange: noop }
    });

    // Export common variants
    export const Variants = {};

    // Default export
    export default {
      motion,
      AnimatePresence,
      MotionConfig,
      LazyMotion,
      m,
      useAnimation,
      useMotionValue,
      useTransform,
      useSpring,
      useCycle,
      useMotionTemplate,
      useInView,
      useScroll,
      Variants,
      MotionConfigContext,
      LayoutGroupContext
    };
  `;
  return {
    name: "exclude-framer-motion",
    resolveId(id) {
      if (id === "framer-motion" || id.startsWith("framer-motion/") || id.includes("LayoutGroupContext") || id.includes("MotionConfigContext") || id.includes("framer-motion")) {
        console.log(`[ExcludeFramerMotion] Excluding import: ${id}`);
        return `\0virtual:framer-motion-mock`;
      }
      return null;
    },
    load(id) {
      if (id === `\0virtual:framer-motion-mock`) {
        console.log("[ExcludeFramerMotion] Providing mock implementation");
        return mockFramerMotionCode;
      }
      return null;
    },
    transform(code, id) {
      if (code.includes("framer-motion") || code.includes("LayoutGroupContext") || code.includes("MotionConfigContext")) {
        console.log(`[ExcludeFramerMotion] Transforming code in: ${id}`);
        code = code.replace(
          /import\s*\(\s*['"](.+?framer-motion.+?)['"]\s*\)/g,
          `Promise.resolve({})`
        );
        code = code.replace(
          /import\s+.*?from\s+['"](.+?framer-motion.+?)['"]\s*;?/g,
          "// Removed framer-motion import"
        );
        return code;
      }
      return null;
    }
  };
}

// vite.config.js
import path from "path";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Scout_Finalize";
var vite_config_default = defineConfig(({ mode }) => ({
  define: {
    // Define global variables for the build
    "process.env.VITE_DISABLE_FRAMER_MOTION": JSON.stringify(process.env.VITE_DISABLE_FRAMER_MOTION || "false"),
    "process.env.NODE_ENV": JSON.stringify(mode),
    // Supabase environment variables
    "import.meta.env.VITE_SUPABASE_URL": JSON.stringify(process.env.VITE_SUPABASE_URL || "https://utopqxsvudgrtiwenlzl.supabase.co"),
    "import.meta.env.VITE_SUPABASE_KEY": JSON.stringify(process.env.VITE_SUPABASE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"),
    "import.meta.env.VITE_SUPABASE_ANON_KEY": JSON.stringify(process.env.VITE_SUPABASE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"),
    // Vapi environment variables
    "import.meta.env.VITE_VAPI_PUBLIC_KEY": JSON.stringify(process.env.VITE_VAPI_PUBLIC_KEY || "310f0d43-27c2-47a5-a76d-e55171d024f7"),
    "import.meta.env.VITE_VAPI_SECRET_KEY": JSON.stringify(process.env.VITE_VAPI_SECRET_KEY || "6734febc-fc65-4669-93b0-929b31ff6564"),
    // React App environment variables for compatibility
    "import.meta.env.REACT_APP_SUPABASE_URL": JSON.stringify(process.env.REACT_APP_SUPABASE_URL || "https://utopqxsvudgrtiwenlzl.supabase.co"),
    "import.meta.env.REACT_APP_SUPABASE_KEY": JSON.stringify(process.env.REACT_APP_SUPABASE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"),
    "import.meta.env.REACT_APP_SUPABASE_ANON_KEY": JSON.stringify(process.env.REACT_APP_SUPABASE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU")
  },
  plugins: [react(), excludeFramerMotion()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes("node_modules")) {
            if (id.includes("framer-motion")) {
              return null;
            }
            if (id.includes("react"))
              return "vendor-react";
            if (id.includes("three"))
              return "vendor-three";
            if (id.includes("react-icons"))
              return "vendor-icons";
            return "vendor";
          }
          if (id.includes("/pages/")) {
            return "pages";
          }
        },
        assetFileNames: (assetInfo) => {
          let extType = assetInfo.name.split(".").at(1);
          if (/png|jpe?g|svg|gif|tiff|bmp|ico|webp/i.test(extType)) {
            extType = "img";
          }
          return `assets/${extType}/[name]-[hash][extname]`;
        }
      }
    },
    chunkSizeWarningLimit: 500,
    target: "es2020",
    outDir: "dist",
    assetsDir: "assets",
    emptyOutDir: true,
    sourcemap: true,
    copyPublicDir: true
  },
  optimizeDeps: {
    include: ["react", "react-dom", "react-router-dom"],
    exclude: ["framer-motion", "framer-motion/*", "three", "three/*"]
  },
  resolve: {
    extensions: [".js", ".jsx", ".ts", ".tsx", ".json"],
    alias: {
      // Add explicit alias for problematic Framer Motion modules
      "framer-motion": path.resolve(__vite_injected_original_dirname, "src/mocks/framer-motion.js"),
      "framer-motion/dist/es/context/LayoutGroupContext.mjs": path.resolve(__vite_injected_original_dirname, "src/mocks/LayoutGroupContext.js"),
      "framer-motion/dist/es/context/MotionConfigContext.mjs": path.resolve(__vite_injected_original_dirname, "src/mocks/MotionConfigContext.js")
    }
  },
  publicDir: "public",
  server: {
    port: 5173,
    strictPort: false,
    host: true,
    hmr: {
      timeout: 5e3
    },
    proxy: {
      // Proxy Vapi MCP requests to bypass CORS during development
      "/vapi-mcp/sse": {
        target: "https://mcp.vapi.ai/sse",
        changeOrigin: true,
        secure: true,
        rewrite: (path2) => path2.replace(/^\/vapi-mcp\/sse/, ""),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("Vapi proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Vapi Request to the Target:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Vapi Response from the Target:", proxyRes.statusCode, req.url);
          });
        }
      },
      // Proxy for the Vapi MCP server
      "/vapi-mcp-server/sse": {
        target: "https://mcp.vapi.ai",
        changeOrigin: true,
        secure: true,
        rewrite: (path2) => path2.replace(/^\/vapi-mcp-server\/sse/, "/sse"),
        headers: {
          "Authorization": `Bearer ${process.env.VITE_VAPI_PUBLIC_KEY || "310f0d43-27c2-47a5-a76d-e55171d024f7"}`
        },
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("Vapi MCP server proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Vapi MCP server request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Vapi MCP server response:", proxyRes.statusCode, req.url);
          });
        }
      },
      // Proxy for Vapi API requests
      "/api/vapi-proxy": {
        target: "https://api.vapi.ai",
        changeOrigin: true,
        secure: true,
        rewrite: (path2) => path2.replace(/^\/api\/vapi-proxy/, ""),
        headers: {
          "Authorization": `Bearer ${process.env.VITE_VAPI_SECRET_KEY || "6734febc-fc65-4669-93b0-929b31ff6564"}`,
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("Vapi proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Vapi proxy request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Vapi proxy response:", proxyRes.statusCode, req.url);
          });
        }
      },
      // Proxy for Slack webhook to bypass CORS in development
      "/api/bug-report": {
        target: "*******************************************************************************",
        changeOrigin: true,
        secure: true,
        rewrite: (path2) => path2.replace(/^\/api\/bug-report/, ""),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("Slack webhook proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Slack webhook request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log("Received Slack webhook response:", proxyRes.statusCode, req.url);
          });
        }
      },
      // Add other proxies if needed
      // Proxy for Supabase API requests
      "/supabase-proxy": {
        target: "https://utopqxsvudgrtiwenlzl.supabase.co",
        changeOrigin: true,
        secure: true,
        rewrite: (path2) => path2.replace(/^\/supabase-proxy/, ""),
        headers: {
          "apikey": process.env.VITE_SUPABASE_KEY || ""
        },
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("Supabase proxy error", err);
          });
        }
      },
      // Health endpoint for Vapi Web SDK
      "/api/health": {
        target: "http://localhost:5173",
        changeOrigin: true,
        secure: false,
        rewrite: (path2) => "/api/health.json",
        configure: (proxy, _options) => {
          proxy.on("error", (err, req, res) => {
            console.log("Health endpoint error:", err.message);
            res.writeHead(200, {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type, Authorization"
            });
            res.end(JSON.stringify({
              status: "healthy",
              timestamp: (/* @__PURE__ */ new Date()).toISOString(),
              service: "LegalScout Voice API (Development)",
              version: "1.0.0",
              environment: "development"
            }));
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Health check request:", req.method, req.url);
          });
        }
      },
      // Proxy API requests to development server
      "/api": {
        target: "http://localhost:3001",
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on("error", (err, req, res) => {
            console.log("API proxy error:", err.message);
            console.log("Make sure dev server is running: npm run dev:api");
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Proxying API request:", req.method, req.url);
          });
        }
      },
      // Proxy for mock services to handle CORS
      "/mock-*": {
        target: "http://localhost:5173",
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on("proxyReq", (proxyReq, req, res) => {
            if (req.method === "OPTIONS") {
              res.writeHead(200, {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept",
                "Access-Control-Max-Age": "86400"
              });
              res.end();
              return;
            }
          });
        }
      }
    }
  },
  base: "/"
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
