import React, { useState, useRef, useCallback, useEffect } from 'react';
// import { getApifyService } from '../services/apifyService';
import { getMockAttorneyService } from '../services/mockAttorneyService';
import GlobeDossierView from './GlobeDossierView';
import Pagination from './Pagination';
import ActiveCallDetail from './ActiveCallDetail';
import './CallController.css';
import StickThrowButton from './StickThrowButton';

// Mock attorney data for development
const mockAttorneys = [
  {
    id: 1,
    name: "<PERSON>",
    firm: "Smith & Associates",
    rating: 4.8,
    practiceAreas: ["Family Law", "Divorce"],
    location: { lat: 40.7282, lng: -73.9942, address: "123 Legal Ave, New York, NY" }
  },
  {
    id: 2,
    name: "<PERSON>",
    firm: "Johnson Legal Group",
    rating: 4.5,
    practiceAreas: ["Family Law", "Child Custody"],
    location: { lat: 40.7112, lng: -74.0123, address: "456 Court St, New York, NY" }
  },
  {
    id: 3,
    name: "<PERSON>",
    firm: "Williams Law Firm",
    rating: 4.9,
    practiceAreas: ["Family Law", "Domestic Violence"],
    location: { lat: 40.7212, lng: -73.9982, address: "789 Justice Blvd, New York, NY" }
  }
]

const CallController = ({ status, dossierData, onEndCall, volumeLevel, assistantIsSpeaking, vapiClient }) => {
  // State for attorney search
  const [isSearchingAttorneys, setIsSearchingAttorneys] = useState(false);
  const [attorneySearchError, setAttorneySearchError] = useState(null);
  const [attorneyMatches, setAttorneyMatches] = useState([]);
  const [showAttorneyResults, setShowAttorneyResults] = useState(false);
  const [showMapView, setShowMapView] = useState(true);
  const [caseData, setCaseData] = useState({});
  const [webhookData, setWebhookData] = useState({
    message: "Hello! I'm Scout, your legal assistant. Tell me about your situation and I'll help find the right attorney for you."
  });
  
  // State for text input
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef(null);
  const [messages, setMessages] = useState([
    { 
      type: 'assistant', 
      text: "Hello! I'm Scout, your legal assistant. Tell me about your situation and I'll help find the right attorney for you." 
    }
  ]);
  
  // Add pagination state
  const [paginationInfo, setPaginationInfo] = useState({
    currentPage: 1,
    totalPages: 1,
    totalResults: 0,
    limit: 5,
  });
  
  // Add ref to all attorney results (for client-side pagination)
  const allAttorneyResultsRef = useRef([]);
  const mapRef = useRef(null);
  
  // Update case data when dossierData changes
  useEffect(() => {
    if (dossierData && Object.keys(dossierData).length > 0) {
      console.log("Updating case data with:", dossierData);
      setCaseData(prevData => {
        const newData = {
          ...prevData,
          ...dossierData
        };
        
        // Show map view if we have location data
        if (dossierData.location && !showMapView) {
          setShowMapView(true);
        }
        
        return newData;
      });
    }
  }, [dossierData]);
  
  // Scroll to bottom of messages when they change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Function to search for attorneys
  const searchForAttorneys = useCallback(async (location, practiceArea) => {
    if (!location || !practiceArea) {
      console.log("Cannot search for attorneys without location and practice area");
      return;
    }
    
    try {
      setIsSearchingAttorneys(true);
      setAttorneySearchError(null);
      
      // Use mock service for development
      const attorneyService = getMockAttorneyService();
      
      console.log("Searching for attorneys with:", {
        location,
        practiceArea
      });
      
      const results = await attorneyService.findAttorneys({
        location,
        practiceArea,
        limit: 10
      });
      
      console.log("Attorney search results:", results);
      
      // Store all results in the ref for pagination
      allAttorneyResultsRef.current = results;
      
      // Update pagination info
      setPaginationInfo({
        currentPage: 1,
        totalPages: Math.ceil(results.length / paginationInfo.limit),
        totalResults: results.length,
        limit: paginationInfo.limit
      });
      
      // Get the first page of results
      const firstPageResults = results.slice(0, paginationInfo.limit);
      setAttorneyMatches(firstPageResults);
      
      // Show the results panel
      setShowAttorneyResults(true);
      
    } catch (error) {
      console.error("Error searching for attorneys:", error);
      setAttorneySearchError(error.message || "Failed to search for attorneys");
    } finally {
      setIsSearchingAttorneys(false);
    }
  }, [paginationInfo.limit]);
  
  const handleFindAttorney = () => {
    // Extract the needed data from the case data
    const location = caseData.location;
    const practiceArea = caseData.practiceArea || 'Family Law'; // Default to Family Law if none specified
    
    if (location) {
      searchForAttorneys(location, practiceArea);
    }
  };
  
  const handlePageChange = (newPage) => {
    console.log("Changing to page:", newPage);
    setPaginationInfo(prev => ({
      ...prev,
      currentPage: newPage
    }));
    
    // Calculate the slice of results for this page
    const startIdx = (newPage - 1) * paginationInfo.limit;
    const endIdx = startIdx + paginationInfo.limit;
    
    // Update the visible attorney matches
    setAttorneyMatches(allAttorneyResultsRef.current.slice(startIdx, endIdx));
  };
  
  const toggleMapView = () => {
    setShowMapView(!showMapView);
  };
  
  // Callback to handle when a user sends a message
  const handleSendMessage = (text) => {
    // Add the user message to the messages list
    setMessages(prev => [...prev, { type: 'user', text }]);
    
    // Clear the input field
    setMessageText('');
    
    // Simulate assistant response (in a real app, this would come from the assistant)
    setTimeout(() => {
      setMessages(prev => [...prev, { 
        type: 'assistant', 
        text: "I'm processing your information. This is just a simulated response for demonstration purposes." 
      }]);
    }, 1000);
  };
  
  // Render the dossier information panel
  const renderDossierInfo = () => {
    return (
      <div className="dossier-component-container">
        <h2>Case Information</h2>
        <div className="dossier-info">
          {Object.keys(caseData).length === 0 ? (
            <p className="empty-message">No case information collected yet.</p>
          ) : (
            <div className="dossier-items">
              {caseData.clientName && (
                <div className="dossier-item">
                  <span className="dossier-label">Client:</span>
                  <span className="dossier-value">{caseData.clientName}</span>
                </div>
              )}
              {caseData.jurisdiction && (
                <div className="dossier-item">
                  <span className="dossier-label">Jurisdiction:</span>
                  <span className="dossier-value">{caseData.jurisdiction}</span>
                </div>
              )}
              {caseData.clientBackground && (
                <div className="dossier-item">
                  <span className="dossier-label">Background:</span>
                  <span className="dossier-value">{caseData.clientBackground}</span>
                </div>
              )}
              {caseData.legalIssue && (
                <div className="dossier-item">
                  <span className="dossier-label">Legal Issue:</span>
                  <span className="dossier-value">{caseData.legalIssue}</span>
                </div>
              )}
              {caseData.statementOfFacts && (
                <div className="dossier-item">
                  <span className="dossier-label">Statement of Facts:</span>
                  <span className="dossier-value">{caseData.statementOfFacts}</span>
                </div>
              )}
              {caseData.objectives && (
                <div className="dossier-item">
                  <span className="dossier-label">Objectives:</span>
                  <span className="dossier-value">{caseData.objectives}</span>
                </div>
              )}
              {caseData.location && (
                <div className="dossier-item">
                  <span className="dossier-label">Location:</span>
                  <span className="dossier-value">
                    {caseData.location.address || 
                     `${caseData.location.lat?.toFixed(4)}, ${caseData.location.lng?.toFixed(4)}`}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="webhook-data">
          <h3>Assistant State</h3>
          {webhookData.message && <p>{webhookData.message}</p>}
        </div>
        
        <div className="action-buttons">
          <button 
            className="toggle-map-button" 
            onClick={toggleMapView}
          >
            {showMapView ? 'Hide Map' : 'Show Map'}
          </button>
          
          <button 
            className="find-attorney-button"
            onClick={handleFindAttorney}
            disabled={!caseData.location}
          >
            Find Attorney
          </button>
        </div>
      </div>
    );
  };
  
  // Render the end call button
  const renderEndCallButton = () => {
    return (
      <div className="end-call-button-container">
        <button className="vapi-end-call-button" onClick={onEndCall}>
          <img src="/detective.svg" alt="End Call" className="call-detective-icon" />
          End Call
        </button>
      </div>
    );
  };
  
  // Render the messages and text input
  const renderMessagesAndInput = () => {
    return (
      <div className="conversation-container">
        <div className="conversation-messages">
          {messages.map((msg, i) => (
            <div key={i} className={`message ${msg.type}`}>
              {msg.text}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
        
        <div className="text-entry-container">
          <div className="text-input-container">
            <input
              type="text"
              className="text-input"
              placeholder="Type your message..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && messageText.trim()) {
                  handleSendMessage(messageText);
                }
              }}
            />
            <StickThrowButton
              onClick={() => {
                if (messageText.trim()) {
                  handleSendMessage(messageText);
                }
              }}
              disabled={!messageText.trim()}
            />
          </div>
        </div>
      </div>
    );
  };
  
  // Render attorney results
  const renderAttorneyResults = () => {
    if (!showAttorneyResults) return null;
    
    return (
      <div className="attorney-results">
        <h3>Attorney Matches</h3>
        
        {isSearchingAttorneys ? (
          <div className="loading-overlay">
            <div className="loading-spinner"></div>
            <p>Searching for qualified attorneys...</p>
          </div>
        ) : attorneySearchError ? (
          <div className="error-message">
            <p>{attorneySearchError}</p>
          </div>
        ) : attorneyMatches.length > 0 ? (
          <>
            <div className="attorney-list">
              {attorneyMatches.map(attorney => (
                <div key={attorney.id} className="attorney-card">
                  <h4>{attorney.name}</h4>
                  <p className="firm">{attorney.firm}</p>
                  <p className="specialty">{attorney.practiceAreas?.join(', ')}</p>
                  <p className="address">{attorney.location?.address}</p>
                  <p className="distance">{attorney.distance} miles away</p>
                  <p className="rating">Rating: {attorney.rating}/5</p>
                  <button 
                    className="contact-button"
                    onClick={() => handleSelectAttorney(attorney)}
                  >
                    Contact Now
                  </button>
                </div>
              ))}
            </div>
            
            {paginationInfo.totalPages > 1 && (
              <Pagination 
                currentPage={paginationInfo.currentPage}
                totalPages={paginationInfo.totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </>
        ) : (
          <p>No attorneys matching your criteria were found.</p>
        )}
      </div>
    );
  };
  
  // Handle attorney selection
  const handleSelectAttorney = (attorney) => {
    console.log("Selected attorney:", attorney);
    
    // TODO: Implement contact/selection logic
    
    // For now, just add a message about selecting this attorney
    setMessages(prev => [...prev, { 
      type: 'assistant', 
      text: `You've selected ${attorney.name} from ${attorney.firm}. In a real implementation, we would connect you with this attorney.` 
    }]);
    
    // Hide attorney results
    setShowAttorneyResults(false);
  };
  
  return (
    <div className="call-controller-wrapper">
      {/* The GlobeDossierView component is now in the App.jsx file */}
      
      {/* Dossier information panel - left side */}
      {renderDossierInfo()}
      
      {/* End Call button - top right */}
      {renderEndCallButton()}
      
      {/* Messages and text input - bottom */}
      {renderMessagesAndInput()}
      
      {/* Attorney results - will appear based on state */}
      {renderAttorneyResults()}
      
      {/* Active call indicator */}
      <div className="call-status-indicator">
        <div className={`status-dot ${assistantIsSpeaking ? 'connected pulse' : 'connected'}`}></div>
        <span className="status-text">
          {assistantIsSpeaking ? 'Assistant is speaking...' : 'Call active'}
        </span>
      </div>
    </div>
  );
};

export default CallController;