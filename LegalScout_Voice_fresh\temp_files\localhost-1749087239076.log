callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:21 ✅ Vapi public key set globally
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:42 ✅ Supabase keys set globally - should load correct assistant by domain
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:53 🚀 [EMERGENCY] Starting emergency critical fixes...
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:57 🔧 [EMERGENCY] Adding process polyfill
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:64 ✅ [EMERGENCY] Process polyfill added
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:75 🔧 [EMERGENCY] Development mode: false (forced production)
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:105 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:108 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
callback:130 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:221 Supabase loaded from CDN
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:231 Creating Supabase client from CDN
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:235 Supabase client created from CDN
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_OUT
clean-auth-solution.js:131 🧹 [CleanAuthSolution] User signed out, clearing local state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749083508362:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749083508362:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749083508362:46 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749083508362:60 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749083508362:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749083508362:99 Supabase client initialized successfully with proper headers
supabase.js?t=1749083508362:102 Testing Supabase connection...
supabase.js?t=1749083508362:137 Supabase client ready for use
supabase.js?t=1749083508362:145 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749086315592:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749086315592:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
initAttorneyProfileManager.js?t=1749086315592:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749086315592:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749086315592:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749086315592:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749086315592:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749086315592:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749086315592:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749086315592:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749086315592:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js?t=1749083564131:21 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749083564131:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749086315592:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749086315592:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749086315592:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749086315592:128 [initAttorneyProfileManager] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js?t=1749083508362:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749083508362:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749083508362:64 Testing Supabase connection...
testSupabase.js?t=1749083508362:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749083508362:56 Supabase URL configured: true
testSupabase.js?t=1749083508362:57 Supabase Key configured: true
testSupabase.js?t=1749083508362:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749083508362:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749083508362:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js?t=1749083508362:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749083508362:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749083508362:64 Testing Supabase connection...
testSupabase.js?t=1749083508362:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749083508362:56 Supabase URL configured: true
testSupabase.js?t=1749083508362:57 Supabase Key configured: true
testSupabase.js?t=1749083508362:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749083508362:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749083508362:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749083508362:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
index.ts:5 Loaded contentScript
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:96 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
callback:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
_exchangeCodeForSession @ @supabase_supabase-js.js?v=fb518282:5243
await in _exchangeCodeForSession
_getSessionFromURL @ @supabase_supabase-js.js?v=fb518282:5868
_initialize @ @supabase_supabase-js.js?v=fb518282:4999
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
_exchangeCodeForSession @ @supabase_supabase-js.js?v=fb518282:5243
await in _exchangeCodeForSession
_getSessionFromURL @ @supabase_supabase-js.js?v=fb518282:5868
_initialize @ @supabase_supabase-js.js?v=fb518282:4999
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=fb518282:6263:11
    at async Promise.all (:5175/auth/index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=fb518282:6268:7)
    at async SupabaseAuthClient._exchangeCodeForSession (@supabase_supabase-js.js?v=fb518282:5243:9)
    at async SupabaseAuthClient._getSessionFromURL (@supabase_supabase-js.js?v=fb518282:5868:48)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=fb518282:4999:33)
    at async @supabase_supabase-js.js?v=fb518282:4977:16
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
_exchangeCodeForSession @ @supabase_supabase-js.js?v=fb518282:5243
await in _exchangeCodeForSession
_getSessionFromURL @ @supabase_supabase-js.js?v=fb518282:5868
_initialize @ @supabase_supabase-js.js?v=fb518282:4999
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:96 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=fb518282:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=fb518282:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=fb518282:6263:11
    at async Promise.all (:5175/auth/index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=fb518282:6268:7)
    at async @supabase_supabase-js.js?v=fb518282:5018:13
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=fb518282:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=fb518282:6261
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=fb518282:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4977
(anonymous) @ @supabase_supabase-js.js?v=fb518282:5542
(anonymous) @ @supabase_supabase-js.js?v=fb518282:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:96 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
supabase.js?t=1749083508362:116 Supabase connection test successful!
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:96 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
AuthContext.jsx:182 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
supabaseConfigVerifier.js?t=1749083508362:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749083508362:143 ✅ Supabase configuration verified and working
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
supabaseConfigVerifier.js?t=1749083508362:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749083508362:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749083508362:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749083508362:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.bafd37ba-a143-40ea-bcf5-e25fe149b55e 406 (Not Acceptable)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:51 Attorney not found by user_id, trying email lookup
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.bafd37ba-a143-40ea-bcf5-e25fe149b55e 406 (Not Acceptable)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:51 Attorney not found by user_id, trying email lookup
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonandlaurakost%40gmail.com 406 (Not Acceptable)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:101 ❌ No attorney profile found, redirecting to complete profile
CompleteProfile.jsx:19 Complete Profile - environment mode: development
CompleteProfile.jsx:23 Development mode: Creating mock user for profile completion
CompleteProfile.jsx:19 Complete Profile - environment mode: development
CompleteProfile.jsx:23 Development mode: Creating mock user for profile completion
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonandlaurakost%40gmail.com 406 (Not Acceptable)
window.fetch @ callback?code=9b55857b-0ab5-4a1d-a60a-57efbed78401:103
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:101 ❌ No attorney profile found, redirecting to complete profile
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}fallback: falsehasAttorney: truemessage: "Client-side fallback: Session refreshed successfully"success: true[[Prototype]]: Object
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
