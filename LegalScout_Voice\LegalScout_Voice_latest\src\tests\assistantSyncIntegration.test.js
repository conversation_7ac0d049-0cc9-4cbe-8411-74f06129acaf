/**
 * Comprehensive Assistant Synchronization Integration Tests
 * 
 * Tests the complete data synchronization system across all dashboard components
 * to ensure consistent assistant-level data flow and eliminate data inconsistencies.
 */

import { assistantSyncManager } from '../services/assistantSyncManager';
import { AssistantDataService } from '../services/assistantDataService';
import { useAssistantSync } from '../hooks/useAssistantSync';

// Mock dependencies
jest.mock('../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null }))
        }))
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => Promise.resolve({ error: null }))
      })),
      upsert: jest.fn(() => Promise.resolve({ error: null })),
      channel: jest.fn(() => ({
        on: jest.fn(() => ({
          on: jest.fn(() => ({
            on: jest.fn(() => ({
              subscribe: jest.fn()
            }))
          }))
        }))
      }))
    }))
  }
}));

jest.mock('../services/vapiMcpService', () => ({
  vapiMcpService: {
    updateAssistant: jest.fn(() => Promise.resolve()),
    getAssistant: jest.fn(() => Promise.resolve({}))
  }
}));

describe('Assistant Synchronization Integration Tests', () => {
  const mockAttorneyId = 'attorney-123';
  const mockAssistantId = 'assistant-456';
  const mockAssistantData = {
    id: mockAssistantId,
    name: 'Test Assistant',
    instructions: 'Test instructions',
    voice: { voiceId: 'echo' },
    model: { model: 'gpt-4' }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset sync manager state
    assistantSyncManager.currentAssistantId = null;
    assistantSyncManager.currentAttorneyId = null;
    assistantSyncManager.syncInProgress = false;
  });

  describe('AssistantSyncManager', () => {
    test('should initialize with correct default state', () => {
      const status = assistantSyncManager.getSyncStatus();
      
      expect(status.currentAssistantId).toBeNull();
      expect(status.currentAttorneyId).toBeNull();
      expect(status.syncInProgress).toBe(false);
      expect(status.subscriberCount).toBe(0);
    });

    test('should set current assistant context correctly', () => {
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      const status = assistantSyncManager.getSyncStatus();
      expect(status.currentAssistantId).toBe(mockAssistantId);
      expect(status.currentAttorneyId).toBe(mockAttorneyId);
    });

    test('should notify subscribers when assistant changes', () => {
      const mockCallback = jest.fn();
      const unsubscribe = assistantSyncManager.subscribe(mockCallback);
      
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      expect(mockCallback).toHaveBeenCalledWith('assistant_changed', {
        previousAssistantId: null,
        currentAssistantId: mockAssistantId,
        attorneyId: mockAttorneyId,
        timestamp: expect.any(String)
      });
      
      unsubscribe();
    });

    test('should handle subscription cleanup correctly', () => {
      const mockCallback = jest.fn();
      const unsubscribe = assistantSyncManager.subscribe(mockCallback);
      
      expect(assistantSyncManager.getSyncStatus().subscriberCount).toBe(1);
      
      unsubscribe();
      
      expect(assistantSyncManager.getSyncStatus().subscriberCount).toBe(0);
    });
  });

  describe('Assistant Selection Synchronization', () => {
    test('should synchronize assistant selection across all components', async () => {
      const mockCallback = jest.fn();
      assistantSyncManager.subscribe(mockCallback);
      
      // Mock AssistantDataService methods
      jest.spyOn(AssistantDataService, 'updateCurrentAssistantSelection')
        .mockResolvedValue();
      jest.spyOn(AssistantDataService, 'getAssistantData')
        .mockResolvedValue(mockAssistantData);
      jest.spyOn(AssistantDataService, 'refreshComponentData')
        .mockResolvedValue();
      
      await assistantSyncManager.synchronizeAssistantSelection(mockAttorneyId, mockAssistantId);
      
      // Verify database update was called
      expect(AssistantDataService.updateCurrentAssistantSelection)
        .toHaveBeenCalledWith(mockAttorneyId, mockAssistantId);
      
      // Verify assistant data was loaded
      expect(AssistantDataService.getAssistantData)
        .toHaveBeenCalledWith(mockAssistantId, mockAttorneyId);
      
      // Verify component refresh was triggered
      expect(AssistantDataService.refreshComponentData)
        .toHaveBeenCalledWith(mockAttorneyId, mockAssistantId);
      
      // Verify subscribers were notified
      expect(mockCallback).toHaveBeenCalledWith('assistant_selected', {
        assistantId: mockAssistantId,
        attorneyId: mockAttorneyId,
        assistantData: mockAssistantData,
        timestamp: expect.any(String)
      });
    });

    test('should handle synchronization errors gracefully', async () => {
      const mockCallback = jest.fn();
      assistantSyncManager.subscribe(mockCallback);
      
      const mockError = new Error('Sync failed');
      jest.spyOn(AssistantDataService, 'updateCurrentAssistantSelection')
        .mockRejectedValue(mockError);
      
      await expect(
        assistantSyncManager.synchronizeAssistantSelection(mockAttorneyId, mockAssistantId)
      ).rejects.toThrow('Sync failed');
      
      // Verify error notification was sent
      expect(mockCallback).toHaveBeenCalledWith('sync_error', {
        error: 'Sync failed',
        assistantId: mockAssistantId,
        attorneyId: mockAttorneyId
      });
    });
  });

  describe('Data Modification Synchronization', () => {
    test('should synchronize UI config changes to both Supabase and Vapi', async () => {
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      const mockCallback = jest.fn();
      assistantSyncManager.subscribe(mockCallback);
      
      // Mock the sync methods
      jest.spyOn(AssistantDataService, 'saveToSupabase').mockResolvedValue();
      jest.spyOn(AssistantDataService, 'syncToVapi').mockResolvedValue();
      
      const testData = {
        vapi_instructions: 'Updated instructions',
        voice_id: 'nova',
        ai_model: 'gpt-4'
      };
      
      await assistantSyncManager.synchronizeDataModification('ui_config', testData);
      
      // Verify Supabase save was called
      expect(AssistantDataService.saveToSupabase)
        .toHaveBeenCalledWith(mockAttorneyId, mockAssistantId, 'ui_config', testData);
      
      // Verify Vapi sync was called
      expect(AssistantDataService.syncToVapi)
        .toHaveBeenCalledWith(mockAssistantId, 'ui_config', testData);
      
      // Verify subscribers were notified
      expect(mockCallback).toHaveBeenCalledWith('data_synchronized', {
        dataType: 'ui_config',
        data: testData,
        assistantId: mockAssistantId,
        attorneyId: mockAttorneyId,
        timestamp: expect.any(String)
      });
    });

    test('should handle custom fields synchronization', async () => {
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      jest.spyOn(AssistantDataService, 'saveCustomFieldsToSupabase').mockResolvedValue();
      jest.spyOn(AssistantDataService, 'syncCustomFieldsToVapi').mockResolvedValue();
      
      const customFieldsData = {
        customFields: [
          { name: 'client_type', type: 'string', required: true },
          { name: 'case_value', type: 'number', required: false }
        ],
        structuredDataPrompt: 'Extract client information'
      };
      
      await assistantSyncManager.synchronizeDataModification('custom_fields', customFieldsData);
      
      // Verify both Supabase and Vapi were updated
      expect(AssistantDataService.saveCustomFieldsToSupabase)
        .toHaveBeenCalledWith(mockAttorneyId, mockAssistantId, customFieldsData);
      expect(AssistantDataService.syncCustomFieldsToVapi)
        .toHaveBeenCalledWith(mockAssistantId, customFieldsData);
    });
  });

  describe('Real-time Updates', () => {
    test('should handle real-time database updates correctly', () => {
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      const mockCallback = jest.fn();
      assistantSyncManager.subscribe(mockCallback);
      
      // Simulate a real-time update
      const mockPayload = {
        new: {
          assistant_id: mockAssistantId,
          updated_at: new Date().toISOString()
        }
      };
      
      assistantSyncManager.handleRealtimeUpdate('ui_config', mockPayload);
      
      expect(mockCallback).toHaveBeenCalledWith('realtime_update', {
        dataType: 'ui_config',
        payload: mockPayload,
        assistantId: mockAssistantId,
        timestamp: expect.any(String)
      });
    });

    test('should ignore updates for different assistants', () => {
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      const mockCallback = jest.fn();
      assistantSyncManager.subscribe(mockCallback);
      
      // Simulate update for different assistant
      const mockPayload = {
        new: {
          assistant_id: 'different-assistant-id',
          updated_at: new Date().toISOString()
        }
      };
      
      assistantSyncManager.handleRealtimeUpdate('ui_config', mockPayload);
      
      // Should not notify subscribers
      expect(mockCallback).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should prevent concurrent synchronization operations', async () => {
      assistantSyncManager.syncInProgress = true;
      
      const result = await assistantSyncManager.synchronizeAssistantSelection(
        mockAttorneyId, 
        mockAssistantId
      );
      
      expect(result).toBeUndefined();
    });

    test('should reset sync state after errors', async () => {
      const mockError = new Error('Test error');
      jest.spyOn(AssistantDataService, 'updateCurrentAssistantSelection')
        .mockRejectedValue(mockError);
      
      await expect(
        assistantSyncManager.synchronizeAssistantSelection(mockAttorneyId, mockAssistantId)
      ).rejects.toThrow('Test error');
      
      // Verify sync state was reset
      expect(assistantSyncManager.getSyncStatus().syncInProgress).toBe(false);
    });
  });

  describe('Component Integration', () => {
    test('should provide consistent data across all dashboard components', () => {
      // This test would verify that all components receive the same assistant data
      // when an assistant is selected, ensuring no data inconsistencies
      
      assistantSyncManager.setCurrentAssistant(mockAssistantId, mockAttorneyId);
      
      const dropdownCallback = jest.fn();
      const shareCallback = jest.fn();
      const dataCollectionCallback = jest.fn();
      const callsCallback = jest.fn();
      const briefsCallback = jest.fn();
      
      // Simulate multiple components subscribing
      assistantSyncManager.subscribe(dropdownCallback);
      assistantSyncManager.subscribe(shareCallback);
      assistantSyncManager.subscribe(dataCollectionCallback);
      assistantSyncManager.subscribe(callsCallback);
      assistantSyncManager.subscribe(briefsCallback);
      
      // Trigger an assistant change
      assistantSyncManager.setCurrentAssistant('new-assistant-id', mockAttorneyId);
      
      // Verify all components received the same notification
      const expectedEvent = {
        previousAssistantId: mockAssistantId,
        currentAssistantId: 'new-assistant-id',
        attorneyId: mockAttorneyId,
        timestamp: expect.any(String)
      };
      
      expect(dropdownCallback).toHaveBeenCalledWith('assistant_changed', expectedEvent);
      expect(shareCallback).toHaveBeenCalledWith('assistant_changed', expectedEvent);
      expect(dataCollectionCallback).toHaveBeenCalledWith('assistant_changed', expectedEvent);
      expect(callsCallback).toHaveBeenCalledWith('assistant_changed', expectedEvent);
      expect(briefsCallback).toHaveBeenCalledWith('assistant_changed', expectedEvent);
    });
  });
});
