/**
 * DEFINITIVE TEST - This will make everything crystal clear
 * 
 * Tests the exact same Supabase connection the webhook uses
 */

import fetch from 'node-fetch';

const TEST_CALL_ID = 'definitive-' + Date.now();

async function testWebhookSupabaseConnection() {
  console.log('🎯 DEFINITIVE WEBHOOK TEST');
  console.log('=' .repeat(50));
  
  const url = 'https://legalscout.net/api/vapi-webhook-direct';
  
  const testData = {
    id: TEST_CALL_ID,
    assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
    status: 'completed',
    duration: 60,
    start_time: new Date().toISOString(),
    end_time: new Date().toISOString(),
    customer: { phone_number: '+1234567890' },
    transcripts: [],
    messages: [],
    tool_executions: [],
    metadata: {}
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log('WEBHOOK RESPONSE:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success && !result.error) {
      console.log('\n✅ SUCCESS: Webhook processed without errors');
      console.log('🎉 Your Vapi calls will now appear in dashboard!');
      console.log('\n📋 NEXT STEP: Configure Vapi webhook URL to:');
      console.log('   https://legalscout.net/api/vapi-webhook-direct');
    } else if (result.error) {
      console.log('\n❌ FAILED: Webhook error:');
      console.log('   ' + result.error);
      
      if (result.error.includes('attorneys') && result.error.includes('does not exist')) {
        console.log('\n🔍 DIAGNOSIS: Wrong Supabase database');
        console.log('   The webhook is connecting to a different database');
      } else if (result.error.includes('configuration missing')) {
        console.log('\n🔍 DIAGNOSIS: Missing environment variables');
      } else {
        console.log('\n🔍 DIAGNOSIS: Unknown Supabase error');
      }
    }
    
  } catch (error) {
    console.log('\n❌ NETWORK ERROR:', error.message);
  }
}

testWebhookSupabaseConnection();
