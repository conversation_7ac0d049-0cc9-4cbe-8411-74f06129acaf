const fs = require('fs');
const path = require('path');
const { writeFile } = require('fs/promises');

// Function to update the HTML table with subdomain data
async function updateTable() {
  const configFilePath = path.join(__dirname, 'subdomain_config.json');
  if (fs.existsSync(configFilePath)) {
    const fileContent = fs.readFileSync(configFilePath, 'utf8');
    const configData = JSON.parse(fileContent);
    
    let table = '<table border="1"><tr><th>Subdomain</th><th>Firm Name</th><th>Logo URL</th><th>Mascot URL</th><th>VAPI URL</th><th>VAPI Instructions</th><th>VAPI Context</th><th>Interaction Deposit URL</th></tr>';
    for (const subdomain in configData) {
      const data = configData[subdomain];
      table += `<tr>
                  <td>${subdomain}</td>
                  <td>${data.firmName}</td>
                  <td>${data.logo}</td>
                  <td>${data.mascot}</td>
                  <td>${data.vapi_url}</td>
                  <td>${data.vapiInstructions}</td>
                  <td>${data.vapiContext}</td>
                  <td>${data.interactionDepositUrl}</td>
                </tr>`;
    }
    table += '</table>';
    const tableFilePath = path.join(__dirname, 'subdomain_table.html');
    await writeFile(tableFilePath, table, 'utf8');
    console.log(`Table written to: ${tableFilePath}`);
  }
}

module.exports = async (req, res) => {
  try {
    await updateTable();
    res.status(200).json({ message: 'Table updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};