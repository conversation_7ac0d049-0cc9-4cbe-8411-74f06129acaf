/**
 * Assistant Routing Service
 * 
 * This service handles assistant-level routing, bypassing attorney-level confusion.
 * It provides a clean interface for loading assistant configurations from subdomains.
 */

import { getSupabaseUrl, getSupabaseKey } from '../lib/supabase';
import { getCurrentSubdomain } from '../utils/subdomainTester';

class AssistantRoutingService {
  constructor() {
    this.baseUrl = getSupabaseUrl();
    this.apiKey = getSupabaseKey();
    this.cache = new Map();
  }

  /**
   * Direct database query without Supabase client
   */
  async directQuery(table, options = {}) {
    try {
      const { select = '*', eq, single = false } = options;
      
      let url = `${this.baseUrl}/rest/v1/${table}?select=${select}`;
      
      if (eq) {
        Object.entries(eq).forEach(([key, value]) => {
          url += `&${key}=eq.${encodeURIComponent(value)}`;
        });
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': this.apiKey,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const data = await response.json();
      return { data: single ? (data[0] || null) : data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get assistant configuration for current subdomain
   */
  async getAssistantConfig(subdomain = null) {
    try {
      const targetSubdomain = subdomain || getCurrentSubdomain() || 'default';

      console.log(`🎯 [AssistantRouting] Loading config for subdomain: ${targetSubdomain}`);

      // Skip processing for default/www subdomains
      if (targetSubdomain === 'default' || targetSubdomain === 'www') {
        console.log(`📭 [AssistantRouting] Skipping default subdomain`);
        return null;
      }

      // CRITICAL: Add detailed logging for debugging
      console.log(`🔍 [AssistantRouting] Environment check:`, {
        hasWindow: typeof window !== 'undefined',
        hasFetch: typeof fetch !== 'undefined',
        hasHeaders: typeof Headers !== 'undefined',
        baseUrl: this.baseUrl,
        hasApiKey: !!this.apiKey
      });

      // Check cache first
      const cacheKey = `assistant_config_${targetSubdomain}`;
      if (this.cache.has(cacheKey)) {
        console.log(`💾 [AssistantRouting] Using cached config for ${targetSubdomain}`);
        return this.cache.get(cacheKey);
      }

      // Get assistant mapping
      const mappingResult = await this.directQuery('v_subdomain_assistant_lookup', {
        select: '*',
        eq: { 
          subdomain: targetSubdomain,
          is_active: true 
        },
        single: true
      });

      if (mappingResult.error || !mappingResult.data) {
        console.log(`📭 [AssistantRouting] No assistant mapping found for: ${targetSubdomain}`);
        return null;
      }

      const mapping = mappingResult.data;
      console.log(`✅ [AssistantRouting] Found assistant mapping:`, {
        assistant_id: mapping.assistant_id,
        attorney_id: mapping.attorney_id,
        firm_name: mapping.firm_name
      });

      // Get attorney details
      const attorneyResult = await this.directQuery('attorneys', {
        select: '*',
        eq: { id: mapping.attorney_id },
        single: true
      });

      if (attorneyResult.error || !attorneyResult.data) {
        console.log(`❌ [AssistantRouting] Attorney not found: ${mapping.attorney_id}`);
        return null;
      }

      const attorney = attorneyResult.data;

      // CRITICAL: Load assistant-specific UI config
      let assistantUIConfig = null;
      try {
        const uiConfigResult = await this.directQuery('assistant_ui_configs', {
          select: '*',
          eq: { assistant_id: mapping.assistant_id },
          single: true
        });

        if (!uiConfigResult.error && uiConfigResult.data) {
          assistantUIConfig = uiConfigResult.data;
          console.log(`✅ [AssistantRouting] Loaded assistant UI config:`, {
            assistant_id: assistantUIConfig.assistant_id,
            firm_name: assistantUIConfig.firm_name,
            voice_id: assistantUIConfig.voice_id,
            primary_color: assistantUIConfig.primary_color
          });
        } else {
          console.log(`📭 [AssistantRouting] No assistant UI config found for: ${mapping.assistant_id}`);
        }
      } catch (uiConfigError) {
        console.warn(`⚠️ [AssistantRouting] Failed to load assistant UI config:`, uiConfigError.message);
      }

      // Create assistant configuration with assistant-specific UI variables
      const assistantConfig = {
        // Core identifiers
        id: attorney.id,
        assistant_id: mapping.assistant_id,
        attorney_id: mapping.attorney_id,
        subdomain: mapping.subdomain,

        // Display information (prioritize assistant UI config)
        firmName: assistantUIConfig?.firm_name || attorney.firm_name || mapping.firm_name || "LegalScout",
        name: attorney.name || attorney.firm_name,
        email: attorney.email,

        // Assistant-specific settings
        vapi_assistant_id: mapping.assistant_id,
        current_assistant_id: mapping.assistant_id,
        is_primary_assistant: mapping.is_primary,

        // UI Configuration (prioritize assistant-specific)
        welcomeMessage: assistantUIConfig?.welcome_message || attorney.welcome_message || "Hello! How can I help you today?",
        primaryColor: assistantUIConfig?.primary_color || attorney.primary_color || "#3B82F6",
        secondaryColor: assistantUIConfig?.secondary_color || attorney.secondary_color || "#1e40af",
        buttonColor: assistantUIConfig?.button_color || attorney.button_color || "#3b82f6",
        backgroundColor: assistantUIConfig?.background_color || attorney.background_color || "#ffffff",
        backgroundOpacity: assistantUIConfig?.background_opacity || attorney.background_opacity || "1.00",
        buttonOpacity: assistantUIConfig?.button_opacity || attorney.button_opacity || "1.00",
        practiceAreaBackgroundOpacity: assistantUIConfig?.practice_area_background_opacity || attorney.practice_area_background_opacity || "0.10",
        textBackgroundColor: assistantUIConfig?.text_background_color || attorney.text_background_color || "#ffffff",
        logoUrl: assistantUIConfig?.logo_url || attorney.logo_url,
        mascot: assistantUIConfig?.mascot_url || attorney.profile_image,
        buttonImage: assistantUIConfig?.assistant_image_url || attorney.button_image || attorney.profile_image,
        practiceDescription: assistantUIConfig?.practice_description || attorney.practice_description,
        informationGathering: assistantUIConfig?.information_gathering || attorney.information_gathering,
        practiceAreas: assistantUIConfig?.practice_areas || attorney.practice_areas || [],
        officeAddress: assistantUIConfig?.office_address || attorney.office_address,
        schedulingLink: assistantUIConfig?.scheduling_link || attorney.scheduling_link,

        // Vapi Configuration (prioritize assistant-specific)
        vapiInstructions: assistantUIConfig?.vapi_instructions || attorney.vapi_instructions || "You are a helpful legal assistant.",
        vapiContext: assistantUIConfig?.vapi_context || attorney.vapi_context || "",
        aiModel: assistantUIConfig?.ai_model || attorney.ai_model || "gpt-4o",
        voiceId: assistantUIConfig?.voice_id || attorney.voice_id || "alloy",
        voiceProvider: assistantUIConfig?.voice_provider || attorney.voice_provider || "openai",

        // Status
        isActive: true,
        loadedVia: 'assistant_routing_service',
        vapiSyncStatus: 'assistant_subdomain_mapped',
        hasAssistantUIConfig: !!assistantUIConfig
      };

      // Cache the result
      this.cache.set(cacheKey, assistantConfig);
      
      console.log(`🎯 [AssistantRouting] Assistant config loaded:`, {
        firmName: assistantConfig.firmName,
        assistant_id: assistantConfig.assistant_id,
        subdomain: assistantConfig.subdomain
      });

      return assistantConfig;
    } catch (error) {
      console.error('❌ [AssistantRouting] Error loading assistant config:', error);
      return null;
    }
  }

  /**
   * Check if current subdomain has an assistant
   */
  async hasAssistant(subdomain = null) {
    const config = await this.getAssistantConfig(subdomain);
    return !!config;
  }

  /**
   * Get assistant status for subdomain
   */
  async getAssistantStatus(subdomain = null) {
    const targetSubdomain = subdomain || getCurrentSubdomain() || 'default';
    
    if (targetSubdomain === 'default' || targetSubdomain === 'www') {
      return { 
        hasAssistant: false, 
        isMainDomain: true,
        subdomain: targetSubdomain
      };
    }

    const config = await this.getAssistantConfig(targetSubdomain);
    
    return {
      hasAssistant: !!config,
      isMainDomain: false,
      subdomain: targetSubdomain,
      assistant_id: config?.assistant_id,
      firmName: config?.firmName
    };
  }

  /**
   * Clear cache for subdomain
   */
  clearCache(subdomain = null) {
    if (subdomain) {
      this.cache.delete(`assistant_config_${subdomain}`);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Preload assistant config for faster access
   */
  async preloadConfig(subdomain) {
    return this.getAssistantConfig(subdomain);
  }
}

// Export singleton instance
export const assistantRoutingService = new AssistantRoutingService();
export default assistantRoutingService;
