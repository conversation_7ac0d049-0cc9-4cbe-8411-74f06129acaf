import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react'
import { withDevTools } from '../utils/debugConfig'
import useVapiCall from '../hooks/useVapiCall'
import useVapiCallWithDebug from '../hooks/useVapiCallWithDebug'
import useVapiEmissions from '../hooks/useVapiEmissions'
import { CALL_STATUS, DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants'
import './VapiCall.css'
import TextShimmerWave from './TextShimmerWave'
import SpeechParticles from './SpeechParticles'
import FirecrawlResultsDisplay from './search/FirecrawlResultsDisplay'
import { createDemoPreviewConfig, createAttorneyPreviewConfig } from '../utils/previewConfigHandler'
import { getCallDebugger } from '../utils/callDebugger'
import CallDebugPanel from './CallDebugPanel'

// Create a global variable to track if the call is active
// This will prevent the component from being unmounted while the call is active
window.vapiCallActive = false;

/**
 * Component for handling voice calls with Vapi.ai assistant
 *
 * This component manages:
 * - Call initialization and connection
 * - Audio processing
 * - Message handling
 * - Dossier data collection
 *
 * @param {Object} props Component props
 * @param {Function} props.onEndCall Callback function when call ends
 * @param {string} props.subdomain Attorney subdomain for customization
 * @param {Object} props.customInstructions Custom instructions for the Vapi call
 * @param {Object} props.assistantOverrides Overrides for the Vapi assistant configuration
 * @param {boolean} props.isDemo Whether this is a demo preview (uses default assistant)
 * @param {Object} props.attorneyData Attorney data from Supabase (for attorney-specific preview)
 * @param {boolean} props.forceDefaultAssistant Whether to force using the default assistant
 * @param {number} props.initializationDelay Delay in milliseconds before starting the call (default: 500)
 */
const VapiCall = ({
  onEndCall,
  subdomain = 'default',
  customInstructions = null,
  assistantOverrides = null,
  assistantId = null,
  isDemo = false,
  attorneyData = null,
  forceDefaultAssistant = false,
  initializationDelay = 500,
  showDebugPanel = false
}) => {
  // Initialize call debugger
  const callDebugger = useMemo(() => getCallDebugger('VapiCall'), []);
  // Ensure customInstructions is always an object with default values
  const defaultCustomInstructions = {
    firmName: 'LegalScout',
    welcomeMessage: 'Hello, I\'m Scout from LegalScout. How can I help you today?',
    voiceId: 'sarah',
    voiceProvider: '11labs'
  };

  // Merge provided customInstructions with defaults - using useMemo to prevent recreation on every render
  const mergedCustomInstructions = useMemo(() => ({
    ...defaultCustomInstructions,
    ...(customInstructions || {})
  }), [customInstructions]);

  // Create default assistantOverrides if none provided - using useMemo to prevent recreation on every render
  const defaultAssistantOverrides = useMemo(() => ({
    firstMessage: mergedCustomInstructions.welcomeMessage,
    firstMessageMode: 'assistant-speaks-first',
    voice: {
      provider: mergedCustomInstructions.voiceProvider || '11labs',
      voiceId: mergedCustomInstructions.voiceId || 'sarah'
    }
  }), [mergedCustomInstructions.welcomeMessage, mergedCustomInstructions.voiceProvider, mergedCustomInstructions.voiceId]);

  // Use provided assistantOverrides or defaults - using useMemo to prevent recreation on every render
  const mergedAssistantOverrides = useMemo(() =>
    assistantOverrides || defaultAssistantOverrides
  , [assistantOverrides, defaultAssistantOverrides]);
  // Process configuration based on whether this is a demo or attorney-specific preview
  const [processedConfig, setProcessedConfig] = useState(null);

  // Process the configuration on component mount
  useEffect(() => {
    // Skip if we already have a processed config
    if (processedConfig) {
      return;
    }

    callDebugger.log('Processing call configuration');
    let config;

    // If forceDefaultAssistant is true, use the default assistant ID with minimal overrides
    if (forceDefaultAssistant) {
      callDebugger.log('Using default assistant with minimal overrides');
      console.log('VapiCall: Using default assistant with minimal overrides');
      config = {
        previewConfig: {
          welcomeMessage: "Hello! I'm Scout, your AI legal assistant. How can I help you today?",
          firmName: "LegalScout",
          voiceId: "sarah",
          voiceProvider: "11labs"
        },
        vapiConfig: {
          assistantId: DEFAULT_ASSISTANT_ID,
          assistantOverrides: null // No overrides at all for default assistant
        }
      };
    } else if (isDemo) {
      // For demo preview, use the default assistant with template overrides
      config = createDemoPreviewConfig(mergedCustomInstructions);
      callDebugger.log('Using demo preview configuration', config);
      console.log('VapiCall: Using demo preview configuration');
    } else if (attorneyData) {
      // For attorney dashboard preview, use attorney-specific configuration
      config = createAttorneyPreviewConfig(attorneyData);
      callDebugger.log('Using attorney preview configuration', {
        assistantId: config.vapiConfig.assistantId,
        attorneyId: attorneyData.id
      });
      console.log('VapiCall: Using attorney preview configuration');
    } else {
      // Fallback to direct configuration
      config = {
        previewConfig: mergedCustomInstructions,
        vapiConfig: {
          assistantId: assistantId || mergedCustomInstructions.assistantId || DEFAULT_ASSISTANT_ID,
          assistantOverrides: mergedAssistantOverrides
        }
      };
      callDebugger.log('Using direct configuration', {
        assistantId: config.vapiConfig.assistantId
      });
      console.log('VapiCall: Using direct configuration');
    }

    const finalAssistantId = forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : (config.vapiConfig.assistantId || 'Not set');
    callDebugger.log(`Setting processed config with assistantId: ${finalAssistantId}`);
    console.log('VapiCall: Setting processed config with assistantId:', finalAssistantId);

    setProcessedConfig(config);
  }, [isDemo, attorneyData, mergedCustomInstructions, mergedAssistantOverrides, assistantId, forceDefaultAssistant, processedConfig]);

  // Local state for volume level to handle direct updates
  const [localVolumeLevel, setVolumeLevel] = useState(0);

  // Use our custom hook with debugging to manage call state and functionality
  const {
    status,
    dossierData,
    volumeLevel: hookVolumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    startCall: originalStartCall,
    stopCall: originalStopCall,
    vapi,
    messageHistory,
    callId
  } = useVapiCall({
    subdomain,
    onEndCall: (data) => {
      callDebugger.log('Call ended', data);
      callDebugger.updateStatus('ended');
      if (onEndCall) onEndCall(data);
    },
    // Only pass customInstructions if we're not forcing the default assistant
    customInstructions: forceDefaultAssistant ? null : (processedConfig?.previewConfig || mergedCustomInstructions),
    // CRITICAL: For existing assistants, NEVER pass overrides - they're already configured in Vapi
    assistantOverrides: null,
    // Always pass the assistantId - prioritize attorney data
    assistantId: forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : (
      attorneyData?.vapi_assistant_id ||
      processedConfig?.vapiConfig?.assistantId ||
      assistantId
    )
  });

  // Use the local volume level if it's been set, otherwise use the hook's volume level
  const volumeLevel = localVolumeLevel > 0 ? localVolumeLevel : hookVolumeLevel;

  // Wrap the startCall function to add debugging
  const startCall = useCallback(() => {
    const finalAssistantId = forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : (
      attorneyData?.vapi_assistant_id ||
      processedConfig?.vapiConfig?.assistantId ||
      assistantId
    );

    console.log('[VapiCall] Starting call with:', {
      finalAssistantId,
      attorneyDataAssistantId: attorneyData?.vapi_assistant_id,
      processedConfigAssistantId: processedConfig?.vapiConfig?.assistantId,
      propAssistantId: assistantId,
      forceDefaultAssistant,
      subdomain
    });

    callDebugger.log('Starting call', {
      assistantId: finalAssistantId,
      subdomain
    });
    callDebugger.updateStatus('connecting');
    return originalStartCall();
  }, [originalStartCall, forceDefaultAssistant, processedConfig, subdomain, attorneyData, assistantId]);

  // Wrap the stopCall function to add debugging
  const stopCall = useCallback(() => {
    callDebugger.log('Stopping call', { callId });
    return originalStopCall();
  }, [originalStopCall, callId]);

  // Use the emissions service to capture call data
  const {
    initialized: emissionsInitialized,
    monitoring: emissionsMonitoring,
    error: emissionsError,
    transcripts,
    messages: emissionMessages,
    toolExecutions,
    callData,
    dossierData: emissionsDossierData,
    startMonitoring,
    stopMonitoring
  } = useVapiEmissions({
    apiKey: import.meta.env.VITE_VAPI_PUBLIC_KEY,
    callId
  });

  // Use emissions data to update UI
  useEffect(() => {
    if (transcripts && transcripts.length > 0) {
      // Get the latest transcript
      const latestTranscript = transcripts[transcripts.length - 1];

      if (latestTranscript) {
        console.log('Latest transcript from emissions service:', latestTranscript);

        // Extract transcript text
        const transcriptText = latestTranscript.transcript || latestTranscript.text || '';

        if (transcriptText) {
          // Update current transcript for real-time display
          setCurrentTranscript(transcriptText);

          // If this is a final transcript, add it to messages
          if (latestTranscript.is_final) {
            handleTranscript({
              transcript: transcriptText,
              is_final: true
            });
          }
        }
      }
    }
  }, [transcripts]);

  // Use emission messages to update UI
  useEffect(() => {
    if (emissionMessages && emissionMessages.length > 0) {
      // Get the latest message
      const latestMessage = emissionMessages[emissionMessages.length - 1];

      if (latestMessage) {
        console.log('Latest message from emissions service:', latestMessage);

        // Check if this message contains volume level data
        if (latestMessage.volumeLevel !== undefined ||
            latestMessage.volume !== undefined ||
            latestMessage.audioLevel !== undefined) {

          const level = latestMessage.volumeLevel ||
                       latestMessage.volume ||
                       latestMessage.audioLevel || 0;

          console.log('Volume level found in message:', level);
          setVolumeLevel(typeof level === 'number' ? Math.min(Math.max(level, 0), 1) : 0);
        }
      }
    }
  }, [emissionMessages]);

  // Add global event listener to capture all iframe messages
  useEffect(() => {
    // Create a global message handler to capture all iframe messages
    const handleGlobalMessage = (event) => {
      if (!event.data) return;

      // Log all iframe messages for debugging
      if (event.data.what === 'iframe-call-message' ||
          event.data.type === 'model-output' ||
          event.data.type === 'assistant-message' ||
          event.data.type === 'message') {
        console.log('Global iframe message received:', event.data);
      }
    };

    // Add the global event listener
    window.addEventListener('message', handleGlobalMessage);

    // Store the handler reference for cleanup
    window.vapiGlobalMessageHandler = handleGlobalMessage;

    // Cleanup function
    return () => {
      window.removeEventListener('message', handleGlobalMessage);
      delete window.vapiGlobalMessageHandler;
    };
  }, []);

  // Add window event listener for processing specific events
  useEffect(() => {
    // Create a window message handler to capture and process specific events
    const handleWindowMessage = (event) => {
      if (!event.data) return;

      console.log('Window message received for processing:', event.data);

      // Enhanced assistant message detection - handle all possible formats
      const isAssistantMessage = (
        event.data.type === 'model-output' ||
        event.data.type === 'assistant-message' ||
        event.data.type === 'message'
      );

      if (isAssistantMessage) {
        console.log('Assistant message detected in iframe message:', event.data);

        // Extract message content from all possible formats
        let messageContent = '';

        // Handle message object format
        if (event.data.message) {
          if (typeof event.data.message === 'object') {
            messageContent = event.data.message.content ||
                            event.data.message.text ||
                            (typeof event.data.message === 'string' ? event.data.message : '');
          } else if (typeof event.data.message === 'string') {
            messageContent = event.data.message;
          }
        }
        // Handle direct text/content format
        else if (event.data.text) {
          messageContent = event.data.text;
        } else if (event.data.content) {
          messageContent = event.data.content;
        } else if (event.data.output) {
          if (typeof event.data.output === 'object') {
            messageContent = event.data.output.text ||
                            event.data.output.content ||
                            (event.data.output.message ? (
                              typeof event.data.output.message === 'object' ?
                                event.data.output.message.content || event.data.output.message.text :
                                event.data.output.message
                            ) : '');
          } else if (typeof event.data.output === 'string') {
            messageContent = event.data.output;
          }
        }

        // Skip empty messages
        if (!messageContent || !messageContent.trim()) {
          console.log('Skipping empty assistant message');
          return;
        }

        // Skip messages that look like system prompts
        if (messageContent.length > 500 &&
            (messageContent.includes('You are a legal assistant') ||
             messageContent.includes('You are an AI assistant') ||
             messageContent.includes('You should always identify yourself as "Scout"'))) {
          console.log('Skipping message that looks like a system prompt');
          return;
        }

        console.log('Adding assistant message to UI:', messageContent);

        // Remove any temporary "thinking" messages
        setMessages(prev => {
          return prev.filter(msg => !msg.isTemporary);
        });

        // Add the message to the UI via React state update
        setMessages(prev => {
          // Check for duplicates - only consider the last few messages to avoid false positives
          const recentMessages = prev.slice(-5);
          const isDuplicate = recentMessages.some(msg =>
            msg.type === 'assistant' &&
            msg.text === messageContent
          );

          if (isDuplicate) {
            console.log('Skipping duplicate assistant message');
            return prev;
          }

          const newMessage = {
            type: 'assistant',
            text: messageContent,
            timestamp: new Date().toISOString(),
            animated: true,
            id: `assistant-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          };

          console.log('Added new assistant message:', newMessage);
          return [...prev, newMessage];
        });
      }

      // Check for transcript data
      if (event.data.type === 'transcript' ||
          event.data.type === 'transcription' ||
          event.data.action === 'transcript' ||
          (event.data.what === 'iframe-call-message' && event.data.action === 'transcript') ||
          (event.data.what === 'iframe-call-message' && event.data.transcript) ||
          (event.data.what === 'iframe-call-message' && event.data.message && typeof event.data.message === 'object' && event.data.message.content)) {

        // Extract transcript text from various possible formats
        let transcriptText = '';
        let isFinal = false;

        if (event.data.transcript) {
          transcriptText = event.data.transcript;
          isFinal = event.data.is_final || event.data.isFinal || false;
        } else if (event.data.text) {
          transcriptText = event.data.text;
          isFinal = event.data.is_final || event.data.isFinal || false;
        } else if (event.data.message && typeof event.data.message === 'object' && event.data.message.content) {
          transcriptText = event.data.message.content;
          isFinal = event.data.message.is_final || false;
        } else if (event.data.content) {
          transcriptText = event.data.content;
          isFinal = event.data.is_final || false;
        }

        console.log(`Window transcript event detected - text: "${transcriptText}", isFinal: ${isFinal}`);

        if (transcriptText && transcriptText.trim()) {
          // Always display the current transcript
          setCurrentTranscript(transcriptText);

          // If final, add to messages (only via React state, not DOM manipulation)
          if (isFinal) {
            console.log('Adding final transcript to messages:', transcriptText);

            // Clear current transcript display
            setCurrentTranscript('');

            // Add to messages via React state only
            handleTranscript({
              transcript: transcriptText,
              is_final: true
            });
          }
        }
      }

      // Check for volume level data
      if (event.data.type === 'volume-level' ||
          event.data.type === 'volume' ||
          event.data.action === 'volume-level' ||
          event.data.action === 'remote-participants-audio-level' ||
          (event.data.what === 'iframe-call-message' && event.data.action === 'remote-participants-audio-level')) {

        let level = 0;

        // Handle regular volume events
        if (event.data.level !== undefined || event.data.volume !== undefined) {
          level = event.data.level || event.data.volume || 0;
        }
        // Handle remote-participants-audio-level events
        else if ((event.data.action === 'remote-participants-audio-level' ||
                 (event.data.what === 'iframe-call-message' && event.data.action === 'remote-participants-audio-level')) &&
                 event.data.participantsAudioLevel) {

          // Extract the audio level from participantsAudioLevel
          // This is an object with participant IDs as keys and audio levels as values
          const audioLevels = Object.values(event.data.participantsAudioLevel);

          // If we have any audio levels, use the maximum value
          if (audioLevels.length > 0) {
            // Convert to numbers and filter out NaN values
            const numericLevels = audioLevels
              .map(level => typeof level === 'number' ? level : parseFloat(level))
              .filter(level => !isNaN(level));

            if (numericLevels.length > 0) {
              level = Math.max(...numericLevels);

              // Scale the level to be more visible
              // The raw levels are often very small (0.01-0.1)
              level = Math.min(level * 5, 1);
            }
          }
        }

        // Only log if level is significant to reduce console spam
        if (level > 0.05) {
          console.log('Window volume event detected:', level);
        }

        // Normalize the level to be between 0 and 1
        const normalizedLevel = typeof level === 'number' ? Math.min(Math.max(level, 0), 1) : 0;

        // Dispatch a custom event for the SpeechParticles component
        try {
          const volumeEvent = new CustomEvent('vapi-volume-change', {
            detail: {
              level: normalizedLevel,
              speaker: 'assistant'
            }
          });
          window.dispatchEvent(volumeEvent);
          console.log('VapiCall: Dispatched volume change event:', normalizedLevel);
        } catch (error) {
          console.warn('Error dispatching volume change event:', error);
        }

        // Update the DOM directly for volume bars (keep this for backward compatibility)
        try {
          const volumeBars = document.querySelectorAll('.volume-bar');
          if (volumeBars && volumeBars.length > 0) {
            const activeBarCount = Math.floor(normalizedLevel * 10);

            volumeBars.forEach((bar, index) => {
              if (index < activeBarCount) {
                bar.classList.add('active');
              } else {
                bar.classList.remove('active');
              }
            });
          }
        } catch (error) {
          console.warn('Error directly manipulating volume bars:', error);
        }
      }
    };

    // Add window event listener
    window.addEventListener('message', handleWindowMessage);

    // Cleanup function
    return () => {
      window.removeEventListener('message', handleWindowMessage);
    };
  }, []);

  // DISABLED: Direct event handlers to prevent conflicts with useVapiCall
  useEffect(() => {
    if (vapi && typeof vapi.on === 'function') {
      console.log('SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts');

      // Event listeners are now handled by useVapiCall hook to prevent conflicts
      // This was causing duplicate event listeners that prevented events from firing properly
      return; // Exit early to skip all the duplicate event listener setup
    }

    // The entire event handler setup section is commented out to prevent conflicts
    /*
      // COMMENTED OUT: This entire section was causing event listener conflicts
      // Create an enhanced message handler function that also extracts transcript and volume data
      const handleDirectMessage = (message) => {
        console.log('Direct message received from Vapi:', message);

        // Process different message formats
        let processedMessage = message;

        // Normalize message structure if needed
        if (typeof message === 'string') {
          try {
            processedMessage = JSON.parse(message);
          } catch (e) {
            processedMessage = { content: message, role: 'assistant' };
          }
        }

        // Check for transcript data in the message
        if (processedMessage.type === 'transcript' ||
            processedMessage.type === 'transcription' ||
            processedMessage.transcriptType ||
            processedMessage.transcript ||
            (processedMessage.text && processedMessage.role === 'user')) {

          console.log('Transcript data found in message:', processedMessage);

          // Extract transcript text
          const transcriptText = processedMessage.transcript ||
                                (processedMessage.role === 'user' ? processedMessage.text : null) ||
                                (processedMessage.message && processedMessage.message.content) ||
                                '';

          if (transcriptText) {
            console.log('Extracted transcript text from message:', transcriptText);
            // Update current transcript for real-time display
            setCurrentTranscript(transcriptText);

            // Check if this is a final transcript
            const isFinal = processedMessage.is_final ||
                           processedMessage.transcriptType === 'final' ||
                           processedMessage.type === 'final' ||
                           processedMessage.role === 'user'; // Treat user messages as final

            if (isFinal) {
              // Add to messages if final
              handleTranscript({
                transcript: transcriptText,
                is_final: true
              });
            }
          }

          // If this is just a transcript, don't process it as a regular message
          if (!processedMessage.role) {
            return;
          }
        }

        // Check for volume level data in the message
        if (processedMessage.volumeLevel !== undefined ||
            processedMessage.volume !== undefined ||
            processedMessage.audioLevel !== undefined) {

          const level = processedMessage.volumeLevel ||
                       processedMessage.volume ||
                       processedMessage.audioLevel || 0;

          console.log('Volume level found in message:', level);
          setVolumeLevel(typeof level === 'number' ? Math.min(Math.max(level, 0), 1) : 0);
        }

        // Extract the message content for regular messages
        let messageContent = '';
        let messageRole = 'assistant';

        if (processedMessage.content) {
          messageContent = processedMessage.content;
          messageRole = processedMessage.role || 'assistant';
        } else if (processedMessage.text) {
          messageContent = processedMessage.text;
          messageRole = processedMessage.role || 'assistant';
        } else if (processedMessage.message) {
          if (typeof processedMessage.message === 'object') {
            messageContent = processedMessage.message.content || processedMessage.message.text || '';
            messageRole = processedMessage.message.role || 'assistant';
          } else if (typeof processedMessage.message === 'string') {
            messageContent = processedMessage.message;
          }
        } else if (typeof processedMessage === 'string') {
          messageContent = processedMessage;
        }

        // Skip empty messages
        if (!messageContent.trim()) {
          console.log('Skipping empty message');
          return;
        }

        // Skip system messages
        if (messageRole === 'system') {
          console.log('Skipping system message:', messageContent.substring(0, 50) + '...');
          return;
        }

        // Remove any temporary "thinking" messages if this is an assistant message
        if (messageRole === 'assistant') {
          setMessages(prev => {
            return prev.filter(msg => !msg.isTemporary);
          });
        }

        // Add the message to the UI
        console.log(`Adding ${messageRole} message to UI:`, messageContent);
        setMessages(prev => {
          // Check for duplicates - only consider recent messages
          const recentMessages = prev.slice(-5);
          const isDuplicate = recentMessages.some(msg =>
            msg.type === (messageRole === 'assistant' ? 'assistant' : 'user') &&
            msg.text === messageContent
          );

          if (isDuplicate) {
            console.log('Skipping duplicate message');
            return prev;
          }

          const newMessage = {
            type: messageRole === 'assistant' ? 'assistant' : 'user',
            text: messageContent,
            timestamp: new Date().toISOString(),
            animated: messageRole === 'assistant',
            id: `${messageRole}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          };

          console.log('Added new message:', newMessage);
          return [...prev, newMessage];
        });
      };

      // Create speech handlers
      const handleSpeechStart = () => {
        console.log('Direct speech-start event received from Vapi');
        // Set assistantIsSpeaking to true to ensure the UI reflects the speaking state
        // This is handled by the useVapiCall hook, but we set it here as well for redundancy
        console.log('Setting assistantIsSpeaking to true from direct event handler');
      };

      const handleSpeechEnd = () => {
        console.log('Direct speech-end event received from Vapi');
      };

      // Create enhanced model output handler for direct message content
      const handleModelOutput = (output) => {
        console.log('Direct model-output event received from Vapi:', output);

        // Extract message content from all possible formats
        let messageContent = '';

        if (output && typeof output === 'object') {
          messageContent = output.text || output.content ||
                          (output.message ? (output.message.content || output.message.text || '') : '') ||
                          '';
        } else if (typeof output === 'string') {
          messageContent = output;
        }

        // Skip empty messages
        if (!messageContent.trim()) {
          console.log('Skipping empty model output message');
          return;
        }

        // Add the message to the UI
        console.log('Adding model output message to UI:', messageContent);

        // Remove any temporary "thinking" messages
        setMessages(prev => {
          return prev.filter(msg => !msg.isTemporary);
        });

        // Update React state with the new message
        setMessages(prev => {
          // Check for duplicates - only consider the last few messages to avoid false positives
          const recentMessages = prev.slice(-5);
          const isDuplicate = recentMessages.some(msg =>
            msg.type === 'assistant' &&
            msg.text === messageContent
          );

          if (isDuplicate) {
            console.log('Skipping duplicate model output message');
            return prev;
          }

          const newMessage = {
            type: 'assistant',
            text: messageContent,
            timestamp: new Date().toISOString(),
            animated: true,
            id: `model-output-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          };

          console.log('Added new model output message:', newMessage);
          return [...prev, newMessage];
        });
      };

      // Create enhanced transcript handler with direct DOM manipulation
      const handleTranscript = (transcript) => {
        console.log('Direct transcript event received from Vapi:', transcript);

        // Handle different transcript formats
        let transcriptText = '';
        let isFinal = false;

        // Extract text from various possible formats
        if (typeof transcript === 'string') {
          // Direct string format
          transcriptText = transcript;
        } else if (transcript && typeof transcript === 'object') {
          // Object format with various possible properties
          transcriptText = transcript.transcript || transcript.text || transcript.content ||
                          (transcript.message ? transcript.message.content : '') || '';

          // Determine if this is a final transcript
          isFinal = transcript.is_final ||
                   transcript.transcriptType === 'final' ||
                   transcript.type === 'final' ||
                   (transcript.message && transcript.message.is_final);
        }

        // Skip empty transcripts
        if (!transcriptText.trim()) {
          console.log('Skipping empty transcript');
          return;
        }

        console.log('Processed transcript:', { text: transcriptText, isFinal });

        // Always update current transcript for real-time display
        setCurrentTranscript(transcriptText);

        // Direct DOM manipulation to ensure transcript is displayed
        try {
          // Try to find or create a transcript element
          let transcriptElement = document.querySelector('.transcribing');

          if (!transcriptElement && transcriptText) {
            // If no transcript element exists, try to create one
            const conversationArea = document.querySelector('.conversation-area');
            if (conversationArea) {
              console.log('Creating transcript element via DOM');
              transcriptElement = document.createElement('div');
              transcriptElement.className = 'message user transcribing';

              const messageContent = document.createElement('div');
              messageContent.className = 'message-content';

              const textElement = document.createElement('p');
              const typingIndicator = document.createElement('div');
              typingIndicator.className = 'typing-indicator';
              typingIndicator.textContent = '...';

              messageContent.appendChild(textElement);
              messageContent.appendChild(typingIndicator);
              transcriptElement.appendChild(messageContent);

              // Insert before the messagesEndRef element
              const messagesEndElement = conversationArea.querySelector('[ref="messagesEndRef"]');
              if (messagesEndElement) {
                conversationArea.insertBefore(transcriptElement, messagesEndElement);
              } else {
                conversationArea.appendChild(transcriptElement);
              }
            }
          }

          // Update the transcript text if we have an element
          if (transcriptElement) {
            const textElement = transcriptElement.querySelector('p');
            if (textElement) {
              textElement.textContent = transcriptText;
              console.log('Updated transcript text via DOM:', transcriptText);
            }
          }
        } catch (error) {
          console.warn('Error directly manipulating transcript element:', error);
        }

        // Only add final transcripts as messages
        if (isFinal) {
          console.log('Adding final transcript to UI:', transcriptText);
          setMessages(prev => {
            // Check for duplicates - be more strict about duplicate detection
            const isDuplicate = prev.some(msg =>
              msg.type === 'user' &&
              (msg.text === transcriptText || msg.text.includes(transcriptText) || transcriptText.includes(msg.text))
            );

            if (isDuplicate) {
              console.log('Skipping duplicate transcript message:', transcriptText);
              return prev;
            }

            return [...prev, {
              type: 'user',
              text: transcriptText,
              timestamp: new Date().toISOString(),
              animated: false,
              isTranscript: true,
              id: `transcript-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
            }];
          });

          // Clear current transcript after adding final transcript
          setCurrentTranscript('');

          // Also remove the transcript element via DOM
          try {
            const transcriptElement = document.querySelector('.transcribing');
            if (transcriptElement) {
              setTimeout(() => {
                transcriptElement.remove();
                console.log('Removed transcript element via DOM');
              }, 500);
            }
          } catch (error) {
            console.warn('Error removing transcript element:', error);
          }
        }
      };

      // Add a generic event handler to log all events
      const handleAnyEvent = (eventName) => (data) => {
        console.log(`🔍 DEBUG: Vapi event '${eventName}' received:`, data);
      };

      // Create a map to store event handlers for cleanup
      const eventHandlers = new Map();

      // Register the generic event handler for all possible events
      const possibleEvents = [
        'message', 'speech-start', 'speech-end', 'model-output',
        'transcript', 'transcription', 'transcription-update', 'transcription-partial', 'transcription-final',
        'interim-result', 'final-result', 'volume-level', 'volume', 'audio-level',
        'call-start', 'call-end', 'call-started', 'call-ended', 'error'
      ];

      // Register all possible events with the debug handler
      possibleEvents.forEach(eventName => {
        try {
          // Create a handler for this specific event
          const handler = handleAnyEvent(eventName);
          // Store the handler in the map for cleanup
          eventHandlers.set(eventName, handler);
          // Register the handler
          vapi.on(eventName, handler);
        } catch (error) {
          console.warn(`Error registering debug handler for event '${eventName}':`, error);
        }
      });

      // Store the event handlers map on the vapi instance for cleanup
      vapi._eventHandlers = eventHandlers;

      // Add all event handlers with comprehensive event coverage
      try {
        eventHandlers.set('message', handleDirectMessage);
        vapi.on('message', handleDirectMessage);
      } catch (error) {
        console.warn('Error registering message handler:', error);
      }

      try {
        eventHandlers.set('speech-start', handleSpeechStart);
        vapi.on('speech-start', handleSpeechStart);
      } catch (error) {
        console.warn('Error registering speech-start handler:', error);
      }

      try {
        eventHandlers.set('speech-end', handleSpeechEnd);
        vapi.on('speech-end', handleSpeechEnd);
      } catch (error) {
        console.warn('Error registering speech-end handler:', error);
      }

      try {
        eventHandlers.set('model-output', handleModelOutput);
        vapi.on('model-output', handleModelOutput);
      } catch (error) {
        console.warn('Error registering model-output handler:', error);
      }

      // Add additional event listeners for assistant messages
      try {
        eventHandlers.set('assistant-message', handleDirectMessage);
        vapi.on('assistant-message', handleDirectMessage);
      } catch (error) {
        console.warn('Error registering assistant-message handler:', error);
      }

      try {
        eventHandlers.set('assistant-response', handleDirectMessage);
        vapi.on('assistant-response', handleDirectMessage);
      } catch (error) {
        console.warn('Error registering assistant-response handler:', error);
      }

      // Define the assistant speaking handler
      const assistantSpeakingHandler = (data) => {
        console.log('Assistant speaking event:', data);
        // This event indicates the assistant is speaking
        if (data && typeof data.speaking === 'boolean') {
          console.log('Setting assistantIsSpeaking to:', data.speaking);
          setAssistantIsSpeaking(data.speaking);
        }
      };

      // Define the text handler
      const textHandler = (text) => {
        console.log('Text event received:', text);
        if (text && typeof text === 'string') {
          handleDirectMessage({
            type: 'message',
            role: 'assistant',
            content: text
          });
        }
      };

      // Define the output handler
      const outputHandler = (output) => {
        console.log('Output event received:', output);
        if (output) {
          if (typeof output === 'string') {
            handleDirectMessage({
              type: 'message',
              role: 'assistant',
              content: output
            });
          } else if (typeof output === 'object') {
            handleModelOutput(output);
          }
        }
      };

      // Define the any handler
      const anyHandler = (data) => {
        console.log('Any event received:', data);
        // Try to extract message content from any event
        if (data) {
          let messageContent = '';
          let messageRole = 'assistant';

          if (typeof data === 'string') {
            messageContent = data;
          } else if (typeof data === 'object') {
            messageContent = data.text || data.content ||
                            (data.message ? (typeof data.message === 'object' ?
                              data.message.content || data.message.text : data.message) : '');
            messageRole = data.role || 'assistant';
          }

          if (messageContent && messageContent.trim()) {
            handleDirectMessage({
              type: 'message',
              role: messageRole,
              content: messageContent
            });
          }
        }
      };

      // Store the handlers in the event handlers map
      eventHandlers.set('assistant-speaking', assistantSpeakingHandler);
      eventHandlers.set('text', textHandler);
      eventHandlers.set('output', outputHandler);
      eventHandlers.set('any', anyHandler);

      // Register the handlers
      try { vapi.on('assistant-speaking', assistantSpeakingHandler); } catch (e) { console.warn('Error registering assistant-speaking handler:', e); }
      try { vapi.on('text', textHandler); } catch (e) { console.warn('Error registering text handler:', e); }
      try { vapi.on('output', outputHandler); } catch (e) { console.warn('Error registering output handler:', e); }
      try { vapi.on('any', anyHandler); } catch (e) { console.warn('Error registering any handler:', e); }

      // Add all possible transcript event names for different SDK versions
      const transcriptEvents = [
        'transcript', 'transcription', 'transcription-update',
        'transcription-partial', 'transcription-final',
        'interim-result', 'final-result'
      ];

      // Register transcript handlers and store them in the event handlers map
      transcriptEvents.forEach(eventName => {
        try {
          eventHandlers.set(eventName, handleTranscript);
          vapi.on(eventName, handleTranscript);
        } catch (error) {
          console.warn(`Error registering transcript handler for event '${eventName}':`, error);
        }
      });

      // Create named handler functions for volume events with direct DOM manipulation
      const handleVolumeLevel = (level) => {
        console.log('Direct volume-level event received from Vapi:', level);
        // Ensure level is a number between 0 and 1
        const normalizedLevel = typeof level === 'number' ? Math.min(Math.max(level, 0), 1) : 0;
        console.log('Setting volume level to:', normalizedLevel);

        // Update React state
        setVolumeLevel(normalizedLevel);

        // Dispatch a custom event for the SpeechParticles component
        try {
          const volumeEvent = new CustomEvent('vapi-volume-change', {
            detail: {
              level: normalizedLevel,
              speaker: 'assistant'
            }
          });
          window.dispatchEvent(volumeEvent);
          console.log('VapiCall: Dispatched volume change event from direct handler:', normalizedLevel);
        } catch (error) {
          console.warn('Error dispatching volume change event from direct handler:', error);
        }

        // Direct DOM manipulation as a fallback
        try {
          const volumeBars = document.querySelectorAll('.volume-bar');
          if (volumeBars && volumeBars.length > 0) {
            const activeBarCount = Math.floor(normalizedLevel * 10);
            console.log(`Directly updating ${activeBarCount} volume bars in the DOM`);

            volumeBars.forEach((bar, index) => {
              if (index < activeBarCount) {
                bar.classList.add('active');
              } else {
                bar.classList.remove('active');
              }
            });
          }
        } catch (error) {
          console.warn('Error directly manipulating volume bars:', error);
        }
      };

      const handleVolume = (level) => {
        console.log('Alternative volume event received from Vapi:', level);
        handleVolumeLevel(level); // Reuse the same handler for consistency
      };

      const handleAudioLevel = (level) => {
        console.log('Audio-level event received from Vapi:', level);
        handleVolumeLevel(level); // Reuse the same handler for consistency
      };

      // Add volume event listeners using the named handler functions
      try {
        eventHandlers.set('volume-level', handleVolumeLevel);
        vapi.on('volume-level', handleVolumeLevel);
      } catch (error) {
        console.warn('Error registering volume-level handler:', error);
      }

      try {
        eventHandlers.set('volume', handleVolume);
        vapi.on('volume', handleVolume);
      } catch (error) {
        console.warn('Error registering volume handler:', error);
      }

      try {
        eventHandlers.set('audio-level', handleAudioLevel);
        vapi.on('audio-level', handleAudioLevel);
      } catch (error) {
        console.warn('Error registering audio-level handler:', error);
      }

      // Return cleanup function with comprehensive event cleanup
      return () => {
        if (vapi && typeof vapi.off === 'function') {
          // Remove debug event handlers using the stored handlers
          if (vapi._eventHandlers && vapi._eventHandlers instanceof Map) {
            vapi._eventHandlers.forEach((handler, eventName) => {
              try {
                vapi.off(eventName, handler);
              } catch (error) {
                console.warn(`Error removing debug handler for event '${eventName}':`, error);
              }
            });
          } else {
            console.warn('No event handlers map found on vapi instance, skipping debug handler cleanup');
          }

          // Remove basic event listeners using the stored handlers
          try {
            const messageHandler = eventHandlers.get('message');
            if (messageHandler) {
              vapi.off('message', messageHandler);
            } else {
              vapi.off('message', handleDirectMessage);
            }
          } catch (error) {
            console.warn('Error removing message handler:', error);
          }

          try {
            const speechStartHandler = eventHandlers.get('speech-start');
            if (speechStartHandler) {
              vapi.off('speech-start', speechStartHandler);
            } else {
              vapi.off('speech-start', handleSpeechStart);
            }
          } catch (error) {
            console.warn('Error removing speech-start handler:', error);
          }

          try {
            const speechEndHandler = eventHandlers.get('speech-end');
            if (speechEndHandler) {
              vapi.off('speech-end', speechEndHandler);
            } else {
              vapi.off('speech-end', handleSpeechEnd);
            }
          } catch (error) {
            console.warn('Error removing speech-end handler:', error);
          }

          try {
            const modelOutputHandler = eventHandlers.get('model-output');
            if (modelOutputHandler) {
              vapi.off('model-output', modelOutputHandler);
            } else {
              vapi.off('model-output', handleModelOutput);
            }
          } catch (error) {
            console.warn('Error removing model-output handler:', error);
          }

          // Remove additional event listeners
          try {
            const assistantMessageHandler = eventHandlers.get('assistant-message');
            if (assistantMessageHandler) {
              vapi.off('assistant-message', assistantMessageHandler);
            } else {
              vapi.off('assistant-message', handleDirectMessage);
            }
          } catch (error) {
            console.warn('Error removing assistant-message handler:', error);
          }

          try {
            const assistantResponseHandler = eventHandlers.get('assistant-response');
            if (assistantResponseHandler) {
              vapi.off('assistant-response', assistantResponseHandler);
            } else {
              vapi.off('assistant-response', handleDirectMessage);
            }
          } catch (error) {
            console.warn('Error removing assistant-response handler:', error);
          }

          // Remove handlers with proper references using the stored handlers from eventHandlers map
          try {
            const assistantSpeakingHandler = eventHandlers.get('assistant-speaking');
            if (assistantSpeakingHandler) {
              vapi.off('assistant-speaking', assistantSpeakingHandler);
            }
          } catch (e) { console.warn('Error removing assistant-speaking handler:', e); }
          try {
            const textHandler = eventHandlers.get('text');
            if (textHandler) {
              vapi.off('text', textHandler);
            }
          } catch (e) { console.warn('Error removing text handler:', e); }
          try {
            const outputHandler = eventHandlers.get('output');
            if (outputHandler) {
              vapi.off('output', outputHandler);
            }
          } catch (e) { console.warn('Error removing output handler:', e); }
          try {
            const anyHandler = eventHandlers.get('any');
            if (anyHandler) {
              vapi.off('any', anyHandler);
            }
          } catch (e) { console.warn('Error removing any handler:', e); }

          // Remove all transcript event listeners using the stored handlers
          const transcriptEvents = [
            'transcript', 'transcription', 'transcription-update',
            'transcription-partial', 'transcription-final',
            'interim-result', 'final-result'
          ];

          transcriptEvents.forEach(eventName => {
            try {
              const handler = eventHandlers.get(eventName);
              if (handler) {
                vapi.off(eventName, handler);
              } else {
                // Fallback to using the direct handler reference
                vapi.off(eventName, handleTranscript);
              }
            } catch (error) {
              console.warn(`Error removing transcript handler for event '${eventName}':`, error);
            }
          });

          // Remove all volume event listeners with their specific handlers
          try {
            const volumeLevelHandler = eventHandlers.get('volume-level');
            if (volumeLevelHandler) {
              vapi.off('volume-level', volumeLevelHandler);
            } else {
              vapi.off('volume-level', handleVolumeLevel);
            }
          } catch (error) {
            console.warn('Error removing volume-level handler:', error);
          }

          try {
            const volumeHandler = eventHandlers.get('volume');
            if (volumeHandler) {
              vapi.off('volume', volumeHandler);
            } else {
              vapi.off('volume', handleVolume);
            }
          } catch (error) {
            console.warn('Error removing volume handler:', error);
          }

          try {
            const audioLevelHandler = eventHandlers.get('audio-level');
            if (audioLevelHandler) {
              vapi.off('audio-level', audioLevelHandler);
            } else {
              vapi.off('audio-level', handleAudioLevel);
            }
          } catch (error) {
            console.warn('Error removing audio-level handler:', error);
          }

          console.log('Removed all event listeners from Vapi instance');
        }
        */

        // END OF COMMENTED OUT SECTION - Event listeners now handled by useVapiCall
  }, [vapi]); // Close the useEffect hook

  // Track initialization state
  const [isInitializing, setIsInitializing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationAttempts, setInitializationAttempts] = useState(0);
  const MAX_INITIALIZATION_ATTEMPTS = 3;

  // Auto-start the call when the component mounts and clean up when unmounting
  useEffect(() => {
    console.log('🚀 VapiCall component mounted, preparing to start call...');
    console.log('📊 Current status:', status);
    console.log('🤖 Using assistant ID:', assistantId || processedConfig?.vapiConfig?.assistantId || 'Not set yet');
    console.log('🔧 Vapi instance available:', !!vapi);
    console.log('⚙️ Processed config:', processedConfig);

    // Check if another call is already active globally (but allow this component to initialize)
    if (window.vapiCallActive === true && window.vapiCallActive !== 'initializing') {
      console.log('⚠️ Another call is already active, skipping initialization');
      return () => {
        console.log('🧹 Cleanup function for skipped initialization');
      };
    }

    // CRITICAL FIX: Don't wait for processedConfig if we have a direct assistantId
    const effectiveAssistantId = assistantId || processedConfig?.vapiConfig?.assistantId;

    // Check if we have the minimum requirements to start
    if (!effectiveAssistantId || !vapi) {
      console.log('⏸️ Not ready to initialize yet - missing assistantId or vapi');
      console.log('⏸️ Assistant ID:', effectiveAssistantId);
      console.log('⏸️ Vapi instance:', !!vapi);
      return () => {
        console.log('🧹 Cleanup function for not ready state');
      };
    }

    // Set the global variable to indicate that a call is initializing
    window.vapiCallActive = 'initializing';
    console.log('✅ Set window.vapiCallActive to initializing');

    // Track if the component is mounted
    const isMounted = { current: true };

    // Use the provided initialization delay or default to 500ms
    const delay = initializationDelay || 800;
    console.log(`⏱️ Using initialization delay of ${delay}ms before starting call`);

    // Set initializing state
    setIsInitializing(true);

    // Start the call after a delay to ensure everything is initialized
    const timer = setTimeout(() => {
      // Only proceed if the component is still mounted
      if (isMounted.current && status === 'idle' && !isInitialized) {
        console.log('🎯 Auto-starting call from VapiCall component after delay');
        console.log('📋 Current call parameters:', {
          assistantId: forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : processedConfig?.vapiConfig?.assistantId,
          assistantOverrides: processedConfig?.vapiConfig?.assistantOverrides ? 'Set' : 'Not set',
          vapi: !!vapi,
          processedConfig: !!processedConfig
        });

        // Check if we have the required configuration
        if (!processedConfig) {
          console.error('❌ No processed config available, cannot start call');
          setIsInitializing(false);
          setMessages(prev => [
            ...prev,
            {
              type: 'system',
              text: 'Configuration not ready. Please try again.',
              timestamp: new Date().toISOString()
            }
          ]);
          return;
        }

        const finalAssistantId = forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : processedConfig?.vapiConfig?.assistantId;
        if (!finalAssistantId) {
          console.error('❌ [VAPI CALL ERROR] No assistant ID available, cannot start call');
          setIsInitializing(false);
          setMessages(prev => [
            ...prev,
            {
              type: 'system',
              text: 'Assistant ID not available. Please try again.',
              timestamp: new Date().toISOString()
            }
          ]);
          return;
        }

        console.log('🎯 [VAPI CALL INIT] Final assistant ID:', finalAssistantId);
        console.log('⚙️ [VAPI CALL INIT] Processed config:', processedConfig);

        try {
          // Increment initialization attempts
          setInitializationAttempts(prev => prev + 1);
          console.log(`🔄 Starting call attempt ${initializationAttempts + 1}`);

          // Start the call
          startCall();

          // Mark as initialized after a short delay to ensure the call has time to start
          setTimeout(() => {
            if (isMounted.current) {
              console.log('✅ Marking call as initialized');
              setIsInitialized(true);
              setIsInitializing(false);
              // Now set the global flag to true since call is actually started
              window.vapiCallActive = true;
              console.log('✅ Set window.vapiCallActive to true after initialization');
            }
          }, 500);
        } catch (error) {
          console.error('❌ Error starting call:', error);
          setIsInitializing(false);

          // Add an error message to the UI
          if (isMounted.current) {
            setMessages(prev => [
              ...prev,
              {
                type: 'system',
                text: `There was an error starting the call: ${error.message}. Please try again.`,
                timestamp: new Date().toISOString()
              }
            ]);
          }

          // If we haven't reached the maximum number of attempts, try again
          if (initializationAttempts < MAX_INITIALIZATION_ATTEMPTS && isMounted.current) {
            console.log(`🔄 Retrying call initialization (attempt ${initializationAttempts + 1}/${MAX_INITIALIZATION_ATTEMPTS})`);
            setTimeout(() => {
              if (isMounted.current) {
                setIsInitializing(true);
              }
            }, 1000);
          }
        }
      } else {
        console.log('⏭️ Skipping call start:', {
          mounted: isMounted.current,
          status,
          isInitialized,
          reason: !isMounted.current ? 'not mounted' :
                  status !== 'idle' ? `status is ${status}` :
                  isInitialized ? 'already initialized' : 'unknown'
        });
      }
    }, delay);

    // Clean up function to ensure call is properly terminated when component unmounts
    return () => {
      // Mark component as unmounted
      isMounted.current = false;

      // Clear the timeout to prevent starting a call after unmount
      clearTimeout(timer);
      console.log('VapiCall component unmounting, performing cleanup...');

      // If the call is still initializing, we need to handle this differently
      if (isInitializing && !isInitialized) {
        console.log('Call was still initializing during unmount, cancelling initialization');
        setIsInitializing(false);
        window.vapiCallActive = false;

        // Don't call onEndCall here to prevent the cycle of mount/unmount
        console.log('Skipping onEndCall during initialization cancellation to prevent mount/unmount cycle');
        return;
      }

      // Only proceed with full cleanup if the call was actually started and initialized
      if (!vapi || status === 'idle' || !isInitialized) {
        console.log('Call was not fully initialized, performing simple cleanup');
        window.vapiCallActive = false;

        // Only call onEndCall if the component was fully initialized
        // This prevents the cycle of mount/unmount when initialization is incomplete
        if (isInitialized && typeof onEndCall === 'function' && !onEndCallCalled.current) {
          console.log('Calling onEndCall during unmount for initialized but idle state');
          onEndCall(dossierData);
          onEndCallCalled.current = true;
        } else {
          console.log('Skipping onEndCall for non-initialized state to prevent mount/unmount cycle');
        }

        return;
      }

      // Check if the assistant is still speaking - if so, we need to handle this case carefully
      if (assistantIsSpeaking) {
        console.log('Assistant is still speaking during unmount - showing confirmation');

        // Show a confirmation dialog
        const confirmEnd = window.confirm('The assistant is still speaking. Are you sure you want to end the call?');

        if (!confirmEnd) {
          console.log('User cancelled ending the call while assistant is speaking during unmount');

          // Keep the global variable set to true
          window.vapiCallActive = true;

          // Force the call interface to stay visible
          const callInterface = document.querySelector('.call-interface');
          if (callInterface) {
            callInterface.classList.add('force-visible');
            console.log('Added force-visible class to call interface during unmount attempt');
          }

          // Force the call-card-container to be visible
          const callCardContainer = document.querySelector('.call-card-container');
          if (callCardContainer) {
            callCardContainer.classList.add('active');
            callCardContainer.style.display = 'flex';
            callCardContainer.style.visibility = 'visible';
            callCardContainer.style.opacity = '1';
            callCardContainer.style.zIndex = '1000';
            console.log('Forced call-card-container to be visible during unmount attempt');
          }

          // Don't proceed with cleanup while speaking
          return;
        }

        console.log('User confirmed ending the call while assistant is speaking during unmount');
      }

      // Set the global variable to indicate that the call is no longer active
      // This is critical to prevent race conditions during cleanup
      window.vapiCallActive = false;
      console.log('Set window.vapiCallActive to false during unmount');

      // Stop emissions monitoring if active
      if (emissionsMonitoring && typeof stopMonitoring === 'function') {
        console.log('Stopping emissions monitoring during component unmount');
        stopMonitoring();
      }

      // Clean up any lingering Daily.co iframes first
      try {
        const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
        if (existingIframes.length > 0) {
          console.log(`Found ${existingIframes.length} lingering Daily.co iframes. Removing during unmount...`);
          existingIframes.forEach(iframe => {
            iframe.parentNode.removeChild(iframe);
          });
        }
      } catch (cleanupError) {
        console.warn("Error cleaning up iframes during unmount:", cleanupError);
      }

      // Ensure call is stopped when component unmounts
      if ((status === 'connected' || status === 'connecting' || status === 'active') &&
          typeof stopCall === 'function' && vapi) {
        console.log('Stopping call during component unmount');
        try {
          stopCall();
        } catch (error) {
          console.error('Error stopping call during component unmount:', error);

          // If stopCall fails, try to manually call onEndCall as a fallback
          if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
            console.log('Manually calling onEndCall as fallback during unmount');
            onEndCall(dossierData);
            onEndCallCalled.current = true;
          }
        }
      } else if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
        // If we're not in a connected state but need to clean up, still call onEndCall
        console.log('Calling onEndCall during unmount for non-connected state');
        onEndCall(dossierData);
        onEndCallCalled.current = true;
      }

      // Reset the onEndCallCalled ref
      onEndCallCalled.current = false;

      // Double-check that the global flag is set to false
      window.vapiCallActive = false;
      console.log('Double-checked window.vapiCallActive is false at end of unmount cleanup');
    };
  }, [processedConfig, vapi]); // Only depend on essential dependencies that indicate readiness

  // Start monitoring emissions when call is connected and callId is available
  useEffect(() => {
    if (status === CALL_STATUS.CONNECTED && callId && !emissionsMonitoring && emissionsInitialized) {
      console.log('Starting emissions monitoring for call:', callId);
      startMonitoring(callId);
    }
  }, [status, callId, emissionsMonitoring, emissionsInitialized, startMonitoring]);

  // Stop monitoring emissions when call ends
  useEffect(() => {
    if (status === CALL_STATUS.ENDED && emissionsMonitoring) {
      console.log('Stopping emissions monitoring for call:', callId);
      stopMonitoring();
    }
  }, [status, emissionsMonitoring, stopMonitoring, callId]);

  // Only log essential emissions data for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Removed excessive logging of emissions data
    }
  }, [transcripts, toolExecutions, callData, emissionsDossierData]);

  // Get the logo URL from mergedCustomInstructions or use the default
  const logoUrl = mergedCustomInstructions.logoUrl || '/PRIMARY CLEAR.png';

  // Removed excessive voice settings logging

  // For message handling
  const [messageText, setMessageText] = useState('');
  const [currentTranscript, setCurrentTranscript] = useState(''); // Add state for current transcript
  const [messages, setMessages] = useState([
    // Add a default welcome message to ensure there's always at least one message
    {
      type: 'assistant',
      text: "Hello, I'm Scout from LegalScout. How can I help you today?",
      timestamp: new Date().toISOString(),
      animated: true
    }
  ]);
  const messagesEndRef = useRef(null);

  // Track if onEndCall has been called to prevent duplicate calls
  const onEndCallCalled = useRef(false);

  // For search results
  const [searchResults, setSearchResults] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('web');
  const [isSearching, setIsSearching] = useState(false);

  // Auto-scroll to bottom when messages change - improved to prevent global scroll interference
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const conversationArea = messagesEndRef.current.closest('.conversation-area');
      if (conversationArea) {
        // Check if user is near the bottom (within 100px)
        const isNearBottom = conversationArea.scrollHeight - conversationArea.scrollTop - conversationArea.clientHeight < 100;

        // Only auto-scroll if user is near the bottom (not reading old messages)
        if (isNearBottom) {
          try {
            // Directly set scrollTop without any smooth scrolling or events
            conversationArea.scrollTop = conversationArea.scrollHeight;

            // Ensure the scroll stays within the conversation area
            conversationArea.style.scrollBehavior = 'auto';

            // Force a layout recalculation to ensure scroll position is applied
            conversationArea.offsetHeight;

            console.log('VapiCall: Scrolled conversation area to bottom');
          } catch (error) {
            console.warn('Error scrolling conversation area:', error);
          }
        }
      }
    }
  };

  // Scroll to bottom when messages change - with debouncing
  useEffect(() => {
    // Use a timeout to debounce scroll calls
    const scrollTimeout = setTimeout(() => {
      scrollToBottom();
    }, 50); // Small delay to batch multiple message updates

    return () => clearTimeout(scrollTimeout);
  }, [messages]);

  // Update messages when messageHistory changes
  useEffect(() => {
    if (messageHistory && messageHistory.length > 0) {
      const latestMessage = messageHistory[messageHistory.length - 1];

      if (latestMessage && latestMessage.content) {
        // Skip system messages - they should never be displayed to the user
        if (latestMessage.role === 'system') {
          console.log('Skipping system message, not for display:', latestMessage.content.substring(0, 50) + '...');
          return;
        }

        // Check if the message looks like a system prompt (contains specific keywords)
        const looksLikeSystemPrompt = (content) => {
          const systemPromptIndicators = [
            'You are a legal assistant',
            'You are an AI assistant',
            'You have access to a powerful web search tool',
            'When to use the Firecrawl search tool',
            'How to use the firecrawl_search tool',
            'You should always identify yourself as "Scout"',
            'Your name is Scout',
            'You are Scout',
            'You are a helpful assistant'
          ];

          const contentLower = content.toLowerCase();
          return systemPromptIndicators.some(indicator =>
            contentLower.includes(indicator.toLowerCase())
          );
        };

        // Skip messages that look like system prompts
        if (looksLikeSystemPrompt(latestMessage.content)) {
          console.log('Skipping message that looks like a system prompt:', latestMessage.content.substring(0, 50) + '...');
          return;
        }

        const newMessage = {
          type: latestMessage.role === 'assistant' ? 'assistant' : 'user',
          text: latestMessage.content,
          timestamp: latestMessage.timestamp,
          animated: true,
          isTranscript: latestMessage.isTranscript
        };

        // Check for duplicates before adding the message
        setMessages(prev => {
          // Check if this exact message already exists in the last few messages
          const isDuplicate = prev.some(msg =>
            msg.type === newMessage.type &&
            msg.text === newMessage.text
          );

          if (isDuplicate) {
            console.log('Skipping duplicate message:', newMessage.text);
            return prev;
          }

          // Additional check for very long messages that might be system prompts
          if (newMessage.text.length > 500 && newMessage.type === 'assistant') {
            console.log('Message is suspiciously long, checking if it might be a system prompt...');

            // If it contains multiple newlines and looks like instructions, skip it
            if (newMessage.text.split('\n').length > 5 && looksLikeSystemPrompt(newMessage.text)) {
              console.log('Skipping long message that appears to be a system prompt');
              return prev;
            }
          }

          console.log('Adding new message to UI:', newMessage.text);
          return [...prev, newMessage];
        });
      }
    }
  }, [messageHistory]);

  // Handle status changes - specifically when the call connects
  useEffect(() => {
    console.log('Status changed to:', status);
    console.log('CALL_STATUS.CONNECTED value:', CALL_STATUS.CONNECTED);

    // Force a re-render when status changes to ensure UI updates
    if (status === CALL_STATUS.CONNECTED) {
      console.log('Call connected - checking for custom welcome message');

      // Force a DOM update to ensure the UI reflects the connected state
      setTimeout(() => {
        const callInterface = document.querySelector('.call-interface');
        if (callInterface) {
          callInterface.classList.add('force-visible');
          console.log('Added force-visible class to call interface');
        }

        // Also force the call-card-container to be visible
        const callCardContainer = document.querySelector('.call-card-container');
        if (callCardContainer) {
          callCardContainer.classList.add('active');
          callCardContainer.style.display = 'flex';
          callCardContainer.style.visibility = 'visible';
          callCardContainer.style.opacity = '1';
          console.log('Forced call-card-container to be visible');
        }
      }, 100);

      console.log('mergedCustomInstructions:', mergedCustomInstructions);
      console.log('mergedAssistantOverrides:', {
        firstMessage: mergedAssistantOverrides?.firstMessage ? mergedAssistantOverrides.firstMessage : 'Not present',
        model: mergedAssistantOverrides?.model ? 'Present' : 'Not present',
        artifactPlan: mergedAssistantOverrides?.artifactPlan ? 'Present' : 'Not present'
      });

      // Log the system prompt if present
      if (mergedAssistantOverrides?.model && mergedAssistantOverrides.model.messages) {
        const systemMessage = mergedAssistantOverrides.model.messages.find(m => m.role === 'system');
        if (systemMessage) {
          console.log('System prompt found in mergedAssistantOverrides.model.messages:', {
            length: systemMessage.content.length,
            preview: systemMessage.content.substring(0, 100) + '...'
          });
        }
      }

      // Skip adding welcome message for default assistant - let Vapi handle it
      if (forceDefaultAssistant) {
        console.log('Using default assistant - skipping manual welcome message to avoid duplication');
        return;
      }

      // First check if we have assistantOverrides with a welcome message
      let welcomeMessage = null;

      // Check all possible sources for the welcome message in priority order
      if (mergedCustomInstructions?.welcomeMessage) {
        welcomeMessage = mergedCustomInstructions.welcomeMessage;
        console.log('Found welcome message from mergedCustomInstructions.welcomeMessage:', welcomeMessage);
      }
      else if (mergedAssistantOverrides?.firstMessage) {
        welcomeMessage = mergedAssistantOverrides.firstMessage;
        console.log('Found welcome message from mergedAssistantOverrides.firstMessage:', welcomeMessage);
      }
      else if (mergedCustomInstructions?.firstMessage) {
        welcomeMessage = mergedCustomInstructions.firstMessage;
        console.log('Found welcome message from mergedCustomInstructions.firstMessage:', welcomeMessage);
      }
      else if (mergedCustomInstructions?.initialMessage) {
        welcomeMessage = mergedCustomInstructions.initialMessage;
        console.log('Found welcome message from mergedCustomInstructions.initialMessage:', welcomeMessage);
      }

      // If we found a welcome message, add it to the UI
      // The assistant will speak this automatically due to firstMessageMode: "assistant-speaks-first"
      if (welcomeMessage) {
        console.log('Adding welcome message to UI:', welcomeMessage);

        // Add the welcome message to the UI only
        setMessages([{ type: 'assistant', text: welcomeMessage, animated: true }]);

        console.log('Welcome message should be spoken by the assistant automatically');

        // Log the voice settings for debugging
        console.log('Voice settings:', {
          voiceId: mergedCustomInstructions?.voiceId,
          voiceProvider: mergedCustomInstructions?.voiceProvider
        });
      } else {
        console.log('No custom welcome message found');
      }
    }
  }, [status, mergedCustomInstructions, mergedAssistantOverrides, vapi, forceDefaultAssistant]);

  // Track dossier data changes
  useEffect(() => {
    console.log("🔄 VapiCall component received dossier update:", dossierData);

    // Force a re-render of the dossier items
    const dossierItems = document.querySelector('.dossier-items');
    if (dossierItems) {
      // Add a temporary class to trigger a re-render animation
      dossierItems.classList.add('updating');
      setTimeout(() => {
        dossierItems.classList.remove('updating');
      }, 100);
    }
  }, [dossierData]);

  // Handle tool calls from Vapi
  useEffect(() => {
    if (messageHistory && messageHistory.length > 0) {
      const latestMessage = messageHistory[messageHistory.length - 1];

      // Check if this is a tool call message with web search results
      if (latestMessage && latestMessage.output && Array.isArray(latestMessage.output)) {
        // Look for web_search tool calls
        const webSearchCall = latestMessage.output.find(output =>
          output.function && (output.function.name === 'web_search' || output.function.name === 'firecrawl_search')
        );

        if (webSearchCall) {
          try {
            console.log('Web search tool call detected:', webSearchCall);

            // Parse the arguments
            const args = typeof webSearchCall.function.arguments === 'string'
              ? JSON.parse(webSearchCall.function.arguments)
              : webSearchCall.function.arguments;

            // Set search query and format
            setSearchQuery(args.query || '');
            setSearchType(args.format || 'simple');
            setIsSearching(true);

            // Process the results if available
            if (webSearchCall.response) {
              // The response should already be formatted correctly from our API
              const searchResponse = typeof webSearchCall.response === 'string'
                ? JSON.parse(webSearchCall.response)
                : webSearchCall.response;

              setSearchResults(searchResponse.results);
              setIsSearching(false);
            }
          } catch (error) {
            console.error('Error processing web search results:', error);
            setIsSearching(false);
          }
        }
      }
    }
  }, [messageHistory]);

  // Add CSS for the update animation
  useEffect(() => {
    // Add the CSS if it doesn't exist
    if (!document.querySelector('#dossier-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dossier-animation-style';
      style.textContent = `
        .dossier-items.updating {
          animation: dossier-update 0.3s ease-in-out;
        }
        @keyframes dossier-update {
          0% { opacity: 0.7; transform: scale(0.98); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }

    // Cleanup
    return () => {
      const style = document.querySelector('#dossier-animation-style');
      if (style) {
        style.remove();
      }
    };
  }, []);

  // Function wrapper for ending the call
  const handleEndCall = () => {
    console.log('End call button clicked, stopping call...');

    // Immediately set the global variable to indicate that the call is no longer active
    // This is critical to prevent race conditions during cleanup
    window.vapiCallActive = false;
    console.log('Set window.vapiCallActive to false immediately on end call button click');

    // Check if the assistant is still speaking
    if (assistantIsSpeaking) {
      console.log('Assistant is still speaking - showing confirmation before ending call');

      // Show a confirmation dialog
      const confirmEnd = window.confirm('The assistant is still speaking. Are you sure you want to end the call?');

      if (!confirmEnd) {
        console.log('User cancelled ending the call while assistant is speaking');
        // Reset the flag since we're not ending the call
        window.vapiCallActive = true;
        return;
      }

      console.log('User confirmed ending the call while assistant is speaking');
    }

    // Stop emissions monitoring if active
    if (emissionsMonitoring && typeof stopMonitoring === 'function') {
      console.log('Stopping emissions monitoring');
      stopMonitoring();
    }

    // Clean up any lingering Daily.co iframes
    try {
      const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
      if (existingIframes.length > 0) {
        console.log(`Found ${existingIframes.length} lingering Daily.co iframes. Removing during call end...`);
        existingIframes.forEach(iframe => {
          iframe.parentNode.removeChild(iframe);
        });
      }
    } catch (cleanupError) {
      console.warn("Error cleaning up iframes during call end:", cleanupError);
    }

    // Only call stopCall if it exists and is a function
    if (typeof stopCall === 'function') {
      try {
        console.log('Calling stopCall function from VapiCall component');
        stopCall();
        console.log('stopCall function called successfully');

        // Set local state to reflect call has ended
        setMessages(prev => [
          ...prev,
          {
            type: 'system',
            text: 'Call ended',
            timestamp: new Date().toISOString()
          }
        ]);
      } catch (error) {
        console.error('Error calling stopCall:', error);

        // If stopCall fails, try to manually call onEndCall as a fallback
        if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
          console.log('Manually calling onEndCall as fallback');
          onEndCall(dossierData);
          onEndCallCalled.current = true;
        }
      }
    } else {
      console.error('stopCall is not a function');

      // If stopCall is not available, try to manually call onEndCall as a fallback
      if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
        console.log('Manually calling onEndCall as fallback');
        onEndCall(dossierData);
        onEndCallCalled.current = true;
      }
    }

    // Double-check that the global flag is set to false
    window.vapiCallActive = false;
  };

  // Function to close search results
  const handleCloseSearchResults = () => {
    setSearchResults(null);
    setSearchQuery('');
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (!messageText.trim()) return;

    // Store the message text before clearing the input
    const messageToSend = messageText.trim();

    // Add user message to the UI with duplicate checking
    const userMessage = {
      type: 'user',
      text: messageToSend,
      animated: true,
      id: `manual-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };

    // Check for duplicates before adding the message
    setMessages(prev => {
      // Check if this exact message already exists in the recent messages
      const recentMessages = prev.slice(-5);
      const isDuplicate = recentMessages.some(msg =>
        msg.type === 'user' &&
        msg.text === messageToSend
      );

      if (isDuplicate) {
        console.log('Skipping duplicate manual message:', messageToSend);
        return prev;
      }

      console.log('Adding new manual message to UI:', messageToSend);
      return [...prev, userMessage];
    });

    // Clear the input immediately for better UX
    setMessageText('');

    // Send the message to Vapi if available
    if (vapi && status === CALL_STATUS.CONNECTED) {
      try {
        console.log("Sending message to Vapi:", messageToSend);

        // Try multiple message formats to ensure compatibility
        try {
          // Format 1: add-message type
          vapi.send({
            type: 'add-message',
            message: {
              role: 'user',
              content: messageToSend
            }
          });
          console.log("Message sent to Vapi using add-message format");
        } catch (sendError1) {
          console.warn("Error sending message using add-message format:", sendError1);

          // Format 2: direct message object
          try {
            vapi.send({
              role: 'user',
              content: messageToSend
            });
            console.log("Message sent to Vapi using direct message object format");
          } catch (sendError2) {
            console.warn("Error sending message using direct message object format:", sendError2);

            // Format 3: simple text
            try {
              vapi.send(messageToSend);
              console.log("Message sent to Vapi using simple text format");
            } catch (sendError3) {
              console.error("All message sending formats failed:", sendError3);
              throw new Error("Failed to send message in any format");
            }
          }
        }

        // Add a temporary "thinking" message that will be replaced by the actual response
        const tempId = `thinking-${Date.now()}`;
        setTimeout(() => {
          setMessages(prev => {
            // Only add thinking message if no assistant response has been received yet
            const hasRecentAssistantMessage = prev.slice(-3).some(msg =>
              msg.type === 'assistant' && !msg.isTemporary && Date.now() - new Date(msg.timestamp).getTime() < 2000
            );

            if (hasRecentAssistantMessage) {
              console.log('Recent assistant message found, skipping thinking message');
              return prev;
            }

            return [...prev, {
              type: 'assistant',
              text: "Thinking...",
              timestamp: new Date().toISOString(),
              animated: true,
              id: tempId,
              isTemporary: true
            }];
          });
        }, 500);

        // Set a timeout to update the temporary message if no response is received within 5 seconds
        setTimeout(() => {
          setMessages(prev => {
            // Check if the temporary message is still there
            const hasTemp = prev.some(msg => msg.id === tempId);
            if (hasTemp) {
              // Replace it with a more permanent message
              return prev.map(msg =>
                msg.id === tempId
                  ? { ...msg, text: "I'm processing your request. Please wait a moment...", isTemporary: false }
                  : msg
              );
            }
            return prev;
          });
        }, 5000);
      } catch (error) {
        console.error("Error sending message to Vapi:", error);
        // Add error message to UI
        setMessages(prev => [...prev, {
          type: 'assistant',
          text: "Sorry, I encountered an error processing your message. Please try again.",
          timestamp: new Date().toISOString(),
          id: `error-${Date.now()}`
        }]);

        // Try to reconnect if there's an error
        try {
          console.log("Attempting to reconnect to Vapi...");
          if (typeof startCall === 'function') {
            setTimeout(() => {
              startCall();
            }, 1000);
          }
        } catch (reconnectError) {
          console.error("Error reconnecting to Vapi:", reconnectError);
        }
      }
    } else {
      console.warn("Vapi not available or not connected. Message not sent.");
      // Add fallback response
      setMessages(prev => [...prev, {
        type: 'assistant',
        text: "I'm having trouble with the connection. Please wait a moment or try refreshing the page.",
        timestamp: new Date().toISOString(),
        id: `connection-error-${Date.now()}`
      }]);

      // Try to reconnect
      try {
        console.log("Attempting to reconnect to Vapi...");
        if (typeof startCall === 'function') {
          setTimeout(() => {
            startCall();
          }, 1000);
        }
      } catch (reconnectError) {
        console.error("Error reconnecting to Vapi:", reconnectError);
      }
    }
  };

  // Render message with enhanced styling
  const renderMessage = (message, index) => (
    <div key={index} className={`message ${message.type}`}>
      <div className="message-content">
        <p className="message-text">{message.text}</p>
        <div className="message-timestamp">
          {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : ''}
        </div>
      </div>
    </div>
  );

  // Render the component
  return (
    <>
      {/* Debug Panel - DISABLED */}
      {false && <CallDebugPanel visible={true} />}

      {/* Text input container completely outside the main component structure */}
      {status === CALL_STATUS.CONNECTED && (
        <div className="fixed-text-input-container">
          <div className="text-input-inner-container">
            <input
              type="text"
              className="text-input-field"
              placeholder="Type your message..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSendMessage();
                }
              }}
            />
            <button
              className="text-input-send-button"
              onClick={handleSendMessage}
              disabled={!messageText.trim()}
            >
              Send
            </button>
          </div>
        </div>
      )}

      <div className="vapi-call-container">
        {/* Speech Particles Visualization (visible when connected) */}
        {status === CALL_STATUS.CONNECTED && (() => {
          const primaryColor = attorneyData?.primary_color ||
                              processedConfig?.previewConfig?.primaryColor ||
                              '#4B74AA';
          const secondaryColor = attorneyData?.secondary_color ||
                                processedConfig?.previewConfig?.secondaryColor ||
                                '#2C3E50';

          console.log('🎨 VapiCall: Rendering SpeechParticles with colors:', {
            primaryColor,
            secondaryColor,
            attorneyData: attorneyData,
            processedConfig: processedConfig?.previewConfig
          });

          return (
            <SpeechParticles
              className="speech-particles"
              primaryColor={primaryColor}
              secondaryColor={secondaryColor}
            />
          );
        })()}

        {/* Globe Background (visible when connected) */}
        {status === CALL_STATUS.CONNECTED && (
          <div className="globe-background">
            <div className="globe-visualization">
              <div className="globe-sphere"></div>
              <div className="globe-highlight usa-highlight">
                <span className="location-label">USA begins highlighted all warm blue, as location updates zooms in</span>
              </div>
            </div>
          </div>
        )}

        {status === CALL_STATUS.IDLE && (
          <div className="call-status idle">
            <h2>Initializing...</h2>
            <div className="connecting-animation">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
            {subdomainConfig && subdomain !== 'default' && (
              <div className="subdomain-info">
                <p>Firm: {subdomainConfig.firmName}</p>
              </div>
            )}
          </div>
        )}

        {status === CALL_STATUS.CONNECTING && (
          <div className="call-status connecting">
            <h2>Connecting...</h2>
            <div className="connecting-animation">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
          </div>
        )}

        {status === CALL_STATUS.CONNECTED && (
          <div className="call-interface">
            {/* End Call button - positioned at top right corner */}
            <div className="end-call-container">
              <button
                className="end-call-button"
                onClick={handleEndCall}
                aria-label="End call">
                <img
                  src="/PRIMARY CLEAR.png"
                  alt="End"
                  className="end-call-logo"
                  onError={(e) => {
                    console.error('Error loading logo in end call button:', e);
                    e.target.src = '/PRIMARY CLEAR.png'; // Fallback to default logo
                  }}
                />
              </button>
              <span className="end-call-text">End Call</span>
            </div>

            <div className="three-column-layout">
              {/* Left Column - Dossier Component */}
              <div className="column left-column">
                <div className="dossier-component">
                  <h3 className="dossier-title">Case Information</h3>
                  {process.env.NODE_ENV === 'development' && (
                    <button
                      onClick={() => console.log('Current dossier data:', dossierData)}
                      style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        padding: '4px 8px',
                        fontSize: '12px',
                        background: 'rgba(0,0,0,0.5)',
                        color: '#fff',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '4px',
                        cursor: 'pointer'
                      }}
                    >
                      Debug
                    </button>
                  )}
                  <div className="dossier-items">
                    <div className="case-info-item status-item">
                      <span className="item-icon">📊</span>
                      <span className="item-label">STATUS</span>
                      <div className="item-value">
                        {dossierData?.status || "Awaiting client information..."}
                      </div>
                    </div>

                    {dossierData?.clientBackground && (
                      <div className="case-info-item">
                        <span className="item-icon">📁</span>
                        <span className="item-label">CLIENT BACKGROUND</span>
                        <div className="item-value">
                          {dossierData.clientBackground}
                        </div>
                      </div>
                    )}

                    {dossierData?.statementOfFacts && (
                      <div className="case-info-item">
                        <span className="item-icon">📝</span>
                        <span className="item-label">STATEMENT OF FACTS</span>
                        <div className="item-value">
                          {dossierData.statementOfFacts}
                        </div>
                      </div>
                    )}

                    {dossierData?.legalIssues && (
                      <div className="case-info-item">
                        <span className="item-icon">⚖️</span>
                        <span className="item-label">LEGAL ISSUES</span>
                        <div className="item-value">
                          {dossierData.legalIssues}
                        </div>
                      </div>
                    )}

                    {dossierData?.objectives && (
                      <div className="case-info-item">
                        <span className="item-icon">🎯</span>
                        <span className="item-label">OBJECTIVES</span>
                        <div className="item-value">
                          {dossierData.objectives}
                        </div>
                      </div>
                    )}

                    {dossierData?.location && (
                      <div className="case-info-item">
                        <span className="item-icon">📍</span>
                        <span className="item-label">JURISDICTION</span>
                        <div className="item-value">
                          {typeof dossierData.location === 'string'
                            ? dossierData.location
                            : dossierData.location.address || "Location not yet provided"}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Middle Column - For Map Visibility or Search Results */}
              <div className="column middle-column">
                {isSearching ? (
                  <div className="search-loading">
                    <div className="search-loading-spinner"></div>
                    <p>Searching for "{searchQuery}"...</p>
                  </div>
                ) : searchResults ? (
                  <FirecrawlResultsDisplay
                    results={searchResults}
                    query={searchQuery}
                    onClose={handleCloseSearchResults}
                  />
                ) : (
                  /* This space intentionally left empty to show the map when not searching */
                  <div className="map-placeholder"></div>
                )}
              </div>

              {/* Right Column - Conversation Area with enhanced styling */}
              <div className="column right-column">
                <div className="conversation-container">
                  {/* Voice indicators at the top of conversation */}
                  <div className="voice-indicators">
                    <div className="voice-status">
                      <div className={`speaking-indicator ${assistantIsSpeaking ? 'active' : ''}`}>
                        <span className="indicator-dot"></span>
                        {assistantIsSpeaking ? 'Assistant is speaking' : 'Assistant idle'}
                      </div>
                      {/* Volume level container - hidden when speech particles are active */}
                      <div className="volume-level-container" style={{ display: 'none' }}>
                        <div className="volume-label">Scout:</div>
                        <div className="volume-bars">
                          {[...Array(10)].map((_, i) => (
                            <div
                              key={i}
                              className={`volume-bar ${i < Math.floor(volumeLevel * 10) ? 'active' : ''}`}
                            ></div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="conversation-area">
                    {messages.map((message, index) => renderMessage(message, index))}

                    {/* Current transcript display */}
                    {currentTranscript && (
                      <div className="message user transcribing">
                        <div className="message-content">
                          <TextShimmerWave
                            text={currentTranscript}
                            rainbow={false}
                            colors={['#66c6ff', '#53ffed', '#fff78a', '#66c6ff']}
                            speed={2}
                            italic={true}
                          />
                          <div className="typing-indicator">...</div>
                        </div>
                      </div>
                    )}

                    <div ref={messagesEndRef} />
                  </div>
                </div>
              </div>
            </div>

            {/* Text input container is now outside the main component structure */}
          </div>
        )}

        {status === CALL_STATUS.ERROR && (
          <div className="call-status error">
            <h2>Error</h2>
            <div className="error-details">
              {errorMessage || "An unknown error occurred"}
            </div>
            <div className="error-actions">
              <button onClick={startCall} className="retry-button">
                Retry Call
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default withDevTools(VapiCall, {
  displayName: 'VapiCall',
  type: 'component',
  description: 'Handles voice call interaction with Vapi.ai'
});