import { renderHook, act } from '@testing-library/react-hooks';
import useVapiCall from '../useVapiCall';
import { vapiService } from '../../services/vapiService.jsx';
import { getAttorneyConfigAsync } from '../../config/attorneys';
import { CALL_STATUS } from '../../constants/vapiConstants';

// Mock the vapiService
jest.mock('../../services/vapiService', () => ({
  vapiService: {
    initialize: jest.fn(),
    setupEventListeners: jest.fn(),
    removeEventListeners: jest.fn(),
    startCall: jest.fn().mockResolvedValue(true),
    stopCall: jest.fn(),
    extractLocationData: jest.fn(),
    storeCallData: jest.fn()
  }
}));

// Mock the attorneys config
jest.mock('../../config/attorneys', () => ({
  getAttorneyConfigAsync: jest.fn().mockResolvedValue({
    firmName: 'Test Firm',
    vapiInstructions: 'Test instructions',
    vapiContext: 'Test context'
  })
}));

// Mock environment variables
const originalEnv = process.env;
beforeEach(() => {
  process.env = {
    ...originalEnv,
    VITE_VAPI_PUBLIC_KEY: 'test-api-key',
    MODE: 'development'
  };
});

afterEach(() => {
  process.env = originalEnv;
  jest.clearAllMocks();
});

// Mock MediaDevices getUserMedia
const mockUserMedia = jest.fn();
Object.defineProperty(global.navigator, 'mediaDevices', {
  value: {
    getUserMedia: mockUserMedia
  },
  writable: true
});

// Mock mock stream with stop method for tracks
const mockTrack = {
  stop: jest.fn()
};

describe('useVapiCall', () => {
  const mockOnEndCall = jest.fn();
  const mockSubdomain = 'testfirm';
  
  beforeEach(() => {
    // Mock successful microphone permission
    mockUserMedia.mockResolvedValue({
      getTracks: () => [mockTrack]
    });
    
    // Mock successful Vapi initialization
    const mockVapiInstance = {
      on: jest.fn(),
      off: jest.fn(),
      start: jest.fn().mockResolvedValue(true),
      stop: jest.fn()
    };
    vapiService.initialize.mockReturnValue(mockVapiInstance);
  });
  
  test('should initialize with default state', () => {
    const { result } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain, 
      onEndCall: mockOnEndCall 
    }));
    
    // Verify initial state
    expect(result.current.status).toBe(CALL_STATUS.IDLE);
    expect(result.current.dossierData).toEqual({});
    expect(result.current.errorMessage).toBeNull();
    expect(result.current.forceMockMode).toBe(false);
  });
  
  test('should load subdomain config on mount', async () => {
    const { waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Verify attorney config was fetched
    expect(getAttorneyConfigAsync).toHaveBeenCalledWith(mockSubdomain);
  });
  
  test('should initialize Vapi with API key', async () => {
    const { waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Verify Vapi was initialized
    expect(vapiService.initialize).toHaveBeenCalledWith('test-api-key', mockSubdomain);
    
    // Verify event listeners were set up
    expect(vapiService.setupEventListeners).toHaveBeenCalled();
  });
  
  test('should handle missing API key', async () => {
    // Remove the API key
    process.env.VITE_VAPI_PUBLIC_KEY = '';
    
    const { result, waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Verify error state
    expect(result.current.status).toBe(CALL_STATUS.ERROR);
    expect(result.current.errorMessage).toBe('Missing Vapi API key');
  });
  
  test('should start call when startCall is called', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Call the startCall function
    act(() => {
      result.current.startCall();
    });
    
    // Verify status is set to connecting
    expect(result.current.status).toBe(CALL_STATUS.CONNECTING);
    
    // Wait for microphone permission and call to start
    await waitForNextUpdate();
    
    // Verify microphone permission was requested
    expect(mockUserMedia).toHaveBeenCalledWith({ audio: true });
  });
  
  test('should update dossier data', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Call the updateDossierData function
    act(() => {
      result.current.updateDossierData({
        practiceArea: 'Family Law',
        location: { lat: 40.7128, lng: -74.0060, address: 'New York, NY' }
      });
    });
    
    // Verify dossier data was updated
    expect(result.current.dossierData).toEqual({
      practiceArea: 'Family Law',
      location: { lat: 40.7128, lng: -74.0060, address: 'New York, NY' }
    });
    
    // Update again with additional data
    act(() => {
      result.current.updateDossierData({
        issue: 'Divorce',
        urgency: 'High'
      });
    });
    
    // Verify data was merged correctly
    expect(result.current.dossierData).toEqual({
      practiceArea: 'Family Law',
      location: { lat: 40.7128, lng: -74.0060, address: 'New York, NY' },
      issue: 'Divorce',
      urgency: 'High'
    });
  });
  
  test('should handle special location merging', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // First set initial location
    act(() => {
      result.current.updateDossierData({
        location: { lat: 40.7128, lng: -74.0060 }
      });
    });
    
    // Then update with address but without coordinates
    act(() => {
      result.current.updateDossierData({
        location: { address: 'New York, NY' }
      });
    });
    
    // Verify location was merged correctly
    expect(result.current.dossierData.location).toEqual({
      lat: 40.7128,
      lng: -74.0060,
      address: 'New York, NY'
    });
  });
  
  test('should stop call and call onEndCall', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useVapiCall({ 
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall 
    }));
    
    await waitForNextUpdate();
    
    // Add some dossier data first
    act(() => {
      result.current.updateDossierData({
        practiceArea: 'Family Law'
      });
    });
    
    // Call the stopCall function
    act(() => {
      result.current.stopCall();
    });
    
    // Verify Vapi call was stopped
    expect(vapiService.stopCall).toHaveBeenCalled();
    
    // Verify status was set to idle
    expect(result.current.status).toBe(CALL_STATUS.IDLE);
    
    // Verify onEndCall was called with dossier data
    expect(mockOnEndCall).toHaveBeenCalledWith({ practiceArea: 'Family Law' });
  });
  
  test('should handle mock mode', async () => {
    // Render hook with forceMockMode initial state
    const { result } = renderHook(() => useVapiCall({
      subdomain: mockSubdomain,
      onEndCall: mockOnEndCall,
      initialMockMode: true
    }));
    
    // Verify initial state with mock mode
    expect(result.current.forceMockMode).toBe(true);
    
    // Wait for mock data to be set
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verify status was set to connected
    expect(result.current.status).toBe(CALL_STATUS.CONNECTED);
  });
}); 