import React from 'react';
import './CrmViews.css';
import ConsultationTableView from './ConsultationTableView';
import ConsultationCardView from './ConsultationCardView';
import ConsultationMapView from './ConsultationMapView';

const CrmViewSelector = ({ viewMode, onViewChange }) => {
  return (
    <div className="crm-panel">
      <div className="crm-content">
        <div className="crm-header">
          <h2>Client Consultations</h2>
          <div className="view-toggle">
            <button 
              className={`view-toggle-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => onViewChange('table')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="8" y1="6" x2="21" y2="6"></line>
                <line x1="8" y1="12" x2="21" y2="12"></line>
                <line x1="8" y1="18" x2="21" y2="18"></line>
                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                <line x1="3" y1="18" x2="3.01" y2="18"></line>
              </svg> Table
            </button>
            <button 
              className={`view-toggle-btn ${viewMode === 'card' ? 'active' : ''}`}
              onClick={() => onViewChange('card')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg> Cards
            </button>
            <button 
              className={`view-toggle-btn ${viewMode === 'map' ? 'active' : ''}`}
              onClick={() => onViewChange('map')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon>
                <line x1="8" y1="2" x2="8" y2="18"></line>
                <line x1="16" y1="6" x2="16" y2="22"></line>
              </svg> Map
            </button>
          </div>
        </div>
        
        <div className="crm-filters">
          <input 
            type="text" 
            className="search-input" 
            placeholder="Search consultations..." 
          />
          <select className="filter-select">
            <option value="all">All Consultations</option>
            <option value="recent">Recent (30 days)</option>
            <option value="follow-up">Needs Follow-up</option>
          </select>
        </div>
        
        {viewMode === 'table' && <ConsultationTableView />}
        {viewMode === 'card' && <ConsultationCardView />}
        {viewMode === 'map' && <ConsultationMapView />}
      </div>
    </div>
  );
};

export default CrmViewSelector;
