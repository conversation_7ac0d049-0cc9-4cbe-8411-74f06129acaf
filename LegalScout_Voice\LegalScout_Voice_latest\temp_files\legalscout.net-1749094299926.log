 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:30 Environment variables: Object
supabase.js:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:47 Supabase Key configured: eyJhb...K4cRU
supabase.js:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:101 Supabase client initialized successfully with proper headers
supabase.js:104 Testing Supabase connection...
supabase.js:150 Running in production mode
supabase.js:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized Object
environmentVerifier.js:58 Environment Variable Verification
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: url.includes is not a function
_connect @ vapiMcpService.js:322
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
It @ supabaseConfigVerifier.js:83
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
supabase.js:115 Supabase query error: No API key found in request
(anonymous) @ supabase.js:115
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
AuthCallback.jsx:105 Auth callback error: Object
(anonymous) @ AuthCallback.jsx:105
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com&order=updated_at.desc:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
vapiAssistantUtils.js:157 Error fetching attorney by email: Object
ho @ vapiAssistantUtils.js:157
vapiAssistantUtils.js:205 Error in findAttorneyByEmailAndEnsureVapiAssistant: Error: Supabase fetch error: No API key found in request
    at ho (vapiAssistantUtils.js:158:13)
    at async w (DashboardNew.jsx:296:30)
ho @ vapiAssistantUtils.js:205
DashboardNew.jsx:309 [DashboardNew] Error finding attorney by email: Error: Supabase fetch error: No API key found in request
    at ho (vapiAssistantUtils.js:158:13)
    at async w (DashboardNew.jsx:296:30)
w @ DashboardNew.jsx:309
DashboardNew.jsx:336 [DashboardNew] No attorney profile found for this user or loading failed.
w @ DashboardNew.jsx:336
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com&order=updated_at.desc:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
vapiAssistantUtils.js:157 Error fetching attorney by email: Object
ho @ vapiAssistantUtils.js:157
vapiAssistantUtils.js:205 Error in findAttorneyByEmailAndEnsureVapiAssistant: Error: Supabase fetch error: No API key found in request
    at ho (vapiAssistantUtils.js:158:13)
    at async w (DashboardNew.jsx:296:30)
ho @ vapiAssistantUtils.js:205
DashboardNew.jsx:309 [DashboardNew] Error finding attorney by email: Error: Supabase fetch error: No API key found in request
    at ho (vapiAssistantUtils.js:158:13)
    at async w (DashboardNew.jsx:296:30)
w @ DashboardNew.jsx:309
DashboardNew.jsx:336 [DashboardNew] No attorney profile found for this user or loading failed.
w @ DashboardNew.jsx:336
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com:1 
            
            
           Failed to load resource: the server responded with a status of 401 ()
 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ pages-ef9138b8.js:215
