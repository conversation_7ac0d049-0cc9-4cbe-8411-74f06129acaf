export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const response = await fetch('https://api.makecomputer.io/v1/actions/composio/check-active-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MCP_API_KEY}`
      },
      body: JSON.stringify(req.body)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to check connection');
    }

    return res.status(200).json({ hasActiveConnection: data.active });
  } catch (error) {
    console.error('Connection check error:', error);
    return res.status(500).json({ error: 'Failed to check connection status' });
  }
} 