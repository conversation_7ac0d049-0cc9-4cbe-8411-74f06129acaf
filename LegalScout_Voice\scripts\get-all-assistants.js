/**
 * Get ALL Vapi Assistants with Pagination
 * This script fetches all assistants using the Vapi API directly with proper pagination
 */

const fetch = require('node-fetch');

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

async function makeVapiRequest(endpoint, method = 'GET') {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json'
    }
  };

  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

async function getAllAssistants() {
  console.log('📋 Fetching ALL assistants from Vapi...');
  
  let allAssistants = [];
  let hasMore = true;
  let batchCount = 0;
  const limit = 1000; // Maximum allowed by API
  
  try {
    while (hasMore) {
      batchCount++;
      console.log(`   Fetching batch ${batchCount} (limit: ${limit})...`);
      
      // For the first request, we don't have a cursor, so we use createdAt filtering
      let endpoint = `/assistant?limit=${limit}`;
      
      // If we have assistants already, use the oldest one's createdAt as a filter
      if (allAssistants.length > 0) {
        const oldestAssistant = allAssistants[allAssistants.length - 1];
        const createdAtFilter = encodeURIComponent(oldestAssistant.createdAt);
        endpoint = `/assistant?limit=${limit}&createdAtLt=${createdAtFilter}`;
      }
      
      const batch = await makeVapiRequest(endpoint);
      
      console.log(`   Retrieved ${batch.length} assistants in batch ${batchCount}`);
      
      if (batch.length === 0) {
        hasMore = false;
      } else {
        allAssistants = allAssistants.concat(batch);
        
        // If we got fewer than the limit, we've reached the end
        if (batch.length < limit) {
          hasMore = false;
        }
      }
      
      // Small delay between requests to be nice to the API
      if (hasMore) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    console.log(`\n✅ Successfully fetched ${allAssistants.length} total assistants in ${batchCount} batches`);
    return allAssistants;
    
  } catch (error) {
    console.error('❌ Error fetching assistants:', error);
    throw error;
  }
}

function analyzeAssistants(assistants) {
  console.log('\n🔍 Analyzing all assistants...\n');
  
  // Group by name
  const byName = {};
  const byDate = {};
  
  assistants.forEach(assistant => {
    // Group by name
    if (!byName[assistant.name]) {
      byName[assistant.name] = [];
    }
    byName[assistant.name].push(assistant);
    
    // Group by date
    const date = assistant.createdAt.split('T')[0]; // Get just the date part
    if (!byDate[date]) {
      byDate[date] = 0;
    }
    byDate[date]++;
  });
  
  console.log('📊 COMPLETE ASSISTANT TALLY:');
  console.log(`   Total Assistants: ${assistants.length}`);
  console.log(`   Unique Names: ${Object.keys(byName).length}`);
  
  console.log('\n📋 Breakdown by Name:');
  const sortedNames = Object.entries(byName).sort((a, b) => b[1].length - a[1].length);
  
  sortedNames.forEach(([name, assistantList]) => {
    console.log(`   "${name}": ${assistantList.length} assistants`);
    
    // Show date range for this name
    const dates = assistantList.map(a => a.createdAt).sort();
    const firstDate = dates[0].split('T')[0];
    const lastDate = dates[dates.length - 1].split('T')[0];
    
    if (firstDate === lastDate) {
      console.log(`     Created on: ${firstDate}`);
    } else {
      console.log(`     Created between: ${firstDate} and ${lastDate}`);
    }
    
    // Show a few example IDs
    console.log(`     Example IDs: ${assistantList.slice(0, 3).map(a => a.id).join(', ')}`);
    if (assistantList.length > 3) {
      console.log(`     ... and ${assistantList.length - 3} more`);
    }
    console.log('');
  });
  
  console.log('📅 Creation Timeline:');
  const sortedDates = Object.entries(byDate).sort();
  sortedDates.forEach(([date, count]) => {
    console.log(`   ${date}: ${count} assistants created`);
  });
  
  // Check for your specific assistant
  const yourAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  const yourAssistant = assistants.find(a => a.id === yourAssistantId);
  
  console.log('\n🎯 Your Specific Assistant:');
  if (yourAssistant) {
    console.log(`   ✅ FOUND: ${yourAssistant.id}`);
    console.log(`   Name: ${yourAssistant.name}`);
    console.log(`   Created: ${yourAssistant.createdAt}`);
  } else {
    console.log(`   ❌ NOT FOUND: ${yourAssistantId}`);
    console.log('   This assistant may have been deleted or the ID is incorrect');
  }
  
  // Calculate cleanup potential
  const duplicates = sortedNames.filter(([name, list]) => list.length > 1);
  const totalDuplicates = duplicates.reduce((sum, [name, list]) => sum + (list.length - 1), 0);
  
  console.log('\n🧹 Cleanup Potential:');
  console.log(`   Names with duplicates: ${duplicates.length}`);
  console.log(`   Total duplicate assistants: ${totalDuplicates}`);
  console.log(`   Assistants that could be deleted: ${totalDuplicates}`);
  
  return {
    total: assistants.length,
    byName,
    byDate,
    yourAssistant,
    duplicates: totalDuplicates
  };
}

async function main() {
  console.log('🚀 Complete Vapi Assistant Analysis\n');
  
  try {
    const allAssistants = await getAllAssistants();
    const analysis = analyzeAssistants(allAssistants);
    
    console.log('\n💡 Next Steps:');
    if (analysis.duplicates > 0) {
      console.log(`   - You can clean up ${analysis.duplicates} duplicate assistants`);
      console.log('   - Use the bulk cleanup script to remove duplicates');
    }
    
    if (!analysis.yourAssistant) {
      console.log('   - Your specific assistant was not found - you may need to create a new one');
    }
    
    console.log('\n📄 Full assistant list saved to: all-assistants.json');
    
    // Save full list to file for reference
    const fs = require('fs');
    fs.writeFileSync('all-assistants.json', JSON.stringify(allAssistants, null, 2));
    
  } catch (error) {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  }
}

main();
