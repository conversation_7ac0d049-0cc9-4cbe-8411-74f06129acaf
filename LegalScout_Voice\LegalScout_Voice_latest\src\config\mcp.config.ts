import { MCPConfig } from '../types/mcp';

export const mcpConfig: MCPConfig = {
  email: {
    gmail: {
      enabled: true,
      templates: {
        consultationSummary: {
          subject: 'Your LegalScout Consultation Summary',
          includeAttachments: true,
          includePDF: true,
        },
        attorneyNotification: {
          subject: 'New Legal Consultation Request',
          includeClientInfo: true,
          includeDossier: true,
        },
        followUp: {
          subject: 'Follow-up: Your Legal Consultation',
          includeCalendlyLink: true,
        },
      },
    },
    outlook: {
      enabled: true,
      templates: {
        consultationSummary: {
          subject: 'Your LegalScout Consultation Summary',
          includeAttachments: true,
          includePDF: true,
        },
        attorneyNotification: {
          subject: 'New Legal Consultation Request',
          includeClientInfo: true,
          includeDossier: true,
        },
        followUp: {
          subject: 'Follow-up: Your Legal Consultation',
          includeCalendlyLink: true,
        },
      },
    },
  },
  search: {
    serpApi: {
      enabled: true,
      services: {
        maps: true,
        news: true,
        scholar: false,
        images: true,
      },
      rateLimit: 100,
    },
  },
  calendar: {
    calendly: {
      enabled: true,
      eventTypes: {
        initialConsultation: {
          duration: 30,
          name: 'Initial Legal Consultation',
        },
        followUp: {
          duration: 60,
          name: 'Follow-up Consultation',
        },
      },
    },
  },
  ai: {
    perplexity: {
      enabled: true,
      features: {
        legalResearch: true,
        caseLaw: true,
        jurisdictionInfo: true,
      },
      model: 'claude-3-sonnet',
      temperature: 0.7,
    },
  },
  document: {
    textToPDF: {
      enabled: true,
      templates: {
        consultationSummary: true,
        legalDocuments: true,
        agreements: true,
      },
      options: {
        encryption: true,
        watermark: true,
        signature: true,
      },
    },
  },
  voice: {
    vapi: {
      enabled: true,
      apiKey: '310f0d43-27c2-47a5-a76d-e55171d024f7',
      features: {
        assistantManagement: true,
        callManagement: true,
        phoneNumberManagement: true,
      },
    },
  },
};