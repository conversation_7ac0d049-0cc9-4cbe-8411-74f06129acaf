home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
index-36955b16.js:48 Attaching Supabase client to window.supabase
index-36955b16.js:60 [VapiLoader] Starting Vapi SDK loading process
index-36955b16.js:60 [Vapi<PERSON>oader] Attempting to import @vapi-ai/web package
index-36955b16.js:147 [VapiMcpService] Created clean fetch from iframe
index-36955b16.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at cfe.autoInitializeFromLocalStorage (index-36955b16.js:459:27820)
    at new cfe (index-36955b16.js:459:27600)
    at index-36955b16.js:459:56692
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at cfe.autoInitializeFromLocalStorage (index-36955b16.js:459:27820)
    at new cfe (index-36955b16.js:459:27600)
    at index-36955b16.js:459:56692
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 🔐 [AuthContext] Unexpected error checking auth: Error: Supabase client not initialized. Use getSupabaseClient() first.
    at Object.get (index-36955b16.js:48:72984)
    at index-36955b16.js:48:76779
    at index-36955b16.js:48:78400
    at m_ (index-36955b16.js:40:24270)
    at Id (index-36955b16.js:40:42393)
    at index-36955b16.js:40:40710
    at D (index-36955b16.js:25:1585)
    at MessagePort.j (index-36955b16.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-36955b16.js:48
(anonymous) @ index-36955b16.js:48
m_ @ index-36955b16.js:40
Id @ index-36955b16.js:40
(anonymous) @ index-36955b16.js:40
D @ index-36955b16.js:25
j @ index-36955b16.js:25
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at index-36955b16.js:48:78438
    at index-36955b16.js:48:79824
    at m_ (index-36955b16.js:40:24270)
    at Id (index-36955b16.js:40:42393)
    at index-36955b16.js:40:40710
    at D (index-36955b16.js:25:1585)
    at MessagePort.j (index-36955b16.js:25:1948)
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
(anonymous) @ index-36955b16.js:48
(anonymous) @ index-36955b16.js:48
m_ @ index-36955b16.js:40
Id @ index-36955b16.js:40
(anonymous) @ index-36955b16.js:40
D @ index-36955b16.js:25
j @ index-36955b16.js:25
index-36955b16.js:48 Failed to set up auth listener: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at index-36955b16.js:48:78438
    at index-36955b16.js:48:79824
    at m_ (index-36955b16.js:40:24270)
    at Id (index-36955b16.js:40:42393)
    at index-36955b16.js:40:40710
    at D (index-36955b16.js:25:1585)
    at MessagePort.j (index-36955b16.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-36955b16.js:48
await in (anonymous)
(anonymous) @ index-36955b16.js:48
m_ @ index-36955b16.js:40
Id @ index-36955b16.js:40
(anonymous) @ index-36955b16.js:40
D @ index-36955b16.js:25
j @ index-36955b16.js:25
index.ts:5 Loaded contentScript
home:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.loadAttorneyById (index-36955b16.js:459:34723)
    at index-36955b16.js:459:28060
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
loadAttorneyById @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error loading attorney by id: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.loadAttorneyById (index-36955b16.js:459:34723)
    at index-36955b16.js:459:28060
overrideMethod @ hook.js:608
loadAttorneyById @ index-36955b16.js:459
await in loadAttorneyById
(anonymous) @ index-36955b16.js:459
setTimeout
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.loadAttorneyById (index-36955b16.js:459:34723)
    at index-36955b16.js:459:28060
overrideMethod @ hook.js:608
(anonymous) @ index-36955b16.js:459
setTimeout
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
ActiveCheckHelper.ts:21 received intentional event
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at gL (index-36955b16.js:48:73483)
    at w (index-36955b16.js:405:41536)
    at Object.XV (index-36955b16.js:37:9864)
    at QV (index-36955b16.js:37:10018)
    at e7 (index-36955b16.js:37:10075)
    at XC (index-36955b16.js:37:31482)
    at _6 (index-36955b16.js:37:31899)
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
gL @ index-36955b16.js:48
w @ index-36955b16.js:405
XV @ index-36955b16.js:37
QV @ index-36955b16.js:37
e7 @ index-36955b16.js:37
XC @ index-36955b16.js:37
_6 @ index-36955b16.js:37
(anonymous) @ index-36955b16.js:37
A2 @ index-36955b16.js:40
$P @ index-36955b16.js:37
dy @ index-36955b16.js:37
t2 @ index-36955b16.js:37
g7 @ index-36955b16.js:37
index-36955b16.js:48 💥 [Auth] Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at gL (index-36955b16.js:48:73483)
    at w (index-36955b16.js:405:41536)
    at Object.XV (index-36955b16.js:37:9864)
    at QV (index-36955b16.js:37:10018)
    at e7 (index-36955b16.js:37:10075)
    at XC (index-36955b16.js:37:31482)
    at _6 (index-36955b16.js:37:31899)
overrideMethod @ hook.js:608
gL @ index-36955b16.js:48
await in gL
w @ index-36955b16.js:405
XV @ index-36955b16.js:37
QV @ index-36955b16.js:37
e7 @ index-36955b16.js:37
XC @ index-36955b16.js:37
_6 @ index-36955b16.js:37
(anonymous) @ index-36955b16.js:37
A2 @ index-36955b16.js:40
$P @ index-36955b16.js:37
dy @ index-36955b16.js:37
t2 @ index-36955b16.js:37
g7 @ index-36955b16.js:37
index-36955b16.js:405 Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at gL (index-36955b16.js:48:73483)
    at w (index-36955b16.js:405:41536)
    at Object.XV (index-36955b16.js:37:9864)
    at QV (index-36955b16.js:37:10018)
    at e7 (index-36955b16.js:37:10075)
    at XC (index-36955b16.js:37:31482)
    at _6 (index-36955b16.js:37:31899)
overrideMethod @ hook.js:608
w @ index-36955b16.js:405
await in w
XV @ index-36955b16.js:37
QV @ index-36955b16.js:37
e7 @ index-36955b16.js:37
XC @ index-36955b16.js:37
_6 @ index-36955b16.js:37
(anonymous) @ index-36955b16.js:37
A2 @ index-36955b16.js:40
$P @ index-36955b16.js:37
dy @ index-36955b16.js:37
t2 @ index-36955b16.js:37
g7 @ index-36955b16.js:37
ActiveCheckHelper.ts:8 updating page active status
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-36955b16.js:459
cfe @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:48 Failed to create Supabase client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
Sn @ index-36955b16.js:48
setupRealtimeSubscription @ index-36955b16.js:459
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
index-36955b16.js:459 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-36955b16.js:48:69816)
    at dG (index-36955b16.js:48:72276)
    at Sn (index-36955b16.js:48:72808)
    at cfe.setupRealtimeSubscription (index-36955b16.js:459:35095)
    at index-36955b16.js:459:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
setTimeout
setupRealtimeSubscription @ index-36955b16.js:459
await in setupRealtimeSubscription
(anonymous) @ index-36955b16.js:459
