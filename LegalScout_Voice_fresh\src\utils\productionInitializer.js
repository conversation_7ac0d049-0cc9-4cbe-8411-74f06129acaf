/**
 * Production Initializer
 * 
 * This utility handles the initialization of the application in production,
 * ensuring all critical systems are properly set up and configured.
 */

import { initEnvironmentVerification } from './environmentVerifier.js';
import { initProductionRouting } from './productionRoutingFix.js';

// Production-safe module loading
const safeImport = async (modulePath) => {
  try {
    return await import(modulePath);
  } catch (error) {
    console.warn(`[ProductionInitializer] Failed to import ${modulePath}:`, error.message);
    return null;
  }
};

// Initialize global polyfills for production
const initializePolyfills = () => {
  console.log('[ProductionInitializer] Initializing production polyfills...');

  // Ensure global objects exist
  if (typeof window !== 'undefined') {
    // Process polyfill for production
    if (!window.process) {
      window.process = {
        env: { NODE_ENV: 'production' },
        browser: true,
        version: '',
        versions: { node: '' }
      };
    }

    // Global flag for environment
    window.__ENVIRONMENT_MODE__ = 'production';
    window.__IS_PRODUCTION__ = true;

    // Ensure import.meta exists for compatibility
    if (!window.import) {
      window.import = {};
    }
    if (!window.import.meta) {
      window.import.meta = {
        env: {
          MODE: 'production',
          PROD: true,
          DEV: false
        }
      };
    }

    console.log('✅ [ProductionInitializer] Production polyfills initialized');
  }
};

// Initialize error handling
const initializeErrorHandling = () => {
  console.log('[ProductionInitializer] Setting up production error handling...');

  if (typeof window !== 'undefined') {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('[ProductionInitializer] Global error caught:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });

      // Prevent the error from breaking the app
      event.preventDefault();
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('[ProductionInitializer] Unhandled promise rejection:', event.reason);
      
      // Prevent the error from breaking the app
      event.preventDefault();
    });

    console.log('✅ [ProductionInitializer] Error handling initialized');
  }
};

// Initialize critical services
const initializeCriticalServices = async () => {
  console.log('[ProductionInitializer] Initializing critical services...');

  try {
    // Initialize environment verification first
    const envResults = initEnvironmentVerification();
    if (!envResults || envResults.missingVariables.length > 0) {
      console.warn('[ProductionInitializer] Environment verification issues detected');
    }

    // Initialize routing
    initProductionRouting();

    // Initialize Supabase if available
    const supabaseModule = await safeImport('../config/supabase.js');
    if (supabaseModule && supabaseModule.initializeSupabase) {
      await supabaseModule.initializeSupabase();
    }

    // Initialize Vapi if available
    const vapiModule = await safeImport('../config/vapiConfig.js');
    if (vapiModule && vapiModule.initializeVapi) {
      await vapiModule.initializeVapi();
    }

    console.log('✅ [ProductionInitializer] Critical services initialized');
  } catch (error) {
    console.error('🚨 [ProductionInitializer] Error initializing critical services:', error);
  }
};

// Check if we're in production
const isProduction = () => {
  if (typeof window === 'undefined') return true;
  
  const hostname = window.location.hostname;
  return !hostname.includes('localhost') && !hostname.includes('127.0.0.1');
};

// Main production initialization function
export const initializeProduction = async () => {
  if (!isProduction()) {
    console.log('[ProductionInitializer] Development environment detected, skipping production initialization');
    return;
  }

  console.log('🚀 [ProductionInitializer] Starting production initialization...');

  try {
    // Step 1: Initialize polyfills
    initializePolyfills();

    // Step 2: Initialize error handling
    initializeErrorHandling();

    // Step 3: Initialize critical services
    await initializeCriticalServices();

    // Step 4: Mark initialization as complete
    if (typeof window !== 'undefined') {
      window.__PRODUCTION_INITIALIZED__ = true;
      window.__INITIALIZATION_TIME__ = new Date().toISOString();
    }

    console.log('✅ [ProductionInitializer] Production initialization completed successfully');
  } catch (error) {
    console.error('🚨 [ProductionInitializer] FATAL ERROR during production initialization:', error);
    
    // Emergency fallback
    if (typeof window !== 'undefined') {
      window.__PRODUCTION_INITIALIZATION_FAILED__ = true;
      window.__INITIALIZATION_ERROR__ = error.message;
    }
  }
};

// Auto-initialize if in production
if (isProduction() && typeof window !== 'undefined') {
  // Initialize as soon as possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProduction);
  } else {
    initializeProduction();
  }
}

// Export utilities
export { isProduction, initializePolyfills, initializeErrorHandling, initializeCriticalServices };

export default {
  initializeProduction,
  isProduction,
  initializePolyfills,
  initializeErrorHandling,
  initializeCriticalServices
};
