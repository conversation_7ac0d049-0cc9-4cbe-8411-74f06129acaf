<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subtle Notifications Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .success-btn {
            background: #10B981;
            color: white;
        }
        
        .error-btn {
            background: #EF4444;
            color: white;
        }
        
        .warning-btn {
            background: #F59E0B;
            color: white;
        }
        
        .info-btn {
            background: #3B82F6;
            color: white;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .demo-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        /* Include the notification styles directly */
        .subtle-notification {
            position: fixed;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-family: inherit;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 400px;
            min-width: 300px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(100%);
            opacity: 0;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: auto;
            z-index: 10000;
        }
        
        .subtle-notification-success {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
        }
        
        .subtle-notification-error {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            color: white;
        }
        
        .subtle-notification-warning {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
        }
        
        .subtle-notification-info {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            color: white;
        }
        
        .notification-top-right {
            top: 20px;
            right: 20px;
        }
        
        .notification-top-center {
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .notification-enter {
            transform: translateX(0) !important;
            opacity: 1 !important;
        }
        
        .notification-top-center.notification-enter {
            transform: translateX(-50%) !important;
        }
        
        .notification-exit {
            transform: translateX(100%) !important;
            opacity: 0 !important;
        }
        
        .notification-top-center.notification-exit {
            transform: translateX(-50%) translateY(-100%) !important;
        }
        
        .notification-icon {
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .notification-message {
            flex: 1;
            line-height: 1.4;
        }
        
        .notification-close {
            background: none;
            border: none;
            color: inherit;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-left: auto;
            opacity: 0.7;
            transition: opacity 0.2s ease;
            flex-shrink: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .notification-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .subtle-notification:hover {
            transform: scale(1.02) !important;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
        }
        
        .notification-top-center:hover {
            transform: translateX(-50%) scale(1.02) !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔔 Subtle Notifications Test</h1>
        <p>Test the new subtle notification system that replaces intrusive alert() popups.</p>
        
        <div class="button-group">
            <button class="success-btn" onclick="showSuccess()">
                ✅ Success Notification
            </button>
            <button class="error-btn" onclick="showError()">
                ❌ Error Notification
            </button>
            <button class="warning-btn" onclick="showWarning()">
                ⚠️ Warning Notification
            </button>
            <button class="info-btn" onclick="showInfo()">
                ℹ️ Info Notification
            </button>
        </div>
        
        <div class="demo-section">
            <h3>🎯 Authentication Success Demo</h3>
            <button class="success-btn" onclick="showAuthSuccess()">
                Simulate "LegalScout assistant configured successfully!"
            </button>
        </div>
        
        <div class="demo-section">
            <h3>📍 Position Tests</h3>
            <div class="button-group">
                <button class="info-btn" onclick="showTopRight()">Top Right</button>
                <button class="info-btn" onclick="showTopCenter()">Top Center</button>
            </div>
        </div>
    </div>

    <script>
        // Simple notification manager for testing
        class TestNotificationManager {
            constructor() {
                this.container = null;
                this.createContainer();
            }
            
            createContainer() {
                this.container = document.createElement('div');
                this.container.id = 'test-notifications-container';
                this.container.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    pointer-events: none;
                    z-index: 10000;
                `;
                document.body.appendChild(this.container);
            }
            
            show(message, options = {}) {
                const {
                    type = 'success',
                    duration = 5000,
                    position = 'top-right'
                } = options;
                
                const notification = document.createElement('div');
                notification.className = `subtle-notification subtle-notification-${type} notification-${position}`;
                notification.style.pointerEvents = 'auto';
                
                notification.innerHTML = `
                    <span class="notification-icon">${this.getIcon(type)}</span>
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" aria-label="Close notification">×</button>
                `;
                
                this.container.appendChild(notification);
                
                // Animate in
                setTimeout(() => {
                    notification.classList.add('notification-enter');
                }, 100);
                
                // Auto-remove
                const removeTimer = setTimeout(() => {
                    this.remove(notification);
                }, duration);
                
                // Close button handler
                const closeBtn = notification.querySelector('.notification-close');
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    clearTimeout(removeTimer);
                    this.remove(notification);
                });
                
                // Click to dismiss
                notification.addEventListener('click', () => {
                    clearTimeout(removeTimer);
                    this.remove(notification);
                });
            }
            
            remove(notification) {
                notification.classList.add('notification-exit');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
            
            getIcon(type) {
                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };
                return icons[type] || '✅';
            }
            
            success(message, options = {}) {
                return this.show(message, { ...options, type: 'success' });
            }
            
            error(message, options = {}) {
                return this.show(message, { ...options, type: 'error' });
            }
            
            warning(message, options = {}) {
                return this.show(message, { ...options, type: 'warning' });
            }
            
            info(message, options = {}) {
                return this.show(message, { ...options, type: 'info' });
            }
        }
        
        const testNotifications = new TestNotificationManager();
        
        // Test functions
        function showSuccess() {
            testNotifications.success('Operation completed successfully!');
        }
        
        function showError() {
            testNotifications.error('Something went wrong. Please try again.');
        }
        
        function showWarning() {
            testNotifications.warning('Please check your input and try again.');
        }
        
        function showInfo() {
            testNotifications.info('Here\'s some helpful information for you.');
        }
        
        function showAuthSuccess() {
            testNotifications.success(
                'LegalScout assistant configured successfully! Your subdomain is ready.',
                { 
                    duration: 4000,
                    position: 'top-center'
                }
            );
        }
        
        function showTopRight() {
            testNotifications.info('This notification appears in the top-right corner.', {
                position: 'top-right'
            });
        }
        
        function showTopCenter() {
            testNotifications.info('This notification appears in the top-center.', {
                position: 'top-center'
            });
        }
        
        // Global functions for compatibility
        window.showSuccessNotification = (message, options) => testNotifications.success(message, options);
        window.showErrorNotification = (message, options) => testNotifications.error(message, options);
        window.showWarningNotification = (message, options) => testNotifications.warning(message, options);
        window.showInfoNotification = (message, options) => testNotifications.info(message, options);
    </script>
</body>
</html>
