import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { KnowledgeBase } from './KnowledgeBase';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeSanitize from 'rehype-sanitize';
import { setupIframePingPong, sendMessageToParent } from '../../utils/iframeUtils';
import ErrorBoundary from './ErrorBoundary';
import { DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants';

// Type definition for environment variables
interface ImportMetaEnv {
  VITE_VAPI_PUBLIC_KEY?: string;
  [key: string]: string | undefined;
}

// Extend ImportMeta interface to include env
declare global {
  interface ImportMeta {
    env: ImportMetaEnv;
  }
}

interface PreviewInterfaceProps {
  firmName?: string;
  attorneyName?: string;
  practiceAreas?: string[];
  primaryColor?: string;
  secondaryColor?: string;
  buttonColor?: string; // Add buttonColor property
  logoUrl?: string;
  welcomeMessage?: string;
  informationGathering?: string;
  state?: string;
  backgroundOpacity?: number;
  backgroundColor?: string;
  practiceDescription?: string;
  firmNameAnimation?: string;
  buttonText?: string;
  buttonOpacity?: number;
  practiceAreaBackgroundOpacity?: number;
  textBackgroundColor?: string;
  theme?: string;
}

export const PreviewInterface = ({
  firmName = 'Your Law Firm',
  attorneyName = 'Your Name',
  practiceAreas = [],
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  buttonColor = '#3498db',
  logoUrl,
  welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",
  state = '',
  backgroundOpacity = 0.9,
  backgroundColor = '#1a1a1a',
  practiceDescription = "Your AI legal assistant is ready to help",
  firmNameAnimation,
  buttonText,
  buttonOpacity = 1,
  practiceAreaBackgroundOpacity = 0.1,
  textBackgroundColor = '#634C38',
  theme = 'dark'
}: PreviewInterfaceProps) => {
  // Get URL search parameters
  const searchParams = new URLSearchParams(window.location.search);

  const [showWelcomeSection, setShowWelcomeSection] = useState(true);
  const [activeTab, setActiveTab] = useState<'chat' | 'knowledge'>('chat');
  const [messages, setMessages] = useState<{ sender: 'user' | 'bot', text: string }[]>([
    { sender: 'bot', text: welcomeMessage }
  ]);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [isDark, setIsDark] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  const isLightMode = theme === 'light';
  const [showStartButton, setShowStartButton] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Add state variables for customizations
  const [firmNameState, setFirmName] = useState(firmName);
  const [attorneyNameState, setAttorneyName] = useState(attorneyName);
  const [practiceAreasState, setPracticeAreas] = useState(practiceAreas);
  const [stateState, setState] = useState(state);
  const [practiceDescriptionState, setPracticeDescription] = useState(practiceDescription);
  const [welcomeMessageState, setWelcomeMessage] = useState(welcomeMessage);
  const [informationGatheringState, setInformationGathering] = useState(informationGathering);
  const [logoUrlState, setLogoUrl] = useState(logoUrl);
  const [firmNameAnimationState, setFirmNameAnimation] = useState(firmNameAnimation);
  const [backgroundOpacityState, setBackgroundOpacity] = useState(backgroundOpacity);
  const [primaryColorState, setPrimaryColor] = useState(primaryColor);
  const [secondaryColorState, setSecondaryColor] = useState(secondaryColor);
  const [backgroundColorState, setBackgroundColor] = useState(backgroundColor);
  const [buttonOpacityState, setButtonOpacity] = useState<number>(buttonOpacity);
  const [buttonTextState, setButtonText] = useState(buttonText || 'Start Consultation');
  const [practiceAreaBackgroundOpacityState, setPracticeAreaBackgroundOpacity] = useState<number>(practiceAreaBackgroundOpacity);
  const [textBackgroundColorState, setTextBackgroundColor] = useState<string>(textBackgroundColor);

  // Animation variants for firm name
  const animationVariants = {
    fadeIn: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.8, ease: "easeOut", delay: 0.5 }
    },
    slideIn: {
      initial: { opacity: 0, x: -50 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: 0.8, type: "spring", stiffness: 50, damping: 10, delay: 0.5 }
    },
    scaleIn: {
      initial: { opacity: 0, scale: 0.8 },
      animate: { opacity: 1, scale: 1 },
      transition: { duration: 0.8, type: "spring", stiffness: 100, damping: 10, delay: 0.5 }
    },
    bounceIn: {
      initial: { opacity: 0, y: -50 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.8, type: "spring", stiffness: 100, damping: 8, delay: 0.5 }
    }
  };

  // Simplify shimmer animation
  const shimmerAnimation = {
    initial: { opacity: 0.95 },
    animate: {
      opacity: 1,
      transition: {
        duration: 2,
        repeat: 0,
        ease: "easeInOut"
      }
    }
  };

  // Split welcome text into parts
  const welcomeTextPart1 = "Welcome to the";
  const welcomeTextPart2 = "LegalScout";
  const welcomeTextPart3 = "of:";
  const [displayedPart1, setDisplayedPart1] = useState("");
  const [displayedPart2, setDisplayedPart2] = useState("");
  const [displayedPart3, setDisplayedPart3] = useState("");
  const [showMascot, setShowMascot] = useState(false);
  const [mascotAnimationComplete, setMascotAnimationComplete] = useState(false);
  const [firmNameAnimationComplete, setFirmNameAnimationComplete] = useState(false);

  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingPongCleanupRef = useRef<Function | null>(null);
  const forceVisibleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reportHeightIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let currentIndex = 0;
    let currentPart = 1;
    let typingInterval: NodeJS.Timeout;

    const startTyping = () => {
      typingInterval = setInterval(() => {
        if (currentPart === 1) {
          if (currentIndex <= welcomeTextPart1.length) {
            setDisplayedPart1(welcomeTextPart1.slice(0, currentIndex));
            currentIndex++;
          } else {
            currentPart = 2;
            currentIndex = 0;
          }
        } else if (currentPart === 2) {
          if (currentIndex <= welcomeTextPart2.length) {
            setDisplayedPart2(welcomeTextPart2.slice(0, currentIndex));
            currentIndex++;
          } else {
            clearInterval(typingInterval);
            setShowMascot(true); // Show mascot after LegalScout is typed
          }
        }
      }, 150);
    };

    startTyping();

    return () => clearInterval(typingInterval);
  }, []);

  // Start typing "of:" after mascot animation completes
  useEffect(() => {
    if (mascotAnimationComplete) {
      let currentIndex = 0;
      const typingInterval = setInterval(() => {
        if (currentIndex <= welcomeTextPart3.length) {
          setDisplayedPart3(welcomeTextPart3.slice(0, currentIndex));
          currentIndex++;
        } else {
          clearInterval(typingInterval);
        }
      }, 150);

      return () => clearInterval(typingInterval);
    }
  }, [mascotAnimationComplete]);

  // Watch for firm name animation completion
  useEffect(() => {
    if (firmNameAnimationComplete) {
      setTimeout(() => {
        setShowMascot(true);
      }, 500); // Wait 500ms after firm name animates before showing mascot
    }
  }, [firmNameAnimationComplete]);

  // Detect system theme changes and sync with global theme
  useEffect(() => {
    // Check global theme from document attribute first
    const globalTheme = document.documentElement.getAttribute('data-theme');

    if (globalTheme) {
      setIsDark(globalTheme === 'dark');
    } else if (theme) {
      setIsDark(theme !== 'light');
    } else {
      // Use system preference as fallback
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      setIsDark(darkModeQuery.matches);

      const handleChange = (e: MediaQueryListEvent) => {
        setIsDark(e.matches);
      };

      darkModeQuery.addEventListener('change', handleChange);
      return () => darkModeQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  // Mark component as mounted once animations and loading are complete
  useEffect(() => {
    setIsMounted(true);
    console.log('[PreviewInterface] Component mounted');

    // Ensure the start button is shown
    setShowStartButton(true);
    debugLog('Ensuring start button is shown');

    // Ensure loading state completes
    let progressInterval: NodeJS.Timeout;
    if (isLoading) {
      progressInterval = setInterval(() => {
        setLoadingProgress(prev => {
          const newProgress = prev + (100 - prev) * 0.1;
          if (newProgress >= 99) {
            console.log('[PreviewInterface] Loading complete, setting isLoading to false');
            setIsLoading(false);
            // Also ensure the button is shown after loading
            setShowStartButton(true);
            return 100;
          }
          return newProgress;
        });
      }, 100);
    }

    return () => {
      if (progressInterval) {
        console.log('[PreviewInterface] Clearing loading interval on unmount');
        clearInterval(progressInterval);
      }
    };
  }, []);

  // Add a dedicated effect to handle loading state transition
  useEffect(() => {
    if (!isLoading && loadingProgress >= 100) {
      console.log('[PreviewInterface] Loading completed, initializing chat interface');

      // Make sure content is visible after a short delay
      const showContentTimer = setTimeout(() => {
        // Force any state updates that might be needed after loading
        setMessages([{ sender: 'bot', text: welcomeMessageState }]);
        setShowStartButton(true);
        console.log('[PreviewInterface] Content visibility ensured');
        debugLog('Button visibility enforced after loading');
      }, 300);

      return () => clearTimeout(showContentTimer);
    }
  }, [isLoading, loadingProgress, welcomeMessageState]);

  // Prevent unmounting after initialization
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isLoading) {
        console.log('[PreviewInterface] Document became visible, ensuring content is shown');
        // Re-render content if needed
        setMessages(prev => [...prev]);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Set a backup timer to ensure we're visible
    const visibilityTimer = setTimeout(() => {
      if (!isLoading && loadingProgress >= 100) {
        console.log('[PreviewInterface] Backup visibility check passed');
        // Force re-render if needed
        setShowStartButton(prev => prev);
      }
    }, 1000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearTimeout(visibilityTimer);
    };
  }, [isLoading, loadingProgress]);

  // Handle messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('PreviewInterface received message:', event.data?.type);
      if (event.data && event.data.type === 'updateCustomizations') {
        const { customizations } = event.data;

        // Update all state variables based on customizations
        if (customizations.firmName !== undefined) setFirmName(customizations.firmName);
        if (customizations.attorneyName !== undefined) setAttorneyName(customizations.attorneyName);
        if (customizations.practiceAreas !== undefined) setPracticeAreas(customizations.practiceAreas);
        if (customizations.state !== undefined) setState(customizations.state);
        if (customizations.practiceDescription !== undefined) setPracticeDescription(customizations.practiceDescription);
        if (customizations.welcomeMessage !== undefined) setWelcomeMessage(customizations.welcomeMessage);
        if (customizations.informationGathering !== undefined) setInformationGathering(customizations.informationGathering);
        if (customizations.logoUrl !== undefined) setLogoUrl(customizations.logoUrl);
        if (customizations.primaryColor !== undefined) setPrimaryColor(customizations.primaryColor);
        if (customizations.secondaryColor !== undefined) setSecondaryColor(customizations.secondaryColor);
        if (customizations.backgroundColor !== undefined) setBackgroundColor(customizations.backgroundColor);
        if (customizations.backgroundOpacity !== undefined) {
          console.log('Setting background opacity:', customizations.backgroundOpacity);
          setBackgroundOpacity(Number(customizations.backgroundOpacity));
        }
        if (customizations.firmNameAnimation !== undefined) setFirmNameAnimation(customizations.firmNameAnimation);
        if (customizations.buttonOpacity !== undefined) setButtonOpacity(customizations.buttonOpacity);
        if (customizations.buttonText !== undefined) setButtonText(customizations.buttonText);
        if (customizations.practiceAreaBackgroundOpacity !== undefined) setPracticeAreaBackgroundOpacity(customizations.practiceAreaBackgroundOpacity);
        if (customizations.textBackgroundColor) setTextBackgroundColor(customizations.textBackgroundColor);
      }
    };

    window.addEventListener('message', handleMessage);

    // Send a ready message to parent
    if (window.parent && window !== window.parent) {
      try {
        window.parent.postMessage({ type: 'previewReady' }, '*');
      } catch (err) {
        console.warn('Failed to send ready message to parent', err);
      }
    }

    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Send parent window the height of the iframe content (for responsive iframe height)
  useEffect(() => {
    // Force content to be visible
    if (isLoading && loadingProgress > 90) {
      setIsLoading(false);
    }

    // Check if we should enable responsive resizing
    const urlParams = new URLSearchParams(window.location.search);
    const isResponsive = urlParams.get('responsive') === 'true';

    if (!isResponsive && !isMounted) return;

    const sendHeight = () => {
      if (window.parent && window !== window.parent) {
        try {
          const height = document.documentElement.scrollHeight;
          window.parent.postMessage({
            type: 'iframeHeight',
            height: height,
            source: 'previewInterface'
          }, '*');
        } catch (err) {
          console.warn('Failed to send height to parent', err);
        }
      }
    };

    // Send height multiple times to ensure it's captured
    sendHeight();
    const timeouts = [100, 500, 1000, 2000].map(delay => setTimeout(sendHeight, delay));

    // Use ResizeObserver for more accurate height measurements
    let resizeObserver: ResizeObserver | null = null;
    const contentEl = document.getElementById('preview-content');

    if (contentEl && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => sendHeight());
      resizeObserver.observe(contentEl);
    } else {
      // Fallback to interval
      const interval = setInterval(sendHeight, 2000);
      return () => clearInterval(interval);
    }

    return () => {
      timeouts.forEach(clearTimeout);
      if (resizeObserver && contentEl) {
        resizeObserver.unobserve(contentEl);
        resizeObserver.disconnect();
      }
    };
  }, [isLoading, loadingProgress, isMounted]);

  useEffect(() => {
    // Parse URL parameters - fix this to default to true if parameter is missing
    const urlParams = new URLSearchParams(window.location.search);
    const showStartButtonParam = urlParams.get('showStartButton');
    console.log('showStartButton param:', showStartButtonParam);

    // Only set to false if explicitly specified, otherwise default to true
    if (showStartButtonParam === 'false') {
      setShowStartButton(false);
    } else {
      setShowStartButton(true);
    }

    // Add this to ensure the button shows up
      setTimeout(() => {
        console.log('Forcing button visibility');
        setShowStartButton(true);
      debugLog('Button visibility enforced by timeout');
      }, 1000);
  }, []);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // Add user message
    setMessages(prev => [...prev, { sender: 'user', text: message }]);

    // Clear input
    setMessage('');

    // Simulate bot response after a short delay
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        {
          sender: 'bot',
          text: `Thanks for your message. This is a demo of how the chat interface works for ${firmNameState}.`
        }
      ]);
    }, 1000);
  };

  // Helper function to get the animation variant
  const getAnimationVariant = () => {
    // Only use animation if we have a valid one defined
    if (firmNameAnimationState &&
        (firmNameAnimationState === 'fadeIn' ||
         firmNameAnimationState === 'slideIn' ||
         firmNameAnimationState === 'scaleIn' ||
         firmNameAnimationState === 'bounceIn')) {
      return animationVariants[firmNameAnimationState as keyof typeof animationVariants];
    }
    // Default to fadeIn
    return animationVariants.fadeIn;
  };

  // Helper function for message styling with proper TypeScript types
  const getMessageStyle = (message: { sender: 'user' | 'bot', text: string }) => {
    const isUser = message.sender === 'user';
    return {
      backgroundColor: isDark ? '#2a2a2a' : '#f8f8f8',
      color: isDark ? '#ffffff' : '#333333',
      borderRadius: '8px',
      padding: '12px 16px',
      marginBottom: '12px',
      maxWidth: '85%',
      wordBreak: 'break-word' as 'break-word', // Type assertion for wordBreak
      boxShadow: theme === 'light' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',
      alignSelf: isUser ? 'flex-end' : 'flex-start',
      position: 'relative' as 'relative', // Type assertion for position
    };
  };

  // Update the render method to check specific conditions for showing the chat interface
  const render = () => {
    // If we have messages or the start button was clicked, ensure loading is hidden
    if (messages.length > 1 || !showStartButton) {
      isLoading && setIsLoading(false);
      loadingProgress < 100 && setLoadingProgress(100);
    }

    // Debug render state
    debugLog('Rendering with state', {
      showWelcomeSection,
      activeTab,
      messagesCount: messages.length,
      showStartButton,
      isLoading
    });

    // Check the specific condition for showing chat interface
    const shouldShowChat = messages.length > 1 && !showStartButton;
    debugLog('Should show chat interface:', shouldShowChat);

    const content = (
      <div
        id="preview-content"
        className={`preview-interface ${firmNameAnimationState} relative w-full min-h-screen`}
        style={{
          visibility: 'visible',
          display: 'flex',
          flexDirection: 'column',
          opacity: 1,
          zIndex: 10,
          overflowY: 'auto',
          overflowX: 'hidden',
          height: 'auto',
          minHeight: '100vh',
          width: '100%',
          maxWidth: searchParams.get('maxWidth') === 'true' ? '100%' : '720px',
          margin: '0 auto',
          boxSizing: 'border-box',
          left: '0',
          right: '0',
          backgroundColor: `rgba(${hexToRgb(backgroundColorState)}, ${backgroundOpacityState})`,
          color: theme === 'dark' ? '#ffffff' : '#333333',
          paddingBottom: messages.length > 1 ? '400px' : '0'
        }}
        ref={contentRef}
      >
        {isLoading && loadingProgress < 100 && !messages.length ? (
          <div className="loading-screen">
            <div className="loading-animation">
              <div className="spinner"></div>
              <div className="loading-text">Loading preview...</div>
            </div>
          </div>
        ) : (
          <>
            {renderHeader()}
            <div style={{ flex: 1 }}>
              {messages.length === 1 && (
                <>
                  {showWelcomeSection && renderWelcomeSection()}
                  {showStartButton && renderConsultationButton()}
                </>
              )}
              {messages.length > 1 && (
                <div style={{ paddingBottom: '400px' }}>
                  {showWelcomeSection && renderWelcomeSection()}
                </div>
              )}
            </div>
            {shouldShowChat && renderChatInterface()}
          </>
        )}
      </div>
    );

    return content;
  };

  // Add a debug log wrapper that's prominent in the console
  const debugLog = (message: string, data?: any) => {
    console.log(`%c [PreviewInterface DEBUG] ${message}`, 'background: #ff5722; color: white; padding: 2px 5px; border-radius: 3px;', data || '');
  };

  // Function to handle start consultation button click
  const handleStartConsultation = async () => {
    try {
      debugLog('Button clicked - ACTIVATING CHAT INTERFACE');

      // Force the welcome section to hide and activate chat
      setShowWelcomeSection(false);
      setActiveTab('chat');

      // Update the UI immediately with multiple messages
    setMessages([
      { sender: 'bot', text: welcomeMessageState },
        { sender: 'bot', text: informationGatheringState },
        { sender: 'bot', text: "This is a demo version of the consultation interface. Type a message to continue." }
    ]);

      // Hide the start button
    setShowStartButton(false);

      // Log what we're doing
      debugLog('Changing UI state to chat mode', {
        welcomeSection: false,
        activeTab: 'chat',
        messageCount: 3,
        startButton: false
      });

      // Try to send message to parent, but don't rely on it
      if (window.parent && window !== window.parent) {
        try {
          debugLog('Attempting to notify parent window (optional)');
          window.parent.postMessage({
            type: 'REQUEST_START_CONSULTATION',
            firmName: firmNameState,
            practiceDescription: practiceDescriptionState,
            assistantId: DEFAULT_ASSISTANT_ID
          }, '*');
        } catch (e) {
          debugLog('Error sending to parent, but continuing anyway', e);
        }
      }

      // Force a refresh of the content
      setTimeout(() => {
        debugLog('Forcing UI refresh');
        setMessages(prev => [...prev]); // Force re-render
      }, 100);

    } catch (error) {
      debugLog('Error in simplified consultation start', error);

      // Even if there's an error, still update the UI
      setMessages([
        { sender: 'bot', text: welcomeMessageState },
        { sender: 'bot', text: "I'm ready to help with your legal questions. What would you like to know?" }
      ]);
      setShowStartButton(false);
      setShowWelcomeSection(false);
    }
  };

  // Add initialization debug logging
  useEffect(() => {
    debugLog('Component mounted');

    // Debug iframe detection
    const isInIframe = window !== window.parent;
    debugLog(`Is in iframe: ${isInIframe}`);

    if (isInIframe) {
      debugLog('Parent referrer:', document.referrer);

      // Setup test message to parent
      const testMessageHandler = (event: MessageEvent) => {
        debugLog('Test message response received', {
          type: event.data?.type,
          origin: event.origin
        });
      };

      window.addEventListener('message', testMessageHandler);

      // Send test message
      debugLog('Sending test message to parent');
      window.parent.postMessage({ type: 'PREVIEW_READY_TEST' }, '*');

      // Also use the utility function
      debugLog('Sending test message using utility');
      sendMessageToParent('PREVIEW_READY_TEST', { timestamp: Date.now() });

      return () => {
        window.removeEventListener('message', testMessageHandler);
      };
    }
  }, []);

  // Debug iframeUtils setup
  useEffect(() => {
    debugLog('Setting up iframe ping/pong communication');
    const cleanup = setupIframePingPong();
    return () => {
      debugLog('Cleaning up iframe ping/pong');
      cleanup();
    };
  }, []);

  // Update the header rendering with color dots
  const renderHeader = () => {
    // Use the same effective background color logic as the main content
    const headerBackgroundColor = isLightMode ? backgroundColorState : '#1a1a1a';

    return (
      <header
        className="px-4 py-3 flex items-center border-b w-full"
        style={{
          borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
          backgroundColor: `rgba(${hexToRgb(headerBackgroundColor)}, ${backgroundOpacityState})`,
        }}
      >
      {messages.length > 1 && (
        <div className="flex-shrink-0 mr-3">
          {logoUrlState ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: '6px',
              padding: '4px',
              justifyContent: 'center'
            }}>
              <img
                src={logoUrlState}
                alt={`${firmNameState} logo`}
                style={{
                  height: '32px',
                  width: 'auto',
                  maxWidth: '100%',
                  objectFit: 'contain',
                  display: 'block',
                  visibility: 'visible',
                  opacity: 1,
                  zIndex: 100
                }}
                className="object-contain"
              />
            </div>
          ) : (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: '6px',
              padding: '4px',
              justifyContent: 'center'
            }}>
              <img
                src="/PRIMARY CLEAR.png"
                alt="LegalScout"
                style={{ height: '32px', width: 'auto' }}
                className="object-contain"
              />
            </div>
          )}
        </div>
      )}
    </header>
    );
  };

  // Adjust background color based on theme
  const effectiveBackgroundColor = isLightMode ? '#f5f5f5' : backgroundColorState;
  const themeBackgroundStyle = {
    backgroundColor: `rgba(${hexToRgb(effectiveBackgroundColor)}, ${backgroundOpacityState})`,
  };

  // Update the consultation button JSX
  const renderConsultationButton = () => {
    const isCustomLogo = !!logoUrlState;

    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: 'auto',
        maxWidth: '210px',
        margin: '0 auto'
      }}
      className="button-container"
      >
        <button
          onClick={handleStartConsultation}
          id="start-consultation-button"
          name="start-consultation"
          aria-label={buttonTextState || 'Start Consultation'}
          className="consultation-button-direct"
          style={{
            width: "210px",
            height: "210px",
            borderRadius: "50%",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: `rgba(${hexToRgb(secondaryColorState)}, ${Number(buttonOpacityState || 1)})`,
            color: 'white',
            fontSize: '18px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            border: 'none',
            marginBottom: '16px',
            position: 'relative',
            overflow: 'visible',
            margin: '0 auto'
          }}
        >
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {isCustomLogo ? (
              <img
                src={logoUrlState}
                alt={`${firmNameState} logo`}
                style={{
                  maxHeight: '120px',
                  maxWidth: '140px',
                  objectFit: 'contain',
                  display: 'block',
                  margin: '0 auto 12px'
                }}
                className="logo-image"
              />
            ) : (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                margin: '0 auto 12px'
              }}>
                <svg width="100" height="100" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            )}
            <span style={{
              textAlign: 'center',
              width: '100%',
              padding: '0 10px'
            }}>
              {buttonTextState || 'Start Consultation'}
            </span>
          </div>
        </button>
      </div>
    );
  };

  // Update the chat container styles
  const renderChatInterface = () => {
    debugLog('Rendering chat interface');

    return (
      <div
        id="chat-interface-container"
        style={{
          display: 'flex',
          flexDirection: 'column',
          position: 'fixed',
          bottom: 0,
        left: '50%',
        transform: 'translateX(-50%)',
          width: '100%',
          maxWidth: '800px',
          padding: '16px',
          backgroundColor: isLightMode ? 'rgba(255,255,255,0.9)' : 'rgba(0,0,0,0.2)',
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          boxShadow: isLightMode ? '0 -4px 6px rgba(0,0,0,0.1)' : '0 -4px 6px rgba(0,0,0,0.3)',
          zIndex: 100
        }}
      >
        <div
          id="messages-container"
                  style={{
            maxHeight: '300px',
            overflowY: 'auto',
            padding: '8px',
            display: 'flex',
            flexDirection: 'column',
            marginBottom: '16px',
          }}
        >
          {renderMessages()}
        </div>

        <div
          id="input-container"
            style={{
            display: 'flex',
            position: 'relative',
            padding: '8px',
          }}
        >
          <label htmlFor="chat-message-input" className="sr-only">Type your message</label>
          <input
            id="chat-message-input"
            name="message"
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleSendMessage();
              }
            }}
            placeholder="Type your message here..."
            aria-label="Message input"
                  style={{
              flex: 1,
              padding: '12px 16px',
              borderRadius: '24px',
              border: `1px solid ${inputBorderColor}`,
              backgroundColor: inputBgColor,
              color: inputTextColor,
              outline: 'none',
              fontSize: '16px',
              boxShadow: isLightMode ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',
            }}
          />
    <button
            type="button"
            id="send-message-button"
            name="send-button"
      onClick={handleSendMessage}
      disabled={!message.trim()}
      className="send-button p-2 rounded-full flex items-center justify-center"
      aria-label="Send message"
            style={{
              marginLeft: '8px',
              backgroundColor: primaryColorState,
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: message.trim() ? 'pointer' : 'not-allowed',
              opacity: message.trim() ? 1 : 0.5
            }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    </button>
        </div>
      </div>
  );
  };

  // Get URL parameters for styling and override the values if specified in the URL
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);

    // Check for fullWidth parameter
    const fullWidth = searchParams.get('fullWidth') === 'true';

    // Force the content to use the full width if specified
    if (fullWidth) {
      const contentEl = document.getElementById('preview-content');
      if (contentEl) {
        contentEl.style.maxWidth = '100%';
        contentEl.style.width = '100%';
        contentEl.style.margin = '0 auto';

        // Also set the welcome title to use full width
        const welcomeTitleEl = document.querySelector('.welcome-title');
        if (welcomeTitleEl) {
          (welcomeTitleEl as HTMLElement).style.maxWidth = '100%';
          (welcomeTitleEl as HTMLElement).style.width = '100%';
          (welcomeTitleEl as HTMLElement).style.margin = '0 auto';
          (welcomeTitleEl as HTMLElement).style.padding = '0 16px';
        }
      }
    }
  }, [isMounted]);

  // Override the global JSON.parse to handle errors
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const originalJSONParse = JSON.parse;

      (window as any).JSON.parse = (text: string) => {
        try {
          return originalJSONParse(text);
        } catch (e) {
          // If the text starts with <!DOCTYPE, it's definitely HTML, not JSON
          if (typeof text === 'string' && text.trim().startsWith('<!DOCTYPE')) {
            console.warn('Received HTML instead of JSON, returning empty object');
            return {};
          }

          console.warn('JSON parse error:', e);
          return {};
        }
      };

      return () => {
        (window as any).JSON.parse = originalJSONParse;
      };
    }
  }, []);

  // Add detailed logging for component lifecycle
  useEffect(() => {
    console.log('[PreviewInterface] Initializing component with props:', {
      firmName,
      attorneyName,
      practiceAreas,
      primaryColor,
      secondaryColor,
      logoUrl,
      theme,
      backgroundOpacity
    });

    // Check if we're in production
    const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
    console.log('[PreviewInterface] Environment:', isProduction ? 'Production' : 'Development');

    // Log DOM structure
    const logDOMState = () => {
      const content = document.getElementById('preview-content');
      console.log('[PreviewInterface] Content element exists:', !!content);
      if (content) {
        console.log('[PreviewInterface] Content element styles:', {
          display: window.getComputedStyle(content).display,
          visibility: window.getComputedStyle(content).visibility,
          opacity: window.getComputedStyle(content).opacity,
          backgroundColor: window.getComputedStyle(content).backgroundColor
        });
      }

      // Log critical elements
      ['welcome-title', 'button-container', 'consultation-button-direct'].forEach(id => {
        const el = document.querySelector(`.${id}`);
        console.log(`[PreviewInterface] ${id} exists:`, !!el);
        if (el) {
          console.log(`[PreviewInterface] ${id} styles:`, {
            display: window.getComputedStyle(el).display,
            visibility: window.getComputedStyle(el).visibility,
            opacity: window.getComputedStyle(el).opacity
          });
        }
      });
    };

    // Monitor style changes - but filter out minor animation changes
    const styleObserver = new MutationObserver((mutations) => {
      let significantChanges = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const target = mutation.target as HTMLElement;
          const oldDisplay = target.style.display;
          const oldVisibility = target.style.visibility;
          const oldOpacity = target.style.opacity;

          // Only log significant style changes (visibility, display, opacity)
          // that might affect the user experience, not minor position changes
          if (target.style.display !== oldDisplay ||
              target.style.visibility !== oldVisibility ||
              (parseFloat(target.style.opacity) < 0.5 && oldOpacity !== target.style.opacity)) {

            console.log('[PreviewInterface] Significant style mutation detected:', {
              element: mutation.target,
              display: target.style.display,
              visibility: target.style.visibility,
              opacity: target.style.opacity
            });

            significantChanges = true;
          }
        }
      });

      // If we detected significant changes, log the full DOM state
      if (significantChanges) {
        logDOMState();
      }
    });

    // Monitor DOM changes - but be more selective about what we log
    const domObserver = new MutationObserver((mutations) => {
      // Only log when elements are actually removed, not just text changes
      const removedElements = mutations.filter(m =>
        m.type === 'childList' && m.removedNodes.length > 0 &&
        Array.from(m.removedNodes).some(node => node.nodeType === Node.ELEMENT_NODE)
      );

      if (removedElements.length > 0) {
        console.log('[PreviewInterface] DOM elements removed:', {
          count: removedElements.length,
          mutations: removedElements.map(m => ({
            target: m.target,
            removedNodes: m.removedNodes.length
          }))
        });
      }
    });

    // Start observers
    const content = document.getElementById('preview-content');
    if (content) {
      styleObserver.observe(content, {
        attributes: true,
        attributeFilter: ['style'],
        subtree: true
      });
      domObserver.observe(content, {
        childList: true,
        subtree: true
      });
    }

    // Log initial state
    logDOMState();

    // Setup error boundary
    const errorHandler = (event: ErrorEvent) => {
      console.error('[PreviewInterface] Caught error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
      event.preventDefault();
      return true;
    };

    // Setup unhandled rejection handler
    const rejectionHandler = (event: PromiseRejectionEvent) => {
      console.error('[PreviewInterface] Unhandled rejection:', {
        reason: event.reason,
        promise: event.promise
      });
      event.preventDefault();
    };

    // Monitor resource loading
    const resourceLoadHandler = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target instanceof HTMLImageElement || target instanceof HTMLScriptElement || target instanceof HTMLLinkElement) {
        console.log('[PreviewInterface] Resource loaded:', {
          type: target.tagName,
          src: target instanceof HTMLImageElement ? target.src :
               target instanceof HTMLScriptElement ? target.src :
               target instanceof HTMLLinkElement ? target.href : null,
          success: !target.hasAttribute('data-error')
        });
      }
    };

    const resourceErrorHandler = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target instanceof HTMLImageElement || target instanceof HTMLScriptElement || target instanceof HTMLLinkElement) {
        console.error('[PreviewInterface] Resource failed to load:', {
          type: target.tagName,
          src: target instanceof HTMLImageElement ? target.src :
               target instanceof HTMLScriptElement ? target.src :
               target instanceof HTMLLinkElement ? target.href : null,
          error: event
        });
        target.setAttribute('data-error', 'true');
      }
    };

    // Add event listeners
    window.addEventListener('error', errorHandler);
    window.addEventListener('unhandledrejection', rejectionHandler);
    window.addEventListener('load', resourceLoadHandler, true);
    window.addEventListener('error', resourceErrorHandler, true);

    // Periodic state check
    const stateInterval = setInterval(() => {
      console.log('[PreviewInterface] Current state:', {
        isLoading,
        loadingProgress,
        isMounted,
        showStartButton,
        messages: messages.length
      });
      logDOMState();
    }, 5000);

    // Monitor iframe messages
    const messageHandler = (event: MessageEvent) => {
      console.log('[PreviewInterface] Received message:', {
        type: event.data?.type,
        origin: event.origin,
        source: event.source === window.parent ? 'parent' : 'other'
      });
    };
    window.addEventListener('message', messageHandler);

    return () => {
      // Cleanup
      window.removeEventListener('error', errorHandler);
      window.removeEventListener('unhandledrejection', rejectionHandler);
      window.removeEventListener('load', resourceLoadHandler, true);
      window.removeEventListener('error', resourceErrorHandler, true);
      window.removeEventListener('message', messageHandler);
      styleObserver.disconnect();
      domObserver.disconnect();
      clearInterval(stateInterval);
      console.log('[PreviewInterface] Component cleanup completed');
    };
  }, [isLoading, loadingProgress, isMounted, showStartButton, messages.length]);

  // Add debounced loading state handler
  const [debouncedLoading, setDebouncedLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedLoading(isLoading);
    }, 300); // Debounce loading state changes

    return () => clearTimeout(timer);
  }, [isLoading]);

  // Add node removal protection
  useEffect(() => {
    const protectContent = (mutations: MutationRecord[]) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
          const target = mutation.target as HTMLElement;

          // Check if this is a critical element being removed
          if (target.id === 'preview-content' ||
              target.classList.contains('preview-interface') ||
              target.classList.contains('content-container')) {

            console.warn('[PreviewInterface] Prevented removal of critical element:', target);

            // If the element was removed, add it back
            mutation.removedNodes.forEach(node => {
              if (!document.body.contains(node)) {
                target.appendChild(node);
              }
            });
          }
        }
      });
    };

    const observer = new MutationObserver(protectContent);
    const content = document.getElementById('preview-content');

    if (content) {
      observer.observe(content, {
        childList: true,
        subtree: true
      });
    }

    return () => observer.disconnect();
  }, []);

  // Stabilize message handling
  const [lastUpdateTime, setLastUpdateTime] = useState(0);

  const handleMessage = useCallback((event: MessageEvent) => {
    // Debounce message handling
    const now = Date.now();
    if (now - lastUpdateTime < 300) { // Ignore messages that come too quickly
      return;
    }

    if (event.data?.type === 'updateCustomizations') {
      setLastUpdateTime(now);
      // Handle customization updates...
    }
  }, [lastUpdateTime]);

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [handleMessage]);

  // Add production-specific protections
  useEffect(() => {
    const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

    if (isProduction) {
      console.log('[PreviewInterface] Applying production-specific protections');

      // Force content visibility in production
      const forceVisibility = () => {
        const content = document.getElementById('preview-content');
        if (content) {
          content.style.display = 'block';
          content.style.visibility = 'visible';
          content.style.opacity = '1';

          // Also force visibility of critical child elements
          ['welcome-title', 'button-container', 'consultation-button-direct'].forEach(className => {
            const el = content.querySelector(`.${className}`);
            if (el instanceof HTMLElement) {
              el.style.display = 'block';
              el.style.visibility = 'visible';
              el.style.opacity = '1';
            }
          });
        }
      };

      // Run visibility check multiple times
      const visibilityIntervals = [100, 500, 1000, 2000, 5000].map(delay =>
        setTimeout(forceVisibility, delay)
      );

      // Protect against unwanted removals in production
      const protectNodes = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
            const target = mutation.target as HTMLElement;

            // Check if this is an essential element
            if (target.id === 'preview-content' ||
                target.classList.contains('preview-interface') ||
                target.classList.contains('content-container')) {

              console.warn('[PreviewInterface] Preventing removal of essential element in production');

              // Re-add removed nodes
              mutation.removedNodes.forEach(node => {
                if (!document.body.contains(node)) {
                  try {
                    target.appendChild(node);
                    console.log('[PreviewInterface] Restored removed node:', node);
                  } catch (e) {
                    console.error('[PreviewInterface] Failed to restore node:', e);
                  }
                }
              });
          }
        }
      });
    });

      // Start protection observer
      const content = document.getElementById('preview-content');
      if (content) {
        protectNodes.observe(content, {
      childList: true,
      subtree: true
    });
      }

      // Handle production-specific message restrictions
      const handleProductionMessages = (event: MessageEvent) => {
        // Verify origin in production
        const allowedOrigins = [
          window.location.origin,
          'https://legalscout.net',
          'https://www.legalscout.net'
        ];

        if (!allowedOrigins.includes(event.origin)) {
          console.warn('[PreviewInterface] Blocked message from unauthorized origin:', event.origin);
          return;
        }

        // Handle the message
        if (event.data?.type === 'updateCustomizations') {
          // Debounce updates in production
          if (Date.now() - lastUpdateTime < 300) {
            return;
          }

          try {
            // Safely apply updates
            requestAnimationFrame(() => {
              forceVisibility();
            });
          } catch (e) {
            console.error('[PreviewInterface] Error handling production message:', e);
          }
        }
      };

      window.addEventListener('message', handleProductionMessages);

      // Production-specific error recovery
      const recoveryInterval = setInterval(() => {
        const content = document.getElementById('preview-content');
        if (!content || !document.body.contains(content)) {
          console.warn('[PreviewInterface] Content missing, attempting recovery');
          forceVisibility();
        }
      }, 1000);

      return () => {
        visibilityIntervals.forEach(clearTimeout);
        protectNodes.disconnect();
        window.removeEventListener('message', handleProductionMessages);
        clearInterval(recoveryInterval);
      };
    }
  }, [lastUpdateTime]);

  // Add custom CSS for the logo image and accessibility
  useEffect(() => {
    // Create a style element for custom CSS
    const style = document.createElement('style');
    style.textContent = `
      .logo-image {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
      }

      /* Accessibility helper class */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Update the message rendering
  const renderMessages = () => {
    debugLog(`Rendering ${messages.length} messages`);

    return messages.map((message, index) => {
      const bubbleStyle = getMessageStyle(message);

      return (
        <div
          key={index}
          style={{
            display: 'flex',
            justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
            width: '100%',
            marginBottom: '16px',
          }}
        >
          <div style={bubbleStyle}>
            {message.text}
          </div>
        </div>
      );
    });
  };

  // Fix welcome text style to properly use isLightMode
  const welcomeTextStyle = {
    fontFamily: "'Courier New', Courier, monospace",
    fontSize: '24px',
    color: theme === 'light' ? '#333333' : 'rgba(255,255,255,0.9)',
    whiteSpace: 'pre-wrap',
    minHeight: '36px',
        display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    position: 'relative' as 'relative',
  };

  // Define input colors for chat interface
  const inputBgColor = theme === 'light' ? '#ffffff' : 'rgba(255,255,255,0.1)';
  const inputTextColor = theme === 'light' ? '#333333' : 'rgba(255,255,255,0.9)';
  const inputBorderColor = theme === 'light' ? '#e0e0e0' : 'rgba(255,255,255,0.2)';

  // Update the practice description style to correctly use textBackgroundColor
  const practiceDescriptionStyle = {
              fontSize: '16px',
    opacity: 0.9,
    marginBottom: '24px',
    maxWidth: '600px',
    margin: '0 auto',
    backgroundColor: theme === 'light'
      ? 'rgba(255,255,255,0.9)'
      : `rgba(${hexToRgb(textBackgroundColorState)}, ${practiceAreaBackgroundOpacityState})`,
    padding: '20px',
    borderRadius: '8px',
    color: theme === 'light' ? '#333333' : 'rgba(255,255,255,0.95)',
    boxShadow: theme === 'light' ? '0 4px 6px rgba(0, 0, 0, 0.1)' : '0 4px 12px rgba(0, 0, 0, 0.3)'
  };

  // Update the welcome section with proper centering
  const renderWelcomeSection = () => {
    const animation = getAnimationVariant();

    // Define proper motion style for firm name with type assertions
    const firmNameMotionStyle = {
      fontSize: '64px',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      fontWeight: '700',
      marginBottom: '20px',
      color: primaryColorState,
      lineHeight: '1.1',
      letterSpacing: '-0.02em',
      textAlign: 'center' as 'center',
      maxWidth: '100%',
      width: '100%',
      margin: '0 auto 20px',
      overflow: 'hidden',
      wordWrap: 'break-word' as 'break-word'
    };

  return (
      <div style={{
        textAlign: 'center',
        marginBottom: '20px',
        width: '100%',
        maxWidth: '100%',
        padding: '20px',
            position: 'relative',
        top: 0,
        left: '50%',
        transform: 'translateX(-50%)',
        overflow: 'hidden'
      }}>
        <div style={{ marginBottom: '16px', position: 'relative' }}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
            style={welcomeTextStyle}
          >
            <span>{displayedPart1}</span>{' '}
            <span style={{
              display: 'inline-flex',
            alignItems: 'center',
                    position: 'relative',
              marginRight: '48px'
            }}>
              {displayedPart2}
              {showMascot && (
                <motion.img
                  src="/PRIMARY CLEAR.png"
                  alt="LegalScout Mascot"
                  initial={{
                    scale: 0.2,
                    opacity: 0,
                    x: 150,
                    y: -50
                  }}
                  animate={{
                    scale: 1,
                    opacity: 1,
                    x: 0,
                    y: '-50%'
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 120,
                    damping: 20,
                    duration: 1.5,
                    mass: 1.2
                  }}
                  onAnimationComplete={() => setMascotAnimationComplete(true)}
                              style={{
                    width: '48px',
                    height: '48px',
                    position: 'absolute',
                    left: '100%',
                    top: '50%',
                    marginLeft: '8px',
                    transform: 'translateY(-50%)',
                    display: 'block',
                    zIndex: 2
                  }}
                />
              )}
            </span>
            <span>{displayedPart3}</span>
                        </motion.div>
                      </div>

        <motion.div
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <motion.h2
            className="welcome-title"
            initial={animation?.initial}
            animate={animation?.animate}
            transition={animation?.transition}
            onAnimationComplete={() => setFirmNameAnimationComplete(true)}
            style={firmNameMotionStyle}
          >
            {firmNameState || 'Your AI Legal Assistant'}
          </motion.h2>
                </motion.div>

                <motion.div
          style={practiceDescriptionStyle}
          initial={{ opacity: 0, y: 20 }}
          animate={{
            opacity: firmNameAnimationComplete ? 0.9 : 0,
            y: firmNameAnimationComplete ? 0 : 20
          }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: "easeOut"
          }}
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeSanitize]}
          >
            {practiceDescriptionState}
          </ReactMarkdown>
                </motion.div>
    </div>
  );
  };

  // Add a useEffect specifically to monitor the showStartButton state
  useEffect(() => {
    debugLog(`showStartButton state changed to: ${showStartButton}`);
    // Check if button exists in DOM after state change
    setTimeout(() => {
      const buttonContainer = document.querySelector('.button-container');
      const consultationButton = document.querySelector('.consultation-button-direct');
      debugLog('Button elements after state change:', {
        buttonContainerExists: !!buttonContainer,
        consultationButtonExists: !!consultationButton
      });
    }, 100);
  }, [showStartButton]);

  return render();
};

// Helper function to convert hex to rgb
function hexToRgb(hex: string) {
  try {
    // Default to black if no color is provided
    if (!hex) return '0, 0, 0';

    // Remove the hash at the start if it exists
    hex = hex.replace(/^#/, '');

    // Ensure we have a valid hex string
    if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
      return '0, 0, 0'; // Return black for invalid hex
    }

    // Parse the hex values
    const bigint = parseInt(hex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    return `${r}, ${g}, ${b}`;
  } catch (error) {
    console.error('Error converting hex to RGB:', error);
    return '0, 0, 0'; // Return black as fallback
  }
}