import React, { useState, useEffect, useRef } from 'react';
import { DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants';
import VapiCall from '../VapiCall';
import DirectVapiCall from '../DirectVapiCall';
import { processImageUrl } from '../../utils/imageStorage';

// Utility to convert hex to RGB for opacity handling
const hexToRgb = (hex) => {
  if (!hex) return '0, 0, 0';

  // Remove the # if present
  hex = hex.replace('#', '');

  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Return the RGB values as a string
  return `${r}, ${g}, ${b}`;
};

const SimplifiedPreview = ({
  firmName = 'Your Law Firm',
  titleText = '',  // Add titleText property with default empty string
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  buttonColor = '#3498db', // Add buttonColor property with default value
  backgroundColor = '#1a1a1a', // Add backgroundColor property with default value
  backgroundOpacity = 0.9, // Add backgroundOpacity property with default value
  practiceDescription = "Your AI legal assistant is ready to help",
  welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",
  theme = 'dark',
  logoUrl = '/PRIMARY CLEAR.png',
  buttonText = 'Start Consultation',
  mascot = '/PRIMARY CLEAR.png',
  vapiInstructions = '',
  buttonOpacity = 1,
  practiceAreaBackgroundOpacity = 0.1,
  textBackgroundColor = '#634C38',
  vapi_assistant_id = null // Add assistant ID prop
}) => {
  // State for messages and chat UI
  const [messages, setMessages] = useState([
    { sender: 'bot', text: welcomeMessage }
  ]);
  const [message, setMessage] = useState('');
  const [showStartButton, setShowStartButton] = useState(true);
  const [chatActive, setChatActive] = useState(false);

  // State for customizable properties
  const [firmNameState, setFirmName] = useState(firmName);
  const [titleTextState, setTitleText] = useState(titleText || firmName); // Add titleText state with fallback to firmName
  const [primaryColorState, setPrimaryColor] = useState(primaryColor);
  const [secondaryColorState, setSecondaryColor] = useState(secondaryColor);
  const [buttonColorState, setButtonColor] = useState(buttonColor); // Add buttonColor state
  const [backgroundColorState, setBackgroundColor] = useState(backgroundColor); // Add backgroundColor state
  const [backgroundOpacityState, setBackgroundOpacity] = useState(backgroundOpacity); // Add backgroundOpacity state
  const [practiceDescriptionState, setPracticeDescription] = useState(practiceDescription);
  const [welcomeMessageState, setWelcomeMessage] = useState(welcomeMessage);
  const [informationGatheringState, setInformationGathering] = useState(informationGathering);
  const [themeState, setTheme] = useState(theme);
  const [logoUrlState, setLogoUrl] = useState(logoUrl);
  const [buttonTextState, setButtonText] = useState(buttonText);
  const [buttonOpacityState, setButtonOpacity] = useState(buttonOpacity);
  const [practiceAreaBackgroundOpacityState, setPracticeAreaBackgroundOpacity] = useState(practiceAreaBackgroundOpacity);
  const [textBackgroundColorState, setTextBackgroundColor] = useState(textBackgroundColor);
  const [assistantIdState, setAssistantId] = useState(vapi_assistant_id);

  const isDark = themeState !== 'light';

  // Debug logging for logo paths and colors
  useEffect(() => {
    console.log('SimplifiedPreview mounted with props:');
    console.log('firmName:', firmNameState);
    console.log('titleText:', titleTextState);
    console.log('logoUrl:', logoUrlState);
    console.log('mascot:', mascot);
    console.log('buttonText:', buttonTextState);
    console.log('primaryColor:', primaryColorState);
    console.log('secondaryColor:', secondaryColorState);
    console.log('buttonColor:', buttonColorState);
    console.log('backgroundColor:', backgroundColorState);
    console.log('backgroundOpacity:', backgroundOpacityState);
    console.log('textBackgroundColor:', textBackgroundColorState);
    console.log('practiceAreaBackgroundOpacity:', practiceAreaBackgroundOpacityState);
    console.log('theme:', themeState);
    console.log('assistantId:', assistantIdState);

    // Reset messages when welcome message changes
    setMessages([{ sender: 'bot', text: welcomeMessageState }]);
  }, [welcomeMessageState, logoUrlState, mascot, buttonTextState, primaryColorState, secondaryColorState, buttonColorState, backgroundColorState, backgroundOpacityState, textBackgroundColorState, practiceAreaBackgroundOpacityState, themeState, firmNameState, titleTextState, assistantIdState]);

  // Handle messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('SimplifiedPreview received message:', event.data?.type);

      // Handle both message types: 'updateCustomizations' (from DemoPage) and 'UPDATE_PREVIEW_CONFIG' (from Dashboard)
      if (event.data && (event.data.type === 'updateCustomizations' || event.data.type === 'UPDATE_PREVIEW_CONFIG')) {
        // Get customizations from the appropriate property based on message type
        const customizations = event.data.type === 'updateCustomizations'
          ? event.data.customizations
          : event.data.config;

        console.log('Received customization updates:', customizations);
        console.log('Button color in customizations:', customizations.buttonColor);
        console.log('Background color in customizations:', customizations.backgroundColor);
        console.log('Background opacity in customizations:', customizations.backgroundOpacity);

        // Update all state variables based on customizations
        if (customizations.firmName) {
          console.log('Updating firmName from:', firmNameState, 'to:', customizations.firmName);
          setFirmName(customizations.firmName);
        }

        // Handle titleText with proper fallback logic
        if (customizations.titleText !== undefined) {
          // If titleText is explicitly provided (even if empty), use it
          console.log('Updating titleText from:', titleTextState, 'to:', customizations.titleText);
          setTitleText(customizations.titleText);
        } else if (customizations.firmName) {
          // If no titleText is provided but firmName is updated,
          // always update titleText to match firmName for consistency
          console.log('Updating titleText to match firmName:', customizations.firmName);
          setTitleText(customizations.firmName);
        }
        if (customizations.primaryColor) setPrimaryColor(customizations.primaryColor);
        if (customizations.secondaryColor) setSecondaryColor(customizations.secondaryColor);
        if (customizations.buttonColor) setButtonColor(customizations.buttonColor); // Add buttonColor handling
        if (customizations.backgroundColor) setBackgroundColor(customizations.backgroundColor); // Add backgroundColor handling
        if (customizations.backgroundOpacity !== undefined) setBackgroundOpacity(customizations.backgroundOpacity); // Add backgroundOpacity handling
        if (customizations.practiceDescription) setPracticeDescription(customizations.practiceDescription);
        if (customizations.welcomeMessage) {
          setWelcomeMessage(customizations.welcomeMessage);
          setMessages([{ sender: 'bot', text: customizations.welcomeMessage }]);
        }
        if (customizations.informationGathering) setInformationGathering(customizations.informationGathering);
        if (customizations.theme) setTheme(customizations.theme);
        if (customizations.logoUrl) setLogoUrl(customizations.logoUrl);
        if (customizations.buttonText) setButtonText(customizations.buttonText);
        if (customizations.buttonOpacity !== undefined) setButtonOpacity(customizations.buttonOpacity);
        if (customizations.practiceAreaBackgroundOpacity !== undefined) setPracticeAreaBackgroundOpacity(customizations.practiceAreaBackgroundOpacity);
        if (customizations.textBackgroundColor) setTextBackgroundColor(customizations.textBackgroundColor);
        if (customizations.vapi_assistant_id || customizations.vapiAssistantId) {
          const assistantId = customizations.vapi_assistant_id || customizations.vapiAssistantId;
          console.log('Received assistant ID update:', assistantId);
          setAssistantId(assistantId);
        }
        if (customizations.vapiInstructions) console.log('Received vapiInstructions update');

        // Log all updates for debugging
        console.log('Updated preview with new configuration:', {
          firmName: firmNameState,
          titleText: titleTextState,
          primaryColor: primaryColorState,
          secondaryColor: secondaryColorState,
          buttonColor: buttonColorState,
          backgroundColor: backgroundColorState,
          backgroundOpacity: backgroundOpacityState,
          buttonText: buttonTextState,
          buttonOpacity: buttonOpacityState,
          practiceAreaBackgroundOpacity: practiceAreaBackgroundOpacityState,
          textBackgroundColor: textBackgroundColorState,
          theme: themeState
        });
      }
    };

    window.addEventListener('message', handleMessage);

    // Send ready message to parent
    if (window !== window.parent) {
      try {
        window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');
        console.log('Sent ready message to parent');
      } catch (e) {
        console.error('Error sending message to parent:', e);
      }
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Get the appropriate logo to use
  const getLogoToUse = () => {
    // Prioritize user uploaded logo
    if (logoUrlState && logoUrlState !== '/PRIMARY CLEAR.png') {
      console.log('Using custom logoUrl:', logoUrlState);
      // Process the logo URL to get the actual image data if it's an image ID
      const processedUrl = processImageUrl(logoUrlState);
      console.log('Processed URL:', processedUrl ? 'URL processed successfully' : 'Failed to process URL');
      return processedUrl || '/PRIMARY CLEAR.png';
    }

    // Fallback to mascot
    if (mascot && mascot !== '/PRIMARY CLEAR.png') {
      console.log('Using mascot:', mascot);
      // Process the mascot URL to get the actual image data if it's an image ID
      const processedUrl = processImageUrl(mascot);
      return processedUrl || '/PRIMARY CLEAR.png';
    }

    // Default fallback
    console.log('Using default logo: /PRIMARY CLEAR.png');
    return '/PRIMARY CLEAR.png';
  };

  const logoToUse = getLogoToUse();

  const handleStartConsultation = () => {
    console.log('🚀 [PREVIEW CALL START] Start consultation clicked');
    console.log('🎯 [PREVIEW CALL START] Using assistant ID:', assistantIdState);
    console.log('💬 [PREVIEW CALL START] Welcome message:', welcomeMessageState);
    console.log('🔊 [PREVIEW CALL START] Voice settings:', { voiceId: voiceIdState, voiceProvider: voiceProviderState });

    // Remove the simulated chat setup - we want real Vapi calls
    // setMessages([
    //   { sender: 'bot', text: welcomeMessageState },
    //   { sender: 'bot', text: informationGatheringState },
    //   { sender: 'bot', text: "This is a demo of the chat interface. Type a message to continue the conversation." }
    // ]);

    setShowStartButton(false);
    setChatActive(true);
    console.log('✅ [PREVIEW CALL START] DirectVapiCall component will be rendered');

    // Try to notify parent if in iframe
    if (window !== window.parent) {
      try {
        // Create the assistantOverrides object
        const assistantOverrides = {
          // First Message override - this is the message that will be spoken first
          firstMessage: welcomeMessageState,
          // First Message Mode - ensure the assistant speaks first
          firstMessageMode: "assistant-speaks-first",
          // Ensure the first message is not interrupted
          firstMessageInterruptionsEnabled: false,
          // Model configuration with system prompt
          model: {
            provider: "anthropic", // Required provider field
            model: "claude-3-sonnet-20240229", // Required model field
            messages: [
              {
                role: "system",
                content: `Special Instructions: you, Scout are acting on behalf of ${firmNameState}.\n\nHere are your instructions: ${informationGatheringState}\n\n[Identity]\nYou are Scout, an AI legal assistant representing LegalScout. You are akin to a legal K9, navigating the complex legal terrain in the USA, and occasionally making light-hearted dog puns when appropriate. Your primary role is to guide users through understanding their legal matters and gathering necessary details for creating a precise legal brief.\n\n[Style]\n- Maintain a professional yet friendly tone.\n- Inject dog-themed humor when suitable without detracting from the seriousness of the information being gathered.\n- Spell slowly when pronouncing letters for clarity.\n\n[Response Guidelines]\n- Confirm the user's email by spelling it back to them letter by letter.\n- Ensure all contact information is gathered before informing the user that LegalScout is finding them an attorney.\n- Keep responses clear and focused on the user's legal situation.\n\n[Task & Goals]\n1. Begin by requesting the user's email address and confirm its accuracy by spelling it back letter by letter.\n2. Introduce yourself and your role, incorporating the user's name into a dog pun if inferred from their email. Inform the user that you will gather information to help attorneys understand their situation but clarify that you cannot provide legal advice.\n3. Use iterative questioning to understand the user's legal matter. Examples:\n   - Initial Question: "What kind of legal problem are you dealing with?"\n   - Follow-up (based on the user's response): "You mentioned a contract dispute. Could you tell me more about the type of contract involved and what specifically is in dispute?" (Wait for the answer before proceeding.)\n   - Further Follow-up: "And when did this dispute arise? What steps, if any, have you already taken to resolve it?" (Wait for the answer.)\n4. Upon obtaining a thorough grasp of the user's situation and if they wish to seek legal representation, transition to interview mode: "Thanks for sharing those details. It sounds like LegalScout can help you find an attorney experienced in this area. Let's gather a few more details to complete your case file."\n\n[Interview Mode - Collecting Required Information]\n- Ensure clarity and ease for the user in collecting each piece of information.\n- Collect client background, jurisdiction, statement of facts, legal issues, client objectives, brief practice area, user jurisdiction, name, phone number, street address, city, and zip code.\n- Verify all information, particularly the user's state and email address.\n\n[Error Handling / Fallback]\n- If user input is unclear or incomplete, gently solicit clarification or confirmation.\n- Use polite rephrasing methods to encourage users to provide specific information.\n\n[Completion and Summary]\n- Conclude with, "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."\n- Inform the user of additional options to manage their case by visiting LegalScout.ai/mycases.\n\n[Important Tool Usage]\nYou must initiate the tool "Live_Dossier" 4a0d63cf-0b84-4eec-bddf-9c5869439d7e once the user provides their email and with every reply following that.\n\nNote: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.`
              }
            ]
          },
          // Ensure recording is enabled
          artifactPlan: {
            recordingEnabled: true
          }
        };

        // Send message to parent window
        window.parent.postMessage({
          type: 'REQUEST_START_CONSULTATION',
          firmName: firmNameState,
          practiceDescription: practiceDescriptionState,
          assistantId: DEFAULT_ASSISTANT_ID,
          mascot: logoToUse, // Send the correct logo to use
          vapiInstructions,
          primaryColor: primaryColorState,
          secondaryColor: secondaryColorState,
          theme: isDark ? 'dark' : 'light',
          // Add direct Vapi overrides
          ...assistantOverrides
        }, '*');
        console.log('Sent message to parent window with logo:', logoToUse);
      } catch (e) {
        console.error('Error sending message to parent:', e);
      }
    }
  };

  // Cleanup effect to ensure call is properly ended when component unmounts
  useEffect(() => {
    return () => {
      if (chatActive) {
        console.log('SimplifiedPreview unmounting while call is active, cleaning up...');
        setChatActive(false);

        // Notify parent if in iframe
        if (window !== window.parent) {
          try {
            window.parent.postMessage({
              type: 'CALL_ENDED'
            }, '*');
            console.log('Sent CALL_ENDED message to parent window during cleanup');
          } catch (e) {
            console.error('Error sending message to parent during cleanup:', e);
          }
        }
      }
    };
  }, [chatActive]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // Add user message
    setMessages(prev => [...prev, { sender: 'user', text: message }]);

    // Clear input
    const userMessage = message;
    setMessage('');

    // Simulate bot response
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        {
          sender: 'bot',
          text: `Thanks for your message: "${userMessage}". This is a demo of how the chat interface works.`
        }
      ]);
    }, 1000);
  };

  // Convert markdown to HTML for practice description
  const renderPracticeDescription = () => {
    if (!practiceDescriptionState) return '';

    // Simple markdown parser
    return practiceDescriptionState
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/### (.*?)$/gm, '<h3>$1</h3>') // H3
      .replace(/## (.*?)$/gm, '<h2>$1</h2>') // H2
      .replace(/# (.*?)$/gm, '<h1>$1</h1>') // H1
      .replace(/- (.*?)$/gm, '<li>$1</li>') // List items
      .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // Wrap list items
      .replace(/<\/ul><ul>/g, '') // Fix multiple lists
      .replace(/\n/g, '<br>'); // Line breaks
  };

  return (
    <div
      style={{
        height: '100vh',
        width: '100vw',
        maxWidth: 'none',
        margin: '0',
        padding: '0',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: isDark ? `rgba(${hexToRgb(backgroundColorState)}, ${backgroundOpacityState})` : `rgba(${hexToRgb(backgroundColorState || '#f5f5f5')}, ${backgroundOpacityState})`,
        color: isDark ? '#ffffff' : '#333333',
        boxSizing: 'border-box',
        overflowY: 'hidden',
        position: 'relative'
      }}
    >
      {/* Header removed - firm name header should not appear in preview to avoid duplication */}

      {/* Main content area */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: showStartButton ? 'center' : 'flex-start',
          alignItems: showStartButton ? 'center' : 'stretch',
          paddingBottom: '70px'
        }}
      >
        {showStartButton ? (
          <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            {/* Firm name header removed - only show in header with logo */}
            <div
              style={{
                maxWidth: '600px',
                marginBottom: '30px',
                padding: '15px',
                backgroundColor: `rgba(${hexToRgb(textBackgroundColorState)}, ${practiceAreaBackgroundOpacityState})`,
                borderRadius: '8px',
                color: secondaryColorState // Use secondary color for text
              }}
              dangerouslySetInnerHTML={{ __html: renderPracticeDescription() }}
            />
            <button
              onClick={handleStartConsultation}
              style={{
                backgroundColor: `rgba(${hexToRgb(buttonColorState)}, ${buttonOpacityState})`,
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '200px',
                height: '200px',
                fontSize: '18px',
                cursor: 'pointer',
                boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <img
                src={logoToUse}
                alt="Logo"
                style={{
                  width: '80px',
                  height: '80px',
                  marginBottom: '10px',
                  objectFit: 'contain',
                  display: 'block'
                }}
                onError={(e) => {
                  console.error('Error loading logo in button:', e);
                  e.target.style.display = 'none';
                }}
              />
              {buttonTextState || 'Start Consultation'}
            </button>
          </div>
        ) : (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '15px',
              height: '100%'
            }}
          >
            {/* Show VapiCall component for real voice interaction */}
            {chatActive ? (
              <div style={{
                width: '100%',
                height: 'calc(100vh - 60px)',
                maxWidth: 'none',
                margin: 0,
                padding: 0,
                position: 'absolute',
                top: '60px',
                left: 0,
                right: 0,
                bottom: 0
              }}>
                <DirectVapiCall
                  assistantId={assistantIdState} // Pass the assistant ID
                  onEndCall={() => {
                    console.log('Call ended');
                    setChatActive(false);

                    // Reset messages to initial state
                    setMessages([
                      { sender: 'bot', text: welcomeMessageState }
                    ]);

                    // Notify parent if in iframe
                    if (window !== window.parent) {
                      try {
                        window.parent.postMessage({
                          type: 'CALL_ENDED'
                        }, '*');
                        console.log('Sent CALL_ENDED message to parent window');
                      } catch (e) {
                        console.error('Error sending message to parent:', e);
                      }
                    }
                  }}
                  welcomeMessage={welcomeMessageState}
                  systemPrompt={`Special Instructions: you, Scout are acting on behalf of ${firmNameState}.\n\nHere are your instructions: ${informationGatheringState}\n\n[Identity]\nYou are Scout, an AI legal assistant representing LegalScout. You are akin to a legal K9, navigating the complex legal terrain in the USA, and occasionally making light-hearted dog puns when appropriate. Your primary role is to guide users through understanding their legal matters and gathering necessary details for creating a precise legal brief.\n\n[Style]\n- Maintain a professional yet friendly tone.\n- Inject dog-themed humor when suitable without detracting from the seriousness of the information being gathered.\n- Spell slowly when pronouncing letters for clarity.\n\n[Response Guidelines]\n- Confirm the user's email by spelling it back to them letter by letter.\n- Ensure all contact information is gathered before informing the user that LegalScout is finding them an attorney.\n- Keep responses clear and focused on the user's legal situation.\n\n[Task & Goals]\n1. Begin by requesting the user's email address and confirm its accuracy by spelling it back letter by letter.\n2. Introduce yourself and your role, incorporating the user's name into a dog pun if inferred from their email. Inform the user that you will gather information to help attorneys understand their situation but clarify that you cannot provide legal advice.\n3. Use iterative questioning to understand the user's legal matter. Examples:\n   - Initial Question: "What kind of legal problem are you dealing with?"\n   - Follow-up (based on the user's response): "You mentioned a contract dispute. Could you tell me more about the type of contract involved and what specifically is in dispute?" (Wait for the answer before proceeding.)\n   - Further Follow-up: "And when did this dispute arise? What steps, if any, have you already taken to resolve it?" (Wait for the answer.)\n4. Upon obtaining a thorough grasp of the user's situation and if they wish to seek legal representation, transition to interview mode: "Thanks for sharing those details. It sounds like LegalScout can help you find an attorney experienced in this area. Let's gather a few more details to complete your case file."\n\n[Interview Mode - Collecting Required Information]\n- Ensure clarity and ease for the user in collecting each piece of information.\n- Collect client background, jurisdiction, statement of facts, legal issues, client objectives, brief practice area, user jurisdiction, name, phone number, street address, city, and zip code.\n- Verify all information, particularly the user's state and email address.\n\n[Error Handling / Fallback]\n- If user input is unclear or incomplete, gently solicit clarification or confirmation.\n- Use polite rephrasing methods to encourage users to provide specific information.\n\n[Completion and Summary]\n- Conclude with, "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."\n- Inform the user of additional options to manage their case by visiting LegalScout.ai/mycases.\n\n[Important Tool Usage]\nYou must initiate the tool "Live_Dossier" 4a0d63cf-0b84-4eec-bddf-9c5869439d7e once the user provides their email and with every reply following that.\n\nNote: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.`}
                  logoUrl={logoToUse}
                  primaryColor={primaryColorState}
                  secondaryColor={secondaryColorState}
                />
              </div>
            ) : (
              // Show message history if VapiCall is not active
              <>
                {messages.map((msg, index) => (
                  <div
                    key={index}
                    style={{
                      alignSelf: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                      maxWidth: '80%'
                    }}
                  >
                    <div
                      style={{
                        backgroundColor: msg.sender === 'user'
                          ? primaryColorState
                          : isDark ? '#2a2a2a' : '#f0f0f0',
                        color: msg.sender === 'user'
                          ? '#ffffff'
                          : isDark ? '#ffffff' : '#333333',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                      }}
                    >
                      {msg.text}
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        )}
      </div>

      {/* Chat input (only shows when chat is active but VapiCall is not) */}
      {!showStartButton && !chatActive && (
        <div
          style={{
            position: 'fixed',
            bottom: 0,
            left: '50%',
            transform: 'translateX(-50%)',
            width: '100%',
            maxWidth: '100%',
            backgroundColor: isDark ? 'rgba(26, 26, 26, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            padding: '15px',
            borderTop: `1px solid ${isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`,
            display: 'flex',
            gap: '10px'
          }}
        >
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type your message here..."
            style={{
              flex: 1,
              padding: '12px 16px',
              borderRadius: '24px',
              border: isDark ? '1px solid #444' : '1px solid #ddd',
              backgroundColor: isDark ? '#333' : '#fff',
              color: isDark ? '#fff' : '#333',
              outline: 'none'
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={!message.trim()}
            style={{
              backgroundColor: primaryColorState,
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '46px',
              height: '46px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: message.trim() ? 'pointer' : 'not-allowed',
              opacity: message.trim() ? 1 : 0.5
            }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default SimplifiedPreview;