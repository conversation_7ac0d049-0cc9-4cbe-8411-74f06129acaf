-- Add enhanced CRM fields to attorneys table
ALTER TABLE public.attorneys
ADD COLUMN IF NOT EXISTS success_evaluation_prompt TEXT,
ADD COLUMN IF NOT EXISTS summary_prompt TEXT,
ADD COLUMN IF NOT EXISTS structured_data_prompt TEXT,
ADD COLUMN IF NOT EXISTS structured_data_schema JSONB;

-- Create a new table for assistant templates
CREATE TABLE IF NOT EXISTS public.assistant_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Template information
  name TEXT NOT NULL,
  description TEXT,
  attorney_id UUID REFERENCES public.attorneys(id),
  
  -- Template configuration
  success_prompt TEXT,
  summary_prompt TEXT,
  structured_data_prompt TEXT,
  structured_data_schema JSONB,
  assistant_config <PERSON><PERSON><PERSON><PERSON>,
  
  -- Metadata
  is_public BOOLEAN DEFAULT false,
  category TEXT
);

-- Add RLS policies for assistant_templates
ALTER TABLE public.assistant_templates ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view templates
CREATE POLICY "Users can view templates" ON public.assistant_templates
  FOR SELECT USING (
    is_public = true OR 
    auth.uid()::TEXT = attorney_id::TEXT OR 
    auth.role() = 'authenticated'
  );

-- Create policy to allow users to create templates
CREATE POLICY "Users can create templates" ON public.assistant_templates
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Create policy to allow users to update their own templates
CREATE POLICY "Users can update their own templates" ON public.assistant_templates
  FOR UPDATE USING (auth.uid()::TEXT = attorney_id::TEXT OR auth.role() = 'authenticated');

-- Add trigger to update updated_at column
CREATE TRIGGER set_assistant_templates_updated_at
BEFORE UPDATE ON public.assistant_templates
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
