import { defaultVapiConfig } from '../config/vapiDefaults';
import { safeVapiConfig } from '../utils/vapiHelpers';

export const configureVapi = (attorneyConfig) => {
  const baseConfig = {
    model: 'gpt-4o',
    voice: {
      provider: 'openai',
      voiceId: 'alloy',
      speed: 1.0
    },
    firstMessage: 'How can I help you today?',
    ...defaultVapiConfig
  };

  const attorneyOverrides = {
    model: attorneyConfig?.vapiModel,
    voice: {
      provider: attorneyConfig?.voiceProvider,
      voiceId: attorneyConfig?.voiceId,
      speed: attorneyConfig?.voiceSpeed
    },
    firstMessage: attorneyConfig?.customGreeting
  };

  return vapi.configure(
    safeVapiConfig(baseConfig, attorneyOverrides)
  );
};