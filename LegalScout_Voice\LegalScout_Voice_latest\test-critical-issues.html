<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout Critical Issues Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pending { background: #f39c12; }
        .status-running { background: #3498db; animation: pulse 1s infinite; }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 LegalScout Critical Issues Test Suite</h1>
            <p>Comprehensive testing for production deployment issues</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3><span class="status-indicator status-pending" id="status-overall"></span>Overall Test Status</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="overall-progress"></div>
                </div>
                <button class="test-button success" onclick="runAllTests()">🚀 Run All Tests</button>
                <button class="test-button" onclick="clearConsole()">🧹 Clear Console</button>
                <button class="test-button danger" onclick="location.reload()">🔄 Reload Page</button>
            </div>
            
            <div class="test-section">
                <h3><span class="status-indicator status-pending" id="status-react"></span>React Context Issues</h3>
                <p>Tests and fixes React Context creation errors (createContext undefined)</p>
                <button class="test-button" onclick="runReactContextTest()">🔧 Test React Context</button>
                <button class="test-button" onclick="fixReactContext()">🛠️ Apply React Fixes</button>
            </div>
            
            <div class="test-section">
                <h3><span class="status-indicator status-pending" id="status-vapi"></span>Vapi SDK Loading</h3>
                <p>Tests Vapi SDK loading from multiple sources and creates fallbacks</p>
                <button class="test-button" onclick="runVapiSDKTest()">🚀 Test Vapi SDK</button>
                <button class="test-button" onclick="loadVapiFallback()">🔄 Load Fallback</button>
            </div>
            
            <div class="test-section">
                <h3><span class="status-indicator status-pending" id="status-critical"></span>Critical Issues Suite</h3>
                <p>Comprehensive test of all critical issues identified in logs</p>
                <button class="test-button" onclick="runCriticalIssuesTest()">🔍 Run Critical Tests</button>
                <button class="test-button" onclick="generateDiagnosticReport()">📊 Generate Report</button>
            </div>
            
            <div class="test-section">
                <h3><span class="status-indicator status-pending" id="status-assistant"></span>Assistant Cleanup</h3>
                <p>Tests and fixes assistant deletion and mapping issues</p>
                <button class="test-button" onclick="testAssistantCleanup()">🗑️ Test Cleanup</button>
                <button class="test-button danger" onclick="runAssistantPurge()">⚠️ Run Purge (Careful!)</button>
            </div>
            
            <div class="console-output" id="console-output">
🎯 LegalScout Critical Issues Test Suite Ready
==============================================

Click "Run All Tests" to start comprehensive testing, or run individual test suites.

Available Tests:
- React Context Issues (createContext undefined errors)
- Vapi SDK Loading (CDN and fallback loading)
- Critical Issues Suite (comprehensive log analysis)
- Assistant Cleanup (orphaned assistant handling)

Console output will appear here...
            </div>
        </div>
    </div>

    <!-- Load test scripts -->
    <script src="tests/react-context-fix-test.js"></script>
    <script src="tests/vapi-sdk-loading-test.js"></script>
    <script src="tests/critical-issues-test-suite.js"></script>

    <script>
        let testProgress = 0;
        let totalTests = 4;
        
        function updateStatus(testId, status) {
            const indicator = document.getElementById(`status-${testId}`);
            if (indicator) {
                indicator.className = `status-indicator status-${status}`;
            }
        }
        
        function updateProgress() {
            testProgress++;
            const percentage = (testProgress / totalTests) * 100;
            document.getElementById('overall-progress').style.width = percentage + '%';
            
            if (testProgress >= totalTests) {
                updateStatus('overall', 'success');
                logToConsole('🎉 All tests completed!');
            }
        }
        
        function logToConsole(message) {
            const output = document.getElementById('console-output');
            output.textContent += '\n' + message;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console-output').textContent = '🧹 Console cleared\n';
        }
        
        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole('📝 ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole('❌ ' + args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole('⚠️ ' + args.join(' '));
        };
        
        async function runAllTests() {
            logToConsole('🚀 Starting comprehensive test suite...');
            updateStatus('overall', 'running');
            testProgress = 0;
            
            try {
                await runReactContextTest();
                await runVapiSDKTest();
                await runCriticalIssuesTest();
                await testAssistantCleanup();
                
                logToConsole('✅ All tests completed successfully!');
                updateStatus('overall', 'success');
            } catch (error) {
                logToConsole('❌ Test suite failed: ' + error.message);
                updateStatus('overall', 'error');
            }
        }
        
        async function runReactContextTest() {
            logToConsole('\n🔧 Running React Context Test...');
            updateStatus('react', 'running');
            
            try {
                if (typeof ReactContextFixTest !== 'undefined') {
                    const test = new ReactContextFixTest();
                    await test.runTest();
                    updateStatus('react', 'success');
                } else {
                    logToConsole('❌ ReactContextFixTest not loaded');
                    updateStatus('react', 'error');
                }
                updateProgress();
            } catch (error) {
                logToConsole('❌ React Context test failed: ' + error.message);
                updateStatus('react', 'error');
            }
        }
        
        async function runVapiSDKTest() {
            logToConsole('\n🚀 Running Vapi SDK Test...');
            updateStatus('vapi', 'running');
            
            try {
                if (typeof VapiSDKLoadingTest !== 'undefined') {
                    const test = new VapiSDKLoadingTest();
                    await test.runTest();
                    updateStatus('vapi', 'success');
                } else {
                    logToConsole('❌ VapiSDKLoadingTest not loaded');
                    updateStatus('vapi', 'error');
                }
                updateProgress();
            } catch (error) {
                logToConsole('❌ Vapi SDK test failed: ' + error.message);
                updateStatus('vapi', 'error');
            }
        }
        
        async function runCriticalIssuesTest() {
            logToConsole('\n🔍 Running Critical Issues Test...');
            updateStatus('critical', 'running');
            
            try {
                if (typeof CriticalIssuesTestSuite !== 'undefined') {
                    const test = new CriticalIssuesTestSuite();
                    await test.runAllTests();
                    updateStatus('critical', 'success');
                } else {
                    logToConsole('❌ CriticalIssuesTestSuite not loaded');
                    updateStatus('critical', 'error');
                }
                updateProgress();
            } catch (error) {
                logToConsole('❌ Critical issues test failed: ' + error.message);
                updateStatus('critical', 'error');
            }
        }
        
        async function testAssistantCleanup() {
            logToConsole('\n🗑️ Testing Assistant Cleanup...');
            updateStatus('assistant', 'running');
            
            try {
                // Basic assistant cleanup test
                if (typeof window.vapiAssistantCleanup !== 'undefined') {
                    logToConsole('✅ Assistant cleanup utility available');
                    const analysis = await window.vapiAssistantCleanup.analyzeAssistants();
                    logToConsole('📊 Assistant analysis completed');
                } else {
                    logToConsole('⚠️ Assistant cleanup utility not loaded');
                }
                
                updateStatus('assistant', 'success');
                updateProgress();
            } catch (error) {
                logToConsole('❌ Assistant cleanup test failed: ' + error.message);
                updateStatus('assistant', 'error');
            }
        }
        
        function fixReactContext() {
            logToConsole('🛠️ Applying React Context fixes...');
            if (typeof window.createSafeReactContext !== 'undefined') {
                logToConsole('✅ React Context fixes already applied');
            } else {
                logToConsole('⚠️ React Context fixes not available - run test first');
            }
        }
        
        function loadVapiFallback() {
            logToConsole('🔄 Loading Vapi fallback...');
            if (typeof window.Vapi !== 'undefined') {
                logToConsole('✅ Vapi SDK already loaded');
            } else {
                logToConsole('🔧 Creating Vapi fallback...');
                // This would be handled by the VapiSDKLoadingTest
                runVapiSDKTest();
            }
        }
        
        function generateDiagnosticReport() {
            logToConsole('\n📊 Generating Diagnostic Report...');
            logToConsole('================================');
            
            const report = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                reactAvailable: typeof React !== 'undefined',
                vapiAvailable: typeof window.Vapi !== 'undefined',
                supabaseAvailable: typeof window.supabase !== 'undefined',
                testUtilitiesLoaded: {
                    reactContextTest: typeof ReactContextFixTest !== 'undefined',
                    vapiSDKTest: typeof VapiSDKLoadingTest !== 'undefined',
                    criticalIssuesTest: typeof CriticalIssuesTestSuite !== 'undefined'
                }
            };
            
            logToConsole(JSON.stringify(report, null, 2));
            logToConsole('📋 Report generated - copy from console if needed');
        }
        
        function runAssistantPurge() {
            if (confirm('⚠️ This will attempt to delete orphaned assistants. Are you sure?')) {
                logToConsole('🗑️ Running assistant purge...');
                if (typeof window.vapiAssistantCleanup !== 'undefined') {
                    window.vapiAssistantCleanup.runCleanup({ dryRun: false });
                } else {
                    logToConsole('❌ Assistant cleanup utility not available');
                }
            }
        }
        
        // Auto-start basic diagnostics
        setTimeout(() => {
            logToConsole('\n🔍 Running initial diagnostics...');
            generateDiagnosticReport();
        }, 1000);
    </script>
</body>
</html>
