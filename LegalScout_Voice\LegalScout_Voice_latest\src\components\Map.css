/* Map Component Styles - Modern, compatible overrides for Leaflet */

/* Base map container styles */
.leaflet-container {
  width: 100%;
  height: 100%;
  background-color: #1a1a1a;
  position: relative;
  z-index: 1;
}

/* User selection - modern browsers */
.leaflet-container,
.leaflet-control-container,
.leaflet-popup-content-wrapper,
.leaflet-popup-tip,
.leaflet-tooltip {
  -webkit-user-select: text;
  user-select: text;
}

/* Map controls */
.leaflet-control-container {
  position: relative;
  z-index: 800;
  pointer-events: auto;
}

.leaflet-control-zoom {
  background-color: rgba(26, 26, 26, 0.9);
  border: 1px solid #333;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.leaflet-control-zoom a {
  color: #fff;
  background-color: rgba(59, 130, 246, 0.7);
  border: none;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.leaflet-control-zoom a:hover {
  background-color: rgba(59, 130, 246, 1);
}

/* Map attribution */
.leaflet-control-attribution {
  background-color: rgba(26, 26, 26, 0.9);
  color: #fff;
  border: 1px solid #333;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.leaflet-control-attribution a {
  color: #3b82f6;
  text-decoration: none;
}

/* Map tiles */
.leaflet-tile {
  filter: brightness(0.8) contrast(1.2) saturate(0.8);
  mix-blend-mode: normal;
}

/* Popups */
.leaflet-popup-content-wrapper {
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid #333;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
}

.leaflet-popup-tip {
  background-color: #1a1a1a;
  border-top-color: #333;
}

.leaflet-popup-close-button {
  color: #fff;
  transition: color 0.2s ease;
}

.leaflet-popup-close-button:hover {
  color: #3b82f6;
}

/* Tooltips */
.leaflet-tooltip {
  background-color: rgba(26, 26, 26, 0.9);
  color: #fff;
  border: 1px solid #333;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: auto;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .leaflet-control-zoom {
    display: none;
  }
  
  .leaflet-control-attribution {
    font-size: 10px;
  }
}

/* General Map Styles */

/* Full-screen map container */
.globe-map-container {
  position: absolute;
  top: 60px; /* Start below navbar */
  left: 0;
  width: 100vw;
  height: calc(100vh - 60px);
  overflow: hidden;
  z-index: 0;
  background-color: #000;
  margin: 0;
  padding: 0;
}

/* Globe container */
.globe-container {
  position: fixed;
  top: 60px; /* Start below navbar */
  left: 0;
  width: 100vw;
  height: calc(100vh - 60px);
  pointer-events: none;
  z-index: 0;
  margin: 0;
  padding: 0;
}

/* Globe canvas styling */
.globe-container canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  touch-action: none;
}

/* Hide globe when not visible */
.globe-hidden {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

/* Country highlighting on globe */
.highlighted-country {
  fill: rgba(67, 126, 255, 0.7);
  stroke: #4169E1;
  stroke-width: 1;
}

/* Location marker styling */
.location-marker {
  width: 20px;
  height: 20px;
  background-color: rgba(255, 165, 0, 0.8);
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 10px rgba(255, 165, 0, 0.6);
  transform: translate(-50%, -50%);
}

/* Location info popup */
.location-info-popup {
  position: absolute;
  background-color: rgba(10, 20, 40, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 10;
  font-size: 14px;
  max-width: 250px;
  pointer-events: auto;
}

/* Animation for globe rotation */
@keyframes globe-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Globe view information panel */
.globe-info-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(10, 20, 40, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  z-index: 5;
  pointer-events: auto;
  max-width: 90%;
  width: 300px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .globe-info-panel {
    bottom: 70px;
    padding: 8px 12px;
    font-size: 12px;
    width: 250px;
  }
  
  .location-info-popup {
    padding: 8px 12px;
    font-size: 12px;
    max-width: 200px;
  }
} 