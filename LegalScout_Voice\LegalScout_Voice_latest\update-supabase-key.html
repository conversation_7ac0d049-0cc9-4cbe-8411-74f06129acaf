<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Update Supabase Key</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .success {
      color: green;
      font-weight: bold;
    }
    .error {
      color: red;
      font-weight: bold;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    input {
      padding: 8px;
      margin: 5px 0;
      width: 100%;
      box-sizing: border-box;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Update Supabase Key</h1>
  
  <div class="container">
    <h2>Current Configuration</h2>
    <div>
      <label for="supabaseUrl">Supabase URL:</label>
      <input type="text" id="supabaseUrl" value="https://utopqxsvudgrtiwenlzl.supabase.co" readonly>
    </div>
    <div>
      <label for="currentKey">Current Supabase Key (first and last 10 chars):</label>
      <input type="text" id="currentKey" readonly>
    </div>
  </div>
  
  <div class="container">
    <h2>Update Key</h2>
    <div>
      <label for="newKey">New Supabase Anon Key:</label>
      <input type="text" id="newKey" placeholder="Enter your new Supabase anon key">
    </div>
    <button id="updateKey">Update Key</button>
    <div id="updateResult"></div>
  </div>
  
  <div class="container">
    <h2>Test Connection</h2>
    <button id="testConnection">Test Connection</button>
    <div id="connectionResult"></div>
  </div>
  
  <div class="container">
    <h2>Results</h2>
    <pre id="results">No results yet</pre>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get DOM elements
      const currentKeyInput = document.getElementById('currentKey');
      const newKeyInput = document.getElementById('newKey');
      const updateKeyBtn = document.getElementById('updateKey');
      const testConnectionBtn = document.getElementById('testConnection');
      const updateResult = document.getElementById('updateResult');
      const connectionResult = document.getElementById('connectionResult');
      const resultsElement = document.getElementById('results');
      
      // Get current key from localStorage or .env
      const currentKey = localStorage.getItem('supabaseKey') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs';
      
      // Display current key (first and last 10 chars)
      if (currentKey) {
        currentKeyInput.value = `${currentKey.substring(0, 10)}...${currentKey.substring(currentKey.length - 10)}`;
      } else {
        currentKeyInput.value = 'No key found';
      }
      
      // Update key
      updateKeyBtn.addEventListener('click', function() {
        const newKey = newKeyInput.value.trim();
        
        if (!newKey) {
          updateResult.innerHTML = '<span class="error">Please enter a new key</span>';
          return;
        }
        
        // Store the new key in localStorage
        localStorage.setItem('supabaseKey', newKey);
        
        // Update the current key display
        currentKeyInput.value = `${newKey.substring(0, 10)}...${newKey.substring(newKey.length - 10)}`;
        
        // Show success message
        updateResult.innerHTML = '<span class="success">Key updated successfully!</span>';
        
        // Show instructions for updating .env files
        resultsElement.textContent = `
To update your .env files with the new key:

1. Open .env file and replace the current key with:
VITE_SUPABASE_KEY=${newKey}
REACT_APP_SUPABASE_KEY=${newKey}

2. Open .env.development file and replace the current key with:
VITE_SUPABASE_KEY=${newKey}

3. Restart your development server
`;
      });
      
      // Test connection
      testConnectionBtn.addEventListener('click', async function() {
        connectionResult.innerHTML = 'Testing connection...';
        resultsElement.textContent = 'Testing...';
        
        const supabaseUrl = document.getElementById('supabaseUrl').value;
        const key = localStorage.getItem('supabaseKey') || currentKey;
        
        try {
          // Create a simple fetch request to test the connection
          const response = await fetch(`${supabaseUrl}/rest/v1/attorneys?limit=1`, {
            headers: {
              'apikey': key,
              'Authorization': `Bearer ${key}`
            }
          });
          
          const data = await response.json();
          
          if (response.ok) {
            connectionResult.innerHTML = '<span class="success">Connection successful!</span>';
            resultsElement.textContent = JSON.stringify(data, null, 2);
          } else {
            connectionResult.innerHTML = `<span class="error">Connection failed: ${data.message || response.statusText}</span>`;
            resultsElement.textContent = JSON.stringify(data, null, 2);
          }
        } catch (error) {
          connectionResult.innerHTML = `<span class="error">Connection failed: ${error.message}</span>`;
          resultsElement.textContent = JSON.stringify(error, null, 2);
        }
      });
    });
  </script>
</body>
</html>
