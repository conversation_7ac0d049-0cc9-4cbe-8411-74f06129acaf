<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LegalScout Test Environment</title>
  
  <!-- Minimal CSP for testing -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:;
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https: data: blob:;
    style-src 'self' 'unsafe-inline' https: data:;
    img-src 'self' data: blob: https:;
    font-src 'self' data: https:;
    connect-src 'self' https: wss: ws:;
    media-src 'self' data: blob: https:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">
  
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f5f5f5;
    }
    
    #root {
      min-height: 100vh;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-size: 18px;
      color: #666;
    }
    
    .error {
      padding: 20px;
      background: #ffebee;
      color: #c62828;
      font-family: monospace;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <!-- Loading indicator -->
  <div id="loading" class="loading">
    🔄 Loading LegalScout Test Environment...
  </div>
  
  <!-- Main app container -->
  <div id="root"></div>
  
  <!-- Environment injection for testing -->
  <script>
    console.log('🚀 Test Environment Starting...');
    
    // Inject environment variables for testing
    window.VITE_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
    window.VITE_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
    window.VITE_VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
    window.VITE_VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
    
    // Ensure process.env exists
    if (!window.process) {
      window.process = { env: {}, browser: true };
    }
    
    // Copy to process.env for compatibility
    window.process.env.VITE_SUPABASE_URL = window.VITE_SUPABASE_URL;
    window.process.env.VITE_SUPABASE_KEY = window.VITE_SUPABASE_KEY;
    window.process.env.VITE_VAPI_PUBLIC_KEY = window.VITE_VAPI_PUBLIC_KEY;
    window.process.env.VITE_VAPI_SECRET_KEY = window.VITE_VAPI_SECRET_KEY;
    window.process.env.NODE_ENV = 'development';
    
    console.log('✅ Environment variables injected');
    
    // Global error handlers
    window.addEventListener('error', (event) => {
      console.error('🚨 Global Error:', event.error);
      hideLoading();
      showError(`Global Error: ${event.error?.message || 'Unknown error'}\n\nStack: ${event.error?.stack || 'No stack trace'}`);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 Unhandled Promise Rejection:', event.reason);
      hideLoading();
      showError(`Unhandled Promise Rejection: ${event.reason}`);
    });
    
    function hideLoading() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    }
    
    function showError(message) {
      const root = document.getElementById('root');
      if (root) {
        root.innerHTML = `
          <div class="error">
            <h1>❌ Test Environment Error</h1>
            <p>${message}</p>
            <h2>Environment Info:</h2>
            <pre>${JSON.stringify({
              userAgent: navigator.userAgent,
              url: window.location.href,
              timestamp: new Date().toISOString(),
              windowGlobals: {
                React: !!window.React,
                ReactDOM: !!window.ReactDOM,
                process: !!window.process
              }
            }, null, 2)}</pre>
            <button onclick="location.reload()" style="
              padding: 10px 20px;
              background: #c62828;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              margin-top: 10px;
            ">Reload Page</button>
          </div>
        `;
      }
    }
    
    // Hide loading when React takes over
    setTimeout(() => {
      const root = document.getElementById('root');
      if (root && root.children.length > 0) {
        hideLoading();
      }
    }, 2000);
  </script>
  
  <!-- Load the test app -->
  <script type="module" src="/test-main.jsx"></script>
</body>
</html>
