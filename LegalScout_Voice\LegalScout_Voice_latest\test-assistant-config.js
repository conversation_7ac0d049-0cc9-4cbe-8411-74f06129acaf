/**
 * Test script for AssistantConfigService
 * Run this in browser console to test the service
 */

// Test the AssistantConfigService
async function testAssistantConfig() {
  console.log('🧪 Testing AssistantConfigService...');
  
  try {
    // Import the service
    const { AssistantConfigService } = await import('./src/services/assistantConfigService.js');
    
    const testAssistantId = 'test-assistant-123';
    const testAttorneyId = 'test-attorney-456';
    
    console.log('1. Testing saveAssistantConfig...');
    const testConfig = {
      firmName: 'Test Law Firm',
      titleText: 'Test Assistant',
      primaryColor: '#ff0000',
      welcomeMessage: 'Hello from test!',
      voiceId: 'test-voice'
    };
    
    const savedConfig = await AssistantConfigService.saveAssistantConfig(
      testAssistantId, 
      testAttorneyId, 
      testConfig
    );
    console.log('✅ Save successful:', savedConfig);
    
    console.log('2. Testing loadAssistantConfig...');
    const loadedConfig = await AssistantConfigService.loadAssistantConfig(
      testAssistantId, 
      testAttorneyId
    );
    console.log('✅ Load successful:', loadedConfig);
    
    console.log('3. Testing updateAssistantConfig...');
    const updates = {
      primaryColor: '#00ff00',
      welcomeMessage: 'Updated welcome message!'
    };
    
    const updatedConfig = await AssistantConfigService.updateAssistantConfig(
      testAssistantId, 
      testAttorneyId, 
      updates
    );
    console.log('✅ Update successful:', updatedConfig);
    
    console.log('4. Testing deleteAssistantConfig...');
    await AssistantConfigService.deleteAssistantConfig(
      testAssistantId, 
      testAttorneyId
    );
    console.log('✅ Delete successful');
    
    console.log('🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAssistantConfig();
