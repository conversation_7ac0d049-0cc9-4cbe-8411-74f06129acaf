callback?code=bfd0ef76-c272-470d-a35c-b02d7babadeb:28 🚀 [LegalScout] Initializing environment...
callback?code=bfd0ef76-c272-470d-a35c-b02d7babadeb:50 ✅ [LegalScout] Environment initialized
callback?code=bfd0ef76-c272-470d-a35c-b02d7babadeb:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback?code=bfd0ef76-c272-470d-a35c-b02d7babadeb:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
chunk-Q72EVS5P.js?v=9711cfb6:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
supabase.js?t=1750158965174:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js?t=1750158965174:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js?t=1750158965174:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5176', hasUrl: true, hasKey: true}
supabase.js?t=1750158965174:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js?t=1750158965174:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js?t=1750151356053:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js?t=1750151356053:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=1750158965174:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:813 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:843 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:813 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:843 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
subdomainTester.js?t=1750158965174:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js?t=1750158965174:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 🔐 [AuthCallback] Starting unified OAuth callback handling...
unifiedAuthService.js?t=1750158965174:92 🔐 [UnifiedAuth] Handling OAuth callback...
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
useStandaloneAttorney.js:71 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:79 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:626 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
AuthCallback.jsx:15 🔐 [AuthCallback] Starting unified OAuth callback handling...
unifiedAuthService.js?t=1750158965174:92 🔐 [UnifiedAuth] Handling OAuth callback...
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
useStandaloneAttorney.js:71 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:79 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:626 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
assistantSyncManager.js?t=1750158965174:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:641 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:651 ✅ Supabase configured successfully
subdomainTester.js?t=1750158965174:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:671 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 2/10)
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/auth/callback', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 3/10)
 ✅ [UnifiedAuth] OAuth callback handled successfully for: <EMAIL>
 🔐 [AuthCallback] Unified auth callback successful for: <EMAIL>
 🔧 [AuthCallback] Calling fixAuthProfile...
 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AuthProfileFixer] Access token length: 1297
 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 ✅ [UnifiedAuth] OAuth callback handled successfully for: <EMAIL>
 🔐 [AuthCallback] Unified auth callback successful for: <EMAIL>
 🔧 [AuthCallback] Calling fixAuthProfile...
 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [AuthProfileFixer] Access token length: 1297
 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 4/10)
 [AuthProfileFixer] 🔍 Method 1 response status: 200
 [AuthProfileFixer] 🔍 Method 1 response data length: 1
 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
 💾 [AuthCallback] Attorney profile stored in localStorage
 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5176/dashboard
 🔐 [AuthCallback] Found OAuth tokens in URL
 Auth callback error: 
handleCallback @ AuthCallback.jsx:113
await in handleCallback
(anonymous) @ AuthCallback.jsx:134
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AuthProfileFixer] 🔍 Method 1 response status: 200
 [AuthProfileFixer] 🔍 Method 1 response data length: 1
 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
 💾 [AuthCallback] Attorney profile stored in localStorage
 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5176/dashboard
 🔐 [AuthCallback] Found OAuth tokens in URL
 Auth callback error: 
handleCallback @ AuthCallback.jsx:113
await in handleCallback
(anonymous) @ AuthCallback.jsx:134
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 5/10)
Navigated to http://localhost:5176/dashboard
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 🚀 [Supabase-Fixed] Initializing client...
 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5176', hasUrl: true, hasKey: true}
 ✅ [Supabase-Fixed] Client created successfully
 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🚀 [LegalScout] Starting React app...
 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
 ✅ [LegalScout] React app rendered successfully
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 Warning: React has detected a change in the order of Hooks called by App. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
1. useContext                 useContext
2. useContext                 useContext
3. useContext                 useContext
4. undefined                  useState
   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

    at App (http://localhost:5176/src/App.jsx?t=1750160278364:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5176/src/contexts/AuthContext.jsx?t=1750160001895:43:32)
    at InnerAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:20:30)
    at Provider (http://localhost:5176/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5176/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:28:29)
    at AttorneyStateProvider (http://localhost:5176/src/contexts/AttorneyStateContext.jsx?t=1750158965174:31:41)
    at ThemeProvider (http://localhost:5176/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5176/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5176/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js:521
error @ chunk-Q72EVS5P.js:505
warnOnHookMismatchInDev @ chunk-Q72EVS5P.js:11515
updateHookTypesDev @ chunk-Q72EVS5P.js:11485
useState @ chunk-Q72EVS5P.js:12665
useState @ chunk-2N3A5BUM.js:1066
App @ App.jsx:528
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 Warning: Internal React error: Expected static flag was missing. Please notify the React team.
    at App (http://localhost:5176/src/App.jsx?t=1750160278364:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5176/src/contexts/AuthContext.jsx?t=1750160001895:43:32)
    at InnerAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:20:30)
    at Provider (http://localhost:5176/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5176/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:28:29)
    at AttorneyStateProvider (http://localhost:5176/src/contexts/AttorneyStateContext.jsx?t=1750158965174:31:41)
    at ThemeProvider (http://localhost:5176/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5176/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5176/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js:521
error @ chunk-Q72EVS5P.js:505
renderWithHooks @ chunk-Q72EVS5P.js:11610
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 Warning: Internal React error: Expected static flag was missing. Please notify the React team.
    at App (http://localhost:5176/src/App.jsx?t=1750160278364:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5176/src/contexts/AuthContext.jsx?t=1750160001895:43:32)
    at InnerAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:20:30)
    at Provider (http://localhost:5176/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5176/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:28:29)
    at AttorneyStateProvider (http://localhost:5176/src/contexts/AttorneyStateContext.jsx?t=1750158965174:31:41)
    at ThemeProvider (http://localhost:5176/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5176/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5176/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js:521
error @ chunk-Q72EVS5P.js:505
renderWithHooks @ chunk-Q72EVS5P.js:11610
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.170Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.172Z'}
 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    at form
    at div
    at WebsiteImporter (http://localhost:5176/src/components/dashboard/WebsiteImporter.jsx:23:28)
    at form
    at div
    at ProfileTab (http://localhost:5176/src/components/dashboard/ProfileTab.jsx?t=1750160001895:31:23)
    at div
    at div
    at div
    at div
    at DashboardNew (http://localhost:5176/src/pages/DashboardNew.jsx?t=1750160001895:48:55)
    at RenderedRoute (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:5722:26)
    at Routes (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6454:3)
    at main
    at div
    at AssistantAwareProvider (http://localhost:5176/src/contexts/AssistantAwareContext.jsx?t=1750158965174:30:42)
    at App (http://localhost:5176/src/App.jsx?t=1750160278364:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5176/src/contexts/AuthContext.jsx?t=1750160001895:43:32)
    at InnerAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:20:30)
    at Provider (http://localhost:5176/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5176/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5176/src/components/SyncAuthProvider.jsx?t=1750160044462:28:29)
    at AttorneyStateProvider (http://localhost:5176/src/contexts/AttorneyStateContext.jsx?t=1750158965174:31:41)
    at ThemeProvider (http://localhost:5176/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5176/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5176/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5176/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js:521
error @ chunk-Q72EVS5P.js:505
validateDOMNesting @ chunk-Q72EVS5P.js:8267
createInstance @ chunk-Q72EVS5P.js:8339
completeWork @ chunk-Q72EVS5P.js:16311
completeUnitOfWork @ chunk-Q72EVS5P.js:19252
performUnitOfWork @ chunk-Q72EVS5P.js:19234
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T11:44:16.271Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] ⏳ Waiting for attorney manager to initialize...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: false, userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701, managerReady: false)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T11:44:16.280Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] ⏳ Waiting for attorney manager to initialize...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: false, userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701, managerReady: false)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.387Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.388Z'}
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.431Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.432Z'}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.476Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.477Z'}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 2/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 2/10)
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.871Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:16.872Z'}
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 3/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 3/10)
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 4/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 4/10)
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 5/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 5/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 6/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 6/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 7/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 7/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 8/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 8/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 9/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 9/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 10/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 10/10)
 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:353
setTimeout
(anonymous) @ DashboardNew.jsx:351
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:26.288Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:26.290Z'}
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:26.305Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:26.305Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 [useStandaloneAttorney] Max retries reached, using fallback approach
retryInitialization @ useStandaloneAttorney.js:36
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
(anonymous) @ useStandaloneAttorney.js:72
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 [useStandaloneAttorney] Max retries reached, using fallback approach
retryInitialization @ useStandaloneAttorney.js:36
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
(anonymous) @ useStandaloneAttorney.js:72
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.503Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.504Z'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-2f157a27.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 [VeryCoolAssistants] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T11:44:27.611Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] ⏳ Waiting for attorney manager to initialize...
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-2f157a27.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.673Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.677Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.679Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:27.681Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'Loading assistant URL...', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 🚀 [Supabase-Fixed] Initializing client...
 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5176', hasUrl: true, hasKey: true}
 ✅ [Supabase-Fixed] Client created successfully
 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🚀 [LegalScout] Starting React app...
 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
 ✅ [LegalScout] React app rendered successfully
 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 🚀 [Supabase-Fixed] Initializing client...
 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5176', hasUrl: true, hasKey: true}
 ✅ [Supabase-Fixed] Client created successfully
 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🚀 [LegalScout] Starting React app...
 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
 ✅ [LegalScout] React app rendered successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 [useStandaloneAttorney] Setting up retry mechanism for manager...
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 1/10)
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 2/10)
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 2/10)
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:30.820Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:30.822Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:30.966Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:30.968Z'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.036Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.037Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔄 [AssistantDataService] Loading fresh data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.074Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.075Z'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] Using fallback iframe communication
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.108Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.109Z'}
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.156Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.157Z'}
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 3/10)
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 3/10)
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [VeryCoolAssistants] Loaded assistants from centralized service: (2) [{…}, {…}]
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 primaryColor: #10b981
 secondaryColor: #059669
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.769Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:31.771Z'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 4/10)
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.006Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.007Z'}
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 4/10)
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.234Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.235Z'}
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.535Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:32.537Z'}
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.2f157a27-067c-439e-823c-f0a2bbdd66e0 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 Error loading stats for assistant 2f157a27-067c-439e-823c-f0a2bbdd66e0: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:102
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:81
loadAssistants @ VeryCoolAssistants.jsx:72
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 5/10)
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.1d3471b7-8694-4844-b3ef-e05720693efc 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 Error loading stats for assistant 1d3471b7-8694-4844-b3ef-e05720693efc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:102
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:81
loadAssistants @ VeryCoolAssistants.jsx:72
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5176/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 5/10)
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:33.085Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T11:44:33.086Z'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [AssistantDataService] Loaded Vapi data for 2f157a27-067c-439e-823c-f0a2bbdd66e0: Damon's Assistant Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 1d3471b7-8694-4844-b3ef-e05720693efc: Damon's Assistant
 ✅ [AssistantDataService] Loaded 2 assistants for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 6/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 6/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 7/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 7/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 8/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 8/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 9/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 9/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 10/10)
 [useStandaloneAttorney] Manager not ready, will retry... (attempt 10/10)
 [useStandaloneAttorney] Max retries reached, using fallback approach
retryInitialization @ useStandaloneAttorney.js:36
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
(anonymous) @ useStandaloneAttorney.js:72
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 [useStandaloneAttorney] Max retries reached, using fallback approach
retryInitialization @ useStandaloneAttorney.js:36
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
setTimeout
retryInitialization @ useStandaloneAttorney.js:62
(anonymous) @ useStandaloneAttorney.js:72
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
