/**
 * Local Deployment Testing Script
 * 
 * Tests the application locally before deployment to catch issues early
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

class LocalDeploymentTester {
  constructor() {
    this.testResults = [];
    this.errors = [];
    this.warnings = [];
    this.processes = [];
  }

  async runAllTests() {
    console.log('🚀 Local Deployment Testing Suite');
    console.log('=================================');

    try {
      await this.checkEnvironment();
      await this.testBuildProcess();
      await this.testLocalServer();
      await this.testCriticalPaths();
      await this.validateAssets();
      await this.testAPIEndpoints();
      
      this.generateReport();
      await this.cleanup();
    } catch (error) {
      console.error('❌ Local deployment test failed:', error);
      this.errors.push(`Test suite failure: ${error.message}`);
      await this.cleanup();
    }
  }

  async checkEnvironment() {
    console.log('\n🔍 Checking Environment...');
    
    try {
      // Check Node.js version
      const nodeVersion = process.version;
      console.log(`📦 Node.js version: ${nodeVersion}`);
      
      if (parseInt(nodeVersion.slice(1)) < 18) {
        this.warnings.push('Node.js version < 18, may cause issues');
      }

      // Check package.json
      if (!fs.existsSync('package.json')) {
        this.errors.push('package.json not found');
        return;
      }

      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      console.log(`📋 Project: ${packageJson.name} v${packageJson.version}`);

      // Check critical files
      const criticalFiles = [
        'src/main.jsx',
        'src/App.jsx',
        'index.html',
        'vite.config.js',
        'vercel.json'
      ];

      for (const file of criticalFiles) {
        if (fs.existsSync(file)) {
          console.log(`✅ ${file} exists`);
        } else {
          this.errors.push(`Critical file missing: ${file}`);
        }
      }

      // Check environment variables
      const requiredEnvVars = [
        'VITE_SUPABASE_URL',
        'VITE_SUPABASE_KEY',
        'VITE_VAPI_PUBLIC_KEY'
      ];

      for (const envVar of requiredEnvVars) {
        if (process.env[envVar]) {
          console.log(`✅ ${envVar} is set`);
        } else {
          this.warnings.push(`Environment variable not set: ${envVar}`);
        }
      }

      console.log('✅ Environment check completed');
      this.testResults.push('Environment Check: PASS');
    } catch (error) {
      console.error('❌ Environment check failed:', error);
      this.errors.push(`Environment Check: ${error.message}`);
    }
  }

  async testBuildProcess() {
    console.log('\n🔨 Testing Build Process...');
    
    try {
      // Test the safe build command
      console.log('🔄 Running vercel-build-safe...');
      
      const buildResult = await this.runCommand('npm run vercel-build-safe');
      
      if (buildResult.success) {
        console.log('✅ Build completed successfully');
        
        // Check if dist directory was created
        if (fs.existsSync('dist')) {
          console.log('✅ dist directory created');
          
          // Check critical build outputs
          const buildOutputs = [
            'dist/index.html',
            'dist/assets'
          ];
          
          for (const output of buildOutputs) {
            if (fs.existsSync(output)) {
              console.log(`✅ ${output} exists`);
            } else {
              this.warnings.push(`Build output missing: ${output}`);
            }
          }
          
          // Check build size
          const distStats = this.getDirectorySize('dist');
          console.log(`📊 Build size: ${(distStats / 1024 / 1024).toFixed(2)} MB`);
          
          if (distStats > 50 * 1024 * 1024) { // 50MB
            this.warnings.push('Build size is quite large (>50MB)');
          }
          
        } else {
          this.errors.push('dist directory not created after build');
        }
        
        this.testResults.push('Build Process: PASS');
      } else {
        this.errors.push(`Build failed: ${buildResult.error}`);
      }
    } catch (error) {
      console.error('❌ Build process test failed:', error);
      this.errors.push(`Build Process: ${error.message}`);
    }
  }

  async testLocalServer() {
    console.log('\n🌐 Testing Local Server...');
    
    try {
      // Start preview server
      console.log('🔄 Starting preview server...');
      
      const serverProcess = spawn('npm', ['run', 'preview'], {
        stdio: 'pipe',
        shell: true
      });
      
      this.processes.push(serverProcess);
      
      // Wait for server to start
      await this.waitForServer('http://localhost:4173', 30000);
      
      console.log('✅ Preview server started');
      
      // Test server response
      const response = await this.testURL('http://localhost:4173');
      
      if (response.success) {
        console.log('✅ Server responds correctly');
        console.log(`📊 Response time: ${response.responseTime}ms`);
        
        if (response.responseTime > 5000) {
          this.warnings.push('Server response time is slow (>5s)');
        }
        
        this.testResults.push('Local Server: PASS');
      } else {
        this.errors.push(`Server test failed: ${response.error}`);
      }
      
    } catch (error) {
      console.error('❌ Local server test failed:', error);
      this.errors.push(`Local Server: ${error.message}`);
    }
  }

  async testCriticalPaths() {
    console.log('\n🛣️ Testing Critical Paths...');
    
    try {
      const criticalPaths = [
        '/',
        '/dashboard',
        '/auth',
        '/preview',
        '/api/health'
      ];
      
      for (const path of criticalPaths) {
        const url = `http://localhost:4173${path}`;
        const response = await this.testURL(url);
        
        if (response.success) {
          console.log(`✅ ${path} - ${response.status}`);
        } else {
          this.warnings.push(`Path ${path} failed: ${response.error}`);
        }
      }
      
      console.log('✅ Critical paths tested');
      this.testResults.push('Critical Paths: TESTED');
    } catch (error) {
      console.error('❌ Critical paths test failed:', error);
      this.errors.push(`Critical Paths: ${error.message}`);
    }
  }

  async validateAssets() {
    console.log('\n📦 Validating Assets...');
    
    try {
      if (!fs.existsSync('dist')) {
        this.warnings.push('dist directory not found for asset validation');
        return;
      }
      
      // Check for common assets
      const assetPatterns = [
        'dist/assets/*.js',
        'dist/assets/*.css',
        'dist/index.html'
      ];
      
      const glob = require('glob');
      
      for (const pattern of assetPatterns) {
        const files = glob.sync(pattern);
        if (files.length > 0) {
          console.log(`✅ Found ${files.length} files matching ${pattern}`);
        } else {
          this.warnings.push(`No files found matching ${pattern}`);
        }
      }
      
      // Check for source maps (should not be in production)
      const sourceMaps = glob.sync('dist/**/*.map');
      if (sourceMaps.length > 0) {
        this.warnings.push(`Found ${sourceMaps.length} source map files in production build`);
      }
      
      console.log('✅ Asset validation completed');
      this.testResults.push('Asset Validation: COMPLETED');
    } catch (error) {
      console.error('❌ Asset validation failed:', error);
      this.errors.push(`Asset Validation: ${error.message}`);
    }
  }

  async testAPIEndpoints() {
    console.log('\n🔌 Testing API Endpoints...');
    
    try {
      // Test if API server is running
      const apiResponse = await this.testURL('http://localhost:4173/api/health');
      
      if (apiResponse.success) {
        console.log('✅ API health endpoint responds');
        this.testResults.push('API Endpoints: ACCESSIBLE');
      } else {
        this.warnings.push('API endpoints may not be accessible in preview mode');
      }
      
    } catch (error) {
      console.error('❌ API endpoint test failed:', error);
      this.warnings.push(`API Endpoints: ${error.message}`);
    }
  }

  async runCommand(command) {
    return new Promise((resolve) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          resolve({ success: false, error: error.message, stdout, stderr });
        } else {
          resolve({ success: true, stdout, stderr });
        }
      });
    });
  }

  async waitForServer(url, timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(url);
        if (response.ok) {
          return true;
        }
      } catch (error) {
        // Server not ready yet
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Server at ${url} did not start within ${timeout}ms`);
  }

  async testURL(url) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url);
      const responseTime = Date.now() - startTime;
      
      return {
        success: response.ok,
        status: response.status,
        responseTime,
        error: response.ok ? null : `HTTP ${response.status}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
      const stats = fs.statSync(currentPath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
      } else if (stats.isDirectory()) {
        const files = fs.readdirSync(currentPath);
        files.forEach(file => {
          calculateSize(path.join(currentPath, file));
        });
      }
    }
    
    if (fs.existsSync(dirPath)) {
      calculateSize(dirPath);
    }
    
    return totalSize;
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    // Kill any running processes
    for (const process of this.processes) {
      if (process && !process.killed) {
        process.kill();
        console.log('🛑 Stopped process');
      }
    }
    
    this.processes = [];
  }

  generateReport() {
    console.log('\n📊 Local Deployment Test Report');
    console.log('===============================');
    
    console.log('\n✅ Completed Tests:');
    this.testResults.forEach(result => console.log(`  - ${result}`));
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    const totalTests = this.testResults.length;
    const totalIssues = this.errors.length + this.warnings.length;
    
    console.log(`\n📈 Summary: ${totalTests} tests completed, ${this.errors.length} errors, ${this.warnings.length} warnings`);
    
    if (this.errors.length === 0) {
      console.log('🎉 Ready for deployment!');
      console.log('\n🚀 Next steps:');
      console.log('  1. git add .');
      console.log('  2. git commit -m "Ready for deployment"');
      console.log('  3. git push origin main');
      console.log('  4. Monitor Vercel deployment');
    } else {
      console.log('🚨 Fix errors before deployment');
    }
  }
}

// Run if called directly
if (require.main === module) {
  const tester = new LocalDeploymentTester();
  tester.runAllTests().catch(console.error);
}

module.exports = LocalDeploymentTester;
