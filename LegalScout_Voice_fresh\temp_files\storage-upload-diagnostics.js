/**
 * Storage Upload Diagnostics Script
 * 
 * Run this in the browser console to diagnose storage upload issues
 */

window.storageUploadDiagnostics = {
  
  async runDiagnostics() {
    console.log('🔍 [Storage Diagnostics] Starting storage upload diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      issues: [],
      fixes: [],
      recommendations: []
    };
    
    try {
      // 1. Check authentication
      console.log('🔐 [Diagnostics] Checking authentication...');
      await this.checkAuthentication(results);
      
      // 2. Check storage bucket access
      console.log('🪣 [Diagnostics] Checking storage bucket access...');
      await this.checkStorageBucketAccess(results);
      
      // 3. Check storage policies
      console.log('📋 [Diagnostics] Checking storage policies...');
      await this.checkStoragePolicies(results);
      
      // 4. Test file upload simulation
      console.log('📤 [Diagnostics] Testing file upload simulation...');
      await this.testFileUploadSimulation(results);
      
      // 5. Generate report
      this.generateReport(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ [Diagnostics] Error running diagnostics:', error);
      results.issues.push({
        type: 'diagnostic_error',
        message: error.message,
        severity: 'high'
      });
      return results;
    }
  },
  
  async checkAuthentication(results) {
    try {
      // Import Supabase client
      const { supabase } = await import('/src/lib/supabase.js');
      
      // Check current session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        results.issues.push({
          type: 'auth_error',
          message: `Authentication error: ${error.message}`,
          severity: 'high'
        });
        return;
      }
      
      if (!session?.user) {
        results.issues.push({
          type: 'no_session',
          message: 'No authenticated user session found',
          severity: 'high'
        });
        
        results.recommendations.push({
          type: 'auth_required',
          message: 'Please sign in to upload files'
        });
      } else {
        results.fixes.push({
          type: 'auth_ok',
          message: `Authenticated as: ${session.user.email}`,
          data: {
            userId: session.user.id,
            email: session.user.email,
            role: session.user.role
          }
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'auth_check_error',
        message: error.message,
        severity: 'high'
      });
    }
  },
  
  async checkStorageBucketAccess(results) {
    try {
      const { supabase } = await import('/src/lib/supabase.js');
      
      // Try to list files in the bucket
      const { data, error } = await supabase.storage
        .from('legalscout_bucket1')
        .list('', { limit: 1 });
      
      if (error) {
        results.issues.push({
          type: 'bucket_access_error',
          message: `Cannot access storage bucket: ${error.message}`,
          severity: 'high'
        });
      } else {
        results.fixes.push({
          type: 'bucket_access_ok',
          message: 'Storage bucket is accessible',
          data: { fileCount: data?.length || 0 }
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'bucket_check_error',
        message: error.message,
        severity: 'high'
      });
    }
  },
  
  async checkStoragePolicies(results) {
    try {
      const { supabase } = await import('/src/lib/supabase.js');
      
      // Try to call the debug function if it exists
      try {
        const testFileName = 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1234567890.webp';
        const { data, error } = await supabase.rpc('debug_storage_access', {
          file_path: testFileName
        });
        
        if (error) {
          results.issues.push({
            type: 'policy_debug_error',
            message: `Cannot check storage policies: ${error.message}`,
            severity: 'medium'
          });
        } else if (data && data.length > 0) {
          const policyInfo = data[0];
          results.fixes.push({
            type: 'policy_info',
            message: 'Storage policy information retrieved',
            data: policyInfo
          });
          
          if (!policyInfo.can_insert) {
            results.issues.push({
              type: 'no_insert_permission',
              message: 'User does not have permission to upload files',
              severity: 'high'
            });
          }
        }
      } catch (debugError) {
        results.recommendations.push({
          type: 'run_migration',
          message: 'Run the storage policies migration to fix upload permissions'
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'policy_check_error',
        message: error.message,
        severity: 'medium'
      });
    }
  },
  
  async testFileUploadSimulation(results) {
    try {
      // Create a small test file
      const testContent = 'test image content';
      const testFile = new Blob([testContent], { type: 'image/png' });
      testFile.name = 'test.png';
      
      const assistantId = 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d'; // Example assistant ID
      
      // Try to import and use the upload service
      const { assistantUIConfigService } = await import('/src/services/assistantUIConfigService.js');
      
      try {
        const uploadUrl = await assistantUIConfigService.uploadImage(testFile, assistantId, 'test');
        
        results.fixes.push({
          type: 'upload_test_success',
          message: 'Test file upload successful',
          data: { uploadUrl }
        });
        
        // Clean up test file
        try {
          await assistantUIConfigService.deleteImage(uploadUrl);
          console.log('Test file cleaned up successfully');
        } catch (cleanupError) {
          console.warn('Could not clean up test file:', cleanupError);
        }
        
      } catch (uploadError) {
        results.issues.push({
          type: 'upload_test_failed',
          message: `Test upload failed: ${uploadError.message}`,
          severity: 'high'
        });
        
        if (uploadError.message.includes('row-level security')) {
          results.recommendations.push({
            type: 'fix_rls_policies',
            message: 'Storage RLS policies need to be updated. Run the migration or contact admin.'
          });
        }
      }
      
    } catch (error) {
      results.issues.push({
        type: 'upload_simulation_error',
        message: error.message,
        severity: 'medium'
      });
    }
  },
  
  generateReport(results) {
    console.log('\n📊 [Storage Upload Diagnostics] REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n🕒 Timestamp: ${results.timestamp}`);
    
    if (results.issues.length > 0) {
      console.log(`\n❌ Issues Found (${results.issues.length}):`);
      results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.type}: ${issue.message}`);
        if (issue.data) {
          console.log(`     Data:`, issue.data);
        }
      });
    }
    
    if (results.fixes.length > 0) {
      console.log(`\n✅ Working Components (${results.fixes.length}):`);
      results.fixes.forEach((fix, index) => {
        console.log(`  ${index + 1}. ${fix.type}: ${fix.message}`);
        if (fix.data) {
          console.log(`     Data:`, fix.data);
        }
      });
    }
    
    if (results.recommendations.length > 0) {
      console.log(`\n💡 Recommendations (${results.recommendations.length}):`);
      results.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.type}: ${rec.message}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
    
    // Summary
    const highIssues = results.issues.filter(i => i.severity === 'high').length;
    const mediumIssues = results.issues.filter(i => i.severity === 'medium').length;
    
    if (highIssues > 0) {
      console.log(`🚨 CRITICAL: ${highIssues} high-severity issues need immediate attention`);
      console.log('💡 SOLUTION: Run the storage policies migration or check authentication');
    }
    if (mediumIssues > 0) {
      console.log(`⚠️  WARNING: ${mediumIssues} medium-severity issues should be addressed`);
    }
    if (results.issues.length === 0) {
      console.log('🎉 SUCCESS: No issues detected! Storage upload should work.');
    }
  }
};

// Auto-run diagnostics
console.log('🔧 [Storage Upload Diagnostics] Diagnostic tool loaded. Run window.storageUploadDiagnostics.runDiagnostics() to start.');
