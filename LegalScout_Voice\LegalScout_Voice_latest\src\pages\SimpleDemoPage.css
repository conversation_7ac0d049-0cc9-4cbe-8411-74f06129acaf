/* Root variables for the configuration panel only */
:root {
  --primary-color: #4B74AA;
  --secondary-color: #607D8B;
  --background-color: #ffffff;
  --background-color-rgb: 255, 255, 255;
  --border-color: #E0E7EF;
  --text-primary: #37474F;
  --text-secondary: #607D8B;
  --shadow-soft: 0 4px 12px rgba(0, 20, 50, 0.04);
  --shadow-medium: 0 8px 24px rgba(0, 20, 50, 0.06);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --transition-default: all 0.3s ease;

  /* Derived colors */
  --primary-hover: color-mix(in srgb, var(--primary-color) 85%, black);
  --secondary-hover: color-mix(in srgb, var(--secondary-color) 85%, black);

  --preview-primary-color: #4B74AA;
  --preview-secondary-color: #607D8B;
  --preview-background-color: #ffffff;

  /* Modern Dark theme colors */
  --dark-bg: #0F1218;
  --dark-bg-rgb: 15, 18, 24;
  --dark-card-bg: #1A202C;
  --dark-accent: #5B8DEF;
  --dark-text-primary: rgba(255, 255, 255, 0.95);
  --dark-text-secondary: rgba(255, 255, 255, 0.7);
  --dark-border: rgba(255, 255, 255, 0.1);
  --dark-input-bg: rgba(30, 41, 59, 0.6);
}

/* Modern dark theme variables */
:root[data-theme="dark"] {
  --dark-bg: #121212;
  --dark-card-bg: rgba(18, 18, 20, 0.5);
  --dark-accent: #64B5F6;
  --dark-accent-hover: #90CAF9;
  --dark-text-primary: rgba(255, 255, 255, 0.95);
  --dark-text-secondary: rgba(255, 255, 255, 0.7);
  --dark-border: rgba(100, 181, 246, 0.2);
  --dark-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  --dark-input-bg: rgba(24, 24, 28, 0.4);
  --dark-glass-border: rgba(100, 181, 246, 0.25);
}

/* When in dark mode - Enhanced modern styling */
[data-theme="dark"] .config-container {
  background-color: rgba(15, 18, 24, 0.15);
  color: var(--dark-text-primary);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .config-tab {
  color: var(--dark-text-secondary);
}

[data-theme="dark"] .config-tab:hover {
  background-color: rgba(100, 181, 246, 0.08);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .config-tab.active {
  color: var(--dark-accent);
}

[data-theme="dark"] .config-tab.active::after {
  background: var(--dark-accent);
}

[data-theme="dark"] .input-group label {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 5px rgba(100, 181, 246, 0.3);
}

[data-theme="dark"] .input-group input,
[data-theme="dark"] .input-group select,
[data-theme="dark"] .input-group textarea {
  background-color: rgba(24, 24, 28, 0.3);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .input-group input:focus,
[data-theme="dark"] .input-group select:focus,
[data-theme="dark"] .input-group textarea:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.2), 0 0 15px rgba(100, 181, 246, 0.2);
  background-color: rgba(30, 41, 59, 0.4);
}

[data-theme="dark"] .input-group small {
  color: var(--dark-text-secondary);
}

.config-mode-section {
  background: transparent;
  border-radius: var(--radius-medium);
  border: none;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 700px;
  width: 100%;
  transition: all 0.3s ease;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  scroll-snap-align: start;
  scroll-margin-top: 0;
  box-shadow: none;
}

[data-theme="dark"] .config-mode-section {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Glass effect for modern transparent look - dark mode */
[data-theme="dark"] .glass-effect {
  background: rgba(18, 18, 20, 0.2);
  border: 1px solid var(--dark-glass-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  width: 100%;
  margin: 0 auto;
}

[data-theme="dark"] .glass-effect:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(100, 181, 246, 0.3);
  border-color: rgba(100, 181, 246, 0.4);
  transform: translateY(-2px);
  animation: glow-border 1.5s infinite alternate;
}

@keyframes glow-border {
  0% {
    border-color: rgba(100, 181, 246, 0.2);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 5px rgba(100, 181, 246, 0.2);
  }
  100% {
    border-color: rgba(100, 181, 246, 0.7);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 15px rgba(100, 181, 246, 0.6);
  }
}

/* Modern button dark mode enhancements */
[data-theme="dark"] .modern-button {
  background: rgba(100, 181, 246, 0.15) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(100, 181, 246, 0.2) !important;
  color: var(--dark-accent) !important;
  border: none !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

[data-theme="dark"] .modern-button:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(100, 181, 246, 0.4) !important;
  background: rgba(100, 181, 246, 0.25) !important;
  transform: translateY(-2px) !important;
}

/* Modern input styles for dark mode */
[data-theme="dark"] .modern-input,
[data-theme="dark"] .modern-select {
  background: rgba(18, 18, 20, 0.4);
  border: 1px solid var(--dark-glass-border);
  color: var(--dark-text-primary);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

[data-theme="dark"] .modern-input:focus,
[data-theme="dark"] .modern-select:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.2);
}

/* Updated divider for dark mode */
[data-theme="dark"] .divider-line {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .divider-text {
  color: var(--dark-accent);
}

/* Modern input styles for dark mode */
[data-theme="dark"] .modern-input {
  background: var(--dark-input-bg);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .modern-input:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.15), inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Modern select styles for dark mode */
[data-theme="dark"] .modern-select {
  background-color: var(--dark-input-bg);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .modern-select:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.15), inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Modern button dark mode enhancements */
[data-theme="dark"] .modern-button {
  background: linear-gradient(135deg, #64B5F6, #42A5F5) !important;
  box-shadow: 0 4px 15px rgba(66, 165, 245, 0.3) !important;
  border: none !important;
}

[data-theme="dark"] .modern-button:hover {
  box-shadow: 0 6px 20px rgba(66, 165, 245, 0.4) !important;
  background: linear-gradient(135deg, #90CAF9, #64B5F6) !important;
  transform: translateY(-2px) !important;
}

.modern-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modern-button:hover:before {
  transform: translateX(100%);
}

/* Spinner for loading state */
.flex-center {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  animation: spin 1.2s linear infinite;
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
}

.spinner-circle {
  fill: none;
  stroke: currentColor;
  stroke-width: 3;
  stroke-dasharray: 30, 200;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -125px;
  }
}

.start-option {
  padding: 1.75rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-soft);
  text-align: center;
}

.start-option .input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.start-option h3 {
  margin: 0 0 1.25rem 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
}

.start-option .url-input-group,
.start-option .modern-select {
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

/* Center the configuration options vertically with appropriate margins */
.start-options.vertical {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 650px;
  margin: 0 auto;
  width: 100%;
  padding: 0 1.5rem;
}

[data-theme="dark"] .start-option {
  background: rgba(18, 18, 20, 0.1);
  border: 1px solid var(--dark-glass-border);
  color: var(--dark-text-primary);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.start-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: rgba(75, 116, 170, 0.6);
  animation: pulse-border 1.5s infinite alternate;
}

[data-theme="dark"] .start-option:hover {
  border-color: rgba(100, 181, 246, 0.7);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(100, 181, 246, 0.5);
  background: rgba(24, 24, 28, 0.2);
  animation: pulse-border-dark 1.5s infinite alternate;
}

@keyframes pulse-border {
  0% {
    border-color: rgba(75, 116, 170, 0.3);
    box-shadow: var(--shadow-medium), 0 0 5px rgba(75, 116, 170, 0.3);
  }
  100% {
    border-color: rgba(75, 116, 170, 0.8);
    box-shadow: var(--shadow-medium), 0 0 15px rgba(75, 116, 170, 0.5);
  }
}

@keyframes pulse-border-dark {
  0% {
    border-color: rgba(100, 181, 246, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 0 5px rgba(100, 181, 246, 0.3);
  }
  100% {
    border-color: rgba(100, 181, 246, 0.9);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(100, 181, 246, 0.6);
  }
}

[data-theme="dark"] .start-option h3 {
  color: var(--dark-text-primary);
  font-weight: 600;
  letter-spacing: 0.01em;
}

[data-theme="dark"] .start-option p {
  color: rgba(255, 255, 255, 0.8);
}

.config-tabs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.75rem;
}

.config-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.925rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-default);
  border-radius: var(--radius-small);
  position: relative;
}

.config-tab:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--text-primary);
}

.config-tab.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

.config-tab.active::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-color);
  border-radius: 4px 4px 0 0;
}

.config-tab svg {
  width: 18px;
  height: 18px;
}

.auto-config-tab {
  margin-right: auto;
  color: rgba(75, 116, 170, 0.8);
  font-size: 0.9rem;
  background: none;
  border: none;
  position: relative;
  overflow: visible;
  padding-right: 24px;
}

.auto-config-tab[data-animate="true"] {
  animation: gentle-pulse 2s infinite;
}

.auto-config-text {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.2;
  color: #FF8C00; /* Bright orange color */
  font-weight: 500;
}

.emoji-container {
  position: absolute;
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
  animation: float-emojis 3s infinite;
  opacity: 0;
}

@keyframes gentle-pulse {
  0% {
    transform: scale(1);
    color: rgba(75, 116, 170, 0.8);
  }
  50% {
    transform: scale(1.02);
    color: rgba(75, 116, 170, 1);
  }
  100% {
    transform: scale(1);
    color: rgba(75, 116, 170, 0.8);
  }
}

@keyframes float-emojis {
  0% {
    opacity: 0;
    transform: translate(0, 0);
  }
  20% {
    opacity: 1;
    transform: translate(-5px, -5px);
  }
  80% {
    opacity: 1;
    transform: translate(-10px, -10px);
  }
  100% {
    opacity: 0;
    transform: translate(-15px, -15px);
  }
}

.auto-config-icon {
  opacity: 0.8;
  animation: spin-slow 4s linear infinite;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.auto-config-tab:hover {
  background: rgba(75, 116, 170, 0.05);
  color: rgba(75, 116, 170, 1);
}

.auto-config-tab:hover .auto-config-icon {
  opacity: 1;
  animation: spin-slow 2s linear infinite;
}

/* Add emoji animation sequence */
.auto-config-tab:hover .emoji-container {
  animation: float-emojis 1.5s infinite;
}

.auto-config-tab::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(75, 116, 170, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.auto-config-tab:hover::after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.config-section {
  padding: 1rem 0;
  transition: var(--transition-default);
  animation: fade-in 0.3s ease-in-out;
  background: none;
  border-radius: 0;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  box-shadow: none;
  border: none;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.input-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: 0.9rem;
}

.input-group input,
.input-group select,
.input-group textarea {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  padding: 0.75rem 1rem;
  width: 100%;
  font-size: 0.95rem;
  color: var(--text-primary);
  transition: var(--transition-default);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

.input-group small {
  display: block;
  margin-top: 0.4rem;
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

.input-group.with-color-picker .input-header {
  margin-bottom: 0.75rem;
}

.input-group.with-color-picker .control-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.color-picker-inline {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.4rem;
}

.color-picker-inline label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.color-picker-inline input[type="color"] {
  width: 48px;
  height: 48px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: #FFFFFF;
  padding: 4px;
  cursor: pointer;
  transition: var(--transition-default);
}

.color-picker-inline input[type="color"]:hover {
  transform: scale(1.05);
}

.opacity-control {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.opacity-control label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.opacity-control input[type="range"] {
  width: 100%;
  height: 6px;
  appearance: none;
  background: linear-gradient(to right, var(--primary-color), var(--primary-color) 50%, #e0e7ef 50%);
  border-radius: 3px;
  outline: none;
}

.opacity-control input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: white;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition-default);
}

.opacity-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.color-display {
  position: relative;
  transition: transform 0.2s ease;
}

.color-display:hover {
  transform: scale(1.05);
}

/* Animation select styling */
.animation-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  min-width: 120px;
}

/* Background settings section */
.appearance-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.appearance-section .section-title {
  font-size: 1.25rem;
  margin-bottom: 1.25rem;
  color: #333;
}

[data-theme="dark"] .appearance-section .section-title {
  color: #e0e0e0;
}

.appearance-section .color-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.appeloarance-section .color-picker-container {
  margin-bottom: 0.75rem;
}

/* Update existing styles */
.input-group {
  margin-bottom: 1.5rem;
}

.logo-input-container {
  margin-top: 0.5rem;
}

.preview-controls {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
  display: flex;
  gap: 8px;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: white;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-default);
  box-shadow: var(--shadow-soft);
}

.icon-button:hover {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-medium);
}

.icon-button svg {
  width: 20px;
  height: 20px;
}

.minimal-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(75, 116, 170, 0.08);
  border: none;
  color: rgb(75, 116, 170);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.minimal-button:hover {
  background: rgba(75, 116, 170, 0.12);
}

@keyframes pulse-rotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.minimal-button:hover .auto-config-icon {
  opacity: 1;
  animation: pulse-rotate 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

/* Floating Preview Button */
.floating-preview-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: auto;
  height: 56px;
  border-radius: 28px;
  padding: 0 24px;
  border: none;
  background: rgba(100, 181, 246, 0.2);
  color: var(--dark-accent);
  cursor: pointer;
  transition: var(--transition-default);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px var(--dark-glass-border);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 2000;
  font-weight: var(--font-weight-medium);
  font-size: 0.95rem;
}

.floating-preview-button:hover {
  background: rgba(100, 181, 246, 0.3);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(100, 181, 246, 0.4);
  border-color: rgba(100, 181, 246, 0.5);
}

[data-theme="dark"] .floating-preview-button {
  background: rgba(100, 181, 246, 0.2);
  color: var(--dark-accent);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px var(--dark-glass-border);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid var(--dark-glass-border);
}

[data-theme="dark"] .floating-preview-button:hover {
  background: rgba(100, 181, 246, 0.3);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(100, 181, 246, 0.4);
  border-color: rgba(100, 181, 246, 0.5);
}

[data-theme="light"] .floating-preview-button {
  background: #4B74AA;
  color: white;
  box-shadow: 0 4px 12px rgba(75, 116, 170, 0.25);
}

[data-theme="light"] .floating-preview-button:hover {
  background: #3A5A88;
  box-shadow: 0 6px 16px rgba(75, 116, 170, 0.35);
}

.floating-preview-button svg {
  width: 20px;
  height: 20px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-preview-button:hover svg {
  transform: scale(1.1);
}

.preview-active .floating-preview-button {
  right: calc(50% + 2rem);
}

/* Button Styles */
.button {
  background: var(--accent-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-small);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  font-size: 0.95rem;
  transition: var(--transition-default);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.button:hover {
  background: var(--gradient-start);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.button.secondary {
  background: white;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.button.secondary:hover {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.15);
}

.button.small {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.button.danger {
  background: #e74c3c;
}

.button.danger:hover {
  background: #c0392b;
}

.begin-config {
  width: 100%;
  margin-top: 1rem;
  padding: 0.85rem !important;
  font-size: 0.95rem !important;
  background: var(--accent-primary) !important;
  color: white;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-default) !important;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .begin-config {
  background: rgba(75, 116, 170, 0.9) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.begin-config:hover {
  background: var(--gradient-start) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .begin-config:hover {
  background: rgba(75, 116, 170, 1) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.config-mode-section {
  background: transparent;
  border-radius: var(--radius-medium);
  border: none;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 700px;
  width: 100%;
  transition: all 0.3s ease;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  /* Remove scroll-snap-align to allow free scrolling */
  scroll-margin-top: 20px; /* Add margin to ensure content is visible */
  box-shadow: none;
}

[data-theme="dark"] .config-mode-section {
  background: transparent;
  border: none;
  box-shadow: none;
}

.section-title {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.4rem;
  font-weight: var(--font-weight-semibold);
  position: relative;
  padding-bottom: 0.75rem;
}

[data-theme="dark"] .section-title {
  color: var(--dark-text-primary);
  letter-spacing: 0.01em;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--dark-accent);
  border-radius: 3px;
}

.section-subtitle {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .section-subtitle {
  color: rgba(255, 255, 255, 0.9);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.url-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.url-input-group input {
  width: 100%;
  padding: 0.85rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: white;
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: var(--transition-default);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .url-input-group input {
  background-color: rgba(30, 35, 45, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.url-input-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

[data-theme="dark"] .url-input-group input:focus {
  border-color: rgba(75, 116, 170, 0.6);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.2);
}

.url-input-group input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

[data-theme="dark"] .url-input-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.url-input-group input.valid {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}

.logo-preview {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  margin-top: 1rem;
}

.logo-preview img {
  max-width: 200px;
  max-height: 80px;
  object-fit: contain;
  align-self: center;
  border-radius: var(--radius-small);
  padding: 0.5rem;
  background: white;
  box-shadow: var(--shadow-soft);
}

.dark-theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.5rem;
  border-radius: var(--radius-small);
  transition: var(--transition-default);
}

.dark-theme-toggle:hover {
  color: var(--text-primary);
  background: rgba(0, 0, 0, 0.05);
}

.dark-theme-toggle svg {
  width: 18px;
  height: 18px;
}

/* Tag-like styles for practice areas */
.practice-area-tag {
  padding: 0.4rem 0.75rem;
  border-radius: 16px;
  background: rgba(100, 181, 246, 0.15);
  border: 1px solid rgba(100, 181, 246, 0.2);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
  color: var(--dark-accent);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  transition: var(--transition-default);
}

[data-theme="dark"] .practice-area-tag {
  background: rgba(100, 181, 246, 0.15);
  border: 1px solid rgba(100, 181, 246, 0.2);
  color: var(--dark-accent);
}

.practice-area-tag:hover {
  background: rgba(100, 181, 246, 0.2);
}

[data-theme="dark"] .practice-area-tag button {
  background: rgba(100, 181, 246, 0.2);
  color: var(--dark-accent);
}

[data-theme="dark"] .practice-area-tag button:hover {
  background: rgba(100, 181, 246, 0.3);
}

.practice-area-tag button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: none;
  background: rgba(75, 116, 170, 0.1);
  color: var(--accent-primary);
  cursor: pointer;
  transition: var(--transition-default);
  padding: 0;
  font-size: 1rem;
  line-height: 1;
}

[data-theme="dark"] .practice-area-tag button {
  background: rgba(75, 116, 170, 0.25);
  color: #7EB3FF;
}

.practice-area-tag button:hover {
  background: rgba(75, 116, 170, 0.2);
}

[data-theme="dark"] .practice-area-tag button:hover {
  background: rgba(75, 116, 170, 0.35);
}

[data-theme="dark"] .input-group select {
  background-color: rgba(30, 35, 45, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px; /* Space for the dropdown arrow */
  appearance: none;
}

[data-theme="dark"] .input-group select option {
  background-color: #1E232D;
  color: rgba(255, 255, 255, 0.9);
  padding: 12px;
}

[data-theme="dark"] .input-group select:focus {
  border-color: rgba(75, 116, 170, 0.6);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.2);
  outline: none;
}

/* Fix dropdown appearance in light mode too for consistency */
.input-group select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23607D8B' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px; /* Space for the dropdown arrow */
  appearance: none;
}

/* Modern slider styles */
.slider-container {
  margin: 16px 0;
}

.slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.slider-value {
  font-weight: 500;
  color: var(--text-primary);
  background-color: rgba(75, 116, 170, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
  letter-spacing: 0.02em;
}

.modern-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: #e1e1e1;
  border-radius: 5px;
  outline: none;
  padding: 0;
  margin: 10px 0;
}

.modern-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.modern-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.modern-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.modern-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
}

.modern-slider::-webkit-slider-runnable-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  border-radius: 5px;
}

.modern-slider::-moz-range-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  border-radius: 5px;
}

/* Dark theme adjustments */
[data-theme="dark"] .modern-slider {
  background: rgba(30, 30, 30, 0.6);
}

[data-theme="dark"] .modern-slider::-webkit-slider-thumb {
  background: var(--dark-accent);
  border: 2px solid rgba(18, 18, 20, 0.8);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .modern-slider::-moz-range-thumb {
  background: var(--dark-accent);
  border: 2px solid rgba(18, 18, 20, 0.8);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .slider-value {
  background-color: rgba(100, 181, 246, 0.15);
  color: var(--dark-accent);
  border: 1px solid rgba(100, 181, 246, 0.2);
}

/* Improve input styles for consistency */
.input-group input[type="text"],
.input-group input[type="url"],
.input-group select,
.input-group textarea {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

[data-theme="dark"] .input-group input[type="text"],
[data-theme="dark"] .input-group input[type="url"],
[data-theme="dark"] .input-group select,
[data-theme="dark"] .input-group textarea {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(30, 30, 30, 0.8);
  color: #eee;
}

.input-group input[type="text"]:focus,
.input-group input[type="url"]:focus,
.input-group select:focus,
.input-group textarea:focus {
  border-color: #634C38;
  box-shadow: 0 0 0 2px rgba(99, 76, 56, 0.2);
  outline: none;
}

/* Refined color picker styles */
.color-picker-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker-inline input[type="color"] {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-picker-inline input[type="color"]:hover {
  transform: scale(1.05);
}

.color-picker-inline label {
  font-size: 13px;
  color: var(--text-secondary);
}

/* Color picker dot button styles */
.color-picker-container {
  position: relative;
  display: flex;
  align-items: center;
}

.color-dot-button {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-dot-button:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.15);
}

.color-dot-button:active {
  transform: scale(0.95);
}

.color-dot-label {
  position: absolute;
  opacity: 0;
  left: 45px;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  transition: all 0.2s ease;
  transform: translateY(-50%);
  top: 50%;
}

.color-dot-button:hover .color-dot-label {
  opacity: 1;
  transform: translateY(-50%);
}

.hidden-color-input {
  position: absolute;
  visibility: hidden;
  width: 0;
  height: 0;
  opacity: 0;
}

[data-theme="dark"] .color-dot-button {
  border-color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .color-dot-label {
  background: rgba(50, 50, 50, 0.9);
  color: #eee;
}

/* Improve layout of the color picker sections */
.input-group.with-color-picker {
  margin-bottom: 24px;
}

.input-group.with-color-picker .input-header {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
}

.input-group.with-color-picker .input-header > label {
  margin-bottom: 8px;
  font-weight: 600;
}

.input-group.with-color-picker .control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Inline label for color pickers */
.inline-label {
  margin: 0 0 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

[data-theme="dark"] .inline-label {
  color: rgba(255, 255, 255, 0.85);
}

/* Compact slider container */
.slider-container.compact {
  margin: 0 0 0 12px;
  flex: 1;
}

/* Update color picker container */
.color-picker-container {
  position: relative;
  display: flex;
  align-items: center;
}

/* Update control group layout for space saving */
.input-group.with-color-picker .control-group {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 8px;
  margin-bottom: 8px;
}

/* Reduce spacing for picker groups */
.input-group.with-color-picker {
  margin-bottom: 16px;
}

.input-group.with-color-picker .input-header {
  margin-bottom: 8px;
}

/* Enhanced glass effect for dark mode */
.glass-effect {
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-medium);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  width: 100%;
  margin: 0 auto;
}

[data-theme="dark"] .glass-effect {
  background: rgba(18, 18, 20, 0.2);
  border: 1px solid var(--dark-glass-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Modern buttons for dark mode */
[data-theme="dark"] .modern-button {
  background: rgba(91, 141, 239, 0.2);
  color: var(--dark-accent);
  border: 1px solid var(--dark-border);
  transition: all 0.2s ease;
}

[data-theme="dark"] .modern-button:hover {
  background: rgba(91, 141, 239, 0.3);
  border-color: var(--dark-accent);
  transform: translateY(-1px);
}

/* Modern inputs for dark mode */
[data-theme="dark"] .modern-input {
  background: rgba(17, 24, 39, 0.7);
  border: 1px solid var(--dark-border);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .modern-input:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

/* OR divider */
.or-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem auto;
  padding: 0.75rem 0;
  width: 100%;
  max-width: 300px;
  background: transparent;
  box-shadow: none;
}

.divider-line {
  flex-grow: 1;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}

.divider-text {
  margin: 0 1rem;
  color: rgba(0, 0, 0, 0.5);
  font-weight: 500;
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Updated divider for dark mode */
[data-theme="dark"] .divider-line {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .divider-text {
  color: var(--dark-accent);
}

/* OR divider styling for dark mode */
[data-theme="dark"] .or-divider {
  color: var(--dark-text-secondary);
}

.preview-container {
  position: fixed;
  top: 60px;
  right: 0;
  width: 50%;
  height: calc(100vh - 60px);
  transform: translate(100%);
  transition: var(--transition-default);
  overflow: hidden;
  background: transparent;
  isolation: isolate;
  z-index: 10;
}

.preview-active .preview-container {
  transform: translate(0);
}

.preview-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.demo-page-container {
  position: relative;
  overflow-x: hidden;
  scroll-behavior: smooth;
  height: 100vh; /* Set to 100vh to enable proper snap scrolling */
  min-height: 100vh;
  overflow-y: auto; /* Change to auto to enable scrolling */
  scroll-snap-type: y mandatory; /* Enable snap scrolling */
  -webkit-overflow-scrolling: touch; /* For better momentum scrolling on iOS */
  display: flex;
  flex-direction: column;
  /* Remove pattern background completely */
  background-color: transparent;
  background-image: none;
  /* Ensure no horizontal scrollbar appears */
  max-width: 100vw;
}

/* Dark mode - remove background pattern completely */
[data-theme="dark"] .demo-page-container {
  background-color: transparent;
  background-image: none;
}

.preview-active.demo-page-container {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}

/* When preview is active, adjust the config container */
.preview-active .config-container {
  position: fixed;
  top: 60px;
  left: 0;
  width: 50%;
  height: calc(100vh - 60px);
  overflow-y: auto;
  padding-bottom: 100px;
  transition: all var(--transition-default);
}

/* Default config container full width */
.config-container {
  min-height: 100vh;
  height: 100vh; /* Fixed height for proper snap scrolling */
  padding: 2rem;
  background-color: var(--background-color);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-medium);
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  scroll-margin-top: 0; /* Remove margin to ensure proper snapping */
  margin-top: 0; /* Remove margin to ensure proper snapping */
  padding-top: 3rem;
  display: flex;
  flex-direction: column;
  scroll-snap-align: start; /* Enable snap scrolling */
  transition: transform 0.5s ease, opacity 0.5s ease;
  z-index: 2; /* Ensure it's above other elements */
  overflow-y: auto; /* Allow scrolling within the container */
}

/* Hero Section Styles - Enhanced */
.hero-section {
  min-height: 100vh; /* Set to 100vh for full viewport height */
  height: 100vh; /* Fixed height for proper snap scrolling */
  width: 100%;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: transparent;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  scroll-snap-align: start; /* Enable snap scrolling */
  left: 0;
  right: 0;
  box-sizing: border-box;
  margin-bottom: 0; /* Remove margin to ensure proper snapping */
}

[data-theme="dark"] .hero-section {
  border-bottom: 1px solid var(--dark-border);
}

/* Add the frame with pattern behind hero content - similar to the screenshot */
.hero-section::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, rgba(75, 116, 170, 0.01), rgba(44, 62, 80, 0.01));
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234b74aa' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: -1;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(0.5px);
  -webkit-backdrop-filter: blur(0.5px);
  /* Add a radial gradient to fade out the edges */
  mask-image: radial-gradient(ellipse 80% 80% at 50% 50%, black 30%, transparent 80%);
  -webkit-mask-image: radial-gradient(ellipse 80% 80% at 50% 50%, black 30%, transparent 80%);
}

[data-theme="dark"] .hero-section::before {
  background: linear-gradient(135deg, rgba(75, 116, 170, 0.015), rgba(32, 45, 60, 0.015));
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234b74aa' fill-opacity='0.025'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.25;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  /* Same mask for dark mode */
  mask-image: radial-gradient(ellipse 80% 80% at 50% 50%, black 30%, transparent 80%);
  -webkit-mask-image: radial-gradient(ellipse 80% 80% at 50% 50%, black 30%, transparent 80%);
}

@keyframes morph {
  0% {
    border-radius: 50%;
  }
  50% {
    border-radius: 48%;
  }
  100% {
    border-radius: 50%;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes emanate {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.hero-content {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  min-height: 50vh; /* Reduced to 50vh to make it smaller */
  padding: 4vh 0 2vh 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--text-primary);
  line-height: 1.2;
  animation: fadeIn 1s ease-out;
}

.title-highlight {
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.title-highlight::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: -5px;
  right: -5px;
  height: 10px;
  background-color: rgba(75, 116, 170, 0.2);
  z-index: -1;
  transform: skew(-12deg);
}

[data-theme="dark"] .hero-title {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .title-highlight {
  color: var(--dark-accent);
}

[data-theme="dark"] .title-highlight::after {
  background-color: rgba(100, 181, 246, 0.2);
  box-shadow: 0 0 20px rgba(100, 181, 246, 0.2);
}

/* Solari board flipboard styling */
.flipboard-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3.5rem;
  margin: 0 auto;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  position: relative;
  background: rgba(20, 20, 20, 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 8px;
  padding: 0.5rem 0.25rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: visible; /* Allow content to be fully visible */
  max-width: 100vw; /* Use full viewport width */
  width: 90vw; /* Set a fixed percentage of viewport width */
  box-sizing: border-box;
  min-width: 0;
}

[data-theme="dark"] .flipboard-container {
  background: rgba(80, 80, 80, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.flipboard-container.hidden {
  opacity: 0;
  transform: translateY(10px);
  position: absolute;
  pointer-events: none;
}

.solari-board {
  display: flex;
  justify-content: center;
  perspective: 1000px;
  width: auto; /* Let content determine width */
  height: 100%;
  overflow: visible; /* Allow content to be fully visible */
  min-width: 0;
  flex-wrap: nowrap;
}

.solari-flap {
  position: relative;
  width: 1.2rem;
  height: 100%;
  margin: 0 1px;
  transform-style: preserve-3d;
  transition: transform 0.12s cubic-bezier(0.6, 0.2, 0.4, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  flex-shrink: 1; /* Allow flaps to shrink if needed */
  min-width: 0.6rem; /* Reduced minimum width for better mobile scaling */
  flex: 1 1 auto; /* Allow flaps to grow and shrink proportionally */
  /* Ensure proper spacing between flaps */
  box-sizing: border-box;
}

.solari-flap.flipping {
  transform: rotateX(180deg);
  animation: flap-noise 0.12s cubic-bezier(0.6, 0.2, 0.4, 1);
}

@keyframes flap-noise {
  0% {
    transform: rotateX(0deg);
  }
  70% {
    transform: rotateX(170deg);
  }
  100% {
    transform: rotateX(180deg);
  }
}

.solari-front,
.solari-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: bold;
  font-size: 1.5rem;
  font-family: monospace;
  background: linear-gradient(180deg, #2c3e50 0%, #1a1c20 100%);
  color: #4fc3f7;
  border-radius: 2px;
  padding: 0;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.6);
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.3);
  letter-spacing: 0;
  overflow: hidden;
  text-transform: lowercase;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  border-bottom: 1px solid rgba(0, 0, 0, 0.5);
}

.solari-front::after,
.solari-back::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  top: 50%;
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05);
}

.solari-back {
  transform: rotateX(180deg);
  background: linear-gradient(180deg, #1a1c20 0%, #2c3e50 100%);
}

[data-theme="dark"] .solari-front,
[data-theme="dark"] .solari-back {
  background: linear-gradient(180deg, #131922 0%, #0a0c10 100%);
  color: #64b5f6;
  text-shadow: 0 0 12px rgba(100, 181, 246, 0.8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Additional Role Styling */
.main-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  width: 100%;
  flex-wrap: wrap; /* Changed from nowrap to wrap to prevent overflow */
  padding: 0 15px;
  /* Ensure container doesn't overflow on small screens */
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.main-title-text {
  font-size: min(3.5rem, 10vw);
  font-weight: 700;
  line-height: 1.3;
  letter-spacing: -0.02em;
  color: rgba(55, 65, 81, 0.98); /* Default light mode color */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  /* Remove white-space: nowrap to allow text to wrap if needed */
}

.main-title-white {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dark mode text color */
[data-theme="dark"] .main-title-text {
  color: rgba(255, 255, 255, 0.98);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* White style override (when explicitly using main-title-white class) */
.main-title-white {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Attribute Flipboard */
.attribute-flipboard-wrapper {
  display: inline-flex;
  align-items: center;
  perspective: 1200px;
  margin: 0 0 0 8px;
  height: 2.75rem;
  position: relative;
  max-width: calc(100% - 8px);
  overflow: visible;
  box-sizing: border-box;
  z-index: 1;
}

.attribute-flipboard {
  height: 40px;
  min-width: 200px;
  transform-style: preserve-3d;
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 1.25rem;
  border-radius: 12px;
  overflow: hidden;
  perspective: 1000px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(59, 130, 246, 0.2), 0 0 15px rgba(59, 130, 246, 0.15);
  box-sizing: border-box;
  max-width: 100%;
}

.attribute-flipboard.flipping {
  transform: rotateX(180deg);
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.attr-front,
.attr-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  color: #fff;
  border-radius: 12px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 1.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 0 8px rgba(124, 58, 237, 0.3);
  transform: rotateX(0deg);
  letter-spacing: 0.01em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.attr-back {
  transform: rotateX(180deg);
}

.emoji-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  margin-right: 10px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.attr-text {
  font-weight: 700;
  letter-spacing: 0.02em;
}

/* Icon flipper styling */
.icon-flip-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.75rem;
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
}

.icon-flip {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.12s cubic-bezier(0.6, 0.2, 0.4, 1);
}

.icon-flip.flipping {
  transform: rotateX(180deg);
  animation: flap-noise 0.12s cubic-bezier(0.6, 0.2, 0.4, 1);
}

.icon-flip-front,
.icon-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
}

.icon-flip-front {
  transform: rotateX(0deg);
}

.icon-flip-back {
  transform: rotateX(180deg);
}

/* Goal text transition */
.goal-text-transition {
  width: 100%;
  max-width: 800px;
  margin: 2rem auto 2.5rem;
  text-align: center;
  position: relative;
  min-height: 3.5rem;
  height: auto;
  overflow: visible;
  padding: 0 15px;
}

.goal-text {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 0;
  color: white;
  animation: fadeChange 1.2s ease-in-out;
  position: relative; /* Changed from absolute to relative */
  width: 100%;
  opacity: 1;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  padding: 0 10px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

@keyframes fadeChange {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  10% {
    opacity: 0.2;
    transform: translateY(10px);
  }
  25% {
    opacity: 1;
    transform: translateY(0);
  }
  85% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

[data-theme="dark"] .goal-text {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .goal-text {
  color: rgba(55, 65, 81, 0.95);
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

@keyframes flap-noise {
  0%, 100% {
    box-shadow: 0 0 0 transparent;
  }
  10% {
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.3);
  }
  90% {
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  }
}

/* Config hint and scroll indicator */
.config-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 3vh;
  padding: 1rem;
  text-align: center;
  margin-bottom: 4vh;
}

.config-hint p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

/* Light mode styles for config hint text */
[data-theme="light"] .config-hint p {
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.scroll-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: bounce 2s infinite;
  opacity: 0.9;
  margin-top: 1.5rem;
  z-index: 10;
  position: relative;
}

/* Dark style for scroll indicator in light mode */
[data-theme="light"] .scroll-indicator {
  background-color: rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.scroll-indicator:hover {
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

/* Dark hover style for scroll indicator in light mode */
[data-theme="light"] .scroll-indicator:hover {
  background-color: rgba(0, 0, 0, 0.25);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.15);
}

.scroll-indicator svg {
  width: 24px;
  height: 24px;
  color: rgba(255, 255, 255, 0.9);
}

/* Dark icon color for scroll indicator in light mode */
[data-theme="light"] .scroll-indicator svg {
  color: rgba(0, 0, 0, 0.8);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-12px) scale(1.05);
  }
  60% {
    transform: translateY(-6px) scale(1.02);
  }
}

/* Role flipper positioning */
.role-flipboard-container {
  margin: 0 auto 1.5rem;
  padding: 0.75rem 0.25rem;
  max-width: 100vw; /* Use full viewport width */
  width: 90vw; /* Set a fixed percentage of viewport width */
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible; /* Allow content to be fully visible */
}

/* Vertical spacing for hero section */
.vertical-spacer {
  height: 3vh;
  min-height: 20px;
}

/* Ensure smooth scrolling at the root level */
html, body {
  scroll-behavior: smooth;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Ensure proper container sizing */
.hero-section, .hero-content {
  max-width: 100%;
  overflow-x: hidden;
}

.main-title-container {
  max-width: 100%;
  overflow-x: hidden;
}

.logo-agent-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 3vh 0;
  gap: 0.75rem;
}

.orange-logo {
  height: 79px;
  width: auto;
  object-fit: contain;
  margin-right: 2px;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.2));
}

.agent-text {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #D85722;
  letter-spacing: -0.01em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding-top: 2px;
}

/* Collapsible section styles */
.appearance-section .section-title {
  position: relative;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  padding: 8px 0;
  font-size: 1.25rem;
  color: #333;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

[data-theme="dark"] .appearance-section .section-title {
  color: #e0e0e0;
}

.appearance-section .section-title:hover {
  color: #4B74AA;
}

[data-theme="dark"] .appearance-section .section-title:hover {
  color: #6B94CA;
}

.collapse-toggle {
  cursor: pointer;
  color: #667788;
  transition: transform 0.3s ease;
  background: none;
  border: none;
  padding: 4px;
  outline: none;
}

.collapse-toggle:hover {
  color: #4B74AA;
}

[data-theme="dark"] .collapse-toggle:hover {
  color: #6B94CA;
}

/* Logo and Button Text Layout */
.control-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}

.logo-control {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-text-control {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.button-text-control input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,0.1);
  font-size: 0.9rem;
}

[data-theme="dark"] .button-text-control input {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.button-text-control small {
  font-size: 0.8rem;
  color: rgba(60, 80, 100, 0.7);
}

[data-theme="dark"] .button-text-control small {
  color: rgba(255, 255, 255, 0.6);
}

/* Page Navigation Dots */
.page-navigation-dots {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: rgba(180, 180, 180, 0.5);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  opacity: 0.8;
}

[data-theme="dark"] .nav-dot {
  background-color: rgba(120, 120, 120, 0.5);
}

.nav-dot:hover {
  transform: scale(1.2);
  background-color: rgba(150, 150, 150, 0.8);
  opacity: 1;
}

[data-theme="dark"] .nav-dot:hover {
  background-color: rgba(200, 200, 200, 0.8);
}

.nav-dot.active {
  width: 14px;
  height: 14px;
  background-color: #4B74AA;
  transform: scale(1.2);
  opacity: 1;
  box-shadow: 0 0 10px rgba(75, 116, 170, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .nav-dot.active {
  background-color: #6B94CA;
  box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
  border: 2px solid rgba(0, 0, 0, 0.5);
}

.nav-dot.active:hover {
  transform: scale(1.3);
}

/* Hide scrollbar for cleaner look but maintain scroll functionality */
.demo-page-container {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.demo-page-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

.start-option .input-group input,
.start-option .input-group label {
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.start-option .begin-config {
  margin-left: auto;
  margin-right: auto;
  max-width: 200px;
}

/* Add styles for the CreateAgentButton container */
.create-agent-overlay {
  position: fixed;
  left: 50% !important;
  bottom: 40px;
  transform: translateX(-50%) !important;
  z-index: 1100; /* Higher than preview container */
  pointer-events: none; /* Allow clicking through the overlay */
  width: auto;
  margin: 0 !important;
}

.create-agent-overlay > * {
  pointer-events: auto; /* Re-enable pointer events for the button itself */
}

/* Enhanced mobile responsive styles for hero section */
@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding: 20px 15px;
  }

  .hero-content {
    min-height: 80vh;
    padding: 3vh 10px 2vh 10px;
  }

  .hero-title {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    padding: 0 15px;
  }

  .main-title-container {
    padding: 0 10px;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    /* Ensure container adapts to screen size */
    max-width: 100%;
    overflow-x: hidden;
  }

  .main-title-text {
    font-size: min(2.2rem, 8vw);
    line-height: 1.3;
    /* Remove white-space: nowrap to allow text to wrap if needed */
  }

  .attribute-flipboard-wrapper {
    margin-left: 6px;
  }

  .attribute-flipboard {
    min-width: 180px;
  }

  .goal-text-transition {
    min-height: 5rem;
    margin: 1.5rem auto 2rem;
  }

  .goal-text {
    font-size: 1.8rem;
    line-height: 1.4;
  }

  .logo-agent-container {
    margin: 2vh 0;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .orange-logo {
    height: 60px;
    width: auto;
    margin-right: 0;
    margin-bottom: 5px;
  }

  .agent-text {
    font-size: 1.6rem;
  }

  .flipboard-container {
    width: 95vw;
    max-width: 100vw;
    height: 3rem;
    padding: 0.5rem 0.25rem;
  }

  .role-flipboard-container {
    padding: 0.5rem 0.25rem;
    width: 95vw;
    max-width: 100vw;
  }

  .solari-flap {
    min-width: 0.5rem;
    margin: 0 1px;
  }

  .page-navigation-dots {
    right: 10px;
  }

  .nav-dot {
    width: 10px;
    height: 10px;
  }

  .create-agent-overlay {
    bottom: 20px;
    width: 90%;
    max-width: 280px;
  }
}

/* Medium-sized mobile devices */
@media (max-width: 600px) {
  .main-title-text {
    font-size: min(2rem, 7vw);
    line-height: 1.3;
  }

  .attribute-flipboard {
    min-width: 170px;
  }

  .attr-front, .attr-back {
    font-size: 1.15rem;
  }
}

/* Even smaller screens like smartphones */
@media (max-width: 480px) {
  .hero-title {
    font-size: 1.5rem;
  }

  .hero-section::before {
    width: 90%;
    height: 90%;
  }

  .orange-logo {
    height: 50px;
  }

  .agent-text {
    font-size: 1.4rem;
  }

  .flipboard-container {
    height: 2.5rem;
    width: 95vw;
    max-width: 100vw;
    padding: 0.4rem 0.25rem;
  }

  .role-flipboard-container {
    padding: 0.4rem 0.25rem;
    width: 95vw;
    max-width: 100vw;
  }

  .solari-flap {
    min-width: 0.4rem;
  }

  .icon-flip-container {
    margin: 0 0.5rem;
    width: 2rem;
    height: 2rem;
  }

  .vertical-spacer {
    height: 2vh;
    min-height: 10px;
  }

  .create-agent-overlay {
    bottom: 15px;
  }

  .main-title-container {
    flex-wrap: nowrap;
    gap: 4px;
  }

  .main-title-text {
    font-size: min(1.6rem, 6vw);
    line-height: 1.3;
    /* Allow text to wrap if needed */
  }

  .attribute-flipboard {
    min-width: 150px;
    height: 36px;
  }

  .attribute-flipboard-wrapper {
    margin: 0 0 0 4px;
    height: 2.5rem;
  }

  .attr-front, .attr-back {
    font-size: 1.1rem;
  }

  .emoji-circle {
    width: 24px;
    height: 24px;
    margin-right: 6px;
  }

  .goal-text-transition {
    min-height: 6rem;
    margin: 1.5rem auto 2rem;
  }

  .goal-text {
    font-size: 1.6rem;
    line-height: 1.4;
  }
}

/* Extra small screens */
@media (max-width: 400px) {
  .main-title-text {
    font-size: min(1.4rem, 5.5vw);
  }

  .attribute-flipboard {
    min-width: 140px;
  }

  .attr-front, .attr-back {
    font-size: 1rem;
  }

  .emoji-circle {
    width: 22px;
    height: 22px;
    margin-right: 5px;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .main-title-text {
    font-size: min(1.2rem, 5vw);
  }

  .attribute-flipboard {
    min-width: 120px;
    height: 32px;
  }

  .attr-front, .attr-back {
    font-size: 0.85rem;
    padding: 0 0.75rem;
  }

  .emoji-circle {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }

  .main-title-container {
    gap: 2px;
    padding: 0 5px;
    /* Ensure proper spacing on very small screens */
    margin-bottom: 1rem;
  }

  .flipboard-container {
    height: 2.2rem;
    width: 95vw;
    max-width: 100vw;
    padding: 0.3rem 0.25rem;
  }

  .role-flipboard-container {
    padding: 0.3rem 0.25rem;
    margin-bottom: 1rem;
    width: 95vw;
    max-width: 100vw;
  }

  .solari-flap {
    min-width: 0.35rem;
  }

  .icon-flip-container {
    margin: 0 0.4rem;
    width: 1.8rem;
    height: 1.8rem;
  }
}

/* Extremely small screens */
@media (max-width: 320px) {
  .main-title-text {
    font-size: 1.1rem;
  }

  .attribute-flipboard {
    min-width: 110px;
    height: 30px;
  }

  .attr-front, .attr-back {
    font-size: 0.8rem;
    padding: 0 0.5rem;
  }

  .emoji-circle {
    width: 16px;
    height: 16px;
    margin-right: 3px;
  }

  .flipboard-container {
    height: 2rem;
    width: 100%;
    max-width: 260px;
    padding: 0.25rem 0.3rem;
  }

  .role-flipboard-container {
    padding: 0.25rem 0.3rem;
    margin-bottom: 0.75rem;
  }

  .solari-flap {
    min-width: 0.3rem;
  }

  .icon-flip-container {
    margin: 0 0.3rem;
    width: 1.6rem;
    height: 1.6rem;
  }

  /* Ensure the entire word is visible */
  .solari-board {
    justify-content: center;
    overflow: visible;
  }
}

/* Add semi-transparent background in light mode */
[data-theme="light"] .logo-agent-container {
  position: relative;
  background: none;
  padding: 18px 32px;
  border: none;
  border-radius: 0;
  -webkit-backdrop-filter: none;
  backdrop-filter: none;
  box-shadow: none;
  overflow: visible;
}

[data-theme="light"] .logo-agent-container::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 200%;
  height: 200%;
  transform: translate(-50%, -50%);
  background: radial-gradient(ellipse at center,
    rgba(0, 0, 0, 0.98) 0%,
    rgba(0, 0, 0, 0.95) 10%,
    rgba(0, 0, 0, 0.85) 20%,
    rgba(0, 0, 0, 0.6) 30%,
    rgba(0, 0, 0, 0.3) 40%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0) 60%
  );
  pointer-events: none;
  z-index: -1;
}

.main-title-text {
  font-size: min(3.5rem, 10vw);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: rgba(55, 65, 81, 0.98); /* Default light mode color */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.main-title-white {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dark mode text color */
[data-theme="dark"] .main-title-text {
  color: rgba(255, 255, 255, 0.98);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Light mode text color when not using the white style */
[data-theme="light"] .main-title-text:not(.main-title-white) {
  color: rgba(55, 65, 81, 0.98);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

