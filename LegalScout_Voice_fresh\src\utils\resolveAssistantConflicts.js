/**
 * Utility to resolve Vapi assistant ID conflicts
 * 
 * This script helps identify and resolve situations where multiple attorneys
 * are sharing the same Vapi assistant ID, which causes configuration conflicts.
 */

import { supabase } from '../lib/supabase';
import { createVapiAssistant, checkAssistantIdConflicts } from '../services/syncHelpers';

/**
 * Find all assistant ID conflicts in the database
 */
export const findAllAssistantConflicts = async () => {
  try {
    // Get all attorneys with assistant IDs
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('id, email, firm_name, subdomain, vapi_assistant_id')
      .not('vapi_assistant_id', 'is', null);

    if (error) {
      throw error;
    }

    // Group attorneys by assistant ID
    const assistantGroups = {};
    attorneys.forEach(attorney => {
      const assistantId = attorney.vapi_assistant_id;
      if (!assistantGroups[assistantId]) {
        assistantGroups[assistantId] = [];
      }
      assistantGroups[assistantId].push(attorney);
    });

    // Find conflicts (assistant IDs used by multiple attorneys)
    const conflicts = {};
    Object.entries(assistantGroups).forEach(([assistantId, attorneyList]) => {
      if (attorneyList.length > 1) {
        conflicts[assistantId] = attorneyList;
      }
    });

    return {
      success: true,
      totalAttorneys: attorneys.length,
      totalAssistants: Object.keys(assistantGroups).length,
      conflictingAssistants: Object.keys(conflicts).length,
      conflicts
    };
  } catch (error) {
    console.error('Error finding assistant conflicts:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Resolve conflicts for a specific attorney by creating a new assistant
 */
export const resolveConflictForAttorney = async (attorneyId, options = {}) => {
  try {
    // Get attorney data
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();

    if (error) {
      throw error;
    }

    if (!attorney.vapi_assistant_id) {
      return {
        success: false,
        message: 'Attorney does not have a Vapi assistant ID'
      };
    }

    // Check for conflicts
    const conflictCheck = await checkAssistantIdConflicts(attorney.vapi_assistant_id, attorneyId);
    
    if (!conflictCheck.hasConflicts) {
      return {
        success: true,
        message: 'No conflicts found for this attorney',
        action: 'none'
      };
    }

    console.log(`Resolving conflict for attorney ${attorney.email} (${attorney.firm_name})`);
    console.log(`Current assistant ID: ${attorney.vapi_assistant_id}`);
    console.log(`Conflicts with:`, conflictCheck.conflicts);

    // Create new assistant with attorney's current configuration
    const newAssistant = await createVapiAssistant({
      name: attorney.firm_name || 'LegalScout Assistant',
      firstMessage: attorney.welcome_message || 'Hello, how can I help you today?',
      instructions: attorney.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
      voice: {
        provider: attorney.voice_provider || 'openai',
        voiceId: attorney.voice_id || 'alloy'
      }
    });

    // Update attorney record with new assistant ID
    const { error: updateError } = await supabase
      .from('attorneys')
      .update({ 
        vapi_assistant_id: newAssistant.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorneyId);

    if (updateError) {
      throw updateError;
    }

    return {
      success: true,
      message: 'Conflict resolved successfully',
      action: 'created_new_assistant',
      oldAssistantId: attorney.vapi_assistant_id,
      newAssistantId: newAssistant.id,
      conflictsResolved: conflictCheck.conflicts
    };
  } catch (error) {
    console.error('Error resolving conflict for attorney:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Resolve all conflicts by creating new assistants for all but the oldest attorney
 */
export const resolveAllConflicts = async (options = {}) => {
  try {
    const conflictReport = await findAllAssistantConflicts();
    
    if (!conflictReport.success) {
      return conflictReport;
    }

    if (conflictReport.conflictingAssistants === 0) {
      return {
        success: true,
        message: 'No conflicts found',
        action: 'none'
      };
    }

    const results = [];

    // For each conflicting assistant ID
    for (const [assistantId, attorneys] of Object.entries(conflictReport.conflicts)) {
      console.log(`\nResolving conflicts for assistant ID: ${assistantId}`);
      console.log(`Attorneys involved:`, attorneys.map(a => `${a.email} (${a.firm_name})`));

      // Sort attorneys by creation date (oldest first)
      const sortedAttorneys = [...attorneys].sort((a, b) => 
        new Date(a.created_at || 0) - new Date(b.created_at || 0)
      );

      // Keep the oldest attorney with the current assistant ID
      const keeperAttorney = sortedAttorneys[0];
      const conflictedAttorneys = sortedAttorneys.slice(1);

      console.log(`Keeping assistant for: ${keeperAttorney.email} (oldest)`);
      console.log(`Creating new assistants for: ${conflictedAttorneys.map(a => a.email).join(', ')}`);

      // Create new assistants for the other attorneys
      for (const attorney of conflictedAttorneys) {
        const result = await resolveConflictForAttorney(attorney.id, options);
        results.push({
          attorneyId: attorney.id,
          email: attorney.email,
          firmName: attorney.firm_name,
          result
        });
      }
    }

    return {
      success: true,
      message: 'All conflicts resolved',
      action: 'resolved_all',
      totalConflicts: conflictReport.conflictingAssistants,
      resolutionResults: results
    };
  } catch (error) {
    console.error('Error resolving all conflicts:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get a detailed report of the current conflict situation
 */
export const getConflictReport = async () => {
  try {
    const conflictReport = await findAllAssistantConflicts();
    
    if (!conflictReport.success) {
      return conflictReport;
    }

    console.log('\n=== VAPI ASSISTANT CONFLICT REPORT ===');
    console.log(`Total attorneys: ${conflictReport.totalAttorneys}`);
    console.log(`Total unique assistants: ${conflictReport.totalAssistants}`);
    console.log(`Conflicting assistants: ${conflictReport.conflictingAssistants}`);

    if (conflictReport.conflictingAssistants > 0) {
      console.log('\n=== CONFLICTS FOUND ===');
      
      Object.entries(conflictReport.conflicts).forEach(([assistantId, attorneys]) => {
        console.log(`\nAssistant ID: ${assistantId}`);
        console.log('Used by:');
        attorneys.forEach(attorney => {
          console.log(`  - ${attorney.email} (${attorney.firm_name}) [${attorney.subdomain}]`);
        });
      });
    } else {
      console.log('\n✅ No conflicts found!');
    }

    return conflictReport;
  } catch (error) {
    console.error('Error generating conflict report:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
