/**
 * Dynamic Assistant Loader
 * 
 * This utility ensures that the system always uses the authenticated user's
 * actual assistant ID from their Supabase record, with NO hardcoded fallbacks.
 */

import { supabase } from '../lib/supabase-fixed';

/**
 * Get the authenticated user's assistant ID from their Supabase record
 * @returns {Promise<string|null>} The user's assistant ID or null if not found
 */
export const getAuthenticatedUserAssistantId = async () => {
  try {
    // Get the current authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Error getting authenticated user:', authError);
      return null;
    }
    
    if (!user?.email) {
      console.error('No authenticated user found');
      return null;
    }
    
    console.log(`[DynamicAssistantLoader] Getting assistant ID for authenticated user: ${user.email}`);
    
    // Get the attorney record for this user
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('vapi_assistant_id, firm_name')
      .eq('email', user.email)
      .single();
    
    if (attorneyError) {
      console.error('Error getting attorney record:', attorneyError);
      return null;
    }
    
    if (!attorney) {
      console.error('No attorney record found for user:', user.email);
      return null;
    }
    
    if (!attorney.vapi_assistant_id) {
      console.warn(`Attorney ${attorney.firm_name} has no vapi_assistant_id set`);
      return null;
    }
    
    console.log(`[DynamicAssistantLoader] Found assistant ID for ${attorney.firm_name}: ${attorney.vapi_assistant_id}`);
    return attorney.vapi_assistant_id;
    
  } catch (error) {
    console.error('Error in getAuthenticatedUserAssistantId:', error);
    return null;
  }
};

/**
 * Get the authenticated user's full attorney data
 * @returns {Promise<Object|null>} The user's attorney data or null if not found
 */
export const getAuthenticatedUserAttorneyData = async () => {
  try {
    // Get the current authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Error getting authenticated user:', authError);
      return null;
    }
    
    if (!user?.email) {
      console.error('No authenticated user found');
      return null;
    }
    
    console.log(`[DynamicAssistantLoader] Getting attorney data for authenticated user: ${user.email}`);
    
    // Get the attorney record for this user
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();
    
    if (attorneyError) {
      console.error('Error getting attorney record:', attorneyError);
      return null;
    }
    
    if (!attorney) {
      console.error('No attorney record found for user:', user.email);
      return null;
    }
    
    console.log(`[DynamicAssistantLoader] Found attorney data for ${attorney.firm_name}`);
    return attorney;
    
  } catch (error) {
    console.error('Error in getAuthenticatedUserAttorneyData:', error);
    return null;
  }
};

/**
 * Validate that an assistant ID belongs to the authenticated user
 * @param {string} assistantId - The assistant ID to validate
 * @returns {Promise<boolean>} True if the assistant belongs to the user
 */
export const validateAssistantOwnership = async (assistantId) => {
  if (!assistantId) {
    return false;
  }
  
  const userAssistantId = await getAuthenticatedUserAssistantId();
  return userAssistantId === assistantId;
};

/**
 * Get assistant ID with strict authentication check
 * This function will NEVER return a hardcoded fallback
 * @returns {Promise<string|null>} The user's assistant ID or null
 */
export const getStrictAuthenticatedAssistantId = async () => {
  const assistantId = await getAuthenticatedUserAssistantId();
  
  if (!assistantId) {
    console.error('[DynamicAssistantLoader] CRITICAL: No assistant ID found for authenticated user. System will not use hardcoded fallbacks.');
    return null;
  }
  
  return assistantId;
};
