#!/usr/bin/env node

/**
 * Check what assistants are available in your Vapi account
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const VAPI_SECRET_KEY = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_SECRET_KEY;

if (!VAPI_SECRET_KEY) {
  console.error('❌ No Vapi secret key found in environment variables');
  process.exit(1);
}

async function checkVapiAssistants() {
  console.log('🔍 Checking available assistants in your Vapi account...\n');
  
  try {
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API Error (${response.status}):`, errorText);
      return;
    }

    const assistants = await response.json();
    
    if (!assistants || assistants.length === 0) {
      console.log('📭 No assistants found in your Vapi account');
      console.log('\n💡 You need to create an assistant first. You can:');
      console.log('   1. Create one through the Vapi dashboard');
      console.log('   2. Use the Vapi API to create one programmatically');
      return;
    }

    console.log(`✅ Found ${assistants.length} assistant(s) in your Vapi account:\n`);
    
    assistants.forEach((assistant, index) => {
      console.log(`${index + 1}. Assistant ID: ${assistant.id}`);
      console.log(`   Name: ${assistant.name || 'Unnamed'}`);
      console.log(`   Created: ${assistant.createdAt ? new Date(assistant.createdAt).toLocaleDateString() : 'Unknown'}`);
      if (assistant.firstMessage) {
        console.log(`   First Message: "${assistant.firstMessage.substring(0, 100)}${assistant.firstMessage.length > 100 ? '...' : ''}"`);
      }
      console.log('');
    });

    // Check if the current default assistant exists
    const currentDefault = 'eb8533fa-902e-46be-8ce9-df20f5c550d7';
    const foundDefault = assistants.find(a => a.id === currentDefault);
    
    if (foundDefault) {
      console.log(`✅ Current default assistant (${currentDefault}) exists in your account`);
    } else {
      console.log(`❌ Current default assistant (${currentDefault}) NOT found in your account`);
      console.log('\n🔧 Recommended actions:');
      console.log('   1. Update your code to use one of the assistant IDs listed above');
      console.log('   2. Or create a new assistant with the expected ID');
      
      if (assistants.length > 0) {
        console.log(`\n💡 You could use this assistant ID instead: ${assistants[0].id}`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking assistants:', error.message);
  }
}

// Run the check
checkVapiAssistants();
