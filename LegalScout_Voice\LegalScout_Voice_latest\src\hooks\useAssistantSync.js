/**
 * useAssistantSync Hook - React integration for assistant synchronization
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { assistantSyncManager } from '../services/assistantSyncManager';
import { useAssistantAware } from '../contexts/AssistantAwareContext';

export const useAssistantSync = (options = {}) => {
  const { autoSync = true, dataTypes = [] } = options;
  const [syncStatus, setSyncStatus] = useState(assistantSyncManager.getSyncStatus());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // CRITICAL FIX: Memoize dataTypes to prevent infinite re-renders
  const memoizedDataTypes = useMemo(() => dataTypes, [JSON.stringify(dataTypes)]);

  const handleSyncEvent = useCallback((eventType, data) => {
    console.log('🔄 [useAssistantSync] Received sync event:', eventType);

    switch (eventType) {
      case 'assistant_changed':
      case 'sync_completed':
        setIsLoading(false);
        setError(null);
        setSyncStatus(assistantSyncManager.getSyncStatus());
        setLastUpdate(data);
        break;

      case 'sync_error':
        setIsLoading(false);
        setError(data.error);
        break;

      case 'realtime_update':
        if (memoizedDataTypes.length === 0 || memoizedDataTypes.includes(data.dataType)) {
          setLastUpdate(data);
        }
        break;
    }
  }, [memoizedDataTypes]);

  useEffect(() => {
    const unsubscribe = assistantSyncManager.subscribe(handleSyncEvent);
    setSyncStatus(assistantSyncManager.getSyncStatus());
    return unsubscribe;
  }, [handleSyncEvent]);

  const syncAssistantSelection = useCallback(async (attorneyId, assistantId) => {
    try {
      setIsLoading(true);
      setError(null);
      await assistantSyncManager.synchronizeAssistantSelection(attorneyId, assistantId);
    } catch (error) {
      setError(error.message);
    }
  }, []);

  const forceRefresh = useCallback(async () => {
    if (currentAssistant?.attorneyId && currentAssistant?.id) {
      await syncAssistantSelection(currentAssistant.attorneyId, currentAssistant.id);
    }
  }, [currentAssistant, syncAssistantSelection]);

  return {
    syncStatus,
    isLoading,
    error,
    lastUpdate,
    syncAssistantSelection,
    forceRefresh,
    clearError: () => setError(null),
    currentAssistantId: syncStatus.currentAssistantId,
    currentAttorneyId: syncStatus.currentAttorneyId,
    isAssistantSelected: !!syncStatus.currentAssistantId
  };
};

export default useAssistantSync;
