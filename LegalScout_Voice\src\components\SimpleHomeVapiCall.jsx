/**
 * Simple Home Vapi Call Component
 * 
 * A simplified version of VapiCall specifically for the home page that bypasses
 * complex initialization and uses direct Vapi Web SDK integration.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Vapi } from '@vapi-ai/web';
import './VapiCall.css';

const SimpleHomeVapiCall = ({ onEndCall }) => {
  const [status, setStatus] = useState('idle');
  const [error, setError] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const vapiRef = useRef(null);

  // Default assistant ID for home page - use the working assistant ID
  const DEFAULT_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  
  // Public API key for client-side operations
  const PUBLIC_API_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';

  // Initialize Vapi instance
  useEffect(() => {
    console.log('[SimpleHomeVapiCall] Initializing Vapi instance...');
    
    try {
      // Create Vapi instance with public key
      const vapi = new Vapi(PUBLIC_API_KEY);
      vapiRef.current = vapi;

      // Set up event listeners
      vapi.on('call-start', () => {
        console.log('[SimpleHomeVapiCall] Call started');
        setStatus('connected');
        setIsConnected(true);
        setError(null);
      });

      vapi.on('call-end', () => {
        console.log('[SimpleHomeVapiCall] Call ended');
        setStatus('idle');
        setIsConnected(false);
        if (onEndCall) onEndCall();
      });

      vapi.on('speech-start', () => {
        console.log('[SimpleHomeVapiCall] Assistant started speaking');
      });

      vapi.on('speech-end', () => {
        console.log('[SimpleHomeVapiCall] Assistant stopped speaking');
      });

      vapi.on('message', (message) => {
        console.log('[SimpleHomeVapiCall] Message received:', message);
        
        if (message.type === 'transcript' && message.transcript) {
          setCurrentTranscript(message.transcript);
          
          if (message.transcript_type === 'final') {
            setMessages(prev => [...prev, {
              type: 'user',
              text: message.transcript,
              timestamp: new Date().toISOString()
            }]);
            setCurrentTranscript('');
          }
        }
        
        if (message.type === 'model-output' && message.output) {
          setMessages(prev => [...prev, {
            type: 'assistant',
            text: message.output,
            timestamp: new Date().toISOString()
          }]);
        }
      });

      vapi.on('error', (error) => {
        console.error('[SimpleHomeVapiCall] Vapi error:', error);
        setError(error.message || 'An error occurred');
        setStatus('error');
        setIsConnected(false);
      });

      console.log('[SimpleHomeVapiCall] Vapi instance initialized successfully');
      
    } catch (error) {
      console.error('[SimpleHomeVapiCall] Failed to initialize Vapi:', error);
      setError('Failed to initialize voice assistant');
      setStatus('error');
    }

    // Cleanup on unmount
    return () => {
      if (vapiRef.current) {
        try {
          vapiRef.current.stop();
        } catch (error) {
          console.warn('[SimpleHomeVapiCall] Error stopping call during cleanup:', error);
        }
      }
    };
  }, [onEndCall]);

  // Start call function
  const startCall = async () => {
    if (!vapiRef.current) {
      setError('Voice assistant not initialized');
      return;
    }

    try {
      console.log('[SimpleHomeVapiCall] Starting call with assistant:', DEFAULT_ASSISTANT_ID);
      setStatus('connecting');
      setError(null);
      setMessages([]);
      setCurrentTranscript('');

      // Start the call with the default assistant
      await vapiRef.current.start(DEFAULT_ASSISTANT_ID);
      
    } catch (error) {
      console.error('[SimpleHomeVapiCall] Error starting call:', error);
      setError(error.message || 'Failed to start call');
      setStatus('error');
    }
  };

  // Stop call function
  const stopCall = async () => {
    if (!vapiRef.current) return;

    try {
      console.log('[SimpleHomeVapiCall] Stopping call');
      await vapiRef.current.stop();
      setStatus('idle');
      setIsConnected(false);
    } catch (error) {
      console.error('[SimpleHomeVapiCall] Error stopping call:', error);
    }
  };

  // Render the component
  return (
    <div className="vapi-call-container">
      <div className="call-status">
        {status === 'idle' && (
          <button 
            onClick={startCall}
            className="start-call-button"
            disabled={!!error}
          >
            Start Voice Call
          </button>
        )}
        
        {status === 'connecting' && (
          <div className="connecting-status">
            <div className="spinner"></div>
            <p>Connecting...</p>
          </div>
        )}
        
        {status === 'connected' && (
          <div className="connected-status">
            <div className="call-active-indicator"></div>
            <p>Call Active</p>
            <button onClick={stopCall} className="end-call-button">
              End Call
            </button>
          </div>
        )}
        
        {status === 'error' && (
          <div className="error-status">
            <p className="error-message">{error}</p>
            <button onClick={startCall} className="retry-button">
              Try Again
            </button>
          </div>
        )}
      </div>

      {/* Current transcript display */}
      {currentTranscript && (
        <div className="current-transcript">
          <p><strong>You:</strong> {currentTranscript}</p>
        </div>
      )}

      {/* Message history */}
      {messages.length > 0 && (
        <div className="message-history">
          {messages.map((message, index) => (
            <div key={index} className={`message ${message.type}`}>
              <strong>{message.type === 'user' ? 'You' : 'Scout'}:</strong> {message.text}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SimpleHomeVapiCall;
