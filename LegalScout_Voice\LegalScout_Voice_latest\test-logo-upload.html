<!DOCTYPE html>
<html>
<head>
    <title>Test Logo Upload for <PERSON></title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 20px 0; }
        .preview { margin: 20px 0; }
        .preview img { max-width: 200px; max-height: 200px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 10px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Logo Upload <NAME_EMAIL></h1>
        
        <div class="upload-area">
            <input type="file" id="logoFile" accept="image/*" style="display: none;">
            <button onclick="document.getElementById('logoFile').click()">Choose Logo Image</button>
            <p>Select an image file to upload as your logo</p>
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <h3>Preview:</h3>
            <img id="previewImg" alt="Logo preview">
        </div>
        
        <button id="uploadBtn" onclick="uploadLogo()" disabled>Upload Logo to Supabase</button>
        
        <div id="result"></div>
    </div>

    <script>
        let selectedFile = null;
        let logoDataUrl = null;

        document.getElementById('logoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoDataUrl = e.target.result;
                    document.getElementById('previewImg').src = logoDataUrl;
                    document.getElementById('preview').style.display = 'block';
                    document.getElementById('uploadBtn').disabled = false;
                };
                reader.readAsDataURL(file);
            }
        });

        async function uploadLogo() {
            if (!logoDataUrl) {
                alert('Please select an image first');
                return;
            }

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Uploading logo...';

            try {
                // Update the attorney record with the logo
                const response = await fetch('/api/supabase-proxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'update',
                        table: 'attorneys',
                        data: {
                            logo_url: logoDataUrl,
                            profile_image: logoDataUrl,
                            button_image: logoDataUrl,
                            updated_at: new Date().toISOString()
                        },
                        filter: {
                            email: '<EMAIL>'
                        }
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Logo uploaded successfully!<br>
                            Data URL length: ${logoDataUrl.length} characters<br>
                            File name: ${selectedFile.name}<br>
                            File size: ${(selectedFile.size / 1024).toFixed(2)} KB
                        </div>
                    `;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="error">❌ Upload failed: ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
