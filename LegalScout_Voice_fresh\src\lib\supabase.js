/**
 * FIXED Supabase Client - Resolves Authentication Issues
 * 
 * This is a completely rewritten Supabase client that fixes:
 * 1. Headers undefined errors
 * 2. OAuth callback timing issues
 * 3. Realtime subscription failures
 * 4. Stub client fallback problems
 */

import { createClient } from '@supabase/supabase-js';

// Environment detection
const getEnvironmentInfo = () => {
  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
  const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
  const port = typeof window !== 'undefined' ? window.location.port : '5174';
  
  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1';
  const isProduction = hostname.includes('legalscout.net') || hostname.includes('vercel.app');
  
  const baseUrl = isDevelopment 
    ? `${protocol}//${hostname}${port ? `:${port}` : ''}`
    : `${protocol}//${hostname}`;
    
  return {
    isDevelopment,
    isProduction,
    hostname,
    protocol,
    port,
    baseUrl
  };
};

// Get Supabase configuration with proper fallbacks
const getSupabaseConfig = () => {
  // Try multiple environment variable patterns
  const url = 
    import.meta.env?.VITE_SUPABASE_URL ||
    window?.VITE_SUPABASE_URL ||
    process?.env?.VITE_SUPABASE_URL ||
    'https://utopqxsvudgrtiwenlzl.supabase.co';
    
  const key = 
    import.meta.env?.VITE_SUPABASE_KEY ||
    window?.VITE_SUPABASE_KEY ||
    process?.env?.VITE_SUPABASE_KEY ||
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
    
  return { url, key };
};

// Global client instance
let supabaseClient = null;
let clientInitialized = false;

/**
 * Create Supabase client with proper error handling
 */
const createSupabaseClient = () => {
  try {
    const { url, key } = getSupabaseConfig();
    const envInfo = getEnvironmentInfo();

    console.log('🔧 [Supabase-Fixed] Creating client for environment:', {
      isDevelopment: envInfo.isDevelopment,
      isProduction: envInfo.isProduction,
      baseUrl: envInfo.baseUrl,
      hasUrl: !!url,
      hasKey: !!key
    });

    if (!url || !key) {
      throw new Error(`Missing Supabase configuration: url=${!!url}, key=${!!key}`);
    }

    // Try minimal configuration first to avoid headers issues
    try {
      const minimalClient = createClient(url, key);
      console.log('✅ [Supabase-Fixed] Minimal client created successfully');
      return minimalClient;
    } catch (minimalError) {
      console.warn('⚠️ [Supabase-Fixed] Minimal client failed, trying full config:', minimalError);
    }
    
    // Create client with robust configuration
    const clientConfig = {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        // Fix for headers undefined error
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        storageKey: 'supabase.auth.token'
      }
    };

    // Only add global headers in browser environment to prevent server-side issues
    if (typeof window !== 'undefined') {
      clientConfig.global = {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      };
    }

    // Only add realtime config if in browser environment
    if (typeof window !== 'undefined') {
      clientConfig.realtime = {
        params: {
          eventsPerSecond: 10
        }
      };
    }

    const client = createClient(url, key, clientConfig);
    
    console.log('✅ [Supabase-Fixed] Client created successfully');
    return client;
    
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Failed to create client:', error);
    throw error;
  }
};

/**
 * Initialize Supabase client (singleton pattern)
 */
export const initializeSupabaseClient = async () => {
  if (clientInitialized && supabaseClient) {
    return supabaseClient;
  }
  
  try {
    console.log('🚀 [Supabase-Fixed] Initializing client...');
    supabaseClient = createSupabaseClient();
    clientInitialized = true;
    
    // Test the client to ensure it works
    await testClient(supabaseClient);
    
    console.log('✅ [Supabase-Fixed] Client initialized and tested successfully');
    return supabaseClient;
    
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Initialization failed:', error);
    clientInitialized = false;
    supabaseClient = null;
    throw error;
  }
};

/**
 * Test client functionality
 */
const testClient = async (client) => {
  try {
    // Test basic functionality
    const { data, error } = await client.from('attorneys').select('count').limit(1);
    if (error && !error.message.includes('permission')) {
      throw error;
    }
    console.log('✅ [Supabase-Fixed] Client test passed');
  } catch (error) {
    console.warn('⚠️ [Supabase-Fixed] Client test failed (may be normal):', error.message);
    // Don't throw - some errors are expected (like permission errors)
  }
};

/**
 * Get Supabase client (main export)
 */
export const getSupabaseClient = async () => {
  if (!supabaseClient || !clientInitialized) {
    return await initializeSupabaseClient();
  }
  return supabaseClient;
};

/**
 * Get real Supabase client (legacy compatibility)
 * This is the same as getSupabaseClient since we no longer use stub clients
 */
export const getRealSupabaseClient = async () => {
  return await getSupabaseClient();
};

/**
 * Enhanced authentication functions
 */
export const signInWithGoogle = async () => {
  try {
    const client = await getSupabaseClient();
    const envInfo = getEnvironmentInfo();
    
    const redirectUrl = `${envInfo.baseUrl}/auth/callback`;
    console.log('🔐 [Supabase-Fixed] Starting Google OAuth with redirect:', redirectUrl);
    
    const { data, error } = await client.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });
    
    if (error) {
      throw error;
    }
    
    console.log('✅ [Supabase-Fixed] Google OAuth initiated successfully');
    return { data, error: null };
    
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Google OAuth error:', error);
    return { data: null, error };
  }
};

export const getSession = async () => {
  try {
    const client = await getSupabaseClient();
    const { data: { session }, error } = await client.auth.getSession();
    
    if (error) {
      throw error;
    }
    
    return session;
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Get session error:', error);
    return null;
  }
};

export const getCurrentUser = async () => {
  try {
    const client = await getSupabaseClient();
    const { data: { user }, error } = await client.auth.getUser();
    
    if (error) {
      throw error;
    }
    
    return user;
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Get user error:', error);
    return null;
  }
};

export const signOut = async () => {
  try {
    const client = await getSupabaseClient();
    const { error } = await client.auth.signOut();
    
    if (error) {
      throw error;
    }
    
    console.log('✅ [Supabase-Fixed] Sign out successful');
    return { success: true };
  } catch (error) {
    console.error('❌ [Supabase-Fixed] Sign out error:', error);
    return { success: false, error };
  }
};

/**
 * Enhanced OAuth callback handling with retry logic
 */
export const handleOAuthCallback = async (retries = 3, delay = 1000) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🔐 [Supabase-Fixed] OAuth callback attempt ${attempt}/${retries}`);
      
      const client = await getSupabaseClient();
      
      // Wait for session to be available
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
      
      const { data: { session }, error: sessionError } = await client.auth.getSession();
      if (sessionError) {
        throw sessionError;
      }
      
      if (session?.user) {
        console.log('✅ [Supabase-Fixed] OAuth callback successful for:', session.user.email);
        return { success: true, user: session.user, session };
      }
      
      if (attempt === retries) {
        throw new Error('No user session found after OAuth callback');
      }
      
      console.log(`⚠️ [Supabase-Fixed] No session found, retrying in ${delay * attempt}ms...`);
      
    } catch (error) {
      console.error(`❌ [Supabase-Fixed] OAuth callback attempt ${attempt} failed:`, error);
      
      if (attempt === retries) {
        return { success: false, error: error.message };
      }
    }
  }
};

// Export the client for direct access (with initialization check)
export const supabase = new Proxy({}, {
  get(target, prop) {
    if (!supabaseClient || !clientInitialized) {
      console.warn('⚠️ [Supabase-Fixed] Client not initialized, returning stub for:', prop);
      // Return a stub function that throws a helpful error
      return () => {
        throw new Error('Supabase client not initialized. Call getSupabaseClient() first.');
      };
    }
    return supabaseClient[prop];
  }
});

// Legacy exports for compatibility
export const getSupabaseUrl = () => getSupabaseConfig().url;
export const getSupabaseKey = () => getSupabaseConfig().key;

export const isSupabaseConfigured = () => {
  const { url, key } = getSupabaseConfig();
  return !!(url && key && url !== 'your-supabase-url' && key !== 'your-anon-key');
};

// Legacy unified auth export for compatibility
export const unifiedAuth = {
  signInWithGoogle: signInWithGoogle
};

// Legacy emergency auth export for compatibility
export const emergencyAuth = {
  getCurrentUser: getCurrentUser
};

// Initialize immediately if in browser environment (with error handling)
if (typeof window !== 'undefined') {
  // Make emergency auth globally available for console testing
  window.emergencyAuth = emergencyAuth;
  console.log('🚨 [EmergencyAuth] Available globally as window.emergencyAuth');

  // Delay auto-initialization to prevent blocking the main thread
  setTimeout(() => {
    initializeSupabaseClient().catch(error => {
      console.error('❌ [Supabase-Fixed] Auto-initialization failed:', error);
      // Don't throw - let components handle initialization on demand
    });
  }, 100);
}
