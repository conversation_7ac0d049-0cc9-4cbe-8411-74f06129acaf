home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
home:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
home:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-2aa762de.js:85 [Vapi<PERSON>oader] Starting Vapi SDK loading process
index-2aa762de.js:85 [VapiLoader] Attempting to import @vapi-ai/web package
index-2aa762de.js:172 [VapiMcpService] Created clean fetch from iframe
index-2aa762de.js:172 [VapiMcpService] INFO: Vapi MCP Service initialized Object
hook.js:608 ❌ [Supabase] Error creating client, falling back to stub: TypeError: Cannot read properties of undefined (reading 'headers')
    at new pG (index-2aa762de.js:48:69816)
    at wL (index-2aa762de.js:48:72276)
    at bL (index-2aa762de.js:48:74304)
    at Ja (index-2aa762de.js:48:76324)
    at index-2aa762de.js:48:79812
    at index-2aa762de.js:48:81473
    at __ (index-2aa762de.js:40:24270)
    at kd (index-2aa762de.js:40:42393)
    at index-2aa762de.js:40:40710
    at D (index-2aa762de.js:25:1585)
overrideMethod @ hook.js:608
index.ts:5 Loaded contentScript
home:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
hook.js:608 Supabase client not available, using stub
overrideMethod @ hook.js:608
hook.js:608 [AttorneyConfig] ⚠️ No attorney found for subdomain: assistant1test
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
api.vapi.ai/call/web:1 
            
            
           Failed to load resource: the server responded with a status of 400 ()
hook.js:608 Response
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
speech-particles.js:1 Uncaught SyntaxError: Unexpected token '<'
hook.js:608 SpeechParticles: updateAudioSource function is not available
overrideMethod @ hook.js:608
api.vapi.ai/call/web:1 
            
            
           Failed to load resource: the server responded with a status of 400 ()
hook.js:608 Response
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
hook.js:608 Vapi error: Response
overrideMethod @ hook.js:608
hook.js:608 Continuing despite Vapi error
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
