import React from 'react';
import './SearchResultsDisplay.css';

/**
 * Component to display search results in the center pane
 * 
 * @param {Object} props Component props
 * @param {Object} props.results Search results to display
 * @param {string} props.query Original search query
 * @param {string} props.type Type of search (web, legal, etc.)
 * @param {Function} props.onClose Function to close the results panel
 */
const SearchResultsDisplay = ({ results, query, type = 'web', onClose }) => {
  if (!results || !results.items || results.items.length === 0) {
    return (
      <div className="search-results-container empty">
        <div className="search-results-header">
          <h3>Search Results</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="search-results-empty">
          <p>No results found for "{query}"</p>
        </div>
      </div>
    );
  }

  // Render different layouts based on the result type
  const renderResults = () => {
    switch (results.type) {
      case 'cards':
        return (
          <div className="search-results-cards">
            {results.items.map((item, index) => (
              <div key={index} className="search-result-card">
                {item.thumbnail && (
                  <div className="card-thumbnail">
                    <img src={item.thumbnail} alt={item.title} />
                  </div>
                )}
                <div className="card-content">
                  <h4 className="card-title">
                    <a href={item.url} target="_blank" rel="noopener noreferrer">
                      {item.title}
                    </a>
                  </h4>
                  <p className="card-description">{item.description}</p>
                  <div className="card-url">{new URL(item.url).hostname}</div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'timeline':
        return (
          <div className="search-results-timeline">
            <div className="timeline-track">
              {results.items.map((item, index) => (
                <div key={index} className="timeline-item">
                  <div className="timeline-point"></div>
                  <div className="timeline-date">{item.date}</div>
                  <div className="timeline-content">
                    <h4 className="timeline-title">
                      <a href={item.url} target="_blank" rel="noopener noreferrer">
                        {item.title}
                      </a>
                    </h4>
                    <p className="timeline-description">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'statute':
        return (
          <div className="search-results-statute">
            <h3 className="statute-title">{results.title}</h3>
            {results.sections.map((section, index) => (
              <div key={index} className="statute-section">
                <h4 className="section-title">{section.title}</h4>
                <div className="section-text">{section.text}</div>
                <a href={section.url} target="_blank" rel="noopener noreferrer" className="section-link">
                  View full text
                </a>
              </div>
            ))}
          </div>
        );

      case 'concept-map':
        return (
          <div className="search-results-concept-map">
            <div className="concept-map-container">
              <div className="concept-map-center">
                <div className="concept-node main-concept">
                  <span>{query}</span>
                </div>
              </div>
              <div className="concept-map-branches">
                {results.items.map((item, index) => (
                  <div key={index} className="concept-branch">
                    <div className="concept-node">
                      <a href={item.url} target="_blank" rel="noopener noreferrer">
                        {item.title}
                      </a>
                    </div>
                    <div className="concept-description">{item.description}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="search-results-list">
            {results.items.map((item, index) => (
              <div key={index} className="search-result-item">
                <h4>
                  <a href={item.url} target="_blank" rel="noopener noreferrer">
                    {item.title}
                  </a>
                </h4>
                <p>{item.description}</p>
                <div className="item-url">{new URL(item.url).hostname}</div>
              </div>
            ))}
          </div>
        );
    }
  };

  return (
    <div className="search-results-container">
      <div className="search-results-header">
        <h3>
          {type === 'legal' ? 'Legal Research' : 'Search Results'}: "{query}"
        </h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      <div className="search-results-content">
        {renderResults()}
      </div>
      <div className="search-results-footer">
        <div className="search-attribution">
          Results powered by {type === 'legal' ? 'Legal Research API' : 'Web Search'}
        </div>
      </div>
    </div>
  );
};

export default SearchResultsDisplay;
