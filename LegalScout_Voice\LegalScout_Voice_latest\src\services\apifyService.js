import { ApifyClient } from 'apify-client';

class ApifyService {
  constructor() {
    // Use the API token from your documentation
    this.client = new ApifyClient({
      token: '**********************************************',
    });
  }
  
  /**
   * Search for attorneys based on location and practice area
   * @param {string} location - The location to search in
   * @param {string} practiceArea - The practice area to search for
   * @param {number} radius - Search radius in miles
   * @param {number} limit - Maximum number of results to return per page
   * @param {number} offset - Number of results to skip (for pagination)
   * @returns {Object} The search results with pagination info
   */
  async searchAttorneys(location, practiceArea, radius = 25, limit = 5, offset = 0) {
    try {
      console.log(`Searching for ${practiceArea} attorneys near ${location} within ${radius} miles (limit: ${limit}, offset: ${offset})`);
      
      // Prepare Actor input based on the structure in your Apify docs
      const input = {
        "actors": [
          "lukaskrivka/google-maps-with-contact-details"
        ],
        "enableActorAutoLoading": false,
        "maxActorMemoryBytes": 4096,
        "debugActor": "lukaskrivka/google-maps-with-contact-details",
        "debugActorInput": {
          "searchStrings": [`${practiceArea} attorney ${location}`],
          // Request more results than needed for pagination
          "maxCrawledPlaces": 50,
          "exportPlaceUrls": false,
          "maxCrawledPlacesPerSearch": 30,
          "searchRadius": radius * 1609, // Convert miles to meters
          "language": "en",
          "maxReviews": 0 
        }
      };

      // Run the Actor using the exact pattern from your documentation
      const run = await this.client.actor("1lSvMAaRcadrM1Vgv").call(input);
      console.log(`Apify search completed with run ID: ${run.id}`);

      // Fetch all results from the run's dataset
      const { items, total } = await this.client.dataset(run.defaultDatasetId).listItems({
        limit: 100, // Get all available results to handle locally
        offset: 0
      });
      
      // Format all attorney results
      const formattedResults = items.map(item => {
        return {
          name: item.title || "Unknown Attorney",
          firm: item.title || "Law Office",
          specialty: practiceArea,
          address: item.address || "Address unavailable",
          phone: item.phoneNumber || item.phone || "Phone unavailable",
          email: item.email || null,
          website: item.website || null,
          latitude: item.location?.lat,
          longitude: item.location?.lng,
          distance: item.distanceMeters 
            ? `${(item.distanceMeters / 1609).toFixed(1)} miles` 
            : "Distance unknown",
          rating: item.rating || null,
          reviewCount: item.reviewsCount || 0,
          source: "google_maps",
          placeId: item.placeId || null
        };
      });
      
      // Calculate pagination information
      const totalResults = formattedResults.length;
      const totalPages = Math.ceil(totalResults / limit);
      
      // Apply pagination on the client side
      const paginatedResults = formattedResults.slice(offset, offset + limit);
      
      // Return paginated results with metadata
      return {
        results: paginatedResults,
        pagination: {
          total: totalResults,
          limit,
          offset,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages
        }
      };
    } catch (error) {
      console.error('Apify search failed:', error);
      throw error;
    }
  }
}

// Singleton instance
let apifyServiceInstance = null;

export const getApifyService = () => {
  if (!apifyServiceInstance) {
    apifyServiceInstance = new ApifyService();
  }
  return apifyServiceInstance;
}; 