emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
dashboard:32 ✅ Vapi keys set globally
dashboard:53 ✅ Supabase keys set globally - should load correct assistant by domain
dashboard:64 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:68 🔧 [EMERGENCY] Adding process polyfill
dashboard:75 ✅ [EMERGENCY] Process polyfill added
dashboard:86 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:116 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
dashboard:119 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
dashboard:235 Supabase loaded from CDN
dashboard:245 Creating Supabase client from CDN
dashboard:249 Supabase client created from CDN
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1420 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:410 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:443 [AttorneyProfileManager] Realtime subscription set up using channel API
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:248 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:249 [VapiMcpService] API key being used: 310f0d43...
vapiMcpService.js:261 [VapiMcpService] Using direct API key for server operations: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:21 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:118 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
emergency-api-key-fix.js:19 🔑 [EmergencyApiKeyFix] Setting global environment variables...
emergency-api-key-fix.js:38 ✅ [EmergencyApiKeyFix] Global environment variables set
emergency-api-key-fix.js:43 🔧 [EmergencyApiKeyFix] Creating API key helper function...
emergency-api-key-fix.js:57 ✅ [EmergencyApiKeyFix] API key helper function created
emergency-api-key-fix.js:62 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
emergency-api-key-fix.js:129 ✅ [EmergencyApiKeyFix] Fetch override applied
emergency-api-key-fix.js:134 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
emergency-api-key-fix.js:138 [EmergencyApiKeyFix] Updating VapiMcpService API key...
emergency-api-key-fix.js:150 ✅ [EmergencyApiKeyFix] Existing services updated
emergency-api-key-fix.js:164 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
critical-production-fix.js:232 🚀 [CriticalProductionFix] Initializing all critical fixes...
critical-production-fix.js:19 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
critical-production-fix.js:54 ✅ [CriticalProductionFix] Vapi API key configuration fixed
critical-production-fix.js:175 🔧 [CriticalProductionFix] Fixing environment variables...
critical-production-fix.js:203 ✅ [CriticalProductionFix] Environment variables fixed
critical-production-fix.js:59 🌐 [CriticalProductionFix] Fixing CORS issues...
critical-production-fix.js:115 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
critical-production-fix.js:120 🛡️ [CriticalProductionFix] Fixing CSP issues...
critical-production-fix.js:170 ✅ [CriticalProductionFix] CSP issues fixed
critical-production-fix.js:208 📦 [CriticalProductionFix] Fixing import statement issues...
critical-production-fix.js:227 ✅ [CriticalProductionFix] Import statement issues fixed
critical-production-fix.js:241 🎉 [CriticalProductionFix] All critical fixes applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
production-cors-fix.js:181 [ProductionCorsFix] 🚀 Initializing all production fixes...
production-cors-fix.js:59 [ProductionCorsFix] 🌍 Ensuring production environment variables...
production-cors-fix.js:84 [ProductionCorsFix] ✅ Environment variables configured
production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
production-cors-fix.js:54 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:89 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:109 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:114 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:131 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:136 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:149 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:193 [ProductionCorsFix] 🎉 All production fixes initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
hook.js:608 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>. Error Component Stack
    at form (<anonymous>)
    at div (<anonymous>)
    at WebsiteImporter (WebsiteImporter.jsx:11:28)
    at form (<anonymous>)
    at div (<anonymous>)
    at ProfileTab (ProfileTab.jsx:20:23)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at DashboardNew (DashboardNew.jsx:36:20)
    at RenderedRoute (react-router-dom.js?v=306847c5:5722:26)
    at Routes (react-router-dom.js?v=306847c5:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at App (App.jsx:321:20)
    at LegalScoutApp (<anonymous>)
    at Provider (AuthContext.jsx:20:16)
    at AuthProvider (AuthContext.jsx:61:32)
    at InnerAuthProvider (SyncAuthProvider.jsx:22:30)
    at Provider (SyncContext.jsx:13:16)
    at SyncProvider (SyncContext.jsx:34:32)
    at SyncAuthProvider (SyncAuthProvider.jsx:43:29)
    at AttorneyStateProvider (AttorneyStateContext.jsx:25:41)
    at Provider (ThemeContext.jsx:6:16)
    at ThemeProvider (ThemeContext.jsx:31:33)
    at Router (react-router-dom.js?v=306847c5:6397:13)
    at BrowserRouter (react-router-dom.js?v=306847c5:8631:3)
    at ErrorBoundary (ErrorBoundary.jsx:5:5)
    at ProductionErrorBoundary (ProductionErrorBoundary.jsx:5:5)
overrideMethod @ hook.js:608
printWarning @ chunk-Q72EVS5P.js?v=306847c5:521
error @ chunk-Q72EVS5P.js?v=306847c5:505
validateDOMNesting @ chunk-Q72EVS5P.js?v=306847c5:8267
createInstance @ chunk-Q72EVS5P.js?v=306847c5:8339
completeWork @ chunk-Q72EVS5P.js?v=306847c5:16311
completeUnitOfWork @ chunk-Q72EVS5P.js?v=306847c5:19252
performUnitOfWork @ chunk-Q72EVS5P.js?v=306847c5:19234
workLoopSync @ chunk-Q72EVS5P.js?v=306847c5:19165
renderRootSync @ chunk-Q72EVS5P.js?v=306847c5:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18706
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 new preview iframes in added content
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
ProfileTab.jsx:54 Attorney object in ProfileTab: null
ProfileTab.jsx:55 User object in ProfileTab: null
ProfileTab.jsx:70 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:101 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:97 [useStandaloneAttorney] Found attorney in localStorage: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
useStandaloneAttorney.js:116 [useStandaloneAttorney] Setting initial attorney: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:174 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:231 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:232 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:483 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
ProfileTab.jsx:54 Attorney object in ProfileTab: null
ProfileTab.jsx:55 User object in ProfileTab: null
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:116 [useStandaloneAttorney] Setting initial attorney: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:174 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:231 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:232 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:483 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
ProfileTab.jsx:70 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:101 Using email from previewConfig or previous state: 
ProfileTab.jsx:70 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:101 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
index.ts:5 Loaded contentScript
ProfileTab.jsx:54 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:55 User object in ProfileTab: null
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:70 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:81 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:70 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:81 Updated form data with attorney email from database: <EMAIL>
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
production-cors-fix.js:154 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
production-cors-fix.js:167 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
AttorneyProfileManager.js:385 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
supabase.js:116 Supabase connection test successful!
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
DashboardNew.jsx:188 [DashboardNew] 🛡️ User found: <EMAIL>
DashboardNew.jsx:191 [DashboardNew] 🛡️ Calling AUTOMATED robust state handler for: <EMAIL>
robust-state-handler.js:90 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
 🔄 [RobustStateHandler] Attempt 1/3 for <EMAIL>
 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [DashboardNew] 🛡️ User found: <EMAIL>
 [DashboardNew] 🛡️ Calling AUTOMATED robust state handler for: <EMAIL>
 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
 ⏳ [RobustStateHandler] Operation already in progress, waiting...
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
 [App] Available subdomains for testing: (5) ['coscettelaw', 'damon', 'damonkost', 'generalcounselonline', 'scout']
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
 [DashboardNew] fetchAttorneyData called.
 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
 [DashboardNew] 🛡️ Checking for robust state handler...
 [DashboardNew] ✅ Robust state handler found, calling it...
 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
 ⏳ [RobustStateHandler] Operation already in progress, waiting...
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 ✅ [RobustStateHandler] Found existing attorney by user_id: 87756a2c-a398-43f2-889a-b8815684df71
 🎯 [RobustStateHandler] Resolving assistant state for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 ✅ [RobustStateHandler] Found 1 assistant - using it
 [DashboardNew] 🛡️ AUTOMATED robust state handler result: {success: true, attorney: {…}, assistants: Array(1), selectedAssistant: {…}, needsCreation: false, …}
 [DashboardNew] ✅ AUTOMATED: Attorney state resolved successfully!
 [DashboardNew] ✅ AUTOMATED: Attorney: LegalScout Assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] ✅ AUTOMATED: Standalone manager updated
 [DashboardNew] ✅ AUTOMATED: State resolved, system should update automatically
 [DashboardNew] 🛡️ AUTOMATED robust state handler result: {success: true, attorney: {…}, assistants: Array(1), selectedAssistant: {…}, needsCreation: false, …}
 [DashboardNew] ✅ AUTOMATED: Attorney state resolved successfully!
 [DashboardNew] ✅ AUTOMATED: Attorney: LegalScout Assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] ✅ AUTOMATED: Standalone manager updated
 [DashboardNew] ✅ AUTOMATED: State resolved, system should update automatically
 [DashboardNew] 🔍 Robust state handler result: {success: true, attorney: {…}, assistants: Array(1), selectedAssistant: {…}, needsCreation: false, …}
 [DashboardNew] ✅ Attorney state resolved via robust handler: {firm: 'LegalScout', email: '<EMAIL>', assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', needsCreation: false}
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] ✅ Robust state handler succeeded, skipping fallback logic
 [DashboardIframeManager] Found 1 new preview iframes in added content
 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Iframe already ready
 [DashboardIframeManager] Found 1 new preview iframes in added content
 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Iframe already ready
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
 🚨 [CriticalProductionFix] Starting critical production fixes...
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🚨 [CriticalProductionFix] Starting critical production fixes...
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 [vite] connecting...
 [vite] connecting...
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [vite] connected.
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [DashboardIframeManager] Iframe observer set up successfully
 [DashboardIframeManager] Iframe observer set up successfully
 [vite] connected.
 ✅ [CleanAuthSolution] Auth state monitoring set up
 ✅ [CleanAuthSolution] Auth state monitoring set up
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [initAttorneyProfileManager] Initialization complete
 🔑 [EmergencyApiKeyFix] Setting global environment variables...
 ✅ [EmergencyApiKeyFix] Global environment variables set
 🔧 [EmergencyApiKeyFix] Creating API key helper function...
 ✅ [EmergencyApiKeyFix] API key helper function created
 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
 ✅ [EmergencyApiKeyFix] Fetch override applied
 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
 [EmergencyApiKeyFix] Updating VapiMcpService API key...
 ✅ [EmergencyApiKeyFix] Existing services updated
 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
 🚀 [CriticalProductionFix] Initializing all critical fixes...
 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
 ✅ [CriticalProductionFix] Vapi API key configuration fixed
 🔧 [CriticalProductionFix] Fixing environment variables...
 ✅ [CriticalProductionFix] Environment variables fixed
 🌐 [CriticalProductionFix] Fixing CORS issues...
 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
 🛡️ [CriticalProductionFix] Fixing CSP issues...
 ✅ [CriticalProductionFix] CSP issues fixed
 📦 [CriticalProductionFix] Fixing import statement issues...
 ✅ [CriticalProductionFix] Import statement issues fixed
 🎉 [CriticalProductionFix] All critical fixes applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 [UnifiedBannerFix] Ensuring upload interface is visible
 [UnifiedBannerFix] Ensuring upload interface is visible
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [initAttorneyProfileManager] Initialization complete
 🔑 [EmergencyApiKeyFix] Setting global environment variables...
 ✅ [EmergencyApiKeyFix] Global environment variables set
 🔧 [EmergencyApiKeyFix] Creating API key helper function...
 ✅ [EmergencyApiKeyFix] API key helper function created
 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
 ✅ [EmergencyApiKeyFix] Fetch override applied
 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
 [EmergencyApiKeyFix] Updating VapiMcpService API key...
 ✅ [EmergencyApiKeyFix] Existing services updated
 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
 🚀 [CriticalProductionFix] Initializing all critical fixes...
 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
 ✅ [CriticalProductionFix] Vapi API key configuration fixed
 🔧 [CriticalProductionFix] Fixing environment variables...
 ✅ [CriticalProductionFix] Environment variables fixed
 🌐 [CriticalProductionFix] Fixing CORS issues...
 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
 🛡️ [CriticalProductionFix] Fixing CSP issues...
 ✅ [CriticalProductionFix] CSP issues fixed
 📦 [CriticalProductionFix] Fixing import statement issues...
 ✅ [CriticalProductionFix] Import statement issues fixed
 🎉 [CriticalProductionFix] All critical fixes applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566400}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566400}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566408}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566408}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566632}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566632}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566634}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566634}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566654}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566654}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566656}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566656}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 Supabase connection test successful!
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566694}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566694}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566703}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566703}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [UnifiedBannerFix] Ensuring upload interface is visible
 [ProductionCorsFix] 🧪 Testing API connectivity...
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Using SECRET key for server operations: server
 [CriticalProductionFix] SECRET key value: 6734febc...
 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
 [EmergencyApiKeyFix] Using API key: 6734febc...
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566809}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566809}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566810}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276566810}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567077}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567077}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567094}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567094}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 [UnifiedBannerFix] Ensuring upload interface is visible
 [ProductionCorsFix] 🧪 Testing API connectivity...
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Using SECRET key for server operations: server
 [CriticalProductionFix] SECRET key value: 6734febc...
 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
 [EmergencyApiKeyFix] Using API key: 6734febc...
 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
 Supabase connection test successful!
 [UnifiedBannerFix] Ensuring upload interface is visible
 [UnifiedBannerFix] Ensuring upload interface is visible
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [App] Available subdomains for testing: (5) ['coscettelaw', 'damon', 'damonkost', 'generalcounselonline', 'scout']
 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'NEE!', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: NEE!
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABDAAAADHCAYAAADvTKOkAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAP+lSURBVHhe7J0HeFTF18Y/0qiiNOmg2Av2LooVexd7F8FesFcUaTbEhiJgr4ii0kTxj0hT6UVAijRBeg0Bkuzyvb/ZO+vNpm2S3U0C8z7Pydy9uXfmzJl+7pkz/+fg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4JAIjBkzZnfRwZ988knzp556qnnNmjUb6nZq6L8ODg4ODg4ODg4ODg4ODg4OpYRgMFg1Ozv7AYWzsrKyVijM2rZtW3YgEMjeunXrinbt2k2tUKFCp4oVKzbzXikTGDFiRIp4PVW8P6Owf2Zm5hxdz9P1zIyMjH7z589vP2zYsNN69uy5e4cOHZK816LGhAkTUpcuXVpFMiGNpxTvN7qer+uVul4p+az44osvlqalpf3jveLg4ODg4ODg4ODg4ODg4BBr9OvXL00L8b1FZ2sx/pEW55m6zs7MzAwoDGqhDnGddf/993+sVw4KvVnqSJs+ffrR27dvbyvevhW/meLfAL4t4F152qbbm/RzoX6P1vUnuu7ovXul/n+h6FJdXyO6X/e7KfxKNEnPLhZt0bWRi54jfkOKi9/ZXH/wwQcBjy8HBwcHBwcHBwcHBwcHB4dYQ4v1XTMyMm7QIvxLLdRnKAwjM5M1e0iJATZv3vxD/fr1r9Zr+4qqmghKCYccckhVsXSNePxc4TSrcIkW5En59X6FYPMJ8vpfVlaWud62bZsJgb3+999/gx5rDg4ODg4ODg4ODg4ODg4Oscb27dvraf3dnbW4iBX8di3WdTs3tKhf+8orr8zWa6+JmpsISglnnnkmW15uEr8Ds7KyZitE42D5NAqISCUEQCkTed//O6/3/L+tkoN7nkIjsHXrVh5wFhgODg4ODg4ODg4ODg4ODnFAktb6VbTw3k/0thbhZisEC3S7YPdfewisXr06s2LFil/r/WNC0ZQODjzwwGri5xbRENFf4jNgtBeCVTIAP/8ReckX1vLED8nHu8rTUiOwdu1ap8BwcHBwcHBwcHBwcHBwcIg13n///d208G4lwvnlSC3K0QGwGDfg2v62i3cW7jx34YUXDlUU54hqiNKIrxRQbdGiRe0yMzNHiM+/xR7KF6PAgFfLOzzbaxsWBJ7xk19xAfxx+BUd7733nttC4uDg4ODg4ODg4ODg4OAQa6Snp9fT4v4Orb2/F81hEW4X5yz6bej392CVAV9//fVoRXGH6FDRbsRXCqhy0EEHXfbPP//0Fk/jYQ8e4W/jxo3B+fPnB5cvX25+s9XDhtHC/zzh33//HVywYIGRh6e4QFg8sPmnn35aVaNGjX89vhwcHBwcHBwcHBwcHBwcHGIFLbz31CL9dYVrFGYoNIt2v/LCWhiwgLfX3N+yZcsf1atXf0LRtBTtbiJMPCqIKlerVq320UcffcXpp5/+9kEHHTS8Ro0aC3R/WXJy8nKFq+rXr7+ua9eum5UHlA0oHQo0w7D5RxabNm0Ktm7dOlilShW2hyxOSkoao/CZPffc85BDDz10t5YtW6bot4ODg4ODg4ODg4ODg4ODQxyQPGLEiGqZmZlHap3OcaLhBT2L9khwz+zLEOziPisra9Ytt9zSR3HdKNrTxFq2sUubNm32FOu3isaK0MaEMpMHJBt8fWQffvjhmXq3Z2pq6hEK64qSiczBwcHBwcHBwcHBwcHBwSHOaNSoUc2srKxTt2/f/lggEBgVUk3kPLlj0qRJJvRbIwCfVca2ESNGbFB0A1JSUk4KxVz2MXbs2CPE+wsilBj/ishg2Pmnri2yu3TpslmvrBZhaVKJ9x0cHBwcHBwcHBwcHBwcHBKEBx98sKkW6A+IhgYCgQUis3i3Sgpw6KGHsk0krLggtMoMC3xBNG3adKSivFRUms48o0abNm32FevtRB+L/hKZbSVGAIKuySeZ3qK8/ZacnNxLr10kSjURODg4ODg4ODg4ODg4ODg4JAYzZ848OCsr6wPRJi3U8dBpFu9WQTFmzJhgSkpKcPDgwea3X4lhr0PuJILBhx56aL4W+R8p2rai/UwCZRi1a9dGgXGn6DMRjktzKTD4PXHixE3K1wd6BeXFAaIk3ndwcHBwcHBwcHBwcHBwcEgQtEA/JBAIfJKVlcX5oObcUYXh7ROPPPIIx4EGb7nllrDCAlilhQ+BqVOnssVikugt0Qmiso7mmZmZT4n3H5SfRQqzlEcymUOB8cMPP2yqUKGCU2A4ODg4ODiUN+Doa/HixQ03bdp0UEZGRostW7acmZ6efu7ff/991u+//37ywIEDD+3Tp0+Dl156qWqHDh2cN24HBwcHB4eyid179Ohxphboz4iMI0ut3TG7MIt31vFYYTRp0iSoxXtw9913z2VxQWhPI9E1/9x22GGHpSvun5OTk88JJVOmccCyZcseUL6+Fs0nG6KwBYYng8CHH364Sc/2E90swoGnc97p4ODg4FBsVNBCOWnOnDnVV61a1UADz+Gi8zSQnrNy5cqTJk6ceMj333/fpGvXrjX0XJH2Yy5durSK4qojOlZ0vegGLdSvbNGixYn6N8eEsQeSY7t2KGjMThLVUH4xrXxKA/hUhTi34stEji8w9rf/HsBjt8BEaK3o18mTJ/fo1KnTOYccckhVJeH2jjo4ODg4OJQiqlSpcqQW7301Vi/ROL1BlI0SwqzcBZQXmkMZ5YUeN/Tzzz+Hx3yruAAZGebUVZDdvXv3bXr2V9F5orKOPaZMmXKV8v2meJ8hYp4Tdu7h5TX7pZde2qhnPxa1Fh0sip0CQ7Ku8OGHH9bq0aPHPo8//njdyy+/vHJZPI+1X79+yR999NHun3766f6vvvrqAffdd98B4rWO/rXDTYQdHBwc4gX6fI0v7TS4TBSxwGY01aUBY48ZfLx9nAGFDEwsqBmkftT1h6JXdd1dxNnnX+mZMRrI5uk6XcRe0IB+K/gP69evX12nTp0ZycnJr4qNQ0LclF8gR1GasraX6HnRn5LDSpHEYwSZQzsRuvWfbCFAiKwgew94zxv5K9yoCdP/3n333RcuvvjiVkq+xJOAGTNmGN6V7plKq6Pof0prnGiK6HfdHzl69Og377///hv33HPPo/VK9dCbDg4ODg4OOydatWp1jMbOzzVmrlO4RWTGa80HDPj9wAMPoMAI6PGs1NTUbW3btjUWCvwPeI+aZ3HiqfezlyxZkpmWlvZ7UlLS9Xqvtqgsn9jRaOLEiReI727KwmTmKaIcCgwh+/nnn98gObyned/5egffHsXbQiJZVUhPT6+vSNsr8pFKayHpKAzw5UcTFt02qcIEwh6/atWqHtOmTbvuu+++O6hnz541VAhx/Qq0ePHiyhkZGY3F6ylbt259Urx9Jz6sgxD4gj/Dpwp97bp16xZ26NChiwp9H71eTeT21zg4ODjkRtKQIUMaqf88Rt3nWwpRNtj+VEEIjEH+3xbci7yf3z0/7G+FAQ1mWeLje1F52OOZJ5YuXVqb8UlZelRj0CiFZlyKVED4r5GBJT/8zwD/M/53lA4mmXzZURBYunz58ok33HDDfWJnlxBX0WP+/Pm7itczlMRTCn9RnHZczQHSZU7g/X/+gw8+OFCvPyDay0Tk4ODg4OCwk+Gbb75hDvWphsY1Ghu3hIbI/5QSKCT22GMPLC9QYODbYu7ee++9SOvv5Xpso4gXzLPMGwADu4Ks00477S89/7roMlFZHmvrjx8/nm00zykbEyQDs0YnG5b43aVLl/UVKlTorefPErFOL/oa/eeff26qRB5ThL8q4sUKc01YImELReBZiC9reFtdoTjmiUboursK5frFixcfPGHChFTMkr0k84R4SBbx5QeTZY5g6SX6XfSPaIPi3CYKJ+wHBW0LOwJbhg4dipbntUqVKjXxknJwcHBw8NCyZctK6iuv2bJlSz/1sdN0nUkfr2vTieYF3xjgH5QM+J///8Afnw39R4h99dVXAfXTA1NSUsqdAmPDhg37ZmZm3qVsfKkQaxMypaz9JxMQOU5F/t/C/0x+5RApX0xPvXuku/CPP/4YeeKJJ6LIwBoxKmj8bab33xZhu5qp3wZECuDDkv0tkGjmoEGDvq9ateppioYtLc5Xh4ODg4PDTgWNw0drXESBsVrjYoZvrDRjqdbC2/XYds11sMKYrOsPRZ+OGTPmaz02Se/z8ciAMR1s3bqVSLLef//9pXp2cFJS0lMKjxKVVRgFhrL7LFmGd1FYgQEkn+wXX3xxvZ7tKzpXtK+oSAqMGvfdd9/xiudexfeTQmvimwv+hC30fK57kYh8j2ves2TvMWHzP+f/X2Q6/KZg/ff8sO8APYfQsq6//vpByu+NokNFWGM4ODg4OAh33nlnNXWXT4tWq7/cnJcy2Pa3hJ
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/png;base64,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
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are the knights that say NEEE!

You will not let the user pass .

Go full method!
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/png;base64,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
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are the knights that say NEEE!

You will not let the user pass .

Go full method!
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [CriticalProductionFix] Using SECRET key for server operations: server
 [CriticalProductionFix] SECRET key value: 6734febc...
 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
 [EmergencyApiKeyFix] Using API key: 6734febc...
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567750}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567750}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567750}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567753}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567753}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567753}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [App] Available subdomains for testing: (5) ['coscettelaw', 'damon', 'damonkost', 'generalcounselonline', 'scout']
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'NEE!', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: NEE!
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'NEE!', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: NEE!
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/png;base64,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
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are the knights that say NEEE!

You will not let the user pass .

Go full method!
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/png;base64,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
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are the knights that say NEEE!

You will not let the user pass .

Go full method!
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABDAAAADHCAYAAADvTKOkAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAP+lSURBVHhe7J0HeFTF18Y/0qiiNOmg2Av2LooVexd7F8FesFcUaTbEhiJgr4ii0kTxj0hT6UVAijRBeg0Bkuzyvb/ZO+vNpm2S3U0C8z7Pydy9uXfmzJl+7pkz/+fg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4JAIjBkzZnfRwZ988knzp556qnnNmjUb6nZq6L8ODg4ODg4ODg4ODg4ODg4OpYRgMFg1Ozv7AYWzsrKyVijM2rZtW3YgEMjeunXrinbt2k2tUKFCp4oVKzbzXikTGDFiRIp4PVW8P6Owf2Zm5hxdz9P1zIyMjH7z589vP2zYsNN69uy5e4cOHZK816LGhAkTUpcuXVpFMiGNpxTvN7qer+uVul4p+az44osvlqalpf3jveLg4ODg4ODg4ODg4ODg4BBr9OvXL00L8b1FZ2sx/pEW55m6zs7MzAwoDGqhDnGddf/993+sVw4KvVnqSJs+ffrR27dvbyvevhW/meLfAL4t4F152qbbm/RzoX6P1vUnuu7ovXul/n+h6FJdXyO6X/e7KfxKNEnPLhZt0bWRi54jfkOKi9/ZXH/wwQcBjy8HBwcHBwcHBwcHBwcHB4dYQ4v1XTMyMm7QIvxLLdRnKAwjM5M1e0iJATZv3vxD/fr1r9Zr+4qqmghKCYccckhVsXSNePxc4TSrcIkW5En59X6FYPMJ8vpfVlaWud62bZsJgb3+999/gx5rDg4ODg4ODg4ODg4ODg4Oscb27dvraf3dnbW4iBX8di3WdTs3tKhf+8orr8zWa6+JmpsISglnnnkmW15uEr8Ds7KyZitE42D5NAqISCUEQCkTed//O6/3/L+tkoN7nkIjsHXrVh5wFhgODg4ODg4ODg4ODg4ODnFAktb6VbTw3k/0thbhZisEC3S7YPdfewisXr06s2LFil/r/WNC0ZQODjzwwGri5xbRENFf4jNgtBeCVTIAP/8ReckX1vLED8nHu8rTUiOwdu1ap8BwcHBwcHBwcHBwcHBwcIg13n///d208G4lwvnlSC3K0QGwGDfg2v62i3cW7jx34YUXDlUU54hqiNKIrxRQbdGiRe0yMzNHiM+/xR7KF6PAgFfLOzzbaxsWBJ7xk19xAfxx+BUd7733nttC4uDg4ODg4ODg4ODg4OAQa6Snp9fT4v4Orb2/F81hEW4X5yz6bej392CVAV9//fVoRXGH6FDRbsRXCqhy0EEHXfbPP//0Fk/jYQ8e4W/jxo3B+fPnB5cvX25+s9XDhtHC/zzh33//HVywYIGRh6e4QFg8sPmnn35aVaNGjX89vhwcHBwcHBwcHBwcHBwcHGIFLbz31CL9dYVrFGYoNIt2v/LCWhiwgLfX3N+yZcsf1atXf0LRtBTtbiJMPCqIKlerVq320UcffcXpp5/+9kEHHTS8Ro0aC3R/WXJy8nKFq+rXr7+ua9eum5UHlA0oHQo0w7D5RxabNm0Ktm7dOlilShW2hyxOSkoao/CZPffc85BDDz10t5YtW6bot4ODg4ODg4ODg4ODg4ODQxyQPGLEiGqZmZlHap3OcaLhBT2L9khwz+zLEOziPisra9Ytt9zSR3HdKNrTxFq2sUubNm32FOu3isaK0MaEMpMHJBt8fWQffvjhmXq3Z2pq6hEK64qSiczBwcHBwcHBwcHBwcHBwSHOaNSoUc2srKxTt2/f/lggEBgVUk3kPLlj0qRJJvRbIwCfVca2ESNGbFB0A1JSUk4KxVz2MXbs2CPE+wsilBj/ishg2Pmnri2yu3TpslmvrBZhaVKJ9x0cHBwcHBwcHBwcHBwcHBKEBx98sKkW6A+IhgYCgQUis3i3Sgpw6KGHsk0krLggtMoMC3xBNG3adKSivFRUms48o0abNm32FevtRB+L/hKZbSVGAIKuySeZ3qK8/ZacnNxLr10kSjURODg4ODg4ODg4ODg4ODg4JAYzZ848OCsr6wPRJi3U8dBpFu9WQTFmzJhgSkpKcPDgwea3X4lhr0PuJILBhx56aL4W+R8p2rai/UwCZRi1a9dGgXGn6DMRjktzKTD4PXHixE3K1wd6BeXFAaIk3ndwcHBwcHBwcHBwcHBwcEgQtEA/JBAIfJKVlcX5oObcUYXh7ROPPPIIx4EGb7nllrDCAlilhQ+BqVOnssVikugt0Qmiso7mmZmZT4n3H5SfRQqzlEcymUOB8cMPP2yqUKGCU2A4ODg4ODiUN+Doa/HixQ03bdp0UEZGRostW7acmZ6efu7ff/991u+//37ywIEDD+3Tp0+Dl156qWqHDh2cN24HBwcHB4eyid179Ohxphboz4iMI0ut3TG7MIt31vFYYTRp0iSoxXtw9913z2VxQWhPI9E1/9x22GGHpSvun5OTk88JJVOmccCyZcseUL6+Fs0nG6KwBYYng8CHH364Sc/2E90swoGnc97p4ODg4FBsVNBCOWnOnDnVV61a1UADz+Gi8zSQnrNy5cqTJk6ceMj333/fpGvXrjX0XJH2Yy5durSK4qojOlZ0vegGLdSvbNGixYn6N8eEsQeSY7t2KGjMThLVUH4xrXxKA/hUhTi34stEji8w9rf/HsBjt8BEaK3o18mTJ/fo1KnTOYccckhVJeH2jjo4ODg4OJQiqlSpcqQW7301Vi/ROL1BlI0SwqzcBZQXmkMZ5YUeN/Tzzz+Hx3yruAAZGebUVZDdvXv3bXr2V9F5orKOPaZMmXKV8v2meJ8hYp4Tdu7h5TX7pZde2qhnPxa1Fh0sip0CQ7Ku8OGHH9bq0aPHPo8//njdyy+/vHJZPI+1X79+yR999NHun3766f6vvvrqAffdd98B4rWO/rXDTYQdHBwc4gX6fI0v7TS4TBSxwGY01aUBY48ZfLx9nAGFDEwsqBmkftT1h6JXdd1dxNnnX+mZMRrI5uk6XcRe0IB+K/gP69evX12nTp0ZycnJr4qNQ0LclF8gR1GasraX6HnRn5LDSpHEYwSZQzsRuvWfbCFAiKwgew94zxv5K9yoCdP/3n333RcuvvjiVkq+xJOAGTNmGN6V7plKq6Pof0prnGiK6HfdHzl69Og377///hv33HPPo/VK9dCbDg4ODg4OOydatWp1jMbOzzVmrlO4RWTGa80HDPj9wAMPoMAI6PGs1NTUbW3btjUWCvwPeI+aZ3HiqfezlyxZkpmWlvZ7UlLS9Xqvtqgsn9jRaOLEiReI727KwmTmKaIcCgwh+/nnn98gObyned/5egffHsXbQiJZVUhPT6+vSNsr8pFKayHpKAzw5UcTFt02qcIEwh6/atWqHtOmTbvuu+++O6hnz541VAhx/Qq0ePHiyhkZGY3F6ylbt259Urx9Jz6sgxD4gj/Dpwp97bp16xZ26NChiwp9H71eTeT21zg4ODjkRtKQIUMaqf88Rt3nWwpRNtj+VEEIjEH+3xbci7yf3z0/7G+FAQ1mWeLje1F52OOZJ5YuXVqb8UlZelRj0CiFZlyKVED4r5GBJT/8zwD/M/53lA4mmXzZURBYunz58ok33HDDfWJnlxBX0WP+/Pm7itczlMRTCn9RnHZczQHSZU7g/X/+gw8+OFCvPyDay0Tk4ODg4OCwk+Gbb75hDvWphsY1Ghu3hIbI/5QSKCT22GMPLC9QYODbYu7ee++9SOvv5Xpso4gXzLPMGwADu4Ks00477S89/7roMlFZHmvrjx8/nm00zykbEyQDs0YnG5b43aVLl/UVKlTorefPErFOL/oa/eeff26qRB5ThL8q4sUKc01YImELReBZiC9reFtdoTjmiUboursK5frFixcfPGHChFTMkr0k84R4SBbx5QeTZY5g6SX6XfSPaIPi3CYKJ+wHBW0LOwJbhg4dipbntUqVKjXxknJwcHBw8NCyZctK6iuv2bJlSz/1sdN0nUkfr2vTieYF3xjgH5QM+J///8Afnw39R4h99dVXAfXTA1NSUsqdAmPDhg37ZmZm3qVsfKkQaxMypaz9JxMQOU5F/t/C/0x+5RApX0xPvXuku/CPP/4YeeKJJ6LIwBoxKmj8bab33xZhu5qp3wZECuDDkv0tkGjmoEGDvq9ateppioYtLc5Xh4ODg4PDTgWNw0drXESBsVrjYoZvrDRjqdbC2/XYds11sMKYrOsPRZ+OGTPmaz02Se/z8ciAMR1s3bqVSLLef//9pXp2cFJS0lMKjxKVVRgFhrL7LFmGd1FYgQEkn+wXX3xxvZ7tKzpXtK+oSAqMGvfdd9/xiudexfeTQmvimwv+hC30fK57kYh8j2ves2TvMWHzP+f/X2Q6/KZg/ff8sO8APYfQsq6//vpByu+NokNFWGM4ODg4OAh33nlnNXWXT4tWq7/cnJcy2Pa3hJ
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567969}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567969}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567969}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567969}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567971}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567971}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567971}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749276567971}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-07T03:11:27.956203+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'NEE!', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'NEE!'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: NEE!
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using custom logoUrl: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 Button component received mascot URL: data:image/png;base64,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
 Processing image URL or ID: data:image/png;base64,[100800 chars]
 Image is a data URL
 Processed mascot URL: data:image/png;base64,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
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ installHook.js:1
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
 [ReactPolyfill] Stopped monitoring React.createContext
 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ installHook.js:1
(anonymous) @ DashboardNew.jsx:346
setTimeout
(anonymous) @ DashboardNew.jsx:344
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
 [ReactPolyfill] Stopped monitoring React.createContext
 [ReactPolyfill] Stopped monitoring React.createContext
