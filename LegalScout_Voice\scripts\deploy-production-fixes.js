#!/usr/bin/env node

/**
 * Deploy Production Fixes
 * 
 * This script ensures all production fixes are properly deployed and configured.
 * It should be run before deploying to production.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Deploying production fixes...');

// 1. Verify critical files exist
const criticalFiles = [
  'public/production-signin-fix.js',
  'public/consolidated-dashboard-fix.js',
  'src/lib/supabase.js',
  'vercel.json',
  'index.html'
];

console.log('📋 Checking critical files...');
for (const file of criticalFiles) {
  const filePath = path.join(projectRoot, file);
  if (!fs.existsSync(filePath)) {
    console.error(`❌ Critical file missing: ${file}`);
    process.exit(1);
  } else {
    console.log(`✅ ${file}`);
  }
}

// 2. Verify environment variables in vercel.json
console.log('🔧 Checking Vercel configuration...');
const vercelConfigPath = path.join(projectRoot, 'vercel.json');
const vercelConfig = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));

const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_KEY',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_VAPI_PUBLIC_KEY',
  'VITE_VAPI_SECRET_KEY',
  'VAPI_TOKEN'
];

if (!vercelConfig.env) {
  console.error('❌ No environment variables found in vercel.json');
  process.exit(1);
}

for (const envVar of requiredEnvVars) {
  if (!vercelConfig.env[envVar]) {
    console.error(`❌ Missing environment variable in vercel.json: ${envVar}`);
    process.exit(1);
  } else {
    console.log(`✅ ${envVar}`);
  }
}

// 3. Verify production-signin-fix.js is included in index.html
console.log('📄 Checking index.html includes production fixes...');
const indexHtmlPath = path.join(projectRoot, 'index.html');
const indexHtml = fs.readFileSync(indexHtmlPath, 'utf8');

if (!indexHtml.includes('production-signin-fix.js')) {
  console.error('❌ production-signin-fix.js not included in index.html');
  process.exit(1);
} else {
  console.log('✅ production-signin-fix.js included in index.html');
}

// 4. Verify Supabase client has fallback values
console.log('🗄️ Checking Supabase client configuration...');
const supabaseClientPath = path.join(projectRoot, 'src/lib/supabase.js');
const supabaseClient = fs.readFileSync(supabaseClientPath, 'utf8');

if (!supabaseClient.includes('https://utopqxsvudgrtiwenlzl.supabase.co')) {
  console.error('❌ Supabase client missing fallback URL');
  process.exit(1);
} else {
  console.log('✅ Supabase client has fallback URL');
}

if (!supabaseClient.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9')) {
  console.error('❌ Supabase client missing fallback key');
  process.exit(1);
} else {
  console.log('✅ Supabase client has fallback key');
}

// 5. Check CSP configuration
console.log('🔒 Checking Content Security Policy...');
if (!indexHtml.includes('connect-src')) {
  console.error('❌ CSP missing connect-src directive');
  process.exit(1);
} else {
  console.log('✅ CSP configured');
}

if (!indexHtml.includes('mcp.vapi.ai')) {
  console.error('❌ CSP missing Vapi MCP domain');
  process.exit(1);
} else {
  console.log('✅ CSP includes Vapi MCP domain');
}

if (!indexHtml.includes('utopqxsvudgrtiwenlzl.supabase.co')) {
  console.error('❌ CSP missing Supabase domain');
  process.exit(1);
} else {
  console.log('✅ CSP includes Supabase domain');
}

// 6. Verify CORS headers in vercel.json
console.log('🌐 Checking CORS configuration...');
if (!vercelConfig.headers) {
  console.error('❌ No headers configuration found in vercel.json');
  process.exit(1);
}

const corsHeaders = vercelConfig.headers.find(h => h.source === '/(.*)')?.headers;
if (!corsHeaders) {
  console.error('❌ No CORS headers found for all routes');
  process.exit(1);
}

const hasAccessControlAllowOrigin = corsHeaders.some(h => h.key === 'Access-Control-Allow-Origin');
if (!hasAccessControlAllowOrigin) {
  console.error('❌ Missing Access-Control-Allow-Origin header');
  process.exit(1);
} else {
  console.log('✅ CORS headers configured');
}

// 7. Create deployment summary
console.log('\n📊 Deployment Summary:');
console.log('='.repeat(50));
console.log('✅ All critical files present');
console.log('✅ Environment variables configured');
console.log('✅ Production fixes included in HTML');
console.log('✅ Supabase client has fallbacks');
console.log('✅ CSP properly configured');
console.log('✅ CORS headers configured');
console.log('='.repeat(50));

console.log('\n🎉 Production fixes deployment check completed successfully!');
console.log('\n📝 Next steps:');
console.log('1. Commit all changes to git');
console.log('2. Push to main branch');
console.log('3. Vercel will automatically deploy');
console.log('4. Test sign-in functionality in production');

console.log('\n🔍 Key fixes applied:');
console.log('• Fixed Supabase API key missing in requests');
console.log('• Fixed CORS issues with Vapi MCP server');
console.log('• Fixed MutationObserver errors');
console.log('• Fixed environment variable access');
console.log('• Added fallback values for all critical services');

process.exit(0);
