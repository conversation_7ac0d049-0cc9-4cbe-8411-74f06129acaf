/**
 * Comprehensive App Diagnostics
 * 
 * This component runs extensive diagnostics to identify the root cause
 * of the black screen issue in both local and production environments.
 */

import React, { useState, useEffect } from 'react';

const ComprehensiveAppDiagnostics = () => {
  const [diagnostics, setDiagnostics] = useState({
    environment: {},
    modules: {},
    auth: {},
    routing: {},
    errors: [],
    timeline: []
  });

  const [isRunning, setIsRunning] = useState(false);

  const addToTimeline = (message, type = 'info') => {
    const timestamp = new Date().toISOString();
    setDiagnostics(prev => ({
      ...prev,
      timeline: [...prev.timeline, { timestamp, message, type }]
    }));
    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
  };

  const runComprehensiveDiagnostics = async () => {
    setIsRunning(true);
    addToTimeline('🚀 Starting comprehensive diagnostics...', 'info');

    try {
      // 1. Environment Diagnostics
      await runEnvironmentDiagnostics();
      
      // 2. Module Loading Diagnostics
      await runModuleDiagnostics();
      
      // 3. Authentication Diagnostics
      await runAuthDiagnostics();
      
      // 4. Routing Diagnostics
      await runRoutingDiagnostics();
      
      // 5. Error Capture
      await captureErrors();
      
      addToTimeline('✅ Comprehensive diagnostics completed', 'success');
    } catch (error) {
      addToTimeline(`❌ Diagnostics failed: ${error.message}`, 'error');
    } finally {
      setIsRunning(false);
    }
  };

  const runEnvironmentDiagnostics = async () => {
    addToTimeline('🔍 Running environment diagnostics...', 'info');
    
    const envData = {
      // Basic environment
      nodeEnv: process.env.NODE_ENV,
      isDev: import.meta.env.DEV,
      isProd: import.meta.env.PROD,
      mode: import.meta.env.MODE,
      
      // Vite environment variables
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
      supabaseKeyExists: !!import.meta.env.VITE_SUPABASE_KEY,
      vapiPublicKeyExists: !!import.meta.env.VITE_VAPI_PUBLIC_KEY,
      vapiSecretKeyExists: !!import.meta.env.VITE_VAPI_SECRET_KEY,
      
      // Window globals
      windowGlobals: {
        React: !!window.React,
        ReactDOM: !!window.ReactDOM,
        supabase: !!window.supabase,
        Vapi: !!window.Vapi
      },
      
      // Browser info
      userAgent: navigator.userAgent,
      url: window.location.href,
      origin: window.location.origin
    };

    setDiagnostics(prev => ({ ...prev, environment: envData }));
    addToTimeline(`Environment check: ${envData.mode} mode, Supabase: ${envData.supabaseKeyExists ? '✅' : '❌'}`, 'info');
  };

  const runModuleDiagnostics = async () => {
    addToTimeline('🔍 Running module diagnostics...', 'info');
    
    const moduleTests = {
      react: false,
      supabase: false,
      vapiConfig: false,
      authContext: false,
      moduleResolver: false
    };

    // Test React
    try {
      if (React && React.version) {
        moduleTests.react = true;
        addToTimeline(`React loaded: v${React.version}`, 'success');
      }
    } catch (error) {
      addToTimeline(`React test failed: ${error.message}`, 'error');
    }

    // Test Supabase
    try {
      const { supabase } = await import('../lib/supabase');
      if (supabase) {
        moduleTests.supabase = true;
        addToTimeline('Supabase module loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`Supabase test failed: ${error.message}`, 'error');
    }

    // Test Vapi Config
    try {
      const vapiConfig = await import('../config/vapiConfig');
      if (vapiConfig) {
        moduleTests.vapiConfig = true;
        addToTimeline('Vapi config loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`Vapi config test failed: ${error.message}`, 'error');
    }

    // Test Auth Context
    try {
      const { useAuth } = await import('../contexts/AuthContext');
      if (useAuth) {
        moduleTests.authContext = true;
        addToTimeline('Auth context loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`Auth context test failed: ${error.message}`, 'error');
    }

    // Test Module Resolver
    try {
      const { serviceResolvers } = await import('../utils/moduleResolver');
      if (serviceResolvers) {
        moduleTests.moduleResolver = true;
        addToTimeline('Module resolver loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`Module resolver test failed: ${error.message}`, 'error');
    }

    setDiagnostics(prev => ({ ...prev, modules: moduleTests }));
  };

  const runAuthDiagnostics = async () => {
    addToTimeline('🔍 Running authentication diagnostics...', 'info');
    
    const authData = {
      supabaseClient: false,
      session: null,
      user: null,
      error: null
    };

    try {
      const { supabase } = await import('../lib/supabase');
      authData.supabaseClient = true;
      
      const { data: { session }, error } = await supabase.auth.getSession();
      authData.session = !!session;
      authData.user = session?.user?.email || null;
      authData.error = error?.message || null;
      
      if (session) {
        addToTimeline(`User authenticated: ${session.user.email}`, 'success');
      } else {
        addToTimeline('No active session found', 'warning');
      }
    } catch (error) {
      authData.error = error.message;
      addToTimeline(`Auth diagnostics failed: ${error.message}`, 'error');
    }

    setDiagnostics(prev => ({ ...prev, auth: authData }));
  };

  const runRoutingDiagnostics = async () => {
    addToTimeline('🔍 Running routing diagnostics...', 'info');
    
    const routingData = {
      currentPath: window.location.pathname,
      hash: window.location.hash,
      search: window.location.search,
      reactRouter: false,
      appComponent: false
    };

    try {
      // Check if React Router is available
      const ReactRouter = await import('react-router-dom');
      if (ReactRouter) {
        routingData.reactRouter = true;
        addToTimeline('React Router loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`React Router test failed: ${error.message}`, 'error');
    }

    // Check if main App component exists
    try {
      const App = await import('../App');
      if (App) {
        routingData.appComponent = true;
        addToTimeline('Main App component loaded successfully', 'success');
      }
    } catch (error) {
      addToTimeline(`App component test failed: ${error.message}`, 'error');
    }

    setDiagnostics(prev => ({ ...prev, routing: routingData }));
  };

  const captureErrors = async () => {
    addToTimeline('🔍 Capturing console errors...', 'info');
    
    // Capture console errors
    const errors = [];
    
    // Override console.error temporarily to capture errors
    const originalError = console.error;
    console.error = (...args) => {
      errors.push({
        type: 'console.error',
        message: args.join(' '),
        timestamp: new Date().toISOString()
      });
      originalError.apply(console, args);
    };

    // Check for React error boundaries
    if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      const internals = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
      if (internals.ReactCurrentDispatcher && internals.ReactCurrentDispatcher.current) {
        addToTimeline('React internals accessible', 'success');
      }
    }

    setDiagnostics(prev => ({ ...prev, errors }));
    
    // Restore original console.error after a delay
    setTimeout(() => {
      console.error = originalError;
    }, 5000);
  };

  useEffect(() => {
    // Auto-run diagnostics on mount
    runComprehensiveDiagnostics();
  }, []);

  const getStatusColor = (status) => {
    if (status === true) return '#4CAF50';
    if (status === false) return '#F44336';
    return '#FF9800';
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#1a1a1a', 
      color: '#ffffff',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#4CAF50' }}>🔬 Comprehensive App Diagnostics</h1>
      
      <button 
        onClick={runComprehensiveDiagnostics}
        disabled={isRunning}
        style={{
          padding: '10px 20px',
          backgroundColor: isRunning ? '#666' : '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isRunning ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {isRunning ? '🔄 Running Diagnostics...' : '🚀 Run Diagnostics'}
      </button>

      {/* Environment Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#2196F3' }}>🌍 Environment</h2>
        <div style={{ backgroundColor: '#2a2a2a', padding: '10px', borderRadius: '4px' }}>
          <div>Mode: <span style={{ color: getStatusColor(true) }}>{diagnostics.environment.mode}</span></div>
          <div>Supabase URL: <span style={{ color: getStatusColor(!!diagnostics.environment.supabaseUrl) }}>
            {diagnostics.environment.supabaseUrl ? '✅ Set' : '❌ Missing'}
          </span></div>
          <div>Supabase Key: <span style={{ color: getStatusColor(diagnostics.environment.supabaseKeyExists) }}>
            {diagnostics.environment.supabaseKeyExists ? '✅ Set' : '❌ Missing'}
          </span></div>
        </div>
      </div>

      {/* Modules Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#2196F3' }}>📦 Modules</h2>
        <div style={{ backgroundColor: '#2a2a2a', padding: '10px', borderRadius: '4px' }}>
          {Object.entries(diagnostics.modules).map(([module, status]) => (
            <div key={module}>
              {module}: <span style={{ color: getStatusColor(status) }}>
                {status ? '✅ Loaded' : '❌ Failed'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Timeline Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ color: '#2196F3' }}>📋 Timeline</h2>
        <div style={{ 
          backgroundColor: '#2a2a2a', 
          padding: '10px', 
          borderRadius: '4px',
          maxHeight: '300px',
          overflowY: 'auto'
        }}>
          {diagnostics.timeline.map((entry, index) => (
            <div key={index} style={{ 
              marginBottom: '5px',
              color: entry.type === 'error' ? '#F44336' : 
                     entry.type === 'success' ? '#4CAF50' : 
                     entry.type === 'warning' ? '#FF9800' : '#ffffff'
            }}>
              <small>{entry.timestamp.split('T')[1].split('.')[0]}</small> {entry.message}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveAppDiagnostics;
