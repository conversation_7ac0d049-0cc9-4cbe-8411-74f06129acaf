/**
 * Synchronization Helper Functions
 *
 * This file contains helper functions for synchronizing data between Supabase and Vapi.
 */

import { supabase } from '../lib/supabase-fixed.js';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';



// Create a mock Vapi client for testing
const createMockVapiClient = () => {
  return {
    callTool: async (params) => {
      if (params.name === 'get_assistant') {
        return {
          content: [
            {
              text: JSON.stringify({
                id: 'test-assistant-id',
                name: 'Test Law Firm',
                firstMessage: 'Welcome to Test Law Firm',
                instructions: 'You are a legal assistant for Test Law Firm',
                voice: {
                  provider: 'playht',
                  voiceId: 'ranger'
                }
              })
            }
          ]
        };
      } else if (params.name === 'create_assistant') {
        return {
          content: [
            {
              text: JSON.stringify({
                id: 'new-assistant-id',
                ...params.arguments
              })
            }
          ]
        };
      }
      return { content: [{ text: '{}' }] };
    },
    disconnect: async () => {}
  };
};

// Note: Using shared Supabase client from lib/supabase.js to avoid multiple instances

// Helper functions for Supabase operations
export const fetchFromSupabase = async (attorneyId) => {
  const { data, error } = await supabase
    .from('attorneys')
    .select('*')
    .eq('id', attorneyId)
    .single();

  if (error) throw error;
  return data;
};

export const updateSupabaseAttorney = async (attorneyId, data) => {
  const { data: updatedData, error } = await supabase
    .from('attorneys')
    .update(data)
    .eq('id', attorneyId)
    .select()
    .single();

  if (error) throw error;
  return updatedData;
};

export const getAttorneyByEmail = async (email) => {
  // Handle duplicates by getting the most recent attorney
  const { data: attorneys, error } = await supabase
    .from('attorneys')
    .select('*')
    .eq('email', email)
    .order('updated_at', { ascending: false });

  if (error) throw error;

  if (attorneys && attorneys.length > 0) {
    if (attorneys.length > 1) {
      console.warn(`Found ${attorneys.length} attorneys for email ${email}, using the most recent one`);
    }
    return attorneys[0];
  }

  return null;
};

export const getAttorneyByAuthId = async (authId) => {
  // Always use real authentication - no more mock users
  if (false && authId === 'dev-user-id') { // Disabled mock authentication
    console.log('Development mode: Using mock attorney for dev-user-id');
    return {
      id: 'dev-attorney-id',
      name: 'Development Attorney',
      email: '<EMAIL>',
      firm_name: 'Dev Law Firm',
      welcome_message: 'Welcome to Dev Law Firm',
      vapi_instructions: 'You are a legal assistant for Dev Law Firm',
      voice_provider: 'playht',
      voice_id: 'ranger',
      vapi_assistant_id: 'dev-assistant-id',
      user_id: 'dev-user-id'
    };
  }

  // For real users, query Supabase
  try {
    console.log(`Fetching attorney by user_id: ${authId}`);

    // Use user_id column instead of auth_id (which doesn't exist)
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('user_id', authId)
      .single();

    if (error) {
      console.log(`Error response from Supabase:`, error);

      // Only throw if it's not a "no rows returned" error
      if (error.code !== 'PGRST116') throw error;
    }

    console.log(`Attorney data from Supabase:`, data);
    return data || null;
  } catch (error) {
    console.error('Error fetching attorney by auth ID:', error);

    // Always use real authentication - no more mock fallbacks
    if (false && (import.meta.env.DEV || import.meta.env.MODE === 'development')) { // Disabled mock fallback
      console.log('Development mode: Using mock attorney due to error');
      return {
        id: 'dev-attorney-id',
        name: 'Development Attorney',
        email: '<EMAIL>',
        firm_name: 'Dev Law Firm',
        welcome_message: 'Welcome to Dev Law Firm',
        vapi_instructions: 'You are a legal assistant for Dev Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'dev-assistant-id',
        user_id: authId
      };
    }

    throw error;
  }
};

export const getAttorneyById = async (id) => {
  // Always use real authentication - no more mock attorneys
  if (false && id === 'dev-attorney-id') { // Disabled mock authentication
    console.log('Development mode: Using mock attorney for dev-attorney-id');
    return {
      id: 'dev-attorney-id',
      name: 'Development Attorney',
      email: '<EMAIL>',
      firm_name: 'Dev Law Firm',
      welcome_message: 'Welcome to Dev Law Firm',
      vapi_instructions: 'You are a legal assistant for Dev Law Firm',
      voice_provider: 'playht',
      voice_id: 'ranger',
      vapi_assistant_id: 'dev-assistant-id',
      auth_id: 'dev-user-id'
    };
  }

  // For real attorneys, query Supabase
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  } catch (error) {
    console.error('Error fetching attorney by ID:', error);

    // In development mode, return a mock attorney if there's an error
    let isDevelopment = false;

    // Check Node.js environment
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
      isDevelopment = true;
    }

    // Skip Vite environment check during build to avoid import.meta issues
    // Development detection via NODE_ENV is sufficient for most cases

    if (isDevelopment) {
      console.log('Development mode: Using mock attorney due to error');
      return {
        id: id,
        name: 'Development Attorney',
        email: '<EMAIL>',
        firm_name: 'Dev Law Firm',
        welcome_message: 'Welcome to Dev Law Firm',
        vapi_instructions: 'You are a legal assistant for Dev Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'dev-assistant-id',
        auth_id: 'dev-user-id'
      };
    }

    throw error;
  }
};

export const createAttorney = async (data) => {
  const { data: newAttorney, error } = await supabase
    .from('attorneys')
    .insert([data])
    .select()
    .single();

  if (error) throw error;
  return newAttorney;
};

export const updateAttorney = async (id, data) => {
  const { data: updatedAttorney, error } = await supabase
    .from('attorneys')
    .update(data)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return updatedAttorney;
};

// Helper functions for Vapi operations
export const getVapiMcpClient = async () => {
  // Check for API key in different environments
  let apiKey = null;

  // Server-side (Node.js environment)
  if (typeof process !== 'undefined' && process.env) {
    apiKey = process.env.VITE_VAPI_PUBLIC_KEY || process.env.VAPI_PUBLIC_KEY;
  }

  // Client-side (browser environment)
  if (!apiKey && typeof window !== 'undefined') {
    apiKey = window.VITE_VAPI_PUBLIC_KEY;
  }

  // Skip Vite environment check during build to avoid import.meta parsing issues
  // API key should be available via process.env or window in runtime environments

  // Skip MCP connection to avoid CORS issues - use mock client
  console.log('[syncHelpers] Skipping MCP connection - using mock client to avoid CORS');
  return createMockVapiClient();

  // Otherwise, use the mock client
  console.warn('No Vapi API key found, using mock client');
  return createMockVapiClient();
};

export const fetchFromVapi = async (assistantId) => {
  const mcpClient = await getVapiMcpClient();

  try {
    const result = await mcpClient.callTool({
      name: 'get_assistant',
      arguments: {
        assistantId
      },
    });

    await mcpClient.disconnect();

    if (result.content && result.content[0] && result.content[0].text) {
      return JSON.parse(result.content[0].text);
    }

    throw new Error('Failed to fetch assistant from Vapi');
  } catch (error) {
    await mcpClient.disconnect();
    throw error;
  }
};

export const createVapiAssistant = async (data) => {
  const mcpClient = await getVapiMcpClient();

  try {
    const result = await mcpClient.callTool({
      name: 'create_assistant',
      arguments: {
        name: data.name,
        firstMessage: data.firstMessage,
        instructions: data.instructions,
        voice: data.voice,
        llm: {
          provider: "openai",
          model: "gpt-4o"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        }
      },
    });

    await mcpClient.disconnect();

    if (result.content && result.content[0] && result.content[0].text) {
      return JSON.parse(result.content[0].text);
    }

    throw new Error('Failed to create assistant in Vapi');
  } catch (error) {
    await mcpClient.disconnect();
    throw error;
  }
};

export const updateVapiAssistant = async (assistantId, data) => {
  const mcpClient = await getVapiMcpClient();

  try {
    // Vapi doesn't have a direct update assistant endpoint
    // We need to get the current assistant and then create a new one with the updated data
    const currentAssistant = await fetchFromVapi(assistantId);

    const updateResult = await mcpClient.callTool({
      name: 'create_assistant',
      arguments: {
        name: data.name || currentAssistant.name,
        firstMessage: data.firstMessage || currentAssistant.firstMessage,
        instructions: data.instructions || currentAssistant.instructions,
        voice: data.voice || currentAssistant.voice,
        llm: currentAssistant.llm,
        transcriber: currentAssistant.transcriber
      },
    });

    await mcpClient.disconnect();

    if (updateResult.content && updateResult.content[0] && updateResult.content[0].text) {
      return JSON.parse(updateResult.content[0].text);
    }

    throw new Error('Failed to update assistant in Vapi');
  } catch (error) {
    await mcpClient.disconnect();
    throw error;
  }
};

export const getVapiAssistant = async (assistantId) => {
  return await fetchFromVapi(assistantId);
};

// Validation helpers
export const findProfileDiscrepancies = (supabaseData, vapiData) => {
  const discrepancies = {};

  // Map of attorney fields to Vapi assistant fields
  const fieldMappings = {
    firm_name: "name",
    welcome_message: "firstMessage",
    vapi_instructions: "instructions"
  };

  // Check for discrepancies in mapped fields
  Object.entries(fieldMappings).forEach(([attorneyField, vapiField]) => {
    if (supabaseData[attorneyField] !== vapiData[vapiField]) {
      discrepancies[attorneyField] = {
        supabase: supabaseData[attorneyField],
        vapi: vapiData[vapiField]
      };
    }
  });

  // Check voice configuration
  if (supabaseData.voice_provider !== vapiData.voice?.provider ||
      supabaseData.voice_id !== vapiData.voice?.voiceId) {
    discrepancies.voice = {
      supabase: {
        provider: supabaseData.voice_provider,
        voiceId: supabaseData.voice_id
      },
      vapi: vapiData.voice
    };
  }

  return discrepancies;
};

// Check if a Vapi assistant ID is being used by multiple attorneys
export const checkAssistantIdConflicts = async (assistantId, currentAttorneyId) => {
  try {
    const { data: conflictingAttorneys, error } = await supabase
      .from('attorneys')
      .select('id, email, firm_name, subdomain')
      .eq('vapi_assistant_id', assistantId)
      .neq('id', currentAttorneyId);

    if (error) {
      console.error('Error checking assistant ID conflicts:', error);
      return { hasConflicts: false, conflicts: [] };
    }

    return {
      hasConflicts: conflictingAttorneys && conflictingAttorneys.length > 0,
      conflicts: conflictingAttorneys || []
    };
  } catch (error) {
    console.error('Error checking assistant ID conflicts:', error);
    return { hasConflicts: false, conflicts: [] };
  }
};

export const getValidVoicesForProvider = async (provider) => {
  // This would ideally come from an API or configuration
  // For now, we'll use a static mapping
  const voicesByProvider = {
    "11labs": ["sarah", "adam", "daniel", "josh", "rachel", "domi", "freya", "antoni", "thomas", "charlie", "emily", "elli", "callum", "patrick", "harry", "liam", "dorothy", "josh", "arnold", "charlotte", "matilda", "matthew", "james", "joseph", "jeremy", "michael", "ethan"],
    "playht": ["ranger", "waylon", "leyro", "nova", "stella", "cody", "maya", "ryan", "tyler", "luke", "jackson", "hudson", "brett", "theo", "ruby", "daisy", "olivia", "lily", "emma", "sophia", "ava", "mia", "isabella", "charlotte", "amelia", "harper", "evelyn"],
    "deepgram": ["nova-1", "nova-2", "nova-3", "aura-1", "aura-2", "stella-1", "stella-2", "luna-1", "luna-2"],
    "openai": ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
  };

  return voicesByProvider[provider] || [];
};

/**
 * Ensure profile persistence by synchronizing data between local storage, Supabase, and Vapi
 *
 * This function ensures that profile data is consistent across all storage mechanisms:
 * 1. Local storage (for immediate access)
 * 2. Supabase (source of truth for attorney data)
 * 3. Vapi (for voice assistant configuration)
 *
 * @param {Object} params - The parameters for the function
 * @param {string} params.attorneyId - The ID of the attorney
 * @param {Object} [params.localData] - Optional local data to use as a source
 * @param {boolean} [params.forceUpdate=false] - Whether to force an update even if no discrepancies are found
 * @returns {Object} The result of the synchronization
 */
export const ensureProfilePersistence = async (params) => {
  const { attorneyId, localData, forceUpdate = false } = params;

  console.log(`Ensuring profile persistence for attorney ${attorneyId}`);

  try {
    // Step 1: Get data from all sources
    let supabaseData = null;
    let vapiData = null;
    let localStorageData = null;

    // Get data from Supabase (source of truth)
    try {
      supabaseData = await fetchFromSupabase(attorneyId);
      console.log('Retrieved data from Supabase:', supabaseData ? 'success' : 'not found');
    } catch (supabaseError) {
      console.error('Error fetching from Supabase:', supabaseError);
    }

    // Get data from local storage (only in browser environment)
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          localStorageData = JSON.parse(storedAttorney);
          console.log('Retrieved data from localStorage:', localStorageData ? 'success' : 'not found');
        }
      } catch (localStorageError) {
        console.error('Error fetching from localStorage:', localStorageError);
      }
    } else {
      console.log('localStorage not available (server-side environment)');
    }

    // Get data from provided local data (if available)
    if (localData) {
      console.log('Using provided local data');
      if (!localStorageData) {
        localStorageData = localData;
      }
    }

    // If we have Supabase data and it has a Vapi assistant ID, get the Vapi data
    if (supabaseData && supabaseData.vapi_assistant_id) {
      try {
        vapiData = await fetchFromVapi(supabaseData.vapi_assistant_id);
        console.log('Retrieved data from Vapi:', vapiData ? 'success' : 'not found');
      } catch (vapiError) {
        console.error('Error fetching from Vapi:', vapiError);
      }
    }

    // Step 2: Determine the source of truth
    // Priority: Supabase > Local Storage > Provided Local Data
    const sourceOfTruth = supabaseData || localStorageData || localData;

    if (!sourceOfTruth) {
      return {
        action: "error",
        message: `Could not determine source of truth for attorney ${attorneyId}`,
        success: false
      };
    }

    // Step 3: Ensure consistency across all storage mechanisms

    // Update local storage if needed (only in browser environment)
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      if (supabaseData && (!localStorageData || forceUpdate)) {
        try {
          localStorage.setItem('attorney', JSON.stringify(supabaseData));
          console.log('Updated localStorage with Supabase data');
        } catch (localStorageError) {
          console.error('Error updating localStorage:', localStorageError);
        }
      }
    } else {
      console.log('localStorage update skipped (server-side environment)');
    }

    // Update Vapi if needed
    let vapiUpdateResult = null;
    if (supabaseData) {
      // Check if we need to create or update a Vapi assistant
      if (!supabaseData.vapi_assistant_id || !vapiData) {
        // Create a new assistant
        try {
          let newAssistant = await createVapiAssistant({
            name: supabaseData.firm_name || 'LegalScout Assistant',
            firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
            instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
            voice: {
              provider: supabaseData.voice_provider || '11labs',
              voiceId: supabaseData.voice_id || 'sarah'
            }
          });

          // Update Supabase with the new assistant ID (with conflict handling)
          try {
            await updateSupabaseAttorney(attorneyId, {
              vapi_assistant_id: newAssistant.id
            });
          } catch (updateError) {
            // If unique constraint violation, the assistant ID is already in use
            if (updateError.code === '23505' || updateError.message?.includes('unique_vapi_assistant_id')) {
              console.warn('Assistant ID already in use, creating another assistant');
              // Create another assistant and try again
              const anotherAssistant = await createVapiAssistant({
                name: supabaseData.firm_name || 'LegalScout Assistant',
                firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
                instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
                voice: {
                  provider: supabaseData.voice_provider || '11labs',
                  voiceId: supabaseData.voice_id || 'sarah'
                }
              });

              await updateSupabaseAttorney(attorneyId, {
                vapi_assistant_id: anotherAssistant.id
              });

              newAssistant = anotherAssistant;
            } else {
              throw updateError;
            }
          }

          console.log('Created new Vapi assistant and updated Supabase:', newAssistant.id);

          vapiUpdateResult = {
            action: "created",
            assistantId: newAssistant.id
          };
        } catch (createError) {
          console.error('Error creating Vapi assistant:', createError);
        }
      } else if (vapiData) {
        // Check for assistant ID conflicts before updating
        const conflictCheck = await checkAssistantIdConflicts(supabaseData.vapi_assistant_id, attorneyId);

        if (conflictCheck.hasConflicts) {
          console.warn(`Assistant ID ${supabaseData.vapi_assistant_id} is being used by multiple attorneys:`, conflictCheck.conflicts);
          console.warn(`Creating new assistant for attorney ${attorneyId} to resolve conflict`);

          // Create a new assistant to resolve the conflict
          try {
            let newAssistant = await createVapiAssistant({
              name: supabaseData.firm_name || 'LegalScout Assistant',
              firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
              instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
              voice: {
                provider: supabaseData.voice_provider || '11labs',
                voiceId: supabaseData.voice_id || 'sarah'
              }
            });

            // Update Supabase with the new assistant ID (with conflict handling)
            try {
              await updateSupabaseAttorney(attorneyId, {
                vapi_assistant_id: newAssistant.id
              });
            } catch (updateError) {
              // If unique constraint violation, the assistant ID is already in use
              if (updateError.code === '23505' || updateError.message?.includes('unique_vapi_assistant_id')) {
                console.warn('Assistant ID already in use during conflict resolution, creating another assistant');
                // Create another assistant and try again
                const anotherAssistant = await createVapiAssistant({
                  name: supabaseData.firm_name || 'LegalScout Assistant',
                  firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
                  instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
                  voice: {
                    provider: supabaseData.voice_provider || '11labs',
                    voiceId: supabaseData.voice_id || 'sarah'
                  }
                });

                await updateSupabaseAttorney(attorneyId, {
                  vapi_assistant_id: anotherAssistant.id
                });

                newAssistant = anotherAssistant;
              } else {
                throw updateError;
              }
            }

            console.log('Created new Vapi assistant to resolve conflict:', newAssistant.id);

            vapiUpdateResult = {
              action: "created_new_to_resolve_conflict",
              assistantId: newAssistant.id,
              conflictResolved: true,
              previousConflicts: conflictCheck.conflicts
            };
          } catch (createError) {
            console.error('Error creating new Vapi assistant to resolve conflict:', createError);
          }
        } else {
          // No conflicts, proceed with normal discrepancy check and update
          const discrepancies = findProfileDiscrepancies(supabaseData, vapiData);

          if (Object.keys(discrepancies).length > 0 || forceUpdate) {
            try {
              const updatedAssistant = await updateVapiAssistant(supabaseData.vapi_assistant_id, {
                name: supabaseData.firm_name || 'LegalScout Assistant',
                firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
                instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
                voice: {
                  provider: supabaseData.voice_provider || '11labs',
                  voiceId: supabaseData.voice_id || 'sarah'
                }
              });

              console.log('Updated Vapi assistant:', updatedAssistant.id);

              vapiUpdateResult = {
                action: "updated",
                assistantId: updatedAssistant.id,
                discrepancies: Object.keys(discrepancies).length > 0 ? discrepancies : null
              };
            } catch (updateError) {
              console.error('Error updating Vapi assistant:', updateError);
            }
          } else {
            console.log('No discrepancies found, Vapi assistant is up to date');

            vapiUpdateResult = {
              action: "none",
              assistantId: supabaseData.vapi_assistant_id
            };
          }
        }
      }
    }

    // Return the result
    return {
      action: vapiUpdateResult ? vapiUpdateResult.action : "checked",
      success: true,
      message: "Profile persistence ensured",
      sources: {
        supabase: !!supabaseData,
        localStorage: !!localStorageData,
        vapi: !!vapiData
      },
      vapiResult: vapiUpdateResult
    };
  } catch (error) {
    console.error('Error ensuring profile persistence:', error);

    return {
      action: "error",
      success: false,
      message: `Error ensuring profile persistence: ${error.message}`,
      error: error.message
    };
  }
};
