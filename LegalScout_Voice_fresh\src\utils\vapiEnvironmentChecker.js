/**
 * Utility to check and manage Vapi environment variables
 */

// Known API keys for fallback
const KNOWN_KEYS = {
  PUBLIC: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  PRIVATE: '6734febc-fc65-4669-93b0-929b31ff6564'
};

/**
 * Get the appropriate Vapi API key for the given operation type
 * @param {string} operationType - 'client' for client-side operations, 'server' for server-side operations
 * @returns {string} The appropriate API key
 */
export const getVapiApiKey = (operationType = 'client') => {
  console.log(`[VapiEnvironmentChecker] Getting API key for operation type: ${operationType}`);

  // Check environment variables with more comprehensive checking
  const envSecretKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                      (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY) ||
                      (typeof process !== 'undefined' && process.env?.VITE_VAPI_SECRET_KEY);

  const envPublicKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                      (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||
                      (typeof process !== 'undefined' && process.env?.VITE_VAPI_PUBLIC_KEY);

  const localStorageKey = (typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key'));

  // CRITICAL FIX: In production, always use the known keys as fallback
  // The environment variables might not be properly set in Vercel
  const isProduction = typeof window !== 'undefined' &&
    (window.location.hostname === 'dashboard.legalscout.net' ||
     window.location.hostname.endsWith('.legalscout.net') ||
     window.location.hostname === 'legalscout.net');

  console.log(`[VapiEnvironmentChecker] Production check:`, {
    isProduction,
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
    hasEnvSecretKey: !!envSecretKey,
    hasEnvPublicKey: !!envPublicKey
  });

  console.log(`[VapiEnvironmentChecker] Environment check:`, {
    hasEnvSecretKey: !!envSecretKey,
    hasEnvPublicKey: !!envPublicKey,
    hasLocalStorageKey: !!localStorageKey,
    envSecretKeyPreview: envSecretKey ? envSecretKey.substring(0, 8) + '...' : 'none',
    envPublicKeyPreview: envPublicKey ? envPublicKey.substring(0, 8) + '...' : 'none'
  });

  // For server-side operations (assistant management, etc.), prefer private key
  if (operationType === 'server') {
    const key = envSecretKey || KNOWN_KEYS.PRIVATE;
    console.log(`[VapiEnvironmentChecker] Using ${envSecretKey ? 'environment' : 'fallback'} PRIVATE key for server operations`);
    return key;
  }

  // For client-side operations (voice calls), use public key
  if (operationType === 'client') {
    const key = envPublicKey || KNOWN_KEYS.PUBLIC;
    console.log(`[VapiEnvironmentChecker] Using ${envPublicKey ? 'environment' : 'fallback'} PUBLIC key for client operations`);
    return key;
  }

  // Default fallback
  const key = envPublicKey || envSecretKey || localStorageKey || KNOWN_KEYS.PUBLIC;
  console.log(`[VapiEnvironmentChecker] Using fallback key for default operations`);
  return key;
};

/**
 * Check if we're in a production environment
 * @returns {boolean} True if in production
 */
export const isProductionEnvironment = () => {
  return typeof window !== 'undefined' &&
    (window.location.hostname === 'dashboard.legalscout.net' ||
     window.location.hostname.endsWith('.legalscout.net'));
};

/**
 * Check if we're in a development environment
 * @returns {boolean} True if in development
 */
export const isDevelopmentEnvironment = () => {
  return typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development';
};

/**
 * Log environment information for debugging
 */
export const logEnvironmentInfo = () => {
  console.log('[VapiEnvironmentChecker] Environment Information:', {
    isProduction: isProductionEnvironment(),
    isDevelopment: isDevelopmentEnvironment(),
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
    hasViteEnv: typeof import.meta !== 'undefined' && !!import.meta.env,
    hasWindowEnv: typeof window !== 'undefined',
    hasProcessEnv: typeof process !== 'undefined' && !!process.env,
    mode: typeof import.meta !== 'undefined' ? import.meta.env?.MODE : 'unknown'
  });
};

export default {
  getVapiApiKey,
  isProductionEnvironment,
  isDevelopmentEnvironment,
  logEnvironmentInfo,
  KNOWN_KEYS
};
