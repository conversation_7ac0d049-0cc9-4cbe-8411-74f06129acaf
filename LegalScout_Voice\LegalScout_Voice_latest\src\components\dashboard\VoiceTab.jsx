import React, { useState, useRef } from 'react';
import { supabase } from '../../lib/supabase';
import { FaMicrophone, FaUpload, FaVolumeUp, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { enhancedVapiAssistantManager } from '../../services/EnhancedVapiAssistantManager';

/**
 * VoiceTab component for the attorney dashboard
 * Allows attorneys to customize their AI assistant's voice
 */
const VoiceTab = ({ attorney, onUpdate, previewConfig, vapiAssistantData }) => {
  // Initialize voice from Vapi assistant data (source of truth), then fallback to other sources
  const getInitialVoice = () => {
    // Priority 1: Vapi assistant data (source of truth)
    if (vapiAssistantData?.voice?.voiceId) {
      console.log('🎵 [VoiceTab] Loading voice from Vapi assistant:', vapiAssistantData.voice);
      return {
        voiceId: vapiAssistantData.voice.voiceId,
        voiceProvider: vapiAssistantData.voice.provider || '11labs'
      };
    }

    // Priority 2: Preview config
    if (previewConfig?.voiceId) {
      return {
        voiceId: previewConfig.voiceId,
        voiceProvider: previewConfig.voiceProvider || '11labs'
      };
    }

    // Priority 3: Attorney data
    if (attorney?.voice_id) {
      return {
        voiceId: attorney.voice_id,
        voiceProvider: attorney.voice_provider || '11labs'
      };
    }

    // Default fallback
    return {
      voiceId: 'sarah',
      voiceProvider: '11labs'
    };
  };

  // State for form data
  const [formData, setFormData] = useState(getInitialVoice());

  // Update form data when Vapi assistant data changes
  React.useEffect(() => {
    const newVoiceData = getInitialVoice();
    console.log('🔄 [VoiceTab] Updating voice data:', newVoiceData);
    setFormData(newVoiceData);
  }, [vapiAssistantData, previewConfig, attorney]);

  // State for voice cloning
  const [voiceFile, setVoiceFile] = useState(null);
  const [voiceCloning, setVoiceCloning] = useState(false);
  const [voicePresets, setVoicePresets] = useState([
    // 11labs voices (only ones that work with your account)
    { id: 'sarah', name: 'Sarah (Female)', gender: 'female', provider: '11labs' },

    // OpenAI voices (these always work)
    { id: 'alloy', name: 'Alloy (Neutral)', gender: 'neutral', provider: 'openai' },
    { id: 'echo', name: 'Echo (Male)', gender: 'male', provider: 'openai' },
    { id: 'fable', name: 'Fable (Female)', gender: 'female', provider: 'openai' },
    { id: 'onyx', name: 'Onyx (Male)', gender: 'male', provider: 'openai' },
    { id: 'nova', name: 'Nova (Female)', gender: 'female', provider: 'openai' },
    { id: 'shimmer', name: 'Shimmer (Female)', gender: 'female', provider: 'openai' },

    // PlayHT voices - using the exact IDs that work with PlayHT
    { id: 's3://voice-cloning-zero-shot/d4ff0efe-21ce-4720-8c5e-3b4719b79457/waylon/manifest.json', name: 'Waylon (Male)', gender: 'male', provider: 'playht' },
    { id: 's3://voice-cloning-zero-shot/9a3fb33c-e084-4363-b4c8-215c123c871c/leyro/manifest.json', name: 'Leyro (Male)', gender: 'male', provider: 'playht' },
    { id: 's3://voice-cloning-zero-shot/2cbffa49-5dfe-4378-a54f-b824f7bbb032/theodoresaad/manifest.json', name: 'Dusty (Male)', gender: 'male', provider: 'playht' },
    { id: 's3://mockingbird-prod/agent_47_carmelo_pampillonio_58e796e1-0b87-4f3e-8b36-7def6d65ce66/voices/speaker/manifest.json', name: 'Decker (Male)', gender: 'male', provider: 'playht' },
    { id: 's3://voice-cloning-zero-shot/abc2d0e6-9433-4dcc-b416-0b035169f37e/original/manifest.json', name: 'Cali (Male)', gender: 'male', provider: 'playht' }
  ]);
  const fileInputRef = useRef(null);

  // State for UI
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  // Handle voice change
  const handleVoiceChange = async (e) => {
    const voiceId = e.target.value;

    // Find the selected voice preset to get the provider
    const selectedVoice = voicePresets.find(voice => voice.id === voiceId);
    const voiceProvider = selectedVoice?.provider || '11labs';

    // Update form data with both voiceId and provider
    setFormData(prev => ({
      ...prev,
      voiceId,
      voiceProvider
    }));

    // Update preview config for real-time preview with both voiceId and provider
    onUpdate({
      voiceId,
      voiceProvider
    });

    console.log(`[VoiceTab] Voice changed to: ${voiceId} (Provider: ${voiceProvider})`);

    // 🎯 PRIORITY: Update Vapi assistant directly (source of truth)
    try {
      setLoading(true);

      // Check if attorney object and assistant ID exist
      if (!attorney || !attorney.vapi_assistant_id) {
        console.error('Attorney or Vapi assistant ID is missing');
        setError('Assistant ID is missing. Please refresh the page.');
        setLoading(false);
        return;
      }

      console.log(`🔄 Updating Vapi assistant ${attorney.vapi_assistant_id} with voice: ${voiceId} (${voiceProvider})`);

      // Initialize the enhanced manager
      await enhancedVapiAssistantManager.initialize();

      // Update the Vapi assistant with new voice settings
      const updateConfig = {
        voice: {
          provider: voiceProvider,
          voiceId: voiceId
        }
      };

      const vapiResult = await enhancedVapiAssistantManager.mcpService.updateAssistant(
        attorney.vapi_assistant_id,
        updateConfig
      );

      console.log('✅ Successfully updated Vapi assistant voice settings:', vapiResult);

      // 📝 BACKUP: Also update Supabase for reference
      const { error: updateError } = await supabase
        .from('attorneys')
        .update({
          voice_id: voiceId,
          voice_provider: voiceProvider,
          updated_at: new Date()
        })
        .eq('id', attorney.id);

      if (updateError) {
        console.warn('⚠️ Failed to update Supabase backup:', updateError);
        // Don't fail the whole operation if Supabase update fails
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 1500);

    } catch (error) {
      console.error('❌ Error updating voice settings:', error);
      setError(`Failed to update voice settings: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle voice file selection
  const handleVoiceFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setVoiceFile(file);
  };

  // Handle voice cloning
  const handleVoiceCloning = async () => {
    if (!voiceFile) {
      setError("Please select a voice recording file first");
      return;
    }

    // Get attorney data from localStorage if not provided
    let currentAttorney = attorney;
    if (!currentAttorney || !currentAttorney.id) {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        currentAttorney = JSON.parse(storedAttorney);
        console.log('Retrieved attorney from localStorage:', currentAttorney);
      }

      // If still no attorney ID, show error
      if (!currentAttorney || !currentAttorney.id) {
        // In development mode, create a temporary ID
        if (import.meta.env.DEV) {
          console.warn('Development mode: Using temporary attorney ID');
          currentAttorney = {
            ...currentAttorney,
            id: 'dev-' + Date.now()
          };
        } else {
          console.error('Attorney ID is undefined or null');
          setError('Attorney ID is missing. Please refresh the page.');
          return;
        }
      }
    }

    setVoiceCloning(true);
    setError(null);

    try {
      // Handle file upload based on environment
      let publicUrl;

      if (import.meta.env.DEV) {
        console.log('Development mode: Skipping file upload to Supabase');

        // Create a local object URL for the file (this will only work for the current session)
        publicUrl = URL.createObjectURL(voiceFile);
        console.log('Created local URL for voice file:', publicUrl);
      } else {
        // Production mode - upload to Supabase
        try {
          // Generate a safe file name with the attorney ID
          const safeId = currentAttorney.id;
          const fileName = `voice-clone-${safeId}-${Date.now()}`;

          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('legalscout_bucket1')
            .upload(fileName, voiceFile, {
              cacheControl: '3600',
              upsert: true,
              public: true
            });

          if (uploadError) throw uploadError;

          // Get the public URL
          const { data: { publicUrl: url } } = supabase.storage
            .from('legalscout_bucket1')
            .getPublicUrl(fileName);

          publicUrl = url;
        } catch (error) {
          console.error('Error uploading voice file to Supabase:', error);
          throw error;
        }
      }

      // In a real implementation, we would call the PlayHT API here
      // For now, we'll simulate a successful voice cloning
      const clonedVoiceId = `cloned-${Date.now()}`;

      // Update the form data with the new voice ID
      setFormData(prev => ({
        ...prev,
        voiceId: clonedVoiceId,
        voiceProvider: 'playht'
      }));

      // Update the preview
      onUpdate({
        voiceId: clonedVoiceId,
        voiceProvider: 'playht'
      });

      // Update based on environment
      if (import.meta.env.DEV) {
        console.log('Development mode: Skipping database update for voice cloning');

        // Update the attorney object in localStorage
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          const attorneyData = JSON.parse(storedAttorney);
          const updatedAttorney = {
            ...attorneyData,
            voice_id: clonedVoiceId,
            voice_provider: 'playht',
            updated_at: new Date().toISOString()
          };

          // Save to localStorage
          localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
          console.log('Updated attorney in localStorage with new voice ID:', updatedAttorney);
        }
      } else {
        // Production mode - update the database
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({
            voice_id: clonedVoiceId,
            updated_at: new Date()
          })
          .eq('id', currentAttorney.id);

        if (updateError) {
          throw updateError;
        }
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error cloning voice:', error);
      setError(error.message);
    } finally {
      setVoiceCloning(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get attorney data from localStorage if not provided
      let currentAttorney = attorney;
      if (!currentAttorney || !currentAttorney.id) {
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          currentAttorney = JSON.parse(storedAttorney);
          console.log('Retrieved attorney from localStorage:', currentAttorney);
        }

        // If still no attorney ID, show error
        if (!currentAttorney || !currentAttorney.id) {
          // In development mode, create a temporary ID
          if (import.meta.env.DEV) {
            console.warn('Development mode: Using temporary attorney ID');
            currentAttorney = {
              ...currentAttorney,
              id: 'dev-' + Date.now()
            };
          } else {
            console.error('Attorney ID is undefined or null');
            setError('Attorney ID is missing. Please refresh the page.');
            setLoading(false);
            return;
          }
        }
      }

      // Update based on environment
      if (import.meta.env.DEV) {
        console.log('Development mode: Skipping database update for voice settings');

        // Update the attorney object in localStorage
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          const attorneyData = JSON.parse(storedAttorney);
          const updatedAttorney = {
            ...attorneyData,
            voice_id: formData.voiceId,
            updated_at: new Date().toISOString()
          };

          // Save to localStorage
          localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
          console.log('Updated attorney in localStorage with voice settings:', updatedAttorney);
        }
      } else {
        // Production mode - update the database
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({
            voice_id: formData.voiceId,
            updated_at: new Date()
          })
          .eq('id', currentAttorney.id);

        if (updateError) throw updateError;
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error updating voice settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="voice-tab">
      <h2>Voice Configuration</h2>
      <p className="tab-description">
        Customize your AI assistant's voice to match your preferences.
      </p>

      {error && (
        <div className="alert alert-error">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <FaCheck />
          <span>Voice settings updated successfully!</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="dashboard-card">
          <h3>Voice Selection</h3>
          <p className="card-description">
            Choose a voice for your AI assistant from our selection of professional voices.
          </p>

          <div className="form-group">
            <label htmlFor="voiceId">Select Voice</label>
            <select
              id="voiceId"
              name="voiceId"
              className="form-control"
              value={formData.voiceId}
              onChange={handleVoiceChange}
            >
              {voicePresets.map(voice => (
                <option key={voice.id} value={voice.id}>
                  {voice.name}
                </option>
              ))}
            </select>
            <small className="form-text">
              This voice will be used for all conversations with your AI assistant.
            </small>
          </div>
        </div>

        <div className="dashboard-card">
          <h3>Voice Cloning</h3>
          <p className="card-description">
            Create a personalized AI voice by uploading a recording of your voice.
          </p>

          <div className="voice-clone-section">
            <div className="voice-upload-container">
              <input
                type="file"
                id="voiceFile"
                ref={fileInputRef}
                onChange={handleVoiceFileChange}
                accept="audio/*"
                style={{ display: 'none' }}
              />

              <div className="voice-upload-controls">
                <button
                  type="button"
                  className="upload-button"
                  onClick={() => fileInputRef.current.click()}
                >
                  <FaUpload />
                  <span>Upload Voice Recording</span>
                </button>

                {voiceFile && (
                  <div className="file-info">
                    <FaVolumeUp />
                    <span>{voiceFile.name}</span>
                  </div>
                )}
              </div>

              <button
                type="button"
                className="clone-button"
                onClick={handleVoiceCloning}
                disabled={!voiceFile || voiceCloning}
              >
                <FaMicrophone />
                <span>{voiceCloning ? 'Cloning Voice...' : 'Clone Voice'}</span>
              </button>
            </div>

            <div className="voice-requirements">
              <h5>Voice Recording Requirements:</h5>
              <ul>
                <li>Clear audio with minimal background noise</li>
                <li>Duration: 30 seconds to 2 minutes</li>
                <li>Speak naturally in your normal tone</li>
                <li>Supported formats: MP3, WAV, M4A</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="action-buttons">
          <button
            type="submit"
            className="dashboard-button"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default VoiceTab;
