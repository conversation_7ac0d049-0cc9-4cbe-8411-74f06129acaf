import React, { useState, useEffect } from 'react';
import { FaTools, FaCheck, FaTimes, FaSync, FaPhone, FaExclamationTriangle, FaCog, FaPlus, FaRobot, FaClock, FaTag } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';
import './ToolsTab.css';

/**
 * ToolsTab component for managing Vapi tools and call forwarding
 * Allows attorneys to select tools for their assistant and configure call forwarding
 */
const ToolsTab = ({ attorney, onUpdate }) => {
  const [availableTools, setAvailableTools] = useState([]);
  const [selectedTools, setSelectedTools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [syncing, setSyncing] = useState(false);
  const [expandedTool, setExpandedTool] = useState(null);
  const [toolConfigs, setToolConfigs] = useState({});

  // Use AssistantAware context to get current assistant ID
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // Get the current assistant ID from context (preferred) or fallback to attorney
  const currentAssistantId = currentAssistant?.id || attorney?.current_assistant_id || attorney?.vapi_assistant_id;



  // Standard tool definitions
  const STANDARD_TOOLS = {
    'live_dossier': {
      id: '4a0d63cf-0b84-4eec-bddf-9c5869439d7e',
      name: 'Live Dossier',
      description: 'Real-time case information display that updates during conversations',
      category: 'Legal Research',
      recommended: true
    },
    'transfer_call_tool': {
      id: 'e470c69b-62af-475d-a727-652a40a46e35',
      name: 'Call Transfer',
      description: 'Transfer calls to attorneys or team members when needed',
      category: 'Call Management',
      recommended: true
    },
    'lookup_legal_citations': {
      id: '8ea7a5ef-f0be-421d-a551-fa3e73babdb7',
      name: 'Legal Citations',
      description: 'Verify and lookup legal citations using CourtListener API',
      category: 'Legal Research',
      recommended: false
    }
  };

  // Fetch forwarding rules from Supabase
  const fetchForwardingRules = async () => {
    try {
      // This is a placeholder function since forwarding rules are now handled in CallsTab
      // We can remove this call or implement it if needed
      console.log('[ToolsTab] Forwarding rules are now managed in CallsTab');
    } catch (error) {
      console.error('Error fetching forwarding rules:', error);
    }
  };

  // Fetch available tools from Vapi and forwarding rules
  useEffect(() => {
    fetchAvailableTools();
    if (attorney?.id) {
      fetchForwardingRules();
    }
  }, []);

  // Load attorney's selected tools
  useEffect(() => {
    if (attorney?.vapi_tools) {
      try {
        const tools = Array.isArray(attorney.vapi_tools)
          ? attorney.vapi_tools
          : JSON.parse(attorney.vapi_tools || '[]');
        setSelectedTools(tools);
      } catch (error) {
        console.error('Error parsing attorney tools:', error);
        setSelectedTools([]);
      }
    }

    // Load tool configurations
    if (attorney?.tool_configurations) {
      try {
        const configs = typeof attorney.tool_configurations === 'string'
          ? JSON.parse(attorney.tool_configurations)
          : attorney.tool_configurations;
        setToolConfigs(configs);
      } catch (error) {
        console.error('Error parsing tool configurations:', error);
      }
    }
  }, [attorney]);

  const fetchAvailableTools = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a comprehensive tool mapping with better categorization
      const toolCategories = {
        'live_dossier': { category: 'Legal Research', recommended: true },
        'transfer_call_tool': { category: 'Call Management', recommended: true },
        'lookup_legal_citations': { category: 'Legal Research', recommended: true },
        'google_calendar_tool': { category: 'Scheduling', recommended: false },
        'check_calendar': { category: 'Scheduling', recommended: false },
        'dtmf_tool': { category: 'Call Control', recommended: false },
        'mcp_tool': { category: 'Integration', recommended: false },
        'query_tool': { category: 'Data Access', recommended: false },
        'function_tool': { category: 'Custom Functions', recommended: false }
      };

      // Use the standard tools as base, but enhance with real Vapi data
      const standardTools = Object.values(STANDARD_TOOLS);

      // For now, create a comprehensive list based on known tools
      // In production, this would fetch from Vapi API
      const allKnownTools = [
        {
          id: '4a0d63cf-0b84-4eec-bddf-9c5869439d7e',
          name: 'Live Dossier',
          description: 'Real-time case information display that updates during conversations',
          category: 'Legal Research',
          recommended: true,
          configurable: false
        },
        {
          id: 'e470c69b-62af-475d-a727-652a40a46e35',
          name: 'Call Transfer',
          description: 'Transfer calls to attorneys or team members when needed',
          category: 'Call Management',
          recommended: true,
          configurable: true,
          configFields: [
            {
              key: 'phoneNumber',
              label: 'Transfer Phone Number',
              type: 'tel',
              placeholder: 'Enter phone number (E164 format)',
              defaultValue: attorney?.phone || '',
              required: true
            },
            {
              key: 'message',
              label: 'Transfer Message',
              type: 'textarea',
              placeholder: 'Message spoken to customer before transfer',
              defaultValue: 'Transferring you to the attorney for this matter.',
              required: false
            },
            {
              key: 'callerIdOverride',
              label: 'Caller ID Override (Optional)',
              type: 'tel',
              placeholder: 'e.g. +14155551234',
              defaultValue: '',
              required: false
            }
          ]
        },
        {
          id: '8ea7a5ef-f0be-421d-a551-fa3e73babdb7',
          name: 'Legal Citations',
          description: 'Verify and lookup legal citations using CourtListener API',
          category: 'Legal Research',
          recommended: true,
          configurable: true,
          configFields: [
            {
              key: 'autoLookup',
              label: 'Automatic Citation Lookup',
              type: 'checkbox',
              defaultValue: true,
              description: 'Automatically verify citations mentioned in conversations'
            },
            {
              key: 'includeContext',
              label: 'Include Case Context',
              type: 'checkbox',
              defaultValue: true,
              description: 'Include brief case context with citation results'
            }
          ]
        },
        {
          id: '7a6aebe4-ea41-4f7c-b2c8-f07a808f7c9b',
          name: 'Google Calendar',
          description: 'Schedule events and add invitees to calendar',
          category: 'Scheduling',
          recommended: false,
          configurable: true,
          configFields: [
            {
              key: 'defaultDuration',
              label: 'Default Meeting Duration (minutes)',
              type: 'number',
              defaultValue: 30,
              min: 15,
              max: 240
            },
            {
              key: 'calendarEmail',
              label: 'Calendar Email',
              type: 'email',
              placeholder: '<EMAIL>',
              defaultValue: attorney?.email || '',
              required: true
            },
            {
              key: 'meetingType',
              label: 'Default Meeting Type',
              type: 'select',
              defaultValue: 'consultation',
              options: [
                { value: 'consultation', label: 'Initial Consultation' },
                { value: 'follow-up', label: 'Follow-up Meeting' },
                { value: 'document-review', label: 'Document Review' },
                { value: 'court-prep', label: 'Court Preparation' }
              ]
            }
          ]
        },
        {
          id: 'd11de3bc-8c03-472c-8241-9c0b9745c9e3',
          name: 'Calendar Availability',
          description: 'Check calendar availability for scheduling',
          category: 'Scheduling',
          recommended: false,
          configurable: true,
          configFields: [
            {
              key: 'businessHoursStart',
              label: 'Business Hours Start',
              type: 'time',
              defaultValue: '09:00'
            },
            {
              key: 'businessHoursEnd',
              label: 'Business Hours End',
              type: 'time',
              defaultValue: '17:00'
            },
            {
              key: 'timeZone',
              label: 'Time Zone',
              type: 'select',
              defaultValue: 'America/New_York',
              options: [
                { value: 'America/New_York', label: 'Eastern Time' },
                { value: 'America/Chicago', label: 'Central Time' },
                { value: 'America/Denver', label: 'Mountain Time' },
                { value: 'America/Los_Angeles', label: 'Pacific Time' }
              ]
            }
          ]
        },
        {
          id: '8318fda5-3af7-46ff-9b04-743f0f14fb07',
          name: 'DTMF Control',
          description: 'Handle phone keypad input during calls',
          category: 'Call Control',
          recommended: false,
          configurable: false
        }
      ];

      setAvailableTools(allKnownTools);

    } catch (error) {
      console.error('Error fetching tools:', error);
      setError('Failed to load available tools');
      // Fallback to standard tools
      setAvailableTools(Object.values(STANDARD_TOOLS));
    } finally {
      setLoading(false);
    }
  };





  const handleToolToggle = (toolId) => {
    setSelectedTools(prev => {
      const isSelected = prev.includes(toolId);
      if (isSelected) {
        return prev.filter(id => id !== toolId);
      } else {
        return [...prev, toolId];
      }
    });
  };

  const handleToolExpand = (toolId) => {
    setExpandedTool(expandedTool === toolId ? null : toolId);
  };

  const handleToolConfigChange = (toolId, field, value) => {
    setToolConfigs(prev => ({
      ...prev,
      [toolId]: {
        ...prev[toolId],
        [field]: value
      }
    }));
  };

  const initializeToolConfig = (tool) => {
    if (!tool.configurable || toolConfigs[tool.id]) return;

    const defaultConfig = {};
    tool.configFields?.forEach(field => {
      defaultConfig[field.key] = field.defaultValue;
    });

    setToolConfigs(prev => ({
      ...prev,
      [tool.id]: defaultConfig
    }));
  };

  const handleSaveChanges = async () => {
    try {
      setSyncing(true);

      // Prepare update data
      const updateData = {
        vapi_tools: JSON.stringify(selectedTools),
        tool_configurations: JSON.stringify(toolConfigs)
      };

      // Call the onUpdate function to save changes
      if (onUpdate) {
        await onUpdate(updateData);
      }

      console.log('Tools saved successfully');
    } catch (error) {
      console.error('Error saving tools:', error);
      setError('Failed to save changes');
    } finally {
      setSyncing(false);
    }
  };

  if (loading) {
    return (
      <div className="tools-tab">
        <div className="loading-state">
          <FaSync className="spinning" />
          <p>Loading available tools...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="tools-tab">
      <div className="tools-header">
        <h2>AI Assistant Tools</h2>
        <p className="tab-description">
          Configure tools that your AI assistant can use during conversations with clients.
          {isAssistantSelected && currentAssistant?.assistantName && (
            <span className="assistant-context">
              {' '}Currently configuring: <strong>{currentAssistant.assistantName}</strong>
            </span>
          )}
        </p>
      </div>

      {error && (
        <div className="error-message">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      {/* Available Tools Section */}
      <div className="dashboard-card">
        <h3>Available Tools</h3>
        <p className="card-description">
          Select tools that your AI assistant can use during conversations.
        </p>

        <div className="tools-grid">
          {availableTools.map(tool => {
            const isSelected = selectedTools.includes(tool.id);
            const isExpanded = expandedTool === tool.id;

            // Initialize tool config if needed
            if (tool.configurable && isSelected) {
              initializeToolConfig(tool);
            }

            return (
              <div
                key={tool.id}
                className={`tool-card ${isSelected ? 'selected' : ''} ${tool.recommended ? 'recommended' : ''} ${isExpanded ? 'expanded' : ''}`}
              >
                <div
                  className="tool-header"
                  onClick={() => handleToolToggle(tool.id)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="tool-info">
                    <h4>{tool.name}</h4>
                    <span className="tool-category">{tool.category}</span>
                    {tool.recommended && <span className="recommended-badge">Recommended</span>}
                    {tool.configurable && <span className="configurable-badge">Configurable</span>}
                  </div>
                  <div className="tool-actions">
                    <div className="tool-toggle">
                      {isSelected ? (
                        <FaCheck className="selected-icon" />
                      ) : (
                        <div className="unselected-icon"></div>
                      )}
                    </div>
                    {tool.configurable && isSelected && (
                      <button
                        className="config-toggle"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToolExpand(tool.id);
                        }}
                        title="Configure tool"
                      >
                        <FaCog className={isExpanded ? 'expanded' : ''} />
                      </button>
                    )}
                  </div>
                </div>
                <p className="tool-description">
                  {tool.description}
                </p>

                {/* Configuration Panel */}
                {tool.configurable && isSelected && isExpanded && (
                  <div className="tool-config-panel">
                    <h5>Configuration</h5>
                    <div className="config-fields">
                      {tool.configFields?.map(field => (
                        <div key={field.key} className="config-field">
                          <label htmlFor={`${tool.id}-${field.key}`}>
                            {field.label}
                            {field.required && <span className="required">*</span>}
                          </label>

                          {field.type === 'textarea' && (
                            <textarea
                              id={`${tool.id}-${field.key}`}
                              value={toolConfigs[tool.id]?.[field.key] || field.defaultValue || ''}
                              onChange={(e) => handleToolConfigChange(tool.id, field.key, e.target.value)}
                              placeholder={field.placeholder}
                              rows="3"
                            />
                          )}

                          {field.type === 'select' && (
                            <select
                              id={`${tool.id}-${field.key}`}
                              value={toolConfigs[tool.id]?.[field.key] || field.defaultValue || ''}
                              onChange={(e) => handleToolConfigChange(tool.id, field.key, e.target.value)}
                            >
                              {field.options?.map(option => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          )}

                          {field.type === 'checkbox' && (
                            <label className="checkbox-label">
                              <input
                                type="checkbox"
                                checked={toolConfigs[tool.id]?.[field.key] ?? field.defaultValue}
                                onChange={(e) => handleToolConfigChange(tool.id, field.key, e.target.checked)}
                              />
                              <span className="checkmark"></span>
                              {field.description}
                            </label>
                          )}

                          {['text', 'email', 'tel', 'number', 'time'].includes(field.type) && (
                            <input
                              type={field.type}
                              id={`${tool.id}-${field.key}`}
                              value={toolConfigs[tool.id]?.[field.key] || field.defaultValue || ''}
                              onChange={(e) => handleToolConfigChange(tool.id, field.key, e.target.value)}
                              placeholder={field.placeholder}
                              min={field.min}
                              max={field.max}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>



      {/* Action Buttons */}
      <div className="action-buttons">
        <button
          type="button"
          className="dashboard-button"
          onClick={handleSaveChanges}
          disabled={syncing}
        >
          {syncing ? (
            <>
              <FaSync className="spinning" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>


    </div>
  );
};

export default ToolsTab;
