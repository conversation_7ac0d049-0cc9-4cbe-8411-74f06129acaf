<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Self-Healing CSP Test</title>
    
    <!-- Load the self-healing CSP system -->
    <script src="/comprehensive-csp-manager.js"></script>
    
    <!-- Basic CSP that will be enhanced by the self-healing system -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; frame-src 'self';">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.danger {
            background: #dc3545;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .log-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
        
        iframe {
            width: 100%;
            height: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Self-Healing CSP Test Suite</h1>
            <p>Testing automatic CSP violation detection and healing</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🔍 CSP Manager Status</h3>
                <button class="test-button" onclick="checkCSPStatus()">Check Status</button>
                <button class="test-button" onclick="showCurrentCSP()">Show Current CSP</button>
                <button class="test-button success" onclick="clearLogs()">Clear Logs</button>
                <div id="status-output" class="log-output">Click "Check Status" to see CSP manager information...</div>
            </div>
            
            <div class="test-section">
                <h3>🧪 Violation Tests</h3>
                <p>These tests will trigger CSP violations that should be automatically healed:</p>
                
                <button class="test-button" onclick="testVercelLiveFrame()">Test Vercel.live Frame</button>
                <span id="vercel-test-status" class="status pending">Pending</span>
                
                <button class="test-button" onclick="testExternalScript()">Test External Script</button>
                <span id="script-test-status" class="status pending">Pending</span>
                
                <button class="test-button" onclick="testExternalStylesheet()">Test External Stylesheet</button>
                <span id="style-test-status" class="status pending">Pending</span>
                
                <button class="test-button danger" onclick="testUntrustedDomain()">Test Untrusted Domain</button>
                <span id="untrusted-test-status" class="status pending">Pending</span>
                
                <div id="test-container"></div>
            </div>
            
            <div class="test-section">
                <h3>📊 Violation Log</h3>
                <button class="test-button" onclick="showViolations()">Show Violations</button>
                <button class="test-button" onclick="showUntrustedAttempts()">Show Untrusted Attempts</button>
                <div id="violation-log" class="log-output">Violation log will appear here...</div>
            </div>
            
            <div class="test-section">
                <h3>🔧 Manual Controls</h3>
                <button class="test-button" onclick="addTrustedDomain()">Add Trusted Domain</button>
                <button class="test-button" onclick="enableDebugMode()">Enable Debug Mode</button>
                <button class="test-button" onclick="disableDebugMode()">Disable Debug Mode</button>
                <button class="test-button success" onclick="forceCSPUpdate()">Force CSP Update</button>
            </div>
            
            <div class="test-section">
                <h3>📈 Real-time Monitoring</h3>
                <button class="test-button" onclick="startMonitoring()">Start Real-time Monitoring</button>
                <button class="test-button" onclick="stopMonitoring()">Stop Monitoring</button>
                <div id="monitoring-output" class="log-output">Real-time monitoring will appear here...</div>
            </div>
        </div>
    </div>

    <script>
        let monitoringInterval;
        let logCount = 0;
        
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            // Add to status output
            const statusOutput = document.getElementById('status-output');
            statusOutput.textContent += logMessage;
            statusOutput.scrollTop = statusOutput.scrollHeight;
            
            // Add to monitoring if active
            if (monitoringInterval) {
                const monitoringOutput = document.getElementById('monitoring-output');
                monitoringOutput.textContent += logMessage;
                monitoringOutput.scrollTop = monitoringOutput.scrollHeight;
            }
            
            logCount++;
        }
        
        function checkCSPStatus() {
            if (window.cspManager) {
                const status = window.cspManager.getStatus();
                log('🛡️ CSP Manager Status:');
                log(`  - Auto-fix enabled: ${status.autoFixEnabled}`);
                log(`  - Self-healing enabled: ${status.selfHealingEnabled}`);
                log(`  - Debug mode: ${status.debugMode}`);
                log(`  - Total violations: ${status.violations.length}`);
                log(`  - Untrusted attempts: ${status.untrustedAttempts.length}`);
            } else {
                log('❌ CSP Manager not found!', 'error');
            }
        }
        
        function showCurrentCSP() {
            if (window.cspManager) {
                const csp = window.cspManager.getCurrentCSP();
                log('📋 Current CSP Directives:');
                for (const [directive, sources] of Object.entries(csp)) {
                    log(`  ${directive}: ${sources.join(' ')}`);
                }
            } else {
                log('❌ CSP Manager not found!', 'error');
            }
        }
        
        function testVercelLiveFrame() {
            log('🧪 Testing Vercel.live frame...');
            const iframe = document.createElement('iframe');
            iframe.src = 'https://vercel.live';
            iframe.style.display = 'none';
            
            iframe.onload = function() {
                log('✅ Vercel.live frame loaded successfully');
                document.getElementById('vercel-test-status').textContent = 'PASS';
                document.getElementById('vercel-test-status').className = 'status pass';
            };
            
            iframe.onerror = function() {
                log('❌ Vercel.live frame blocked');
                document.getElementById('vercel-test-status').textContent = 'BLOCKED';
                document.getElementById('vercel-test-status').className = 'status fail';
            };
            
            document.getElementById('test-container').appendChild(iframe);
            
            // Check status after a delay
            setTimeout(() => {
                if (document.getElementById('vercel-test-status').textContent === 'Pending') {
                    document.getElementById('vercel-test-status').textContent = 'TIMEOUT';
                    document.getElementById('vercel-test-status').className = 'status fail';
                }
            }, 5000);
        }
        
        function testExternalScript() {
            log('🧪 Testing external script loading...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js';
            
            script.onload = function() {
                log('✅ External script loaded successfully');
                document.getElementById('script-test-status').textContent = 'PASS';
                document.getElementById('script-test-status').className = 'status pass';
            };
            
            script.onerror = function() {
                log('❌ External script blocked');
                document.getElementById('script-test-status').textContent = 'BLOCKED';
                document.getElementById('script-test-status').className = 'status fail';
            };
            
            document.head.appendChild(script);
        }
        
        function testExternalStylesheet() {
            log('🧪 Testing external stylesheet loading...');
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap';
            
            link.onload = function() {
                log('✅ External stylesheet loaded successfully');
                document.getElementById('style-test-status').textContent = 'PASS';
                document.getElementById('style-test-status').className = 'status pass';
            };
            
            link.onerror = function() {
                log('❌ External stylesheet blocked');
                document.getElementById('style-test-status').textContent = 'BLOCKED';
                document.getElementById('style-test-status').className = 'status fail';
            };
            
            document.head.appendChild(link);
        }
        
        function testUntrustedDomain() {
            log('🧪 Testing untrusted domain (should be blocked)...');
            const script = document.createElement('script');
            script.src = 'https://malicious-example.com/script.js';
            
            script.onload = function() {
                log('⚠️ Untrusted script loaded (security risk!)');
                document.getElementById('untrusted-test-status').textContent = 'LOADED';
                document.getElementById('untrusted-test-status').className = 'status fail';
            };
            
            script.onerror = function() {
                log('✅ Untrusted script properly blocked');
                document.getElementById('untrusted-test-status').textContent = 'BLOCKED';
                document.getElementById('untrusted-test-status').className = 'status pass';
            };
            
            document.head.appendChild(script);
        }
        
        function showViolations() {
            if (window.cspManager) {
                const status = window.cspManager.getStatus();
                const violationLog = document.getElementById('violation-log');
                
                if (status.violations.length === 0) {
                    violationLog.textContent = 'No violations recorded.';
                } else {
                    violationLog.textContent = 'CSP Violations:\n';
                    status.violations.forEach((violation, index) => {
                        violationLog.textContent += `${index + 1}. ${violation.directive}: ${violation.blockedURI} (count: ${violation.count})\n`;
                    });
                }
            }
        }
        
        function showUntrustedAttempts() {
            if (window.cspManager) {
                const status = window.cspManager.getStatus();
                const violationLog = document.getElementById('violation-log');
                
                if (status.untrustedAttempts.length === 0) {
                    violationLog.textContent = 'No untrusted attempts recorded.';
                } else {
                    violationLog.textContent = 'Untrusted Domain Attempts:\n';
                    status.untrustedAttempts.forEach((attempt, index) => {
                        violationLog.textContent += `${index + 1}. ${attempt.directive}: ${attempt.domain} (${attempt.timestamp})\n`;
                    });
                }
            }
        }
        
        function addTrustedDomain() {
            const domain = prompt('Enter domain to add to trusted list (e.g., https://example.com):');
            if (domain && window.cspManager) {
                window.cspManager.addTrustedDomain(domain);
                log(`✅ Added trusted domain: ${domain}`);
            }
        }
        
        function enableDebugMode() {
            if (window.cspManager) {
                window.cspManager.enableDebugMode();
                log('🔧 Debug mode enabled');
            }
        }
        
        function disableDebugMode() {
            if (window.cspManager) {
                window.cspManager.disableDebugMode();
                log('🔇 Debug mode disabled');
            }
        }
        
        function forceCSPUpdate() {
            if (window.cspManager) {
                window.cspManager.ensureEssentialDirectives();
                window.cspManager.addKnownSafeSources();
                log('🔄 Forced CSP update completed');
            }
        }
        
        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }
            
            const monitoringOutput = document.getElementById('monitoring-output');
            monitoringOutput.textContent = 'Real-time monitoring started...\n';
            
            monitoringInterval = setInterval(() => {
                if (window.cspManager) {
                    const status = window.cspManager.getStatus();
                    const timestamp = new Date().toLocaleTimeString();
                    monitoringOutput.textContent += `[${timestamp}] Violations: ${status.violations.length}, Untrusted: ${status.untrustedAttempts.length}\n`;
                    monitoringOutput.scrollTop = monitoringOutput.scrollHeight;
                }
            }, 2000);
            
            log('📈 Started real-time monitoring');
        }
        
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('📈 Stopped real-time monitoring');
            }
        }
        
        function clearLogs() {
            document.getElementById('status-output').textContent = 'Logs cleared.\n';
            document.getElementById('violation-log').textContent = 'Violation log cleared.\n';
            document.getElementById('monitoring-output').textContent = 'Monitoring log cleared.\n';
            logCount = 0;
        }
        
        // Initialize
        window.addEventListener('load', function() {
            log('🛡️ Self-Healing CSP Test Suite loaded');
            log('Click "Check Status" to verify CSP Manager is active');
            
            // Auto-check status after a short delay
            setTimeout(checkCSPStatus, 1000);
        });
        
        // Listen for CSP violations
        document.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP Violation detected: ${e.violatedDirective} blocked ${e.blockedURI}`, 'warn');
        });
    </script>
</body>
</html>
