/**
 * Enhanced React Polyfill for Framer Motion
 *
 * This file provides robust polyfills for React features that Framer Motion depends on.
 * It ensures that React's createContext is available globally to prevent errors
 * like "Cannot read properties of undefined (reading 'createContext')"
 */

import React from 'react';

// Make React available globally with enhanced protection
if (typeof window !== 'undefined') {
  // Ensure React is defined globally
  if (!window.React) {
    window.React = {};
    console.log('[ReactPolyfill] Created window.React object');
  }

  // Store the original React object and its createContext method
  const originalReact = window.React || {};
  const originalCreateContext = originalReact.createContext;

  // Directly assign React methods to window.React
  Object.keys(React).forEach(key => {
    // Don't overwrite existing methods unless they're undefined
    if (window.React[key] === undefined) {
      window.React[key] = React[key];
      console.log(`[ReactPolyfill] Added ${key} to window.React`);
    }
  });

  // Ensure createContext is available and protected
  // Prioritize the original createContext if it exists
  if (!window.React.createContext) {
    window.React.createContext = originalCreateContext || React.createContext;
    console.log('[ReactPolyfill] Added createContext to window.React');
  }

  // Define all essential React methods
  const reactMethods = [
    'useState', 'useEffect', 'useLayoutEffect', 'useRef',
    'useCallback', 'useMemo', 'useContext', 'forwardRef',
    'createElement', 'cloneElement', 'createRef', 'Component',
    'PureComponent', 'Fragment', 'Children', 'isValidElement'
  ];

  // Add all React methods that might be needed
  reactMethods.forEach(method => {
    if (!window.React[method] && React[method]) {
      window.React[method] = React[method];
      console.log(`[ReactPolyfill] Added ${method} from React`);
    }
  });

  // Instead of using Object.defineProperty, set up a periodic check
  const backupCreateContext = window.React.createContext;
  const checkAndRestoreReact = function() {
    if (window.React && !window.React.createContext && backupCreateContext) {
      console.log('[ReactPolyfill] Restoring React.createContext');
      window.React.createContext = backupCreateContext;
    }
  };

  // Set up an interval to periodically check React.createContext
  const intervalId = setInterval(checkAndRestoreReact, 100);

  // Clean up the interval after 10 seconds
  setTimeout(() => {
    clearInterval(intervalId);
    console.log('[ReactPolyfill] Stopped monitoring React.createContext');
  }, 10000);

  // Create a global LayoutGroupContext to prevent errors
  if (!window.LayoutGroupContext) {
    window.LayoutGroupContext = {
      Provider: function(props) { return props.children || null; },
      Consumer: function(props) { return props.children ? props.children({}) : null; }
    };
    console.log('[ReactPolyfill] Created global LayoutGroupContext');
  }

  console.log('[ReactPolyfill] Enhanced React polyfill applied successfully');
}

export default {};
