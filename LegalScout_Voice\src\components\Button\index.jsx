import React from 'react';

const Button = ({ onClick, mascot, label = "Start Free Consultation" }) => {
  return (
    <div className="button-container">
      <button
        onClick={onClick}
        className="mascot-button"
        style={{
          width: '200px',
          height: '200px',
          border: 'none',
          background: `url(${mascot}) center/contain no-repeat`,
          cursor: 'pointer'
        }}
      >
        <div className="button-label">{label}</div>
      </button>
    </div>
  );
};

export default Button; 