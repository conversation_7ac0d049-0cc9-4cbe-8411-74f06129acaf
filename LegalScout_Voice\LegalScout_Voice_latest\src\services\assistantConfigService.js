/**
 * Assistant Configuration Service
 *
 * Handles saving and loading configuration data at the assistant level
 * instead of attorney level. This enables multiple assistants per attorney
 * with different branding, messaging, and configuration.
 */

import { supabase } from '../lib/supabase';

export class AssistantConfigService {
  /**
   * Save complete assistant configuration
   * @param {string} assistantId - The assistant ID
   * @param {string} attorneyId - The attorney ID
   * @param {Object} config - Configuration object with all fields
   */
  static async saveAssistantConfig(assistantId, attorneyId, config) {
    if (!assistantId || !attorneyId) {
      throw new Error('Assistant ID and Attorney ID are required');
    }

    try {
      console.log(`[AssistantConfigService] Saving complete config for assistant:`, assistantId);

      const configData = {
        assistant_id: assistantId,
        attorney_id: attorneyId,
        // Branding fields
        firm_name: config.firmName,
        assistant_name: config.titleText || config.assistantName,
        logo_url: config.logoUrl,
        primary_color: config.primaryColor,
        secondary_color: config.secondaryColor,
        button_color: config.buttonColor,
        background_color: config.backgroundColor,
        background_opacity: config.backgroundOpacity,
        button_opacity: config.buttonOpacity,
        practice_area_background_opacity: config.practiceAreaBackgroundOpacity,
        text_background_color: config.textBackgroundColor,
        practice_description: config.practiceDescription,
        practice_areas: config.practiceAreas,
        // Messaging fields
        welcome_message: config.welcomeMessage,
        information_gathering: config.informationGathering || config.firstMessage,
        vapi_instructions: config.vapiInstructions,
        vapi_context: config.vapiContext,
        // Voice fields
        voice_provider: config.voiceProvider,
        voice_id: config.voiceId,
        ai_model: config.aiModel,
        // Other fields
        office_address: config.officeAddress,
        scheduling_link: config.schedulingLink,
        custom_fields: config.customFields,
        assistant_image_url: config.assistantImageUrl,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .upsert(configData, {
          onConflict: 'assistant_id,attorney_id'
        })
        .select()
        .single();

      if (error) throw error;

      console.log(`[AssistantConfigService] Successfully saved complete config:`, data);
      return data;
    } catch (error) {
      console.error(`[AssistantConfigService] Error saving complete config:`, error);
      throw error;
    }
  }

  /**
   * Load assistant-specific configuration
   * @param {string} assistantId - The assistant ID
   * @param {string} attorneyId - The attorney ID
   */
  static async loadAssistantConfig(assistantId, attorneyId) {
    if (!assistantId || !attorneyId) {
      console.warn('[AssistantConfigService] Missing assistant ID or attorney ID');
      return null;
    }

    try {
      console.log(`[AssistantConfigService] Loading config for assistant:`, assistantId);

      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .select('*')
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      if (data) {
        console.log(`[AssistantConfigService] Successfully loaded config`);
        return {
          // Branding configuration
          branding: {
            firmName: data.firm_name,
            titleText: data.assistant_name,
            logoUrl: data.logo_url,
            primaryColor: data.primary_color,
            secondaryColor: data.secondary_color,
            buttonColor: data.button_color,
            backgroundColor: data.background_color,
            backgroundOpacity: data.background_opacity,
            buttonOpacity: data.button_opacity,
            practiceAreaBackgroundOpacity: data.practice_area_background_opacity,
            textBackgroundColor: data.text_background_color,
            practiceDescription: data.practice_description,
            practiceAreas: data.practice_areas || []
          },
          // Messaging configuration
          messaging: {
            welcomeMessage: data.welcome_message,
            informationGathering: data.information_gathering,
            vapiInstructions: data.vapi_instructions,
            vapiContext: data.vapi_context
          },
          // Voice configuration
          voice: {
            voiceProvider: data.voice_provider,
            voiceId: data.voice_id
          },
          // Other configuration
          other: {
            aiModel: data.ai_model,
            officeAddress: data.office_address,
            schedulingLink: data.scheduling_link,
            customFields: data.custom_fields,
            assistantImageUrl: data.assistant_image_url
          }
        };
      }

      console.log(`[AssistantConfigService] No config found for assistant`);
      return null;
    } catch (error) {
      console.error(`[AssistantConfigService] Error loading config:`, error);
      return null;
    }
  }

  /**
   * Update specific fields in assistant configuration
   * @param {string} assistantId - The assistant ID
   * @param {string} attorneyId - The attorney ID
   * @param {Object} updates - Object with fields to update
   */
  static async updateAssistantConfig(assistantId, attorneyId, updates) {
    if (!assistantId || !attorneyId) {
      throw new Error('Assistant ID and Attorney ID are required');
    }

    try {
      console.log(`[AssistantConfigService] Updating config for assistant:`, assistantId, updates);

      // Map the updates to the correct database column names
      const dbUpdates = {};

      // Branding fields
      if (updates.firmName !== undefined) dbUpdates.firm_name = updates.firmName;
      if (updates.agent_name !== undefined) dbUpdates.agent_name = updates.agent_name; // New agent_name field
      if (updates.titleText !== undefined) dbUpdates.assistant_name = updates.titleText;
      if (updates.logoUrl !== undefined) dbUpdates.logo_url = updates.logoUrl;
      if (updates.primaryColor !== undefined) dbUpdates.primary_color = updates.primaryColor;
      if (updates.secondaryColor !== undefined) dbUpdates.secondary_color = updates.secondaryColor;
      if (updates.buttonColor !== undefined) dbUpdates.button_color = updates.buttonColor;
      if (updates.backgroundColor !== undefined) dbUpdates.background_color = updates.backgroundColor;
      if (updates.backgroundOpacity !== undefined) dbUpdates.background_opacity = updates.backgroundOpacity;
      if (updates.buttonOpacity !== undefined) dbUpdates.button_opacity = updates.buttonOpacity;
      if (updates.practiceAreaBackgroundOpacity !== undefined) dbUpdates.practice_area_background_opacity = updates.practiceAreaBackgroundOpacity;
      if (updates.textBackgroundColor !== undefined) dbUpdates.text_background_color = updates.textBackgroundColor;
      if (updates.practiceDescription !== undefined) dbUpdates.practice_description = updates.practiceDescription;
      if (updates.practiceAreas !== undefined) dbUpdates.practice_areas = updates.practiceAreas;

      // Messaging fields
      if (updates.welcomeMessage !== undefined) dbUpdates.welcome_message = updates.welcomeMessage;
      if (updates.informationGathering !== undefined) dbUpdates.information_gathering = updates.informationGathering;
      if (updates.firstMessage !== undefined) dbUpdates.information_gathering = updates.firstMessage;
      if (updates.vapiInstructions !== undefined) dbUpdates.vapi_instructions = updates.vapiInstructions;
      if (updates.vapiContext !== undefined) dbUpdates.vapi_context = updates.vapiContext;

      // Voice fields
      if (updates.voiceProvider !== undefined) dbUpdates.voice_provider = updates.voiceProvider;
      if (updates.voiceId !== undefined) dbUpdates.voice_id = updates.voiceId;

      // Other fields
      if (updates.aiModel !== undefined) dbUpdates.ai_model = updates.aiModel;
      if (updates.officeAddress !== undefined) dbUpdates.office_address = updates.officeAddress;
      if (updates.schedulingLink !== undefined) dbUpdates.scheduling_link = updates.schedulingLink;
      if (updates.customFields !== undefined) dbUpdates.custom_fields = updates.customFields;
      if (updates.assistantImageUrl !== undefined) dbUpdates.assistant_image_url = updates.assistantImageUrl;

      // Check for base64 images and prevent database timeouts
      const base64Fields = ['logo_url', 'assistant_image_url', 'mascot_url'];
      for (const field of base64Fields) {
        if (dbUpdates[field] && dbUpdates[field].startsWith('data:')) {
          const base64Size = dbUpdates[field].length;
          console.warn(`⚠️ [AssistantConfigService] Base64 image in ${field} is ${base64Size} bytes. Use uploadImage() instead.`);
          console.error(`❌ [AssistantConfigService] Removing data URL in ${field} to prevent statement timeout.`);
          dbUpdates[field] = null;
        }
      }

      // Always update the timestamp
      dbUpdates.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .upsert({
          assistant_id: assistantId,
          attorney_id: attorneyId,
          ...dbUpdates
        }, {
          onConflict: 'assistant_id,attorney_id'
        })
        .select()
        .single();

      if (error) throw error;

      console.log(`[AssistantConfigService] Successfully updated config:`, data);
      return data;
    } catch (error) {
      console.error(`[AssistantConfigService] Error updating config:`, error);
      throw error;
    }
  }

  /**
   * Get default configuration for a new assistant
   */
  static getDefaultConfig() {
    return {
      firmName: 'Your Law Firm',
      titleText: '',
      primaryColor: '#4B74AA',
      secondaryColor: '#2C3E50',
      buttonColor: '#D85722',
      backgroundColor: '#1a1a1a',
      backgroundOpacity: 0.9,
      buttonOpacity: 1,
      practiceAreaBackgroundOpacity: 0.1,
      textBackgroundColor: '#634C38',
      logoUrl: '/PRIMARY CLEAR.png',
      practiceDescription: 'Your AI legal assistant is ready to help',
      practiceAreas: [],
      welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
      informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",
      vapiInstructions: '',
      vapiContext: '',
      voiceId: 'sarah',
      voiceProvider: 'playht',
      aiModel: 'gpt-4o'
    };
  }

  /**
   * Initialize configuration for a new assistant with defaults
   */
  static async initializeAssistantConfig(assistantId, attorneyId, overrides = {}) {
    const defaultConfig = this.getDefaultConfig();

    // Merge with any overrides
    const config = { ...defaultConfig, ...overrides };

    try {
      await this.saveAssistantConfig(assistantId, attorneyId, config);
      console.log('[AssistantConfigService] Successfully initialized assistant config');
      return config;
    } catch (error) {
      console.error('[AssistantConfigService] Error initializing assistant config:', error);
      throw error;
    }
  }

  /**
   * Delete all configuration for an assistant
   */
  static async deleteAssistantConfig(assistantId, attorneyId) {
    try {
      const { error } = await supabase
        .from('assistant_ui_configs')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId);

      if (error) throw error;

      console.log('[AssistantConfigService] Successfully deleted assistant config');
    } catch (error) {
      console.error('[AssistantConfigService] Error deleting assistant config:', error);
      throw error;
    }
  }
}

export default AssistantConfigService;
