-- Create call_records table
CREATE TABLE IF NOT EXISTS call_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  call_id TEXT NOT NULL UNIQUE,
  assistant_id TEXT NOT NULL,
  attorney_id UUID REFERENCES attorneys(id),
  customer_phone TEXT,
  status TEXT,
  duration INTEGER,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  transcripts JSONB DEFAULT '[]'::JSON<PERSON>,
  messages JSONB DEFAULT '[]'::JSON<PERSON>,
  tool_executions JSONB DEFAULT '[]'::JSON<PERSON>,
  metadata JSONB DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE call_records ENABLE ROW LEVEL SECURITY;

-- Policy for attorneys to see their own calls
CREATE POLICY attorney_select_own_calls ON call_records
  FOR SELECT
  USING (auth.uid() = attorney_id);

-- Policy for attorneys to update their own calls
CREATE POLICY attorney_update_own_calls ON call_records
  FOR UPDATE
  USING (auth.uid() = attorney_id);

-- Policy for service role to do everything
CREATE POLICY service_role_all ON call_records
  FOR ALL
  USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'service_role');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_call_records_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_call_records_updated_at
BEFORE UPDATE ON call_records
FOR EACH ROW
EXECUTE FUNCTION update_call_records_updated_at();

-- Create index on attorney_id for faster queries
CREATE INDEX IF NOT EXISTS call_records_attorney_id_idx ON call_records(attorney_id);

-- Create index on call_id for faster queries
CREATE INDEX IF NOT EXISTS call_records_call_id_idx ON call_records(call_id);

-- Create index on status for faster queries
CREATE INDEX IF NOT EXISTS call_records_status_idx ON call_records(status);

-- Create index on start_time for faster queries
CREATE INDEX IF NOT EXISTS call_records_start_time_idx ON call_records(start_time);
