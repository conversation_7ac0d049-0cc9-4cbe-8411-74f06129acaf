import React, { useState, useEffect, useRef } from 'react';
import './CallTransition.css';

const CallTransition = ({ isActive, onTransitionComplete, buttonPosition, mascotUrl }) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [showButtonClone, setShowButtonClone] = useState(false);
  const [showExpandingCircle, setShowExpandingCircle] = useState(false);
  const [showConnectionText, setShowConnectionText] = useState(false);
  const [showLoadingDots, setShowLoadingDots] = useState(false);
  const [showNeuralNetwork, setShowNeuralNetwork] = useState(false);
  const [showStatusIndicator, setShowStatusIndicator] = useState(false);
  const [buttonCloneStyle, setButtonCloneStyle] = useState({});
  const [particles, setParticles] = useState([]);
  const [nodes, setNodes] = useState([]);
  const [connections, setConnections] = useState([]);

  const overlayRef = useRef(null);
  const neuralNetworkRef = useRef(null);

  // Initialize the transition when isActive changes to true
  useEffect(() => {
    if (isActive) {
      startTransition();
    } else {
      resetTransition();
    }
  }, [isActive]);

  // Start the transition sequence
  const startTransition = () => {
    // Show overlay
    setShowOverlay(true);

    // Sequence of animations - removed button clone animation
    setTimeout(() => setShowExpandingCircle(true), 100);
    setTimeout(() => createParticles(), 300);
    setTimeout(() => setShowConnectionText(true), 600);
    setTimeout(() => setShowLoadingDots(true), 800);
    setTimeout(() => {
      setShowNeuralNetwork(true);
      createNeuralNetwork();
    }, 1000);
    setTimeout(() => setShowStatusIndicator(true), 1300);

    // Complete transition
    setTimeout(() => {
      if (onTransitionComplete) {
        onTransitionComplete();
      }
    }, 2300);
  };

  // Reset all transition states
  const resetTransition = () => {
    setShowOverlay(false);
    setShowButtonClone(false);
    setShowExpandingCircle(false);
    setShowConnectionText(false);
    setShowLoadingDots(false);
    setShowNeuralNetwork(false);
    setShowStatusIndicator(false);
    setParticles([]);
    setNodes([]);
    setConnections([]);
  };

  // Create particle effects
  const createParticles = () => {
    const newParticles = [];
    const particleCount = 30;

    for (let i = 0; i < particleCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = 100 + Math.random() * 150;
      const duration = 1 + Math.random() * 2;
      const delay = Math.random() * 0.5;
      const size = 2 + Math.random() * 4;

      const particle = {
        id: i,
        style: {
          top: '50%',
          left: '50%',
          width: `${size}px`,
          height: `${size}px`,
          transform: `translate(-50%, -50%) translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px)`,
          opacity: 0,
          animation: `particleMove ${duration}s ease-out forwards`,
          animationDelay: `${delay}s`
        }
      };

      newParticles.push(particle);
    }

    setParticles(newParticles);
  };

  // Create neural network visualization
  const createNeuralNetwork = () => {
    if (!neuralNetworkRef.current) return;

    const container = neuralNetworkRef.current;
    const { width, height } = container.getBoundingClientRect();

    // Create nodes in a more structured pattern
    const nodeCount = 25; // Increased node count
    const newNodes = [];
    const centerX = width / 2;
    const centerY = height / 2;

    // Create nodes in a circular pattern around the center
    for (let i = 0; i < nodeCount; i++) {
      // Create a more structured pattern with some randomness
      const angle = (i / nodeCount) * Math.PI * 2 + (Math.random() * 0.5 - 0.25);
      const distance = 50 + Math.random() * (Math.min(width, height) * 0.35);
      const x = centerX + Math.cos(angle) * distance;
      const y = centerY + Math.sin(angle) * distance;

      // Vary the size more for visual interest
      const size = 3 + Math.random() * 6;
      const pulseDelay = Math.random() * 3;
      const pulseDuration = 1.5 + Math.random() * 2;

      newNodes.push({
        id: i,
        x,
        y,
        size,
        style: {
          top: `${y}px`,
          left: `${x}px`,
          width: `${size}px`,
          height: `${size}px`,
          animationDelay: `${pulseDelay}s`,
          animationDuration: `${pulseDuration}s`,
          boxShadow: `0 0 ${size * 2}px rgba(41, 121, 255, 0.8)`,
          background: `radial-gradient(circle, rgba(41, 121, 255, 1) 0%, rgba(41, 121, 255, 0.7) 70%, rgba(41, 121, 255, 0) 100%)`
        }
      });
    }

    setNodes(newNodes);

    // Create more meaningful connections between nodes
    const newConnections = [];

    // Connect each node to its 2-3 nearest neighbors
    for (let i = 0; i < newNodes.length; i++) {
      const startNode = newNodes[i];

      // Calculate distances to all other nodes
      const distances = newNodes
        .filter(node => node.id !== startNode.id)
        .map(node => {
          const dx = node.x - startNode.x;
          const dy = node.y - startNode.y;
          return {
            node,
            distance: Math.sqrt(dx * dx + dy * dy)
          };
        })
        .sort((a, b) => a.distance - b.distance);

      // Connect to 2-3 closest nodes
      const connectCount = 2 + Math.floor(Math.random() * 2);
      for (let j = 0; j < Math.min(connectCount, distances.length); j++) {
        const endNode = distances[j].node;
        const dx = endNode.x - startNode.x;
        const dy = endNode.y - startNode.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * (180 / Math.PI);

        // Add some variation to connection appearance
        const opacity = 0.3 + Math.random() * 0.4;
        const animDelay = Math.random() * 2;
        const animDuration = 1 + Math.random() * 3;

        newConnections.push({
          id: `${startNode.id}-${endNode.id}`,
          style: {
            top: `${startNode.y + startNode.size / 2}px`,
            left: `${startNode.x + startNode.size / 2}px`,
            width: `${distance}px`,
            transform: `rotate(${angle}deg)`,
            opacity: opacity,
            animationDelay: `${animDelay}s`,
            animationDuration: `${animDuration}s`,
            height: '1px', // Thinner lines
            background: `linear-gradient(90deg, rgba(41, 121, 255, ${opacity}) 0%, rgba(41, 121, 255, ${opacity * 0.5}) 100%)`
          }
        });
      }
    }

    setConnections(newConnections);
  };

  return (
    <div
      ref={overlayRef}
      className={`call-transition-overlay ${showOverlay ? 'active' : ''}`}
    >
      {/* Removed button clone with image to prevent warping */}

      {/* Expanding circle animation */}
      {showExpandingCircle && (
        <div className={`expanding-circle ${showExpandingCircle ? 'active' : ''}`}></div>
      )}

      {/* Particles animation */}
      <div className="particles-container">
        {particles.map(particle => (
          <div
            key={`particle-${particle.id}`}
            className="particle"
            style={particle.style}
          ></div>
        ))}
      </div>

      {/* Neural network visualization */}
      <div
        ref={neuralNetworkRef}
        className={`neural-network ${showNeuralNetwork ? 'active' : ''}`}
      >
        {nodes.map(node => (
          <div
            key={`node-${node.id}`}
            className="node pulse"
            style={node.style}
          ></div>
        ))}

        {connections.map(connection => (
          <div
            key={`connection-${connection.id}`}
            className="connection-line"
            style={connection.style}
          ></div>
        ))}
      </div>

      {/* Connection text with integrated loading dots */}
      <div className={`connection-text ${showConnectionText ? 'active' : ''}`}>
        Connecting to LegalScout AI
        <span className={`loading-dots-inline ${showLoadingDots ? 'active' : ''}`}>
          <span className="dot">.</span>
          <span className="dot">.</span>
          <span className="dot">.</span>
        </span>
      </div>

      {/* Status indicator */}
      <div className={`status-indicator ${showStatusIndicator ? 'active' : ''}`}>
        <div className="status-icon"></div>
        <div className="status-text">Establishing secure connection</div>
      </div>
    </div>
  );
};

export default CallTransition;
