export const safeVapiConfig = (baseConfig, overrides) => {
  const safeOverrides = Object.entries(overrides).reduce((acc, [key, value]) => {
    // Preserve original types for critical properties
    if (key === 'voice' && typeof value === 'object') {
      acc[key] = {
        provider: typeof value.provider === 'string' ? value.provider : baseConfig.voice.provider,
        voiceId: typeof value.voiceId === 'string' ? value.voiceId : baseConfig.voice.voiceId,
        speed: typeof value.speed === 'number' ? value.speed : baseConfig.voice.speed
      };
    } else if (value !== null && value !== undefined) {
      acc[key] = typeof value === typeof baseConfig[key] ? value : baseConfig[key];
    }
    return acc;
  }, {});

  return {
    ...baseConfig,
    ...safeOverrides
  };
}; 