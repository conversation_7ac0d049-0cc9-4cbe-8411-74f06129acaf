/**
 * Vapi Network Interceptor
 * 
 * This module intercepts network requests to Vapi API endpoints
 * and logs them for debugging purposes.
 */

import { logNetwork } from './vapiMcpDebugger';

// Original fetch function
const originalFetch = window.fetch;

/**
 * Install the network interceptor
 */
export const installVapiNetworkInterceptor = () => {
  if (typeof window === 'undefined') return;

  // Check if disabled by clean auth solution
  if (window.__VAPI_NETWORK_INTERCEPTOR_DISABLED) {
    console.log('[Vapi Network Interceptor] 🚫 Disabled by clean auth solution');
    return;
  }

  // Check if already installed
  if (window.vapiNetworkInterceptorInstalled) return;
  
  // Override fetch
  window.fetch = async function(url, options = {}) {
    // Check if this is a Vapi API request
    const isVapiRequest = typeof url === 'string' && (
      url.includes('api.vapi.ai') || 
      url.includes('public.vapi.ai') ||
      url.includes('vapi-mcp-server') ||
      url.includes('vapi-mcp')
    );
    
    if (!isVapiRequest) {
      // Not a Vapi request, use original fetch
      return originalFetch.apply(this, arguments);
    }
    
    // Log request
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;
    
    logNetwork('request', url, method, null, headers, body);
    
    try {
      // Make the request
      const response = await originalFetch.apply(this, arguments);
      
      // Clone the response to avoid consuming it
      const clonedResponse = response.clone();
      
      // Try to parse the response body
      let responseBody;
      try {
        responseBody = await clonedResponse.text();
        
        // Try to parse as JSON
        try {
          responseBody = JSON.parse(responseBody);
        } catch (e) {
          // Not JSON, keep as text
        }
      } catch (e) {
        responseBody = 'Unable to read response body';
      }
      
      // Log response
      logNetwork(
        'response',
        url,
        method,
        response.status,
        Object.fromEntries([...response.headers.entries()]),
        responseBody
      );
      
      return response;
    } catch (error) {
      // Log error
      logNetwork(
        'response',
        url,
        method,
        0,
        {},
        { error: error.message }
      );
      
      throw error;
    }
  };
  
  // Mark as installed
  window.vapiNetworkInterceptorInstalled = true;
  
  console.log('[Vapi Network Interceptor] Installed');
};

/**
 * Uninstall the network interceptor
 */
export const uninstallVapiNetworkInterceptor = () => {
  if (typeof window === 'undefined') return;
  
  // Check if installed
  if (!window.vapiNetworkInterceptorInstalled) return;
  
  // Restore original fetch
  window.fetch = originalFetch;
  
  // Mark as uninstalled
  window.vapiNetworkInterceptorInstalled = false;
  
  console.log('[Vapi Network Interceptor] Uninstalled');
};

// Export default
export default {
  installVapiNetworkInterceptor,
  uninstallVapiNetworkInterceptor
};
