#!/usr/bin/env node

/**
 * Simple Git Analysis Test
 */

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔍 TESTING GIT ANALYSIS...');

try {
  // Test basic Git functionality
  console.log('📋 Testing Git commands...');
  
  const gitVersion = execSync('git --version', { encoding: 'utf8' }).trim();
  console.log('✅ Git version:', gitVersion);
  
  // Test current repo status
  try {
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    console.log('✅ Current branch:', currentBranch);
  } catch (e) {
    console.log('⚠️  Not in a Git repository or no commits');
  }
  
  // Test cloning capability
  console.log('📥 Testing clone capability...');
  
  const testRepo = 'https://github.com/damonkost/LegalScout.git';
  const tempDir = '.temp-test-clone';
  
  // Clean up any existing temp directory
  if (fs.existsSync(tempDir)) {
    execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
  }
  
  console.log(`🌐 Attempting to clone: ${testRepo}`);
  
  // Try shallow clone
  execSync(`git clone --depth 1 "${testRepo}" "${tempDir}"`, { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('✅ Clone successful!');
  
  // Check what's in the cloned repo
  const files = fs.readdirSync(tempDir);
  console.log('📁 Cloned repo contents:', files.slice(0, 10));
  
  // Check for key directories
  const srcExists = fs.existsSync(`${tempDir}/src`);
  const packageExists = fs.existsSync(`${tempDir}/package.json`);
  
  console.log('📦 Has src directory:', srcExists);
  console.log('📄 Has package.json:', packageExists);
  
  if (srcExists) {
    const srcContents = fs.readdirSync(`${tempDir}/src`);
    console.log('📂 src/ contents:', srcContents);
  }
  
  // Cleanup
  execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
  console.log('🧹 Cleanup complete');
  
  console.log('\n✅ ALL TESTS PASSED!');
  console.log('🎯 Git analysis utility should work correctly');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  
  // Try to cleanup on error
  try {
    if (fs.existsSync('.temp-test-clone')) {
      execSync('rmdir /s /q ".temp-test-clone"', { stdio: 'pipe' });
    }
  } catch (cleanupError) {
    console.log('⚠️  Cleanup error (ignore):', cleanupError.message);
  }
}
