/**
 * Enhanced Vapi Assistant Manager Service
 *
 * This service provides robust management of Vapi assistants for attorneys,
 * ensuring consistent creation, updates, and synchronization between Supabase and Vapi.
 *
 * It follows the one-way sync pattern (UI → Supabase → Vapi) and uses Supabase
 * as the primary source of truth.
 *
 * Enhancements:
 * - Improved error handling and logging
 * - Explicit field mapping between Supabase and Vapi
 * - Atomic updates for related fields
 * - Validation before saving to either system
 * - Retry logic for failed API calls
 */

import { supabase } from '../lib/supabase';
import { vapiMcpService } from './vapiMcpService';
import { mcpConfig } from '../config/mcp.config';
import {
  DEFAULT_STRUCTURED_DATA_SCHEMA,
  DEFAULT_SUCCESS_EVALUATION_PROMPT,
  DEFAULT_SUMMARY_PROMPT,
  DEFAULT_STRUCTURED_DATA_PROMPT
} from '../config/defaultTemplates';
import { generateStructuredDataSchema } from '../utils/schemaGenerator';

// Field mapping between Supabase and Vapi
const FIELD_MAPPING = {
  // Supabase field → Vapi field
  welcome_message: 'firstMessage',
  vapi_instructions: 'instructions',
  voice_id: 'voice.voiceId',
  voice_provider: 'voice.provider',
  ai_model: 'llm.model'
};

class EnhancedVapiAssistantManager {
  constructor() {
    this.mcpService = vapiMcpService;
    this.initialized = false;
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
  }

  /**
   * Initialize the Vapi Assistant Manager
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - Whether initialization was successful
   */
  async initialize(options = {}) {
    try {
      if (this.initialized) {
        return true;
      }

      console.log('[EnhancedVapiAssistantManager] Initializing');

      // Get API key from options or environment
      const apiKey = options.apiKey ||
                    import.meta.env.VITE_VAPI_PUBLIC_KEY ||
                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

      // Connect to Vapi MCP service
      await this.mcpService.connect(apiKey, options.forceDirect);

      this.initialized = true;
      console.log('[EnhancedVapiAssistantManager] Initialized with VapiMcpService');
      return true;
    } catch (error) {
      console.error('[EnhancedVapiAssistantManager] Initialization error:', error);
      return false;
    }
  }

  /**
   * Ensure an attorney has a valid Vapi assistant ID
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The updated attorney object
   */
  async ensureAssistant(attorney) {
    if (!attorney) {
      throw new Error('Attorney object is required');
    }

    try {
      console.log(`[EnhancedVapiAssistantManager] Ensuring assistant for attorney: ${attorney.id}`);

      // Check if attorney already has a valid assistant ID
      if (attorney.vapi_assistant_id) {
        // Verify the assistant exists in Vapi
        const assistantExists = await this.verifyAssistant(attorney.vapi_assistant_id);

        if (assistantExists) {
          console.log(`[EnhancedVapiAssistantManager] Assistant ${attorney.vapi_assistant_id} verified`);
          return attorney;
        }

        console.log(`[EnhancedVapiAssistantManager] Assistant ${attorney.vapi_assistant_id} not found in Vapi, will create new one`);
      }

      // Create a new assistant
      const newAssistant = await this.createAssistant(attorney);

      // Update attorney record with new assistant ID
      const updatedAttorney = await this.updateAttorneyAssistantId(attorney.id, newAssistant.id);

      console.log(`[EnhancedVapiAssistantManager] Created and linked new assistant ${newAssistant.id} for attorney ${attorney.id}`);
      return updatedAttorney;
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error ensuring assistant for attorney ${attorney.id}:`, error);
      throw error;
    }
  }

  /**
   * Verify that a Vapi assistant exists
   * @param {string} assistantId - The Vapi assistant ID to verify
   * @returns {Promise<boolean>} - Whether the assistant exists
   */
  async verifyAssistant(assistantId) {
    try {
      // Validate assistant ID format first
      if (!assistantId || typeof assistantId !== 'string') {
        console.warn(`[EnhancedVapiAssistantManager] Invalid assistant ID: ${assistantId}`);
        return false;
      }

      // Check for mock IDs
      if (assistantId.includes('mock') || assistantId.includes('MOCK') || assistantId.includes('DO-NOT-SAVE')) {
        console.warn(`[EnhancedVapiAssistantManager] Mock assistant ID detected: ${assistantId}`);
        return false;
      }

      // Check for API key confusion
      if (assistantId === '310f0d43-27c2-47a5-a76d-e55171d024f7') {
        console.error(`[EnhancedVapiAssistantManager] API key stored as assistant ID: ${assistantId}`);
        return false;
      }

      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      return await this.withRetry(async () => {
        try {
          const assistant = await this.mcpService.getAssistant(assistantId);
          return !!assistant;
        } catch (error) {
          if (error.message.includes('not found') || error.message.includes('404')) {
            console.log(`[EnhancedVapiAssistantManager] Assistant ${assistantId} not found in Vapi`);
            return false;
          }
          throw error;
        }
      });
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error verifying assistant ${assistantId}:`, error);
      return false;
    }
  }

  /**
   * Create a new Vapi assistant for an attorney
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The created assistant
   */
  async createAssistant(attorney) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Validate attorney data before creating assistant
      this.validateAttorneyData(attorney);

      // CRITICAL: Check if attorney already has a valid assistant before creating
      if (attorney.vapi_assistant_id) {
        const exists = await this.verifyAssistant(attorney.vapi_assistant_id);
        if (exists) {
          console.log(`[EnhancedVapiAssistantManager] Attorney ${attorney.id} already has valid assistant ${attorney.vapi_assistant_id}, skipping creation`);
          return await this.mcpService.getAssistant(attorney.vapi_assistant_id);
        }
      }

      // Check for existing assistants with the same name to prevent duplicates
      const existingAssistants = await this.findExistingAssistantsByName(attorney.firm_name);
      if (existingAssistants.length > 0) {
        console.log(`[EnhancedVapiAssistantManager] Found ${existingAssistants.length} existing assistants with name "${attorney.firm_name}"`);

        // Try to reuse an existing assistant if it's not linked to another attorney
        for (const existingAssistant of existingAssistants) {
          const isLinked = await this.isAssistantLinkedToAttorney(existingAssistant.id);
          if (!isLinked) {
            console.log(`[EnhancedVapiAssistantManager] Reusing unlinked assistant ${existingAssistant.id} for attorney ${attorney.id}`);
            return existingAssistant;
          }
        }
      }

      // Create assistant configuration from attorney data
      const assistantConfig = this.createAssistantConfig(attorney);

      console.log(`[EnhancedVapiAssistantManager] Creating new assistant for attorney ${attorney.id} with name "${assistantConfig.name}"`);

      return await this.withRetry(async () => {
        return await this.mcpService.createAssistant(assistantConfig);
      });
    } catch (error) {
      console.error('[EnhancedVapiAssistantManager] Error creating assistant:', error);
      throw error;
    }
  }

  /**
   * Update an attorney's Vapi assistant ID in Supabase
   * @param {string} attorneyId - The attorney ID
   * @param {string} assistantId - The Vapi assistant ID
   * @returns {Promise<Object>} - The updated attorney object
   */
  async updateAttorneyAssistantId(attorneyId, assistantId) {
    // CRITICAL: Prevent mock assistant IDs from being saved to database
    if (assistantId.includes('mock') || assistantId.includes('MOCK') || assistantId.includes('DO-NOT-SAVE')) {
      console.error(`[EnhancedVapiAssistantManager] BLOCKED: Attempted to save mock assistant ID to database: ${assistantId}`);
      console.error('This is a critical error - mock IDs should NEVER be saved to the database');
      throw new Error('Mock assistant IDs cannot be saved to database');
    }

    // Validate that assistant ID looks like a real UUID (not a mock)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(assistantId)) {
      console.error(`[EnhancedVapiAssistantManager] Invalid assistant ID format - must be a valid UUID: ${assistantId}`);
      throw new Error('Assistant ID must be a valid UUID');
    }

    try {
      const { data, error } = await supabase
        .from('attorneys')
        .update({
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error updating attorney ${attorneyId} with assistant ID ${assistantId}:`, error);
      throw error;
    }
  }

  /**
   * Create an assistant configuration from attorney data
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Object} - The assistant configuration
   */
  createAssistantConfig(attorney) {
    // Generate structured data schema from custom fields if available
    let structuredDataSchema = null;
    if (attorney.custom_fields) {
      try {
        const customFields = typeof attorney.custom_fields === 'string'
          ? JSON.parse(attorney.custom_fields)
          : attorney.custom_fields;
        structuredDataSchema = generateStructuredDataSchema(customFields);
      } catch (error) {
        console.error('Error generating schema from custom fields:', error);
      }
    }

    return {
      name: attorney.firm_name || 'Legal Assistant',
      firstMessage: attorney.welcome_message || 'Hello, how can I help you with your legal needs today?',
      instructions: attorney.vapi_instructions || 'You are a legal assistant helping potential clients understand their legal needs.',
      voice: {
        provider: attorney.voice_provider || mcpConfig.voice.vapi.defaultProvider || '11labs',
        voiceId: attorney.voice_id || mcpConfig.voice.vapi.defaultVoice || 'sarah'
      },
      llm: {
        provider: "openai",
        model: attorney.ai_model || mcpConfig.assistant.defaultModel || "gpt-4o"
      },
      transcriber: {
        provider: mcpConfig.assistant.defaultTranscriber?.provider || "deepgram",
        model: mcpConfig.assistant.defaultTranscriber?.model || "nova-3"
      },
      // Add analysis plan configuration for advanced settings (correct Vapi format)
      analysisPlan: {
        // Summary configuration
        summaryPrompt: attorney.summary_prompt || DEFAULT_SUMMARY_PROMPT,
        // Success evaluation configuration
        successEvaluationPrompt: attorney.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
        // Structured data configuration
        structuredDataPrompt: attorney.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
        structuredDataSchema: attorney.structured_data_schema || structuredDataSchema || DEFAULT_STRUCTURED_DATA_SCHEMA
      },
      // Add server URL configuration for webhook integration
      serverUrl: this.generateWebhookUrl(attorney.subdomain),
      serverUrlSecret: process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret'
    };
  }

  /**
   * Generate attorney-specific webhook URL
   * @param {string} subdomain - Attorney subdomain
   * @returns {string} Webhook URL for the attorney
   */
  generateWebhookUrl(subdomain) {
    if (!subdomain) {
      // Fallback to generic webhook URL if no subdomain
      return process.env.VITE_APP_URL ? `${process.env.VITE_APP_URL}/api/vapi-webhook-direct` : 'https://legalscout.net/api/vapi-webhook-direct';
    }

    // Generate attorney-specific webhook URL
    const baseDomain = process.env.VITE_BASE_DOMAIN || 'legalscout.net';
    return `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
  }

  /**
   * Sync an attorney's Vapi assistant with the latest data from Supabase
   * @param {Object} attorney - The attorney object from Supabase
   * @returns {Promise<Object>} - The sync result
   */
  async syncAssistant(attorney) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      if (!attorney.vapi_assistant_id) {
        return await this.ensureAssistant(attorney);
      }

      // Validate attorney data before updating assistant
      this.validateAttorneyData(attorney);

      // Update the assistant with the latest data
      const assistantConfig = this.createAssistantConfig(attorney);

      return await this.withRetry(async () => {
        return await this.mcpService.updateAssistant(attorney.vapi_assistant_id, assistantConfig);
      });
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error syncing assistant for attorney ${attorney.id}:`, error);
      throw error;
    }
  }

  /**
   * Validate attorney data before creating or updating an assistant
   * @param {Object} attorney - The attorney object from Supabase
   * @throws {Error} - If validation fails
   */
  validateAttorneyData(attorney) {
    if (!attorney) {
      throw new Error('Attorney object is required');
    }

    if (!attorney.firm_name) {
      throw new Error('Attorney must have a firm name');
    }

    // Validate voice provider and voice ID
    if (attorney.voice_provider) {
      const validProviders = ['11labs', 'playht', 'openai', 'deepgram'];
      if (!validProviders.includes(attorney.voice_provider)) {
        throw new Error(`Invalid voice provider: ${attorney.voice_provider}`);
      }
    }
  }

  /**
   * Find existing assistants by name to prevent duplicates
   * @param {string} name - The assistant name to search for
   * @returns {Promise<Array>} - Array of matching assistants
   */
  async findExistingAssistantsByName(name) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      const assistants = await this.mcpService.listAssistants();
      return assistants.filter(assistant => assistant.name === name);
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error finding assistants by name ${name}:`, error);
      return [];
    }
  }

  /**
   * Check if an assistant is already linked to an attorney in the database
   * @param {string} assistantId - The Vapi assistant ID
   * @returns {Promise<boolean>} - Whether the assistant is linked
   */
  async isAssistantLinkedToAttorney(assistantId) {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('id')
        .eq('vapi_assistant_id', assistantId)
        .limit(1);

      if (error) {
        console.error(`[EnhancedVapiAssistantManager] Error checking assistant linkage:`, error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error checking assistant linkage:`, error);
      return false;
    }
  }

  /**
   * Clean up orphaned assistants and invalid database entries
   * @returns {Promise<Object>} - Cleanup results
   */
  async cleanupOrphanedAssistants() {
    try {
      console.log('[EnhancedVapiAssistantManager] Starting cleanup of orphaned assistants');

      // Get all assistants from Vapi
      const vapiAssistants = await this.mcpService.listAssistants();
      console.log(`[EnhancedVapiAssistantManager] Found ${vapiAssistants.length} assistants in Vapi`);

      // Get all attorney records from database
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('id, subdomain, firm_name, vapi_assistant_id');

      if (error) {
        throw error;
      }

      const results = {
        orphanedAssistants: [],
        invalidDatabaseEntries: [],
        cleanedUp: 0,
        errors: []
      };

      // Find orphaned assistants (in Vapi but not linked to any attorney)
      for (const assistant of vapiAssistants) {
        const linkedAttorney = attorneys.find(att => att.vapi_assistant_id === assistant.id);
        if (!linkedAttorney) {
          results.orphanedAssistants.push(assistant);
        }
      }

      // Find invalid database entries (assistant IDs that don't exist in Vapi)
      for (const attorney of attorneys) {
        if (attorney.vapi_assistant_id) {
          const vapiAssistant = vapiAssistants.find(ass => ass.id === attorney.vapi_assistant_id);
          if (!vapiAssistant) {
            results.invalidDatabaseEntries.push(attorney);
          }
        }
      }

      console.log(`[EnhancedVapiAssistantManager] Found ${results.orphanedAssistants.length} orphaned assistants and ${results.invalidDatabaseEntries.length} invalid database entries`);

      return results;
    } catch (error) {
      console.error('[EnhancedVapiAssistantManager] Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Find existing assistants by name to prevent duplicates
   * @param {string} name - The assistant name to search for
   * @returns {Promise<Array>} - Array of matching assistants
   */
  async findExistingAssistantsByName(name) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      const assistants = await this.mcpService.listAssistants();
      return assistants.filter(assistant => assistant.name === name);
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error finding assistants by name ${name}:`, error);
      return [];
    }
  }

  /**
   * Check if an assistant is already linked to an attorney in the database
   * @param {string} assistantId - The Vapi assistant ID
   * @returns {Promise<boolean>} - Whether the assistant is linked
   */
  async isAssistantLinkedToAttorney(assistantId) {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('id')
        .eq('vapi_assistant_id', assistantId)
        .limit(1);

      if (error) {
        console.error(`[EnhancedVapiAssistantManager] Error checking assistant linkage:`, error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error(`[EnhancedVapiAssistantManager] Error checking assistant linkage:`, error);
      return false;
    }
  }

  /**
   * Utility function to retry an operation with exponential backoff
   * @param {Function} operation - The operation to retry
   * @returns {Promise<any>} - The result of the operation
   */
  async withRetry(operation) {
    let lastError;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`[EnhancedVapiAssistantManager] Operation failed (attempt ${attempt}/${this.maxRetries}):`, error);
        lastError = error;

        if (attempt < this.maxRetries) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Clean up orphaned assistants and invalid database entries
   * @returns {Promise<Object>} - Cleanup results
   */
  async cleanupOrphanedAssistants() {
    try {
      console.log('[EnhancedVapiAssistantManager] Starting cleanup of orphaned assistants');

      // Get all assistants from Vapi
      const vapiAssistants = await this.mcpService.listAssistants();
      console.log(`[EnhancedVapiAssistantManager] Found ${vapiAssistants.length} assistants in Vapi`);

      // Get all attorney records from database
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('id, subdomain, firm_name, vapi_assistant_id');

      if (error) {
        throw error;
      }

      const results = {
        orphanedAssistants: [],
        invalidDatabaseEntries: [],
        cleanedUp: 0,
        errors: []
      };

      // Find orphaned assistants (in Vapi but not linked to any attorney)
      for (const assistant of vapiAssistants) {
        const linkedAttorney = attorneys.find(att => att.vapi_assistant_id === assistant.id);
        if (!linkedAttorney) {
          results.orphanedAssistants.push(assistant);
        }
      }

      // Find invalid database entries (assistant IDs that don't exist in Vapi)
      for (const attorney of attorneys) {
        if (attorney.vapi_assistant_id) {
          const vapiAssistant = vapiAssistants.find(ass => ass.id === attorney.vapi_assistant_id);
          if (!vapiAssistant) {
            results.invalidDatabaseEntries.push(attorney);
          }
        }
      }

      console.log(`[EnhancedVapiAssistantManager] Found ${results.orphanedAssistants.length} orphaned assistants and ${results.invalidDatabaseEntries.length} invalid database entries`);

      return results;
    } catch (error) {
      console.error('[EnhancedVapiAssistantManager] Error during cleanup:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const enhancedVapiAssistantManager = new EnhancedVapiAssistantManager();
