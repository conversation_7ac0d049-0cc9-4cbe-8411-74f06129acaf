-- Create attorney_assistants table to map attorneys to Vapi assistants
CREATE TABLE IF NOT EXISTS attorney_assistants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(attorney_id, assistant_id)
);

-- Create call_logs table
CREATE TABLE IF NOT EXISTS call_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  call_id TEXT NOT NULL,
  assistant_id TEXT NOT NULL,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  customer_phone TEXT,
  duration INTEGER,
  status TEXT,
  transcript TEXT,
  summary TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(call_id)
);

-- Enable Row Level Security
ALTER TABLE attorney_assistants ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for attorney_assistants
CREATE POLICY "Users can view their own attorney assistants"
  ON attorney_assistants
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own attorney assistants"
  ON attorney_assistants
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own attorney assistants"
  ON attorney_assistants
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own attorney assistants"
  ON attorney_assistants
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for call_logs
CREATE POLICY "Users can view call logs for their attorneys"
  ON call_logs
  FOR SELECT
  USING (
    attorney_id IN (
      SELECT attorneys.id
      FROM attorneys
      WHERE attorneys.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert call logs for their attorneys"
  ON call_logs
  FOR INSERT
  WITH CHECK (
    attorney_id IN (
      SELECT attorneys.id
      FROM attorneys
      WHERE attorneys.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update call logs for their attorneys"
  ON call_logs
  FOR UPDATE
  USING (
    attorney_id IN (
      SELECT attorneys.id
      FROM attorneys
      WHERE attorneys.user_id = auth.uid()
    )
  )
  WITH CHECK (
    attorney_id IN (
      SELECT attorneys.id
      FROM attorneys
      WHERE attorneys.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete call logs for their attorneys"
  ON call_logs
  FOR DELETE
  USING (
    attorney_id IN (
      SELECT attorneys.id
      FROM attorneys
      WHERE attorneys.user_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attorney_assistants_attorney_id ON attorney_assistants(attorney_id);
CREATE INDEX IF NOT EXISTS idx_attorney_assistants_user_id ON attorney_assistants(user_id);
CREATE INDEX IF NOT EXISTS idx_attorney_assistants_assistant_id ON attorney_assistants(assistant_id);

CREATE INDEX IF NOT EXISTS idx_call_logs_attorney_id ON call_logs(attorney_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_assistant_id ON call_logs(assistant_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_call_id ON call_logs(call_id);
