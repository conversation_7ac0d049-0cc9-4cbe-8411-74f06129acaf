/**
 * Enhanced Vapi Service
 *
 * This service provides a comprehensive interface for interacting with Vapi's voice AI services.
 * It handles all Vapi interactions through the MCP server, supporting both SSE and Streamable HTTP.
 *
 * Features:
 * - Call creation and management
 * - Assistant management
 * - SMS notifications
 * - Call control token generation
 */

import { enhancedVapiMcpService } from './EnhancedVapiMcpService';
import { mcpConfig } from '../config/mcp.config';
import { createCallControlTokenSync, verifySecureToken } from '../utils/tokenUtils';
import { supabase } from '../lib/supabase';

class EnhancedVapiService {
  constructor() {
    this.initialized = false;
    this.mcpService = enhancedVapiMcpService;
    this.activeCallListeners = new Map();
    this.callUpdateCallbacks = new Map();
  }

  /**
   * Initialize the service
   * @param {Object} options - Initialization options
   * @returns {Promise<boolean>} - Whether initialization was successful
   */
  async initialize(options = {}) {
    try {
      if (this.initialized) {
        return true;
      }

      // Get API key from options or config
      const apiKey = options.apiKey ||
                    mcpConfig.voice.vapi.secretKey ||
                    mcpConfig.voice.vapi.publicKey;

      if (!apiKey) {
        console.error('[EnhancedVapiService] No API key available');
        return false;
      }

      // Initialize MCP service
      const mcpResult = await this.mcpService.connect(apiKey, options.forceDirect);

      if (!mcpResult) {
        console.error('[EnhancedVapiService] Failed to initialize MCP service');
        return false;
      }

      this.initialized = true;
      return true;
    } catch (error) {
      console.error('[EnhancedVapiService] Initialization error:', error);
      return false;
    }
  }

  /**
   * Create an outbound call
   * @param {string} assistantId - The ID of the assistant to use for the call
   * @param {string} phoneNumber - The phone number to call
   * @param {Object} options - Additional call options
   * @returns {Promise<Object>} - The created call
   */
  async createCall(assistantId, phoneNumber, options = {}) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize Vapi service');
        }
      }

      // Create call using MCP service
      const call = await this.mcpService.createCall(assistantId, phoneNumber, options);

      // Store call in Supabase
      await this.storeCallRecord(call);

      return call;
    } catch (error) {
      console.error('[EnhancedVapiService] Error creating call:', error);
      throw error;
    }
  }

  /**
   * Store call record in Supabase
   * @param {Object} call - The call object
   * @returns {Promise<Object>} - The stored call record
   */
  async storeCallRecord(call) {
    try {
      // Get attorney ID from assistant ID
      const { data: assistantData } = await supabase
        .from('attorney_assistants')
        .select('attorney_id')
        .eq('assistant_id', call.assistant_id)
        .single();

      const attorneyId = assistantData?.attorney_id;

      if (!attorneyId) {
        console.warn('[EnhancedVapiService] No attorney found for assistant:', call.assistant_id);
        return null;
      }

      // Create call record
      const callRecord = {
        call_id: call.id,
        assistant_id: call.assistant_id,
        attorney_id: attorneyId,
        customer_phone: call.customer?.phoneNumber,
        status: call.status,
        start_time: new Date().toISOString(),
        metadata: { raw_call: call }
      };

      // Store in Supabase
      const { data, error } = await supabase
        .from('call_records')
        .upsert(callRecord, { onConflict: 'call_id' })
        .select();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('[EnhancedVapiService] Error storing call record:', error);
      return null;
    }
  }

  /**
   * Get call details
   * @param {string} callId - The ID of the call to get
   * @returns {Promise<Object>} - The call details
   */
  async getCall(callId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize Vapi service');
        }
      }

      // Get call using MCP service
      return await this.mcpService.getCall(callId);
    } catch (error) {
      console.error(`[EnhancedVapiService] Error getting call ${callId}:`, error);
      throw error;
    }
  }

  /**
   * Generate a secure token for call control
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @param {Object} options - Token options
   * @returns {string} - The generated token
   */
  generateCallControlToken(callId, attorneyId, options = {}) {
    const payload = {
      callId,
      attorneyId,
      type: 'call-control',
      ...options
    };

    // Default expiration of 24 hours
    const expiresIn = options.expiresIn || '24h';

    return createCallControlTokenSync(callId, attorneyId, { expiresIn });
  }

  /**
   * Verify a call control token
   * @param {string} token - The token to verify
   * @returns {Promise<Object|null>} - The decoded token payload or null if invalid
   */
  async verifyCallControlToken(token) {
    return await verifySecureToken(token);
  }

  /**
   * Send a message to a call
   * @param {string} callId - The ID of the call
   * @param {string} message - The message to send
   * @returns {Promise<boolean>} - Whether the message was sent successfully
   */
  async sendMessage(callId, message) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize Vapi service');
        }
      }

      // Send message using MCP service
      await this.mcpService.sendMessageToCall(callId, message);

      return true;
    } catch (error) {
      console.error(`[EnhancedVapiService] Error sending message to call ${callId}:`, error);
      return false;
    }
  }

  /**
   * End a call
   * @param {string} callId - The ID of the call to end
   * @returns {Promise<boolean>} - Whether the call was ended successfully
   */
  async endCall(callId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize Vapi service');
        }
      }

      // End call using MCP service
      await this.mcpService.endCall(callId);

      return true;
    } catch (error) {
      console.error(`[EnhancedVapiService] Error ending call ${callId}:`, error);
      return false;
    }
  }

  /**
   * Take over a call
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @returns {Promise<boolean>} - Whether the call was taken over successfully
   */
  async takeOverCall(callId, attorneyId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize Vapi service');
        }
      }

      // Get attorney details
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      // Check if attorney has a phone number
      if (!attorney.phone) {
        throw new Error(`Attorney ${attorneyId} has no phone number`);
      }

      // Take over call using MCP service
      // This would typically involve transferring the call to the attorney's phone
      // For now, this is not implemented in the Vapi API

      return false; // Return false to indicate not implemented
    } catch (error) {
      console.error(`[EnhancedVapiService] Error taking over call ${callId}:`, error);
      return false;
    }
  }

  /**
   * Send SMS notification to attorney about a call
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @param {Object} options - Notification options
   * @returns {Promise<Object>} - The result of the SMS send operation
   */
  async sendCallNotification(callId, attorneyId, options = {}) {
    try {
      // Get attorney details
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      // Get call details
      const call = await this.getCall(callId);

      if (!call) {
        throw new Error(`Call not found: ${callId}`);
      }

      // Generate call control token
      const token = this.generateCallControlToken(callId, attorneyId);

      // Create call control URL
      const callControlUrl = `${window.location.origin}/call-control?token=${token}`;

      // Create message
      const message = options.message ||
        `You have an active call from ${call.customer?.phoneNumber || 'a potential client'}. ` +
        `Click here to monitor and control the call: ${callControlUrl}`;

      // Send SMS using Vapi MCP service
      // Note: This assumes Vapi has an SMS sending capability
      // If not, we would need to integrate with a separate SMS service
      const result = await this.mcpService.sendSms(attorney.phone, message);

      return result;
    } catch (error) {
      console.error('[EnhancedVapiService] Error sending call notification:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const enhancedVapiService = new EnhancedVapiService();
export default enhancedVapiService;
