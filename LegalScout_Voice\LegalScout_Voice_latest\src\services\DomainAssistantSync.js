/**
 * Domain-Aware Assistant Configuration Service
 * 
 * Handles synchronizing assistant configurations when domains/subdomains change.
 * Ensures webhook URLs, domain-specific settings, and other configurations
 * are properly updated when attorneys change their subdomain.
 */

import { supabase } from '../lib/supabase';

class DomainAssistantSync {
  constructor() {
    this.vapiApiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || window.VAPI_PRIVATE_KEY;
    this.baseUrl = 'https://api.vapi.ai';
  }

  /**
   * Update assistant configuration for a new domain/subdomain
   */
  async updateAssistantForDomain(assistantId, subdomain, attorneyData) {
    try {
      console.log(`[DomainAssistantSync] Updating assistant ${assistantId} for subdomain: ${subdomain}`);

      // Get current assistant configuration
      const currentConfig = await this.getAssistant(assistantId);
      if (!currentConfig) {
        throw new Error(`Assistant ${assistantId} not found`);
      }

      // Build domain-specific configuration
      const domainConfig = this.buildDomainSpecificConfig(subdomain, attorneyData, currentConfig);

      // Update the assistant
      const updatedAssistant = await this.updateAssistant(assistantId, domainConfig);

      console.log(`[DomainAssistantSync] ✅ Assistant updated for domain: ${subdomain}`);
      return updatedAssistant;

    } catch (error) {
      console.error(`[DomainAssistantSync] ❌ Error updating assistant for domain:`, error);
      throw error;
    }
  }

  /**
   * Build domain-specific configuration
   */
  buildDomainSpecificConfig(subdomain, attorneyData, currentConfig) {
    const baseUrl = window.location.hostname.includes('localhost') 
      ? 'http://localhost:5175' 
      : `https://${subdomain}.legalscout.net`;

    const webhookUrl = window.location.hostname.includes('localhost')
      ? 'http://localhost:5175/api/webhook/vapi-call'
      : 'https://dashboard.legalscout.net/api/webhook/vapi-call';

    return {
      name: attorneyData.firm_name || currentConfig.name,
      firstMessage: attorneyData.welcome_message ||
        `Hello! I'm Scout from ${attorneyData.firm_name}. How can I help you today?`,

      // Voice configuration
      voice: {
        provider: attorneyData.voice_provider || 'openai',
        voiceId: attorneyData.voice_id || 'alloy'
      },

      // AI Model configuration
      model: {
        provider: attorneyData.ai_model?.includes('gpt') ? 'openai' : 'anthropic',
        model: attorneyData.ai_model || 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: attorneyData.vapi_instructions ||
              `You are a legal assistant for ${attorneyData.firm_name}, helping clients with their legal needs.`
          }
        ]
      },

      // Webhook configuration for the specific subdomain
      server: {
        url: webhookUrl
      },

      // Domain-specific metadata
      metadata: {
        subdomain: subdomain,
        firmName: attorneyData.firm_name,
        baseUrl: baseUrl,
        lastDomainUpdate: new Date().toISOString()
      }
    };
  }

  /**
   * Get assistant from Vapi API
   */
  async getAssistant(assistantId) {
    const response = await fetch(`${this.baseUrl}/assistant/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.vapiApiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get assistant: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Update assistant in Vapi API
   */
  async updateAssistant(assistantId, config) {
    // Use clean fetch to avoid interceptors
    const cleanFetch = window.originalFetch || fetch;

    const response = await cleanFetch(`${this.baseUrl}/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${this.vapiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update assistant: ${response.status} ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Handle subdomain change for an attorney
   */
  async handleSubdomainChange(attorneyId, oldSubdomain, newSubdomain) {
    try {
      console.log(`[DomainAssistantSync] Handling subdomain change: ${oldSubdomain} → ${newSubdomain}`);

      // Get attorney data
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      // Update assistant configuration for new subdomain
      if (attorney.vapi_assistant_id) {
        await this.updateAssistantForDomain(
          attorney.vapi_assistant_id, 
          newSubdomain, 
          attorney
        );
      }

      // Update any other domain-dependent configurations here
      // (e.g., webhook registrations, DNS settings, etc.)

      console.log(`[DomainAssistantSync] ✅ Subdomain change completed: ${oldSubdomain} → ${newSubdomain}`);
      return true;

    } catch (error) {
      console.error(`[DomainAssistantSync] ❌ Error handling subdomain change:`, error);
      throw error;
    }
  }

  /**
   * Validate domain configuration
   */
  async validateDomainConfig(subdomain, assistantId) {
    try {
      // Check if subdomain is available
      const { data: existingAttorney } = await supabase
        .from('attorneys')
        .select('id, firm_name')
        .eq('subdomain', subdomain)
        .single();

      if (existingAttorney) {
        return {
          valid: false,
          error: `Subdomain '${subdomain}' is already taken by ${existingAttorney.firm_name}`
        };
      }

      // Check if assistant exists and is accessible
      if (assistantId) {
        const assistant = await this.getAssistant(assistantId);
        if (!assistant) {
          return {
            valid: false,
            error: `Assistant ${assistantId} not found or not accessible`
          };
        }
      }

      return { valid: true };

    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
export const domainAssistantSync = new DomainAssistantSync();

// Export for use in components
export default DomainAssistantSync;
