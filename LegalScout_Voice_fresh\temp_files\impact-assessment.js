/**
 * Impact Assessment Script
 * 
 * Analyzes how the dashboard session agent change issues affect other system components
 */

(function() {
  'use strict';

  const ImpactAssessment = {
    impacts: {},
    
    log: function(component, severity, issue, details = null) {
      if (!this.impacts[component]) {
        this.impacts[component] = [];
      }
      
      this.impacts[component].push({
        severity, // CRITICAL, HIGH, MEDIUM, LOW
        issue,
        details,
        timestamp: new Date().toISOString()
      });
      
      const emoji = severity === 'CRITICAL' ? '🚨' : severity === 'HIGH' ? '⚠️' : severity === 'MEDIUM' ? '⚡' : '💡';
      console.log(`${emoji} [Impact:${component}] ${severity}: ${issue}`, details || '');
    },

    // Assess impact on Voice/Call functionality
    assessVoiceCallImpact: function() {
      const component = 'VoiceCall';
      
      // Check if assistant switching affects active calls
      const attorney = window.standaloneAttorneyManager?.attorney;
      const assistantId = attorney?.vapi_assistant_id;
      
      if (!assistantId) {
        this.log(component, 'CRITICAL', 'No assistant ID available - calls cannot be initiated');
        return;
      }
      
      if (assistantId.includes('mock') || assistantId.includes('dev-')) {
        this.log(component, 'HIGH', 'Mock assistant ID detected - calls may fail in production');
      }
      
      // Check Vapi SDK availability
      if (!window.vapi && !window.Vapi) {
        this.log(component, 'CRITICAL', 'Vapi SDK not loaded - voice calls impossible');
      } else {
        this.log(component, 'LOW', 'Vapi SDK available');
      }
      
      // Check for call state management
      const callButtons = document.querySelectorAll('[class*="call"], [class*="voice"], button[onclick*="call"]');
      if (callButtons.length === 0) {
        this.log(component, 'MEDIUM', 'No call buttons detected in UI');
      }
    },

    // Assess impact on Preview functionality
    assessPreviewImpact: function() {
      const component = 'Preview';
      
      // Check iframe availability
      const iframes = document.querySelectorAll('iframe');
      const previewIframes = Array.from(iframes).filter(iframe => 
        iframe.src.includes('preview') || iframe.title?.includes('Preview')
      );
      
      if (previewIframes.length === 0) {
        this.log(component, 'HIGH', 'No preview iframes found - preview functionality broken');
      } else {
        // Check if iframes can receive messages
        previewIframes.forEach((iframe, index) => {
          try {
            if (iframe.contentWindow) {
              this.log(component, 'LOW', `Preview iframe ${index + 1} accessible`);
            } else {
              this.log(component, 'MEDIUM', `Preview iframe ${index + 1} not accessible`);
            }
          } catch (error) {
            this.log(component, 'MEDIUM', `Preview iframe ${index + 1} cross-origin restricted`);
          }
        });
      }
      
      // Check preview config propagation
      if (window.updatePreviewConfig) {
        this.log(component, 'LOW', 'Preview config update function available');
      } else {
        this.log(component, 'HIGH', 'Preview config update function missing');
      }
    },

    // Assess impact on Attorney Profile management
    assessProfileImpact: function() {
      const component = 'Profile';
      
      const attorney = window.standaloneAttorneyManager?.attorney;
      
      if (!attorney) {
        this.log(component, 'CRITICAL', 'No attorney profile loaded');
        return;
      }
      
      // Check profile completeness
      const requiredFields = ['id', 'email', 'firm_name', 'vapi_assistant_id'];
      const missingFields = requiredFields.filter(field => !attorney[field]);
      
      if (missingFields.length > 0) {
        this.log(component, 'HIGH', `Missing required profile fields: ${missingFields.join(', ')}`);
      }
      
      // Check profile UI sync
      const profileElements = document.querySelectorAll('[class*="profile"], [class*="Profile"]');
      if (profileElements.length === 0) {
        this.log(component, 'MEDIUM', 'No profile UI elements detected');
      }
      
      // Check localStorage sync
      const storedAttorney = localStorage.getItem('attorney');
      if (!storedAttorney) {
        this.log(component, 'HIGH', 'Attorney profile not persisted in localStorage');
      } else {
        try {
          const stored = JSON.parse(storedAttorney);
          if (stored.id !== attorney.id) {
            this.log(component, 'HIGH', 'localStorage profile out of sync with manager');
          }
        } catch (error) {
          this.log(component, 'MEDIUM', 'Corrupted attorney data in localStorage');
        }
      }
    },

    // Assess impact on Navigation and Routing
    assessNavigationImpact: function() {
      const component = 'Navigation';
      
      // Check for navigation elements
      const navElements = document.querySelectorAll('nav, [class*="nav"], [class*="Nav"], [role="navigation"]');
      if (navElements.length === 0) {
        this.log(component, 'MEDIUM', 'No navigation elements detected');
      }
      
      // Check for tab switching functionality
      const tabs = document.querySelectorAll('[class*="tab"], [class*="Tab"], [role="tab"]');
      if (tabs.length === 0) {
        this.log(component, 'MEDIUM', 'No tab elements detected');
      }
      
      // Check React Router functionality
      if (window.location.pathname === '/dashboard' || window.location.pathname.includes('dashboard')) {
        this.log(component, 'LOW', 'On dashboard route');
      } else {
        this.log(component, 'MEDIUM', 'Not on expected dashboard route');
      }
    },

    // Assess impact on Data Persistence
    assessDataPersistenceImpact: function() {
      const component = 'DataPersistence';
      
      // Check Supabase connectivity
      if (window.supabase) {
        this.log(component, 'LOW', 'Supabase client available');
        
        // Test basic connectivity (non-destructive)
        try {
          window.supabase.from('attorneys').select('count', { count: 'exact', head: true })
            .then(result => {
              if (result.error) {
                this.log(component, 'HIGH', 'Supabase connectivity issues', result.error.message);
              } else {
                this.log(component, 'LOW', 'Supabase connectivity confirmed');
              }
            });
        } catch (error) {
          this.log(component, 'HIGH', 'Supabase query failed', error.message);
        }
      } else {
        this.log(component, 'CRITICAL', 'Supabase client not available');
      }
      
      // Check localStorage health
      try {
        const testKey = 'impact_test_' + Date.now();
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        this.log(component, 'LOW', 'localStorage functioning');
      } catch (error) {
        this.log(component, 'HIGH', 'localStorage not functioning', error.message);
      }
    },

    // Assess impact on Authentication
    assessAuthImpact: function() {
      const component = 'Authentication';
      
      // Check auth context
      const user = window.user || window.currentUser;
      if (!user) {
        this.log(component, 'HIGH', 'No authenticated user detected');
      } else {
        this.log(component, 'LOW', 'User authentication detected');
      }
      
      // Check for auth errors in console
      const authErrors = this.impacts.Authentication?.filter(impact => 
        impact.issue.includes('auth') || impact.issue.includes('login')
      ) || [];
      
      if (authErrors.length > 0) {
        this.log(component, 'HIGH', `${authErrors.length} authentication-related issues detected`);
      }
    },

    // Run comprehensive impact assessment
    runAssessment: function() {
      console.log('🔍 Starting Impact Assessment...');
      
      this.assessVoiceCallImpact();
      this.assessPreviewImpact();
      this.assessProfileImpact();
      this.assessNavigationImpact();
      this.assessDataPersistenceImpact();
      this.assessAuthImpact();
      
      this.generateImpactReport();
    },

    // Generate impact report with prioritized fixes
    generateImpactReport: function() {
      console.log('\n🎯 IMPACT ASSESSMENT REPORT');
      console.log('='.repeat(50));
      
      const priorityOrder = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'];
      const impactsByPriority = {};
      
      // Organize impacts by priority
      priorityOrder.forEach(priority => {
        impactsByPriority[priority] = [];
        Object.entries(this.impacts).forEach(([component, impacts]) => {
          impacts.filter(impact => impact.severity === priority)
                 .forEach(impact => {
                   impactsByPriority[priority].push({
                     component,
                     ...impact
                   });
                 });
        });
      });
      
      // Display by priority
      priorityOrder.forEach(priority => {
        const impacts = impactsByPriority[priority];
        if (impacts.length > 0) {
          console.log(`\n${priority} PRIORITY (${impacts.length} issues):`);
          impacts.forEach(impact => {
            const emoji = priority === 'CRITICAL' ? '🚨' : priority === 'HIGH' ? '⚠️' : priority === 'MEDIUM' ? '⚡' : '💡';
            console.log(`  ${emoji} ${impact.component}: ${impact.issue}`);
          });
        }
      });
      
      // Generate fix recommendations
      this.generateFixRecommendations(impactsByPriority);
      
      // Store results
      window.impactAssessment = this.impacts;
      window.impactsByPriority = impactsByPriority;
    },

    // Generate targeted fix recommendations
    generateFixRecommendations: function(impactsByPriority) {
      console.log('\n🔧 RECOMMENDED FIX SEQUENCE:');
      
      const criticalIssues = impactsByPriority.CRITICAL;
      const highIssues = impactsByPriority.HIGH;
      
      if (criticalIssues.length > 0) {
        console.log('\n1. IMMEDIATE FIXES (CRITICAL):');
        criticalIssues.forEach((issue, index) => {
          console.log(`   ${index + 1}. Fix ${issue.component}: ${issue.issue}`);
        });
      }
      
      if (highIssues.length > 0) {
        console.log('\n2. PRIORITY FIXES (HIGH):');
        highIssues.forEach((issue, index) => {
          console.log(`   ${index + 1}. Fix ${issue.component}: ${issue.issue}`);
        });
      }
      
      console.log('\n💡 STRATEGY: Fix critical issues first to restore basic functionality,');
      console.log('   then address high-priority issues to improve reliability.');
    }
  };

  // Auto-run assessment
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => ImpactAssessment.runAssessment(), 2000);
    });
  } else {
    setTimeout(() => ImpactAssessment.runAssessment(), 2000);
  }

  // Expose for manual testing
  window.ImpactAssessment = ImpactAssessment;

})();
