import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnalysisConfigTab from '../AnalysisConfigTab';
import { useAttorneyState } from '../../../contexts/AttorneyStateContext';
import { vapiAssistantService } from '../../../services/vapiAssistantService';

// Mock the dependencies
jest.mock('../../../contexts/AttorneyStateContext');
jest.mock('../../../services/vapiAssistantService');
jest.mock('../JsonEditor', () => {
  return function MockJsonEditor({ value, onChange }) {
    return (
      <textarea
        data-testid="json-editor"
        value={JSON.stringify(value)}
        onChange={(e) => {
          try {
            const parsed = JSON.parse(e.target.value);
            onChange(parsed);
          } catch (err) {
            // Invalid JSO<PERSON>, ignore
          }
        }}
      />
    );
  };
});
jest.mock('../TemplateManager', () => {
  return function MockTemplateManager({ onApplyTemplate }) {
    return (
      <div data-testid="template-manager">
        <button onClick={() => onApplyTemplate({ attorney: { summary_prompt: 'Test template' } })}>
          Apply Template
        </button>
      </div>
    );
  };
});

describe('AnalysisConfigTab', () => {
  const mockAttorney = {
    id: 'test-attorney-id',
    vapi_assistant_id: 'test-assistant-id',
    summary_prompt: 'Test summary prompt',
    success_evaluation_prompt: 'Test success prompt',
    structured_data_prompt: 'Test structured data prompt',
    structured_data_schema: { type: 'object', properties: {} }
  };

  const mockUpdateAttorney = jest.fn();
  const mockSaveAttorney = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    useAttorneyState.mockReturnValue({
      attorney: mockAttorney,
      updateAttorney: mockUpdateAttorney,
      saveAttorney: mockSaveAttorney
    });

    vapiAssistantService.getAssistant.mockResolvedValue({
      id: 'test-assistant-id',
      analysis: {
        summary: { prompt: 'Assistant summary prompt' },
        success: { prompt: 'Assistant success prompt' },
        structuredData: { 
          prompt: 'Assistant structured data prompt',
          schema: { type: 'object', properties: { test: { type: 'string' } } }
        }
      }
    });

    vapiAssistantService.updateAssistantConfiguration.mockResolvedValue({
      id: 'test-assistant-id'
    });
  });

  test('renders analysis configuration form', async () => {
    render(<AnalysisConfigTab />);

    expect(screen.getByText('Analysis Configuration')).toBeInTheDocument();
    expect(screen.getByText('Success Evaluation')).toBeInTheDocument();
    expect(screen.getByText('End of Call Report')).toBeInTheDocument();
    expect(screen.getByText('Structured Data Extraction')).toBeInTheDocument();
  });

  test('loads attorney data into form fields', async () => {
    render(<AnalysisConfigTab />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('Test summary prompt')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test success prompt')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test structured data prompt')).toBeInTheDocument();
    });
  });

  test('saves changes when save button is clicked', async () => {
    render(<AnalysisConfigTab />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue('Test summary prompt')).toBeInTheDocument();
    });

    // Modify a field
    const summaryField = screen.getByDisplayValue('Test summary prompt');
    fireEvent.change(summaryField, { target: { value: 'Updated summary prompt' } });

    // Click save
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockSaveAttorney).toHaveBeenCalledWith(
        expect.objectContaining({
          summary_prompt: 'Updated summary prompt'
        })
      );
      expect(vapiAssistantService.updateAssistantConfiguration).toHaveBeenCalledWith(
        'test-assistant-id',
        expect.objectContaining({
          summary_prompt: 'Updated summary prompt'
        })
      );
    });
  });

  test('applies template when template manager is used', async () => {
    render(<AnalysisConfigTab />);

    // Click apply template button
    const applyTemplateButton = screen.getByText('Apply Template');
    fireEvent.click(applyTemplateButton);

    await waitFor(() => {
      expect(mockUpdateAttorney).toHaveBeenCalledWith(
        expect.objectContaining({
          summary_prompt: 'Test template'
        })
      );
    });
  });

  test('handles JSON schema editing', async () => {
    render(<AnalysisConfigTab />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByTestId('json-editor')).toBeInTheDocument();
    });

    // Modify the JSON schema
    const jsonEditor = screen.getByTestId('json-editor');
    const newSchema = { type: 'object', properties: { name: { type: 'string' } } };
    fireEvent.change(jsonEditor, { target: { value: JSON.stringify(newSchema) } });

    // Click save
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockSaveAttorney).toHaveBeenCalledWith(
        expect.objectContaining({
          structured_data_schema: newSchema
        })
      );
    });
  });

  test('resets to defaults when reset button is clicked', async () => {
    // Mock window.confirm to return true
    window.confirm = jest.fn(() => true);

    render(<AnalysisConfigTab />);

    // Wait for form to load
    await waitFor(() => {
      expect(screen.getByDisplayValue('Test summary prompt')).toBeInTheDocument();
    });

    // Click reset button
    const resetButton = screen.getByText('Reset to Defaults');
    fireEvent.click(resetButton);

    // Check that fields are reset to defaults
    await waitFor(() => {
      expect(screen.getByDisplayValue(/You are an expert note-taker/)).toBeInTheDocument();
    });
  });
});
