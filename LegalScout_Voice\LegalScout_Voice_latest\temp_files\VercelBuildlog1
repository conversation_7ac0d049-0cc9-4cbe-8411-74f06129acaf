[09:19:11.580] Running build in Washington, D.C., USA (East) – iad1
[09:19:11.581] Build machine configuration: 2 cores, 8 GB
[09:19:11.594] Cloning github.com/damonkost/LegalScout_Voice (Branch: main, Commit: 59fc3ca)
[09:19:13.470] Warning: Failed to fetch one or more git submodules
[09:19:13.470] Cloning completed: 1.876s
[09:19:13.737] Found .vercelignore
[09:19:13.779] Removed 275 ignored files defined in .vercelignore
[09:19:13.956] Restored build cache from previous deployment (42BMKiw5BXmM99JCqTLZg7tYoGRD)
[09:19:15.454] Running "vercel build"
[09:19:15.914] Vercel CLI 42.2.0
[09:19:16.209] WARN! When using Next.js, it is recommended to place JavaScript Functions inside of the `pages/api` (provided by Next.js) directory instead of `api` (provided by Vercel). Other languages (Python, Go, etc) should still go in the `api` directory.
[09:19:16.210] Learn More: https://nextjs.org/docs/api-routes/introduction
[09:19:16.865] Warning: Detected "engines": { "node": ">=18.0.0" } in your `package.json` that will automatically upgrade when a new major Node.js Version is released. Learn More: http://vercel.link/node-version
[09:19:16.874] Running "install" command: `npm ci --prefer-offline --no-audit --legacy-peer-deps`...
[09:19:37.572] 
[09:19:37.573] added 1114 packages in 20s
[09:19:37.574] 
[09:19:37.574] 293 packages are looking for funding
[09:19:37.576]   run `npm fund` for details
[09:19:37.849] 
[09:19:37.849] > legalscout@0.1.0 vercel-build-safe
[09:19:37.850] > cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 VITE_DISABLE_FRAMER_MOTION=true vite build --mode production && node scripts/production-environment-injector.cjs
[09:19:37.850] 
[09:19:38.298] [36mvite v4.5.9 [32mbuilding for production...[36m[39m
[09:19:38.310] [ExcludeFramerMotion] Transforming code in: /vercel/path0/index.html
[09:19:38.375] transforming...
[09:19:38.423] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/utils/reactPolyfill.js
[09:19:38.513] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/components/ProductionErrorBoundary.jsx
[09:19:41.466] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/components/EnhancedSpeechParticles.jsx
[09:19:41.470] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/components/preview/PreviewInterface.tsx
[09:19:42.934] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/mocks/framer-motion.js
[09:19:44.163] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/mocks/LayoutGroupContext.js
[09:19:44.164] [ExcludeFramerMotion] Transforming code in: /vercel/path0/src/mocks/MotionConfigContext.js
[09:19:45.559] src/utils/environmentVerifier.js (19:23) Use of eval in "src/utils/environmentVerifier.js" is strongly discouraged as it poses security risks and may cause issues with minification.
[09:19:45.560] src/utils/environmentVerifier.js (61:25) Use of eval in "src/utils/environmentVerifier.js" is strongly discouraged as it poses security risks and may cause issues with minification.
[09:19:45.590] [32m✓[39m 900 modules transformed.
[09:19:45.696] Generated an empty chunk: "vendor".
[09:19:45.710] Generated an empty chunk: "vendor-data".
[09:19:45.722] Generated an empty chunk: "vendor-data-processing".
[09:19:45.729] Generated an empty chunk: "vendor-maps".
[09:19:45.753] Generated an empty chunk: "vendor-react".
[09:19:45.759] Generated an empty chunk: "vendor-utils".
[09:19:45.760] Generated an empty chunk: "vendor-vapi".
[09:19:45.799] rendering chunks...
[09:19:45.860] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.861] (!) /vercel/path0/src/config/vapiConfig.js is dynamically imported by /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/services/vapiMcpService.js but also statically imported by /vercel/path0/src/config/mcp.config.js, /vercel/path0/src/services/VapiService.js, /vercel/path0/src/utils/vapiCallDiagnostics.js, /vercel/path0/src/utils/vapiDirectApi.js, dynamic import will not move module into another chunk.
[09:19:45.863] [39m
[09:19:45.865] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.865] (!) /vercel/path0/src/config/mcp.config.js is dynamically imported by /vercel/path0/src/services/vapiMcpService.js but also statically imported by /vercel/path0/src/hooks/use-vapi.js, /vercel/path0/src/services/EnhancedVapiAssistantManager.js, /vercel/path0/src/services/EnhancedVapiMcpService.js, /vercel/path0/src/services/EnhancedVapiService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiMcpService.js, /vercel/path0/src/utils/vapiMcpDiagnostics.js, dynamic import will not move module into another chunk.
[09:19:45.865] [39m
[09:19:45.865] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.866] (!) /vercel/path0/src/services/EnhancedVapiMcpService.js is dynamically imported by /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/config/attorneys.js but also statically imported by /vercel/path0/src/hooks/use-vapi.js, /vercel/path0/src/services/EnhancedSyncHelpers.js, /vercel/path0/src/services/EnhancedVapiService.js, /vercel/path0/src/services/VapiService.js, dynamic import will not move module into another chunk.
[09:19:45.866] [39m
[09:19:45.866] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.866] (!) /vercel/path0/src/lib/supabase.js is dynamically imported by /vercel/path0/src/App.jsx, /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/components/dashboard/EnhancedAssistantDropdown.jsx, /vercel/path0/src/contexts/AssistantAwareContext.jsx, /vercel/path0/src/services/AttorneyProfileManager.js but also statically imported by /vercel/path0/src/components/AuthOverlay.jsx, /vercel/path0/src/components/CallRecordsTable.jsx, /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/components/dashboard/AssistantInfoSection.jsx, /vercel/path0/src/components/dashboard/CSVImportModal.jsx, /vercel/path0/src/components/dashboard/CallForwardingRules.jsx, /vercel/path0/src/components/dashboard/CallManagementSection.jsx, /vercel/path0/src/components/dashboard/ConsultationStats.jsx, /vercel/path0/src/components/dashboard/ConsultationsTab.jsx, /vercel/path0/src/components/dashboard/CustomFieldsTab.jsx, /vercel/path0/src/components/dashboard/ProfileTab.jsx, /vercel/path0/src/components/dashboard/SubdomainEditor.jsx, /vercel/path0/src/components/dashboard/ToolsTab.jsx, /vercel/path0/src/components/dashboard/VeryCoolAssistants.jsx, /vercel/path0/src/components/dashboard/VoiceAssistantConfig.jsx, /vercel/path0/src/config/attorneys.js, /vercel/path0/src/contexts/AuthContext.jsx, /vercel/path0/src/hooks/useCallControl.js, /vercel/path0/src/hooks/useDomainSync.js, /vercel/path0/src/pages/AuthCallback.jsx, /vercel/path0/src/pages/DashboardNew.jsx, /vercel/path0/src/pages/SimpleCompleteProfile.jsx, /vercel/path0/src/pages/SimplePreviewPage.jsx, /vercel/path0/src/services/AttorneyProfileManager.js, /vercel/path0/src/services/AutoAssistantReconciler.js, /vercel/path0/src/services/DomainAssistantSync.js, /vercel/path0/src/services/EnhancedSyncHelpers.js, /vercel/path0/src/services/EnhancedVapiAssistantManager.js, /vercel/path0/src/services/EnhancedVapiCallService.js, /vercel/path0/src/services/EnhancedVapiService.js, /vercel/path0/src/services/SmsNotificationService.js, /vercel/path0/src/services/assistantAssignmentService.js, /vercel/path0/src/services/assistantDataRefreshService.js, /vercel/path0/src/services/assistantSubdomainService.js, /vercel/path0/src/services/assistantUIConfigService.js, /vercel/path0/src/services/attorneyStateManager.js, /vercel/path0/src/services/authService.js, /vercel/path0/src/services/callHistoryService.js, /vercel/path0/src/services/supabaseService.js, /vercel/path0/src/services/syncHelpers.js, /vercel/path0/src/services/templateService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiEmissionsService.js, /vercel/path0/src/testSupabase.js, /vercel/path0/src/utils/attorneyUtils.js, /vercel/path0/src/utils/authProfileFixer.js, /vercel/path0/src/utils/supabaseConfigVerifier.js, /vercel/path0/src/utils/vapiAssistantUtils.js, dynamic import will not move module into another chunk.
[09:19:45.866] [39m
[09:19:45.866] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.866] (!) /vercel/path0/src/services/vapiMcpService.js is dynamically imported by /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/components/dashboard/AssistantInfoSection.jsx, /vercel/path0/src/components/dashboard/ConsultationsTab.jsx, /vercel/path0/src/components/dashboard/VeryCoolAssistants.jsx, /vercel/path0/src/components/dashboard/VeryCoolAssistants.jsx, /vercel/path0/src/contexts/AssistantAwareContext.jsx, /vercel/path0/src/services/assistantSubdomainService.js, /vercel/path0/src/utils/previewConfigHandler.js, /vercel/path0/src/utils/previewConfigHandler.js, /vercel/path0/src/utils/previewConfigHandler.js but also statically imported by /vercel/path0/src/components/dashboard/CallManagementSection.jsx, /vercel/path0/src/components/dashboard/WorkflowTab.jsx, /vercel/path0/src/services/EnhancedVapiAssistantManager.js, /vercel/path0/src/services/EnhancedVapiCallService.js, /vercel/path0/src/services/SmsNotificationService.js, /vercel/path0/src/services/assistantAssignmentService.js, /vercel/path0/src/services/assistantDataRefreshService.js, /vercel/path0/src/services/callHistoryService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiEmissionsService.js, /vercel/path0/src/services/vapiServiceManager.js, /vercel/path0/src/utils/vapiCallDiagnostics.js, dynamic import will not move module into another chunk.
[09:19:45.866] [39m
[09:19:45.872] [1m[33m[plugin:vite:reporter][39m[22m [33m
[09:19:45.872] (!) /vercel/path0/src/utils/loggerUtils.js is dynamically imported by /vercel/path0/src/components/dashboard/AgentTab.jsx, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/services/vapiAssistantService.js, /vercel/path0/src/utils/previewConfigHandler.js but also statically imported by /vercel/path0/src/components/dashboard/VoiceAssistantConfig.jsx, /vercel/path0/src/utils/vapiFieldMapping.js, dynamic import will not move module into another chunk.
[09:19:45.872] [39m
[09:19:45.938] [2mdist/[22m[32mindex.html                                    [39m[1m[2m 25.98 kB[22m[1m[22m
[09:19:45.939] [2mdist/[22m[2massets/[22m[35mcss/vendor-react-5e8f3fb6.css          [39m[1m[2m 14.16 kB[22m[1m[22m
[09:19:45.940] [2mdist/[22m[2massets/[22m[35mcss/vendor-maps-69420918.css           [39m[1m[2m 15.65 kB[22m[1m[22m
[09:19:45.940] [2mdist/[22m[2massets/[22m[35mcss/components-maps-4bf1ccba.css       [39m[1m[2m 16.04 kB[22m[1m[22m
[09:19:45.941] [2mdist/[22m[2massets/[22m[35mcss/components-vapi-ee190643.css       [39m[1m[2m 32.64 kB[22m[1m[22m
[09:19:45.941] [2mdist/[22m[2massets/[22m[35mcss/index-6f6d4b70.css                 [39m[1m[2m 37.54 kB[22m[1m[22m
[09:19:45.941] [2mdist/[22m[2massets/[22m[35mcss/components-common-426af7a6.css     [39m[1m[2m 49.95 kB[22m[1m[22m
[09:19:45.942] [2mdist/[22m[2massets/[22m[35mcss/pages-db60df18.css                 [39m[1m[2m131.25 kB[22m[1m[22m
[09:19:45.942] [2mdist/[22m[2massets/[22m[35mcss/components-dashboard-c664c223.css  [39m[1m[2m207.23 kB[22m[1m[22m
[09:19:45.942] [2mdist/[22m[2massets/[22m[36mvendor-utils-4ed993c7.js               [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.943] [2mdist/[22m[2massets/[22m[36mvendor-vapi-4ed993c7.js                [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.943] [2mdist/[22m[2massets/[22m[36mvendor-data-processing-4ed993c7.js     [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.943] [2mdist/[22m[2massets/[22m[36mvendor-data-4ed993c7.js                [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.944] [2mdist/[22m[2massets/[22m[36mvendor-4ed993c7.js                     [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.944] [2mdist/[22m[2massets/[22m[36mvendor-maps-a5ed4575.js                [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.945] [2mdist/[22m[2massets/[22m[36mvendor-react-bfed64c7.js               [39m[1m[2m  0.00 kB[22m[1m[22m
[09:19:45.945] [2mdist/[22m[2massets/[22m[36mcomponents-common-46822bc1.js          [39m[1m[2m  0.04 kB[22m[1m[22m
[09:19:45.945] [2mdist/[22m[2massets/[22m[36mcomponents-vapi-1b56f774.js            [39m[1m[2m  0.04 kB[22m[1m[22m
[09:19:45.945] [2mdist/[22m[2massets/[22m[36mcomponents-maps-b654f21a.js            [39m[1m[2m  0.08 kB[22m[1m[22m
[09:19:45.946] [2mdist/[22m[2massets/[22m[36mcomponents-dashboard-deac3d99.js       [39m[1m[2m  0.08 kB[22m[1m[22m
[09:19:45.946] [2mdist/[22m[2massets/[22m[36mpages-46a59638.js                      [39m[1m[2m  0.12 kB[22m[1m[22m
[09:19:45.946] [2mdist/[22m[2massets/[22m[36mindex-51806598.js                      [39m[1m[2m  0.97 kB[22m[1m[22m
[09:19:45.964] [32m✓ built in 7.67s[39m
[09:19:49.544] 
[09:19:49.545] ✨ [vite-plugin-compression]:algorithm=gzip - compressed file successfully: 
[09:19:49.545] dist//vercel/path0/_preload.html.gz                                  1.48kb / gzip: 0.49kb
[09:19:49.545] dist//vercel/path0/agent-configure-button.js.gz                      3.96kb / gzip: 1.30kb
[09:19:49.546] dist//vercel/path0/agent-tab-debug-loop.js.gz                        10.84kb / gzip: 3.18kb
[09:19:49.546] dist//vercel/path0/aggressive-fix.js.gz                              4.67kb / gzip: 1.16kb
[09:19:49.546] dist//vercel/path0/assets/css/components-common-426af7a6.css.gz      48.78kb / gzip: 9.19kb
[09:19:49.546] dist//vercel/path0/assets/css/components-maps-4bf1ccba.css.gz        15.67kb / gzip: 3.47kb
[09:19:49.546] dist//vercel/path0/assets/css/components-vapi-ee190643.css.gz        31.88kb / gzip: 5.86kb
[09:19:49.546] dist//vercel/path0/assets/css/index-6f6d4b70.css.gz                  36.66kb / gzip: 7.50kb
[09:19:49.546] dist//vercel/path0/api/health/index.html.gz                          1.95kb / gzip: 0.64kb
[09:19:49.546] dist//vercel/path0/assets/css/vendor-react-5e8f3fb6.css.gz           13.82kb / gzip: 2.59kb
[09:19:49.546] dist//vercel/path0/assistant-dropdown-diagnostic.js.gz               6.56kb / gzip: 1.69kb
[09:19:49.546] dist//vercel/path0/assets/css/vendor-maps-69420918.css.gz            15.28kb / gzip: 6.31kb
[09:19:49.546] dist//vercel/path0/attorney-profile-fix.js.gz                        3.77kb / gzip: 0.97kb
[09:19:49.546] dist//vercel/path0/attorney-state-context-fix.js.gz                  3.30kb / gzip: 0.91kb
[09:19:49.546] dist//vercel/path0/attorney-state-initializer.js.gz                  6.51kb / gzip: 2.00kb
[09:19:49.546] dist//vercel/path0/attorney-state-manager-fix.js.gz                  5.57kb / gzip: 1.03kb
[09:19:49.546] dist//vercel/path0/auth-context-fix.js.gz                            3.04kb / gzip: 0.81kb
[09:19:49.547] dist//vercel/path0/auth-diagnostic.js.gz                             5.82kb / gzip: 1.64kb
[09:19:49.547] dist//vercel/path0/auto-configure-direct-fix.js.gz                   5.79kb / gzip: 1.61kb
[09:19:49.547] dist//vercel/path0/auto-configure-fix.js.gz                          12.49kb / gzip: 3.12kb
[09:19:49.547] dist//vercel/path0/auto-diagnostic-runner.js.gz                      13.80kb / gzip: 3.86kb
[09:19:49.547] dist//vercel/path0/attorney-bridge.js.gz                             13.25kb / gzip: 2.85kb
[09:19:49.547] dist//vercel/path0/black-screen-detective.js.gz                      13.52kb / gzip: 3.87kb
[09:19:49.547] dist//vercel/path0/clean-auth-solution.js.gz                         7.02kb / gzip: 2.14kb
[09:19:49.547] dist//vercel/path0/clear-invalid-attorney-data.js.gz                 3.01kb / gzip: 0.96kb
[09:19:49.547] dist//vercel/path0/comparison-test.html.gz                           12.38kb / gzip: 3.35kb
[09:19:49.547] dist//vercel/path0/comprehensive-csp-fix.js.gz                       8.24kb / gzip: 2.42kb
[09:19:49.547] dist//vercel/path0/comprehensive-csp-manager.js.gz                   13.79kb / gzip: 3.45kb
[09:19:49.547] dist//vercel/path0/console-test.js.gz                                6.93kb / gzip: 1.89kb
[09:19:49.547] dist//vercel/path0/attorney-sync-helper.js.gz                        9.23kb / gzip: 2.27kb
[09:19:49.547] dist//vercel/path0/controlled-assistant-creation.js.gz               6.35kb / gzip: 1.79kb
[09:19:49.547] dist//vercel/path0/critical-fixes-v2.js.gz                           4.16kb / gzip: 1.40kb
[09:19:49.547] dist//vercel/path0/critical-production-fix.js.gz                     9.00kb / gzip: 2.65kb
[09:19:49.548] dist//vercel/path0/csp-diagnostic.js.gz                              7.66kb / gzip: 2.16kb
[09:19:49.548] dist//vercel/path0/dashboard-diagnostic.js.gz                        9.64kb / gzip: 2.55kb
[09:19:49.548] dist//vercel/path0/dashboard-iframe-manager.js.gz                    8.16kb / gzip: 2.37kb
[09:19:49.548] dist//vercel/path0/dashboard-loading-interceptor.js.gz               6.23kb / gzip: 1.63kb
[09:19:49.548] dist//vercel/path0/debug-email-state.js.gz                           7.12kb / gzip: 2.14kb
[09:19:49.548] dist//vercel/path0/debug-subdomain.html.gz                           10.12kb / gzip: 2.83kb
[09:19:49.548] dist//vercel/path0/debug-labels.js.gz                                4.19kb / gzip: 1.37kb
[09:19:49.548] dist//vercel/path0/comprehensive-diagnostic-suite.js.gz              12.17kb / gzip: 3.17kb
[09:19:49.548] dist//vercel/path0/deployment-attorney-fix.js.gz                     13.96kb / gzip: 3.03kb
[09:19:49.548] dist//vercel/path0/deployment-sync-button.js.gz                      7.79kb / gzip: 1.96kb
[09:19:49.548] dist//vercel/path0/diagnose-call-issue.js.gz                         7.42kb / gzip: 2.37kb
[09:19:49.549] dist//vercel/path0/direct-fix/MotionConfigContext.mjs.gz             1.02kb / gzip: 0.43kb
[09:19:49.549] dist//vercel/path0/direct-patch/LayoutGroupContext.mjs.gz            1.00kb / gzip: 0.43kb
[09:19:49.549] dist//vercel/path0/direct-patch/MotionConfigContext.mjs.gz           1.08kb / gzip: 0.47kb
[09:19:49.549] dist//vercel/path0/direct-test.js.gz                                 7.02kb / gzip: 2.13kb
[09:19:49.561] dist//vercel/path0/disable-conflicting-scripts.js.gz                 2.96kb / gzip: 1.11kb
[09:19:49.561] dist//vercel/path0/disable-standalone-attorney-manager.js.gz         1.96kb / gzip: 0.51kb
[09:19:49.562] dist//vercel/path0/call-sync-diagnostic.js.gz                        9.98kb / gzip: 2.63kb
[09:19:49.562] dist//vercel/path0/dom-ready-fix.js.gz                               8.93kb / gzip: 2.29kb
[09:19:49.562] dist//vercel/path0/elegant-assistant-creation.js.gz                  11.07kb / gzip: 3.53kb
[09:19:49.562] dist//vercel/path0/consolidated-dashboard-fix.js.gz                  10.70kb / gzip: 3.07kb
[09:19:49.562] dist//vercel/path0/elegant-assistant-configuration.js.gz             9.23kb / gzip: 3.19kb
[09:19:49.562] dist//vercel/path0/emergency-app-loader.js.gz                        10.15kb / gzip: 3.28kb
[09:19:49.562] dist//vercel/path0/emergency-api-key-fix.js.gz                       6.75kb / gzip: 1.98kb
[09:19:49.562] dist//vercel/path0/enhanced-dashboard-integration.js.gz              8.65kb / gzip: 2.40kb
[09:19:49.562] dist//vercel/path0/enhanced-framer-fix.js.gz                         9.42kb / gzip: 2.49kb
[09:19:49.562] dist//vercel/path0/environment-mismatch-test.js.gz                   14.40kb / gzip: 3.59kb
[09:19:49.562] dist//vercel/path0/error-loop-fix.js.gz                              10.30kb / gzip: 3.12kb
[09:19:49.562] dist//vercel/path0/definitive-auth-fix.js.gz                         7.47kb / gzip: 2.34kb
[09:19:49.562] dist//vercel/path0/final-label-fix.js.gz                             6.36kb / gzip: 1.82kb
[09:19:49.562] dist//vercel/path0/deployment-safe-fix.js.gz                         21.31kb / gzip: 4.15kb
[09:19:49.562] dist//vercel/path0/find-label-issue.js.gz                            7.99kb / gzip: 2.28kb
[09:19:49.562] dist//vercel/path0/enhance-attorney-manager.js.gz                    19.42kb / gzip: 3.73kb
[09:19:49.562] dist//vercel/path0/final-supabase-fix.js.gz                          2.69kb / gzip: 1.14kb
[09:19:49.562] dist//vercel/path0/fix-agent-tab.js.gz                               7.52kb / gzip: 2.16kb
[09:19:49.563] dist//vercel/path0/fix-api-errors.js.gz                              9.37kb / gzip: 2.17kb
[09:19:49.563] dist//vercel/path0/fix-api-base-url.js.gz                            2.36kb / gzip: 0.86kb
[09:19:49.563] dist//vercel/path0/fix-assistant-creation-loop.js.gz                 7.63kb / gzip: 1.93kb
[09:19:49.563] dist//vercel/path0/fix-agent-tab-logo-restoration.js.gz              10.90kb / gzip: 2.07kb
[09:19:49.563] dist//vercel/path0/fix-attorney-creation.js.gz                       17.17kb / gzip: 3.76kb
[09:19:49.563] dist//vercel/path0/disable-framer-motion.js.gz                       5.37kb / gzip: 1.52kb
[09:19:49.563] dist//vercel/path0/fix-attorney-loading-issues.js.gz                 8.56kb / gzip: 2.59kb
[09:19:49.563] dist//vercel/path0/fix-attorney-id-v2.js.gz                          11.45kb / gzip: 2.65kb
[09:19:49.563] dist//vercel/path0/fix-attorney-persistence-enhanced.js.gz           9.75kb / gzip: 2.59kb
[09:19:49.563] dist//vercel/path0/fix-attorney-state-manager.js.gz                  13.10kb / gzip: 2.08kb
[09:19:49.564] dist//vercel/path0/fix-attorney-undefined.js.gz                      4.30kb / gzip: 1.14kb
[09:19:49.564] dist//vercel/path0/fix-attorney-persistence.js.gz                    10.35kb / gzip: 2.21kb
[09:19:49.564] dist//vercel/path0/fix-attorney-validation.js.gz                     5.62kb / gzip: 1.80kb
[09:19:49.564] dist//vercel/path0/fix-attorney-id-v2-override.js.gz                 2.39kb / gzip: 1.03kb
[09:19:49.564] dist//vercel/path0/fix-auth-state.js.gz                              4.96kb / gzip: 1.27kb
[09:19:49.564] dist//vercel/path0/fix-banner-removal-persistence.js.gz              10.55kb / gzip: 2.83kb
[09:19:49.565] dist//vercel/path0/fix-banner-upload-interface.js.gz                 11.16kb / gzip: 2.75kb
[09:19:49.565] dist//vercel/path0/fix-configure-button.js.gz                        4.60kb / gzip: 1.40kb
[09:19:49.565] dist//vercel/path0/fix-cors-csp-banner-issues.js.gz                  12.18kb / gzip: 3.00kb
[09:19:49.565] dist//vercel/path0/fix-csp-eval-blocking.js.gz                       8.22kb / gzip: 2.13kb
[09:19:49.565] dist//vercel/path0/fix-damon-assistant-conflict.js.gz                4.05kb / gzip: 1.30kb
[09:19:49.565] dist//vercel/path0/fix-dashboard-saving.js.gz                        7.67kb / gzip: 2.06kb
[09:19:49.565] dist//vercel/path0/fix-enhance-attorney-manager.js.gz                4.78kb / gzip: 1.08kb
[09:19:49.565] dist//vercel/path0/fix-call-start.js.gz                              7.82kb / gzip: 2.54kb
[09:19:49.565] dist//vercel/path0/fix-banner-functionality.js.gz                    9.46kb / gzip: 2.44kb
[09:19:49.565] dist//vercel/path0/fix-form-interactions.js.gz                       9.52kb / gzip: 2.45kb
[09:19:49.569] dist//vercel/path0/fix-call-termination.js.gz                        11.58kb / gzip: 3.16kb
[09:19:49.570] dist//vercel/path0/fix-input-fields.js.gz                            5.89kb / gzip: 1.69kb
[09:19:49.571] dist//vercel/path0/fix-infinite-initialization.js.gz                 6.64kb / gzip: 1.58kb
[09:19:49.571] dist//vercel/path0/fix-key-confusion.js.gz                           7.14kb / gzip: 2.21kb
[09:19:49.571] dist//vercel/path0/fix-label-input-mismatch.js.gz                    9.09kb / gzip: 2.42kb
[09:19:49.571] dist//vercel/path0/fix-invalid-jwt-claim.js.gz                       5.14kb / gzip: 1.56kb
[09:19:49.571] dist//vercel/path0/fix-loading-flicker.js.gz                         5.69kb / gzip: 1.52kb
[09:19:49.571] dist//vercel/path0/fix-mcp-connection.js.gz                          6.93kb / gzip: 1.86kb
[09:19:49.571] dist//vercel/path0/definitive-env-fix.js.gz                          9.70kb / gzip: 2.69kb
[09:19:49.571] dist//vercel/path0/fix-motion-context.js.gz                          3.13kb / gzip: 0.84kb
[09:19:49.571] dist//vercel/path0/fix-react-context-timeout.js.gz                   7.83kb / gzip: 1.62kb
[09:19:49.571] dist//vercel/path0/fix-qr-component.js.gz                            10.66kb / gzip: 2.44kb
[09:19:49.571] dist//vercel/path0/fix-s-function.js.gz                              9.65kb / gzip: 2.50kb
[09:19:49.571] dist//vercel/path0/fix-standalone-attorney-manager.js.gz             10.93kb / gzip: 2.66kb
[09:19:49.571] dist//vercel/path0/fix-supabase-storage.js.gz                        3.64kb / gzip: 1.09kb
[09:19:49.572] dist//vercel/path0/fix-three-globe.js.gz                             3.79kb / gzip: 0.98kb
[09:19:49.572] dist//vercel/path0/fix-validate-attorney-data.js.gz                  6.25kb / gzip: 1.74kb
[09:19:49.572] dist//vercel/path0/fix-vapi-assistant-config.js.gz                   2.82kb / gzip: 1.04kb
[09:19:49.572] dist//vercel/path0/fix-vapi-assistant-switch.js.gz                   7.38kb / gzip: 1.71kb
[09:19:49.572] dist//vercel/path0/fix-s-catch.js.gz                                 5.32kb / gzip: 1.67kb
[09:19:49.572] dist//vercel/path0/fix-supabase-client.js.gz                         3.42kb / gzip: 0.99kb
[09:19:49.572] dist//vercel/path0/fix-websocket-connection.js.gz                    2.23kb / gzip: 0.75kb
[09:19:49.572] dist//vercel/path0/fix-vapi-mcp-connection.js.gz                     4.68kb / gzip: 1.45kb
[09:19:49.572] dist//vercel/path0/force-loading-fix.js.gz                           7.71kb / gzip: 2.24kb
[09:19:49.572] dist//vercel/path0/force-new-assistant.js.gz                         7.29kb / gzip: 1.92kb
[09:19:49.572] dist//vercel/path0/framer-test.html.gz                               1.47kb / gzip: 0.60kb
[09:19:49.572] dist//vercel/path0/fix-dom-manipulation-errors.js.gz                 10.30kb / gzip: 2.64kb
[09:19:49.572] dist//vercel/path0/framer-motion-polyfill.js.gz                      1.26kb / gzip: 0.54kb
[09:19:49.572] dist//vercel/path0/headers-fix.js.gz                                 2.71kb / gzip: 1.04kb
[09:19:49.572] dist//vercel/path0/immediate-label-fix.js.gz                         5.81kb / gzip: 1.72kb
[09:19:49.572] dist//vercel/path0/index-fixed-promise.html.gz                       2.82kb / gzip: 1.18kb
[09:19:49.572] dist//vercel/path0/immediate-diagnostic.js.gz                        8.94kb / gzip: 2.78kb
[09:19:49.573] dist//vercel/path0/index-fixed-s-catch.html.gz                       5.07kb / gzip: 1.64kb
[09:19:49.573] dist//vercel/path0/global-attorney-coordinator.js.gz                 14.50kb / gzip: 3.42kb
[09:19:49.573] dist//vercel/path0/index-fixed.html.gz                               11.83kb / gzip: 3.03kb
[09:19:49.573] dist//vercel/path0/inject-env-vars.js.gz                             1.71kb / gzip: 0.61kb
[09:19:49.573] dist//vercel/path0/interaction-fix.js.gz                             3.83kb / gzip: 1.15kb
[09:19:49.573] dist//vercel/path0/layout-group-context-patch.js.gz                  2.75kb / gzip: 0.93kb
[09:19:49.573] dist//vercel/path0/manual-label-check.js.gz                          3.97kb / gzip: 1.31kb
[09:19:49.573] dist//vercel/path0/fix-vapi-auth.js.gz                               8.35kb / gzip: 2.69kb
[09:19:49.573] dist//vercel/path0/patch-framer.js.gz                                6.26kb / gzip: 1.49kb
[09:19:49.573] dist//vercel/path0/launch.html.gz                                    2.05kb / gzip: 0.85kb
[09:19:49.573] dist//vercel/path0/patch-layout-group-context.js.gz                  3.19kb / gzip: 1.05kb
[09:19:49.573] dist//vercel/path0/dev-csp-fix.js.gz                                 1.27kb / gzip: 0.54kb
[09:19:49.573] dist//vercel/path0/production-diagnostic.js.gz                       11.79kb / gzip: 3.45kb
[09:19:49.573] dist//vercel/path0/local-vs-production-comparison.js.gz              13.93kb / gzip: 3.64kb
[09:19:49.573] dist//vercel/path0/production-environment-validator.js.gz            5.29kb / gzip: 1.73kb
[09:19:49.573] dist//vercel/path0/production-framer-fix.js.gz                       3.87kb / gzip: 1.10kb
[09:19:49.573] dist//vercel/path0/production-signin-fix.js.gz                       7.96kb / gzip: 2.26kb
[09:19:49.573] dist//vercel/path0/production-cors-fix.js.gz                         10.94kb / gzip: 3.36kb
[09:19:49.573] dist//vercel/path0/production-eval-fix.js.gz                         7.52kb / gzip: 2.28kb
[09:19:49.574] dist//vercel/path0/production-diagnostic.html.gz                     10.69kb / gzip: 3.04kb
[09:19:49.574] dist//vercel/path0/purge-assistants.js.gz                            5.32kb / gzip: 2.11kb
[09:19:49.574] dist//vercel/path0/index.html.gz                                     25.45kb / gzip: 6.56kb
[09:19:49.574] dist//vercel/path0/reset-to-home.js.gz                               1.17kb / gzip: 0.55kb
[09:19:49.574] dist//vercel/path0/react-initialization-test.js.gz                   10.70kb / gzip: 3.20kb
[09:19:49.574] dist//vercel/path0/reset.html.gz                                     3.85kb / gzip: 1.33kb
[09:19:49.574] dist//vercel/path0/quick-test.js.gz                                  2.94kb / gzip: 1.08kb
[09:19:49.574] dist//vercel/path0/react-context-fix.js.gz                           3.66kb / gzip: 1.04kb
[09:19:49.574] dist//vercel/path0/routing-diagnostic.js.gz                          6.47kb / gzip: 1.90kb
[09:19:49.574] dist//vercel/path0/self-healing-csp.js.gz                            10.46kb / gzip: 3.12kb
[09:19:49.574] dist//vercel/path0/production-test.html.gz                           19.52kb / gzip: 4.72kb
[09:19:49.574] dist//vercel/path0/set-test-subdomain.js.gz                          2.15kb / gzip: 0.69kb
[09:19:49.574] dist//vercel/path0/robust-state-handler.js.gz                        26.64kb / gzip: 6.45kb
[09:19:49.574] dist//vercel/path0/csp-directive-auto-fixer.js.gz                    10.57kb / gzip: 2.72kb
[09:19:49.574] dist//vercel/path0/simple-csp-test.html.gz                           11.57kb / gzip: 2.88kb
[09:19:49.574] dist//vercel/path0/simple-test.js.gz                                 4.49kb / gzip: 1.29kb
[09:19:49.574] dist//vercel/path0/simple-production-test.js.gz                      10.86kb / gzip: 2.75kb
[09:19:49.574] dist//vercel/path0/simplified-vapi-integration.js.gz                 8.82kb / gzip: 1.98kb
[09:19:49.575] dist//vercel/path0/speech-particles.js.gz                            21.31kb / gzip: 6.02kb
[09:19:49.575] dist//vercel/path0/streamlined-sync-integration.js.gz                7.74kb / gzip: 2.20kb
[09:19:49.575] dist//vercel/path0/standalone-attorney-manager.js.gz                 23.62kb / gzip: 5.24kb
[09:19:49.575] dist//vercel/path0/simplified-attorney-manager.js.gz                 8.93kb / gzip: 2.30kb
[09:19:49.575] dist//vercel/path0/supabase-connection-test.js.gz                    11.13kb / gzip: 3.29kb
[09:19:49.575] dist//vercel/path0/sync-button.js.gz                                 6.22kb / gzip: 1.66kb
[09:19:49.575] dist//vercel/path0/sync-tools-test.html.gz                           8.45kb / gzip: 1.76kb
[09:19:49.575] dist//vercel/path0/sync-tools-fix.js.gz                              11.53kb / gzip: 2.39kb
[09:19:49.575] dist//vercel/path0/test-ai-meta-mcp.html.gz                          7.34kb / gzip: 1.71kb
[09:19:49.575] dist//vercel/path0/test-api-fix.html.gz                              7.81kb / gzip: 2.18kb
[09:19:49.575] dist//vercel/path0/test-assistant-id-propagation.js.gz               5.33kb / gzip: 1.60kb
[09:19:49.575] dist//vercel/path0/test-assistant-propagation.html.gz                13.16kb / gzip: 3.16kb
[09:19:49.575] dist//vercel/path0/test-attorney-creation.html.gz                    11.45kb / gzip: 2.81kb
[09:19:49.575] dist//vercel/path0/test-attorney-fix.js.gz                           3.65kb / gzip: 0.92kb
[09:19:49.575] dist//vercel/path0/standalone-attorney-manager-fixed.js.gz           42.04kb / gzip: 8.46kb
[09:19:49.575] dist//vercel/path0/test-attorney-validation.html.gz                  4.09kb / gzip: 1.17kb
[09:19:49.576] dist//vercel/path0/test-auth-fix.html.gz                             9.77kb / gzip: 2.68kb
[09:19:49.576] dist//vercel/path0/test-auto-refresh-fix.html.gz                     8.83kb / gzip: 2.35kb
[09:19:49.576] dist//vercel/path0/test-bug-report.html.gz                           8.83kb / gzip: 2.00kb
[09:19:49.579] dist//vercel/path0/test-attorney-validation.js.gz                    3.27kb / gzip: 0.72kb
[09:19:49.579] dist//vercel/path0/test-call-functionality.js.gz                     12.74kb / gzip: 3.60kb
[09:19:49.579] dist//vercel/path0/supabase-realtime-sync.js.gz                      5.28kb / gzip: 1.30kb
[09:19:49.579] dist//vercel/path0/test-clean-auth.html.gz                           8.67kb / gzip: 2.39kb
[09:19:49.579] dist//vercel/path0/test-csp-vapi-fix.html.gz                         10.25kb / gzip: 2.85kb
[09:19:49.579] dist//vercel/path0/test-current-state.js.gz                          3.93kb / gzip: 1.18kb
[09:19:49.579] dist//vercel/path0/simple-test.html.gz                               8.35kb / gzip: 2.36kb
[09:19:49.580] dist//vercel/path0/test-bug-reporter.html.gz                         6.72kb / gzip: 2.03kb
[09:19:49.580] dist//vercel/path0/test-elegant-flow.js.gz                           6.87kb / gzip: 2.26kb
[09:19:49.580] dist//vercel/path0/test-conflict-resolution.js.gz                    2.71kb / gzip: 1.02kb
[09:19:49.580] dist//vercel/path0/test-error-loop-fix.html.gz                       9.84kb / gzip: 2.72kb
[09:19:49.580] dist//vercel/path0/test-framer-fix.js.gz                             1.27kb / gzip: 0.36kb
[09:19:49.580] dist//vercel/path0/test-label-fix.html.gz                            6.15kb / gzip: 1.71kb
[09:19:49.580] dist//vercel/path0/test-network-vapi.html.gz                         13.32kb / gzip: 3.11kb
[09:19:49.580] dist//vercel/path0/test-phone-numbers.html.gz                        8.10kb / gzip: 2.11kb
[09:19:49.580] dist//vercel/path0/test-robust-state-handler.js.gz                   5.28kb / gzip: 1.30kb
[09:19:49.580] dist//vercel/path0/test-production-signin.html.gz                    9.77kb / gzip: 2.52kb
[09:19:49.580] dist//vercel/path0/test-three-fixed.html.gz                          5.81kb / gzip: 1.24kb
[09:19:49.580] dist//vercel/path0/test-three-simple.html.gz                         2.50kb / gzip: 0.89kb
[09:19:49.580] dist//vercel/path0/test-vapi-connection.js.gz                        3.00kb / gzip: 1.06kb
[09:19:49.580] dist//vercel/path0/test-three.html.gz                                1.63kb / gzip: 0.62kb
[09:19:49.580] dist//vercel/path0/test-vapi-fix.html.gz                             9.63kb / gzip: 2.46kb
[09:19:49.580] dist//vercel/path0/test-website-import.html.gz                       18.41kb / gzip: 4.21kb
[09:19:49.580] dist//vercel/path0/test.html.gz                                      1.06kb / gzip: 0.59kb
[09:19:49.580] dist//vercel/path0/unified-banner-fix.js.gz                          9.76kb / gzip: 2.57kb
[09:19:49.580] dist//vercel/path0/unified-attorney-manager.js.gz                    13.17kb / gzip: 3.17kb
[09:19:49.580] dist//vercel/path0/test-attorney-persistence.html.gz                 11.78kb / gzip: 2.52kb
[09:19:49.580] dist//vercel/path0/preview-controls-fix.js.gz                        14.45kb / gzip: 3.59kb
[09:19:49.580] dist//vercel/path0/fix-profile-tab.js.gz                             2.17kb / gzip: 0.82kb
[09:19:49.580] dist//vercel/path0/vapi-config-test.html.gz                          11.31kb / gzip: 2.46kb
[09:19:49.580] dist//vercel/path0/fix-promise-catch.js.gz                           14.86kb / gzip: 3.04kb
[09:19:49.580] dist//vercel/path0/vapi-mcp-test.html.gz                             7.09kb / gzip: 1.82kb
[09:19:49.581] dist//vercel/path0/test-dropdown-fix.html.gz                         9.82kb / gzip: 2.76kb
[09:19:49.581] dist//vercel/path0/vapi-sdk-loader.js.gz                             6.43kb / gzip: 2.06kb
[09:19:49.581] dist//vercel/path0/vapi-test.html.gz                                 11.14kb / gzip: 2.55kb
[09:19:49.581] dist//vercel/path0/vercel-framer-fix.js.gz                           5.64kb / gzip: 1.61kb
[09:19:49.581] dist//vercel/path0/verify-conflict-fix.js.gz                         3.27kb / gzip: 1.14kb
[09:19:49.581] dist//vercel/path0/test-csp-fixes.html.gz                            15.09kb / gzip: 3.00kb
[09:19:49.581] dist//vercel/path0/production-debug-vapi.js.gz                       10.18kb / gzip: 2.68kb
[09:19:49.581] dist//vercel/path0/test-damonandlaurakost-scenario.js.gz             7.25kb / gzip: 2.11kb
[09:19:49.581] dist//vercel/path0/fix-profile-editing.js.gz                         6.01kb / gzip: 1.70kb
[09:19:49.581] dist//vercel/path0/vapi-official-pattern-fix.js.gz                   8.89kb / gzip: 2.46kb
[09:19:49.581] dist//vercel/path0/assets/css/components-dashboard-c664c223.css.gz   202.38kb / gzip: 31.30kb
[09:19:49.581] dist//vercel/path0/assets/css/pages-db60df18.css.gz                  128.17kb / gzip: 21.77kb
[09:19:49.581] dist//vercel/path0/vapi-assistant-cleanup.js.gz                      13.57kb / gzip: 3.38kb
[09:19:49.581] dist//vercel/path0/url-submit-fix.js.gz                              2.75kb / gzip: 1.05kb
[09:19:49.581] dist//vercel/path0/vapi-diagnostics.html.gz                          18.94kb / gzip: 4.37kb
[09:19:49.581] dist//vercel/path0/fix-s-catch-standalone.js.gz                      14.72kb / gzip: 2.90kb
[09:19:49.581] dist//vercel/path0/vapi-call-diagnostics.js.gz                       11.96kb / gzip: 3.61kb
[09:19:49.581] dist//vercel/path0/stats.html.gz                                     523.12kb / gzip: 75.85kb
[09:19:49.581] 
[09:19:49.581] 
[09:19:50.173] 
[09:19:50.174] ✨ [vite-plugin-compression]:algorithm=brotliCompress - compressed file successfully: 
[09:19:50.174] dist//vercel/path0/_preload.html.br                                  1.48kb / brotliCompress: 0.39kb
[09:19:50.174] dist//vercel/path0/api/health/index.html.br                          1.95kb / brotliCompress: 0.46kb
[09:19:50.174] dist//vercel/path0/agent-configure-button.js.br                      3.96kb / brotliCompress: 1.00kb
[09:19:50.174] dist//vercel/path0/aggressive-fix.js.br                              4.67kb / brotliCompress: 0.94kb
[09:19:50.174] dist//vercel/path0/agent-tab-debug-loop.js.br                        10.84kb / brotliCompress: 2.67kb
[09:19:50.174] dist//vercel/path0/assets/css/components-vapi-ee190643.css.br        31.88kb / brotliCompress: 5.18kb
[09:19:50.174] dist//vercel/path0/assets/css/index-6f6d4b70.css.br                  36.66kb / brotliCompress: 6.68kb
[09:19:50.174] dist//vercel/path0/assets/css/components-common-426af7a6.css.br      48.78kb / brotliCompress: 8.14kb
[09:19:50.174] dist//vercel/path0/assets/css/vendor-maps-69420918.css.br            15.28kb / brotliCompress: 5.60kb
[09:19:50.174] dist//vercel/path0/assistant-dropdown-diagnostic.js.br               6.56kb / brotliCompress: 1.41kb
[09:19:50.175] dist//vercel/path0/assets/css/vendor-react-5e8f3fb6.css.br           13.82kb / brotliCompress: 2.26kb
[09:19:50.175] dist//vercel/path0/attorney-profile-fix.js.br                        3.77kb / brotliCompress: 0.81kb
[09:19:50.175] dist//vercel/path0/attorney-state-initializer.js.br                  6.51kb / brotliCompress: 1.65kb
[09:19:50.175] dist//vercel/path0/attorney-bridge.js.br                             13.25kb / brotliCompress: 2.42kb
[09:19:50.175] dist//vercel/path0/attorney-state-context-fix.js.br                  3.30kb / brotliCompress: 0.75kb
[09:19:50.175] dist//vercel/path0/auth-context-fix.js.br                            3.04kb / brotliCompress: 0.65kb
[09:19:50.175] dist//vercel/path0/attorney-state-manager-fix.js.br                  5.57kb / brotliCompress: 0.82kb
[09:19:50.175] dist//vercel/path0/attorney-sync-helper.js.br                        9.23kb / brotliCompress: 1.94kb
[09:19:50.175] dist//vercel/path0/auto-configure-direct-fix.js.br                   5.79kb / brotliCompress: 1.27kb
[09:19:50.175] dist//vercel/path0/auth-diagnostic.js.br                             5.82kb / brotliCompress: 1.37kb
[09:19:50.175] dist//vercel/path0/auto-configure-fix.js.br                          12.49kb / brotliCompress: 2.60kb
[09:19:50.175] dist//vercel/path0/black-screen-detective.js.br                      13.52kb / brotliCompress: 3.21kb
[09:19:50.175] dist//vercel/path0/clean-auth-solution.js.br                         7.02kb / brotliCompress: 1.79kb
[09:19:50.175] dist//vercel/path0/call-sync-diagnostic.js.br                        9.98kb / brotliCompress: 2.20kb
[09:19:50.175] dist//vercel/path0/clear-invalid-attorney-data.js.br                 3.01kb / brotliCompress: 0.80kb
[09:19:50.176] dist//vercel/path0/auto-diagnostic-runner.js.br                      13.80kb / brotliCompress: 3.20kb
[09:19:50.176] dist//vercel/path0/comparison-test.html.br                           12.38kb / brotliCompress: 2.67kb
[09:19:50.176] dist//vercel/path0/comprehensive-csp-fix.js.br                       8.24kb / brotliCompress: 2.00kb
[09:19:50.176] dist//vercel/path0/comprehensive-csp-manager.js.br                   13.79kb / brotliCompress: 2.96kb
[09:19:50.177] dist//vercel/path0/comprehensive-diagnostic-suite.js.br              12.17kb / brotliCompress: 2.61kb
[09:19:50.177] dist//vercel/path0/consolidated-dashboard-fix.js.br                  10.70kb / brotliCompress: 2.54kb
[09:19:50.177] dist//vercel/path0/console-test.js.br                                6.93kb / brotliCompress: 1.61kb
[09:19:50.177] dist//vercel/path0/critical-fixes-v2.js.br                           4.16kb / brotliCompress: 1.10kb
[09:19:50.177] dist//vercel/path0/controlled-assistant-creation.js.br               6.35kb / brotliCompress: 1.48kb
[09:19:50.177] dist//vercel/path0/critical-production-fix.js.br                     9.00kb / brotliCompress: 2.18kb
[09:19:50.177] dist//vercel/path0/csp-diagnostic.js.br                              7.66kb / brotliCompress: 1.76kb
[09:19:50.178] dist//vercel/path0/csp-directive-auto-fixer.js.br                    10.57kb / brotliCompress: 2.34kb
[09:19:50.178] dist//vercel/path0/dashboard-loading-interceptor.js.br               6.23kb / brotliCompress: 1.37kb
[09:19:50.178] dist//vercel/path0/dashboard-iframe-manager.js.br                    8.16kb / brotliCompress: 2.00kb
[09:19:50.178] dist//vercel/path0/dashboard-diagnostic.js.br                        9.64kb / brotliCompress: 2.15kb
[09:19:50.179] dist//vercel/path0/debug-labels.js.br                                4.19kb / brotliCompress: 1.13kb
[09:19:50.179] dist//vercel/path0/debug-email-state.js.br                           7.12kb / brotliCompress: 1.77kb
[09:19:50.179] dist//vercel/path0/debug-subdomain.html.br                           10.12kb / brotliCompress: 2.23kb
[09:19:50.179] dist//vercel/path0/definitive-auth-fix.js.br                         7.47kb / brotliCompress: 1.96kb
[09:19:50.179] dist//vercel/path0/definitive-env-fix.js.br                          9.70kb / brotliCompress: 2.28kb
[09:19:50.180] dist//vercel/path0/deployment-attorney-fix.js.br                     13.96kb / brotliCompress: 2.55kb
[09:19:50.180] dist//vercel/path0/dev-csp-fix.js.br                                 1.27kb / brotliCompress: 0.43kb
[09:19:50.180] dist//vercel/path0/deployment-sync-button.js.br                      7.79kb / brotliCompress: 1.64kb
[09:19:50.180] dist//vercel/path0/direct-fix/MotionConfigContext.mjs.br             1.02kb / brotliCompress: 0.33kb
[09:19:50.181] dist//vercel/path0/direct-patch/LayoutGroupContext.mjs.br            1.00kb / brotliCompress: 0.33kb
[09:19:50.181] dist//vercel/path0/direct-patch/MotionConfigContext.mjs.br           1.08kb / brotliCompress: 0.36kb
[09:19:50.181] dist//vercel/path0/diagnose-call-issue.js.br                         7.42kb / brotliCompress: 1.94kb
[09:19:50.182] dist//vercel/path0/deployment-safe-fix.js.br                         21.31kb / brotliCompress: 3.50kb
[09:19:50.182] dist//vercel/path0/direct-test.js.br                                 7.02kb / brotliCompress: 1.69kb
[09:19:50.183] dist//vercel/path0/disable-conflicting-scripts.js.br                 2.96kb / brotliCompress: 0.88kb
[09:19:50.183] dist//vercel/path0/disable-standalone-attorney-manager.js.br         1.96kb / brotliCompress: 0.39kb
[09:19:50.183] dist//vercel/path0/disable-framer-motion.js.br                       5.37kb / brotliCompress: 1.25kb
[09:19:50.183] dist//vercel/path0/dom-ready-fix.js.br                               8.93kb / brotliCompress: 1.88kb
[09:19:50.184] dist//vercel/path0/elegant-assistant-configuration.js.br             9.23kb / brotliCompress: 2.58kb
[09:19:50.184] dist//vercel/path0/emergency-app-loader.js.br                        10.15kb / brotliCompress: 2.68kb
[09:19:50.184] dist//vercel/path0/elegant-assistant-creation.js.br                  11.07kb / brotliCompress: 2.87kb
[09:19:50.184] dist//vercel/path0/enhanced-framer-fix.js.br                         9.42kb / brotliCompress: 2.04kb
[09:19:50.185] dist//vercel/path0/emergency-api-key-fix.js.br                       6.75kb / brotliCompress: 1.68kb
[09:19:50.185] dist//vercel/path0/enhanced-dashboard-integration.js.br              8.65kb / brotliCompress: 1.97kb
[09:19:50.185] dist//vercel/path0/final-label-fix.js.br                             6.36kb / brotliCompress: 1.47kb
[09:19:50.185] dist//vercel/path0/final-supabase-fix.js.br                          2.69kb / brotliCompress: 0.95kb
[09:19:50.185] dist//vercel/path0/environment-mismatch-test.js.br                   14.40kb / brotliCompress: 3.07kb
[09:19:50.185] dist//vercel/path0/error-loop-fix.js.br                              10.30kb / brotliCompress: 2.64kb
[09:19:50.185] dist//vercel/path0/fix-agent-tab.js.br                               7.52kb / brotliCompress: 1.82kb
[09:19:50.185] dist//vercel/path0/find-label-issue.js.br                            7.99kb / brotliCompress: 1.91kb
[09:19:50.186] dist//vercel/path0/fix-api-base-url.js.br                            2.36kb / brotliCompress: 0.72kb
[09:19:50.186] dist//vercel/path0/fix-agent-tab-logo-restoration.js.br              10.90kb / brotliCompress: 1.74kb
[09:19:50.186] dist//vercel/path0/fix-assistant-creation-loop.js.br                 7.63kb / brotliCompress: 1.61kb
[09:19:50.186] dist//vercel/path0/enhance-attorney-manager.js.br                    19.42kb / brotliCompress: 3.16kb
[09:19:50.186] dist//vercel/path0/fix-attorney-id-v2-override.js.br                 2.39kb / brotliCompress: 0.82kb
[09:19:50.186] dist//vercel/path0/fix-attorney-id-v2.js.br                          11.45kb / brotliCompress: 2.21kb
[09:19:50.186] dist//vercel/path0/fix-api-errors.js.br                              9.37kb / brotliCompress: 1.82kb
[09:19:50.186] dist//vercel/path0/fix-attorney-persistence-enhanced.js.br           9.75kb / brotliCompress: 2.23kb
[09:19:50.186] dist//vercel/path0/fix-attorney-loading-issues.js.br                 8.56kb / brotliCompress: 2.14kb
[09:19:50.186] dist//vercel/path0/fix-attorney-persistence.js.br                    10.35kb / brotliCompress: 1.90kb
[09:19:50.186] dist//vercel/path0/fix-attorney-validation.js.br                     5.62kb / brotliCompress: 1.52kb
[09:19:50.187] dist//vercel/path0/fix-attorney-creation.js.br                       17.17kb / brotliCompress: 3.16kb
[09:19:50.187] dist//vercel/path0/fix-attorney-undefined.js.br                      4.30kb / brotliCompress: 0.91kb
[09:19:50.187] dist//vercel/path0/fix-attorney-state-manager.js.br                  13.10kb / brotliCompress: 1.75kb
[09:19:50.187] dist//vercel/path0/fix-auth-state.js.br                              4.96kb / brotliCompress: 1.05kb
[09:19:50.187] dist//vercel/path0/fix-banner-functionality.js.br                    9.46kb / brotliCompress: 2.04kb
[09:19:50.187] dist//vercel/path0/fix-banner-removal-persistence.js.br              10.55kb / brotliCompress: 2.42kb
[09:19:50.187] dist//vercel/path0/fix-call-start.js.br                              7.82kb / brotliCompress: 2.06kb
[09:19:50.187] dist//vercel/path0/fix-configure-button.js.br                        4.60kb / brotliCompress: 1.09kb
[09:19:50.187] dist//vercel/path0/fix-banner-upload-interface.js.br                 11.16kb / brotliCompress: 2.30kb
[09:19:50.187] dist//vercel/path0/fix-damon-assistant-conflict.js.br                4.05kb / brotliCompress: 1.11kb
[09:19:50.187] dist//vercel/path0/fix-call-termination.js.br                        11.58kb / brotliCompress: 2.68kb
[09:19:50.187] dist//vercel/path0/fix-csp-eval-blocking.js.br                       8.22kb / brotliCompress: 1.76kb
[09:19:50.187] dist//vercel/path0/fix-dashboard-saving.js.br                        7.67kb / brotliCompress: 1.75kb
[09:19:50.187] dist//vercel/path0/fix-cors-csp-banner-issues.js.br                  12.18kb / brotliCompress: 2.53kb
[09:19:50.188] dist//vercel/path0/fix-enhance-attorney-manager.js.br                4.78kb / brotliCompress: 0.87kb
[09:19:50.188] dist//vercel/path0/fix-dom-manipulation-errors.js.br                 10.30kb / brotliCompress: 2.21kb
[09:19:50.188] dist//vercel/path0/fix-form-interactions.js.br                       9.52kb / brotliCompress: 2.03kb
[09:19:50.188] dist//vercel/path0/fix-input-fields.js.br                            5.89kb / brotliCompress: 1.35kb
[09:19:50.188] dist//vercel/path0/fix-invalid-jwt-claim.js.br                       5.14kb / brotliCompress: 1.35kb
[09:19:50.188] dist//vercel/path0/fix-infinite-initialization.js.br                 6.64kb / brotliCompress: 1.34kb
[09:19:50.188] dist//vercel/path0/fix-key-confusion.js.br                           7.14kb / brotliCompress: 1.88kb
[09:19:50.188] dist//vercel/path0/fix-label-input-mismatch.js.br                    9.09kb / brotliCompress: 2.07kb
[09:19:50.188] dist//vercel/path0/fix-loading-flicker.js.br                         5.69kb / brotliCompress: 1.30kb
[09:19:50.188] dist//vercel/path0/fix-motion-context.js.br                          3.13kb / brotliCompress: 0.67kb
[09:19:50.188] dist//vercel/path0/fix-mcp-connection.js.br                          6.93kb / brotliCompress: 1.56kb
[09:19:50.188] dist//vercel/path0/fix-profile-tab.js.br                             2.17kb / brotliCompress: 0.63kb
[09:19:50.189] dist//vercel/path0/fix-profile-editing.js.br                         6.01kb / brotliCompress: 1.35kb
[09:19:50.189] dist//vercel/path0/fix-react-context-timeout.js.br                   7.83kb / brotliCompress: 1.33kb
[09:19:50.189] dist//vercel/path0/fix-qr-component.js.br                            10.66kb / brotliCompress: 2.03kb
[09:19:50.189] dist//vercel/path0/fix-promise-catch.js.br                           14.86kb / brotliCompress: 2.53kb
[09:19:50.189] dist//vercel/path0/fix-s-catch.js.br                                 5.32kb / brotliCompress: 1.38kb
[09:19:50.189] dist//vercel/path0/fix-supabase-client.js.br                         3.42kb / brotliCompress: 0.81kb
[09:19:50.189] dist//vercel/path0/fix-s-catch-standalone.js.br                      14.72kb / brotliCompress: 2.38kb
[09:19:50.190] dist//vercel/path0/fix-s-function.js.br                              9.65kb / brotliCompress: 2.11kb
[09:19:50.190] dist//vercel/path0/fix-supabase-storage.js.br                        3.64kb / brotliCompress: 0.91kb
[09:19:50.190] dist//vercel/path0/fix-standalone-attorney-manager.js.br             10.93kb / brotliCompress: 2.24kb
[09:19:50.190] dist//vercel/path0/fix-vapi-assistant-config.js.br                   2.82kb / brotliCompress: 0.86kb
[09:19:50.191] dist//vercel/path0/fix-validate-attorney-data.js.br                  6.25kb / brotliCompress: 1.47kb
[09:19:50.191] dist//vercel/path0/fix-three-globe.js.br                             3.79kb / brotliCompress: 0.81kb
[09:19:50.191] dist//vercel/path0/fix-vapi-mcp-connection.js.br                     4.68kb / brotliCompress: 1.22kb
[09:19:50.191] dist//vercel/path0/fix-websocket-connection.js.br                    2.23kb / brotliCompress: 0.59kb
[09:19:50.191] dist//vercel/path0/framer-motion-polyfill.js.br                      1.26kb / brotliCompress: 0.42kb
[09:19:50.191] dist//vercel/path0/fix-vapi-auth.js.br                               8.35kb / brotliCompress: 2.21kb
[09:19:50.191] dist//vercel/path0/fix-vapi-assistant-switch.js.br                   7.38kb / brotliCompress: 1.46kb
[09:19:50.191] dist//vercel/path0/framer-test.html.br                               1.47kb / brotliCompress: 0.42kb
[09:19:50.191] dist//vercel/path0/force-loading-fix.js.br                           7.71kb / brotliCompress: 1.82kb
[09:19:50.191] dist//vercel/path0/headers-fix.js.br                                 2.71kb / brotliCompress: 0.84kb
[09:19:50.191] dist//vercel/path0/force-new-assistant.js.br                         7.29kb / brotliCompress: 1.63kb
[09:19:50.192] dist//vercel/path0/index-fixed-promise.html.br                       2.82kb / brotliCompress: 0.90kb
[09:19:50.192] dist//vercel/path0/immediate-label-fix.js.br                         5.81kb / brotliCompress: 1.41kb
[09:19:50.192] dist//vercel/path0/global-attorney-coordinator.js.br                 14.50kb / brotliCompress: 2.96kb
[09:19:50.192] dist//vercel/path0/immediate-diagnostic.js.br                        8.94kb / brotliCompress: 2.23kb
[09:19:50.192] dist//vercel/path0/inject-env-vars.js.br                             1.71kb / brotliCompress: 0.52kb
[09:19:50.192] dist//vercel/path0/index-fixed-s-catch.html.br                       5.07kb / brotliCompress: 1.30kb
[09:19:50.192] dist//vercel/path0/launch.html.br                                    2.05kb / brotliCompress: 0.56kb
[09:19:50.192] dist//vercel/path0/interaction-fix.js.br                             3.83kb / brotliCompress: 0.92kb
[09:19:50.192] dist//vercel/path0/layout-group-context-patch.js.br                  2.75kb / brotliCompress: 0.75kb
[09:19:50.192] dist//vercel/path0/index-fixed.html.br                               11.83kb / brotliCompress: 2.45kb
[09:19:50.192] dist//vercel/path0/manual-label-check.js.br                          3.97kb / brotliCompress: 1.08kb
[09:19:50.192] dist//vercel/path0/patch-framer.js.br                                6.26kb / brotliCompress: 1.22kb
[09:19:50.193] dist//vercel/path0/patch-layout-group-context.js.br                  3.19kb / brotliCompress: 0.82kb
[09:19:50.193] dist//vercel/path0/local-vs-production-comparison.js.br              13.93kb / brotliCompress: 3.08kb
[09:19:50.193] dist//vercel/path0/index.html.br                                     25.45kb / brotliCompress: 5.39kb
[09:19:50.193] dist//vercel/path0/production-cors-fix.js.br                         10.94kb / brotliCompress: 2.78kb
[09:19:50.193] dist//vercel/path0/production-debug-vapi.js.br                       10.18kb / brotliCompress: 2.24kb
[09:19:50.193] dist//vercel/path0/preview-controls-fix.js.br                        14.45kb / brotliCompress: 2.92kb
[09:19:50.193] dist//vercel/path0/production-diagnostic.html.br                     10.69kb / brotliCompress: 2.43kb
[09:19:50.193] dist//vercel/path0/production-environment-validator.js.br            5.29kb / brotliCompress: 1.46kb
[09:19:50.193] dist//vercel/path0/production-diagnostic.js.br                       11.79kb / brotliCompress: 2.93kb
[09:19:50.193] dist//vercel/path0/production-eval-fix.js.br                         7.52kb / brotliCompress: 1.90kb
[09:19:50.193] dist//vercel/path0/production-framer-fix.js.br                       3.87kb / brotliCompress: 0.87kb
[09:19:50.193] dist//vercel/path0/purge-assistants.js.br                            5.32kb / brotliCompress: 1.71kb
[09:19:50.193] dist//vercel/path0/quick-test.js.br                                  2.94kb / brotliCompress: 0.82kb
[09:19:50.193] dist//vercel/path0/production-signin-fix.js.br                       7.96kb / brotliCompress: 1.88kb
[09:19:50.194] dist//vercel/path0/reset-to-home.js.br                               1.17kb / brotliCompress: 0.44kb
[09:19:50.194] dist//vercel/path0/reset.html.br                                     3.85kb / brotliCompress: 0.98kb
[09:19:50.194] dist//vercel/path0/react-context-fix.js.br                           3.66kb / brotliCompress: 0.85kb
[09:19:50.194] dist//vercel/path0/react-initialization-test.js.br                   10.70kb / brotliCompress: 2.70kb
[09:19:50.195] dist//vercel/path0/routing-diagnostic.js.br                          6.47kb / brotliCompress: 1.61kb
[09:19:50.195] dist//vercel/path0/production-test.html.br                           19.52kb / brotliCompress: 3.88kb
[09:19:50.195] dist//vercel/path0/set-test-subdomain.js.br                          2.15kb / brotliCompress: 0.59kb
[09:19:50.195] dist//vercel/path0/self-healing-csp.js.br                            10.46kb / brotliCompress: 2.65kb
[09:19:50.195] dist//vercel/path0/simple-csp-test.html.br                           11.57kb / brotliCompress: 2.33kb
[09:19:50.195] dist//vercel/path0/simple-test.html.br                               8.35kb / brotliCompress: 1.86kb
[09:19:50.195] dist//vercel/path0/simple-production-test.js.br                      10.86kb / brotliCompress: 2.31kb
[09:19:50.195] dist//vercel/path0/simple-test.js.br                                 4.49kb / brotliCompress: 1.05kb
[09:19:50.195] dist//vercel/path0/robust-state-handler.js.br                        26.64kb / brotliCompress: 5.46kb
[09:19:50.195] dist//vercel/path0/simplified-vapi-integration.js.br                 8.82kb / brotliCompress: 1.67kb
[09:19:50.195] dist//vercel/path0/simplified-attorney-manager.js.br                 8.93kb / brotliCompress: 1.91kb
[09:19:50.195] dist//vercel/path0/speech-particles.js.br                            21.31kb / brotliCompress: 5.14kb
[09:19:50.195] dist//vercel/path0/standalone-attorney-manager.js.br                 23.62kb / brotliCompress: 4.54kb
[09:19:50.196] dist//vercel/path0/streamlined-sync-integration.js.br                7.74kb / brotliCompress: 1.86kb
[09:19:50.196] dist//vercel/path0/sync-button.js.br                                 6.22kb / brotliCompress: 1.34kb
[09:19:50.196] dist//vercel/path0/supabase-connection-test.js.br                    11.13kb / brotliCompress: 2.79kb
[09:19:50.196] dist//vercel/path0/supabase-realtime-sync.js.br                      5.28kb / brotliCompress: 1.09kb
[09:19:50.196] dist//vercel/path0/standalone-attorney-manager-fixed.js.br           42.04kb / brotliCompress: 7.23kb
[09:19:50.196] dist//vercel/path0/test-api-fix.html.br                              7.81kb / brotliCompress: 1.74kb
[09:19:50.196] dist//vercel/path0/sync-tools-fix.js.br                              11.53kb / brotliCompress: 2.03kb
[09:19:50.196] dist//vercel/path0/sync-tools-test.html.br                           8.45kb / brotliCompress: 1.38kb
[09:19:50.196] dist//vercel/path0/test-assistant-id-propagation.js.br               5.33kb / brotliCompress: 1.33kb
[09:19:50.196] dist//vercel/path0/test-ai-meta-mcp.html.br                          7.34kb / brotliCompress: 1.32kb
[09:19:50.196] dist//vercel/path0/test-attorney-fix.js.br                           3.65kb / brotliCompress: 0.75kb
[09:19:50.196] dist//vercel/path0/test-assistant-propagation.html.br                13.16kb / brotliCompress: 2.54kb
[09:19:50.196] dist//vercel/path0/test-attorney-creation.html.br                    11.45kb / brotliCompress: 2.31kb
[09:19:50.196] dist//vercel/path0/test-attorney-validation.html.br                  4.09kb / brotliCompress: 0.86kb
[09:19:50.196] dist//vercel/path0/test-attorney-validation.js.br                    3.27kb / brotliCompress: 0.59kb
[09:19:50.196] dist//vercel/path0/test-attorney-persistence.html.br                 11.78kb / brotliCompress: 2.04kb
[09:19:50.196] dist//vercel/path0/test-auth-fix.html.br                             9.77kb / brotliCompress: 2.17kb
[09:19:50.196] dist//vercel/path0/test-auto-refresh-fix.html.br                     8.83kb / brotliCompress: 1.83kb
[09:19:50.197] dist//vercel/path0/test-bug-report.html.br                           8.83kb / brotliCompress: 1.56kb
[09:19:50.197] dist//vercel/path0/test-bug-reporter.html.br                         6.72kb / brotliCompress: 1.57kb
[09:19:50.197] dist//vercel/path0/test-conflict-resolution.js.br                    2.71kb / brotliCompress: 0.87kb
[09:19:50.197] dist//vercel/path0/test-call-functionality.js.br                     12.74kb / brotliCompress: 3.09kb
[09:19:50.197] dist//vercel/path0/test-clean-auth.html.br                           8.67kb / brotliCompress: 1.88kb
[09:19:50.197] dist//vercel/path0/test-current-state.js.br                          3.93kb / brotliCompress: 1.00kb
[09:19:50.197] dist//vercel/path0/test-csp-fixes.html.br                            15.09kb / brotliCompress: 2.42kb
[09:19:50.197] dist//vercel/path0/test-damonandlaurakost-scenario.js.br             7.25kb / brotliCompress: 1.76kb
[09:19:50.197] dist//vercel/path0/test-elegant-flow.js.br                           6.87kb / brotliCompress: 1.89kb
[09:19:50.197] dist//vercel/path0/test-csp-vapi-fix.html.br                         10.25kb / brotliCompress: 2.34kb
[09:19:50.197] dist//vercel/path0/test-dropdown-fix.html.br                         9.82kb / brotliCompress: 2.20kb
[09:19:50.197] dist//vercel/path0/test-framer-fix.js.br                             1.27kb / brotliCompress: 0.29kb
[09:19:50.197] dist//vercel/path0/test-label-fix.html.br                            6.15kb / brotliCompress: 1.28kb
[09:19:50.197] dist//vercel/path0/test-error-loop-fix.html.br                       9.84kb / brotliCompress: 2.17kb
[09:19:50.197] dist//vercel/path0/test-phone-numbers.html.br                        8.10kb / brotliCompress: 1.66kb
[09:19:50.198] dist//vercel/path0/test-network-vapi.html.br                         13.32kb / brotliCompress: 2.49kb
[09:19:50.198] dist//vercel/path0/test-production-signin.html.br                    9.77kb / brotliCompress: 2.03kb
[09:19:50.198] dist//vercel/path0/test-three-fixed.html.br                          5.81kb / brotliCompress: 0.92kb
[09:19:50.199] dist//vercel/path0/test-robust-state-handler.js.br                   5.28kb / brotliCompress: 1.08kb
[09:19:50.199] dist//vercel/path0/test-three.html.br                                1.63kb / brotliCompress: 0.42kb
[09:19:50.199] dist//vercel/path0/test-three-simple.html.br                         2.50kb / brotliCompress: 0.63kb
[09:19:50.199] dist//vercel/path0/test.html.br                                      1.06kb / brotliCompress: 0.37kb
[09:19:50.199] dist//vercel/path0/test-vapi-connection.js.br                        3.00kb / brotliCompress: 0.88kb
[09:19:50.199] dist//vercel/path0/test-vapi-fix.html.br                             9.63kb / brotliCompress: 1.99kb
[09:19:50.199] dist//vercel/path0/unified-attorney-manager.js.br                    13.17kb / brotliCompress: 2.67kb
[09:19:50.199] dist//vercel/path0/url-submit-fix.js.br                              2.75kb / brotliCompress: 0.82kb
[09:19:50.199] dist//vercel/path0/unified-banner-fix.js.br                          9.76kb / brotliCompress: 2.16kb
[09:19:50.199] dist//vercel/path0/test-website-import.html.br                       18.41kb / brotliCompress: 3.42kb
[09:19:50.199] dist//vercel/path0/vapi-call-diagnostics.js.br                       11.96kb / brotliCompress: 3.00kb
[09:19:50.199] dist//vercel/path0/vapi-config-test.html.br                          11.31kb / brotliCompress: 1.97kb
[09:19:50.199] dist//vercel/path0/vapi-assistant-cleanup.js.br                      13.57kb / brotliCompress: 2.86kb
[09:19:50.199] dist//vercel/path0/vapi-mcp-test.html.br                             7.09kb / brotliCompress: 1.42kb
[09:19:50.199] dist//vercel/path0/vapi-official-pattern-fix.js.br                   8.89kb / brotliCompress: 2.07kb
[09:19:50.199] dist//vercel/path0/vapi-sdk-loader.js.br                             6.43kb / brotliCompress: 1.72kb
[09:19:50.200] dist//vercel/path0/verify-conflict-fix.js.br                         3.27kb / brotliCompress: 0.95kb
[09:19:50.200] dist//vercel/path0/vapi-diagnostics.html.br                          18.94kb / brotliCompress: 3.59kb
[09:19:50.200] dist//vercel/path0/assets/css/pages-db60df18.css.br                  128.17kb / brotliCompress: 18.54kb
[09:19:50.200] dist//vercel/path0/vapi-test.html.br                                 11.14kb / brotliCompress: 2.02kb
[09:19:50.200] dist//vercel/path0/assets/css/components-dashboard-c664c223.css.br   202.38kb / brotliCompress: 25.34kb
[09:19:50.200] dist//vercel/path0/vercel-framer-fix.js.br                           5.64kb / brotliCompress: 1.32kb
[09:19:50.200] dist//vercel/path0/assets/css/components-maps-4bf1ccba.css.br        15.67kb / brotliCompress: 3.03kb
[09:19:50.201] dist//vercel/path0/stats.html.br                                     523.12kb / brotliCompress: 59.24kb
[09:19:50.201] 
[09:19:50.201] 