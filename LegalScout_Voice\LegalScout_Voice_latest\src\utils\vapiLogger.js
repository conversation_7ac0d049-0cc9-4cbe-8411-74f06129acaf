/**
 * Enhanced Vapi Logger
 * 
 * Provides structured logging specifically for Vapi operations with clean,
 * readable output and debugging capabilities.
 */

// Configuration
const VAPI_LOGGER_CONFIG = {
  enabled: true,
  logLevel: 'info', // 'error', 'warn', 'info', 'debug', 'verbose'
  includeTimestamp: true,
  includeStackTrace: false,
  maxLogEntries: 100,
  // Filter out sensitive data from logs
  sensitiveFields: ['api_key', 'secret', 'token', 'password', 'authorization']
};

// In-memory log storage for debugging
const logHistory = [];

/**
 * Sanitize data to remove sensitive information
 * @param {any} data - Data to sanitize
 * @returns {any} - Sanitized data
 */
const sanitizeData = (data) => {
  if (!data || typeof data !== 'object') return data;
  
  if (Array.isArray(data)) {
    return data.map(sanitizeData);
  }
  
  const sanitized = {};
  for (const [key, value] of Object.entries(data)) {
    const lowerKey = key.toLowerCase();
    const isSensitive = VAPI_LOGGER_CONFIG.sensitiveFields.some(field => 
      lowerKey.includes(field)
    );
    
    if (isSensitive && typeof value === 'string') {
      sanitized[key] = value.substring(0, 8) + '...';
    } else if (typeof value === 'object') {
      sanitized[key] = sanitizeData(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

/**
 * Format log entry with consistent structure
 * @param {string} level - Log level
 * @param {string} component - Component name
 * @param {string} message - Log message
 * @param {any} data - Additional data
 * @returns {Object} - Formatted log entry
 */
const formatLogEntry = (level, component, message, data = null) => {
  const entry = {
    timestamp: new Date().toISOString(),
    level: level.toUpperCase(),
    component,
    message,
    data: data ? sanitizeData(data) : null
  };
  
  // Store in history
  logHistory.push(entry);
  if (logHistory.length > VAPI_LOGGER_CONFIG.maxLogEntries) {
    logHistory.shift();
  }
  
  return entry;
};

/**
 * Create a logger for a specific component
 * @param {string} component - Component name
 * @returns {Object} - Logger instance
 */
export const createVapiLogger = (component) => {
  const log = (level, message, data = null) => {
    if (!VAPI_LOGGER_CONFIG.enabled) return;
    
    const entry = formatLogEntry(level, component, message, data);
    const prefix = `[${entry.component}]`;
    const timestamp = VAPI_LOGGER_CONFIG.includeTimestamp ? 
      `[${entry.timestamp.split('T')[1].split('.')[0]}]` : '';
    
    const logMessage = `${timestamp} ${prefix} ${message}`;
    
    switch (level) {
      case 'error':
        console.error(logMessage, data || '');
        break;
      case 'warn':
        console.warn(logMessage, data || '');
        break;
      case 'info':
        console.info(logMessage, data || '');
        break;
      case 'debug':
        console.debug(logMessage, data || '');
        break;
      case 'verbose':
        console.log(logMessage, data || '');
        break;
      default:
        console.log(logMessage, data || '');
    }
  };
  
  return {
    error: (message, data) => log('error', message, data),
    warn: (message, data) => log('warn', message, data),
    info: (message, data) => log('info', message, data),
    debug: (message, data) => log('debug', message, data),
    verbose: (message, data) => log('verbose', message, data),
    
    // Specialized logging methods for Vapi operations
    assistantCreated: (assistant) => {
      log('info', 'Assistant created successfully', {
        id: assistant.id,
        name: assistant.name,
        model: assistant.model?.model,
        voice: assistant.voice?.voiceId
      });
    },
    
    assistantVerified: (assistant) => {
      log('info', 'Assistant verified in Vapi', {
        id: assistant.id,
        name: assistant.name
      });
    },
    
    assistantNotFound: (assistantId) => {
      log('warn', 'Assistant not found in Vapi', { assistantId });
    },
    
    connectionEstablished: (method) => {
      log('info', 'Vapi connection established', { method });
    },
    
    connectionFailed: (error, fallback) => {
      log('error', 'Vapi connection failed', { 
        error: error.message,
        fallback: fallback || 'none'
      });
    },
    
    syncStarted: (attorneyId, firmName) => {
      log('info', 'Starting Vapi sync for attorney', { attorneyId, firmName });
    },
    
    syncCompleted: (result) => {
      log('info', 'Vapi sync completed', {
        status: result.vapiSyncStatus,
        assistantId: result.vapi_assistant_id,
        assistantName: result.vapiAssistantName
      });
    },
    
    syncFailed: (error) => {
      log('error', 'Vapi sync failed', { error: error.message });
    },
    
    mockModeActivated: (reason) => {
      log('warn', 'Vapi mock mode activated', { reason });
    },
    
    fieldMapping: (field, supabaseValue, vapiValue) => {
      log('debug', 'Field mapping', { 
        field, 
        supabase: supabaseValue, 
        vapi: vapiValue,
        synced: supabaseValue === vapiValue
      });
    }
  };
};

/**
 * Get the complete log history for debugging
 * @returns {Array} - Array of log entries
 */
export const getVapiLogHistory = () => {
  return [...logHistory];
};

/**
 * Clear the log history
 */
export const clearVapiLogHistory = () => {
  logHistory.length = 0;
};

/**
 * Configure the Vapi logger
 * @param {Object} config - Configuration options
 */
export const configureVapiLogger = (config) => {
  Object.assign(VAPI_LOGGER_CONFIG, config);
};

/**
 * Export logs as JSON for debugging
 * @returns {string} - JSON string of logs
 */
export const exportVapiLogs = () => {
  return JSON.stringify(logHistory, null, 2);
};

// Make logger available globally for debugging
if (typeof window !== 'undefined') {
  window.VapiLogger = {
    getHistory: getVapiLogHistory,
    clear: clearVapiLogHistory,
    export: exportVapiLogs,
    configure: configureVapiLogger
  };
}

// Default logger instance
export const vapiLogger = createVapiLogger('VapiCore');
