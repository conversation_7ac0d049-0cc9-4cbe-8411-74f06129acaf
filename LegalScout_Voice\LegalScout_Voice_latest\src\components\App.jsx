import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { getAttorneyConfig } from '../config/attorneys';
import Button from './Button.jsx';

const App = ({ attorneyProfile = {} }) => {
  const subdomain = (window.location.hostname.split('.')[0] || 'default').toLowerCase();
  const config = getAttorneyConfig(subdomain);
  const effectiveProfile = { ...config, ...attorneyProfile };

  // Detect theme from URL parameters or parent window
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const themeParam = urlParams.get('theme');

    // Set theme based on URL parameter or default to dark
    const theme = themeParam || 'dark';
    document.documentElement.setAttribute('data-theme', theme);

    // Also try to get theme from parent window if in iframe
    if (window !== window.parent) {
      try {
        const parentTheme = window.parent.document.documentElement.getAttribute('data-theme');
        if (parentTheme) {
          document.documentElement.setAttribute('data-theme', parentTheme);
        }
      } catch (e) {
        // Cross-origin restrictions, use URL param or default
        console.log('Could not access parent theme, using URL param or default');
      }
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>{effectiveProfile.firmName}</title>
      </Helmet>
      <header>
        <nav>
          <div className="logo-container">
            <img
              className="logo"
              src={effectiveProfile.logo}
              alt={`${effectiveProfile.firmName} Logo`}
            />
          </div>
          <div className="nav-links">
            <a href="https://legalscout.ai/dl/About">About</a>
          </div>
        </nav>
      </header>
      <main className="hero">
        <div className="statement">
          <p>
            <span>Use our free AI to be matched</span>
            <span>with an ideal human lawyer,</span>
            <span>knowledgeable about your case and eager to help.</span>
          </p>
          <div className="button-container">
            <Button
              label="Start Free Consultation"
              onClick={() => console.log('Button clicked')}
              mascot={effectiveProfile.mascot}
            />
          </div>
        </div>
      </main>
    </>
  );
};

export default App; 