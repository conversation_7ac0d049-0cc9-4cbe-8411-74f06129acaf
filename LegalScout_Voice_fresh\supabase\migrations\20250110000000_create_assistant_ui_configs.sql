-- Migration: Create assistant_ui_configs table for per-assistant UI settings
-- This enables attorneys to have different UI configurations for each assistant

-- Create assistant_ui_configs table for per-assistant UI settings
CREATE TABLE IF NOT EXISTS assistant_ui_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT NOT NULL,
  
  -- Basic Info
  firm_name TEXT,
  assistant_name TEXT, -- The agent name from Agent menu (titleText)
  logo_url TEXT,
  mascot_url TEXT,
  
  -- Colors and Styling
  primary_color TEXT DEFAULT '#2563eb',
  secondary_color TEXT DEFAULT '#1e40af',
  button_color TEXT DEFAULT '#3b82f6',
  background_color TEXT DEFAULT '#ffffff',
  background_opacity DECIMAL(3,2) DEFAULT 1.0,
  button_opacity DECIMAL(3,2) DEFAULT 1.0,
  practice_area_background_opacity DECIMAL(3,2) DEFAULT 0.1,
  text_background_color TEXT DEFAULT '#ffffff',
  
  -- Content
  practice_description TEXT,
  welcome_message TEXT,
  information_gathering TEXT,
  office_address TEXT,
  scheduling_link TEXT,
  practice_areas TEXT[], -- Array of practice areas
  
  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,
  voice_provider TEXT DEFAULT '11labs',
  voice_id TEXT DEFAULT 'sarah',
  ai_model TEXT DEFAULT 'gpt-4o',
  
  -- Custom Fields (JSON for flexibility)
  custom_fields JSONB DEFAULT '{}',
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one config per attorney-assistant pair
  UNIQUE(attorney_id, assistant_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS assistant_ui_configs_attorney_id_idx ON assistant_ui_configs (attorney_id);
CREATE INDEX IF NOT EXISTS assistant_ui_configs_assistant_id_idx ON assistant_ui_configs (assistant_id);
CREATE INDEX IF NOT EXISTS assistant_ui_configs_attorney_assistant_idx ON assistant_ui_configs (attorney_id, assistant_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_assistant_ui_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_assistant_ui_configs_updated_at_trigger
  BEFORE UPDATE ON assistant_ui_configs
  FOR EACH ROW
  EXECUTE FUNCTION update_assistant_ui_configs_updated_at();

-- Add RLS policies
ALTER TABLE assistant_ui_configs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access configs for their own attorney records
CREATE POLICY "Users can access their own assistant configs" ON assistant_ui_configs
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );

-- Add comments
COMMENT ON TABLE assistant_ui_configs IS 'Stores UI configuration settings per assistant for each attorney';
COMMENT ON COLUMN assistant_ui_configs.attorney_id IS 'Reference to the attorney who owns this configuration';
COMMENT ON COLUMN assistant_ui_configs.assistant_id IS 'Vapi assistant ID this configuration applies to';
COMMENT ON COLUMN assistant_ui_configs.custom_fields IS 'Flexible JSON storage for additional custom fields and settings';

-- Add current_assistant_id to attorneys table to track which assistant is currently selected
ALTER TABLE attorneys ADD COLUMN IF NOT EXISTS current_assistant_id TEXT;

-- Create index on current_assistant_id
CREATE INDEX IF NOT EXISTS attorneys_current_assistant_id_idx ON attorneys (current_assistant_id);

-- Add comment
COMMENT ON COLUMN attorneys.current_assistant_id IS 'Currently selected assistant ID for this attorney';

-- Migration function to copy existing attorney settings to assistant_ui_configs
CREATE OR REPLACE FUNCTION migrate_attorney_settings_to_assistant_configs()
RETURNS void AS $$
DECLARE
  attorney_record RECORD;
  config_exists BOOLEAN;
BEGIN
  -- Loop through all attorneys that have a vapi_assistant_id
  FOR attorney_record IN 
    SELECT * FROM attorneys 
    WHERE vapi_assistant_id IS NOT NULL 
    AND vapi_assistant_id != ''
  LOOP
    -- Check if config already exists for this attorney-assistant pair
    SELECT EXISTS(
      SELECT 1 FROM assistant_ui_configs 
      WHERE attorney_id = attorney_record.id 
      AND assistant_id = attorney_record.vapi_assistant_id
    ) INTO config_exists;
    
    -- Only create if it doesn't exist
    IF NOT config_exists THEN
      INSERT INTO assistant_ui_configs (
        attorney_id,
        assistant_id,
        firm_name,
        assistant_name,
        logo_url,
        mascot_url,
        primary_color,
        secondary_color,
        button_color,
        background_color,
        background_opacity,
        button_opacity,
        practice_area_background_opacity,
        text_background_color,
        practice_description,
        welcome_message,
        information_gathering,
        office_address,
        scheduling_link,
        practice_areas,
        vapi_instructions,
        vapi_context,
        voice_provider,
        voice_id,
        ai_model,
        custom_fields
      ) VALUES (
        attorney_record.id,
        attorney_record.vapi_assistant_id,
        attorney_record.firm_name,
        attorney_record.title_text, -- Use title_text as assistant_name
        attorney_record.logo_url,
        attorney_record.mascot_url,
        COALESCE(attorney_record.primary_color, '#2563eb'),
        COALESCE(attorney_record.secondary_color, '#1e40af'),
        COALESCE(attorney_record.button_color, '#3b82f6'),
        COALESCE(attorney_record.background_color, '#ffffff'),
        COALESCE(attorney_record.background_opacity, 1.0),
        COALESCE(attorney_record.button_opacity, 1.0),
        COALESCE(attorney_record.practice_area_background_opacity, 0.1),
        COALESCE(attorney_record.text_background_color, '#ffffff'),
        attorney_record.practice_description,
        attorney_record.welcome_message,
        attorney_record.information_gathering,
        attorney_record.office_address,
        attorney_record.scheduling_link,
        attorney_record.practice_areas,
        attorney_record.vapi_instructions,
        attorney_record.vapi_context,
        COALESCE(attorney_record.voice_provider, '11labs'),
        COALESCE(attorney_record.voice_id, 'sarah'),
        COALESCE(attorney_record.ai_model, 'gpt-4o'),
        COALESCE(attorney_record.custom_fields, '{}')
      );
      
      -- Set current_assistant_id to the migrated assistant
      UPDATE attorneys 
      SET current_assistant_id = attorney_record.vapi_assistant_id
      WHERE id = attorney_record.id;
      
      RAISE NOTICE 'Migrated settings for attorney % (%) with assistant %', 
        attorney_record.firm_name, attorney_record.id, attorney_record.vapi_assistant_id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run the migration
SELECT migrate_attorney_settings_to_assistant_configs();

-- Drop the migration function (no longer needed)
DROP FUNCTION migrate_attorney_settings_to_assistant_configs();
