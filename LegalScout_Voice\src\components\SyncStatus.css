/* SyncStatus.css */

.sync-status {
  background-color: var(--background-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.sync-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sync-status-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary, #333);
  font-weight: 600;
}

.sync-status-actions {
  display: flex;
  gap: 8px;
}

.sync-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.check-button {
  background-color: var(--secondary-color, #6c757d);
  color: white;
}

.check-button:hover {
  background-color: var(--secondary-color-dark, #5a6268);
}

.sync-button {
  background-color: var(--primary-color, #007bff);
  color: white;
}

.sync-button:hover {
  background-color: var(--primary-color-dark, #0069d9);
}

.sync-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.sync-status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sync-status-item {
  display: flex;
  align-items: center;
}

.sync-status-label {
  width: 120px;
  font-weight: 600;
  color: var(--text-secondary, #495057);
}

.sync-status-value {
  color: var(--text-primary, #212529);
}

.status-ok {
  color: var(--success-color, #28a745);
  font-weight: 600;
}

.status-error {
  color: var(--danger-color, #dc3545);
  font-weight: 600;
}

.sync-status-message {
  margin-top: 8px;
  padding: 8px;
  background-color: var(--background-color-tertiary, #e9ecef);
  border-radius: 4px;
  color: var(--text-secondary, #495057);
}

.sync-status-fixed {
  margin-top: 8px;
  padding: 8px;
  background-color: var(--success-background, #d4edda);
  border-radius: 4px;
  color: var(--success-text, #155724);
}

details {
  margin-top: 8px;
}

summary {
  cursor: pointer;
  color: var(--link-color, #0056b3);
}

pre {
  background-color: var(--background-color-secondary, #f8f9fa);
  padding: 8px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  max-height: 200px;
}

/* Dark theme adjustments */
.dark-theme .sync-status,
[data-theme="dark"] .sync-status {
  background-color: var(--background-color-secondary, #2a2a2a);
  border-color: var(--border-color, rgba(255, 255, 255, 0.1));
}

.dark-theme .sync-status-header h3,
[data-theme="dark"] .sync-status-header h3 {
  color: var(--text-primary, #e0e0e0);
}

.dark-theme .sync-status-label,
[data-theme="dark"] .sync-status-label {
  color: var(--text-secondary, #b0b0b0);
}

.dark-theme .sync-status-value,
[data-theme="dark"] .sync-status-value {
  color: var(--text-primary, #e0e0e0);
}

.dark-theme .sync-status-message,
[data-theme="dark"] .sync-status-message {
  background-color: var(--background-color-tertiary, #3a3a3a);
  color: var(--text-secondary, #b0b0b0);
}

.dark-theme pre,
[data-theme="dark"] pre {
  background-color: var(--background-color-tertiary, #3a3a3a);
  color: var(--text-primary, #e0e0e0);
}

/* Dashboard sync status container */
.dashboard-sync-status {
  padding: 16px;
  margin-bottom: 0;
}

.sync-status-loading {
  padding: 16px;
  background-color: var(--background-color-secondary, #f8f9fa);
  border-radius: 8px;
  color: var(--text-secondary, #6c757d);
  text-align: center;
}

/* Dark theme adjustments for loading state */
.dark-theme .sync-status-loading,
[data-theme="dark"] .sync-status-loading {
  background-color: var(--background-color-secondary, #2a2a2a);
  color: var(--text-secondary, #b0b0b0);
}

/* Compact sync status for header */
.dashboard-sync-status.compact {
  padding: 0;
  margin: 0;
}

.compact-sync-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.sync-status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.status-ok {
  background-color: var(--success-color, #28a745);
  box-shadow: 0 0 4px var(--success-color, #28a745);
}

.status-dot.status-error {
  background-color: var(--danger-color, #dc3545);
  box-shadow: 0 0 4px var(--danger-color, #dc3545);
}

.sync-status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.compact-sync-actions {
  display: flex;
  gap: 8px;
}

.compact-sync-button {
  background: transparent;
  border: none;
  color: var(--text-secondary, #6c757d);
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.compact-sync-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color, #007bff);
}

.compact-sync-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark theme adjustments for compact sync status */
.dark-theme .sync-status-text,
[data-theme="dark"] .sync-status-text {
  color: var(--text-primary, #e0e0e0);
}

.dark-theme .compact-sync-button,
[data-theme="dark"] .compact-sync-button {
  color: var(--text-secondary, #b0b0b0);
}

.dark-theme .compact-sync-button:hover,
[data-theme="dark"] .compact-sync-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--primary-color, #007bff);
}
