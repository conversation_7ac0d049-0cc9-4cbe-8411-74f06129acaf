import React, { useEffect, useState, useRef } from 'react';
import { PreviewInterface } from '../components/preview/PreviewInterface';
import { useLocation } from 'react-router-dom';
import { processImageUrl } from '../utils/imageStorage';

const PreviewPage = () => {
  // Default values
  const defaultValues = {
    firmName: 'Smith & Associates',
    logoUrl: '',
    backgroundColor: '#f5f5f5',
    backgroundOpacity: 0.8,
    primaryColor: '#4B74AA',
    secondaryColor: '#607D8B',
    buttonColor: '#3498db',
    welcomeMessage: 'Welcome to our virtual legal assistant.',
    informationGathering: 'To better assist you, I\'ll need a few details about your situation.',
    practiceDescription: 'We specialize in providing expert legal services across various practice areas.',
    practiceAreas: ['Personal Injury', 'Family Law', 'Criminal Defense'],
    state: 'California',
    attorneyName: '<PERSON>',
    firmNameAnimation: 'fadeIn',
    practiceAreaBackgroundOpacity: 0.1,
    textBackgroundColor: '#634C38',
    buttonText: 'Start Consultation',
    buttonOpacity: 1
  };

  // Reference to the container div
  const containerRef = useRef(null);

  // Get URL parameters
  const location = useLocation();
  const urlParams = new URLSearchParams(location.search);

  // Parse URL parameters with fallbacks to default values
  const theme = urlParams.get('theme') || 'light';
  const firmName = urlParams.get('firmName') || defaultValues.firmName;
  const logoUrlParam = urlParams.get('logoUrl') || defaultValues.logoUrl;
  // Process the logo URL to get the actual image data if it's an image ID
  const logoUrl = processImageUrl(logoUrlParam);
  const backgroundColor = urlParams.get('backgroundColor') || defaultValues.backgroundColor;
  const backgroundOpacity = parseFloat(urlParams.get('backgroundOpacity') || '0.8');
  const primaryColor = urlParams.get('primaryColor') || defaultValues.primaryColor;
  const secondaryColor = urlParams.get('secondaryColor') || defaultValues.secondaryColor;
  const buttonColor = urlParams.get('buttonColor') || defaultValues.buttonColor;
  const welcomeMessage = urlParams.get('welcomeMessage') || defaultValues.welcomeMessage;
  const informationGathering = urlParams.get('informationGathering') || defaultValues.informationGathering;
  const practiceDescription = urlParams.get('practiceDescription') || defaultValues.practiceDescription;
  const practiceAreasParam = urlParams.get('practiceAreas');
  const practiceAreas = practiceAreasParam ? practiceAreasParam.split(',') : defaultValues.practiceAreas;
  const state = urlParams.get('state') || defaultValues.state;
  const attorneyName = urlParams.get('attorneyName') || defaultValues.attorneyName;
  const firmNameAnimation = urlParams.get('firmNameAnimation') || defaultValues.firmNameAnimation;
  const practiceAreaBackgroundOpacity = parseFloat(urlParams.get('practiceAreaBackgroundOpacity') || '0.1');
  const textBackgroundColor = urlParams.get('textBackgroundColor') || defaultValues.textBackgroundColor;
  const buttonText = urlParams.get('buttonText') || defaultValues.buttonText;
  const buttonOpacity = parseFloat(urlParams.get('buttonOpacity') || '1');
  const isProduction = urlParams.get('production') === 'true' ||
                      window.location.hostname !== 'localhost' &&
                      window.location.hostname !== '127.0.0.1';
  const isCentered = urlParams.get('centered') === 'true';
  const isFullWidth = urlParams.get('fullWidth') === 'true';

  // State to track component mounting
  const [isMounted, setIsMounted] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [forceRender, setForceRender] = useState(false);

  // Handle potential JSON errors
  useEffect(() => {
    // Suppress subdomain config errors
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
      // If it's a subdomain config request, return empty data instead of erroring
      if (url && (typeof url === 'string') &&
          (url.includes('subdomain') || url.includes('config'))) {
        console.log('[PreviewPage] Intercepting config request to prevent errors:', url);
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({}),
          text: () => Promise.resolve("{}")
        });
      }

      // Otherwise proceed as normal
      return originalFetch(url, options);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  // Debug log on mount
  useEffect(() => {
    console.log('[PreviewPage] Component mounted, production mode:', isProduction);
    setIsMounted(true);

    // Error monitoring
    const originalConsoleError = console.error;
    console.error = (...args) => {
      setErrorMessage(args.join(' '));
      originalConsoleError.apply(console, args);
    };

    // Force a re-render after a delay to ensure content becomes visible
    if (isProduction) {
      const renderTimer = setTimeout(() => {
        console.log('[PreviewPage] Forcing re-render in production');
        setForceRender(true);
      }, 1000);

      return () => {
        clearTimeout(renderTimer);
        console.log('[PreviewPage] Component unmounting');
        console.error = originalConsoleError;
      };
    }

    return () => {
      console.log('[PreviewPage] Component unmounting');
      console.error = originalConsoleError;
    };
  }, [isProduction]);

  // Report height to parent iframe
  useEffect(() => {
    // Mark component as mounted
    setIsMounted(true);

    const reportHeight = () => {
      const height = document.documentElement.scrollHeight;
      if (window.parent && window !== window.parent) {
        try {
          console.log('[PreviewPage] Reporting height:', height);
          window.parent.postMessage({
            type: 'iframeHeight',
            height,
            source: 'previewPage'
          }, '*');
        } catch (error) {
          console.warn('[PreviewPage] Error posting message to parent:', error);
        }
      }
    };

    // Report height on load, on mount, and after a delay to ensure content is rendered
    window.addEventListener('load', reportHeight);
    reportHeight(); // Initial report

    // Report height multiple times to ensure it's captured correctly
    const initialReports = [50, 100, 500, 1000, 2000].map(delay =>
      setTimeout(reportHeight, delay)
    );

    // Set up an interval to report height periodically
    const interval = setInterval(reportHeight, 1000);

    return () => {
      window.removeEventListener('load', reportHeight);
      initialReports.forEach(timeout => clearTimeout(timeout));
      clearInterval(interval);
    };
  }, []);

  // Add a MutationObserver to detect DOM changes
  useEffect(() => {
    if (containerRef.current) {
      console.log('[PreviewPage] Setting up MutationObserver');

      // Create a MutationObserver to watch for changes to the DOM
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          // If nodes were removed, log this
          if (mutation.removedNodes.length > 0) {
            console.log('[PreviewPage] Nodes were removed from the DOM', mutation.removedNodes);
          }
        });
      });

      // Start observing the container for changes
      observer.observe(containerRef.current, {
        childList: true,
        subtree: true
      });

      return () => observer.disconnect();
    }
  }, [containerRef.current]);

  // Set the data-theme attribute on the root element
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  // Add persistent visuals to ensure rendering
  useEffect(() => {
    // Add CSS to ensure visibility and centering
    const style = document.createElement('style');
    style.textContent = `
      body, html {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        overflow-x: hidden !important;
        ${isCentered ? `
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        ` : ''}
        box-sizing: border-box !important;
      }

      .preview-container {
        min-height: 500px !important;
        visibility: visible !important;
        display: block !important;
        ${isCentered || isFullWidth ? `
          max-width: 100% !important;
          width: 100% !important;
          margin: 0 auto !important;
          left: 0 !important;
          right: 0 !important;
          box-sizing: border-box !important;
        ` : ''}
      }

      #preview-content {
        min-height: 500px !important;
        visibility: visible !important;
        display: block !important;
        ${isCentered || isFullWidth ? `
          max-width: 100% !important;
          width: 100% !important;
          margin: 0 auto !important;
          overflow-x: hidden !important;
          left: 0 !important;
          right: 0 !important;
          box-sizing: border-box !important;
        ` : ''}
      }

      /* Production-specific styles */
      ${isProduction ? `
        body {
          overflow: visible !important;
        }

        .welcome-title, .button-container, .consultation-button-direct, .content-container {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          z-index: 100 !important;
          position: relative !important;
          ${isCentered || isFullWidth ? `
            max-width: 100% !important;
            width: 100% !important;
            margin-left: auto !important;
            margin-right: auto !important;
            box-sizing: border-box !important;
            text-align: center !important;
          ` : ''}
        }

        .welcome-title {
          max-width: 100% !important;
          width: 100% !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          text-align: center !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }

        img, svg {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }

        /* Hide error messages */
        [style*="background-color: red"], [style*="background-color: rgba(255, 0, 0"], [style*="background-color:#ff0000"] {
          display: none !important;
        }
      ` : ''}
    `;
    document.head.appendChild(style);

    // Force content to be visible in production
    if (isProduction) {
      const visibilityInterval = setInterval(() => {
        // Find and force-show main content elements
        const elements = [
          '.preview-container',
          '#preview-content',
          '.content-container',
          '.welcome-title',
          '.button-container',
          '.consultation-button-direct'
        ];

        elements.forEach(selector => {
          const el = document.querySelector(selector);
          if (el) {
            el.style.display = 'block';
            el.style.visibility = 'visible';
            el.style.opacity = '1';

            // Center elements if needed
            if (isCentered) {
              el.style.maxWidth = '100%';
              el.style.width = '100%';
              el.style.margin = '0 auto';
              el.style.overflowX = 'hidden';
            }
          }
        });

        // Hide any error messages
        const errorElements = document.querySelectorAll('[style*="background-color: red"], [style*="background-color: rgba(255, 0, 0"]');
        errorElements.forEach(el => {
          el.style.display = 'none';
        });
      }, 500);

      return () => {
        clearInterval(visibilityInterval);
        document.head.removeChild(style);
      };
    }

    return () => {
      document.head.removeChild(style);
    };
  }, [isProduction, isCentered, isFullWidth]);

  // Use a more aggressive approach in production
  useEffect(() => {
    if (!isProduction) return;

    // In production, periodically check for elements that should be visible
    const productionFixInterval = setInterval(() => {
      console.log('[PreviewPage] Running production visibility fix');

      // Create a DOM mutation that forces a complete repaint
      const forceRepaintEl = document.createElement('div');
      forceRepaintEl.id = 'force-repaint';
      forceRepaintEl.style.position = 'fixed';
      forceRepaintEl.style.top = '0';
      forceRepaintEl.style.left = '0';
      forceRepaintEl.style.width = '1px';
      forceRepaintEl.style.height = '1px';
      forceRepaintEl.style.zIndex = '0';
      document.body.appendChild(forceRepaintEl);

      // Force repaint by causing a layout calculation
      const _ = forceRepaintEl.offsetHeight;

      // Remove the element
      document.body.removeChild(forceRepaintEl);
    }, 2000);

    return () => clearInterval(productionFixInterval);
  }, [isProduction]);

  return (
    <div
      ref={containerRef}
      className="min-h-screen preview-container"
      style={{
        minHeight: '800px',
        visibility: 'visible',
        display: 'block',
        position: 'relative',
        zIndex: 1
      }}
      data-production={isProduction ? 'true' : 'false'}
      data-force-render={forceRender ? 'true' : 'false'}
    >
      {/* Error display for debugging */}
      {errorMessage && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          background: 'red',
          color: 'white',
          padding: '5px',
          zIndex: 9999,
          fontSize: '12px'
        }}>
          Error: {errorMessage}
        </div>
      )}

      {/* Always render in production to avoid mounting issues */}
      {(isMounted || isProduction) && (
        <PreviewInterface
          firmName={firmName}
          logoUrl={logoUrl}
          backgroundColor={backgroundColor}
          backgroundOpacity={backgroundOpacity}
          primaryColor={primaryColor}
          secondaryColor={secondaryColor}
          buttonColor={buttonColor}
          welcomeMessage={welcomeMessage}
          informationGathering={informationGathering}
          practiceDescription={practiceDescription}
          practiceAreas={practiceAreas}
          state={state}
          attorneyName={attorneyName}
          firmNameAnimation={firmNameAnimation}
          practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
          textBackgroundColor={textBackgroundColor}
          buttonText={buttonText}
          buttonOpacity={buttonOpacity}
          theme={theme}
        />
      )}
    </div>
  );
};

export default PreviewPage;