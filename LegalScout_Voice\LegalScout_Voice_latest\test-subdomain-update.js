import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

console.log('🧪 Testing Subdomain Update Functionality');
console.log('==========================================');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSubdomainUpdate() {
  try {
    // Test 1: Check if we can connect to Supabase
    console.log('🔍 Testing Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('attorneys')
      .select('id, email, subdomain')
      .limit(1);
    
    if (testError) {
      console.error('❌ Supabase connection failed:', testError);
      return;
    }
    
    console.log('✅ Supabase connection successful');
    console.log('📊 Sample attorney data:', testData);
    
    // Test 2: Check assistant_subdomains table
    console.log('\n🔍 Testing assistant_subdomains table...');
    const { data: subdomainData, error: subdomainError } = await supabase
      .from('assistant_subdomains')
      .select('*')
      .limit(5);
    
    if (subdomainError) {
      console.error('❌ assistant_subdomains query failed:', subdomainError);
    } else {
      console.log('✅ assistant_subdomains table accessible');
      console.log('📊 Sample subdomain data:', subdomainData);
    }
    
    // Test 3: Try to update a subdomain (dry run)
    console.log('\n🔍 Testing subdomain update logic...');
    
    if (testData && testData.length > 0) {
      const attorney = testData[0];
      const testSubdomain = 'test-subdomain-' + Date.now();
      
      console.log(`📝 Would update attorney ${attorney.email} subdomain to: ${testSubdomain}`);
      
      // Don't actually update, just test the query structure
      console.log('✅ Subdomain update logic test passed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSubdomainUpdate();
