/**
 * Test the fixed assistant dropdown functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;
const VAPI_PRIVATE_KEY = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

console.log('🔧 Testing Assistant Dropdown Fix');
console.log('=================================');

// Mock the vapiMcpService for testing
const mockVapiMcpService = {
  async connect(apiKey) {
    console.log('📡 Mock MCP Service: Connecting with API key...');
    return true;
  },
  
  async listAssistants() {
    console.log('📋 Mock MCP Service: Listing assistants...');
    
    // Simulate the actual API call
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }
};

// Mock the VapiAssistantService class
class MockVapiAssistantService {
  constructor() {
    this.mcpApiKey = VAPI_PRIVATE_KEY;
    this.connected = false;
  }

  async ensureConnection() {
    if (!this.connected) {
      console.log('🔌 VapiAssistantService: Ensuring connection...');
      this.connected = await mockVapiMcpService.connect(this.mcpApiKey);
    }
    return this.connected;
  }

  // This is the FIXED version of getAllAssistants
  async getAllAssistants() {
    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();
      
      if (!connected) {
        console.warn('VapiAssistantService: Not connected to Vapi MCP server. Returning empty array.');
        return [];
      }

      const assistants = await mockVapiMcpService.listAssistants();
      return assistants || [];
    } catch (error) {
      console.error('Error getting all assistants:', error);
      return [];
    }
  }
}

// Mock the EnhancedAssistantDropdown logic
async function testEnhancedAssistantDropdown() {
  console.log('\n🧪 Testing EnhancedAssistantDropdown Logic');
  console.log('==========================================');

  // Mock attorney data
  const attorney = {
    id: '87756a2c-a398-43f2-889a-b8815684df71',
    email: '<EMAIL>',
    firm_name: 'LegalScout',
    subdomain: 'damon',
    vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
  };

  console.log('👤 Attorney data:', attorney);

  // Create the service instance
  const vapiAssistantService = new MockVapiAssistantService();

  try {
    console.log('\n📋 Step 1: Loading assistants...');
    const allAssistants = await vapiAssistantService.getAllAssistants();
    console.log(`✅ Found ${allAssistants.length} assistants`);

    console.log('\n🔍 Step 2: Filtering relevant assistants...');
    const relevantAssistants = allAssistants.filter(assistant => {
      const name = assistant.name?.toLowerCase() || '';
      const firmName = attorney.firm_name?.toLowerCase() || '';
      const subdomain = attorney.subdomain?.toLowerCase() || '';
      
      // Include assistants with attorney's subdomain
      if (name.includes(subdomain)) return true;
      
      // Include assistants with attorney's name variations
      if (name.includes('kost')) return true;
      if (name.includes('damon')) return true;
      
      // Include generic LegalScout assistants
      if (name.includes('legalscout')) return true;
      
      return false;
    });

    console.log(`✅ Filtered to ${relevantAssistants.length} relevant assistants`);

    console.log('\n📝 Step 3: Simulating dropdown population...');
    const dropdownOptions = [];
    
    // Add default option
    dropdownOptions.push({ value: '', text: 'Select Assistant' });
    
    // Add relevant assistants
    relevantAssistants.forEach(assistant => {
      dropdownOptions.push({
        value: assistant.id,
        text: assistant.name || `Assistant ${assistant.id.slice(0, 8)}`
      });
    });
    
    // Add create new option
    dropdownOptions.push({ value: 'create_new', text: 'Create New Assistant' });

    console.log(`✅ Dropdown would show ${dropdownOptions.length} options:`);
    dropdownOptions.forEach((option, index) => {
      const marker = option.value === attorney.vapi_assistant_id ? ' ← CURRENT' : '';
      console.log(`   ${index + 1}. "${option.text}" (${option.value})${marker}`);
    });

    // Test current assistant selection
    const currentAssistantOption = dropdownOptions.find(opt => opt.value === attorney.vapi_assistant_id);
    if (currentAssistantOption) {
      console.log(`\n✅ Current assistant "${currentAssistantOption.text}" would be selected`);
    } else {
      console.log(`\n⚠️ Current assistant "${attorney.vapi_assistant_id}" not found in dropdown options`);
    }

    return {
      success: true,
      totalAssistants: allAssistants.length,
      relevantAssistants: relevantAssistants.length,
      dropdownOptions: dropdownOptions.length,
      currentAssistantFound: !!currentAssistantOption
    };

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

async function runTest() {
  try {
    const result = await testEnhancedAssistantDropdown();
    
    console.log('\n🎯 TEST RESULTS');
    console.log('===============');
    
    if (result.success) {
      console.log('✅ PASS: Assistant dropdown logic works correctly');
      console.log(`   - Total assistants: ${result.totalAssistants}`);
      console.log(`   - Relevant assistants: ${result.relevantAssistants}`);
      console.log(`   - Dropdown options: ${result.dropdownOptions}`);
      console.log(`   - Current assistant found: ${result.currentAssistantFound}`);
      
      console.log('\n📝 CONCLUSION:');
      console.log('The fix to vapiAssistantService.getAllAssistants() is working correctly.');
      console.log('The dropdown should now populate with assistants in the browser.');
      
    } else {
      console.log('❌ FAIL: Assistant dropdown logic failed');
      console.log(`   Error: ${result.error}`);
    }
    
  } catch (error) {
    console.log(`\n❌ Test execution failed: ${error.message}`);
  }
}

// Run the test
runTest();
