#!/usr/bin/env node
/**
 * Get Assistant Prompt via Vapi MCP
 * 
 * This script retrieves the assistant details, including the prompt,
 * using the Vapi MCP API and the assistant ID.
 * 
 * Usage:
 *   node scripts/get-assistant-prompt.js [assistantId]
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get assistant ID from command line arguments or use default
const assistantId = process.argv[2] || 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865';

// Use the provided API key
const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';

console.log(`Retrieving assistant details for ID: ${assistantId}`);
console.log(`Using API Key: ${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`);

async function getAssistantPrompt() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-prompt-retriever',
      version: '1.0.0',
    });
    
    // Create SSE transport for connection to remote Vapi MCP server
    console.log('Creating SSE transport...');
    const sseUrl = new URL('https://mcp.vapi.ai/sse');
    console.log('SSE URL:', sseUrl.toString());
    
    const transport = new SSEClientTransport({
      url: sseUrl.toString(),
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    console.log('Connecting to Vapi MCP server via SSE...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');
    
    try {
      // Get assistant details
      console.log(`\nRetrieving assistant details for ID: ${assistantId}`);
      const assistantResponse = await mcpClient.callTool({
        name: 'get_assistant',
        arguments: {
          assistantId: assistantId
        },
      });
      
      if (assistantResponse && assistantResponse.content) {
        console.log('✅ Assistant details retrieved successfully');
        
        const assistant = assistantResponse.content;
        
        console.log('\n=== Assistant Details ===');
        console.log(`ID: ${assistant.id}`);
        console.log(`Name: ${assistant.name}`);
        console.log(`Description: ${assistant.description || 'No description'}`);
        console.log(`Created At: ${new Date(assistant.createdAt).toLocaleString()}`);
        
        // Extract and display the system prompt
        if (assistant.systemPrompt) {
          console.log('\n=== System Prompt ===');
          console.log(assistant.systemPrompt);
        } else if (assistant.model && assistant.model.messages) {
          // Look for system messages in the model configuration
          const systemMessages = assistant.model.messages.filter(msg => msg.role === 'system');
          if (systemMessages.length > 0) {
            console.log('\n=== System Messages ===');
            systemMessages.forEach((msg, index) => {
              console.log(`\n--- System Message ${index + 1} ---`);
              console.log(msg.content);
            });
          } else {
            console.log('\n❌ No system prompt or messages found');
          }
        } else {
          console.log('\n❌ No system prompt found');
        }
        
        // Display model information
        if (assistant.model) {
          console.log('\n=== Model Information ===');
          console.log(`Provider: ${assistant.model.provider || 'Not specified'}`);
          console.log(`Model: ${assistant.model.model || 'Not specified'}`);
          console.log(`Temperature: ${assistant.model.temperature || 'Not specified'}`);
        }
        
        // Display voice information
        if (assistant.voice) {
          console.log('\n=== Voice Information ===');
          console.log(`Provider: ${assistant.voice.provider || 'Not specified'}`);
          console.log(`Voice ID: ${assistant.voice.voiceId || 'Not specified'}`);
          console.log(`Speed: ${assistant.voice.speed || 'Not specified'}`);
        }
        
        // Display tools
        if (assistant.tools && assistant.tools.length > 0) {
          console.log('\n=== Tools ===');
          assistant.tools.forEach((tool, index) => {
            console.log(`\n--- Tool ${index + 1} ---`);
            console.log(`Name: ${tool.name || 'No name'}`);
            console.log(`Description: ${tool.description || 'No description'}`);
          });
        }
        
        // Save the full assistant details to a JSON file
        const fs = await import('fs');
        const path = await import('path');
        const outputDir = path.default.join(process.cwd(), 'output');
        
        // Create output directory if it doesn't exist
        if (!fs.default.existsSync(outputDir)) {
          fs.default.mkdirSync(outputDir, { recursive: true });
        }
        
        const outputFile = path.default.join(outputDir, `assistant-${assistantId}.json`);
        fs.default.writeFileSync(outputFile, JSON.stringify(assistant, null, 2));
        console.log(`\n✅ Full assistant details saved to: ${outputFile}`);
      } else {
        console.log('❌ Failed to retrieve assistant details');
        console.log('Response:', assistantResponse);
      }
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('✅ Disconnected');
    }
    
    console.log('\n✅ Operation completed successfully');
  } catch (error) {
    console.error('\n❌ Operation failed with error:', error);
    process.exit(1);
  }
}

getAssistantPrompt();
