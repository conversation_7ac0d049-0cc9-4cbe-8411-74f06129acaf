import React, { useState } from 'react';
import { runVapiCallDiagnostics, formatDiagnosticResults } from '../../utils/vapiCallDiagnostics';
import './VapiDiagnosticsPanel.css';

/**
 * Vapi Diagnostics Panel Component
 * 
 * Provides a UI for running Vapi call diagnostics and displaying results
 */
const VapiDiagnosticsPanel = ({ attorney }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults(null);
    
    try {
      const diagnosticResults = await runVapiCallDiagnostics(attorney);
      setResults(diagnosticResults);
    } catch (error) {
      console.error('Diagnostics failed:', error);
      setResults({
        timestamp: new Date().toISOString(),
        attorney: attorney?.id || 'unknown',
        tests: [{
          name: 'Diagnostics Runner',
          status: 'fail',
          message: `Diagnostics failed: ${error.message}`,
          details: { error: error.stack }
        }],
        summary: {
          passed: 0,
          failed: 1,
          warnings: 0,
          total: 1,
          successRate: 0,
          overallStatus: 'error'
        }
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return '✅';
      case 'warning': return '⚠️';
      case 'fail': return '❌';
      default: return '❓';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'fail': return '#F44336';
      default: return '#757575';
    }
  };

  return (
    <div className="vapi-diagnostics-panel">
      <div className="panel-header">
        <h3>Vapi Call Diagnostics</h3>
        <p>Test your Vapi configuration and call functionality</p>
      </div>

      <div className="diagnostics-controls">
        <button
          className="run-diagnostics-button"
          onClick={runDiagnostics}
          disabled={isRunning}
        >
          {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
        </button>
      </div>

      {results && (
        <div className="diagnostics-results">
          <div className="results-summary">
            <div className="summary-header">
              <h4>Diagnostic Results</h4>
              <span className={`overall-status ${results.summary.overallStatus}`}>
                {results.summary.overallStatus.toUpperCase()}
              </span>
            </div>
            
            <div className="summary-stats">
              <div className="stat">
                <span className="stat-label">Success Rate:</span>
                <span className="stat-value">{results.summary.successRate}%</span>
              </div>
              <div className="stat">
                <span className="stat-label">Tests:</span>
                <span className="stat-value">
                  {results.summary.passed} passed, {results.summary.failed} failed, {results.summary.warnings} warnings
                </span>
              </div>
              <div className="stat">
                <span className="stat-label">Timestamp:</span>
                <span className="stat-value">{new Date(results.timestamp).toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div className="test-results">
            <h5>Test Results</h5>
            {results.tests.map((test, index) => (
              <div key={index} className={`test-result ${test.status}`}>
                <div className="test-header">
                  <span className="test-icon">{getStatusIcon(test.status)}</span>
                  <span className="test-name">{test.name}</span>
                  <span className="test-status" style={{ color: getStatusColor(test.status) }}>
                    {test.status.toUpperCase()}
                  </span>
                </div>
                <div className="test-message">{test.message}</div>
                {test.details && (
                  <details className="test-details">
                    <summary>View Details</summary>
                    <pre>{JSON.stringify(test.details, null, 2)}</pre>
                  </details>
                )}
              </div>
            ))}
          </div>

          <div className="diagnostics-actions">
            <button
              className="toggle-details-button"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide' : 'Show'} Raw Results
            </button>
            
            <button
              className="copy-results-button"
              onClick={() => {
                const formattedResults = formatDiagnosticResults(results);
                navigator.clipboard.writeText(formattedResults);
                alert('Diagnostic results copied to clipboard');
              }}
            >
              Copy Results
            </button>
          </div>

          {showDetails && (
            <div className="raw-results">
              <h5>Raw Results</h5>
              <pre>{JSON.stringify(results, null, 2)}</pre>
            </div>
          )}
        </div>
      )}

      {!results && !isRunning && (
        <div className="no-results">
          <p>Click "Run Diagnostics" to test your Vapi configuration and identify any issues with call functionality.</p>
          
          <div className="diagnostic-info">
            <h5>What this test checks:</h5>
            <ul>
              <li>✅ API key configuration</li>
              <li>✅ MCP server connection</li>
              <li>✅ Assistant configuration</li>
              <li>✅ Phone number availability</li>
              <li>✅ API endpoint connectivity</li>
              <li>✅ Call creation parameters</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default VapiDiagnosticsPanel;
