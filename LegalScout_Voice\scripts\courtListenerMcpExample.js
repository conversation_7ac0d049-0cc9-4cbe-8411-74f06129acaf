/**
 * CourtListener MCP Tool Example
 * 
 * This script demonstrates how to register and use the CourtListener
 * citation lookup tool with the Vapi MCP server.
 * 
 * Usage:
 * 1. Set your CourtListener API key in the environment variable COURTLISTENER_API_KEY (optional)
 * 2. Run this script with: node scripts/courtListenerMcpExample.js
 */

const { McpServer } = require('@modelcontextprotocol/sdk/server/mcp.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { registerCourtListenerTool } = require('../src/tools/courtListenerMcpTool');

// Get API key from environment variable
const apiKey = process.env.COURTLISTENER_API_KEY || null;

// Create an MCP server
const server = new McpServer({
  name: "courtlistener-mcp-server",
  version: "1.0.0",
});

// Register the CourtListener tool with the server
registerCourtListenerTool(server, apiKey);

// Main function
async function main() {
  console.error("CourtListener MCP Server starting...");
  
  // Start receiving messages on stdin and sending messages on stdout
  const transport = new StdioServerTransport();
  await server.connect(transport);
  
  console.error("CourtListener MCP Server connected.");
}

// Start the server
main().catch(err => {
  console.error("Fatal error:", err);
  process.exit(1);
});
