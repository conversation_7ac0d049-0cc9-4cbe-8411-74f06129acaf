home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
index-0c8d8553.js:48 🔧 [Supabase] Creating client with direct credentials...
hook.js:608 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-0c8d8553.js:48:69816)
    at dG (index-0c8d8553.js:48:72276)
    at index-0c8d8553.js:48:72641
overrideMethod @ hook.js:608
index-0c8d8553.js:48 Attaching Supabase client to window.supabase
index-0c8d8553.js:100 [VapiLoader] Starting Vapi SDK loading process
index-0c8d8553.js:100 [VapiLoader] Attempting to import @vapi-ai/web package
index-0c8d8553.js:187 [VapiMcpService] Created clean fetch from iframe
index-0c8d8553.js:187 [VapiMcpService] INFO: Vapi MCP Service initialized Object
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at dfe.autoInitializeFromLocalStorage (index-0c8d8553.js:523:27820)
    at new dfe (index-0c8d8553.js:523:27600)
    at index-0c8d8553.js:523:56692
overrideMethod @ hook.js:608
hook.js:608 🔐 [AuthContext] Unexpected error checking auth: Error: Supabase client not initialized. Use getSupabaseClient() first.
    at Object.get (index-0c8d8553.js:48:72912)
    at index-0c8d8553.js:48:76727
    at index-0c8d8553.js:48:78348
    at m_ (index-0c8d8553.js:40:24270)
    at Ad (index-0c8d8553.js:40:42393)
    at index-0c8d8553.js:40:40710
    at D (index-0c8d8553.js:25:1585)
    at MessagePort.j (index-0c8d8553.js:25:1948)
overrideMethod @ hook.js:608
hook.js:608 Failed to set up auth listener: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at index-0c8d8553.js:48:78386
    at index-0c8d8553.js:48:79772
    at m_ (index-0c8d8553.js:40:24270)
    at Ad (index-0c8d8553.js:40:42393)
    at index-0c8d8553.js:40:40710
    at D (index-0c8d8553.js:25:1585)
    at MessagePort.j (index-0c8d8553.js:25:1948)
overrideMethod @ hook.js:608
index.ts:5 Loaded contentScript
home:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
hook.js:608 [AttorneyProfileManager] Error loading attorney by id: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.loadAttorneyById (index-0c8d8553.js:523:34723)
    at index-0c8d8553.js:523:28060
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.loadAttorneyById (index-0c8d8553.js:523:34723)
    at index-0c8d8553.js:523:28060
overrideMethod @ hook.js:608
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
ActiveCheckHelper.ts:21 received intentional event
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
app.bundle.js:2415 [Violation] 'setInterval' handler took 835ms
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
app.bundle.js:2415 [Violation] 'setInterval' handler took 657ms
app.bundle.js:2415 [Violation] 'setInterval' handler took 313ms
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
[Violation] Forced reflow while executing JavaScript took 104ms
app.bundle.js:2415 [Violation] 'setInterval' handler took 116ms
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
capture.js:8 [Violation] 'setTimeout' handler took 64ms
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
index-0c8d8553.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: Error: Supabase client not initialized
    at Sn (index-0c8d8553.js:48:72817)
    at dfe.setupRealtimeSubscription (index-0c8d8553.js:523:35095)
    at index-0c8d8553.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
await in setupRealtimeSubscription
(anonymous) @ index-0c8d8553.js:523
setTimeout
setupRealtimeSubscription @ index-0c8d8553.js:523
