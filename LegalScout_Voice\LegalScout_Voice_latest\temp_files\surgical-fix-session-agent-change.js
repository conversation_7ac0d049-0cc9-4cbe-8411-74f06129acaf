/**
 * Surgical Fix for Dashboard Session Agent Change Issues
 * 
 * This script makes minimal, targeted fixes to restore core functionality
 * without disrupting the existing system architecture.
 */

(function() {
  'use strict';

  // Prevent multiple loads
  if (window.__SURGICAL_FIX_LOADED) {
    console.log('[SurgicalFix] Already loaded');
    return;
  }
  window.__SURGICAL_FIX_LOADED = true;

  const SurgicalFix = {
    fixes: [],
    
    log: function(message, data = null) {
      console.log(`[SurgicalFix] ${message}`, data || '');
    },

    // Fix 1: Iframe Detection and Communication
    fixIframeDetection: function() {
      this.log('Applying iframe detection fix...');
      
      // Enhanced iframe selector that matches actual DOM structure
      const enhancedFindIframes = function() {
        const selectors = [
          'iframe[src*="simple-preview"]',
          'iframe[src*="preview"]',
          '#preview-iframe',
          '.preview-iframe',
          'iframe[title*="Preview"]',
          'iframe[title*="Agent"]'
        ];
        
        let allIframes = [];
        selectors.forEach(selector => {
          const iframes = document.querySelectorAll(selector);
          allIframes = allIframes.concat(Array.from(iframes));
        });
        
        // Remove duplicates
        const uniqueIframes = allIframes.filter((iframe, index, self) => 
          self.indexOf(iframe) === index
        );
        
        return uniqueIframes.filter(iframe => {
          try {
            return iframe.contentWindow && iframe.src;
          } catch (error) {
            return false;
          }
        });
      };

      // Patch the DashboardIframeManager if it exists
      if (window.DashboardIframeManager) {
        const originalFind = window.DashboardIframeManager.findPreviewIframes;
        window.DashboardIframeManager.findPreviewIframes = enhancedFindIframes;
        
        this.log('Patched DashboardIframeManager.findPreviewIframes');
        this.fixes.push('iframe-detection');
      }

      // Create a fallback iframe manager if none exists
      if (!window.DashboardIframeManager) {
        window.DashboardIframeManager = {
          findPreviewIframes: enhancedFindIframes,
          sendConfigToPreview: function(config) {
            const iframes = this.findPreviewIframes();
            if (iframes.length === 0) {
              console.warn('[SurgicalFix] No iframes found for config update');
              return Promise.resolve([]);
            }
            
            const promises = iframes.map(iframe => {
              return new Promise((resolve) => {
                try {
                  iframe.contentWindow.postMessage({
                    type: 'PREVIEW_CONFIG_UPDATE',
                    config: config,
                    timestamp: Date.now()
                  }, '*');
                  resolve({ success: true, iframe });
                } catch (error) {
                  resolve({ success: false, error: error.message, iframe });
                }
              });
            });
            
            return Promise.allSettled(promises);
          },
          hasPreviewIframes: function() {
            return this.findPreviewIframes().length > 0;
          },
          getIframeCount: function() {
            return this.findPreviewIframes().length;
          }
        };
        
        this.log('Created fallback DashboardIframeManager');
        this.fixes.push('iframe-manager-fallback');
      }
    },

    // Fix 2: State Propagation for Assistant Changes
    fixStatePropagation: function() {
      this.log('Applying state propagation fix...');
      
      // Enhanced state update function
      const enhancedStateUpdate = function(attorney) {
        if (!attorney) return;
        
        // Update localStorage
        try {
          localStorage.setItem('attorney', JSON.stringify(attorney));
        } catch (error) {
          console.warn('[SurgicalFix] Failed to update localStorage:', error);
        }
        
        // Update preview config
        if (window.updatePreviewConfig) {
          window.updatePreviewConfig({
            assistantId: attorney.vapi_assistant_id,
            firmName: attorney.firm_name,
            attorneyName: attorney.name,
            email: attorney.email
          });
        }
        
        // Send to iframes
        if (window.DashboardIframeManager) {
          window.DashboardIframeManager.sendConfigToPreview({
            assistantId: attorney.vapi_assistant_id,
            firmName: attorney.firm_name,
            attorneyName: attorney.name,
            email: attorney.email
          });
        }
        
        // Dispatch custom event for React components
        window.dispatchEvent(new CustomEvent('attorneyStateChanged', {
          detail: { attorney }
        }));
      };

      // Patch the standalone attorney manager if it exists
      if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
        const originalNotify = window.standaloneAttorneyManager.notifySubscribers;
        
        window.standaloneAttorneyManager.notifySubscribers = function() {
          // Call original method
          if (originalNotify) {
            originalNotify.call(this);
          }
          
          // Enhanced state propagation
          if (this.attorney) {
            enhancedStateUpdate(this.attorney);
          }
        };
        
        // Also patch the switchAttorney method if it exists
        if (window.standaloneAttorneyManager.switchAttorney) {
          const originalSwitch = window.standaloneAttorneyManager.switchAttorney;
          
          window.standaloneAttorneyManager.switchAttorney = function(attorneyId) {
            const result = originalSwitch.call(this, attorneyId);
            
            // Force state propagation after switch
            if (result && this.attorney) {
              setTimeout(() => enhancedStateUpdate(this.attorney), 100);
            }
            
            return result;
          };
        }
        
        this.log('Patched standaloneAttorneyManager state propagation');
        this.fixes.push('state-propagation');
      }
    },

    // Fix 3: Assistant Selection Synchronization
    fixAssistantSelection: function() {
      this.log('Applying assistant selection fix...');
      
      // Create a centralized assistant selection handler
      window.handleAssistantSelection = function(assistantId, attorneyId = null) {
        console.log('[SurgicalFix] Handling assistant selection:', assistantId);
        
        // Get current attorney
        let attorney = null;
        if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
          attorney = window.standaloneAttorneyManager.attorney;
        } else {
          try {
            const stored = localStorage.getItem('attorney');
            if (stored) {
              attorney = JSON.parse(stored);
            }
          } catch (error) {
            console.warn('[SurgicalFix] Failed to get attorney from localStorage');
          }
        }
        
        if (!attorney) {
          console.warn('[SurgicalFix] No attorney available for assistant selection');
          return false;
        }
        
        // Update attorney with new assistant ID
        const updatedAttorney = {
          ...attorney,
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        };
        
        // Update in manager if available
        if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
          window.standaloneAttorneyManager.attorney = updatedAttorney;
          window.standaloneAttorneyManager.saveToLocalStorage(updatedAttorney);
          window.standaloneAttorneyManager.notifySubscribers();
        } else {
          // Fallback to localStorage and manual propagation
          localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
          
          // Manual state propagation
          if (window.updatePreviewConfig) {
            window.updatePreviewConfig({
              assistantId: assistantId,
              firmName: updatedAttorney.firm_name,
              attorneyName: updatedAttorney.name
            });
          }
          
          // Send to iframes
          if (window.DashboardIframeManager) {
            window.DashboardIframeManager.sendConfigToPreview({
              assistantId: assistantId,
              firmName: updatedAttorney.firm_name,
              attorneyName: updatedAttorney.name
            });
          }
        }
        
        // Update Supabase if available
        if (window.supabase && updatedAttorney.id && !updatedAttorney.id.startsWith('dev-')) {
          window.supabase
            .from('attorneys')
            .update({ vapi_assistant_id: assistantId })
            .eq('id', updatedAttorney.id)
            .then(({ error }) => {
              if (error) {
                console.warn('[SurgicalFix] Failed to update attorney in Supabase:', error);
              } else {
                console.log('[SurgicalFix] Attorney updated in Supabase');
              }
            });
        }
        
        return true;
      };
      
      this.log('Created centralized assistant selection handler');
      this.fixes.push('assistant-selection');
    },

    // Fix 4: React Component State Sync
    fixReactStateSync: function() {
      this.log('Applying React state sync fix...');
      
      // Listen for attorney state changes and force React re-renders
      window.addEventListener('attorneyStateChanged', (event) => {
        const attorney = event.detail.attorney;
        
        // Force update of any React components that might be listening
        const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
        
        // Dispatch a more specific event for React components
        window.dispatchEvent(new CustomEvent('vapiAssistantChanged', {
          detail: {
            assistantId: attorney.vapi_assistant_id,
            attorney: attorney
          }
        }));
        
        // Also dispatch the preview config update event
        window.dispatchEvent(new CustomEvent('updatePreviewConfig', {
          detail: {
            assistantId: attorney.vapi_assistant_id,
            firmName: attorney.firm_name,
            attorneyName: attorney.name
          }
        }));
      });
      
      this.log('Set up React state sync listeners');
      this.fixes.push('react-state-sync');
    },

    // Apply all fixes
    applyAllFixes: function() {
      this.log('Starting surgical fixes for session agent change issues...');
      
      try {
        this.fixIframeDetection();
        this.fixStatePropagation();
        this.fixAssistantSelection();
        this.fixReactStateSync();
        
        this.log(`✅ Applied ${this.fixes.length} surgical fixes:`, this.fixes);
        
        // Test the fixes
        this.testFixes();
        
      } catch (error) {
        this.log('❌ Error applying fixes:', error);
      }
    },

    // Test that fixes are working
    testFixes: function() {
      this.log('Testing applied fixes...');
      
      const tests = {
        iframeManager: !!window.DashboardIframeManager,
        assistantHandler: !!window.handleAssistantSelection,
        iframeCount: window.DashboardIframeManager?.getIframeCount() || 0
      };
      
      this.log('Fix test results:', tests);
      
      // Store test results
      window.surgicalFixResults = {
        fixes: this.fixes,
        tests: tests,
        timestamp: new Date().toISOString()
      };
    }
  };

  // Apply fixes when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => SurgicalFix.applyAllFixes(), 500);
    });
  } else {
    setTimeout(() => SurgicalFix.applyAllFixes(), 500);
  }

  // Expose for manual testing
  window.SurgicalFix = SurgicalFix;

})();
