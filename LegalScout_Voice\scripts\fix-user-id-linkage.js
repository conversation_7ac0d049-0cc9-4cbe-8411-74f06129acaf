/**
 * Fix User ID Linkage for RLS Policies
 * 
 * This script fixes the user_id linkage in attorney records so that
 * RLS policies work correctly for consultations.
 * 
 * Run this in the browser console while logged into the dashboard.
 */

async function fixUserIdLinkage() {
  console.log('🔧 Fixing User ID Linkage for RLS Policies...\n');

  try {
    // Get supabase client
    const supabase = window.supabase || window.supabaseClient;
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }

    // 1. Get current authenticated user
    console.log('1️⃣ Getting current authenticated user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ No authenticated user found:', userError);
      return;
    }
    
    console.log('✅ Current user ID:', user.id);
    console.log('✅ Current user email:', user.email);

    // 2. Find attorney record for this user
    console.log('\n2️⃣ Finding attorney record...');
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();

    if (attorneyError) {
      console.error('❌ Error finding attorney:', attorneyError);
      
      // Try alternative emails
      const altEmails = ['<EMAIL>', '<EMAIL>'];
      let foundAttorney = null;
      
      for (const email of altEmails) {
        const { data: altAttorney, error: altError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('email', email)
          .single();
        
        if (!altError && altAttorney) {
          foundAttorney = altAttorney;
          console.log(`✅ Found attorney with email: ${email}`);
          break;
        }
      }
      
      if (!foundAttorney) {
        console.error('❌ No attorney record found');
        return;
      }
      
      var currentAttorney = foundAttorney;
    } else {
      var currentAttorney = attorney;
    }

    console.log('✅ Attorney found:', currentAttorney.name);
    console.log('Current user_id in attorney record:', currentAttorney.user_id);
    console.log('Expected user_id (from auth):', user.id);

    // 3. Check if user_id needs to be updated
    if (currentAttorney.user_id === user.id) {
      console.log('\n✅ User ID linkage is already correct!');
    } else {
      console.log('\n🔧 User ID linkage needs to be fixed...');
      
      // Update the attorney record with the correct user_id
      const { data: updatedAttorney, error: updateError } = await supabase
        .from('attorneys')
        .update({ user_id: user.id })
        .eq('id', currentAttorney.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Error updating attorney user_id:', updateError);
        return;
      }

      console.log('✅ Attorney user_id updated successfully!');
      console.log('New user_id:', updatedAttorney.user_id);
    }

    // 4. Test consultation creation now
    console.log('\n4️⃣ Testing consultation creation...');
    
    const testConsultation = {
      attorney_id: currentAttorney.id,
      client_name: 'Test Client - RLS Fix',
      client_email: '<EMAIL>',
      client_phone: '+1234567890',
      summary: 'Test consultation to verify RLS policies are working',
      transcript: 'Test transcript',
      duration: 120,
      practice_area: 'Test',
      location: 'Test Location',
      location_data: { test: true },
      metadata: {
        test_data: true,
        rls_fix_test: true
      },
      status: 'new'
    };

    const { data: newConsultation, error: insertError } = await supabase
      .from('consultations')
      .insert(testConsultation)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Still getting RLS error:', insertError);
      console.log('\n💡 Additional troubleshooting needed...');
      
      // Check if the attorney record is properly linked
      const { data: checkAttorney, error: checkError } = await supabase
        .from('attorneys')
        .select('id, user_id')
        .eq('user_id', user.id);

      if (checkError) {
        console.error('❌ Error checking attorney linkage:', checkError);
      } else {
        console.log('Attorneys linked to current user:', checkAttorney);
      }
    } else {
      console.log('✅ Test consultation created successfully!');
      console.log('Consultation ID:', newConsultation.id);
      
      // Clean up the test consultation
      await supabase
        .from('consultations')
        .delete()
        .eq('id', newConsultation.id);
      
      console.log('🧹 Test consultation cleaned up');
    }

    // 5. Now try creating the original test consultations
    console.log('\n5️⃣ Creating full test consultations...');
    
    const testConsultations = [
      {
        attorney_id: currentAttorney.id,
        client_name: 'John Smith',
        client_email: '<EMAIL>',
        client_phone: '+1234567890',
        summary: 'Client needs help with a contract dispute involving a service agreement.',
        transcript: 'Assistant: Hello! How can I help you today?\nUser: I need help with a contract dispute.',
        duration: 300,
        practice_area: 'Contract Law',
        location: 'New York, NY',
        location_data: { address: 'New York, NY', state: 'NY' },
        metadata: {
          call_id: `test-call-${Date.now()}`,
          assistant_id: currentAttorney.vapi_assistant_id,
          test_data: true
        },
        status: 'new'
      },
      {
        attorney_id: currentAttorney.id,
        client_name: 'Sarah Johnson',
        client_email: '<EMAIL>',
        client_phone: '+1987654321',
        summary: 'Personal injury case - client was injured in a car accident.',
        transcript: 'Assistant: Hello! How can I assist you today?\nUser: I was in a car accident.',
        duration: 450,
        practice_area: 'Personal Injury',
        location: 'Los Angeles, CA',
        location_data: { address: 'Los Angeles, CA', state: 'CA' },
        metadata: {
          call_id: `test-call-${Date.now() + 1}`,
          assistant_id: currentAttorney.vapi_assistant_id,
          test_data: true
        },
        status: 'follow-up'
      }
    ];

    let successCount = 0;
    for (const consultation of testConsultations) {
      try {
        const { data, error } = await supabase
          .from('consultations')
          .insert(consultation)
          .select()
          .single();

        if (error) {
          console.error(`❌ Error creating ${consultation.client_name}:`, error);
        } else {
          console.log(`✅ Created consultation: ${consultation.client_name}`);
          successCount++;
        }
      } catch (error) {
        console.error(`❌ Exception creating ${consultation.client_name}:`, error);
      }
    }

    // 6. Verify final results
    console.log('\n6️⃣ Final verification...');
    
    const { data: finalConsultations, error: finalError } = await supabase
      .from('consultations')
      .select('id, client_name, status, created_at')
      .eq('attorney_id', currentAttorney.id)
      .order('created_at', { ascending: false });

    if (finalError) {
      console.error('❌ Error verifying final consultations:', finalError);
    } else {
      console.log(`✅ Total consultations: ${finalConsultations.length}`);
      finalConsultations.forEach((c, index) => {
        console.log(`  ${index + 1}. ${c.client_name} (${c.status})`);
      });
    }

    console.log('\n📋 RLS FIX SUMMARY:');
    console.log('='.repeat(50));
    console.log(`👤 User: ${user.email} (ID: ${user.id})`);
    console.log(`🏢 Attorney: ${currentAttorney.name} (ID: ${currentAttorney.id})`);
    console.log(`✅ Consultations created: ${successCount}`);
    console.log(`📊 Total consultations: ${finalConsultations?.length || 0}`);
    
    if (successCount > 0) {
      console.log('\n🎉 SUCCESS! RLS policies are now working correctly.');
      console.log('💡 Check your Briefs page - consultations should now appear!');
    } else {
      console.log('\n❌ RLS issues persist. Additional investigation needed.');
    }

  } catch (error) {
    console.error('❌ RLS fix failed:', error);
  }
}

// Instructions
console.log(`
🔧 RLS FIX INSTRUCTIONS:
========================

1. Make sure you're logged into the LegalScout dashboard
2. Open Developer Tools (F12) and go to Console tab
3. Copy and paste this entire script
4. Run: fixUserIdLinkage()

This will:
- Fix the user_id linkage in your attorney record
- Test that RLS policies work correctly
- Create test consultations
- Verify everything is working

Run this now to fix the RLS issue!
`);

// Auto-run if we detect we're in the right environment
if (typeof window !== 'undefined' && window.location && window.location.pathname.includes('dashboard')) {
  console.log('🚀 Auto-running RLS fix...');
  fixUserIdLinkage();
}
