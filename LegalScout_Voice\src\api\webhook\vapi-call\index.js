/**
 * Vapi Call Webhook Handler
 * 
 * This webhook handler processes events from Vapi calls, updates the database,
 * and sends notifications to attorneys.
 */

import { supabase } from '../../../lib/supabase';
import { smsNotificationService } from '../../../services/SmsNotificationService';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    // Get call data from request body
    const callData = req.body;
    
    if (!callData || !callData.id) {
      return res.status(400).json({ error: 'Invalid call data' });
    }
    
    console.log('[Vapi Webhook] Received call event:', callData.id, callData.status);
    
    // Get attorney ID from Supabase
    const { data, error } = await supabase
      .from('calls')
      .select('attorney_id')
      .eq('call_id', callData.id)
      .single();
    
    if (error) {
      console.error('[Vapi Webhook] Error getting attorney ID:', error);
      return res.status(500).json({ error: 'Failed to get attorney ID' });
    }
    
    const attorneyId = data.attorney_id;
    
    // Update call status in Supabase
    await supabase
      .from('calls')
      .update({
        status: callData.status,
        updated_at: new Date().toISOString()
      })
      .eq('call_id', callData.id);
    
    // Handle different call events
    switch (callData.status) {
      case 'in-progress':
        // Call has started
        await smsNotificationService.sendCallStartedNotification(callData.id, attorneyId);
        break;
        
      case 'completed':
      case 'ended':
        // Call has ended
        await smsNotificationService.sendCallEndedNotification(callData.id, attorneyId);
        break;
    }
    
    // If there's a legal issue identified, send an in-progress notification
    if (callData.metadata && callData.metadata.legalIssue) {
      await smsNotificationService.sendCallInProgressNotification(callData.id, attorneyId, {
        legalIssue: callData.metadata.legalIssue
      });
    }
    
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('[Vapi Webhook] Error handling webhook:', error);
    return res.status(500).json({ error: error.message });
  }
}
