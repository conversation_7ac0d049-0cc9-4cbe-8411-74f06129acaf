/**
 * Supabase Configuration Verifier
 *
 * This utility verifies that the Supabase configuration is valid and provides
 * fallback values if needed. It's designed to be used in development to prevent
 * common configuration issues.
 */

import { supabase } from '../lib/supabase-fixed';

// Fallback values for development
const FALLBACK_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const FALLBACK_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs';

/**
 * Verify Supabase configuration and test connection
 * @returns {Promise<Object>} Result of the verification
 */
export async function verifySupabaseConfig() {
  console.log('🔍 Verifying Supabase configuration...');

  // Get environment variables
  const envVars = {
    VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
    REACT_APP_SUPABASE_URL: import.meta.env.REACT_APP_SUPABASE_URL,
    VITE_SUPABASE_KEY: import.meta.env.VITE_SUPABASE_KEY ? '[HIDDEN]' : 'missing',
    VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? '[HIDDEN]' : 'missing',
    REACT_APP_SUPABASE_KEY: import.meta.env.REACT_APP_SUPABASE_KEY ? '[HIDDEN]' : 'missing',
    REACT_APP_SUPABASE_ANON_KEY: import.meta.env.REACT_APP_SUPABASE_ANON_KEY ? '[HIDDEN]' : 'missing',
  };

  console.log('Environment variables:', envVars);

  // Check for placeholder values
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
                     import.meta.env.REACT_APP_SUPABASE_URL;

  const supabaseKey = import.meta.env.VITE_SUPABASE_KEY ||
                     import.meta.env.VITE_SUPABASE_ANON_KEY ||
                     import.meta.env.REACT_APP_SUPABASE_KEY ||
                     import.meta.env.REACT_APP_SUPABASE_ANON_KEY;

  const hasPlaceholderUrl = supabaseUrl === 'your-supabase-url';
  const hasPlaceholderKey = supabaseKey === 'your-anon-key';

  if (hasPlaceholderUrl) {
    console.warn('⚠️ Detected placeholder Supabase URL in environment variables');
    console.log('Using fallback URL:', FALLBACK_SUPABASE_URL);

    // Set the URL in window for potential use by other components
    window.VITE_SUPABASE_URL = FALLBACK_SUPABASE_URL;
  }

  if (hasPlaceholderKey) {
    console.warn('⚠️ Detected placeholder Supabase key in environment variables');
    console.log('Using fallback key (hidden)');

    // Set the key in window for potential use by other components
    window.VITE_SUPABASE_KEY = FALLBACK_SUPABASE_KEY;
  }

  // Test connection
  try {
    console.log('Testing Supabase connection...');
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .limit(1);

    if (error) {
      if (error.message === 'Invalid API key') {
        console.error('❌ Invalid Supabase API key detected');
        console.warn('The application will use mock data instead of real Supabase data');
        console.warn('To fix this, update your Supabase API key in the .env file');

        return {
          success: false,
          error: 'Invalid API key - using mock data instead',
          usingFallback: true,
          useMockData: true
        };
      } else {
        console.error('❌ Supabase connection test failed:', error.message);
        return {
          success: false,
          error: error.message,
          usingFallback: hasPlaceholderUrl || hasPlaceholderKey
        };
      }
    }

    console.log('✅ Supabase connection test successful!');
    return {
      success: true,
      data,
      usingFallback: hasPlaceholderUrl || hasPlaceholderKey
    };
  } catch (error) {
    console.error('❌ Unexpected error testing Supabase:', error.message);
    return {
      success: false,
      error: error.message,
      usingFallback: hasPlaceholderUrl || hasPlaceholderKey
    };
  }
}

/**
 * Initialize Supabase configuration
 * This should be called early in the application lifecycle
 */
export function initializeSupabaseConfig() {
  // Check for development mode
  const isDev = import.meta.env.MODE === 'development' ||
               window.location.hostname === 'localhost' ||
               window.location.hostname === '127.0.0.1';

  if (isDev) {
    console.log('🔧 Development mode detected, initializing Supabase configuration...');

    // Set fallback values in window object if needed
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
                       import.meta.env.REACT_APP_SUPABASE_URL;

    const supabaseKey = import.meta.env.VITE_SUPABASE_KEY ||
                       import.meta.env.VITE_SUPABASE_ANON_KEY ||
                       import.meta.env.REACT_APP_SUPABASE_KEY ||
                       import.meta.env.REACT_APP_SUPABASE_ANON_KEY;

    if (!supabaseUrl || supabaseUrl === 'your-supabase-url') {
      console.warn('⚠️ Setting fallback Supabase URL in window object');
      window.VITE_SUPABASE_URL = FALLBACK_SUPABASE_URL;
    }

    if (!supabaseKey || supabaseKey === 'your-anon-key') {
      console.warn('⚠️ Setting fallback Supabase key in window object');
      window.VITE_SUPABASE_KEY = FALLBACK_SUPABASE_KEY;
    }

    // Verify configuration
    verifySupabaseConfig().then(result => {
      if (result.success) {
        console.log('✅ Supabase configuration verified and working');
      } else {
        console.error('❌ Supabase configuration verification failed:', result.error);
      }
    });
  }
}

export default {
  verifySupabaseConfig,
  initializeSupabaseConfig
};
