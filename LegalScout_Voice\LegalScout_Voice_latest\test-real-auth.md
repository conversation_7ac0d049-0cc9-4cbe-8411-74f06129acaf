# Testing with Real Authentication

This guide will help you test the application with real user accounts from your Supabase database.

## Prerequisites

1. Make sure you have the latest code changes
2. Ensure your Supabase database is properly configured
3. Have a real user account in your Supabase database

## Step 1: Start the Application

Run the following command to start the application:

```bash
npm run dev
```

## Step 2: Log in with a Real User Account

1. Open your browser and navigate to `http://localhost:3000`
2. Click on the "Login" button
3. Enter the credentials for a real user account from your Supabase database:
   - Email: `<EMAIL>` (or another real user account)
   - Password: (use the correct password for the account)

## Step 3: Verify Dashboard Functionality

After logging in, you should be redirected to the dashboard. Verify the following:

1. The attorney data is correctly loaded from Supabase
2. The Vapi assistant ID is correctly retrieved and displayed
3. The agent preview functionality is working correctly

## Step 4: Test Vapi Assistant Configuration

1. Navigate to the "Agent" tab in the dashboard
2. Verify that the Vapi Assistant Configuration component is displayed
3. If the attorney doesn't have a Vapi assistant ID, click the "Create Assistant" button
4. If the attorney has a Vapi assistant ID, verify that the assistant data is correctly loaded
5. Make changes to the Vapi-related fields and verify that they are properly saved

## Step 5: Test Agent Preview

1. Make changes to the agent configuration
2. Verify that the preview is updated in real-time
3. Verify that the preview is using the correct Vapi assistant ID

## Step 6: Check Logs

1. Open the browser developer console
2. Verify that the logs are properly formatted and provide useful information
3. Look for any errors or warnings that might indicate issues with the implementation

## Troubleshooting

If you encounter any issues during testing, check the following:

1. Make sure the attorney has a valid Vapi assistant ID in Supabase
2. Verify that the Vapi MCP server is running and accessible
3. Check the browser console for any errors or warnings
4. Verify that the attorney data is correctly loaded from Supabase

## Expected Behavior

When testing with a real user account, you should see the following behavior:

1. The attorney data is correctly loaded from Supabase
2. If the attorney doesn't have a Vapi assistant ID, one is created automatically
3. If the attorney has a Vapi assistant ID, the assistant data is correctly loaded
4. Changes to Vapi-related fields are properly saved to both Supabase and Vapi
5. The agent preview uses the correct Vapi assistant ID

## Notes

- The application uses the Vapi MCP server for development purposes
- In production, the application will use the Vapi API directly
- The application uses the attorney's Vapi assistant ID for the preview, not a fallback ID
- The application ensures a single source of truth by storing all configuration data in Supabase and synchronizing it with Vapi
