/**
 * Auto Assistant Reconciler
 * 
 * ULTRA-THINKING: Automatically detects and fixes assistant ID conflicts
 * when the dashboard loads, ensuring data consistency without user intervention.
 * 
 * Root Cause Solution:
 * - Validates assistant IDs on dashboard load
 * - Auto-fixes conflicts using smart matching
 * - Updates UI state immediately
 * - Prevents future conflicts
 */

import { supabase } from '../lib/supabase';

class AutoAssistantReconciler {
  constructor() {
    this.isRunning = false;
    this.lastCheck = null;
    this.checkInterval = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get assistant from Vapi API
   */
  async getVapiAssistant(assistantId) {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
      
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.warn(`[AutoReconciler] Error fetching assistant ${assistantId}:`, error.message);
      return null;
    }
  }

  /**
   * List all assistants from Vapi
   */
  async listVapiAssistants() {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
      
      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Vapi API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('[AutoReconciler] Error listing assistants:', error.message);
      return [];
    }
  }

  /**
   * ULTRA-THINKING: Smart assistant matching algorithm
   */
  async findBestAssistantMatch(attorney) {
    const allAssistants = await this.listVapiAssistants();
    
    if (allAssistants.length === 0) {
      return null;
    }

    const firmName = attorney.firm_name?.toLowerCase() || '';
    const potentialMatches = [];

    // Find potential matches with scoring
    for (const assistant of allAssistants) {
      const assistantName = assistant.name?.toLowerCase() || '';
      let score = 0;
      let reasons = [];
      
      // LegalScout specific matching (for your case)
      if (assistantName.includes('legalscout')) {
        score += assistantName.includes('assistant') ? 100 : 80;
        reasons.push('LegalScout pattern');
      }
      
      // Firm name matching
      if (firmName && assistantName.includes(firmName)) {
        score += assistantName === firmName ? 90 : 70;
        reasons.push('firm name match');
      }
      
      // Prefer "Assistant" in name (more complete)
      if (assistantName.includes('assistant')) {
        score += 20;
        reasons.push('has assistant in name');
      }
      
      // Prefer older assistants (more established)
      const createdAt = new Date(assistant.createdAt);
      const daysSinceCreation = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreation > 1) {
        score += Math.min(daysSinceCreation, 30);
        reasons.push(`${Math.floor(daysSinceCreation)} days old`);
      }
      
      if (score > 0) {
        potentialMatches.push({
          ...assistant,
          score,
          matchReasons: reasons
        });
      }
    }

    if (potentialMatches.length === 0) {
      return null;
    }

    // Sort by score and return best match
    potentialMatches.sort((a, b) => b.score - a.score);
    const bestMatch = potentialMatches[0];
    
    console.log(`[AutoReconciler] 🎯 Best match for ${attorney.email}: "${bestMatch.name}" (score: ${bestMatch.score})`);
    
    return bestMatch;
  }

  /**
   * Reconcile assistant for a specific attorney
   */
  async reconcileAttorney(attorney) {
    try {
      // ULTRA-THINKING: Validate attorney object before processing
      if (!attorney || !attorney.id || !attorney.email) {
        console.warn('[AutoReconciler] Invalid attorney object provided:', attorney);
        return {
          success: false,
          action: 'invalid_attorney',
          message: 'Attorney object is invalid or missing required fields'
        };
      }

      const currentAssistantId = attorney.vapi_assistant_id;
      
      // Check if current assistant is valid
      if (currentAssistantId && !currentAssistantId.startsWith('mock-')) {
        const currentAssistant = await this.getVapiAssistant(currentAssistantId);
        
        if (currentAssistant) {
          // Current assistant is valid, no action needed
          return {
            success: true,
            action: 'validated',
            assistantId: currentAssistantId,
            assistantName: currentAssistant.name
          };
        }
      }

      // Current assistant is invalid or missing, find a better match
      const bestMatch = await this.findBestAssistantMatch(attorney);
      
      if (!bestMatch) {
        return {
          success: false,
          action: 'no_match',
          message: 'No suitable assistant found'
        };
      }

      // Update the database
      const { error } = await supabase
        .from('attorneys')
        .update({ 
          vapi_assistant_id: bestMatch.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorney.id);

      if (error) {
        throw error;
      }

      console.log(`[AutoReconciler] ✅ Auto-fixed assistant for ${attorney.email}: "${bestMatch.name}"`);

      return {
        success: true,
        action: 'auto_fixed',
        assistantId: bestMatch.id,
        assistantName: bestMatch.name,
        previousId: currentAssistantId
      };

    } catch (error) {
      console.error(`[AutoReconciler] Error reconciling ${attorney.email}:`, error.message);
      return {
        success: false,
        action: 'error',
        message: error.message
      };
    }
  }

  /**
   * Check and fix assistant conflicts for current user
   */
  async checkAndFixCurrentUser() {
    if (this.isRunning) {
      return null;
    }

    // Throttle checks
    if (this.lastCheck && (Date.now() - this.lastCheck) < this.checkInterval) {
      return null;
    }

    this.isRunning = true;
    this.lastCheck = Date.now();

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return null;
      }

      // Get attorney record
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error || !attorney) {
        // Try by email as fallback
        const { data: attorneyByEmail } = await supabase
          .from('attorneys')
          .select('*')
          .eq('email', user.email)
          .single();
          
        if (!attorneyByEmail) {
          return null;
        }
        
        return await this.reconcileAttorney(attorneyByEmail);
      }

      return await this.reconcileAttorney(attorney);

    } catch (error) {
      console.error('[AutoReconciler] Error in checkAndFixCurrentUser:', error.message);
      return null;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Initialize auto-reconciliation for dashboard
   */
  initializeForDashboard() {
    console.log('[AutoReconciler] 🚀 Initializing auto-reconciliation');
    
    // Run initial check after a short delay
    setTimeout(() => {
      this.checkAndFixCurrentUser().then(result => {
        if (result && result.success && result.action === 'auto_fixed') {
          console.log(`[AutoReconciler] 🎉 Auto-fixed assistant conflict: ${result.assistantName}`);
          
          // Trigger UI refresh
          window.dispatchEvent(new CustomEvent('assistantReconciled', {
            detail: result
          }));
        }
      });
    }, 2000);

    // Set up periodic checks
    setInterval(() => {
      this.checkAndFixCurrentUser();
    }, this.checkInterval);
  }

  /**
   * Manual reconciliation trigger
   */
  async manualReconcile() {
    console.log('[AutoReconciler] 🔧 Manual reconciliation triggered');
    this.lastCheck = null; // Reset throttle
    return await this.checkAndFixCurrentUser();
  }
}

// Export singleton instance
export const autoAssistantReconciler = new AutoAssistantReconciler();
export default AutoAssistantReconciler;
