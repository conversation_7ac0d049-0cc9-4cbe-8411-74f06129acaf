import React, { useState, useEffect } from 'react';
import { getAllDebuggers } from '../utils/callDebugger';

/**
 * Call Debug Panel Component
 *
 * This component displays debugging information for Vapi calls.
 * It can be added to any page to monitor call activity.
 */
const CallDebugPanel = ({ visible = true }) => {
  console.log('🚫 CallDebugPanel: Component called but DISABLED - returning null');
  return null;

  // Refresh debugger data periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setDebuggers(getAllDebuggers());
      setRefreshKey(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // If not visible, don't render
  if (!visible) return null;

  // Get all debugger instances
  const allDebuggers = Object.values(debuggers);

  // Combine events from all debuggers
  const allEvents = allDebuggers.flatMap(d =>
    d.events.map(e => ({ ...e, component: d.componentName }))
  ).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Combine errors from all debuggers
  const allErrors = allDebuggers.flatMap(d =>
    d.errors.map(e => ({ ...e, component: d.componentName }))
  ).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Combine network requests from all debuggers
  const allNetworkRequests = allDebuggers.flatMap(d =>
    d.networkRequests.map(r => ({ ...r, component: d.componentName }))
  ).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Get active calls
  const activeCalls = allDebuggers.filter(d => d.callStatus === 'active');

  // Panel styles
  const panelStyle = {
    position: 'fixed',
    bottom: expanded ? '0' : '-300px',
    right: '0',
    width: '400px',
    height: expanded ? '300px' : '30px',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    zIndex: 9999,
    transition: 'height 0.3s, bottom 0.3s',
    fontFamily: 'monospace',
    fontSize: '12px',
    display: 'flex',
    flexDirection: 'column',
    borderTop: '1px solid #444',
    borderLeft: '1px solid #444'
  };

  const headerStyle = {
    padding: '5px 10px',
    backgroundColor: '#333',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    cursor: 'pointer'
  };

  const contentStyle = {
    padding: '10px',
    overflowY: 'auto',
    flex: 1
  };

  const tabsStyle = {
    display: 'flex',
    borderBottom: '1px solid #444'
  };

  const tabStyle = (isActive) => ({
    padding: '5px 10px',
    cursor: 'pointer',
    backgroundColor: isActive ? '#444' : 'transparent',
    borderRight: '1px solid #444'
  });

  const eventStyle = {
    marginBottom: '5px',
    borderBottom: '1px solid #333',
    paddingBottom: '5px'
  };

  const timestampStyle = {
    color: '#888',
    fontSize: '10px'
  };

  const componentStyle = {
    color: '#4CAF50',
    fontWeight: 'bold'
  };

  const errorStyle = {
    color: '#F44336',
    fontWeight: 'bold'
  };

  const buttonStyle = {
    backgroundColor: '#555',
    border: 'none',
    color: 'white',
    padding: '2px 5px',
    borderRadius: '3px',
    cursor: 'pointer',
    marginLeft: '5px'
  };

  // Toggle panel expansion
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'events':
        return (
          <div>
            <h4>Recent Events ({allEvents.length})</h4>
            {allEvents.slice(0, 50).map((event, index) => (
              <div key={index} style={eventStyle}>
                <div style={timestampStyle}>{new Date(event.timestamp).toLocaleTimeString()}</div>
                <div>
                  <span style={componentStyle}>[{event.component}]</span> {event.message}
                </div>
                {event.data && (
                  <pre style={{ fontSize: '10px', color: '#BBB' }}>
                    {JSON.stringify(event.data, null, 2)}
                  </pre>
                )}
              </div>
            ))}
          </div>
        );

      case 'errors':
        return (
          <div>
            <h4>Errors ({allErrors.length})</h4>
            {allErrors.map((error, index) => (
              <div key={index} style={{ ...eventStyle, borderColor: '#F44336' }}>
                <div style={timestampStyle}>{new Date(error.timestamp).toLocaleTimeString()}</div>
                <div>
                  <span style={componentStyle}>[{error.component}]</span>
                  <span style={errorStyle}>{error.message}</span>
                </div>
                {error.error && (
                  <pre style={{ fontSize: '10px', color: '#F44336' }}>
                    {JSON.stringify(error.error, null, 2)}
                  </pre>
                )}
              </div>
            ))}
          </div>
        );

      case 'network':
        return (
          <div>
            <h4>Network Requests ({allNetworkRequests.length})</h4>
            {allNetworkRequests.map((request, index) => (
              <div key={index} style={eventStyle}>
                <div style={timestampStyle}>{new Date(request.timestamp).toLocaleTimeString()}</div>
                <div>
                  <span style={componentStyle}>[{request.component}]</span>
                  <strong>{request.method}</strong> {request.url}
                </div>
                {request.response && (
                  <div style={{
                    color: request.response.ok ? '#4CAF50' : '#F44336',
                    fontSize: '11px'
                  }}>
                    Status: {request.response.status}
                  </div>
                )}
              </div>
            ))}
          </div>
        );

      case 'calls':
        return (
          <div>
            <h4>Active Calls ({activeCalls.length})</h4>
            {allDebuggers.map((callDebugger, index) => (
              <div key={index} style={eventStyle}>
                <div>
                  <span style={componentStyle}>[{callDebugger.componentName}]</span>
                  <strong>Status:</strong> {callDebugger.callStatus}
                  {callDebugger.callId && <div><strong>Call ID:</strong> {callDebugger.callId}</div>}
                  {callDebugger.startTime && (
                    <div>
                      <strong>Started:</strong> {callDebugger.startTime.toLocaleTimeString()}
                      <strong> Duration:</strong> {
                        ((new Date() - callDebugger.startTime) / 1000).toFixed(1)
                      }s
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        );

      default:
        return <div>Select a tab to view debug information</div>;
    }
  };

  return (
    <div style={panelStyle}>
      <div style={headerStyle} onClick={toggleExpanded}>
        <div>
          Call Debugger {activeCalls.length > 0 && `(${activeCalls.length} active)`}
        </div>
        <div>
          <button
            style={buttonStyle}
            onClick={(e) => {
              e.stopPropagation();
              setRefreshKey(prev => prev + 1);
            }}
          >
            Refresh
          </button>
          <button
            style={buttonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Debug data:', debuggers);
            }}
          >
            Log
          </button>
        </div>
      </div>

      {expanded && (
        <>
          <div style={tabsStyle}>
            <div
              style={tabStyle(activeTab === 'events')}
              onClick={() => setActiveTab('events')}
            >
              Events ({allEvents.length})
            </div>
            <div
              style={tabStyle(activeTab === 'errors')}
              onClick={() => setActiveTab('errors')}
            >
              Errors ({allErrors.length})
            </div>
            <div
              style={tabStyle(activeTab === 'network')}
              onClick={() => setActiveTab('network')}
            >
              Network ({allNetworkRequests.length})
            </div>
            <div
              style={tabStyle(activeTab === 'calls')}
              onClick={() => setActiveTab('calls')}
            >
              Calls ({activeCalls.length})
            </div>
          </div>
          <div style={contentStyle}>
            {renderContent()}
          </div>
        </>
      )}
    </div>
  );
};

export default CallDebugPanel;
