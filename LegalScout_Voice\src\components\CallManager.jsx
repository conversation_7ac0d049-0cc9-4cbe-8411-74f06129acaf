const startCall = async (attorneyConfig) => {
  try {
    const baseConfig = {
      ...DEFAULT_VAPI_PARAMS,
      context: {
        role: 'system',
        content: attorneyConfig.vapiInstructions || DEFAULT_INSTRUCTIONS
      }
    };

    const validatedConfig = safeVapiConfig(
      baseConfig,
      attorneyConfig.vapiOverrides || {}
    );

    const call = await vapi.start(assistantId, validatedConfig);
    
    // Add state monitoring
    let inactivityTimer;
    const resetTimer = () => {
      clearTimeout(inactivityTimer);
      inactivityTimer = setTimeout(() => {
        console.log('Inactivity timeout - ending call');
        call.stop();
      }, 450000); // 7.5 minute safety net
    };

    call.on('speech-start', resetTimer);
    call.on('message', resetTimer);
    
    return call;
  } catch (error) {
    console.error('Call initialization failed:', error);
    throw new Error(`Call failed: ${error.message}`);
  }
}; 