/**
 * Sends a message to the parent window
 * @param {string} type - The message type
 * @param {object} data - The message data 
 */
export const sendMessageToParent = (type, data = {}) => {
  console.log(`[iframeUtils] Sending message to parent: ${type}`);
  if (window.parent) {
    try {
      window.parent.postMessage({ type, ...data }, '*');
    } catch (error) {
      console.error('[iframeUtils] Error sending message to parent:', error);
    }
  }
};

/**
 * Subscribes to messages from the parent window
 * @param {string} type - The message type to listen for
 * @param {function} callback - The callback function
 * @returns {function} - Unsubscribe function
 */
export const subscribeToParentMessages = (type, callback) => {
  console.log(`[iframeUtils] Subscribing to parent messages: ${type}`);
  
  const handleMessage = (event) => {
    if (event.data && event.data.type === type) {
      callback(event.data);
    }
  };
  
  window.addEventListener('message', handleMessage);
  
  return () => {
    window.removeEventListener('message', handleMessage);
  };
};

/**
 * Establishes a ping/pong communication with the parent window
 * to ensure the iframe is responsive and visible
 * @param {boolean} isProduction - Whether the app is running in production
 * @returns {function} - Cleanup function
 */
export const setupIframePingPong = (isProduction = false) => {
  console.log('[iframeUtils] Setting up iframe ping/pong communication');
  
  // Send initial ready message
  sendMessageToParent('IFRAME_READY', { 
    timestamp: Date.now(),
    isProduction 
  });
  
  // Setup ping interval - we'll ping the parent every 2 seconds
  const pingInterval = setInterval(() => {
    sendMessageToParent('IFRAME_PING', { 
      timestamp: Date.now(),
      visible: document.visibilityState === 'visible',
      isProduction
    });
  }, 2000);
  
  // Listen for pong from parent
  const unsubscribe = subscribeToParentMessages('IFRAME_PONG', (data) => {
    console.log('[iframeUtils] Received pong from parent', data);
    
    // If parent requested visibility check, force visibility
    if (data.checkVisibility && isProduction) {
      console.log('[iframeUtils] Parent requested visibility check, forcing visibility');
      forceVisibility();
    }
  });
  
  // Function to force visibility of key elements
  const forceVisibility = () => {
    const style = document.createElement('style');
    style.textContent = `
      .preview-container, #preview-content, .content-container,
      .welcome-title, .button-container, .consultation-button-direct {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 100 !important;
      }
    `;
    document.head.appendChild(style);
    
    // Set a timeout to remove the style to avoid conflicts
    setTimeout(() => {
      try {
        document.head.removeChild(style);
      } catch (e) {
        // Style may have been removed already
      }
    }, 1000);
  };
  
  // Immediately force visibility in production
  if (isProduction) {
    forceVisibility();
  }
  
  // Return cleanup function
  return () => {
    clearInterval(pingInterval);
    unsubscribe();
  };
}; 