<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Assistant UI Variables</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .assistant-config {
            background: #333;
            padding: 15px;
            border-radius: 8px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255,255,255,0.05);
            border-radius: 4px;
        }
        .config-value {
            font-family: monospace;
            color: #34d399;
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎨 Test Assistant UI Variables</h1>
    <p>This tests that each subdomain gets the correct assistant-specific UI variables.</p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testUIVariables()">Test UI Variables</button>
        <button onclick="compareSubdomains()">Compare Both Subdomains</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script type="module">
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.addResult = addResult;
        window.clearResults = clearResults;

        window.testUIVariables = async function() {
            addResult('info', '🎨 Testing assistant-specific UI variables...');
            
            try {
                const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                
                // Test assistant1test subdomain
                const config1 = await loadAttorneyConfig('assistant1test');
                
                if (config1) {
                    addResult('success', `✅ assistant1test UI variables:
Firm Name: ${config1.firmName}
Primary Color: ${config1.primaryColor}
Background Color: ${config1.backgroundColor}
Welcome Message: ${config1.welcomeMessage}
Voice ID: ${config1.voiceId}
Voice Provider: ${config1.voiceProvider}
Vapi Instructions: ${config1.vapiInstructions?.substring(0, 50)}...
Has Assistant UI Config: ${config1.hasAssistantUIConfig}
Assistant ID: ${config1.vapi_assistant_id}`);
                } else {
                    addResult('error', '❌ No config returned for assistant1test');
                }
                
            } catch (error) {
                addResult('error', `❌ Test failed: ${error.message}`);
                console.error('Test error:', error);
            }
        };

        window.compareSubdomains = async function() {
            addResult('info', '🔍 Comparing UI variables between subdomains...');
            
            try {
                const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                
                // Load both configs
                const [damonConfig, testConfig] = await Promise.all([
                    loadAttorneyConfig('damon'),
                    loadAttorneyConfig('assistant1test')
                ]);
                
                if (damonConfig && testConfig) {
                    // Create comparison
                    const comparison = document.createElement('div');
                    comparison.className = 'comparison';
                    
                    const damonDiv = document.createElement('div');
                    damonDiv.className = 'assistant-config';
                    damonDiv.innerHTML = `
                        <h4>damon.legalscout.net</h4>
                        <div class="config-item">
                            <span>Assistant ID:</span>
                            <span class="config-value">${damonConfig.vapi_assistant_id}</span>
                        </div>
                        <div class="config-item">
                            <span>Firm Name:</span>
                            <span class="config-value">${damonConfig.firmName}</span>
                        </div>
                        <div class="config-item">
                            <span>Primary Color:</span>
                            <span class="config-value" style="color: ${damonConfig.primaryColor}">${damonConfig.primaryColor}</span>
                        </div>
                        <div class="config-item">
                            <span>Background:</span>
                            <span class="config-value" style="color: ${damonConfig.backgroundColor}">${damonConfig.backgroundColor}</span>
                        </div>
                        <div class="config-item">
                            <span>Voice ID:</span>
                            <span class="config-value">${damonConfig.voiceId}</span>
                        </div>
                        <div class="config-item">
                            <span>Voice Provider:</span>
                            <span class="config-value">${damonConfig.voiceProvider}</span>
                        </div>
                        <div class="config-item">
                            <span>Has UI Config:</span>
                            <span class="config-value">${damonConfig.hasAssistantUIConfig ? '✅' : '❌'}</span>
                        </div>
                    `;
                    
                    const testDiv = document.createElement('div');
                    testDiv.className = 'assistant-config';
                    testDiv.innerHTML = `
                        <h4>assistant1test.legalscout.net</h4>
                        <div class="config-item">
                            <span>Assistant ID:</span>
                            <span class="config-value">${testConfig.vapi_assistant_id}</span>
                        </div>
                        <div class="config-item">
                            <span>Firm Name:</span>
                            <span class="config-value">${testConfig.firmName}</span>
                        </div>
                        <div class="config-item">
                            <span>Primary Color:</span>
                            <span class="config-value" style="color: ${testConfig.primaryColor}">${testConfig.primaryColor}</span>
                        </div>
                        <div class="config-item">
                            <span>Background:</span>
                            <span class="config-value" style="color: ${testConfig.backgroundColor}">${testConfig.backgroundColor}</span>
                        </div>
                        <div class="config-item">
                            <span>Voice ID:</span>
                            <span class="config-value">${testConfig.voiceId}</span>
                        </div>
                        <div class="config-item">
                            <span>Voice Provider:</span>
                            <span class="config-value">${testConfig.voiceProvider}</span>
                        </div>
                        <div class="config-item">
                            <span>Has UI Config:</span>
                            <span class="config-value">${testConfig.hasAssistantUIConfig ? '✅' : '❌'}</span>
                        </div>
                    `;
                    
                    comparison.appendChild(damonDiv);
                    comparison.appendChild(testDiv);
                    
                    const resultsDiv = document.getElementById('results');
                    const comparisonSection = document.createElement('div');
                    comparisonSection.className = 'test-section success';
                    comparisonSection.innerHTML = '<h4>🎯 Assistant Comparison</h4>';
                    comparisonSection.appendChild(comparison);
                    resultsDiv.appendChild(comparisonSection);
                    
                    // Analysis
                    const differentAssistants = damonConfig.vapi_assistant_id !== testConfig.vapi_assistant_id;
                    const differentColors = damonConfig.primaryColor !== testConfig.primaryColor;
                    const differentVoices = damonConfig.voiceId !== testConfig.voiceId;
                    const bothHaveUIConfigs = damonConfig.hasAssistantUIConfig && testConfig.hasAssistantUIConfig;
                    
                    if (differentAssistants && differentColors && bothHaveUIConfigs) {
                        addResult('success', '🎉 PERFECT! Each subdomain has different assistants with unique UI variables!');
                    } else {
                        addResult('info', `Analysis:
- Different Assistants: ${differentAssistants ? '✅' : '❌'}
- Different Colors: ${differentColors ? '✅' : '❌'}  
- Different Voices: ${differentVoices ? '✅' : '❌'}
- Both Have UI Configs: ${bothHaveUIConfigs ? '✅' : '❌'}`);
                    }
                } else {
                    addResult('error', '❌ Failed to load one or both configs');
                }
                
            } catch (error) {
                addResult('error', `❌ Comparison failed: ${error.message}`);
                console.error('Comparison error:', error);
            }
        };

        // Auto-run test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                compareSubdomains();
            }, 1000);
        });
    </script>
</body>
</html>
