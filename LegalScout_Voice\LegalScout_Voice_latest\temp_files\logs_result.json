[{"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686638, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686638, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686638, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686638, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686637, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686620, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686620, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686620, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686620, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686620, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686603, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686603, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686602, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686602, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686602, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686574, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686574, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686573, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686573, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686572, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686551, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "dm6pj-1748476686497-22b206b17d2e", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 6, "region": "iad1", "maxMemoryUsed": 106, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686516, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "5rc9b-1748476686497-3939dd6e1dfe", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 10, "region": "iad1", "maxMemoryUsed": 106, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686513, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "5rc9b-1748476686497-2fd73b2b986c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 6, "region": "iad1", "maxMemoryUsed": 106, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:58:06", "timestampInMs": 1748476686510, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ntfh9-1748476686496-ae01059b492c", "requestUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 62, "region": "iad1", "maxMemoryUsed": 106, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": "4425ee9018f7e8155341bf363f"}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218495, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "dd8lb-1748475218473-77a9717b6b60", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218480, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "dd8lb-1748475218473-77a9717b6b60", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 263, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218385, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218357, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218210, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218210, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218210, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218210, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218195, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218195, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218195, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218195, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218191, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c5mz9-1748475218176-01724447ea3b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 233, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:38", "timestampInMs": 1748475218179, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "7klll-1748475218172-b98f7796c27d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 281, "region": "iad1", "maxMemoryUsed": 123, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217958, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lvx9c-1748475217936-981c1ba45a8f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217943, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "lvx9c-1748475217936-981c1ba45a8f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 242, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217924, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475217908-bb3cb85f07ed", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217911, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c5mz9-1748475217908-bb3cb85f07ed", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 228, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217910, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217877-10e20d3e06a8", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217886, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "7klll-1748475217877-10e20d3e06a8", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 191, "region": "iad1", "maxMemoryUsed": 119, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217885, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217851-986ace532987", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217873, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "pn2s4-1748475217851-986ace532987", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 272, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217861, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217836-be9dcafa2468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217856, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217839, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m89sq-1748475217836-be9dcafa2468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 263, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217837, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pnqqt-1748475217816-bf45b4caeb1d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217823, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "pnqqt-1748475217816-bf45b4caeb1d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 167, "region": "iad1", "maxMemoryUsed": 126, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217786, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217770, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217749, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217681, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217457, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217457, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217457, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217455, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217454, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217454, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217454, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217454, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217452, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217446, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217446, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217446, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217446, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217444, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217444, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217444, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217444, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217439, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m89sq-1748475217434-975468dd069a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 383, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217438, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m7lnv-1748475217426-b3303dac0a40", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 331, "region": "iad1", "maxMemoryUsed": 129, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217436, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "7klll-1748475217426-23046b10a7fb", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 417, "region": "iad1", "maxMemoryUsed": 123, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217432, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "kfhvf-1748475217406-e2ad3967cece", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 276, "region": "iad1", "maxMemoryUsed": 126, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217430, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "pn2s4-1748475217406-1b0e2becbc31", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 457, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:37", "timestampInMs": 1748475217430, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ps67d-1748475217426-4111d84958c6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 354, "region": "iad1", "maxMemoryUsed": 132, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216889, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ttrcd-1748475216870-1f628ac07737", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216876, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ttrcd-1748475216870-1f628ac07737", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 141, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216832, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475216811-805680f7a37a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216817, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c5mz9-1748475216811-805680f7a37a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 170, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216779, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216554, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216554, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216554, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216554, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216522, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216522, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216522, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216522, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216512, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "rsf6g-1748475216502-e842879a299b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 223, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216504, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2h5hb-1748475216502-9af5e7b3008f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 316, "region": "iad1", "maxMemoryUsed": 130, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216342, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "t47wd-1748475216322-9d78257a3844", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216327, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "t47wd-1748475216322-9d78257a3844", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 247, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216276, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lvx9c-1748475216254-b6c3a1cb9637", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216262, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "lvx9c-1748475216254-b6c3a1cb9637", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 234, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216227, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216210-c046bfc263d9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216212, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2h5hb-1748475216210-c046bfc263d9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 252, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216161, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216154, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "t47wd-1748475216130-38aeeb855ff5", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216140, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "t47wd-1748475216130-38aeeb855ff5", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 180, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216100, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475216082-fd62338e7811", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216098, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216085, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qhm9h-1748475216082-fd62338e7811", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 313, "region": "iad1", "maxMemoryUsed": 119, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216065, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216041, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475216022-e06caa72c004", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:36", "timestampInMs": 1748475216024, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2h5hb-1748475216022-e06caa72c004", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 186, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215985, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215944, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215911, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215760, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215759, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215759, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215759, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215727, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215727, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215727, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215727, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215725, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215725, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215725, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215725, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215721, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215721, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215721, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215721, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215720, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215712, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215712, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215712, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215712, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215709, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qhm9h-1748475215700-f71391e723b7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 310, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215707, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xk26f-1748475215686-ef5ec66890e9", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 363, "region": "iad1", "maxMemoryUsed": 128, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215706, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "nrv89-1748475215700-960ac33a6902", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 423, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215706, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ptv99-1748475215696-7b04fe1be681", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 496, "region": "iad1", "maxMemoryUsed": 129, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215703, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2t7wc-1748475215696-439ad1ac08e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 392, "region": "iad1", "maxMemoryUsed": 119, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:35", "timestampInMs": 1748475215694, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2h5hb-1748475215684-1c5c9637a530", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 258, "region": "iad1", "maxMemoryUsed": 123, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189836, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189836, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189836, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189835, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189835, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189820, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xxfcp-1748475189817-f1d741fafe74", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 5, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189484, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "tj7dk-1748475189460-eb6601868e4d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189467, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "tj7dk-1748475189460-eb6601868e4d", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 263, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189450, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zc2hw-1748475189424-46ebf389d3a2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189429, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "zc2hw-1748475189424-46ebf389d3a2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 267, "region": "iad1", "maxMemoryUsed": 116, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189369, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189341, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189327, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475189308-acc55389ae6f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189314, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "4p2pw-1748475189308-acc55389ae6f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 267, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189179, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189106, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189105, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189092, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m89sq-1748475189088-15a82f4d1082", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 327, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:09", "timestampInMs": 1748475189086, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xxfcp-1748475189084-36203ff3fcc1", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 292, "region": "iad1", "maxMemoryUsed": 124, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:08", "timestampInMs": 1748475188948, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:08", "timestampInMs": 1748475188948, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:08", "timestampInMs": 1748475188948, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:08", "timestampInMs": 1748475188948, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:33:08", "timestampInMs": 1748475188931, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "4p2pw-1748475188928-88722fd6283a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 332, "region": "iad1", "maxMemoryUsed": 128, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179574, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pzck-1748475179570-b4edea85ed39", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 4, "region": "iad1", "maxMemoryUsed": 126, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179172, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475179082-39b86cc3884e", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179121, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "6ntgx-1748475179102-ad0ec8ed5751", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '677bd772-8c07-4102-853e-29edc85294fb' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179107, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "6ntgx-1748475179102-ad0ec8ed5751", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 398, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179085, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "mfkxr-1748475179082-39b86cc3884e", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 340, "region": "iad1", "maxMemoryUsed": 116, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:59", "timestampInMs": 1748475179023, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178978, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178329, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178329, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178329, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178329, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178311, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "mfkxr-1748475178288-2b62b807b218", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 753, "region": "iad1", "maxMemoryUsed": 126, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178293, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178293, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178293, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178293, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:58", "timestampInMs": 1748475178281, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xc8bf-1748475178278-028a10b14468", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 732, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177951, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xfcq5-1748475177878-1f88e7c665b4", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177924, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177890-a558d3e6d6af", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177893, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "27hjf-1748475177890-a558d3e6d6af", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 45, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177887, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177862-1a0f37959f3a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177886, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xfcq5-1748475177878-1f88e7c665b4", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 247, "region": "iad1", "maxMemoryUsed": 110, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177867, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "27hjf-1748475177862-1a0f37959f3a", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 80, "region": "iad1", "maxMemoryUsed": 117, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177864, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177859, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "sml6m-1748475177830-0923383618e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177856, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177856, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5h8zz-1748475177830-c89cc339fa1f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177855, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qck78-1748475177836-7076c01409cc", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177851, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177830-e21f5449d1a8", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177850, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zrpxr-1748475177822-bf114b1fe596", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177841, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qck78-1748475177836-7076c01409cc", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 93, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177837, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "5h8zz-1748475177830-c89cc339fa1f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 102, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177836, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177810-d2f1e74afac7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177835, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "sml6m-1748475177830-0923383618e6", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 59, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177834, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475177830-e21f5449d1a8", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 102, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177828, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177825, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177825, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177825, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177825, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177825, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "zrpxr-1748475177822-bf114b1fe596", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 100, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177821, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177821, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177821, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177821, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177813, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "27hjf-1748475177806-8d1ab4f93b6c", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 44, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177813, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "86d9s-1748475177810-d2f1e74afac7", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 95, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177810, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475177806-d92a68652dd2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 39, "region": "iad1", "maxMemoryUsed": 120, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177799, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177796, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177793, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177792, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177783, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177782, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177776, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177776, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177776, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177776, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177757, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475177746-8160e19c5316", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 61, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177732, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177732, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177732, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177732, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177731, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177731, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177731, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177731, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177729, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177729, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177729, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177729, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177718, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475177715-8f17a8c7a966", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 71, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177717, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "q4qf7-1748475177715-5955d1594278", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 57, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177716, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177715, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177715, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177715, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177714, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c5mz9-1748475177711-1b7f0fa2fc6b", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 69, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177714, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177714, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177714, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177713, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177711, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177711, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177711, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177711, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177704, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177704, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177704, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177704, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177704, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177701, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177701, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177701, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177701, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177701, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177689, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "27hjf-1748475177680-ccc361789ace", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 96, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177688, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c5mz9-1748475177668-f2e63d074d08", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 7, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177687, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "86d9s-1748475177680-813d24a8f1fa", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 104, "region": "iad1", "maxMemoryUsed": 120, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177687, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "b5rsz-1748475177680-1e3c798f06bd", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 78, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177685, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177685, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177685, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177685, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177685, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177684, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475177668-869214d2b077", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 5, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:57", "timestampInMs": 1748475177673, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "q4qf7-1748475177668-cf0ba79ed276", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 5, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170963, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170963, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170963, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170963, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170963, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170949, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2pxtk-1748475170945-f92693401f95", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 5, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170803, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "4qgk7-1748475170782-db7cabde68a2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: '571390ac-5a83-46b2-ad3a-18b9cf39d701' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170787, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "4qgk7-1748475170782-db7cabde68a2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 110, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170746, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170608, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170608, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170608, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170608, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170608, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170607, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170607, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170607, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170607, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170592, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "88tsr-1748475170588-ba011fb6f409", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 144, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:32:50", "timestampInMs": 1748475170590, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qtlm5-1748475170588-013d5efa6c68", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 4, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023871, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'refresh',\n  hasAttorney: false,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023871, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'refresh' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023871, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: refresh", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023871, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'refresh',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023871, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023855, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "5t9q5-1748475023848-ef2427a468bc", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 4, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023717, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023699-e8d0236b663b", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023715, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xvv6j-1748475023699-35512482d8f9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023715, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "8rrdn-1748475023699-8f2d1bc63fc6", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023705, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "g78sb-1748475023699-e8d0236b663b", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 39, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023702, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "8rrdn-1748475023699-8f2d1bc63fc6", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 80, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023701, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xvv6j-1748475023699-35512482d8f9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 70, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023674, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "w9xh7-1748475023640-f279ea729a13", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023668, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023640-b99b71d21df3", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023647, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "g78sb-1748475023640-b99b71d21df3", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 115, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023643, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "w9xh7-1748475023640-f279ea729a13", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 94, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023635, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023611-28e4a7f20758", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023635, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xvv6j-1748475023611-d027f5aec2a2", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023616, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xvv6j-1748475023611-d027f5aec2a2", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 56, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023615, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "cfq8t-1748475023611-28e4a7f20758", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 74, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023613, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "8rrdn-1748475023587-341e4fd227ab", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023596, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023593, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "8rrdn-1748475023587-341e4fd227ab", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 87, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023592, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023591, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023569, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023568, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023557, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023551, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023535, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023487, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023487, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023486, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023486, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023485, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023485, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023485, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023484, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023474, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023474, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023474, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023472, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023470, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023470, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023470, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023470, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023463, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023463, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023463, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023463, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023459, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023459, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023459, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023459, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023455, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "zks2m-1748475023451-cb17f292d8d9", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 101, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023454, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "cfq8t-1748475023451-6b0f44665fb1", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 128, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023450, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023450, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023450, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023450, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023449, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "g78sb-1748475023445-3f5849de6600", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 111, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023448, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "95rvk-1748475023445-3769bb9623ef", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 131, "region": "iad1", "maxMemoryUsed": 120, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023443, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023443, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023443, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023443, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023436, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "nbhf2-1748475023432-153b4d5e0547", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 108, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023426, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "wqx8x-1748475023419-89607ea3b00d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 92, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023423, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "k8f5v-1748475023419-27b8c592c7ed", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 159, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:30:23", "timestampInMs": 1748475023422, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "bht44-1748475023419-e45f813aa789", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 110, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902093, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474902072-5bbecb010ff7", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902074, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xw4sb-1748474902072-5bbecb010ff7", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 81, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902060, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474902036-03c4cf5c189d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902039, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m4qtr-1748474902020-9b0d3be6af98", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902039, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "lb8r7-1748474902036-03c4cf5c189d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 72, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902036, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474902016-dadd50b1853b", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902036, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474902016-95519561d7b5", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902035, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474902016-356ef8dc8e21", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902035, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474902016-8f52729c92f4", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902023, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m4qtr-1748474902020-9b0d3be6af98", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 93, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902020, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c45m7-1748474902016-dadd50b1853b", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 85, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902020, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2dvz5-1748474902016-356ef8dc8e21", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 78, "region": "iad1", "maxMemoryUsed": 117, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902020, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qxfz4-1748474902016-95519561d7b5", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 88, "region": "iad1", "maxMemoryUsed": 125, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902019, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "thlbb-1748474902016-8f52729c92f4", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 87, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902011, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901997-43cb8cbe272d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side checkPreviewConsistency called with: { attorneyId: 'cc7b1c9b-38eb-40db-8f50-7acbc5485e36' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:22", "timestampInMs": 1748474902002, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901999, "requestPath": "legalscout.net/api/sync-tools/check-preview-consistency", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xw4sb-1748474901997-43cb8cbe272d", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/check-preview-consistency", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 63, "region": "iad1", "maxMemoryUsed": 118, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901976, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901968, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901968, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901968, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901968, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901965, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901957, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901954, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901953, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "vktnz-1748474901910-25c93a212c29", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 39, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901951, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901948, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901938, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "manageAuthState result: {\n  success: true,\n  action: 'login',\n  hasAttorney: true,\n  message: 'Auth state managed successfully'\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901899, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901899, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901899, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901899, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901898, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901898, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901898, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901898, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901897, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901897, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901897, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901897, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901893, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901893, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901893, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901893, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901887, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901887, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901887, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901886, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901884, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "qxfz4-1748474901878-bd630f539006", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 62, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901884, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "thlbb-1748474901878-4487d84cea54", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 66, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901882, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "c45m7-1748474901878-0462a05d4b50", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 57, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901877, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901877, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901876, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901876, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901876, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "lb8r7-1748474901870-296e7603d206", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 59, "region": "iad1", "maxMemoryUsed": 120, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901874, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "mwdg4-1748474901870-8b521a76b2be", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 93, "region": "iad1", "maxMemoryUsed": 121, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901868, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Server-side manageAuthState called with: { authData: true, action: 'login' }", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901867, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Calling manageAuthState with action: login", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901867, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Request body: {\n  action: 'login',\n  authData: {\n    hasUser: true,\n    hasSession: true,\n    userEmail: '<EMAIL>',\n    userId: '3ec75e07-5c85-4ec9-bdea-0f5d628ac8a3'\n  }\n}", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901867, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Received manage-auth-state request", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901856, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "xw4sb-1748474901850-ea3d178bb112", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 100, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}, {"TimeUTC": "2025-05-28 23:28:21", "timestampInMs": 1748474901855, "requestPath": "legalscout.net/api/sync-tools/manage-auth-state", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "2dvz5-1748474901850-55c18a80243c", "requestUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/sync-tools/manage-auth-state", "host": "legalscout.net", "deploymentDomain": "legalscout.net", "deploymentId": "dpl_DG5SSpe5wUL7pfFUcSs4SHDeftHC", "durationMs": 77, "region": "iad1", "maxMemoryUsed": 122, "memorySize": 1024, "message": "", "projectId": "prj_2xsZEj57IDtvmNPyUefIr41cO4KK", "traceId": "", "sessionId": ""}]