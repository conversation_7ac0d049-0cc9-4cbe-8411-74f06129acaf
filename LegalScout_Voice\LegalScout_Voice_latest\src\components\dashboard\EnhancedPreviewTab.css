.enhanced-preview-tab {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1.5rem;
}

/* Header */
.preview-tab-header {
  margin-bottom: 1.5rem;
}

.tab-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #1f2937);
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.tab-description {
  font-size: 1rem;
  color: var(--text-secondary, #4b5563);
  margin-top: 0;
  margin-bottom: 1.5rem;
}

/* Sync status */
.sync-status {
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sync-status.idle {
  display: none;
}

.sync-status.syncing {
  background-color: var(--info-background, rgba(59, 130, 246, 0.1));
  border-left: 4px solid var(--info-color, #3b82f6);
  color: var(--info-text, #1e40af);
}

.sync-status.success {
  background-color: var(--success-background, rgba(16, 185, 129, 0.1));
  border-left: 4px solid var(--success-color, #10b981);
  color: var(--success-text, #065f46);
}

.sync-status.error {
  background-color: var(--error-background, rgba(239, 68, 68, 0.1));
  border-left: 4px solid var(--error-color, #ef4444);
  color: var(--error-text, #b91c1c);
}

/* Error message */
.preview-error {
  padding: 1rem;
  background-color: var(--error-background, rgba(239, 68, 68, 0.1));
  border-left: 4px solid var(--error-color, #ef4444);
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
}

.preview-error p {
  margin: 0;
  color: var(--error-text, #b91c1c);
  font-size: 0.875rem;
}

/* Controls */
.preview-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.theme-toggle-button,
.preview-toggle-button,
.sync-button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.theme-toggle-button {
  background-color: var(--secondary-color, #6b7280);
  color: white;
}

.theme-toggle-button:hover {
  background-color: var(--secondary-color-dark, #4b5563);
}

.preview-toggle-button {
  background-color: var(--primary-color, #3b82f6);
  color: white;
}

.preview-toggle-button:hover {
  background-color: var(--primary-color-dark, #2563eb);
}

.sync-button {
  background-color: var(--accent-color, #10b981);
  color: white;
}

.sync-button:hover {
  background-color: var(--accent-color-dark, #059669);
}

.sync-button:disabled {
  background-color: var(--disabled-color, #d1d5db);
  color: var(--disabled-text, #9ca3af);
  cursor: not-allowed;
}

/* Preview container */
.preview-container {
  width: 100%;
  height: 600px;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Footer */
.preview-tab-footer {
  padding: 1.5rem;
  background-color: var(--footer-background, rgba(0, 0, 0, 0.02));
  border-radius: 0.75rem;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

.preview-tab-footer h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin-top: 0;
  margin-bottom: 1rem;
}

.preview-tab-footer p {
  font-size: 0.875rem;
  color: var(--text-secondary, #4b5563);
  margin-top: 0;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.preview-tab-footer p:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-preview-tab {
    padding: 1rem;
  }

  .preview-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .preview-container {
    height: 500px;
  }
}
