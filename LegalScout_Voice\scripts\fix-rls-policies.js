/**
 * Fix RLS Policies for Consultations
 * 
 * This script tests and fixes RLS policies to allow consultation creation.
 * Run this in browser console while logged into the dashboard.
 */

async function fixRlsPolicies() {
  console.log('🔧 Fixing RLS Policies for Consultations...\n');

  try {
    const supabase = window.supabase || window.supabaseClient;
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }

    // 1. Get current user and attorney
    console.log('1️⃣ Getting current user and attorney...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ No authenticated user found');
      return;
    }

    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();

    if (attorneyError) {
      console.error('❌ Attorney not found:', attorneyError);
      return;
    }

    console.log('✅ User ID:', user.id);
    console.log('✅ Attorney ID:', attorney.id);
    console.log('✅ Attorney user_id:', attorney.user_id);

    // 2. Test the RLS policy condition manually
    console.log('\n2️⃣ Testing RLS policy condition...');
    
    // This simulates the exact RLS policy check
    const { data: policyTest, error: policyError } = await supabase
      .from('attorneys')
      .select('id')
      .eq('user_id', user.id)
      .eq('id', attorney.id);

    if (policyError) {
      console.error('❌ Policy test failed:', policyError);
    } else {
      console.log('✅ Policy test result:', policyTest.length > 0 ? 'SHOULD ALLOW' : 'SHOULD DENY');
    }

    // 3. Try a different approach - temporarily disable RLS and test
    console.log('\n3️⃣ Testing with service role approach...');
    
    // Since we can't disable RLS from frontend, let's try a different strategy
    // Let's test if the issue is with the specific fields we're inserting
    
    const minimalTest = {
      attorney_id: attorney.id,
      client_name: 'RLS Test Client'
    };

    console.log('Testing minimal insert:', minimalTest);
    
    const { data: minimalResult, error: minimalError } = await supabase
      .from('consultations')
      .insert(minimalTest)
      .select();

    if (minimalError) {
      console.error('❌ Minimal insert failed:', minimalError);
      
      // 4. Try with even more minimal data
      console.log('\n4️⃣ Trying with absolute minimal data...');
      
      const absoluteMinimal = {
        attorney_id: attorney.id
      };

      const { data: absResult, error: absError } = await supabase
        .from('consultations')
        .insert(absoluteMinimal)
        .select();

      if (absError) {
        console.error('❌ Absolute minimal insert failed:', absError);
        
        // 5. The issue might be with the RLS policy itself
        console.log('\n5️⃣ RLS policy issue detected. Attempting workaround...');
        
        // Let's try using the service role key if available in environment
        // This is a temporary workaround to test if RLS is the issue
        
        console.log('💡 WORKAROUND NEEDED:');
        console.log('The RLS policy is blocking inserts even though it should allow them.');
        console.log('This suggests an issue with the auth.uid() function in the RLS policy.');
        
        // Let's create a test using a different approach
        console.log('\n6️⃣ Testing alternative approach...');
        
        // Try to insert using a raw SQL approach (won't work from frontend, but let's see)
        const rawInsert = `
          INSERT INTO consultations (attorney_id, client_name, status)
          VALUES ('${attorney.id}', 'Raw SQL Test', 'new')
          RETURNING *;
        `;
        
        console.log('Raw SQL that should work:', rawInsert);
        console.log('(This can\'t be executed from frontend for security reasons)');
        
      } else {
        console.log('✅ Absolute minimal insert worked!');
        console.log('Created:', absResult[0]);
        
        // Clean up
        await supabase
          .from('consultations')
          .delete()
          .eq('id', absResult[0].id);
      }
      
    } else {
      console.log('✅ Minimal insert worked!');
      console.log('Created:', minimalResult[0]);
      
      // Clean up
      await supabase
        .from('consultations')
        .delete()
        .eq('id', minimalResult[0].id);
    }

    // 7. Provide manual fix instructions
    console.log('\n📋 RLS POLICY FIX INSTRUCTIONS:');
    console.log('='.repeat(50));
    
    if (minimalError && minimalError.code === '42501') {
      console.log('❌ RLS policies are blocking valid inserts');
      console.log('\n💡 MANUAL FIX REQUIRED:');
      console.log('1. Go to Supabase Dashboard > Authentication > Policies');
      console.log('2. Find the "Users can insert their own consultations" policy');
      console.log('3. Edit the policy and change the WITH CHECK condition to:');
      console.log('   EXISTS (SELECT 1 FROM attorneys WHERE attorneys.id = attorney_id AND attorneys.user_id = auth.uid())');
      console.log('\n🔧 OR run this SQL in Supabase SQL Editor:');
      console.log(`
DROP POLICY IF EXISTS "Users can insert their own consultations" ON consultations;

CREATE POLICY "Users can insert their own consultations" 
ON consultations FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM attorneys 
    WHERE attorneys.id = attorney_id 
    AND attorneys.user_id = auth.uid()
  )
);
      `);
      
      console.log('\n🆔 Your Details for Manual Fix:');
      console.log(`User ID: ${user.id}`);
      console.log(`Attorney ID: ${attorney.id}`);
      console.log(`Attorney user_id: ${attorney.user_id}`);
      
    } else {
      console.log('✅ RLS policies appear to be working correctly');
      console.log('The issue might be elsewhere in the application');
    }

  } catch (error) {
    console.error('❌ RLS fix failed:', error);
  }
}

// Auto-run
console.log(`
🔧 RLS POLICY FIX:
==================

This script will test and attempt to fix RLS policy issues.
Running automatically...
`);

if (typeof window !== 'undefined' && window.location && window.location.pathname.includes('dashboard')) {
  fixRlsPolicies();
}
