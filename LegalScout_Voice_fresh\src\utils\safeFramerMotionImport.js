/**
 * Safe Framer Motion Import
 *
 * This utility provides a safe way to import Framer Motion components and hooks.
 * It falls back to mock implementations if Framer Motion fails to load.
 */

import React from 'react';
import { LayoutGroupContext } from '../components/LayoutGroupContext';

// Create simple mock implementations as fallbacks
const createMockImplementations = () => {
  console.warn('[SafeFramerMotion] Using mock implementations');

  return {
    motion: {
      div: 'div',
      span: 'span',
      button: 'button',
      img: 'img',
      section: 'section',
      article: 'article',
      header: 'header',
      footer: 'footer',
      nav: 'nav',
      main: 'main',
      aside: 'aside',
      p: 'p',
      h1: 'h1',
      h2: 'h2',
      h3: 'h3',
      h4: 'h4',
      h5: 'h5',
      h6: 'h6',
      ul: 'ul',
      ol: 'ol',
      li: 'li',
      a: 'a'
    },
    AnimatePresence: ({ children }) => children,
    LayoutGroup: ({ children }) => children,
    useAnimation: () => ({
      start: () => Promise.resolve(),
      stop: () => {},
      set: () => {}
    }),
    useMotionValue: (initial) => ({
      get: () => initial,
      set: () => {},
      onChange: () => () => {}
    }),
    useTransform: () => ({
      get: () => 0,
      set: () => {},
      onChange: () => () => {}
    }),
    useSpring: (initial) => ({
      get: () => initial,
      set: () => {},
      onChange: () => () => {}
    }),
    useScroll: () => ({
      scrollY: { get: () => 0, onChange: () => () => {} },
      scrollX: { get: () => 0, onChange: () => () => {} }
    }),
    useCycle: (...items) => [items[0] || null, () => {}]
  };
};

// Use mock implementations by default to avoid require issues
const mockImplementations = createMockImplementations();

// Export the mock components and hooks
export const motion = mockImplementations.motion;
export const AnimatePresence = mockImplementations.AnimatePresence;
export const LayoutGroup = mockImplementations.LayoutGroup;
export const useAnimation = mockImplementations.useAnimation;
export const useMotionValue = mockImplementations.useMotionValue;
export const useTransform = mockImplementations.useTransform;
export const useSpring = mockImplementations.useSpring;
export const useScroll = mockImplementations.useScroll;
export const useCycle = mockImplementations.useCycle;

// Also export LayoutGroupContext
export { LayoutGroupContext };
