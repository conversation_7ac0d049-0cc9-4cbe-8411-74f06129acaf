import express from 'express';
import path from 'path';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import cors from 'cors';
import { fileURLToPath } from 'url';
// import callLogsRouter from './src/api/callLogsApi.js';
import { manageAuthState, syncAttorneyProfile, validateConfiguration, checkPreviewConsistency } from './src/services/syncTools.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Load environment variables
dotenv.config();
// Also load .env.local if it exists
try {
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('No .env.local file found, using only .env');
}

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept', 'X-API-KEY', 'X-API-SECRET'],
  credentials: true,
  maxAge: 86400
}));

// Handle preflight requests
app.options('*', (req, res) => {
  res.status(200).end();
});

// Add middleware to parse JSON requests
app.use(express.json());

// Serve the entire build folder (including assets)
app.use(express.static(path.join(__dirname, 'build')));

// Vapi API credentials
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY;
const VAPI_PUBLIC_KEY = process.env.VAPI_PUBLIC_KEY;
const VAPI_HOST_URL = process.env.VAPI_HOST_URL || 'https://api.vapi.ai';

// Add Vapi webhook endpoint
app.post('/api/webhook/vapi', (req, res) => {
  console.log('Received Vapi webhook:', req.body);

  // You can process the webhook data here
  // For example, you could store it in a database

  // Return a success response
  res.status(200).json({ success: true });
});

// Vapi proxy API endpoints
const vapiRouter = express.Router();

// Middleware to check if Vapi is configured
const checkVapiConfigured = (req, res, next) => {
  if (!VAPI_SECRET_KEY || !VAPI_PUBLIC_KEY) {
    return res.status(503).json({
      error: 'Vapi is not configured. Please set up Vapi credentials in the environment variables.'
    });
  }
  next();
};

// Create headers for Vapi API requests
const createVapiHeaders = () => {
  const headers = {
    'Content-Type': 'application/json'
  };

  if (VAPI_PUBLIC_KEY) {
    headers['X-API-KEY'] = VAPI_PUBLIC_KEY;
  }

  if (VAPI_SECRET_KEY) {
    headers['X-API-SECRET'] = VAPI_SECRET_KEY;
  }

  return headers;
};

// Get Vapi configuration status
vapiRouter.get('/config', (req, res) => {
  try {
    const configured = !!(VAPI_SECRET_KEY && VAPI_PUBLIC_KEY);

    // Create a safe response that doesn't include sensitive credentials
    const config = {
      configured,
      hostUrl: configured ? 'Configured' : 'Not configured',
      publicKey: configured ? 'Configured' : 'Not configured',
      secretKey: configured ? 'Configured' : 'Not configured'
    };

    res.json(config);
  } catch (error) {
    console.error('Error getting Vapi config:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all assistants
vapiRouter.get('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistants = await response.json();
    res.json(assistants);
  } catch (error) {
    console.error('Error getting assistants:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get an assistant by ID
vapiRouter.get('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants/${req.params.id}`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error getting assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Special endpoint for the problematic assistant ID with enhanced CORS handling
app.all('/api/assistant/:id', async (req, res) => {
  // Set CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.header('Access-Control-Max-Age', '86400');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const assistantId = req.params.id;

  // Get API key from environment variables
  const apiKey = VAPI_PUBLIC_KEY || VAPI_SECRET_KEY;

  if (!apiKey) {
    return res.status(401).json({ error: 'No API key available' });
  }

  try {
    // Construct the Vapi API URL
    const vapiUrl = `${VAPI_HOST_URL}/assistants/${assistantId}`;

    // Create headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add authorization
    if (VAPI_PUBLIC_KEY) {
      headers['X-API-KEY'] = VAPI_PUBLIC_KEY;
    }

    if (VAPI_SECRET_KEY) {
      headers['X-API-SECRET'] = VAPI_SECRET_KEY;
    }

    // Forward the request to the Vapi API
    const response = await fetch(vapiUrl, {
      method: req.method,
      headers: headers,
      body: req.method !== 'GET' && req.method !== 'HEAD' && req.body ?
            JSON.stringify(req.body) : undefined
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Error proxying request to Vapi API:', error);
    res.status(500).json({ error: 'Error proxying request to Vapi API' });
  }
});

// Create a new assistant
vapiRouter.post('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants`, {
      method: 'POST',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error creating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update an assistant
vapiRouter.patch('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants/${req.params.id}`, {
      method: 'PATCH',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error updating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all phone numbers
vapiRouter.get('/phone-numbers', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/phone-numbers`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const phoneNumbers = await response.json();
    res.json(phoneNumbers);
  } catch (error) {
    console.error('Error getting phone numbers:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create a new call
vapiRouter.post('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/calls`, {
      method: 'POST',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const call = await response.json();
    res.json(call);
  } catch (error) {
    console.error('Error creating call:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all calls
vapiRouter.get('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/calls`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const calls = await response.json();
    res.json(calls);
  } catch (error) {
    console.error('Error getting calls:', error);
    res.status(500).json({ error: error.message });
  }
});

// Mount the Vapi router
app.use('/api/vapi', vapiRouter);

// Mount the call logs API
app.use('/api/call-logs', callLogsRouter);

// Sync Tools API routes
const syncToolsRouter = express.Router();

// Manage Auth State
syncToolsRouter.post('/manage-auth-state', async (req, res) => {
  try {
    console.log('Received manage-auth-state request');
    const { authData, action } = req.body;

    // Log request details for debugging
    console.log('Request body:', {
      action,
      authData: authData ? {
        hasUser: !!authData.user,
        hasSession: !!authData.session,
        userEmail: authData.user?.email,
        userId: authData.user?.id
      } : null
    });

    // Validate required parameters
    if (!authData || !action) {
      console.log('Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: authData and action are required'
      });
    }

    // Call the manageAuthState function
    console.log(`Calling manageAuthState with action: ${action}`);
    const result = await manageAuthState({ authData, action });

    // Log the result for debugging
    console.log('manageAuthState result:', {
      success: result.success,
      action: result.action,
      hasAttorney: !!result.attorney,
      message: result.message
    });

    // Return the result
    return res.status(200).json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error managing auth state:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Sync Attorney Profile
syncToolsRouter.post('/sync-attorney-profile', async (req, res) => {
  try {
    const { attorneyId, forceUpdate } = req.body;

    // Call the syncAttorneyProfile function
    const result = await syncAttorneyProfile({ attorneyId, forceUpdate });

    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error syncing attorney profile:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Validate Configuration
syncToolsRouter.post('/validate-configuration', async (req, res) => {
  try {
    const { attorneyId, configData } = req.body;

    // Call the validateConfiguration function
    const result = await validateConfiguration({ attorneyId, configData });

    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error validating configuration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Check Preview Consistency
syncToolsRouter.post('/check-preview-consistency', async (req, res) => {
  try {
    const { configData } = req.body;

    // Call the checkPreviewConsistency function
    const result = await checkPreviewConsistency({ configData });

    // Return the result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error checking preview consistency:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Mount the sync tools router
app.use('/api/sync-tools', syncToolsRouter);

// Fallback to serve index.html for any route not matching a static file or API route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});