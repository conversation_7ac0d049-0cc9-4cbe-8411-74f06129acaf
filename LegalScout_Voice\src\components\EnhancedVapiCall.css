.enhanced-vapi-call {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: var(--card-background, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Header */
.call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  background-color: var(--header-background, rgba(0, 0, 0, 0.02));
}

/* Status indicator */
.call-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.call-status.idle .status-indicator {
  background-color: var(--idle-color, #9ca3af);
}

.call-status.connecting .status-indicator {
  background-color: var(--connecting-color, #fbbf24);
  animation: pulse 1s infinite;
}

.call-status.active .status-indicator {
  background-color: var(--active-color, #10b981);
  animation: pulse 1.5s infinite;
}

.call-status.ending .status-indicator {
  background-color: var(--ending-color, #f59e0b);
  animation: pulse 1s infinite;
}

.call-status.ended .status-indicator {
  background-color: var(--ended-color, #6b7280);
}

.call-status.error .status-indicator {
  background-color: var(--error-color, #ef4444);
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary, #4b5563);
}

/* End call button */
.end-call-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: var(--error-color, #ef4444);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.end-call-button:hover {
  background-color: var(--error-color-dark, #dc2626);
}

.end-call-button:disabled {
  background-color: var(--disabled-color, #d1d5db);
  cursor: not-allowed;
}

/* Visualization container */
.visualization-container {
  width: 100%;
  height: 150px;
  overflow: hidden;
  background-color: var(--visualization-background, rgba(0, 0, 0, 0.02));
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Error message */
.error-message {
  padding: 1rem;
  background-color: var(--error-background, rgba(239, 68, 68, 0.1));
  border-left: 4px solid var(--error-color, #ef4444);
  margin: 1rem;
  border-radius: 0.5rem;
}

.error-message p {
  margin: 0;
  color: var(--error-text, #b91c1c);
  font-size: 0.875rem;
}

/* Call content */
.call-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 1rem;
}

/* Dossier */
.dossier-container {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--dossier-background, rgba(0, 0, 0, 0.02));
  border-radius: 0.5rem;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  /* Add thin blue piping around the edges per user preference */
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
}

.dossier-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.dossier-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
}

.dossier-item {
  display: flex;
  flex-direction: column;
}

.dossier-key {
  font-size: 0.75rem;
  color: var(--text-secondary, #4b5563);
  text-transform: capitalize;
}

.dossier-value {
  font-size: 0.875rem;
  color: var(--text-primary, #1f2937);
  font-weight: 500;
}

/* Conversation */
.conversation-container {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-right: 0.5rem;
  /* Add transparent scrollbar with light thin blue piping per user preference */
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) transparent;
}

.conversation-container::-webkit-scrollbar {
  width: 6px;
}

.conversation-container::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 3px;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.empty-conversation {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-tertiary, #9ca3af);
  font-style: italic;
}

.message {
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--message-background, rgba(0, 0, 0, 0.02));
  border-left: 3px solid transparent;
}

.message.assistant {
  border-left-color: var(--assistant-color, #3b82f6);
  background-color: var(--assistant-background, rgba(59, 130, 246, 0.05));
}

.message.user {
  border-left-color: var(--user-color, #10b981);
  background-color: var(--user-background, rgba(16, 185, 129, 0.05));
}

.message-header {
  margin-bottom: 0.5rem;
}

.message-role {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary, #4b5563);
}

.message-content p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary, #1f2937);
}

/* Animation */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Status-specific styles */
.enhanced-vapi-call.connecting .visualization-container,
.enhanced-vapi-call.ending .visualization-container {
  opacity: 0.5;
}

.enhanced-vapi-call.ended .visualization-container,
.enhanced-vapi-call.error .visualization-container,
.enhanced-vapi-call.idle .visualization-container {
  opacity: 0.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-vapi-call {
    min-height: 300px;
  }
  
  .visualization-container {
    height: 100px;
  }
  
  .dossier-content {
    grid-template-columns: 1fr;
  }
}
