import React, { useState, useEffect } from 'react';
import { getCurrentSubdomain } from '../../utils/subdomainTester';
import { getAttorneyConfigAsync } from '../../config/attorneys';
import { mapDatabaseToPreview } from '../../utils/configMapping';
import EnhancedPreviewNew from './EnhancedPreviewNew';

/**
 * PreviewFrameLoader - Loads attorney configuration and renders the preview
 * This component is used in the /preview-frame route to show the actual attorney's
 * configuration instead of hardcoded default values.
 */
const PreviewFrameLoader = () => {
  const [attorneyConfig, setAttorneyConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showActivateButton, setShowActivateButton] = useState(false);
  const [activating, setActivating] = useState(false);

  useEffect(() => {
    const loadAttorneyConfig = async () => {
      try {
        console.log('[PreviewFrameLoader] Loading attorney configuration...');

        // Get the current subdomain
        const subdomain = getCurrentSubdomain();
        console.log('[PreviewFrameLoader] Current subdomain:', subdomain);

        // Load attorney configuration from the subdomain
        console.log('[PreviewFrameLoader] Loading config for subdomain:', subdomain);
        const config = await getAttorneyConfigAsync(subdomain);
        console.log('[PreviewFrameLoader] Raw attorney config loaded:', {
          hasConfig: !!config,
          firmName: config?.firmName,
          vapi_assistant_id: config?.vapi_assistant_id,
          id: config?.id,
          subdomain: config?.subdomain,
          isFallback: config?.isFallback,
          configKeys: config ? Object.keys(config) : []
        });

        if (config) {
          // Check if this is a fallback configuration (indicating database lookup failed)
          const isFallbackConfig = config.isFallback ||
                                  (config.firmName === 'Your Law Firm' && !config.id) ||
                                  (!config.id && subdomain === 'damon');

          console.log('[PreviewFrameLoader] Config analysis:', {
            isFallbackConfig,
            hasId: !!config.id,
            firmName: config.firmName,
            subdomain: subdomain
          });

          // Map the database configuration to preview props
          const previewConfig = mapDatabaseToPreview(config);
          console.log('[PreviewFrameLoader] Mapped preview config:', {
            firmName: previewConfig.firmName,
            titleText: previewConfig.titleText,
            vapiAssistantId: previewConfig.vapiAssistantId,
            welcomeMessage: previewConfig.welcomeMessage
          });

          setAttorneyConfig({
            ...previewConfig,
            // Ensure we have the Vapi assistant ID
            vapiAssistantId: config.vapi_assistant_id || previewConfig.vapiAssistantId,
            // Include all the original config for fallback
            ...config,
            // Add flag to indicate if this is fallback config
            isFallbackConfig
          });

          // Show activate button for damon subdomain if using fallback config
          if (isFallbackConfig && subdomain === 'damon') {
            console.log('[PreviewFrameLoader] Fallback config detected for damon, showing activate button');
            setShowActivateButton(true);
          }
        } else {
          console.warn('[PreviewFrameLoader] No attorney config found, using defaults');
          // Use default configuration if no attorney config is found
          setAttorneyConfig({
            firmName: "Your Law Firm",
            titleText: "Your Law Firm",
            primaryColor: "#4B74AA",
            secondaryColor: "#2C3E50",
            backgroundColor: "#1a1a1a",
            backgroundOpacity: 0.9,
            buttonText: "Start Consultation",
            buttonOpacity: 1,
            practiceAreaBackgroundOpacity: 0.1,
            textBackgroundColor: "#634C38",
            welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
            informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",
            logoUrl: "/PRIMARY CLEAR.png",
            mascot: "/PRIMARY CLEAR.png",
            theme: "dark",
            vapiAssistantId: null
          });
        }
      } catch (err) {
        console.error('[PreviewFrameLoader] Error loading attorney config:', err);
        setError(err.message);

        // Use default configuration on error
        setAttorneyConfig({
          firmName: "Your Law Firm",
          titleText: "Your Law Firm",
          primaryColor: "#4B74AA",
          secondaryColor: "#2C3E50",
          backgroundColor: "#1a1a1a",
          backgroundOpacity: 0.9,
          buttonText: "Start Consultation",
          buttonOpacity: 1,
          practiceAreaBackgroundOpacity: 0.1,
          textBackgroundColor: "#634C38",
          welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
          informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",
          logoUrl: "/PRIMARY CLEAR.png",
          mascot: "/PRIMARY CLEAR.png",
          theme: "dark",
          vapiAssistantId: null
        });
      } finally {
        setLoading(false);
      }
    };

    loadAttorneyConfig();
  }, []);

  // Function to activate the assistant for the current subdomain
  const activateAssistant = async () => {
    setActivating(true);
    try {
      console.log('[PreviewFrameLoader] Activating assistant for subdomain:', getCurrentSubdomain());

      // Force a fresh load of the attorney configuration
      const subdomain = getCurrentSubdomain();
      const config = await getAttorneyConfigAsync(subdomain);

      console.log('[PreviewFrameLoader] Activation - loaded config:', {
        hasConfig: !!config,
        firmName: config?.firmName,
        assistantId: config?.vapi_assistant_id,
        id: config?.id
      });

      if (config && config.vapi_assistant_id) {
        // If we have a valid config with assistant ID, update the preview
        const previewConfig = mapDatabaseToPreview(config);
        setAttorneyConfig({
          ...previewConfig,
          vapiAssistantId: config.vapi_assistant_id,
          ...config,
          isFallbackConfig: false
        });
        setShowActivateButton(false);
        console.log('[PreviewFrameLoader] Assistant activated successfully');
      } else {
        console.warn('[PreviewFrameLoader] Activation failed - no valid config or assistant ID found');
      }
    } catch (error) {
      console.error('[PreviewFrameLoader] Error activating assistant:', error);
    } finally {
      setActivating(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#1a1a1a',
        color: 'white'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #333',
          borderTop: '4px solid #4B74AA',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px' }}>Loading preview...</p>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#1a1a1a',
        color: '#ff6b6b',
        padding: '20px',
        textAlign: 'center'
      }}>
        <h2>Preview Error</h2>
        <p>Failed to load attorney configuration: {error}</p>
        <p style={{ color: '#ccc', marginTop: '20px' }}>
          Using default configuration instead.
        </p>
      </div>
    );
  }

  // Render the preview with the loaded configuration
  return (
    <div style={{ position: 'relative', width: '100%', height: '100vh' }}>
      <EnhancedPreviewNew
        {...attorneyConfig}
      />

      {/* Activate Assistant Button Overlay - Mobile Responsive */}
      {showActivateButton && (
        <div style={{
          position: 'fixed',
          bottom: window.innerWidth <= 768 ? '20px' : 'auto',
          top: window.innerWidth <= 768 ? 'auto' : '20px',
          left: window.innerWidth <= 768 ? '50%' : 'auto',
          right: window.innerWidth <= 768 ? 'auto' : '20px',
          transform: window.innerWidth <= 768 ? 'translateX(-50%)' : 'none',
          zIndex: 9999,
          backgroundColor: 'rgba(173, 216, 230, 0.1)', // Hollow light blue
          padding: window.innerWidth <= 768 ? '12px 20px' : '15px',
          borderRadius: window.innerWidth <= 768 ? '25px' : '8px',
          border: '1px solid rgba(173, 216, 230, 0.8)', // Thin light blue piping
          color: '#87CEEB', // Light blue text
          textAlign: 'center',
          maxWidth: window.innerWidth <= 768 ? '90vw' : '250px',
          minWidth: window.innerWidth <= 768 ? '280px' : 'auto',
          boxShadow: '0 2px 15px rgba(173, 216, 230, 0.2)',
          backdropFilter: 'blur(15px)'
        }}>
          <div style={{
            marginBottom: window.innerWidth <= 768 ? '8px' : '10px',
            fontSize: window.innerWidth <= 768 ? '16px' : '14px',
            fontWeight: 'bold'
          }}>
            Assistant Not Active
          </div>
          {window.innerWidth > 768 && (
            <div style={{ marginBottom: '15px', fontSize: '12px', color: '#ccc' }}>
              Your assistant configuration is not synced with this subdomain.
            </div>
          )}
          <button
            onClick={activateAssistant}
            disabled={activating}
            style={{
              backgroundColor: activating ? 'rgba(173, 216, 230, 0.2)' : 'rgba(173, 216, 230, 0.15)',
              color: activating ? '#B0C4DE' : '#87CEEB',
              border: '1px solid rgba(173, 216, 230, 0.6)',
              padding: window.innerWidth <= 768 ? '12px 24px' : '8px 16px',
              borderRadius: window.innerWidth <= 768 ? '20px' : '4px',
              cursor: activating ? 'not-allowed' : 'pointer',
              fontSize: window.innerWidth <= 768 ? '14px' : '12px',
              fontWeight: 'bold',
              minWidth: window.innerWidth <= 768 ? '120px' : 'auto',
              transition: 'all 0.3s ease',
              backdropFilter: 'blur(10px)'
            }}
          >
            {activating ? 'Activating...' : window.innerWidth <= 768 ? 'Activate' : 'Activate Assistant'}
          </button>
        </div>
      )}
    </div>
  );
};

export default PreviewFrameLoader;
