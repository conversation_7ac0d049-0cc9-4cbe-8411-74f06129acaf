/**
 * Call Logs API
 * 
 * Server-side API for managing call logs with row-level security.
 */

import express from 'express';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Middleware to verify user authentication
 */
const verifyAuth = async (req, res, next) => {
  try {
    // Get JWT from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized: No token provided' });
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Unauthorized: Invalid token' });
    }
    
    // Add user to request
    req.user = user;
    next();
  } catch (error) {
    console.error('Error verifying authentication:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * GET /api/call-logs
 * 
 * Get call logs for the authenticated user
 */
router.get('/', verifyAuth, async (req, res) => {
  try {
    const { user } = req;
    
    // Get attorney IDs associated with this user
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id')
      .eq('user_id', user.id);
    
    if (attorneyError) {
      throw attorneyError;
    }
    
    if (!attorneys || attorneys.length === 0) {
      return res.json([]);
    }
    
    // Get attorney assistant mappings
    const attorneyIds = attorneys.map(a => a.id);
    const { data: assistantMappings, error: mappingError } = await supabase
      .from('attorney_assistants')
      .select('attorney_id, assistant_id')
      .in('attorney_id', attorneyIds);
    
    if (mappingError) {
      throw mappingError;
    }
    
    if (!assistantMappings || assistantMappings.length === 0) {
      return res.json([]);
    }
    
    // Get call logs for these assistants
    const assistantIds = assistantMappings.map(m => m.assistant_id);
    const { data: callLogs, error: callLogsError } = await supabase
      .from('call_logs')
      .select('*')
      .in('assistant_id', assistantIds)
      .order('created_at', { ascending: false });
    
    if (callLogsError) {
      throw callLogsError;
    }
    
    // Return the call logs
    res.json(callLogs || []);
  } catch (error) {
    console.error('Error getting call logs:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * POST /api/call-logs
 * 
 * Create a new call log
 */
router.post('/', verifyAuth, async (req, res) => {
  try {
    const { user } = req;
    const { call_id, assistant_id, customer_phone, duration, status, transcript, summary } = req.body;
    
    // Verify that this assistant belongs to the user
    const { data: assistantMappings, error: mappingError } = await supabase
      .from('attorney_assistants')
      .select('attorney_id')
      .eq('assistant_id', assistant_id)
      .eq('user_id', user.id);
    
    if (mappingError) {
      throw mappingError;
    }
    
    if (!assistantMappings || assistantMappings.length === 0) {
      return res.status(403).json({ error: 'Forbidden: This assistant does not belong to you' });
    }
    
    // Create the call log
    const { data: callLog, error: callLogError } = await supabase
      .from('call_logs')
      .insert({
        call_id,
        assistant_id,
        attorney_id: assistantMappings[0].attorney_id,
        customer_phone,
        duration,
        status,
        transcript,
        summary,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (callLogError) {
      throw callLogError;
    }
    
    // Return the created call log
    res.status(201).json(callLog);
  } catch (error) {
    console.error('Error creating call log:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/call-logs/:id
 * 
 * Get a specific call log
 */
router.get('/:id', verifyAuth, async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Get attorney IDs associated with this user
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id')
      .eq('user_id', user.id);
    
    if (attorneyError) {
      throw attorneyError;
    }
    
    if (!attorneys || attorneys.length === 0) {
      return res.status(404).json({ error: 'Not found' });
    }
    
    // Get the call log
    const { data: callLog, error: callLogError } = await supabase
      .from('call_logs')
      .select('*')
      .eq('id', id)
      .in('attorney_id', attorneys.map(a => a.id))
      .single();
    
    if (callLogError) {
      throw callLogError;
    }
    
    if (!callLog) {
      return res.status(404).json({ error: 'Call log not found' });
    }
    
    // Return the call log
    res.json(callLog);
  } catch (error) {
    console.error('Error getting call log:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
