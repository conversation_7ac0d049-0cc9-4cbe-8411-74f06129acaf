/**
 * This service provides mock attorney data for development and testing purposes.
 * It simulates the behavior of the Apify service with pagination support.
 */

// Collection of mock attorney data with different specialties and locations
const mockAttorneyDatabase = [
  // Family Law attorneys in New York
  {
    name: "<PERSON>",
    firm: "Smith Family Law Group",
    specialty: "Family Law",
    address: "123 Broadway, New York, NY 10001",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.smithfamilylaw.example.com",
    latitude: 40.7128,
    longitude: -74.0060,
    distance: "0.5",
    rating: 4.8,
    reviewCount: 42,
    source: "mock_data",
    placeId: "mock-place-1"
  },
  {
    name: "<PERSON>",
    firm: "Johnson & Associates",
    specialty: "Family Law",
    address: "456 5th Avenue, New York, NY 10018",
    phone: "(*************",
    email: "r<PERSON><PERSON><PERSON>@example.com",
    website: "https://www.johnsonlaw.example.com",
    latitude: 40.7412,
    longitude: -73.9896,
    distance: "1.2",
    rating: 4.5,
    reviewCount: 36,
    source: "mock_data",
    placeId: "mock-place-2"
  },
  {
    name: "<PERSON>",
    firm: "Garcia Family Law",
    specialty: "Family Law",
    address: "789 Park Ave, New York, NY 10021",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.garciafamilylaw.example.com",
    latitude: 40.7681,
    longitude: -73.9646,
    distance: "2.1",
    rating: 4.9,
    reviewCount: 53,
    source: "mock_data",
    placeId: "mock-place-3"
  },
  
  // Criminal Defense attorneys in Los Angeles
  {
    name: "David Kim",
    firm: "Kim Criminal Defense",
    specialty: "Criminal Defense",
    address: "100 Wilshire Blvd, Los Angeles, CA 90001",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.kimdefense.example.com",
    latitude: 34.0522,
    longitude: -118.2437,
    distance: "0.7",
    rating: 4.7,
    reviewCount: 29,
    source: "mock_data",
    placeId: "mock-place-4"
  },
  {
    name: "Sarah Wilson",
    firm: "Wilson Defense Group",
    specialty: "Criminal Defense",
    address: "200 Hollywood Blvd, Los Angeles, CA 90028",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.wilsondefense.example.com",
    latitude: 34.1016,
    longitude: -118.3267,
    distance: "1.8",
    rating: 4.6,
    reviewCount: 31,
    source: "mock_data",
    placeId: "mock-place-5"
  },
  
  // Personal Injury attorneys in Chicago
  {
    name: "Michael Brown",
    firm: "Brown Injury Law",
    specialty: "Personal Injury",
    address: "300 Michigan Ave, Chicago, IL 60601",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.browninjurylaw.example.com",
    latitude: 41.8781,
    longitude: -87.6298,
    distance: "0.3",
    rating: 4.8,
    reviewCount: 47,
    source: "mock_data",
    placeId: "mock-place-6"
  },
  {
    name: "Jennifer Lee",
    firm: "Lee & Partners",
    specialty: "Personal Injury",
    address: "400 State St, Chicago, IL 60605",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.leepartners.example.com",
    latitude: 41.8902,
    longitude: -87.6279,
    distance: "1.0",
    rating: 4.7,
    reviewCount: 39,
    source: "mock_data",
    placeId: "mock-place-7"
  },
  
  // Estate Planning attorneys in Miami
  {
    name: "Carlos Rodriguez",
    firm: "Rodriguez Estate Law",
    specialty: "Estate Planning",
    address: "500 Ocean Drive, Miami, FL 33139",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.rodriguezestate.example.com",
    latitude: 25.7617,
    longitude: -80.1918,
    distance: "0.6",
    rating: 4.9,
    reviewCount: 51,
    source: "mock_data",
    placeId: "mock-place-8"
  },
  {
    name: "Ashley Taylor",
    firm: "Taylor Trust & Estate",
    specialty: "Estate Planning",
    address: "600 Biscayne Blvd, Miami, FL 33132",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.taylorestate.example.com",
    latitude: 25.7792,
    longitude: -80.1894,
    distance: "1.4",
    rating: 4.6,
    reviewCount: 33,
    source: "mock_data",
    placeId: "mock-place-9"
  },
  
  // Immigration attorneys in San Francisco
  {
    name: "Sophia Patel",
    firm: "Patel Immigration Services",
    specialty: "Immigration",
    address: "700 Market St, San Francisco, CA 94103",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.patelimmigration.example.com",
    latitude: 37.7749,
    longitude: -122.4194,
    distance: "0.4",
    rating: 4.8,
    reviewCount: 45,
    source: "mock_data",
    placeId: "mock-place-10"
  },
  {
    name: "Luis Gonzalez",
    firm: "Gonzalez Immigration Law",
    specialty: "Immigration",
    address: "800 Valencia St, San Francisco, CA 94110",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.gonzalezimmigration.example.com",
    latitude: 37.7599,
    longitude: -122.4214,
    distance: "1.1",
    rating: 4.7,
    reviewCount: 37,
    source: "mock_data",
    placeId: "mock-place-11"
  }
];

// Add more attorneys with the same practice areas but different locations
const extraAttorneys = [
  // New York area - Family Law
  {
    name: "Thomas Wright",
    firm: "Wright Family Law",
    specialty: "Family Law",
    address: "225 Broadway, New York, NY 10007",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.wrightfamilylaw.example.com",
    latitude: 40.7105,
    longitude: -74.0091,
    distance: "0.8",
    rating: 4.7,
    reviewCount: 38,
    source: "mock_data",
    placeId: "mock-place-extra-1"
  },
  {
    name: "Amanda Chen",
    firm: "Chen & Partners",
    specialty: "Family Law",
    address: "350 Park Avenue, New York, NY 10022",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.chenfamilylaw.example.com",
    latitude: 40.7583,
    longitude: -73.9742,
    distance: "1.9",
    rating: 4.6,
    reviewCount: 42,
    source: "mock_data",
    placeId: "mock-place-extra-2"
  }
];

// Combine all attorney data
const allAttorneys = [...mockAttorneyDatabase, ...extraAttorneys];

class MockAttorneyService {
  /**
   * Search for attorneys based on location and practice area
   * @param {string} location - The location to search in
   * @param {string} practiceArea - The practice area to search for
   * @param {number} radius - Search radius in miles (not used in mock implementation)
   * @param {number} limit - Maximum number of results to return per page
   * @param {number} offset - Number of results to skip (for pagination)
   * @returns {Object} The search results with pagination info
   */
  async searchAttorneys(location, practiceArea, radius = 25, limit = 5, offset = 0) {
    try {
      console.log(`[MOCK] Searching for ${practiceArea} attorneys near ${location} (limit: ${limit}, offset: ${offset})`);
      
      // Simulate a small delay to mimic API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Filter results by practice area and/or location (case insensitive)
      let filteredResults = allAttorneys;
      
      if (practiceArea) {
        const practiceAreaLower = practiceArea.toLowerCase();
        filteredResults = filteredResults.filter(attorney => 
          attorney.specialty.toLowerCase().includes(practiceAreaLower)
        );
      }
      
      if (location) {
        const locationLower = location.toLowerCase();
        filteredResults = filteredResults.filter(attorney => 
          attorney.address.toLowerCase().includes(locationLower)
        );
      }
      
      // Calculate pagination information
      const totalResults = filteredResults.length;
      const totalPages = Math.ceil(totalResults / limit);
      
      // Apply pagination
      const paginatedResults = filteredResults.slice(offset, offset + limit);
      
      // Return paginated results with metadata
      return {
        results: paginatedResults,
        pagination: {
          total: totalResults,
          limit,
          offset,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages
        }
      };
    } catch (error) {
      console.error('[MOCK] Attorney search failed:', error);
      throw error;
    }
  }

  /**
   * Find attorneys based on coordinates and practice area
   * @param {Object} params - Search parameters
   * @param {number} params.latitude - Latitude coordinate
   * @param {number} params.longitude - Longitude coordinate
   * @param {string} params.practiceArea - The practice area to search for
   * @param {number} params.radius - Search radius in miles
   * @param {number} params.limit - Maximum number of results to return
   * @returns {Object} The search results with attorney data
   */
  async findAttorneys({ latitude, longitude, practiceArea, radius = 50, limit = 10 }) {
    try {
      console.log(`[MOCK] Finding ${practiceArea} attorneys near coordinates (${latitude}, ${longitude})`);
      
      // Simulate a small delay to mimic API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Filter results by practice area (case insensitive)
      let filteredResults = allAttorneys;
      
      if (practiceArea) {
        const practiceAreaLower = practiceArea.toLowerCase();
        filteredResults = filteredResults.filter(attorney => 
          attorney.specialty.toLowerCase().includes(practiceAreaLower)
        );
      }
      
      // Calculate distances (in mock data we'll just use the predefined distances)
      // In a real implementation, this would use the Haversine formula to calculate actual distances
      
      // Sort by distance
      filteredResults.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
      
      // Limit results
      const limitedResults = filteredResults.slice(0, limit);
      
      // Return results in the expected format
      return {
        attorneys: limitedResults,
        total: filteredResults.length,
        message: `Found ${limitedResults.length} attorneys near your location`
      };
    } catch (error) {
      console.error('[MOCK] Attorney search by coordinates failed:', error);
      throw error;
    }
  }
}

// Singleton instance
let mockAttorneyServiceInstance = null;

export const getMockAttorneyService = () => {
  if (!mockAttorneyServiceInstance) {
    mockAttorneyServiceInstance = new MockAttorneyService();
  }
  return mockAttorneyServiceInstance;
};

export default MockAttorneyService; 