import React from 'react';
import Navbar from './components/Navbar';
import Button from './components/Button';
import { getAttorneyConfig } from './utils/getAttorneyConfig';
import { getSubdomain } from './utils/getSubdomain';

const App = () => {
  const subdomain = getSubdomain() || 'localhost';
  const { logo, mascot } = getAttorneyConfig(subdomain);

  return (
    <div>
      <Navbar logoUrl={logo} />
      <Button
        label="Start Interview"
        onClick={() => console.log('Button clicked')}
        mascot={mascot}
      />
    </div>
  );
};

export default App; 