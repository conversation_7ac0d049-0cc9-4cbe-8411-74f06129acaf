/**
 * Assistant Subdomain Service
 * 
 * Manages the mapping between subdomains and assistants.
 * Each assistant can have its own subdomain for dedicated routing.
 */

import { supabase } from '../lib/supabase';

class AssistantSubdomainService {
  /**
   * Get assistant ID by subdomain
   * @param {string} subdomain - The subdomain to lookup
   * @returns {Promise<Object|null>} Assistant mapping or null
   */
  async getAssistantBySubdomain(subdomain) {
    try {
      console.log(`🔍 [AssistantSubdomainService] Looking up assistant for subdomain: ${subdomain}`);

      // Import supabase dynamically to ensure proper initialization
      const { getSupabaseClient } = await import('../lib/supabase');
      const supabaseClient = await getSupabaseClient();

      if (!supabaseClient || typeof supabaseClient.from !== 'function') {
        console.error('❌ [AssistantSubdomainService] Supabase client not available');
        return null;
      }

      const { data, error } = await supabaseClient
        .from('v_subdomain_assistant_lookup')
        .select('*')
        .eq('subdomain', subdomain)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`📭 [AssistantSubdomainService] No assistant found for subdomain: ${subdomain}`);
          return null;
        }
        console.error(`❌ [AssistantSubdomainService] Database error for subdomain ${subdomain}:`, error);
        throw error;
      }

      if (!data) {
        console.log(`📭 [AssistantSubdomainService] No data returned for subdomain: ${subdomain}`);
        return null;
      }

      console.log(`✅ [AssistantSubdomainService] Found assistant for subdomain ${subdomain}:`, {
        assistant_id: data.assistant_id,
        attorney_id: data.attorney_id,
        firm_name: data.firm_name,
        is_primary: data.is_primary
      });

      return data;
    } catch (error) {
      console.error('❌ [AssistantSubdomainService] Error getting assistant by subdomain:', error);
      return null;
    }
  }

  /**
   * Get all subdomains for an attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Array>} Array of subdomain mappings
   */
  async getSubdomainsForAttorney(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('assistant_subdomains')
        .select('*')
        .eq('attorney_id', attorneyId)
        .eq('is_active', true)
        .order('is_primary', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting subdomains for attorney:', error);
      return [];
    }
  }

  /**
   * Assign subdomain to assistant
   * @param {string} assistantId - Vapi assistant ID
   * @param {string} subdomain - Subdomain to assign
   * @param {string} attorneyId - Attorney ID
   * @param {boolean} isPrimary - Whether this is the primary assistant
   * @returns {Promise<Object>} Created mapping
   */
  async assignSubdomainToAssistant(assistantId, subdomain, attorneyId, isPrimary = false) {
    try {
      console.log(`🔗 Assigning subdomain ${subdomain} to assistant ${assistantId}`);

      // Check if subdomain is already taken
      const existing = await this.getAssistantBySubdomain(subdomain);
      if (existing && existing.assistant_id !== assistantId) {
        throw new Error(`Subdomain ${subdomain} is already assigned to assistant ${existing.assistant_id}`);
      }

      const { data, error } = await supabase
        .from('assistant_subdomains')
        .upsert({
          assistant_id: assistantId,
          subdomain: subdomain,
          attorney_id: attorneyId,
          is_primary: isPrimary,
          is_active: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'assistant_id'
        })
        .select()
        .single();

      if (error) throw error;

      console.log(`✅ Successfully assigned subdomain ${subdomain} to assistant ${assistantId}`);
      return data;
    } catch (error) {
      console.error('Error assigning subdomain to assistant:', error);
      throw error;
    }
  }

  /**
   * Update assistant's webhook URL based on subdomain
   * @param {string} assistantId - Vapi assistant ID
   * @param {string} subdomain - Subdomain for webhook URL
   * @returns {Promise<boolean>} Success status
   */
  async updateAssistantWebhookUrl(assistantId, subdomain) {
    try {
      const baseDomain = process.env.VITE_BASE_DOMAIN || 'legalscout.net';
      const webhookUrl = `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
      
      console.log(`🔄 Updating webhook URL for assistant ${assistantId}: ${webhookUrl}`);

      // Import the Vapi service
      const { vapiMcpService } = await import('./vapiMcpService');
      
      // Update the assistant's webhook URL
      await vapiMcpService.updateAssistant(assistantId, {
        serverUrl: webhookUrl,
        serverUrlSecret: process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret'
      });

      console.log(`✅ Updated webhook URL for assistant ${assistantId}`);
      return true;
    } catch (error) {
      console.error('Error updating assistant webhook URL:', error);
      return false;
    }
  }

  /**
   * Create subdomain for new assistant
   * @param {string} assistantId - Vapi assistant ID
   * @param {string} attorneyId - Attorney ID
   * @param {string} baseSubdomain - Base subdomain (e.g., "damon")
   * @param {boolean} isPrimary - Whether this is the primary assistant
   * @returns {Promise<Object>} Created mapping
   */
  async createSubdomainForAssistant(assistantId, attorneyId, baseSubdomain, isPrimary = false) {
    try {
      let subdomain = baseSubdomain;
      let suffix = 1;

      // If not primary, add suffix to avoid conflicts
      if (!isPrimary) {
        // Find available subdomain
        while (await this.getAssistantBySubdomain(subdomain)) {
          subdomain = `${baseSubdomain}-${suffix}`;
          suffix++;
        }
      }

      const mapping = await this.assignSubdomainToAssistant(assistantId, subdomain, attorneyId, isPrimary);
      
      // Update webhook URL
      await this.updateAssistantWebhookUrl(assistantId, subdomain);

      return mapping;
    } catch (error) {
      console.error('Error creating subdomain for assistant:', error);
      throw error;
    }
  }

  /**
   * Get primary assistant for attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Object|null>} Primary assistant mapping
   */
  async getPrimaryAssistantForAttorney(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('assistant_subdomains')
        .select('*')
        .eq('attorney_id', attorneyId)
        .eq('is_primary', true)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No primary assistant found
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting primary assistant for attorney:', error);
      return null;
    }
  }

  /**
   * Set assistant as primary for attorney
   * @param {string} assistantId - Assistant ID to set as primary
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<boolean>} Success status
   */
  async setPrimaryAssistant(assistantId, attorneyId) {
    try {
      console.log(`🎯 Setting assistant ${assistantId} as primary for attorney ${attorneyId}`);

      const { error } = await supabase
        .from('assistant_subdomains')
        .update({ 
          is_primary: true,
          updated_at: new Date().toISOString()
        })
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId);

      if (error) throw error;

      console.log(`✅ Set assistant ${assistantId} as primary`);
      return true;
    } catch (error) {
      console.error('Error setting primary assistant:', error);
      return false;
    }
  }

  /**
   * Migrate existing attorney subdomain to assistant
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Object>} Migration result
   */
  async migrateAttorneySubdomain(attorneyId) {
    try {
      console.log(`🔄 Migrating subdomain for attorney ${attorneyId}`);

      // Get attorney data
      const { data: attorney, error: attorneyError } = await supabase
        .from('attorneys')
        .select('subdomain, vapi_assistant_id, current_assistant_id, firm_name')
        .eq('id', attorneyId)
        .single();

      if (attorneyError) throw attorneyError;

      if (!attorney.subdomain) {
        throw new Error('Attorney has no subdomain to migrate');
      }

      const results = [];

      // Migrate vapi_assistant_id as primary
      if (attorney.vapi_assistant_id) {
        const mapping = await this.assignSubdomainToAssistant(
          attorney.vapi_assistant_id,
          attorney.subdomain,
          attorneyId,
          true // Primary
        );
        results.push({ type: 'primary', mapping });
      }

      // Migrate current_assistant_id as secondary if different
      if (attorney.current_assistant_id && 
          attorney.current_assistant_id !== attorney.vapi_assistant_id) {
        const secondaryMapping = await this.createSubdomainForAssistant(
          attorney.current_assistant_id,
          attorneyId,
          attorney.subdomain,
          false // Not primary
        );
        results.push({ type: 'secondary', mapping: secondaryMapping });
      }

      console.log(`✅ Migrated ${results.length} assistant subdomains for ${attorney.firm_name}`);
      return { success: true, results };
    } catch (error) {
      console.error('Error migrating attorney subdomain:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate webhook URL for subdomain
   * @param {string} subdomain - Subdomain
   * @returns {string} Webhook URL
   */
  generateWebhookUrl(subdomain) {
    const baseDomain = process.env.VITE_BASE_DOMAIN || 'legalscout.net';
    return `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
  }
}

// Export both the class and a singleton instance
export { AssistantSubdomainService };
export const assistantSubdomainService = new AssistantSubdomainService();
export default assistantSubdomainService;
