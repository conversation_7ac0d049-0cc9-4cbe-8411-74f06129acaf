.vapi-diagnostics-panel {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.panel-header p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 0.9rem;
}

.diagnostics-controls {
  margin-bottom: 24px;
}

.run-diagnostics-button {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-diagnostics-button:hover:not(:disabled) {
  background: #45a049;
}

.run-diagnostics-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.diagnostics-results {
  border-top: 1px solid #eee;
  padding-top: 24px;
}

.results-summary {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summary-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.overall-status {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.overall-status.healthy {
  background: #d4edda;
  color: #155724;
}

.overall-status.warning {
  background: #fff3cd;
  color: #856404;
}

.overall-status.error {
  background: #f8d7da;
  color: #721c24;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.stat-value {
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.test-results h5 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.test-result {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: border-color 0.2s;
}

.test-result.pass {
  border-left: 4px solid #4CAF50;
}

.test-result.warning {
  border-left: 4px solid #FF9800;
}

.test-result.fail {
  border-left: 4px solid #F44336;
}

.test-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.test-icon {
  font-size: 1rem;
}

.test-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.test-status {
  font-size: 0.8rem;
  font-weight: 600;
}

.test-message {
  color: #666;
  font-size: 0.9rem;
  margin-left: 24px;
}

.test-details {
  margin-top: 8px;
  margin-left: 24px;
}

.test-details summary {
  color: #007bff;
  cursor: pointer;
  font-size: 0.8rem;
  margin-bottom: 8px;
}

.test-details pre {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  font-size: 0.8rem;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.diagnostics-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.toggle-details-button,
.copy-results-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggle-details-button:hover,
.copy-results-button:hover {
  background: #0056b3;
}

.raw-results {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.raw-results h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
}

.raw-results pre {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 12px;
  font-size: 0.8rem;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-results p {
  margin-bottom: 24px;
  font-size: 1rem;
}

.diagnostic-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.diagnostic-info h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
}

.diagnostic-info ul {
  margin: 0;
  padding-left: 20px;
}

.diagnostic-info li {
  margin-bottom: 4px;
  color: #666;
  font-size: 0.9rem;
}
