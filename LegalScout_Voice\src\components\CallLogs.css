.call-logs-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.call-logs-container h2 {
  margin-bottom: 20px;
  color: var(--primary-color, #333);
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.loading, .error, .no-logs {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 20px 0;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
}

.call-logs-content {
  display: flex;
  gap: 20px;
}

.call-logs-list {
  flex: 1;
  max-width: 400px;
  border-right: 1px solid #eee;
  padding-right: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.call-log-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: #f9f9f9;
}

.call-log-item:hover {
  background-color: #f0f0f0;
}

.call-log-item.selected {
  background-color: var(--primary-color, #4a90e2);
  color: white;
}

.call-log-icon {
  margin-right: 15px;
  font-size: 20px;
  color: var(--primary-color, #4a90e2);
}

.call-log-item.selected .call-log-icon {
  color: white;
}

.call-log-info {
  flex: 1;
}

.call-log-phone {
  font-weight: bold;
  margin-bottom: 5px;
}

.call-log-date {
  font-size: 0.8rem;
  color: #666;
}

.call-log-item.selected .call-log-date {
  color: rgba(255, 255, 255, 0.8);
}

.call-log-status {
  margin-left: 10px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  text-transform: uppercase;
  font-weight: bold;
}

.status-badge.completed {
  background-color: #4caf50;
  color: white;
}

.status-badge.in-progress {
  background-color: #2196f3;
  color: white;
}

.status-badge.failed {
  background-color: #f44336;
  color: white;
}

.status-badge.scheduled {
  background-color: #ff9800;
  color: white;
}

.call-log-details {
  flex: 2;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.call-log-details h3 {
  margin-bottom: 20px;
  color: var(--primary-color, #333);
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.detail-icon {
  margin-right: 15px;
  font-size: 18px;
  color: var(--primary-color, #4a90e2);
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 3px;
}

.detail-value {
  font-weight: bold;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--primary-color, #333);
}

.detail-text {
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #eee;
  line-height: 1.5;
}

.detail-text.transcript {
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.detail-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-button {
  padding: 8px 16px;
  background-color: var(--primary-color, #4a90e2);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: var(--secondary-color, #357ae8);
}

/* Responsive styles */
@media (max-width: 768px) {
  .call-logs-content {
    flex-direction: column;
  }
  
  .call-logs-list {
    max-width: 100%;
    border-right: none;
    padding-right: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
