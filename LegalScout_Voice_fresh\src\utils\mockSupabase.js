// Mock Supabase client for development/fallback when real client fails
export const createStubClient = () => {
  console.log('🚧 Creating stub Supabase client for fallback');
  
  return {
    auth: {
      signInWithOAuth: async () => {
        console.warn('🚧 [Stub] OAuth sign-in not available - using stub client');
        return { data: null, error: new Error('Stub client - OAuth not available') };
      },
      getUser: async () => ({ data: { user: null }, error: null }),
      getSession: async () => ({ data: { session: null }, error: null }),
      onAuthStateChange: (callback) => {
        console.log('🚧 [Stub] Auth state change listener registered');
        return { data: { subscription: { unsubscribe: () => {} } } };
      }
    },
    from: (table) => {
      console.log(`🚧 [Stub] Querying table: ${table}`);

      // Create a chainable query builder that supports all Supabase methods
      const createChainableQuery = (data = []) => ({
        select: (columns = '*') => {
          console.log(`🚧 [Stub] SELECT ${columns} FROM ${table}`);
          return createChainableQuery(data);
        },
        eq: (column, value) => {
          console.log(`🚧 [Stub] WHERE ${column} = ${value}`);
          return createChainableQuery(data);
        },
        order: (column, options = {}) => {
          console.log(`🚧 [Stub] ORDER BY ${column} ${options.ascending ? 'ASC' : 'DESC'}`);
          return createChainableQuery(data);
        },
        limit: (count) => {
          console.log(`🚧 [Stub] LIMIT ${count}`);
          return createChainableQuery(data);
        },
        single: () => {
          console.log(`🚧 [Stub] SINGLE result`);
          return {
            then: (callback) => callback({ data: null, error: null })
          };
        },
        then: (callback) => {
          console.log(`🚧 [Stub] Executing query, returning empty results`);
          return callback({ data: [], error: null });
        }
      });

      return {
        ...createChainableQuery(),
        insert: (data) => {
          console.log(`🚧 [Stub] INSERT INTO ${table}:`, data);
          return {
            then: (callback) => callback({ data: null, error: null })
          };
        },
        update: (data) => {
          console.log(`🚧 [Stub] UPDATE ${table}:`, data);
          return createChainableQuery();
        },
        delete: () => {
          console.log(`🚧 [Stub] DELETE FROM ${table}`);
          return createChainableQuery();
        }
      };
    }
  };
};
