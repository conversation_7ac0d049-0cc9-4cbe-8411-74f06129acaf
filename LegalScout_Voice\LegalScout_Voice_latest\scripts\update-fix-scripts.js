/**
 * Update Fix Scripts
 *
 * This script updates the index.html file to include our fix scripts.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const indexHtmlPath = path.resolve(process.cwd(), 'index.html');
const buildIndexHtmlPath = path.resolve(process.cwd(), 'dist', 'index.html');

// Update the index.html file
function updateIndexHtml(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return false;
  }

  let indexHtml = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  // Check if the attorney persistence fix script is already included
  if (!indexHtml.includes('fix-attorney-persistence.js')) {
    // Add the script after the head tag
    indexHtml = indexHtml.replace(
      '<head>',
      '<head>\n    <!-- Attorney persistence fix -->\n    <script src="/fix-attorney-persistence.js"></script>'
    );
    updated = true;
    console.log('Added fix-attorney-persistence.js to index.html');
  }

  // Check if the sync tools fix script is already included
  if (!indexHtml.includes('sync-tools-fix.js')) {
    // Add the script after the attorney persistence fix or after the head tag
    if (indexHtml.includes('fix-attorney-persistence.js')) {
      indexHtml = indexHtml.replace(
        'script src="/fix-attorney-persistence.js"></script>',
        'script src="/fix-attorney-persistence.js"></script>\n    <!-- Sync tools fix -->\n    <script src="/sync-tools-fix.js"></script>'
      );
    } else {
      indexHtml = indexHtml.replace(
        '<head>',
        '<head>\n    <!-- Sync tools fix -->\n    <script src="/sync-tools-fix.js"></script>'
      );
    }
    updated = true;
    console.log('Added sync-tools-fix.js to index.html');
  }

  // Check if the auth state fix script is already included
  if (!indexHtml.includes('fix-auth-state.js')) {
    // Add the script after the sync tools fix or after the head tag
    if (indexHtml.includes('sync-tools-fix.js')) {
      indexHtml = indexHtml.replace(
        'script src="/sync-tools-fix.js"></script>',
        'script src="/sync-tools-fix.js"></script>\n    <!-- Auth state fix -->\n    <script src="/fix-auth-state.js"></script>'
      );
    } else {
      indexHtml = indexHtml.replace(
        '<head>',
        '<head>\n    <!-- Auth state fix -->\n    <script src="/fix-auth-state.js"></script>'
      );
    }
    updated = true;
    console.log('Added fix-auth-state.js to index.html');
  }

  // Check if the attorney creation fix script is already included
  if (!indexHtml.includes('fix-attorney-creation.js')) {
    // Add the script after the auth state fix or after the head tag
    if (indexHtml.includes('fix-auth-state.js')) {
      indexHtml = indexHtml.replace(
        'script src="/fix-auth-state.js"></script>',
        'script src="/fix-auth-state.js"></script>\n    <!-- Attorney creation fix -->\n    <script src="/fix-attorney-creation.js"></script>'
      );
    } else {
      indexHtml = indexHtml.replace(
        '<head>',
        '<head>\n    <!-- Attorney creation fix -->\n    <script src="/fix-attorney-creation.js"></script>'
      );
    }
    updated = true;
    console.log('Added fix-attorney-creation.js to index.html');
  }

  if (updated) {
    fs.writeFileSync(filePath, indexHtml);
    console.log(`Updated ${filePath}`);
    return true;
  } else {
    console.log(`No updates needed for ${filePath}`);
    return false;
  }
}

// Update both the development and build index.html files
console.log('Updating fix scripts in index.html files...');
const devUpdated = updateIndexHtml(indexHtmlPath);
const buildUpdated = updateIndexHtml(buildIndexHtmlPath);

if (devUpdated || buildUpdated) {
  console.log('Successfully updated fix scripts in index.html files');
} else {
  console.log('No updates were needed for index.html files');
}
