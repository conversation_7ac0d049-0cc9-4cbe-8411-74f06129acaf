import React, { useState, useEffect, useRef } from 'react';
import Vapi from '@vapi-ai/web';
import './SimpleVapiCall.css';

// Import the constants from vapiConstants
import { DEFAULT_ASSISTANT_ID, CALL_STATUS } from '../constants/vapiConstants';

/**
 * A simplified Vapi integration component following Vapi documentation best practices
 */
const SimpleVapiCall = ({
  apiKey,
  assistantId = 'legal-consultation',
  customInstructions = {},
  onCallStarted,
  onCallEnded,
  onMessageReceived,
  onError,
  isDarkTheme = false
}) => {
  // State
  const [status, setStatus] = useState('idle');
  const [messages, setMessages] = useState([{
    role: 'assistant',
    content: customInstructions.initialMessage || "This is <PERSON>, your AI legal assistant from Legal Scout. I'm here to help understand your legal matter and guide you through the process of finding the right attorney if needed."
  }]);
  const [inputText, setInputText] = useState('');
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [volume, setVolume] = useState(0);
  const [dossierData, setDossierData] = useState({});
  const [currentTranscript, setCurrentTranscript] = useState('');

  // Refs
  const vapiRef = useRef(null);
  const callIdRef = useRef(null);
  const isMountedRef = useRef(true);
  const messagesEndRef = useRef(null);
  const listenersRef = useRef(new Map()); // Add a ref to store listeners

  // Initialize Vapi on component mount
  useEffect(() => {
    console.log('SimpleVapiCall: Initializing component');
    isMountedRef.current = true;

    // Initialize Vapi instance
    if (!vapiRef.current && apiKey) {
      try {
        console.log('SimpleVapiCall: Creating Vapi instance with apiKey');
        vapiRef.current = new Vapi(apiKey);

        // Set up event listeners
        setupEventListeners();
      } catch (error) {
        console.error('SimpleVapiCall: Error initializing Vapi:', error);
        handleError(error);
      }
    }

    // Set up global message listener for transcript and speaking events
    const handleWindowMessage = (event) => {
      if (!event.data) return;

      console.log('SimpleVapiCall: Window message received:', event.data);

      // Check for transcript data
      if (event.data.type === 'transcript' ||
          event.data.type === 'transcription' ||
          event.data.action === 'transcript') {

        const transcriptText = event.data.transcript || event.data.text || '';
        console.log('SimpleVapiCall: Window transcript event detected:', transcriptText);

        if (transcriptText) {
          setCurrentTranscript(transcriptText);
        }
      }

      // Check for speaking status updates
      if (event.data.type === 'speaking-status-change' ||
          (event.data.type === 'message' && event.data.speaking !== undefined)) {

        const isSpeakingNow = !!event.data.speaking;
        console.log('SimpleVapiCall: Window speaking event detected:', isSpeakingNow);

        setIsSpeaking(isSpeakingNow);
      }
    };

    // Add window message event listener
    window.addEventListener('message', handleWindowMessage);

    // Cleanup on unmount
    return () => {
      console.log('SimpleVapiCall: Cleaning up');
      isMountedRef.current = false;

      // Remove window message listener
      window.removeEventListener('message', handleWindowMessage);

      // End call if active
      if (callIdRef.current) {
        try {
          endCall();
        } catch (err) {
          console.error('SimpleVapiCall: Error ending call during cleanup:', err);
        }
      }

      // Remove event listeners
      if (vapiRef.current) {
        try {
          removeEventListeners();
        } catch (err) {
          console.error('SimpleVapiCall: Error removing event listeners:', err);
        }
      }
    };
  }, [apiKey]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Periodically filter out duplicate welcome messages
  useEffect(() => {
    // Wait until we have multiple messages
    if (messages.length > 1) {
      // Apply filtering and update if changes were made
      const filteredMessages = filterDuplicateWelcomeMessages(messages);

      // Only update if filtering actually changed the messages
      if (filteredMessages.length !== messages.length) {
        console.log('SimpleVapiCall: Filtered out duplicate welcome messages');
        setMessages(filteredMessages);
      }
    }
  }, [messages.length]); // Only run when message count changes

  // Auto-start call when component mounts
  useEffect(() => {
    if (vapiRef.current && status === 'idle') {
      console.log('SimpleVapiCall: Auto-starting call...');
      // Add a small delay to ensure everything is properly initialized
      const timer = setTimeout(() => {
        startCall();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [vapiRef.current, status]);

  // Helper function to check if a message appears to be a greeting/welcome message
  const isWelcomeMessage = (content) => {
    const lowerContent = content.toLowerCase();
    const welcomePhrases = [
      'hello', 'hi', 'greetings', 'welcome', 'how can i help',
      'how may i help', 'i\'m your', 'i am your', 'assistant',
      'legal scout', 'smith & associates', 'this is scout',
      'guide you through', 'understand your legal', 'finding the right attorney'
    ];

    // Check if content contains common welcome phrases
    return welcomePhrases.some(phrase => lowerContent.includes(phrase));
  };

  // Helper function to filter out duplicate welcome messages
  const filterDuplicateWelcomeMessages = (messages) => {
    // If we have fewer than 2 messages, no need to filter
    if (messages.length < 2) return messages;

    // Find all greeting messages from the assistant
    const welcomeMessages = messages.filter(
      m => m.role === 'assistant' && isWelcomeMessage(m.content)
    );

    // If we have multiple welcome messages, keep only the most complete one
    if (welcomeMessages.length > 1) {
      // Find the welcome message with the longest content
      const mostCompleteWelcome = welcomeMessages.reduce(
        (prev, current) => (current.content.length > prev.content.length) ? current : prev,
        welcomeMessages[0]
      );

      // Return messages with only the most complete welcome message
      return messages.filter(m =>
        m === mostCompleteWelcome ||
        m.role !== 'assistant' ||
        !isWelcomeMessage(m.content)
      );
    }

    // No duplicate welcome messages found
    return messages;
  };

  // Add this effect to update the transcript bubble whenever currentTranscript changes
  useEffect(() => {
    console.log('SimpleVapiCall: currentTranscript changed:', currentTranscript);
    // No DOM manipulation needed - the component will re-render with the updated state
  }, [currentTranscript]);

  // Set up event listeners for the Vapi instance
  const setupEventListeners = () => {
    if (!vapiRef.current) return;

    console.log('SimpleVapiCall: Setting up event listeners');

    // Call started event
    const callStartedListener = (data) => {
      console.log('SimpleVapiCall: Call started event received', data);
      if (isMountedRef.current) {
        setStatus('connected');
        if (data && data.callId) {
          callIdRef.current = data.callId;
        }
        if (typeof onCallStarted === 'function') {
          onCallStarted(data);
        }
      }
    };

    // Call ended event
    const callEndedListener = (data) => {
      console.log('SimpleVapiCall: Call ended event received', data);
      if (isMountedRef.current) {
        setStatus('idle');
        callIdRef.current = null;
        if (typeof onCallEnded === 'function') {
          onCallEnded(data);
        }
      }
    };

    // Error event
    const errorListener = (error) => {
      console.error('SimpleVapiCall: Error event received', error);
      if (isMountedRef.current) {
        handleError(error);
      }
    };

    // Message event
    const messageListener = (message) => {
      console.log('SimpleVapiCall: Message event received', message);
      if (isMountedRef.current) {
        // Handle Vapi's various message formats
        let processedMessage = message;

        // Normalize message structure if needed
        if (typeof message === 'string') {
          processedMessage = { content: message, role: 'assistant' };
        } else if (message && typeof message === 'object') {
          // Ensure we have a standard format for all messages
          processedMessage = {
            ...message,
            role: message.role || 'assistant',
            content: message.content || message.text || message.transcript || ''
          };
        }

        // Process different message types
        if (processedMessage.type) {
          // Handle different message types
          switch (processedMessage.type) {
            case 'transcript':
              // Get transcript text and set it to state - avoid DOM manipulation
              const transcriptText = processedMessage.transcript || processedMessage.text || '';
              const isFinal = processedMessage.isFinal || processedMessage.is_final || false;
              const transcriptRole = processedMessage.role || 'user';

              console.log(`SimpleVapiCall: ⭐ TRANSCRIPT MESSAGE ⭐ - text: "${transcriptText}", isFinal: ${isFinal}, role: ${transcriptRole}`);

              // Update state
              setCurrentTranscript(transcriptText);

              // Force update DOM directly as well to ensure visibility
              try {
                const transcriptElement = document.querySelector('.message-bubble.transcript .message-content');
                const transcriptBubble = document.querySelector('.message-bubble.transcript');

                if (transcriptElement) {
                  transcriptElement.textContent = transcriptText;
                  console.log('SimpleVapiCall: Updated transcript element text directly:', transcriptText);

                  if (transcriptBubble) {
                    if (transcriptText && transcriptText.trim()) {
                      transcriptBubble.classList.add('has-content');
                      transcriptBubble.style.display = 'flex';
                      transcriptBubble.style.opacity = '1';
                      transcriptBubble.style.visibility = 'visible';
                      console.log('SimpleVapiCall: Made transcript bubble visible');
                    } else {
                      transcriptBubble.classList.remove('has-content');
                    }
                  }
                } else {
                  console.warn('SimpleVapiCall: Could not find transcript element for direct update');
                }
              } catch (err) {
                console.error('SimpleVapiCall: Error during direct DOM manipulation:', err);
              }

              // If this is a final transcript from the user, add it to messages
              if (isFinal && (transcriptRole === 'user' || !transcriptRole)) {
                if (transcriptText.trim()) {
                  console.log('SimpleVapiCall: Adding final transcript to messages:', transcriptText);
                  setMessages(prev => [...prev, {
                    role: 'user',
                    content: transcriptText,
                    isTranscript: true
                  }]);

                  // Clear transcript after adding to messages
                  setCurrentTranscript('');
                }
              }
              break;

            case 'status-update':
              console.log('Status update:', processedMessage.status);
              break;

            case 'conversation-update':
              console.log('Conversation update received:', processedMessage);
              // Extract the latest assistant message from conversation
              if (processedMessage.conversation && Array.isArray(processedMessage.conversation)) {
                const assistantMessages = processedMessage.conversation.filter(m => m.role === 'assistant');
                if (assistantMessages.length > 0) {
                  const latestMessage = assistantMessages[assistantMessages.length - 1];
                  if (latestMessage && latestMessage.content) {
                    console.log('Latest assistant message:', latestMessage.content);
                    setMessages(prev => {
                      // Check if this is a continuation or completion of an existing message
                      // by comparing the beginning of the content
                      const existingAssistantMessages = prev.filter(m => m.role === 'assistant');

                      // If we have existing assistant messages, check if this is an update
                      if (existingAssistantMessages.length > 0) {
                        const lastAssistantMsg = existingAssistantMessages[existingAssistantMessages.length - 1];

                        // Check if one message starts with the other (partial completion)
                        if (latestMessage.content.startsWith(lastAssistantMsg.content) ||
                            lastAssistantMsg.content.startsWith(latestMessage.content)) {

                          // Use the longer content
                          const newContent = latestMessage.content.length > lastAssistantMsg.content.length
                            ? latestMessage.content
                            : lastAssistantMsg.content;

                          // Create a new array with the updated message
                          const updatedMessages = [...prev];
                          const lastAssistantIndex = prev.indexOf(lastAssistantMsg);

                          if (lastAssistantIndex !== -1) {
                            updatedMessages[lastAssistantIndex] = {
                              ...lastAssistantMsg,
                              content: newContent
                            };
                            return updatedMessages;
                          }
                        }

                        // Check if the content is very similar (e.g., only minor differences)
                        // This helps prevent duplicates with slightly different formatting
                        // This is useful for handling "This is Scout..." type messages
                        const isSimilarContent = (msg1, msg2) => {
                          if (!msg1 || !msg2) return false;
                          const normalize = s => s.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "").trim();
                          const normalizedMsg1 = normalize(msg1);
                          const normalizedMsg2 = normalize(msg2);

                          // If messages start similarly, consider them similar
                          if (normalizedMsg1.length > 20 && normalizedMsg2.length > 20) {
                            const shorterLen = Math.min(normalizedMsg1.length, normalizedMsg2.length);
                            const similarity = shorterLen / Math.max(normalizedMsg1.length, normalizedMsg2.length);

                            // Check if first 20 chars are the same (likely same message)
                            if (normalizedMsg1.substring(0, 20) === normalizedMsg2.substring(0, 20)) {
                              return true;
                            }

                            // If overall content is very similar, consider them duplicates
                            if (similarity > 0.8) {
                              return true;
                            }
                          }
                          return false;
                        };

                        // Check for very similar content to avoid near-duplicates
                        for (const existingMsg of existingAssistantMessages) {
                          if (isSimilarContent(existingMsg.content, latestMessage.content)) {
                            // If new message is longer, update the existing one
                            if (latestMessage.content.length > existingMsg.content.length) {
                              const updatedMessages = [...prev];
                              const existingIndex = prev.indexOf(existingMsg);

                              if (existingIndex !== -1) {
                                updatedMessages[existingIndex] = {
                                  ...existingMsg,
                                  content: latestMessage.content
                                };
                                return updatedMessages;
                              }
                            }
                            // Otherwise keep the existing message
                            return prev;
                          }
                        }
                      }

                      // If we haven't found a similar message, check for exact match
                      const exactMatch = prev.some(m =>
                        m.role === 'assistant' &&
                        m.content === latestMessage.content
                      );

                      // Only add if we don't have an exact match
                      if (!exactMatch) {
                        return [...prev, {
                          role: 'assistant',
                          content: latestMessage.content
                        }];
                      }
                      return prev;
                    });
                  }
                }
              }
              break;

            case 'model-output':
              // Accumulate model output chunks
              const outputChunk = processedMessage.output || '';
              if (outputChunk) {
                setMessages(prev => {
                  // Look for the last assistant message to potentially update
                  const assistantMessages = prev.filter(m => m.role === 'assistant');
                  const lastMessage = assistantMessages.length > 0
                    ? assistantMessages[assistantMessages.length - 1]
                    : null;

                  // If the last message is from the assistant, check if we should append to it
                  if (lastMessage) {
                    // Only append if this seems like a continuation (not a completely new message)
                    // This helps prevent concatenating unrelated messages
                    if (outputChunk.length < 50) { // Likely a continuation chunk
                      const updatedMessages = [...prev];
                      const lastIndex = updatedMessages.findIndex(m =>
                        m.role === 'assistant' && m === lastMessage
                      );

                      if (lastIndex !== -1) {
                        updatedMessages[lastIndex] = {
                          ...lastMessage,
                          content: lastMessage.content + outputChunk
                        };
                        return updatedMessages;
                      }
                    }
                  }

                  // If we shouldn't update an existing message, create a new one
                  return [...prev, {
                    role: 'assistant',
                    content: outputChunk
                  }];
                });
              }
              break;

            default:
              // For other message types or direct content
              if (processedMessage.content) {
                setMessages(prev => [...prev, {
                  role: processedMessage.role || 'assistant',
                  content: processedMessage.content
                }]);
              }
          }
        } else if (processedMessage && processedMessage.content) {
          // Direct message object with content
          setMessages(prev => [...prev, {
            role: processedMessage.role || 'assistant',
            content: processedMessage.content
          }]);
        }

        // Check for dossier data in message
        if (processedMessage && processedMessage.data) {
          try {
            // Attempt to parse dossier data if present
            const parsedData = typeof processedMessage.data === 'string'
              ? JSON.parse(processedMessage.data)
              : processedMessage.data;

            if (parsedData && typeof parsedData === 'object') {
              console.log('SimpleVapiCall: Received dossier data', parsedData);
              setDossierData(prev => ({...prev, ...parsedData}));
            }
          } catch (err) {
            console.error('SimpleVapiCall: Error parsing message data', err);
          }
        }

        if (typeof onMessageReceived === 'function') {
          onMessageReceived(processedMessage);
        }
      }
    };

    // Speaking events
    const speakingListener = (isSpeaking) => {
      console.log('SimpleVapiCall: ⭐ SPEAKING EVENT ⭐ - speaking:', isSpeaking);
      if (isMountedRef.current) {
        setIsSpeaking(!!isSpeaking);

        // Update UI to show speaking status more prominently
        try {
          const speakingStatus = document.querySelector('.speaking-status');
          if (speakingStatus) {
            if (isSpeaking) {
              speakingStatus.style.fontWeight = 'bold';
              speakingStatus.style.color = '#0ea5e9';
              speakingStatus.innerHTML = '🔊 Assistant is speaking...';
            } else {
              speakingStatus.style.fontWeight = 'normal';
              speakingStatus.style.color = '';
              speakingStatus.innerHTML = '🎤 Listening...';
            }
          }
        } catch (err) {
          console.error('SimpleVapiCall: Error updating speaking status:', err);
        }
      }
    };

    // Volume level events
    const volumeLevelListener = (level) => {
      if (isMountedRef.current) {
        setVolume(typeof level === 'number' ? level : 0);
      }
    };

    // Transcription events
    const transcriptionListener = (transcriptionData) => {
      console.log('SimpleVapiCall: ⭐ TRANSCRIPT EVENT RECEIVED ⭐', transcriptionData);
      if (isMountedRef.current) {
        // Process the transcription data
        const text = transcriptionData?.transcript || transcriptionData?.text || '';
        const isFinal = transcriptionData?.is_final || transcriptionData?.isFinal || false;
        const role = transcriptionData?.role || 'user';

        console.log(`SimpleVapiCall: Transcript data - text: "${text}", isFinal: ${isFinal}, role: ${role}`);

        // Update the transcript directly in state AND directly in DOM for immediate display
        setCurrentTranscript(text);

        // Force update DOM directly as well to ensure visibility
        try {
          const transcriptElement = document.querySelector('.message-bubble.transcript .message-content');
          const transcriptBubble = document.querySelector('.message-bubble.transcript');

          if (transcriptElement) {
            transcriptElement.textContent = text;
            console.log('SimpleVapiCall: Updated transcript element text directly:', text);

            if (transcriptBubble) {
              if (text && text.trim()) {
                transcriptBubble.classList.add('has-content');
                transcriptBubble.style.display = 'flex';
                transcriptBubble.style.opacity = '1';
                transcriptBubble.style.visibility = 'visible';
                console.log('SimpleVapiCall: Made transcript bubble visible');
              } else {
                transcriptBubble.classList.remove('has-content');
              }
            }
          } else {
            console.warn('SimpleVapiCall: Could not find transcript element for direct update');
          }
        } catch (err) {
          console.error('SimpleVapiCall: Error during direct DOM manipulation:', err);
        }

        // If this is a final transcript, add it to the message history
        if (isFinal && role === 'user' && text.trim()) {
          console.log('SimpleVapiCall: Adding final transcript to messages:', text);
          setMessages(prev => [...prev, {
            role: 'user',
            content: text,
            isTranscript: true
          }]);

          // Clear the transcript display
          setCurrentTranscript('');
        }
      }
    };

    // Store listeners in the ref for later removal
    listenersRef.current.set('call-started', callStartedListener);
    listenersRef.current.set('call-ended', callEndedListener);
    listenersRef.current.set('error', errorListener);
    listenersRef.current.set('message', messageListener);
    listenersRef.current.set('speaking', speakingListener);
    listenersRef.current.set('volume-level', volumeLevelListener);
    listenersRef.current.set('transcription', transcriptionListener);
    listenersRef.current.set('transcript', transcriptionListener);

    // Additional transcript-related events
    listenersRef.current.set('user-transcript', transcriptionListener);
    listenersRef.current.set('assistant-transcript', transcriptionListener);
    listenersRef.current.set('audio-transcription', transcriptionListener);
    listenersRef.current.set('raw-transcript', transcriptionListener);

    // Attach listeners to Vapi instance
    vapiRef.current.on('call-started', callStartedListener);
    vapiRef.current.on('call-ended', callEndedListener);
    vapiRef.current.on('error', errorListener);
    vapiRef.current.on('message', messageListener);
    vapiRef.current.on('speaking', speakingListener);
    vapiRef.current.on('volume-level', volumeLevelListener);
    vapiRef.current.on('transcription', transcriptionListener);
    vapiRef.current.on('transcript', transcriptionListener);

    // Additional transcript event listeners
    vapiRef.current.on('user-transcript', transcriptionListener);
    vapiRef.current.on('assistant-transcript', transcriptionListener);
    vapiRef.current.on('audio-transcription', transcriptionListener);
    vapiRef.current.on('raw-transcript', transcriptionListener);
  };

  // Remove event listeners
  const removeEventListeners = () => {
    if (!vapiRef.current) return;

    console.log('SimpleVapiCall: Removing event listeners');

    // Try to use removeAllListeners first if available
    if (typeof vapiRef.current.removeAllListeners === 'function') {
      try {
        console.log('SimpleVapiCall: Using removeAllListeners');
        vapiRef.current.removeAllListeners();
        listenersRef.current.clear();
        return;
      } catch (err) {
        console.warn('SimpleVapiCall: Error using removeAllListeners, falling back to individual removal:', err);
      }
    }

    // Fall back to removing individual listeners
    try {
      for (const [event, listener] of listenersRef.current.entries()) {
        console.log(`SimpleVapiCall: Removing listener for ${event}`);
        if (listener) {
          vapiRef.current.off(event, listener);
        } else {
          console.warn(`SimpleVapiCall: No listener found for ${event}`);
        }
      }

      listenersRef.current.clear();
    } catch (err) {
      console.error('SimpleVapiCall: Error removing individual listeners:', err);

      // Last resort: try removing just the events without listeners
      try {
        [
          'call-started', 'call-ended', 'error', 'message', 'speaking', 'volume-level',
          'transcription', 'transcript', 'user-transcript', 'assistant-transcript',
          'audio-transcription', 'raw-transcript'
        ].forEach(event => {
          vapiRef.current.removeAllListeners?.(event) || vapiRef.current.off?.(event);
        });
      } catch (finalErr) {
        console.error('SimpleVapiCall: Final attempt to remove listeners failed:', finalErr);
      }
    }
  };

  // Handle errors
  const handleError = (error) => {
    console.error('SimpleVapiCall: Error occurred:', error);
    setStatus('error');
    if (typeof onError === 'function') {
      onError(error);
    }
  };

  // Map assistantId to a valid UUID if needed
  const getAssistantUUID = (id) => {
    // Use a mapping object to convert known identifiers to UUIDs
    const idMapping = {
      'legal-consultation': DEFAULT_ASSISTANT_ID || 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865',
      'legal-scout': 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865', // Make sure Legal Scout central agent has the right ID
    };

    // If the ID is already a UUID format, return it as is
    if (id && id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      return id;
    }

    // Look up the ID in the mapping
    return idMapping[id] || DEFAULT_ASSISTANT_ID || 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865';
  };

  // Start the call
  const startCall = () => {
    if (!vapiRef.current) {
      console.error('SimpleVapiCall: Cannot start call, Vapi not initialized');
      handleError(new Error('Vapi not initialized'));
      return;
    }

    if (status === 'connected' || status === 'connecting') {
      console.warn('SimpleVapiCall: Call already in progress or connecting');
      return;
    }

    try {
      console.log('SimpleVapiCall: Starting call...');
      setStatus('connecting');

      // Use 'legal-scout' as the default identifier for the central agent
      const assistantIdentifier = assistantId || 'legal-scout';

      // Get valid UUID for assistantId
      const assistantUUID = getAssistantUUID(assistantIdentifier);
      console.log(`SimpleVapiCall: Using assistant ID: ${assistantUUID}`);

      // Prepare call parameters - filter out initialMessage property
      const { initialMessage, ...filteredInstructions } = customInstructions;

      // Start the call
      vapiRef.current.start(assistantUUID, filteredInstructions);

      console.log('SimpleVapiCall: Call started successfully');
    } catch (error) {
      console.error('SimpleVapiCall: Error starting call:', error);
      handleError(error);
    }
  };

  // End the call
  const endCall = () => {
    if (!vapiRef.current) {
      console.warn('SimpleVapiCall: Cannot end call, Vapi not initialized');
      return;
    }

    try {
      console.log('SimpleVapiCall: Ending call...');
      vapiRef.current.stop();

      // Update status even before the event listener fires
      setStatus('idle');
    } catch (error) {
      console.error('SimpleVapiCall: Error ending call:', error);
      handleError(error);
    }
  };

  // Handle sending a message via text
  const sendMessage = () => {
    if (!inputText.trim()) return;

    console.log('SimpleVapiCall: Sending message:', inputText);

    // Add user message to the UI
    setMessages(prev => [...prev, {
      role: 'user',
      content: inputText
    }]);

    // Send the message to Vapi if connected
    if (vapiRef.current && status === 'connected') {
      try {
        vapiRef.current.send({
          type: 'add-message',
          message: {
            role: 'user',
            content: inputText
          }
        });
      } catch (err) {
        console.error('SimpleVapiCall: Error sending message:', err);
        handleError(err);
      }
    }

    // Clear the input
    setInputText('');
  };

  // Handle input change
  const handleInputChange = (e) => {
    setInputText(e.target.value);
  };

  // Handle key press in input field
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Calculate volume level for UI display
  const getVolumeBarLevel = () => {
    // Map volume (typically 0-1) to 0-100% for visual representation
    return `${Math.min(100, Math.max(0, volume * 100))}%`;
  };

  // Render status text based on current status
  const getStatusText = () => {
    switch (status) {
      case 'idle': return 'Ready';
      case 'connecting': return 'Connecting...';
      case 'connected': return 'Connected';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  // Extract relevant dossier fields for simplified display
  const getDossierDisplayItems = () => {
    const items = [];

    if (dossierData.practiceArea) {
      items.push({
        label: 'Practice Area',
        value: dossierData.practiceArea
      });
    }

    if (dossierData.location) {
      let locationStr = '';
      const loc = dossierData.location;

      if (loc.city && loc.state) {
        locationStr = `${loc.city}, ${loc.state}`;
      } else if (loc.city) {
        locationStr = loc.city;
      } else if (loc.state) {
        locationStr = loc.state;
      } else if (loc.address) {
        locationStr = loc.address;
      }

      if (locationStr) {
        items.push({
          label: 'Location',
          value: locationStr
        });
      }
    }

    if (dossierData.caseType) {
      items.push({
        label: 'Case Type',
        value: dossierData.caseType
      });
    }

    if (dossierData.urgency) {
      items.push({
        label: 'Urgency',
        value: dossierData.urgency
      });
    }

    return items;
  };

  // Determine if dossier has enough data to display
  const hasDossierData = () => {
    return Object.keys(dossierData).length > 0;
  };

  // Render component
  return (
    <div className={`simple-vapi-call ${isDarkTheme ? 'dark-theme' : 'light-theme'} status-${status}`}>
      {/* Debug indicator */}
      <div
        className="debug-indicator"
        style={{
          position: 'fixed',
          top: '5px',
          right: '5px',
          background: '#0ea5e9',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 9999,
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}
      >
        Transcript: {currentTranscript ? `"${currentTranscript.substring(0, 20)}${currentTranscript.length > 20 ? '...' : ''}"` : 'None'}
      </div>

      {/* Status indicator */}
      <div className="status-indicator">
        <div className="status-text">{getStatusText()}</div>
        {status === 'connected' && (
          <div className="volume-indicator">
            <div className="volume-bars">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className={`volume-bar ${i < getVolumeBarLevel() ? 'active' : ''}`}
                />
              ))}
            </div>
            <div className="speaking-status">
              {isSpeaking ?
                <span style={{ fontWeight: 'bold', color: '#0ea5e9' }}>🔊 Assistant is speaking...</span> :
                <span>🎤 Listening...</span>
              }
            </div>
          </div>
        )}
      </div>

      {/* Messages container */}
      <div className="messages-container">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`message-bubble ${message.role === 'user' ? 'user' : 'assistant'}`}
          >
            <div className="message-content">
              {message.content}
            </div>
          </div>
        ))}

        {/* Transcript bubble for real-time transcription */}
        <div className={`message-bubble assistant transcript ${currentTranscript && currentTranscript.trim() ? 'has-content' : ''}`}>
          <div className="message-label" style={{ fontSize: '12px', marginBottom: '4px', fontWeight: 'bold', color: '#0ea5e9' }}>
            🎤 Real-time transcript:
          </div>
          <div className="message-content" style={{ fontStyle: 'italic' }}>
            {currentTranscript || 'Listening...'}
          </div>
        </div>

        {/* Auto-scroll reference */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input area - only show if connected */}
      {status === 'connected' && (
        <div className="input-container">
          <input
            type="text"
            value={inputText}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="message-input"
          />
          <button
            onClick={sendMessage}
            className="send-button"
            disabled={!inputText.trim()}
          >
            Send
          </button>
        </div>
      )}

      {/* Error display */}
      {status === 'error' && (
        <div className="error-container">
          <p className="error-message">An error occurred with the call.</p>
          <button
            onClick={startCall}
            className="retry-button"
          >
            Retry
          </button>
        </div>
      )}

      {/* Dossier data display if available */}
      {hasDossierData() && (
        <div className="dossier-container">
          <h3>Case Information</h3>
          <div className="dossier-items">
            {getDossierDisplayItems()}
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleVapiCall;
