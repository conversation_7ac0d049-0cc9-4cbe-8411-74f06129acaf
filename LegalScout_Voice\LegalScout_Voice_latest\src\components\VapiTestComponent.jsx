/**
 * Vapi Test Component
 * 
 * Simple component to test our consolidated Vapi integration
 */

import React, { useState, useEffect } from 'react';
import { validateVapiConfig, getVapiApiKey } from '../config/vapiConfig';
import { vapiService } from '../services/VapiService';

// Simple voice call test component
const VoiceCallTest = () => {
  const [status, setStatus] = useState('idle');
  const [logs, setLogs] = useState([]);
  const [vapiInstance, setVapiInstance] = useState(null);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[VoiceCallTest] ${message}`);
  };

  // Test Vapi instance creation
  const testVapiInstance = async () => {
    try {
      setStatus('testing');
      addLog('🔧 Testing Vapi instance creation...');

      const instance = await vapiService.createVapiInstance();
      setVapiInstance(instance);
      addLog('✅ Vapi instance created successfully', 'success');
      setStatus('ready');
    } catch (error) {
      addLog(`❌ Error creating Vapi instance: ${error.message}`, 'error');
      setStatus('error');
    }
  };

  // Test call initiation
  const testCallStart = async () => {
    try {
      if (!vapiInstance) {
        throw new Error('No Vapi instance available');
      }

      setStatus('calling');
      addLog('🚀 Testing call start...');

      const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
      addLog(`🎯 Using assistant ID: ${assistantId}`);

      // Set up event listeners
      vapiInstance.on('call-start', () => {
        addLog('✅ Call started successfully!', 'success');
        setStatus('connected');
      });

      vapiInstance.on('call-end', () => {
        addLog('📞 Call ended', 'info');
        setStatus('ready');
      });

      vapiInstance.on('error', (error) => {
        addLog(`❌ Call error: ${error.message}`, 'error');
        setStatus('error');
      });

      // Start the call using the service
      await vapiService.startCall(vapiInstance, { assistantId });
      addLog('🔄 Call start command sent...');

    } catch (error) {
      addLog(`❌ Error starting call: ${error.message}`, 'error');
      setStatus('error');
    }
  };

  // Stop call
  const stopCall = async () => {
    try {
      if (vapiInstance) {
        await vapiInstance.stop();
        addLog('🛑 Call stopped');
        setStatus('ready');
      }
    } catch (error) {
      addLog(`❌ Error stopping call: ${error.message}`, 'error');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div>
      <div className="mb-4">
        <strong>Status:</strong>
        <span className={`ml-2 px-2 py-1 rounded text-sm ${
          status === 'connected' ? 'bg-green-100 text-green-800' :
          status === 'error' ? 'bg-red-100 text-red-800' :
          status === 'ready' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'
        }`}>
          {status}
        </span>
      </div>

      <div className="mb-4 space-x-2">
        <button
          onClick={testVapiInstance}
          disabled={status === 'testing' || status === 'calling'}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50"
        >
          Test Vapi Instance
        </button>

        <button
          onClick={testCallStart}
          disabled={!vapiInstance || status === 'calling' || status === 'connected'}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 disabled:opacity-50"
        >
          Test Call Start
        </button>

        <button
          onClick={stopCall}
          disabled={status !== 'connected'}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 disabled:opacity-50"
        >
          Stop Call
        </button>

        <button
          onClick={clearLogs}
          className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          Clear Logs
        </button>
      </div>

      <div className="h-48 overflow-y-auto border border-gray-300 p-2 bg-gray-50 text-xs font-mono">
        {logs.map((log, index) => (
          <div
            key={index}
            className={`mb-1 ${
              log.type === 'error' ? 'text-red-600' :
              log.type === 'success' ? 'text-green-600' : 'text-gray-700'
            }`}
          >
            <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
          </div>
        ))}
      </div>
    </div>
  );
};

const VapiTestComponent = () => {
  const [configStatus, setConfigStatus] = useState(null);
  const [serviceStatus, setServiceStatus] = useState(null);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    const results = [];

    // Test 1: Validate configuration
    try {
      const validation = validateVapiConfig();
      setConfigStatus(validation);
      results.push({
        test: 'Configuration Validation',
        status: validation.isValid ? 'PASS' : 'FAIL',
        details: validation.warnings.join(', ') || 'All checks passed'
      });
    } catch (error) {
      results.push({
        test: 'Configuration Validation',
        status: 'ERROR',
        details: error.message
      });
    }

    // Test 2: API Key Resolution
    try {
      const clientKey = getVapiApiKey('client');
      const serverKey = getVapiApiKey('server');
      
      results.push({
        test: 'API Key Resolution',
        status: (clientKey && serverKey) ? 'PASS' : 'FAIL',
        details: `Client: ${clientKey ? 'Available' : 'Missing'}, Server: ${serverKey ? 'Available' : 'Missing'}`
      });
    } catch (error) {
      results.push({
        test: 'API Key Resolution',
        status: 'ERROR',
        details: error.message
      });
    }

    // Test 3: Service Initialization
    try {
      const initialized = await vapiService.initialize();
      setServiceStatus(initialized ? 'Initialized' : 'Failed');
      
      results.push({
        test: 'Service Initialization',
        status: initialized ? 'PASS' : 'FAIL',
        details: initialized ? 'Service initialized successfully' : 'Service initialization failed'
      });
    } catch (error) {
      results.push({
        test: 'Service Initialization',
        status: 'ERROR',
        details: error.message
      });
    }

    // Test 4: MCP Connection
    try {
      const mcpConnected = vapiService.isMcpConnected();
      
      results.push({
        test: 'MCP Connection',
        status: mcpConnected ? 'PASS' : 'FAIL',
        details: mcpConnected ? 'MCP service connected' : 'MCP service not connected'
      });
    } catch (error) {
      results.push({
        test: 'MCP Connection',
        status: 'ERROR',
        details: error.message
      });
    }

    setTestResults(results);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS': return 'text-green-600';
      case 'FAIL': return 'text-red-600';
      case 'ERROR': return 'text-red-800';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Vapi Integration Test Results</h2>
      
      {/* Configuration Status */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Configuration Status</h3>
        {configStatus ? (
          <div>
            <p className={`font-medium ${configStatus.isValid ? 'text-green-600' : 'text-red-600'}`}>
              {configStatus.isValid ? '✅ Valid' : '❌ Invalid'}
            </p>
            <p className="text-sm text-gray-600">
              Public Key: {configStatus.hasPublicKey ? '✅ Available' : '❌ Missing'}
            </p>
            <p className="text-sm text-gray-600">
              Secret Key: {configStatus.hasSecretKey ? '✅ Available' : '❌ Missing'}
            </p>
            {configStatus.warnings.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium text-yellow-600">Warnings:</p>
                <ul className="text-sm text-yellow-600 list-disc list-inside">
                  {configStatus.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <p className="text-gray-500">Loading...</p>
        )}
      </div>

      {/* Service Status */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Service Status</h3>
        <p className={`font-medium ${serviceStatus === 'Initialized' ? 'text-green-600' : 'text-red-600'}`}>
          {serviceStatus || 'Loading...'}
        </p>
      </div>

      {/* Test Results */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Test Results</h3>
        <div className="space-y-3">
          {testResults.map((result, index) => (
            <div key={index} className="p-3 border rounded-lg">
              <div className="flex justify-between items-start">
                <span className="font-medium">{result.test}</span>
                <span className={`font-bold ${getStatusColor(result.status)}`}>
                  {result.status}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{result.details}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Voice Call Test */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-4">🎤 Voice Call Test</h3>
        <VoiceCallTest />
      </div>

      {/* Actions */}
      <div className="flex gap-4">
        <button
          onClick={runTests}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Re-run Tests
        </button>
        <button
          onClick={() => vapiService.reset()}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Reset Service
        </button>
      </div>
    </div>
  );
};

export default VapiTestComponent;
