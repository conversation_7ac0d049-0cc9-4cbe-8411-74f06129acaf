-- Create consultations table to store client consultation records
CREATE TABLE IF NOT EXISTS public.consultations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationships
  attorney_id UUID REFERENCES public.attorneys(id) NOT NULL,
  
  -- Client Information
  client_name TEXT,
  client_email TEXT,
  client_phone TEXT,
  
  -- Consultation Details
  summary TEXT,
  transcript TEXT,
  duration INTEGER, -- in seconds
  
  -- Additional Data
  practice_area TEXT,
  location TEXT,
  location_data JSONB,
  metadata JSONB DEFAULT '{}'::JSONB,
  
  -- Status
  status TEXT DEFAULT 'new',
  follow_up_date TIMESTAMP WITH TIME ZONE
);

-- Add index for attorney_id for faster lookups
CREATE INDEX IF NOT EXISTS consultations_attorney_id_idx ON public.consultations (attorney_id);

-- Add RLS policies for consultations
ALTER TABLE public.consultations ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view their own consultations
CREATE POLICY "Users can view their own consultations" ON public.consultations
  FOR SELECT
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to insert their own consultations
CREATE POLICY "Users can insert their own consultations" ON public.consultations
  FOR INSERT
  WITH CHECK (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to update their own consultations
CREATE POLICY "Users can update their own consultations" ON public.consultations
  FOR UPDATE
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to delete their own consultations
CREATE POLICY "Users can delete their own consultations" ON public.consultations
  FOR DELETE
  USING (auth.uid() = attorney_id);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_consultations_updated_at
BEFORE UPDATE ON public.consultations
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add comment to the table for documentation
COMMENT ON TABLE public.consultations IS 'Stores client consultation records for attorneys';
