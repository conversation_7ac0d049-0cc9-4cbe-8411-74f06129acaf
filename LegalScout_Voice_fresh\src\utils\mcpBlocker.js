/**
 * MCP Blocker Utility
 * Temporarily blocks MCP requests only when website import is active
 */

class McpBlocker {
  constructor() {
    this.isBlocking = false;
    this.originalFetch = null;
    this.blockedRequests = [];
  }

  /**
   * Start blocking MCP requests
   */
  startBlocking() {
    if (this.isBlocking) return;
    
    console.log('[McpBlocker] 🚫 Starting MCP blocking for website import');
    this.isBlocking = true;
    
    // Store original fetch
    this.originalFetch = window.fetch;
    
    // Override fetch to block MCP requests
    window.fetch = (url, options = {}) => {
      if (typeof url === 'string' && url.includes('mcp')) {
        console.log('[McpBlocker] 🚫 Blocked MCP request during import:', url);
        this.blockedRequests.push({ url, timestamp: Date.now() });
        
        // Return a rejected promise to trigger fallback behavior
        return Promise.reject(new Error('MCP_BLOCKED_DURING_IMPORT'));
      }
      
      // Allow all other requests
      return this.originalFetch.call(window, url, options);
    };
  }

  /**
   * Stop blocking MCP requests
   */
  stopBlocking() {
    if (!this.isBlocking) return;
    
    console.log('[Mc<PERSON><PERSON>lock<PERSON>] ✅ Stopping MCP blocking, restoring normal functionality');
    this.isBlocking = false;
    
    // Restore original fetch
    if (this.originalFetch) {
      window.fetch = this.originalFetch;
      this.originalFetch = null;
    }
    
    // Log blocked requests summary
    if (this.blockedRequests.length > 0) {
      console.log(`[McpBlocker] 📊 Blocked ${this.blockedRequests.length} MCP requests during import`);
      this.blockedRequests = [];
    }
  }

  /**
   * Check if currently blocking
   */
  isCurrentlyBlocking() {
    return this.isBlocking;
  }

  /**
   * Get blocked requests count
   */
  getBlockedCount() {
    return this.blockedRequests.length;
  }
}

// Create singleton instance
const mcpBlocker = new McpBlocker();

export default mcpBlocker;
