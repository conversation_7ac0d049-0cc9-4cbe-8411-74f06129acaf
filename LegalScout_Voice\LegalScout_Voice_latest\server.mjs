import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Load environment variables
dotenv.config();

// Add middleware to parse JSON requests
app.use(express.json());

// Serve the entire build folder (including assets)
app.use(express.static(path.join(__dirname, 'dist')));

// Vapi API credentials
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY;
const VAPI_PUBLIC_KEY = process.env.VAPI_PUBLIC_KEY;
const VAPI_HOST_URL = process.env.VAPI_HOST_URL || 'https://api.vapi.ai';

// Add Vapi webhook endpoint
app.post('/api/webhook/vapi', (req, res) => {
  console.log('Received Vapi webhook:', req.body);

  // You can process the webhook data here
  // For example, you could store it in a database

  // Return a success response
  res.status(200).json({ success: true });
});

// Vapi proxy API endpoints
const vapiRouter = express.Router();

// Middleware to check if Vapi is configured
const checkVapiConfigured = (req, res, next) => {
  if (!VAPI_SECRET_KEY || !VAPI_PUBLIC_KEY) {
    return res.status(503).json({
      error: 'Vapi is not configured. Please set up Vapi credentials in the environment variables.'
    });
  }
  next();
};

// Create headers for Vapi API requests
const createVapiHeaders = () => {
  const headers = {
    'Content-Type': 'application/json'
  };

  if (VAPI_PUBLIC_KEY) {
    headers['X-API-KEY'] = VAPI_PUBLIC_KEY;
  }

  if (VAPI_SECRET_KEY) {
    headers['X-API-SECRET'] = VAPI_SECRET_KEY;
  }

  return headers;
};

// Get Vapi configuration status
vapiRouter.get('/config', (req, res) => {
  try {
    const configured = !!(VAPI_SECRET_KEY && VAPI_PUBLIC_KEY);

    // Create a safe response that doesn't include sensitive credentials
    const config = {
      configured,
      hostUrl: configured ? 'Configured' : 'Not configured',
      publicKey: configured ? 'Configured' : 'Not configured',
      secretKey: configured ? 'Configured' : 'Not configured'
    };

    res.json(config);
  } catch (error) {
    console.error('Error getting Vapi config:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all assistants
vapiRouter.get('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistants = await response.json();
    res.json(assistants);
  } catch (error) {
    console.error('Error getting assistants:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get an assistant by ID
vapiRouter.get('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants/${req.params.id}`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error getting assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create a new assistant
vapiRouter.post('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants`, {
      method: 'POST',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error creating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update an assistant
vapiRouter.patch('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/assistants/${req.params.id}`, {
      method: 'PATCH',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const assistant = await response.json();
    res.json(assistant);
  } catch (error) {
    console.error('Error updating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all phone numbers
vapiRouter.get('/phone-numbers', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/phone-numbers`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const phoneNumbers = await response.json();
    res.json(phoneNumbers);
  } catch (error) {
    console.error('Error getting phone numbers:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create a new call
vapiRouter.post('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/calls`, {
      method: 'POST',
      headers: createVapiHeaders(),
      body: JSON.stringify(req.body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const call = await response.json();
    res.json(call);
  } catch (error) {
    console.error('Error creating call:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all calls
vapiRouter.get('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const response = await fetch(`${VAPI_HOST_URL}/calls`, {
      headers: createVapiHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error (${response.status}): ${errorText}`);
    }

    const calls = await response.json();
    res.json(calls);
  } catch (error) {
    console.error('Error getting calls:', error);
    res.status(500).json({ error: error.message });
  }
});

// Mount the Vapi router
app.use('/api/vapi', vapiRouter);

// Try to import and mount the call logs API
try {
  // Dynamic import for ESM compatibility
  const callLogsModule = await import('./src/api/callLogsApi.js');
  const callLogsRouter = callLogsModule.default;
  app.use('/api/call-logs', callLogsRouter);
} catch (error) {
  console.warn('Could not load call logs API:', error.message);
}

// Fallback to serve index.html for any route not matching a static file or API route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
