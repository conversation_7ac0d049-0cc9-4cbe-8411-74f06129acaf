/* Call Records Table Styles */

.call-records-container {
  margin: 20px 0;
  font-family: 'Inter', sans-serif;
}

.call-records-container h2 {
  margin-bottom: 15px;
  font-size: 20px;
  font-weight: 600;
}

/* Loading and Error States */
.call-records-loading,
.call-records-error,
.call-records-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.call-records-error button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Table Styles */
.call-records-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.call-records-table th,
.call-records-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.call-records-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.call-records-table tr:last-child td {
  border-bottom: none;
}

.call-records-table tr:hover {
  background-color: #f8f9fa;
}

/* Dark theme adjustments for table */
[data-theme="dark"] .call-records-table {
  background-color: #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .call-records-table th,
[data-theme="dark"] .call-records-table td {
  border-bottom-color: #2c2c2c;
  color: #e9ecef;
}

[data-theme="dark"] .call-records-table th {
  background-color: #2c2c2c;
  color: #adb5bd;
}

[data-theme="dark"] .call-records-table tr:hover {
  background-color: #2c2c2c;
}

/* Status Colors */
.call-records-table tr.status-in-progress {
  background-color: rgba(40, 167, 69, 0.05);
}

.call-records-table tr.status-completed {
  background-color: rgba(108, 117, 125, 0.05);
}

.call-records-table tr.status-ended {
  background-color: rgba(108, 117, 125, 0.05);
}

.call-records-table tr.status-error {
  background-color: rgba(220, 53, 69, 0.05);
}

/* Action Buttons */
.call-records-table .actions {
  display: flex;
  gap: 8px;
}

.call-records-table .view-button,
.call-records-table .control-button,
.call-records-table .notify-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.call-records-table .view-button {
  background-color: #6c757d;
  color: #fff;
}

.call-records-table .control-button {
  background-color: #17a2b8;
  color: #fff;
}

.call-records-table .notify-button {
  background-color: #ffc107;
  color: #212529;
}

.call-records-table .view-button:hover {
  background-color: #5a6268;
}

.call-records-table .control-button:hover {
  background-color: #138496;
}

.call-records-table .notify-button:hover {
  background-color: #e0a800;
}

.call-records-table button:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* Dark theme adjustments for buttons */
[data-theme="dark"] .call-records-table .view-button {
  background-color: #495057;
}

[data-theme="dark"] .call-records-table .control-button {
  background-color: #0f6674;
}

[data-theme="dark"] .call-records-table .notify-button {
  background-color: #d39e00;
  color: #212529;
}

[data-theme="dark"] .call-records-table .view-button:hover {
  background-color: #343a40;
}

[data-theme="dark"] .call-records-table .control-button:hover {
  background-color: #0c525d;
}

[data-theme="dark"] .call-records-table .notify-button:hover {
  background-color: #b68500;
}

[data-theme="dark"] .call-records-table button:disabled {
  background-color: #343a40;
  color: #6c757d;
}

/* Call Details Modal */
.call-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.call-details-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.call-details-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}

.call-details-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.call-details-info p {
  margin: 8px 0;
}

/* Dark theme adjustments for call details modal */
[data-theme="dark"] .call-details-content {
  background-color: #1e1e1e;
  color: #e9ecef;
}

[data-theme="dark"] .close-button {
  color: #adb5bd;
}

[data-theme="dark"] .call-details-content h3 {
  color: #f8f9fa;
}

[data-theme="dark"] .call-details-info {
  background-color: #2c2c2c;
  color: #e9ecef;
}

.call-details-transcripts h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.transcripts-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 10px;
}

.transcript-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
}

.transcript-item.assistant {
  background-color: #e9f5ff;
}

.transcript-item.user {
  background-color: #f0f0f0;
}

.transcript-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.transcript-role {
  font-weight: 600;
}

.transcript-time {
  color: #6c757d;
}

.transcript-content {
  font-size: 14px;
  line-height: 1.5;
}

/* Dark theme adjustments for transcripts */
[data-theme="dark"] .transcript-item.assistant {
  background-color: #0d3a58;
  color: #e9ecef;
}

[data-theme="dark"] .transcript-item.user {
  background-color: #2c2c2c;
  color: #e9ecef;
}

[data-theme="dark"] .transcript-role {
  color: #f8f9fa;
}

[data-theme="dark"] .transcript-time {
  color: #adb5bd;
}

.call-details-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: flex-end;
}

.call-details-actions .control-button,
.call-details-actions .notify-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.call-details-actions .control-button {
  background-color: #17a2b8;
  color: #fff;
}

.call-details-actions .notify-button {
  background-color: #ffc107;
  color: #212529;
}

.call-details-actions .control-button:hover {
  background-color: #138496;
}

.call-details-actions .notify-button:hover {
  background-color: #e0a800;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .call-records-table {
    display: block;
    overflow-x: auto;
  }

  .call-details-content {
    width: 95%;
    padding: 15px;
  }

  .call-details-actions {
    flex-direction: column;
  }

  .call-details-actions .control-button,
  .call-details-actions .notify-button {
    width: 100%;
  }
}
