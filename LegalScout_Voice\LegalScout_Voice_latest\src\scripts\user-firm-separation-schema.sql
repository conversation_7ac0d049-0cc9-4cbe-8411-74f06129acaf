-- User/Firm Separation Schema
-- Run this in Supabase SQL Editor to create the separated user/firm structure

-- 1. User Profiles Table (Personal Information)
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- User Identity
  user_id UUID REFERENCES auth.users(id) UNIQUE NOT NULL,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  phone TEXT,
  
  -- Personal Settings
  timezone TEXT DEFAULT 'America/New_York',
  language TEXT DEFAULT 'en',
  
  -- Status
  is_active BOOLEAN DEFAULT true
);

-- 2. Firm Profiles Table (Business Information)
CREATE TABLE IF NOT EXISTS public.firm_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ownership
  owner_user_id UUID REFERENCES auth.users(id) NOT NULL,
  
  -- Firm Information
  firm_name TEXT NOT NULL,
  practice_areas TEXT[] DEFAULT '{}',
  address TEXT,
  phone TEXT,
  website TEXT,
  
  -- Branding
  logo_url TEXT,
  primary_color TEXT DEFAULT '#4B74AA',
  secondary_color TEXT DEFAULT '#2C3E50',
  background_color TEXT DEFAULT '#1a1a1a',
  
  -- Configuration
  is_primary BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Ensure one primary firm per user
  UNIQUE(owner_user_id, is_primary) WHERE is_primary = true
);

-- 3. Assistant Configurations Table (AI Assistant Settings)
CREATE TABLE IF NOT EXISTS public.assistant_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationships
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  firm_id UUID REFERENCES public.firm_profiles(id) NOT NULL,
  
  -- Vapi Integration
  vapi_assistant_id TEXT UNIQUE,
  subdomain TEXT UNIQUE,
  
  -- Assistant Personality
  assistant_name TEXT DEFAULT 'Scout',
  welcome_message TEXT,
  instructions TEXT,
  information_gathering TEXT,
  
  -- Voice Configuration
  voice_provider TEXT DEFAULT 'openai',
  voice_id TEXT DEFAULT 'alloy',
  ai_model TEXT DEFAULT 'gpt-4o',
  
  -- Status
  is_primary BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Ensure one primary assistant per user
  UNIQUE(user_id, is_primary) WHERE is_primary = true
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);

CREATE INDEX IF NOT EXISTS idx_firm_profiles_owner ON public.firm_profiles(owner_user_id);
CREATE INDEX IF NOT EXISTS idx_firm_profiles_primary ON public.firm_profiles(owner_user_id, is_primary) WHERE is_primary = true;

CREATE INDEX IF NOT EXISTS idx_assistant_configs_user ON public.assistant_configurations(user_id);
CREATE INDEX IF NOT EXISTS idx_assistant_configs_firm ON public.assistant_configurations(firm_id);
CREATE INDEX IF NOT EXISTS idx_assistant_configs_vapi ON public.assistant_configurations(vapi_assistant_id);
CREATE INDEX IF NOT EXISTS idx_assistant_configs_subdomain ON public.assistant_configurations(subdomain);

-- 5. Row Level Security (RLS) Policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.firm_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assistant_configurations ENABLE ROW LEVEL SECURITY;

-- User Profiles RLS
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Firm Profiles RLS
CREATE POLICY "Users can view own firms" ON public.firm_profiles
  FOR SELECT USING (auth.uid() = owner_user_id);

CREATE POLICY "Users can update own firms" ON public.firm_profiles
  FOR UPDATE USING (auth.uid() = owner_user_id);

CREATE POLICY "Users can insert own firms" ON public.firm_profiles
  FOR INSERT WITH CHECK (auth.uid() = owner_user_id);

-- Assistant Configurations RLS
CREATE POLICY "Users can view own assistants" ON public.assistant_configurations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own assistants" ON public.assistant_configurations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own assistants" ON public.assistant_configurations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 6. Functions for data integrity

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON public.user_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_firm_profiles_updated_at 
  BEFORE UPDATE ON public.firm_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assistant_configurations_updated_at 
  BEFORE UPDATE ON public.assistant_configurations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Migration function to move existing data
CREATE OR REPLACE FUNCTION migrate_existing_attorney_data()
RETURNS void AS $$
DECLARE
  attorney_record RECORD;
  user_profile_id UUID;
  firm_profile_id UUID;
BEGIN
  -- Loop through existing attorneys table
  FOR attorney_record IN 
    SELECT * FROM public.attorneys WHERE user_id IS NOT NULL
  LOOP
    -- Create user profile
    INSERT INTO public.user_profiles (
      user_id, email, full_name, phone
    ) VALUES (
      attorney_record.user_id,
      attorney_record.email,
      attorney_record.name,
      attorney_record.phone
    ) ON CONFLICT (user_id) DO NOTHING
    RETURNING id INTO user_profile_id;

    -- Create firm profile
    INSERT INTO public.firm_profiles (
      owner_user_id, firm_name, practice_areas, address, phone,
      logo_url, primary_color, secondary_color, is_primary, is_active
    ) VALUES (
      attorney_record.user_id,
      attorney_record.firm_name,
      attorney_record.practice_areas,
      attorney_record.address,
      attorney_record.phone,
      attorney_record.logo_url,
      COALESCE(attorney_record.primary_color, '#4B74AA'),
      COALESCE(attorney_record.secondary_color, '#2C3E50'),
      true,
      attorney_record.is_active
    ) ON CONFLICT (owner_user_id, is_primary) WHERE is_primary = true DO NOTHING
    RETURNING id INTO firm_profile_id;

    -- Create assistant configuration
    INSERT INTO public.assistant_configurations (
      user_id, firm_id, vapi_assistant_id, subdomain,
      welcome_message, instructions, information_gathering,
      voice_provider, voice_id, ai_model, is_primary, is_active
    ) VALUES (
      attorney_record.user_id,
      firm_profile_id,
      attorney_record.vapi_assistant_id,
      attorney_record.subdomain,
      attorney_record.welcome_message,
      attorney_record.vapi_instructions,
      attorney_record.information_gathering,
      COALESCE(attorney_record.voice_provider, 'openai'),
      COALESCE(attorney_record.voice_id, 'alloy'),
      COALESCE(attorney_record.ai_model, 'gpt-4o'),
      true,
      attorney_record.is_active
    ) ON CONFLICT (user_id, is_primary) WHERE is_primary = true DO NOTHING;

  END LOOP;
  
  RAISE NOTICE 'Migration completed successfully';
END;
$$ LANGUAGE plpgsql;

-- 8. Comments for documentation
COMMENT ON TABLE public.user_profiles IS 'Personal information for authenticated users';
COMMENT ON TABLE public.firm_profiles IS 'Business/practice information separate from user identity';
COMMENT ON TABLE public.assistant_configurations IS 'AI assistant settings linking users to their firm assistants';

COMMENT ON COLUMN public.firm_profiles.is_primary IS 'Each user can have one primary firm';
COMMENT ON COLUMN public.assistant_configurations.is_primary IS 'Each user can have one primary assistant per firm';

-- To run the migration after creating tables:
-- SELECT migrate_existing_attorney_data();
