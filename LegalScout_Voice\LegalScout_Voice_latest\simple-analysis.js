const fs = require('fs');
const path = require('path');

console.log('🔍 CODEBASE ANALYSIS STARTING...\n');
console.log('Current working directory:', process.cwd());
console.log('Node version:', process.version);
console.log('');

// Key files to check
const criticalFiles = [
  'src/pages/DashboardNew.jsx',
  'src/components/dashboard/HeaderAssistantSelector.jsx', 
  'src/components/dashboard/EnhancedAssistantDropdown.jsx',
  'src/App.jsx',
  'src/contexts/AuthContext.jsx',
  'src/services/vapiService.js',
  'package.json'
];

// Clone directories
const cloneDirs = ['fresh_clone', 'temp_clone', 'temp_repo'];

const analysis = {
  timestamp: new Date().toISOString(),
  existingFiles: [],
  missingFiles: [],
  cloneDirectories: [],
  recommendations: {
    intentionalChanges: [],
    worthKeeping: [],
    safeToDelete: []
  }
};

console.log('📁 Checking critical files...');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - EXISTS`);
    analysis.existingFiles.push(file);
    
    // Check if it contains Vapi code
    try {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('vapi') || content.includes('Vapi')) {
        analysis.recommendations.worthKeeping.push({
          file: file,
          reason: 'Contains Vapi integration code'
        });
      }
      if (file.includes('DashboardNew') || file.includes('HeaderAssistant')) {
        analysis.recommendations.intentionalChanges.push({
          file: file,
          reason: 'Part of ASSISTANTS_SCOUT branch modern dashboard'
        });
      }
    } catch (e) {
      console.log(`⚠️  Could not read ${file}: ${e.message}`);
    }
  } else {
    console.log(`❌ ${file} - MISSING`);
    analysis.missingFiles.push(file);
  }
});

console.log('\n📂 Checking clone directories...');
cloneDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`📁 ${dir} - EXISTS`);
    analysis.cloneDirectories.push(dir);
    analysis.recommendations.safeToDelete.push({
      file: dir,
      reason: 'Clone directory no longer needed after analysis'
    });
  } else {
    console.log(`📁 ${dir} - NOT FOUND`);
  }
});

// Export analysis
fs.writeFileSync('codebase-analysis-report.json', JSON.stringify(analysis, null, 2));

console.log('\n✅ ANALYSIS COMPLETE!');
console.log(`📄 Report exported to: codebase-analysis-report.json`);
console.log(`📊 Summary: ${analysis.existingFiles.length}/${criticalFiles.length} critical files exist`);
console.log(`📊 Clone directories: ${analysis.cloneDirectories.length}`);
console.log(`📊 Intentional changes: ${analysis.recommendations.intentionalChanges.length}`);
console.log(`📊 Worth keeping: ${analysis.recommendations.worthKeeping.length}`);

console.log('\n🎯 KEY FINDINGS:');
if (analysis.recommendations.intentionalChanges.length > 0) {
  console.log('✨ INTENTIONAL CHANGES (ASSISTANTS_SCOUT features):');
  analysis.recommendations.intentionalChanges.forEach(item => {
    console.log(`  - ${item.file}`);
  });
}

if (analysis.recommendations.worthKeeping.length > 0) {
  console.log('💎 WORTH KEEPING (Vapi integrations):');
  analysis.recommendations.worthKeeping.forEach(item => {
    console.log(`  - ${item.file}`);
  });
}

if (analysis.cloneDirectories.length > 0) {
  console.log('🗑️  SAFE TO DELETE (Clone directories):');
  analysis.cloneDirectories.forEach(dir => {
    console.log(`  - ${dir}`);
  });
}
