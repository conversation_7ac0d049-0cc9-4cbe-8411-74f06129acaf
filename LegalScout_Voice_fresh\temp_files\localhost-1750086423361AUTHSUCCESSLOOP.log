VM21:26 🚀 [LegalScout] Initializing environment...
VM21:48 ✅ [LegalScout] Environment initialized
callback:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
hookSettingsInjector.js:229 [vite] connecting...
hookSettingsInjector.js:325 [vite] connected.
ThemeToggle.css:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
MapDossierView.css:30 [VapiLoader] Starting Vapi SDK loading process
MapDossierView.css:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
useAttorneyProfile.js?t=*************:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
useAttorneyProfile.js?t=*************:42 [AttorneyProfileManager] Auto-initializing from localStorage
useAttorneyProfile.js?t=*************:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
content.js:22 🚀 [LegalScout] Starting React app...
content.js:31 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
content.js:44 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
content.js:89 ✅ [LegalScout] React app rendered successfully
565.js:13699 [ErrorBoundary] Creating React placeholder
565.js:13699 [ErrorBoundary] Adding createContext placeholder
565.js:13699 [ErrorBoundary] Adding useState placeholder
565.js:13699 [ErrorBoundary] Adding useEffect placeholder
565.js:13699 [ErrorBoundary] Adding useLayoutEffect placeholder
565.js:13699 [ErrorBoundary] Adding useRef placeholder
565.js:13699 [ErrorBoundary] Adding useCallback placeholder
565.js:13699 [ErrorBoundary] Adding useMemo placeholder
565.js:13699 [ErrorBoundary] Adding useContext placeholder
565.js:13699 [ErrorBoundary] Adding forwardRef placeholder
565.js:13699 [ErrorBoundary] Adding createElement placeholder
565.js:13699 [ErrorBoundary] Adding cloneElement placeholder
565.js:13699 [ErrorBoundary] Adding createRef placeholder
565.js:13699 [ErrorBoundary] Adding Component placeholder
565.js:13699 [ErrorBoundary] Adding PureComponent placeholder
565.js:13699 [ErrorBoundary] Adding Fragment placeholder
565.js:13699 [ErrorBoundary] Adding Children placeholder
565.js:13699 [ErrorBoundary] Adding isValidElement placeholder
565.js:13699 [ErrorBoundary] React polyfills applied
main.jsx:78 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] App component is starting!
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
main.jsx:78 🔥 [App.jsx] App component is starting!
main.jsx:78 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
main.jsx:78 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
main.jsx:78 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
370.js:39 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
370.js:102 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
370.js:109 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
main.jsx:78 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
main.jsx:78 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
main.jsx:78 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
attorneyStateManager.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
main.jsx:78 🏠 [SubdomainTester] Localhost detected, returning default subdomain
MapDossierView.css:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
MapDossierView.css:51 [VapiLoader] ✅ Vapi SDK validation successful
AboutPage.jsx:17 🔐 [AuthCallback] Starting OAuth callback handling...
AboutPage.jsx:36 🔐 [AuthCallback] Checking for OAuth tokens...
AboutPage.jsx:37 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Flo7hQVoG0VskkGHMhJjpVOWk9G6DSqZjMWMSmQEXp4&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=nlejqtelhn6n&token_type=bearer
AboutPage.jsx:39 🔐 [AuthCallback] Query params: 
AboutPage.jsx:40 🔐 [AuthCallback] Access token found: true
AboutPage.jsx:65 🔐 [AuthCallback] Found OAuth tokens in URL
AboutPage.jsx:73 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AboutPage.jsx:74 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js?t=*************:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js?t=*************:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js?t=*************:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js?t=*************:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js?t=*************:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js?t=*************:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js?t=*************:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
999.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
999.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
370.js:49 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
370.js:59 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
ProductionErrorBoundary.jsx:191 🚀 [App] Initializing with safe subdomain detection...
ThemeToggle.jsx:20 🔐 [AuthContext] Starting auth initialization...
ThemeToggle.jsx:21 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
ThemeToggle.css:31 🔧 [Supabase] Initializing real Supabase client...
ThemeToggle.css:33 ✅ [Supabase] Real client initialized successfully
ThemeToggle.jsx:34 Using real authentication in all environments
AboutPage.jsx:17 🔐 [AuthCallback] Starting OAuth callback handling...
AboutPage.jsx:36 🔐 [AuthCallback] Checking for OAuth tokens...
AboutPage.jsx:37 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Flo7hQVoG0VskkGHMhJjpVOWk9G6DSqZjMWMSmQEXp4&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=nlejqtelhn6n&token_type=bearer
AboutPage.jsx:39 🔐 [AuthCallback] Query params: 
AboutPage.jsx:40 🔐 [AuthCallback] Access token found: true
AboutPage.jsx:65 🔐 [AuthCallback] Found OAuth tokens in URL
AboutPage.jsx:73 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AboutPage.jsx:74 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js?t=*************:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js?t=*************:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js?t=*************:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js?t=*************:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js?t=*************:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js?t=*************:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js?t=*************:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
999.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
999.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
370.js:49 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
370.js:59 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
ProductionErrorBoundary.jsx:191 🚀 [App] Initializing with safe subdomain detection...
ThemeToggle.jsx:20 🔐 [AuthContext] Starting auth initialization...
ThemeToggle.jsx:21 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
ThemeToggle.jsx:34 Using real authentication in all environments
370.js:39 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
370.js:102 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
370.js:109 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
main.jsx:78 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
main.jsx:78 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
main.jsx:78 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
370.js:49 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
370.js:56 ✅ [AssistantAwareContext] Loading assistant data for: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
picture-in-picture.js:69 Loaded contentScript
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
contentScript.bundle.js:134 🔧 [ProductionEnvironment] Initializing production environment...
contentScript.bundle.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
ProductionErrorBoundary.jsx:191 ✅ Production environment initialized
ProductionErrorBoundary.jsx:79 Supabase config initialization (fallback)
ProductionErrorBoundary.jsx:82 Supabase config verification (fallback)
ProductionErrorBoundary.jsx:191 ✅ Supabase configured successfully
attorneyStateManager.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
ProductionErrorBoundary.jsx:191 🔍 [App] Subdomain detected: default
ProductionErrorBoundary.jsx:191 🏠 [App] Localhost detected - treating as main domain
ProductionErrorBoundary.jsx:191 🏁 [App] Initialization complete
contentScript.bundle.js:134 🔧 [ProductionEnvironment] Initializing production environment...
contentScript.bundle.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
ProductionErrorBoundary.jsx:191 ✅ Production environment initialized
ProductionErrorBoundary.jsx:79 Supabase config initialization (fallback)
ProductionErrorBoundary.jsx:82 Supabase config verification (fallback)
ProductionErrorBoundary.jsx:191 ✅ Supabase configured successfully
attorneyStateManager.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
ProductionErrorBoundary.jsx:191 🔍 [App] Subdomain detected: default
ProductionErrorBoundary.jsx:191 🏠 [App] Localhost detected - treating as main domain
ProductionErrorBoundary.jsx:191 🏁 [App] Initialization complete
authProfileFixer.js?t=*************:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
authProfileFixer.js?t=*************:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] App component is starting!
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
main.jsx:78 🔥 [App.jsx] App component is starting!
main.jsx:78 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
main.jsx:78 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
main.jsx:78 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
attorneyStateManager.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
main.jsx:78 🏠 [SubdomainTester] Localhost detected, returning default subdomain
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
authProfileFixer.js?t=*************:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js?t=*************:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js?t=*************:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AboutPage.jsx:76 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AboutPage.jsx:78 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AboutPage.jsx:81 💾 [AuthCallback] Attorney profile stored in localStorage
AboutPage.jsx:92 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
authProfileFixer.js?t=*************:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js?t=*************:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js?t=*************:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AboutPage.jsx:76 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AboutPage.jsx:78 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AboutPage.jsx:81 💾 [AuthCallback] Attorney profile stored in localStorage
AboutPage.jsx:92 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
callback#access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Flo7hQVoG0VskkGHMhJjpVOWk9G6DSqZjMWMSmQEXp4&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=nlejqtelhn6n&token_type=bearer:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useAttorneyProfile.js?t=*************:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] App component is starting!
ProductionErrorBoundary.jsx:191 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
ProductionErrorBoundary.jsx:191 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
main.jsx:78 🔥 [App.jsx] App component is starting!
main.jsx:78 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
main.jsx:78 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
main.jsx:78 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
ThemeToggle.jsx:34 Auth state changed: SIGNED_IN
ThemeToggle.jsx:34 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ThemeToggle.jsx:34 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
ThemeToggle.jsx:34 Found OAuth email (auth change): <EMAIL>
ThemeToggle.jsx:34 AuthContext: Handling auth state change for event: SIGNED_IN
ThemeToggle.jsx:34 SyncContext: Handling auth state for action: login
ThemeToggle.jsx:34 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
ThemeToggle.jsx:22 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
ThemeToggle.jsx:22 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
attorneyStateManager.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
main.jsx:78 🏠 [SubdomainTester] Localhost detected, returning default subdomain
ThemeToggle.jsx:34 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ ThemeToggle.jsx:34
(anonymous) @ useVapiCallWithDebug.js?t=*************:6263
_notifyAllSubscribers @ useVapiCallWithDebug.js?t=*************:6261
(anonymous) @ useVapiCallWithDebug.js?t=*************:5018
setTimeout
_initialize @ useVapiCallWithDebug.js?t=*************:5014
await in _initialize
(anonymous) @ useVapiCallWithDebug.js?t=*************:4977
(anonymous) @ useVapiCallWithDebug.js?t=*************:5542
(anonymous) @ useVapiCallWithDebug.js?t=*************:4828
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
main.jsx:78 Auth state error details: Empty response from server
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ ThemeToggle.jsx:34
(anonymous) @ useVapiCallWithDebug.js?t=*************:6263
_notifyAllSubscribers @ useVapiCallWithDebug.js?t=*************:6261
(anonymous) @ useVapiCallWithDebug.js?t=*************:5018
setTimeout
_initialize @ useVapiCallWithDebug.js?t=*************:5014
await in _initialize
(anonymous) @ useVapiCallWithDebug.js?t=*************:4977
(anonymous) @ useVapiCallWithDebug.js?t=*************:5542
(anonymous) @ useVapiCallWithDebug.js?t=*************:4828
main.jsx:78 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (chunk-CUTKQHYX.js?v=7a56b667:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async Object.callback (ThemeToggle.jsx:34:28)
    at async useVapiCallWithDebug.js?t=*************:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (useVapiCallWithDebug.js?t=*************:6268:7)
    at async useVapiCallWithDebug.js?t=*************:5018:13
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ ThemeToggle.jsx:34
(anonymous) @ useVapiCallWithDebug.js?t=*************:6263
_notifyAllSubscribers @ useVapiCallWithDebug.js?t=*************:6261
(anonymous) @ useVapiCallWithDebug.js?t=*************:5018
setTimeout
_initialize @ useVapiCallWithDebug.js?t=*************:5014
await in _initialize
(anonymous) @ useVapiCallWithDebug.js?t=*************:4977
(anonymous) @ useVapiCallWithDebug.js?t=*************:5542
(anonymous) @ useVapiCallWithDebug.js?t=*************:4828
chunk-CUTKQHYX.js?v=7a56b667:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
chunk-CUTKQHYX.js?v=7a56b667:559 Development mode: Using mock consistency check result
ThemeToggle.jsx:25 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ThemeToggle.jsx:25 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
ThemeToggle.jsx:27 🔐 [AuthContext] Found OAuth email: <EMAIL>
ThemeToggle.jsx:7 🔐 [AuthContext] Handling auth state for refresh...
ThemeToggle.jsx:7 SyncContext: Handling auth state for action: refresh
ThemeToggle.jsx:7 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
ThemeToggle.jsx:7 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
main.jsx:78 Auth state error details: Empty response from server
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
main.jsx:78 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (chunk-CUTKQHYX.js?v=7a56b667:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async initAuth (ThemeToggle.jsx:6:5)
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
chunk-CUTKQHYX.js?v=7a56b667:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
chunk-CUTKQHYX.js?v=7a56b667:559 Development mode: Using mock consistency check result
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
ThemeToggle.jsx:25 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ThemeToggle.jsx:25 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
ThemeToggle.jsx:27 🔐 [AuthContext] Found OAuth email: <EMAIL>
ThemeToggle.jsx:7 🔐 [AuthContext] Handling auth state for refresh...
ThemeToggle.jsx:7 SyncContext: Handling auth state for action: refresh
ThemeToggle.jsx:7 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
ThemeToggle.jsx:34 Auth state changed: INITIAL_SESSION
ThemeToggle.jsx:34 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ThemeToggle.jsx:34 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
ThemeToggle.jsx:34 Found OAuth email (auth change): <EMAIL>
ThemeToggle.jsx:7 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
invokePassiveEffectMountInDEV @ 565.js:18352
invokeEffectsInDev @ 565.js:19729
commitDoubleInvokeEffectsInDEV @ 565.js:19714
flushPassiveEffectsImpl @ 565.js:19531
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
main.jsx:78 Auth state error details: Empty response from server
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
invokePassiveEffectMountInDEV @ 565.js:18352
invokeEffectsInDev @ 565.js:19729
commitDoubleInvokeEffectsInDEV @ 565.js:19714
flushPassiveEffectsImpl @ 565.js:19531
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
main.jsx:78 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (chunk-CUTKQHYX.js?v=7a56b667:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async initAuth (ThemeToggle.jsx:6:5)
overrideMethod @ main.jsx:78
(anonymous) @ chunk-CUTKQHYX.js?v=7a56b667:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ ThemeToggle.jsx:7
await in initAuth
(anonymous) @ ThemeToggle.jsx:34
commitHookEffectListMount @ 565.js:16936
invokePassiveEffectMountInDEV @ 565.js:18352
invokeEffectsInDev @ 565.js:19729
commitDoubleInvokeEffectsInDEV @ 565.js:19714
flushPassiveEffectsImpl @ 565.js:19531
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
chunk-CUTKQHYX.js?v=7a56b667:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
chunk-CUTKQHYX.js?v=7a56b667:559 Development mode: Using mock consistency check result
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
370.js:102 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: 'damon', attorneySubdomain: 'damon', hasAssistantSelected: true}
370.js:126 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon.legalscout.net', embedUrl: 'https://damon.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon&loadFromSupabase=t…&assistantId=50e13a9e-22dd-4fe8-a03e-de627c5206c1', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon.legalscout.net'}
main.jsx:78 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: 'damon', attorneySubdomain: 'damon', hasAssistantSelected: true}
main.jsx:78 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon.legalscout.net', embedUrl: 'https://damon.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon&loadFromSupabase=t…&assistantId=50e13a9e-22dd-4fe8-a03e-de627c5206c1', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon.legalscout.net'}
ImportPreview.css:71 [VapiConfig] Using SECRET key for server operations (server)
ImportPreview.css:72 [VapiConfig] Server key: 6734febc...
vapiMcpService.js:263 [VapiMcpService] Got API key from vapiConfig module
vapiMcpService.js:279 [VapiMcpService] Using SECRET key for server operations: 6734febc...
vapiMcpService.js:19 [VapiMcpService] INFO: Connection attempt 1/3 {attempt: 1, maxAttempts: 3, apiKey: '6734f...'}
vapiMcpService.js:406 [VapiMcpService] Attempting MCP connection to: /api/vapi-mcp-server
vapiMcpService.js:407 [VapiMcpService] Using Streamable HTTP transport (recommended)
vapiMcpService.js:413 [VapiMcpService] Using local MCP proxy at: /api/vapi-mcp-server
vapiMcpService.js:417 [VapiMcpService] Successfully configured to use local MCP proxy
vapiMcpService.js:505 [VapiMcpService] Getting assistant: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
main.jsx:78 [VapiMcpService] Error getting assistant: Error: Not connected
    at node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6616:16
    at new Promise (<anonymous>)
    at Client.request (node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6613:12)
    at Client.callTool (node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6934:31)
    at VapiMcpService.getAssistant (src/services/vapiMcpService.js:568:42)
    at async AssistantDataService.syncAssistantNameFromVapi (src/services/assistantDataService.js?t=1750086017823:188:29)
    at async loadAssistantData (src/contexts/AssistantAwareContext.jsx?t=1750086017823:72:30)
overrideMethod @ main.jsx:78
getAssistant @ vapiMcpService.js:588
await in getAssistant
syncAssistantNameFromVapi @ recording-manager.js:188
await in syncAssistantNameFromVapi
loadAssistantData @ 370.js:72
await in loadAssistantData
(anonymous) @ 370.js:57
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
main.jsx:78 [VapiMcpService] Returning mock assistant due to error
overrideMethod @ main.jsx:78
getAssistant @ vapiMcpService.js:595
await in getAssistant
syncAssistantNameFromVapi @ recording-manager.js:188
await in syncAssistantNameFromVapi
loadAssistantData @ 370.js:72
await in loadAssistantData
(anonymous) @ 370.js:57
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
main.jsx:78 [AssistantDataService] Could not sync assistant name from Vapi: TypeError: assistantUIConfigService.updateAssistantConfig is not a function
    at AssistantDataService.syncAssistantNameFromVapi (src/services/assistantDataService.js?t=1750086017823:192:40)
    at async loadAssistantData (src/contexts/AssistantAwareContext.jsx?t=1750086017823:72:30)
overrideMethod @ main.jsx:78
syncAssistantNameFromVapi @ recording-manager.js:202
await in syncAssistantNameFromVapi
loadAssistantData @ 370.js:72
await in loadAssistantData
(anonymous) @ 370.js:57
commitHookEffectListMount @ 565.js:16936
commitPassiveMountOnFiber @ 565.js:18184
commitPassiveMountEffects_complete @ 565.js:18157
commitPassiveMountEffects_begin @ 565.js:18147
commitPassiveMountEffects @ 565.js:18137
flushPassiveEffectsImpl @ 565.js:19518
flushPassiveEffects @ 565.js:19475
(anonymous) @ 565.js:19356
workLoop @ 565.js:197
flushWork @ 565.js:176
performWorkUntilDeadline @ 565.js:384
370.js:74 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', subdomain: 'damon', assistantName: null}
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
ThemeToggle.jsx:34 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
ThemeToggle.jsx:6 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
ThemeToggle.jsx:34 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
999.js:21 [useStandaloneAttorney] Manager not ready, will retry...
main.jsx:78 [AttorneyProfileManager] Error loading attorney by id: {message: 'TypeError: Failed to fetch', details: 'TypeError: Failed to fetch', hint: '', code: ''}
overrideMethod @ main.jsx:78
loadAttorneyById @ useAttorneyProfile.js?t=*************:435
await in loadAttorneyById
(anonymous) @ useAttorneyProfile.js?t=*************:62
setTimeout
autoInitializeFromLocalStorage @ useAttorneyProfile.js?t=*************:56
AttorneyProfileManager @ useAttorneyProfile.js?t=*************:34
(anonymous) @ useAttorneyProfile.js?t=*************:1589
main.jsx:78 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: {message: 'TypeError: Failed to fetch', details: 'TypeError: Failed to fetch', hint: '', code: ''}
overrideMethod @ main.jsx:78
(anonymous) @ useAttorneyProfile.js?t=*************:73
setTimeout
autoInitializeFromLocalStorage @ useAttorneyProfile.js?t=*************:56
AttorneyProfileManager @ useAttorneyProfile.js?t=*************:34
(anonymous) @ useAttorneyProfile.js?t=*************:1589
useAttorneyProfile.js?t=*************:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
dashboard:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
@react-refresh:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
Navigated to http://localhost:5174/dashboard
client.ts:155 [vite] connected.
authService.js?t=*************:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
EnhancedAssistantDropdown.css?t=1749908513494:30 [VapiLoader] Starting Vapi SDK loading process
EnhancedAssistantDropdown.css?t=1749908513494:34 [VapiLoader] Attempting to import @vapi-ai/web package
AnalysisConfigTab.css:94 [VapiMcpService] Created clean fetch from iframe
AnalysisConfigTab.css:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
AttorneyProfileManager.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
TestToolExecution.jsx:341 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
TestToolExecution.jsx:31 [AttorneyProfileManager] Auto-initializing from localStorage
TestToolExecution.jsx:45 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ThemeContext.jsx:4 [ErrorBoundary] Creating React placeholder
ThemeContext.jsx:7 [ErrorBoundary] Adding createContext placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useState placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useEffect placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useLayoutEffect placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useRef placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useCallback placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useMemo placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding useContext placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding forwardRef placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding createElement placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding cloneElement placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding createRef placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding Component placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding PureComponent placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding Fragment placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding Children placeholder
ThemeContext.jsx:41 [ErrorBoundary] Adding isValidElement placeholder
ThemeContext.jsx:48 [ErrorBoundary] React polyfills applied
hookSettingsInjector.js:1 [ErrorBoundary] React polyfills applied
leaflet.css:457 🔥 [App.jsx] App component is starting!
leaflet.css:460 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
hookSettingsInjector.js:1 🔥 [App.jsx] App component is starting!
hookSettingsInjector.js:1 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
EnhancedAssistantDropdown.css?t=1749908513494:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
EnhancedAssistantDropdown.css?t=1749908513494:51 [VapiLoader] ✅ Vapi SDK validation successful
370.js:63 🔐 [AuthContext] Starting auth initialization...
370.js:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
authService.js?t=*************:31 🔧 [Supabase] Initializing real Supabase client...
authService.js?t=*************:33 ✅ [Supabase] Real client initialized successfully
370.js:356 Using real authentication in all environments
370.js:63 🔐 [AuthContext] Starting auth initialization...
370.js:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
370.js:356 Using real authentication in all environments
companionBubble.js:69 Loaded contentScript
370.js:149 Auth state changed: SIGNED_IN
370.js:151 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
370.js:152 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
370.js:161 Found OAuth email (auth change): <EMAIL>
370.js:170 AuthContext: Handling auth state change for event: SIGNED_IN
370.js:171 SyncContext: Handling auth state for action: login
370.js:171 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
syncHelpers.js?t=*************:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ syncHelpers.js?t=*************:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ 370.js:171
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:6263
_notifyAllSubscribers @ chunk-A5ED6EHL.js?v=7a56b667:6261
_recoverAndRefresh @ chunk-A5ED6EHL.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ chunk-A5ED6EHL.js?v=7a56b667:5023
await in _initialize
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4977
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:5542
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4828
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
hookSettingsInjector.js:1 Auth state error details: Empty response from server
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ 370.js:171
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:6263
_notifyAllSubscribers @ chunk-A5ED6EHL.js?v=7a56b667:6261
_recoverAndRefresh @ chunk-A5ED6EHL.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ chunk-A5ED6EHL.js?v=7a56b667:5023
await in _initialize
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4977
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:5542
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4828
hookSettingsInjector.js:1 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (syncHelpers.js?t=*************:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async Object.callback (370.js:171:38)
    at async chunk-A5ED6EHL.js?v=7a56b667:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (chunk-A5ED6EHL.js?v=7a56b667:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (chunk-A5ED6EHL.js?v=7a56b667:6204:9)
    at async SupabaseAuthClient._initialize (chunk-A5ED6EHL.js?v=7a56b667:5023:7)
    at async chunk-A5ED6EHL.js?v=7a56b667:4977:16
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
(anonymous) @ 370.js:171
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:6263
_notifyAllSubscribers @ chunk-A5ED6EHL.js?v=7a56b667:6261
_recoverAndRefresh @ chunk-A5ED6EHL.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ chunk-A5ED6EHL.js?v=7a56b667:5023
await in _initialize
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4977
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:5542
(anonymous) @ chunk-A5ED6EHL.js?v=7a56b667:4828
syncHelpers.js?t=*************:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
syncHelpers.js?t=*************:559 Development mode: Using mock consistency check result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
370.js:175 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
370.js:82 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
370.js:83 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
370.js:92 🔐 [AuthContext] Found OAuth email: <EMAIL>
370.js:101 🔐 [AuthContext] Handling auth state for refresh...
370.js:102 SyncContext: Handling auth state for action: refresh
370.js:102 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
370.js:82 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
370.js:83 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
370.js:92 🔐 [AuthContext] Found OAuth email: <EMAIL>
370.js:101 🔐 [AuthContext] Handling auth state for refresh...
370.js:102 SyncContext: Handling auth state for action: refresh
370.js:102 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
370.js:149 Auth state changed: INITIAL_SESSION
370.js:151 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
370.js:152 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
370.js:161 Found OAuth email (auth change): <EMAIL>
syncHelpers.js?t=*************:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ syncHelpers.js?t=*************:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
commitPassiveMountOnFiber @ ReactToastify.css:18184
commitPassiveMountEffects_complete @ ReactToastify.css:18157
commitPassiveMountEffects_begin @ ReactToastify.css:18147
commitPassiveMountEffects @ ReactToastify.css:18137
flushPassiveEffectsImpl @ ReactToastify.css:19518
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
hookSettingsInjector.js:1 Auth state error details: Empty response from server
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
commitPassiveMountOnFiber @ ReactToastify.css:18184
commitPassiveMountEffects_complete @ ReactToastify.css:18157
commitPassiveMountEffects_begin @ ReactToastify.css:18147
commitPassiveMountEffects @ ReactToastify.css:18137
flushPassiveEffectsImpl @ ReactToastify.css:19518
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
hookSettingsInjector.js:1 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (syncHelpers.js?t=*************:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async initAuth (370.js:109:34)
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
commitPassiveMountOnFiber @ ReactToastify.css:18184
commitPassiveMountEffects_complete @ ReactToastify.css:18157
commitPassiveMountEffects_begin @ ReactToastify.css:18147
commitPassiveMountEffects @ ReactToastify.css:18137
flushPassiveEffectsImpl @ ReactToastify.css:19518
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
syncHelpers.js?t=*************:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
syncHelpers.js?t=*************:559 Development mode: Using mock consistency check result
syncHelpers.js?t=*************:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ syncHelpers.js?t=*************:237
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
invokePassiveEffectMountInDEV @ ReactToastify.css:18352
invokeEffectsInDev @ ReactToastify.css:19729
commitDoubleInvokeEffectsInDEV @ ReactToastify.css:19714
flushPassiveEffectsImpl @ ReactToastify.css:19531
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
hookSettingsInjector.js:1 Auth state error details: Empty response from server
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:265
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
invokePassiveEffectMountInDEV @ ReactToastify.css:18352
invokeEffectsInDev @ ReactToastify.css:19729
commitDoubleInvokeEffectsInDEV @ ReactToastify.css:19714
flushPassiveEffectsImpl @ ReactToastify.css:19531
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
hookSettingsInjector.js:1 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (syncHelpers.js?t=*************:266:17)
    at async Object.handleAuthState (react-icons_md.js?v=7a56b667:136:24)
    at async initAuth (370.js:109:34)
overrideMethod @ hookSettingsInjector.js:1
(anonymous) @ syncHelpers.js?t=*************:273
await in (anonymous)
(anonymous) @ react-icons_md.js?v=7a56b667:136
initAuth @ 370.js:102
await in initAuth
(anonymous) @ 370.js:143
commitHookEffectListMount @ ReactToastify.css:16936
invokePassiveEffectMountInDEV @ ReactToastify.css:18352
invokeEffectsInDev @ ReactToastify.css:19729
commitDoubleInvokeEffectsInDEV @ ReactToastify.css:19714
flushPassiveEffectsImpl @ ReactToastify.css:19531
flushPassiveEffects @ ReactToastify.css:19475
(anonymous) @ ReactToastify.css:19356
workLoop @ ReactToastify.css:197
flushWork @ ReactToastify.css:176
performWorkUntilDeadline @ ReactToastify.css:384
syncHelpers.js?t=*************:276 Using client-side fallback for auth state management
react-icons_md.js?v=7a56b667:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
syncHelpers.js?t=*************:559 Development mode: Using mock consistency check result
TestToolExecution.jsx:341 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
370.js:70 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
370.js:70 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
370.js:110 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
370.js:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
370.js:110 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
370.js:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
TestToolExecution.jsx:57 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d'}
TestToolExecution.jsx:341 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
TestToolExecution.jsx:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
companionBubble.js:28 received intentional event
leaflet.css:492 🔐 [App] Starting Google sign-in...
authService.js?t=*************:191 🔐 [Supabase] Starting Google sign-in with emergency auth...
authService.js?t=*************:87 🚨 [EmergencyAuth] Starting direct Google OAuth...
authService.js?t=*************:88 🚨 [EmergencyAuth] Current origin: http://localhost:5174
authService.js?t=*************:94 🚨 [EmergencyAuth] Redirect URL: http://localhost:5174/auth/callback
authService.js?t=*************:102 🚨 [EmergencyAuth] Redirecting to: https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/authorize?provider=google&redirect_to=http%3A%2F%2Flocalhost%3A5174%2Fauth%2Fcallback&apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
Navigated to https://accounts.google.com/o/oauth2/v2/auth?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU&client_id=************-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com&redirect_to=http%3A%2F%2Flocalhost%3A5174%2Fauth%2Fcallback&redirect_uri=https%3A%2F%2Futopqxsvudgrtiwenlzl.supabase.co%2Fauth%2Fv1%2Fcallback&response_type=code&scope=email+profile&state=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************.69F64n9aqr3dJKuHKB-Ai1WK4JL7kS2ryV32IBq77BI

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
ThemeToggle.jsx:22 Loaded contentScript
TestComponent.jsx:32 ChatGPT Assistant content script loaded
ThemeToggle.jsx:14 received intentional event
Navigated to http://localhost:5174/auth/callback
callback:26 🚀 [LegalScout] Initializing environment...
callback:48 ✅ [LegalScout] Environment initialized
callback:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
supabase.js?t=*************:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=*************:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=*************:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=*************:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 🔐 [AuthCallback] Starting OAuth callback handling...
AuthCallback.jsx:34 🔐 [AuthCallback] Checking for OAuth tokens...
AuthCallback.jsx:35 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1m3CL9TMw4YmgcOWEqyu_iCsPUpGYDJlCYWt4omFXQs&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=mulbcgry4lfa&token_type=bearer
AuthCallback.jsx:36 🔐 [AuthCallback] Query params: 
AuthCallback.jsx:37 🔐 [AuthCallback] Access token found: true
AuthCallback.jsx:70 🔐 [AuthCallback] Found OAuth tokens in URL
AuthCallback.jsx:91 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AuthCallback.jsx:94 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js?t=*************:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js?t=*************:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js?t=*************:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js?t=*************:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js?t=*************:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js?t=*************:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js?t=*************:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:55 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:624 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=*************:31 🔧 [Supabase] Initializing real Supabase client...
supabase.js?t=*************:33 ✅ [Supabase] Real client initialized successfully
AuthContext.jsx:485 Using real authentication in all environments
AuthCallback.jsx:15 🔐 [AuthCallback] Starting OAuth callback handling...
AuthCallback.jsx:34 🔐 [AuthCallback] Checking for OAuth tokens...
AuthCallback.jsx:35 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1m3CL9TMw4YmgcOWEqyu_iCsPUpGYDJlCYWt4omFXQs&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=mulbcgry4lfa&token_type=bearer
AuthCallback.jsx:36 🔐 [AuthCallback] Query params: 
AuthCallback.jsx:37 🔐 [AuthCallback] Access token found: true
AuthCallback.jsx:70 🔐 [AuthCallback] Found OAuth tokens in URL
AuthCallback.jsx:91 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AuthCallback.jsx:94 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js?t=*************:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js?t=*************:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js?t=*************:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js?t=*************:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js?t=*************:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js?t=*************:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js?t=*************:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:55 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:624 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
index.ts:5 Loaded contentScript
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:639 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:649 ✅ Supabase configured successfully
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:669 🔍 [App] Subdomain detected: default
App.jsx:677 🏠 [App] Localhost detected - treating as main domain
App.jsx:732 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:639 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:649 ✅ Supabase configured successfully
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:669 🔍 [App] Subdomain detected: default
App.jsx:677 🏠 [App] Localhost detected - treating as main domain
App.jsx:732 🏁 [App] Initialization complete
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
AssistantAwareContext.jsx:52 ✅ [AssistantAwareContext] Loading assistant data for: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
authProfileFixer.js?t=*************:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
authProfileFixer.js?t=*************:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
authProfileFixer.js?t=*************:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js?t=*************:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js?t=*************:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthCallback.jsx:96 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AuthCallback.jsx:100 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AuthCallback.jsx:109 💾 [AuthCallback] Attorney profile stored in localStorage
AuthCallback.jsx:122 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
authProfileFixer.js?t=*************:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js?t=*************:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js?t=*************:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthCallback.jsx:96 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T14:58:55.880332+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AuthCallback.jsx:100 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AuthCallback.jsx:109 💾 [AuthCallback] Attorney profile stored in localStorage
AuthCallback.jsx:122 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5174/dashboard
callback#access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1m3CL9TMw4YmgcOWEqyu_iCsPUpGYDJlCYWt4omFXQs&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=mulbcgry4lfa&token_type=bearer:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
AttorneyProfileManager.js?t=*************:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js?t=*************:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AuthContext.jsx:190 Auth state changed: SIGNED_IN
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:224 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=7a56b667:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=7a56b667:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:226:34)
    at async @supabase_supabase-js.js?v=7a56b667:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=7a56b667:6268:7)
    at async @supabase_supabase-js.js?v=7a56b667:5018:13
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5018
setTimeout
_initialize @ @supabase_supabase-js.js?v=7a56b667:5014
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js?t=1749946872708:559 Development mode: Using mock consistency check result
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:143:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js?t=1749946872708:559 Development mode: Using mock consistency check result
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:190 Auth state changed: INITIAL_SESSION
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:143:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js?t=1749946872708:559 Development mode: Using mock consistency check result
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: 'damon', attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:143 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon.legalscout.net', embedUrl: 'https://damon.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon&loadFromSupabase=t…&assistantId=50e13a9e-22dd-4fe8-a03e-de627c5206c1', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon.legalscout.net'}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', assistantSubdomain: 'damon', attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:143 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon.legalscout.net', embedUrl: 'https://damon.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon&loadFromSupabase=t…&assistantId=50e13a9e-22dd-4fe8-a03e-de627c5206c1', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon.legalscout.net'}
vapiConfig.js:71 [VapiConfig] Using SECRET key for server operations (server)
vapiConfig.js:72 [VapiConfig] Server key: 6734febc...
vapiMcpService.js:263 [VapiMcpService] Got API key from vapiConfig module
vapiMcpService.js:279 [VapiMcpService] Using SECRET key for server operations: 6734febc...
vapiMcpService.js:19 [VapiMcpService] INFO: Connection attempt 1/3 {attempt: 1, maxAttempts: 3, apiKey: '6734f...'}
vapiMcpService.js:406 [VapiMcpService] Attempting MCP connection to: /api/vapi-mcp-server
vapiMcpService.js:407 [VapiMcpService] Using Streamable HTTP transport (recommended)
vapiMcpService.js:413 [VapiMcpService] Using local MCP proxy at: /api/vapi-mcp-server
vapiMcpService.js:417 [VapiMcpService] Successfully configured to use local MCP proxy
vapiMcpService.js:505 [VapiMcpService] Getting assistant: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
vapiMcpService.js:588 [VapiMcpService] Error getting assistant: Error: Not connected
    at node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6616:16
    at new Promise (<anonymous>)
    at Client.request (node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6613:12)
    at Client.callTool (node_modules/.vite/deps/@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6934:31)
    at VapiMcpService.getAssistant (src/services/vapiMcpService.js:568:42)
    at async AssistantDataService.syncAssistantNameFromVapi (src/services/assistantDataService.js?t=1750086017823:188:29)
    at async loadAssistantData (src/contexts/AssistantAwareContext.jsx?t=1750086017823:72:30)
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:588
await in getAssistant
syncAssistantNameFromVapi @ assistantDataService.js?t=1750086017823:188
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:77
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:53
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
vapiMcpService.js:595 [VapiMcpService] Returning mock assistant due to error
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:595
await in getAssistant
syncAssistantNameFromVapi @ assistantDataService.js?t=1750086017823:188
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:77
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:53
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
assistantDataService.js?t=1750086017823:202 [AssistantDataService] Could not sync assistant name from Vapi: TypeError: assistantUIConfigService.updateAssistantConfig is not a function
    at AssistantDataService.syncAssistantNameFromVapi (src/services/assistantDataService.js?t=1750086017823:192:40)
    at async loadAssistantData (src/contexts/AssistantAwareContext.jsx?t=1750086017823:72:30)
overrideMethod @ hook.js:608
syncAssistantNameFromVapi @ assistantDataService.js?t=1750086017823:202
await in syncAssistantNameFromVapi
loadAssistantData @ AssistantAwareContext.jsx:77
await in loadAssistantData
(anonymous) @ AssistantAwareContext.jsx:53
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
AssistantAwareContext.jsx:80 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '50e13a9e-22dd-4fe8-a03e-de627c5206c1', subdomain: 'damon', assistantName: null}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js?t=*************:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [useStandaloneAttorney] Manager not ready, will retry...
dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
dashboard:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
supabase.js?t=*************:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
Navigated to http://localhost:5174/dashboard
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js?t=*************:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=*************:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js?t=*************:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=*************:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js?t=*************:31 🔧 [Supabase] Initializing real Supabase client...
supabase.js?t=*************:33 ✅ [Supabase] Real client initialized successfully
AuthContext.jsx:485 Using real authentication in all environments
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
index.ts:5 Loaded contentScript
AuthContext.jsx:190 Auth state changed: SIGNED_IN
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:224 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=7a56b667:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=7a56b667:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:226:34)
    at async @supabase_supabase-js.js?v=7a56b667:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=7a56b667:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=7a56b667:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=7a56b667:5023:7)
    at async @supabase_supabase-js.js?v=7a56b667:4977:16
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:226
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=7a56b667:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=7a56b667:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=7a56b667:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4977
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:5542
(anonymous) @ @supabase_supabase-js.js?v=7a56b667:4828
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js?t=1749946872708:559 Development mode: Using mock consistency check result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AuthContext.jsx:231 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:190 Auth state changed: INITIAL_SESSION
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:143:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=7a56b667:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=7a56b667:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=7a56b667:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=7a56b667:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js?t=1749946872708:559 Development mode: Using mock consistency check result
useSyncTools.js?t=1749946872708:237 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
(anonymous) @ useSyncTools.js?t=1749946872708:237
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:265 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:265
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:273 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js?t=1749946872708:266:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:143:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js?t=1749946872708:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:134
await in initAuth
(anonymous) @ AuthContext.jsx:182
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=7a56b667:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=7a56b667:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=7a56b667:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=7a56b667:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=7a56b667:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=7a56b667:19475
(anonymous) @ chunk-Q72EVS5P.js?v=7a56b667:19356
workLoop @ chunk-Q72EVS5P.js?v=7a56b667:197
flushWork @ chunk-Q72EVS5P.js?v=7a56b667:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=7a56b667:384
useSyncTools.js?t=1749946872708:276 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
