/**
 * Browser Test Data Creation
 * 
 * Copy and paste this code into your browser console while logged into the dashboard
 * to create test consultation data and verify the Briefs page is working.
 */

// Function to create test consultations in the browser
async function createTestConsultations() {
  console.log('🧪 Creating Test Consultations in Browser...\n');

  try {
    // Check if we're in the right environment
    if (typeof window === 'undefined') {
      console.error('❌ This script must be run in a browser console');
      return;
    }

    // Try to get supabase from window or import
    let supabase;
    if (window.supabase) {
      supabase = window.supabase;
    } else if (window.supabaseClient) {
      supabase = window.supabaseClient;
    } else {
      console.error('❌ Supabase client not found. Make sure you\'re on the dashboard page.');
      console.log('💡 Try running this after the page has fully loaded.');
      return;
    }

    // 1. Check current user
    console.log('1️⃣ Checking current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ No authenticated user found. Please log in first.');
      return;
    }
    
    console.log('✅ Current user:', user.email);

    // 2. Find attorney record
    console.log('\n2️⃣ Finding attorney record...');
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();

    if (attorneyError) {
      console.error('❌ Error finding attorney:', attorneyError);
      console.log('Trying alternative emails...');
      
      // Try alternative emails
      const altEmails = ['<EMAIL>', '<EMAIL>'];
      let foundAttorney = null;
      
      for (const email of altEmails) {
        const { data: altAttorney, error: altError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('email', email)
          .single();
        
        if (!altError && altAttorney) {
          foundAttorney = altAttorney;
          console.log(`✅ Found attorney with email: ${email}`);
          break;
        }
      }
      
      if (!foundAttorney) {
        console.error('❌ No attorney record found');
        return;
      }
      
      var currentAttorney = foundAttorney;
    } else {
      var currentAttorney = attorney;
      console.log('✅ Attorney found:', currentAttorney.name);
    }

    console.log('Attorney ID:', currentAttorney.id);
    console.log('Assistant ID:', currentAttorney.vapi_assistant_id);

    // 3. Create test consultations
    console.log('\n3️⃣ Creating test consultations...');
    
    const testConsultations = [
      {
        attorney_id: currentAttorney.id,
        client_name: 'John Smith',
        client_email: '<EMAIL>',
        client_phone: '+1234567890',
        summary: 'Client needs help with a contract dispute involving a service agreement. The other party is not fulfilling their obligations.',
        transcript: 'Assistant: Hello! I\'m Scout, your legal assistant. How can I help you today?\nUser: I need help with a contract dispute.\nAssistant: I can help you with that. Can you tell me more about the contract dispute?',
        duration: 300,
        practice_area: 'Contract Law',
        location: 'New York, NY',
        location_data: { address: 'New York, NY', state: 'NY' },
        metadata: {
          call_id: `test-call-${Date.now()}`,
          assistant_id: currentAttorney.vapi_assistant_id,
          test_data: true,
          urgency: 'Medium'
        },
        status: 'new'
      },
      {
        attorney_id: currentAttorney.id,
        client_name: 'Sarah Johnson',
        client_email: '<EMAIL>',
        client_phone: '+1987654321',
        summary: 'Personal injury case - client was injured in a car accident and needs legal representation.',
        transcript: 'Assistant: Hello! How can I assist you today?\nUser: I was in a car accident and got injured.\nAssistant: I\'m sorry to hear that. Can you tell me more about what happened?',
        duration: 450,
        practice_area: 'Personal Injury',
        location: 'Los Angeles, CA',
        location_data: { address: 'Los Angeles, CA', state: 'CA' },
        metadata: {
          call_id: `test-call-${Date.now() + 1}`,
          assistant_id: currentAttorney.vapi_assistant_id,
          test_data: true,
          urgency: 'High'
        },
        status: 'follow-up'
      },
      {
        attorney_id: currentAttorney.id,
        client_name: 'Mike Davis',
        client_email: '<EMAIL>',
        client_phone: '+1555123456',
        summary: 'Employment law issue - client was wrongfully terminated from their job.',
        transcript: 'Assistant: Welcome! What legal matter can I help you with?\nUser: I think I was wrongfully terminated.\nAssistant: That sounds serious. Can you provide more details about your termination?',
        duration: 600,
        practice_area: 'Employment Law',
        location: 'Chicago, IL',
        location_data: { address: 'Chicago, IL', state: 'IL' },
        metadata: {
          call_id: `test-call-${Date.now() + 2}`,
          assistant_id: currentAttorney.vapi_assistant_id,
          test_data: true,
          urgency: 'Medium'
        },
        status: 'new'
      }
    ];

    let successCount = 0;
    let errorCount = 0;

    for (const consultation of testConsultations) {
      try {
        const { data: newConsultation, error: insertError } = await supabase
          .from('consultations')
          .insert(consultation)
          .select()
          .single();

        if (insertError) {
          console.error(`❌ Error creating consultation for ${consultation.client_name}:`, insertError);
          errorCount++;
        } else {
          console.log(`✅ Created consultation: ${newConsultation.client_name} (ID: ${newConsultation.id})`);
          successCount++;
        }
      } catch (error) {
        console.error(`❌ Exception creating consultation for ${consultation.client_name}:`, error);
        errorCount++;
      }
    }

    // 4. Verify the consultations were created
    console.log('\n4️⃣ Verifying created consultations...');
    
    const { data: verifyConsultations, error: verifyError } = await supabase
      .from('consultations')
      .select('id, client_name, status, created_at')
      .eq('attorney_id', currentAttorney.id)
      .order('created_at', { ascending: false });

    if (verifyError) {
      console.error('❌ Error verifying consultations:', verifyError);
    } else {
      console.log(`✅ Total consultations for ${currentAttorney.email}: ${verifyConsultations.length}`);
      verifyConsultations.forEach((c, index) => {
        console.log(`  ${index + 1}. ${c.client_name} (${c.status}) - ${c.created_at}`);
      });
    }

    // 5. Test real-time updates
    console.log('\n5️⃣ Setting up real-time monitoring...');
    
    const channel = supabase
      .channel('test-consultations-monitor')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'consultations',
          filter: `attorney_id=eq.${currentAttorney.id}`
        },
        (payload) => {
          console.log('🔔 Real-time consultation update:', payload);
        }
      )
      .subscribe((status) => {
        console.log('📡 Real-time subscription status:', status);
      });

    // Store the channel globally so you can unsubscribe later
    window.testConsultationChannel = channel;

    console.log('\n📋 TEST RESULTS:');
    console.log('='.repeat(50));
    console.log(`✅ Consultations created: ${successCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`📊 Total consultations: ${verifyConsultations?.length || 0}`);
    console.log(`👤 Attorney: ${currentAttorney.name} (${currentAttorney.email})`);
    console.log(`🆔 Attorney ID: ${currentAttorney.id}`);
    
    if (successCount > 0) {
      console.log('\n🎉 SUCCESS! Test consultations created.');
      console.log('💡 Now check your Briefs page - it should show the new consultations.');
      console.log('🔄 If they don\'t appear immediately, try refreshing the page.');
    } else {
      console.log('\n❌ No consultations were created successfully.');
      console.log('💡 Check the error messages above for details.');
    }

    console.log('\n🧹 To clean up test data later, run: cleanupTestConsultations()');
    console.log('🔌 To stop real-time monitoring, run: stopTestMonitoring()');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Function to clean up test data
async function cleanupTestConsultations() {
  console.log('🧹 Cleaning up test consultations...');
  
  try {
    const supabase = window.supabase || window.supabaseClient;
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }

    const { data, error } = await supabase
      .from('consultations')
      .delete()
      .eq('metadata->test_data', true);

    if (error) {
      console.error('❌ Error cleaning up test data:', error);
    } else {
      console.log('✅ Test consultations cleaned up');
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Function to stop real-time monitoring
async function stopTestMonitoring() {
  console.log('🔌 Stopping real-time monitoring...');
  
  try {
    const supabase = window.supabase || window.supabaseClient;
    if (window.testConsultationChannel && supabase) {
      await supabase.removeChannel(window.testConsultationChannel);
      window.testConsultationChannel = null;
      console.log('✅ Real-time monitoring stopped');
    } else {
      console.log('ℹ️ No active monitoring to stop');
    }
  } catch (error) {
    console.error('❌ Error stopping monitoring:', error);
  }
}

// Instructions
console.log(`
🔧 BROWSER CONSOLE INSTRUCTIONS:
================================

1. Make sure you're logged into the LegalScout dashboard
2. Open Developer Tools (F12) and go to Console tab
3. Copy and paste this entire script
4. Run: createTestConsultations()

Available functions:
- createTestConsultations() - Create test data
- cleanupTestConsultations() - Remove test data
- stopTestMonitoring() - Stop real-time monitoring

This will create test consultations and help verify if your Briefs page is working correctly.
`);

// Auto-run if we detect we're in the right environment
if (typeof window !== 'undefined' && window.location && window.location.pathname.includes('dashboard')) {
  console.log('🚀 Auto-running test data creation...');
  createTestConsultations();
}
