/**
 * Mock implementation of LayoutGroupContext
 * 
 * This file provides a direct import for LayoutGroupContext to prevent errors
 * when Framer Motion tries to use it.
 */

import React from 'react';

// Create a mock LayoutGroupContext
export const LayoutGroupContext = {
  Provider: function(props) { return props.children || null; },
  Consumer: function(props) { return props.children ? props.children({}) : null; },
  displayName: 'LayoutGroupContext'
};

// Make it available globally
if (typeof window !== 'undefined') {
  window.LayoutGroupContext = LayoutGroupContext;
}

export default LayoutGroupContext;
