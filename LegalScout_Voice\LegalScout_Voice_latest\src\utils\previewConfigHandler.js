/**
 * Preview Configuration Handler
 *
 * This utility handles the different preview configurations needed for:
 * 1. Demo page preview (uses default assistant with template overrides)
 * 2. Dashboard preview for signed-in attorneys (uses attorney-specific assistant)
 */

import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

/**
 * Creates configuration for the demo page preview
 * @param {Object} templateConfig - Basic template configuration
 * @returns {Object} - Configuration for demo preview
 */
export const createDemoPreviewConfig = (templateConfig) => {
  const {
    firmName = 'Your Law Firm',
    attorneyName = 'Your Name',
    welcomeMessage = "Hello! I'm <PERSON>, your legal assistant. How can I help you today?",
    informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",
    practiceAreas = [],
    primaryColor = '#4B74AA',
    secondaryColor = '#2C3E50',
    buttonColor = '#D85722',
    backgroundColor = '#1a1a1a',
    backgroundOpacity = 0.9,
    logoUrl = '',
    buttonImageUrl = '',
    ...rest
  } = templateConfig;

  // Create assistant overrides for the demo preview
  // This uses the default assistant ID but overrides instructions
  const assistantOverrides = {
    // First Message override - this is the message that will be spoken first
    firstMessage: welcomeMessage,
    // First Message Mode - ensure the assistant speaks first
    firstMessageMode: "assistant-speaks-first",
    // Ensure the first message is not interrupted
    firstMessageInterruptionsEnabled: false,
    // Model configuration with system prompt
    model: {
      provider: "anthropic",
      model: "claude-3-sonnet-20240229",
      messages: [
        {
          role: "system",
          content: `You are a legal assistant for ${firmName}.
          Follow the default instructions for being a helpful legal assistant, but also incorporate these specific instructions:
          1. Introduce yourself as Scout from ${firmName}
          2. Gather information about the potential client's legal situation
          3. Be empathetic and professional
          4. Focus on understanding their needs before making recommendations`
        }
      ]
    },
    // Ensure recording is enabled
    artifactPlan: {
      recordingEnabled: true
    }
  };

  return {
    // Basic preview configuration
    previewConfig: {
      firmName,
      attorneyName,
      welcomeMessage,
      informationGathering,
      practiceAreas,
      primaryColor,
      secondaryColor,
      buttonColor,
      backgroundColor,
      backgroundOpacity,
      logoUrl,
      buttonImageUrl,
      ...rest
    },
    // Vapi configuration
    vapiConfig: {
      assistantId: DEFAULT_ASSISTANT_ID,
      assistantOverrides
    }
  };
};

/**
 * Creates configuration for the attorney dashboard preview
 * @param {Object} attorneyData - Attorney data from Supabase
 * @returns {Object} - Configuration for attorney dashboard preview
 */
export const createAttorneyPreviewConfig = (attorneyData) => {
  if (!attorneyData) {
    console.warn('No attorney data provided to createAttorneyPreviewConfig');
    return createDemoPreviewConfig({});
  }

  // Check if attorney has a valid Vapi assistant ID
  if (!attorneyData.vapi_assistant_id) {
    console.warn('Attorney has no Vapi assistant ID');
  }

  // Map database fields to preview configuration
  // Handle both formats: direct Supabase data (firm_name) and processed data (firmName)
  const previewConfig = {
    firmName: attorneyData.firmName || attorneyData.firm_name || 'Your Law Firm',
    titleText: attorneyData.titleText || attorneyData.title_text || attorneyData.firmName || attorneyData.firm_name || '',
    attorneyName: attorneyData.attorneyName || attorneyData.name || 'Your Name',
    welcomeMessage: attorneyData.welcomeMessage || attorneyData.welcome_message || "Hello! I'm Scout, your legal assistant. How can I help you today?",
    informationGathering: attorneyData.informationGathering || attorneyData.information_gathering || "Tell me about your situation, and I'll help find the right solution for you.",
    practiceAreas: attorneyData.practiceAreas || attorneyData.practice_areas || [],
    state: attorneyData.state || '',
    practiceDescription: attorneyData.practiceDescription || attorneyData.practice_description || 'Your AI legal assistant is ready to help',
    primaryColor: attorneyData.primaryColor || attorneyData.primary_color || '#4B74AA',
    secondaryColor: attorneyData.secondaryColor || attorneyData.secondary_color || '#2C3E50',
    buttonColor: attorneyData.buttonColor || attorneyData.button_color || '#D85722',
    backgroundColor: attorneyData.backgroundColor || attorneyData.background_color || '#1a1a1a',
    backgroundOpacity: attorneyData.backgroundOpacity !== undefined ? attorneyData.backgroundOpacity : (attorneyData.background_opacity || 0.9),
    buttonText: attorneyData.buttonText || attorneyData.button_text || 'Start Consultation',
    buttonOpacity: attorneyData.buttonOpacity !== undefined ? attorneyData.buttonOpacity : (attorneyData.button_opacity || 1),
    practiceAreaBackgroundOpacity: attorneyData.practiceAreaBackgroundOpacity !== undefined ? attorneyData.practiceAreaBackgroundOpacity : (attorneyData.practice_area_background_opacity || 0.1),
    textBackgroundColor: attorneyData.textBackgroundColor || attorneyData.text_background_color || '#634C38',
    logoUrl: attorneyData.logoUrl || attorneyData.logo_url || '',
    buttonImageUrl: attorneyData.buttonImageUrl || attorneyData.button_image || attorneyData.profile_image || '',
    vapiInstructions: attorneyData.vapiInstructions || attorneyData.vapi_instructions || '',
    vapiContext: attorneyData.vapiContext || attorneyData.vapi_context || '',
    theme: attorneyData.theme || 'dark',
    // Voice and model configuration
    voiceId: attorneyData.voiceId || attorneyData.voice_id || 'sarah',
    voiceProvider: attorneyData.voiceProvider || attorneyData.voice_provider || '11labs',
    aiModel: attorneyData.aiModel || attorneyData.ai_model || 'gpt-4o',
    // Add custom fields configuration
    customFields: attorneyData.customFields || attorneyData.custom_fields || [],
    summaryPrompt: attorneyData.summaryPrompt || attorneyData.summary_prompt || '',
    structuredDataPrompt: attorneyData.structuredDataPrompt || attorneyData.structured_data_prompt || '',
    structuredDataSchema: attorneyData.structuredDataSchema || attorneyData.structured_data_schema || null,
    // Add Vapi assistant ID to preview config for reference
    vapiAssistantId: attorneyData.vapiAssistantId || attorneyData.vapi_assistant_id || null
  };

  // Create Vapi configuration - for dashboard preview, we should NEVER use fallbacks
  // The dashboard should always use the attorney's specific assistant ID
  const vapiConfig = {
    // CRITICAL: Check for mock assistant IDs and reject them
    assistantId: ((attorneyData.vapiAssistantId || attorneyData.vapi_assistant_id) && !(attorneyData.vapiAssistantId || attorneyData.vapi_assistant_id).includes('mock'))
      ? (attorneyData.vapiAssistantId || attorneyData.vapi_assistant_id)
      : null,
    // Add assistant overrides with proper Vapi API format
    assistantOverrides: {
      // Include essential configuration
      firstMessage: (attorneyData.welcomeMessage || attorneyData.welcome_message) ||
        `Hello! I'm Scout from ${attorneyData.firmName || attorneyData.firm_name || 'our law firm'}. How can I help you today?`,
      firstMessageMode: 'assistant-speaks-first',
      // Voice configuration
      voice: {
        provider: attorneyData.voiceProvider || attorneyData.voice_provider || '11labs',
        voiceId: attorneyData.voiceId || attorneyData.voice_id || 'sarah'
      },
      // Model configuration with system prompt in messages array (correct Vapi format)
      model: {
        provider: 'openai',
        model: attorneyData.aiModel || attorneyData.ai_model || 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: (attorneyData.vapiInstructions || attorneyData.vapi_instructions) ||
              `You are a legal assistant for ${attorneyData.firmName || attorneyData.firm_name || 'this law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`
          }
        ]
      },
      // Add transcriber configuration
      transcriber: {
        provider: 'deepgram',
        model: 'nova-2'
      }
    }
  };

  // Log the assistant ID being used for the preview
  console.log('Preview configuration using Vapi assistant ID:', vapiConfig.assistantId);

  // Warn if mock assistant ID was detected and blocked
  const assistantId = attorneyData.vapiAssistantId || attorneyData.vapi_assistant_id;
  if (assistantId && assistantId.includes('mock')) {
    console.warn('🚨 Mock assistant ID detected and blocked from preview:', assistantId);
    console.log('🔄 Preview will not use voice functionality until real assistant ID is available');
  }

  return {
    previewConfig,
    vapiConfig
  };
};

/**
 * Synchronizes attorney configuration with Vapi assistant
 * @param {Object} attorneyData - Attorney data from Supabase
 * @param {Object} vapiAssistantService - Vapi assistant service instance
 * @returns {Promise<Object>} - Updated attorney data with assistant ID
 */
export const syncAttorneyWithVapiAssistant = async (attorneyData, vapiAssistantService) => {
  if (!attorneyData) {
    throw new Error('Attorney data is required');
  }

  // Import the logger utility
  const { createLogger } = await import('../utils/loggerUtils');
  const logger = createLogger('syncAttorneyWithVapiAssistant');

  // Import the field mapping utility
  const { attorneyToVapi, vapiToAttorney } = await import('../utils/vapiFieldMapping');

  try {
    let assistantId = attorneyData.vapi_assistant_id;
    let assistantExists = false;

    // If attorney has an assistant ID, verify it exists in Vapi
    if (assistantId) {
      logger.info(`Verifying Vapi assistant exists: ${assistantId}`);
      try {
        // Try MCP service first
        let assistant = null;

        try {
          // Import vapiMcpService
          const { vapiMcpService } = await import('../services/vapiMcpService');

          // Ensure connection
          await vapiMcpService.ensureConnection();

          // Get assistant
          assistant = await vapiMcpService.getAssistant(assistantId);

          if (assistant) {
            logger.info(`Vapi assistant found using MCP service: ${assistantId}`);
            assistantExists = true;
          }
        } catch (mcpError) {
          logger.warn(`MCP service failed to get assistant: ${mcpError.message}, trying vapiAssistantService`);
        }

        // If MCP failed, try vapiAssistantService
        if (!assistant) {
          assistant = await vapiAssistantService.getAssistant(assistantId);
          assistantExists = !!assistant;

          if (assistantExists) {
            logger.info(`Vapi assistant found using vapiAssistantService: ${assistantId}`);
          } else {
            logger.warn(`Vapi assistant not found: ${assistantId}`);
          }
        }
      } catch (verifyError) {
        logger.error(`Error verifying Vapi assistant: ${verifyError.message}`);
        assistantExists = false;
      }
    }

    // If attorney doesn't have an assistant ID or it doesn't exist in Vapi, create a new assistant
    if (!assistantId || !assistantExists) {
      logger.info(`Creating new Vapi assistant for attorney: ${attorneyData.firm_name}`);

      // Convert attorney data to Vapi format
      const assistantConfig = attorneyToVapi(attorneyData);

      // Try MCP service first
      let assistant = null;

      try {
        // Import vapiMcpService
        const { vapiMcpService } = await import('../services/vapiMcpService');

        // Ensure connection
        await vapiMcpService.ensureConnection();

        // Create assistant
        assistant = await vapiMcpService.createAssistant(assistantConfig);

        if (assistant) {
          logger.info(`Created assistant using MCP service with ID ${assistant.id}`);
        }
      } catch (mcpError) {
        logger.warn(`MCP service failed to create assistant: ${mcpError.message}, trying vapiAssistantService`);
      }

      // If MCP failed, try vapiAssistantService
      if (!assistant) {
        assistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);

        if (assistant) {
          logger.info(`Created assistant using vapiAssistantService with ID ${assistant.id}`);
        }
      }

      // If we still don't have an assistant, throw error
      if (!assistant || !assistant.id) {
        throw new Error('Failed to create assistant - no valid assistant ID returned');
      }

      assistantId = assistant.id;
      logger.info(`Successfully created new Vapi assistant: ${assistantId}`);

      // Return updated attorney data with new assistant ID
      return {
        ...attorneyData,
        vapi_assistant_id: assistantId
      };
    } else {
      // Update existing assistant with current attorney configuration
      logger.info(`Updating existing Vapi assistant: ${assistantId}`);

      // Convert attorney data to Vapi format
      const assistantConfig = attorneyToVapi(attorneyData);

      // Try MCP service first
      let updatedAssistant = null;

      try {
        // Import vapiMcpService
        const { vapiMcpService } = await import('../services/vapiMcpService');

        // Ensure connection
        await vapiMcpService.ensureConnection();

        // Update assistant
        updatedAssistant = await vapiMcpService.updateAssistant(assistantId, assistantConfig);

        if (updatedAssistant) {
          logger.info(`Updated assistant using MCP service: ${assistantId}`);
        }
      } catch (mcpError) {
        logger.warn(`MCP service failed to update assistant: ${mcpError.message}, trying vapiAssistantService`);
      }

      // If MCP failed, try vapiAssistantService
      if (!updatedAssistant) {
        updatedAssistant = await vapiAssistantService.updateAssistantConfiguration(assistantId, attorneyData);

        if (updatedAssistant) {
          logger.info(`Updated assistant using vapiAssistantService: ${assistantId}`);
        }
      }

      // If we still don't have an updated assistant, log warning but continue
      if (!updatedAssistant) {
        logger.warn(`No updated assistant data returned, using original assistant ID: ${assistantId}`);
        updatedAssistant = { id: assistantId };
      }

      // Return attorney data with any updated fields from the assistant
      return {
        ...attorneyData,
        // Ensure we have the latest assistant ID
        vapi_assistant_id: updatedAssistant.id || assistantId
      };
    }
  } catch (error) {
    logger.error(`Error syncing attorney with Vapi assistant: ${error.message}`);
    // Return original data if sync fails
    return attorneyData;
  }
};
