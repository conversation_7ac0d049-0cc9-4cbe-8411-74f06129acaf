.sign-in-button {
  background-color: transparent;
  color: #39c0ff; /* Vibrant light blue */
  border: none;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin-right: 10px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.sign-in-button:hover {
  color: #5fd4ff; /* Lighter vibrant blue on hover */
  background-color: rgba(0, 0, 0, 0.2);
}

/* Dark mode styles */
body.dark-theme .sign-in-button {
  color: #39c0ff; /* Maintain vibrant blue in dark mode */
}

body.dark-theme .sign-in-button:hover {
  color: #5fd4ff; /* Lighter vibrant blue on hover */
  background-color: rgba(255, 255, 255, 0.1);
}

/* Light mode styles */
body:not(.dark-theme) .sign-in-button {
  color: #39c0ff; /* Maintain vibrant blue in light mode */
}

body:not(.dark-theme) .sign-in-button:hover {
  color: #5fd4ff; /* Lighter vibrant blue on hover */
  background-color: rgba(0, 0, 0, 0.05);
}
