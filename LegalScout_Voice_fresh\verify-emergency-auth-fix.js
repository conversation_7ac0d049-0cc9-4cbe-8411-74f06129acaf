// Verification script for emergencyAuth fix
// Run this with: node verify-emergency-auth-fix.js

import fs from 'fs';
import path from 'path';

console.log('🔍 Verifying emergencyAuth fix...\n');

// Read the supabase.js file
const supabaseFilePath = './src/lib/supabase.js';

try {
  const content = fs.readFileSync(supabaseFilePath, 'utf8');
  
  // Check 1: emergencyAuth export exists
  const hasEmergencyAuthExport = content.includes('export const emergencyAuth');
  console.log(`✅ emergencyAuth export: ${hasEmergencyAuthExport ? 'FOUND' : 'MISSING'}`);
  
  // Check 2: emergencyAuth object has getCurrentUser method
  const hasGetCurrentUserMethod = content.includes('emergencyAuth = {') && content.includes('getCurrentUser: async ()');
  console.log(`✅ getCurrentUser method: ${hasGetCurrentUserMethod ? 'FOUND' : 'MISSING'}`);
  
  // Check 3: window.emergencyAuth assignment exists
  const hasWindowAssignment = content.includes('window.emergencyAuth = emergencyAuth');
  console.log(`✅ window.emergencyAuth assignment: ${hasWindowAssignment ? 'FOUND' : 'MISSING'}`);
  
  // Check 4: No duplicate getCurrentUser in unifiedAuth
  const unifiedAuthSection = content.match(/export const unifiedAuth = \{[\s\S]*?\n\};/);
  if (unifiedAuthSection) {
    const hasGetCurrentUserInUnified = unifiedAuthSection[0].includes('getCurrentUser:');
    console.log(`✅ No getCurrentUser in unifiedAuth: ${!hasGetCurrentUserInUnified ? 'CORRECT' : 'ISSUE - STILL PRESENT'}`);
  }
  
  // Check 5: Proper object structure
  const emergencyAuthSection = content.match(/export const emergencyAuth = \{[\s\S]*?\n\};/);
  if (emergencyAuthSection) {
    console.log(`✅ emergencyAuth object structure: FOUND`);
    
    // Count braces to ensure proper closure
    const openBraces = (emergencyAuthSection[0].match(/\{/g) || []).length;
    const closeBraces = (emergencyAuthSection[0].match(/\}/g) || []).length;
    console.log(`✅ Brace balance: ${openBraces === closeBraces ? 'BALANCED' : 'UNBALANCED'}`);
  } else {
    console.log(`❌ emergencyAuth object structure: NOT FOUND`);
  }
  
  console.log('\n📊 Summary:');
  
  if (hasEmergencyAuthExport && hasGetCurrentUserMethod && hasWindowAssignment) {
    console.log('🎉 SUCCESS: emergencyAuth fix appears to be working correctly!');
    console.log('   - emergencyAuth is properly exported');
    console.log('   - getCurrentUser method is available');
    console.log('   - window.emergencyAuth global is set');
    
    console.log('\n🔧 Next steps:');
    console.log('   1. Test the application in the browser');
    console.log('   2. Check browser console for any remaining errors');
    console.log('   3. Verify authentication flows work correctly');
  } else {
    console.log('❌ ISSUES FOUND: The fix may not be complete');
    if (!hasEmergencyAuthExport) console.log('   - Missing emergencyAuth export');
    if (!hasGetCurrentUserMethod) console.log('   - Missing getCurrentUser method');
    if (!hasWindowAssignment) console.log('   - Missing window.emergencyAuth assignment');
  }
  
} catch (error) {
  console.error('❌ Error reading supabase.js file:', error.message);
  process.exit(1);
}

// Check if assistantUIConfigService.js imports are compatible
console.log('\n🔍 Checking assistantUIConfigService.js compatibility...');

try {
  const assistantServicePath = './src/services/assistantUIConfigService.js';
  const assistantContent = fs.readFileSync(assistantServicePath, 'utf8');
  
  const hasEmergencyAuthImport = assistantContent.includes('const { emergencyAuth } = await import(\'../lib/supabase.js\')');
  const hasEmergencyAuthUsage = assistantContent.includes('await emergencyAuth.getCurrentUser()');
  
  console.log(`✅ emergencyAuth import: ${hasEmergencyAuthImport ? 'FOUND' : 'MISSING'}`);
  console.log(`✅ emergencyAuth usage: ${hasEmergencyAuthUsage ? 'FOUND' : 'MISSING'}`);
  
  if (hasEmergencyAuthImport && hasEmergencyAuthUsage) {
    console.log('🎉 assistantUIConfigService.js is compatible with the fix!');
  } else {
    console.log('⚠️  assistantUIConfigService.js may need updates');
  }
  
} catch (error) {
  console.warn('⚠️  Could not check assistantUIConfigService.js:', error.message);
}

console.log('\n✅ Verification complete!');
