/**
 * Vapi Proxy API
 * 
 * Server-side API endpoints to proxy requests to Vapi.
 * This ensures that sensitive credentials are never exposed to the client.
 */

import express from 'express';
import { callVapiApi, createVapiHeaders, isVapiConfigured } from '../services/vapiCredentialsService';

const router = express.Router();

/**
 * Middleware to check if Vapi is configured
 */
const checkVapiConfigured = (req, res, next) => {
  if (!isVapiConfigured()) {
    return res.status(503).json({
      error: 'Vapi is not configured. Please set up Vapi credentials in the environment variables.'
    });
  }
  next();
};

/**
 * GET /api/vapi/assistants
 * 
 * Get all assistants
 */
router.get('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const assistants = await callVapiApi('/assistants');
    res.json(assistants);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting assistants:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/assistants/:id
 * 
 * Get an assistant by ID
 */
router.get('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const assistant = await callVapiApi(`/assistants/${req.params.id}`);
    res.json(assistant);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * POST /api/vapi/assistants
 * 
 * Create a new assistant
 */
router.post('/assistants', checkVapiConfigured, async (req, res) => {
  try {
    const assistant = await callVapiApi('/assistants', {
      method: 'POST',
      body: req.body
    });
    res.json(assistant);
  } catch (error) {
    console.error('[VapiProxyApi] Error creating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * PATCH /api/vapi/assistants/:id
 * 
 * Update an assistant
 */
router.patch('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const assistant = await callVapiApi(`/assistants/${req.params.id}`, {
      method: 'PATCH',
      body: req.body
    });
    res.json(assistant);
  } catch (error) {
    console.error('[VapiProxyApi] Error updating assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * DELETE /api/vapi/assistants/:id
 * 
 * Delete an assistant
 */
router.delete('/assistants/:id', checkVapiConfigured, async (req, res) => {
  try {
    const result = await callVapiApi(`/assistants/${req.params.id}`, {
      method: 'DELETE'
    });
    res.json(result);
  } catch (error) {
    console.error('[VapiProxyApi] Error deleting assistant:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/phone-numbers
 * 
 * Get all phone numbers
 */
router.get('/phone-numbers', checkVapiConfigured, async (req, res) => {
  try {
    const phoneNumbers = await callVapiApi('/phone-numbers');
    res.json(phoneNumbers);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting phone numbers:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * POST /api/vapi/calls
 * 
 * Create a new call
 */
router.post('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const call = await callVapiApi('/calls', {
      method: 'POST',
      body: req.body
    });
    res.json(call);
  } catch (error) {
    console.error('[VapiProxyApi] Error creating call:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/calls
 * 
 * Get all calls
 */
router.get('/calls', checkVapiConfigured, async (req, res) => {
  try {
    const calls = await callVapiApi('/calls');
    res.json(calls);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting calls:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/calls/:id
 * 
 * Get a call by ID
 */
router.get('/calls/:id', checkVapiConfigured, async (req, res) => {
  try {
    const call = await callVapiApi(`/calls/${req.params.id}`);
    res.json(call);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting call:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/vapi/config
 * 
 * Get Vapi configuration status (without exposing sensitive credentials)
 */
router.get('/config', async (req, res) => {
  try {
    const configured = isVapiConfigured();
    
    // Create a safe response that doesn't include sensitive credentials
    const config = {
      configured,
      hostUrl: configured ? 'Configured' : 'Not configured',
      publicKey: configured ? 'Configured' : 'Not configured',
      secretKey: configured ? 'Configured' : 'Not configured',
      oauth: {
        configured: !!await createVapiHeaders(false).then(h => h.Authorization)
      }
    };
    
    res.json(config);
  } catch (error) {
    console.error('[VapiProxyApi] Error getting config:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
