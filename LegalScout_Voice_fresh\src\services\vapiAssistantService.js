/**
 * Vapi Assistant Service
 *
 * This service provides functionality for creating and managing Vapi assistants
 * for attorneys in the LegalScout platform.
 */

import { vapiMcpService } from './vapiMcpService';
import { mcpConfig } from '../config/mcp.config';
import { supabase } from '../lib/supabase';
import {
  generateStructuredDataSchema,
  DEFAULT_SUMMARY_PROMPT,
  DEFAULT_STRUCTURED_DATA_PROMPT
} from '../utils/schemaGenerator';
import {
  DEFAULT_STRUCTURED_DATA_SCHEMA,
  DEFAULT_SUCCESS_EVALUATION_PROMPT
} from '../config/defaultTemplates';

class VapiAssistantService {
  constructor() {
    // Log the config value directly
    console.log('[VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig:', mcpConfig?.voice?.vapi?.secretKey);

    // Get the Secret Key specifically for MCP connection
    this.mcpApiKey = mcpConfig.voice.vapi.secretKey;
    // Keep public key if needed elsewhere
    this.publicKey = mcpConfig.voice.vapi.publicKey;
    this.connected = false;

    // Only log warning if API key is missing
    if (!this.mcpApiKey) {
      console.warn('VapiAssistantService: No MCP API key available');
    }
  }

  /**
   * Ensure connection to Vapi MCP server
   * @returns {Promise<boolean>} Connection status
   */
  async ensureConnection() {
    if (!this.connected) {
      try {
        console.log('VapiAssistantService: Attempting to connect to Vapi MCP server');

        // Try to connect with the configured SECRET API key
        this.connected = await vapiMcpService.connect(this.mcpApiKey);

        if (this.connected) {
          console.log('VapiAssistantService: Successfully connected to Vapi MCP server');
        } else {
          console.warn('VapiAssistantService: Failed to connect to Vapi MCP server');
        }
      } catch (error) {
        console.error('VapiAssistantService: Error ensuring connection:', error);
        this.connected = false;
      }
    }
    return this.connected;
  }

  /**
   * Generate attorney-specific webhook URL
   * @param {string} subdomain - Attorney subdomain
   * @returns {string} Webhook URL for the attorney
   */
  generateWebhookUrl(subdomain) {
    if (!subdomain) {
      // Fallback to generic webhook URL if no subdomain
      return process.env.VITE_APP_URL ? `${process.env.VITE_APP_URL}/api/vapi-webhook-direct` : 'https://legalscout.net/api/vapi-webhook-direct';
    }

    // Generate attorney-specific webhook URL
    const baseDomain = process.env.VITE_BASE_DOMAIN || 'legalscout.net';
    return `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
  }

  /**
   * Create a new assistant for an attorney
   * @param {Object} attorneyData - Attorney data from Supabase
   * @returns {Promise<Object>} Created assistant data
   */
  async createAssistantForAttorney(attorneyData) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    // Validate attorney data
    if (!attorneyData) {
      const errorMsg = 'Attorney data is required to create an assistant';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();
      if (!connected) {
        const errorMsg = 'Failed to connect to Vapi MCP server';
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // Prepare assistant configuration
      const assistantName = `${attorneyData.firm_name} Assistant`;
      const instructions = attorneyData.vapi_instructions ||
        `You are a legal assistant for ${attorneyData.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`;

      // Generate structured data schema from custom fields
      let structuredDataSchema = null;
      if (attorneyData.custom_fields) {
        try {
          // If stored as string, parse it
          const customFields = typeof attorneyData.custom_fields === 'string'
            ? JSON.parse(attorneyData.custom_fields)
            : attorneyData.custom_fields;

          structuredDataSchema = generateStructuredDataSchema(customFields);
        } catch (error) {
          console.error('Error generating schema from custom fields:', error);
        }
      }

      // Create assistant configuration using correct Vapi API format
      const assistantConfig = {
        name: assistantName,
        firstMessage: attorneyData.first_message || attorneyData.welcome_message || attorneyData.information_gathering || '',
        firstMessageMode: "assistant-speaks-first",
        model: {
          provider: "openai",
          model: attorneyData.ai_model || "gpt-4o",
          messages: [
            {
              role: "system",
              content: instructions
            }
          ]
        },
        voice: {
          provider: attorneyData.voice_provider || "11labs",
          voiceId: attorneyData.voice_id || "sarah"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        },
        // Add analysis plan configuration for new assistants (correct Vapi format)
        analysisPlan: {
          // Summary configuration
          summaryPrompt: attorneyData.summary_prompt || DEFAULT_SUMMARY_PROMPT,
          // Success evaluation configuration
          successEvaluationPrompt: attorneyData.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
          // Structured data configuration
          structuredDataPrompt: attorneyData.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
          structuredDataSchema: attorneyData.structured_data_schema ||
                               structuredDataSchema ||
                               DEFAULT_STRUCTURED_DATA_SCHEMA
        },
        // Add server URL configuration for webhook integration
        serverUrl: this.generateWebhookUrl(attorneyData.subdomain),
        serverUrlSecret: process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret'
      };

      // Create the assistant
      const assistant = await vapiMcpService.createAssistant(assistantConfig);

      // Update attorney record with assistant ID - but NEVER save mock IDs
      if (assistant && assistant.id && attorneyData.id && !assistant.mock) {
        await this.updateAttorneyAssistantId(attorneyData.id, assistant.id);
      } else if (assistant && assistant.mock) {
        console.warn('VapiAssistantService: Mock assistant created, NOT saving to database to prevent mock ID pollution');
      } else if (assistant && assistant.id) {
        console.warn('VapiAssistantService: Assistant created but attorney ID is missing, cannot update record');
      }

      return assistant;
    } catch (error) {
      console.error('💀 VapiAssistantService: Assistant creation failed - NO MOCKS ALLOWED!');
      console.error('💀 Error details:', error);

      // 🔥 NO MORE MOCKS! Throw the error and let the robust state handler deal with it
      throw new Error(`Failed to create Vapi assistant: ${error.message}`);
    }
  }

  /**
   * Update attorney record with assistant ID
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Vapi assistant ID
   * @returns {Promise<Object>} Updated attorney data
   */
  async updateAttorneyAssistantId(attorneyId, assistantId) {
    // Validate inputs
    if (!attorneyId) {
      console.error('Attorney ID is required to update assistant ID');
      return null;
    }

    if (!assistantId) {
      console.error('Assistant ID is required to update attorney record');
      return null;
    }

    // CRITICAL: Prevent mock assistant IDs from being saved to database
    if (assistantId.includes('mock') || assistantId.includes('MOCK') || assistantId.includes('DO-NOT-SAVE')) {
      console.error(`BLOCKED: Attempted to save mock assistant ID to database: ${assistantId}`);
      console.error('This is a critical error - mock IDs should NEVER be saved to the database');
      return null;
    }

    try {
      // Validate UUID format for attorney ID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(attorneyId)) {
        console.error('Invalid attorney ID format:', attorneyId);
        return null;
      }

      // Validate that assistant ID looks like a real UUID (not a mock)
      if (!uuidRegex.test(assistantId)) {
        console.error('Invalid assistant ID format - must be a valid UUID:', assistantId);
        return null;
      }

      console.log(`Updating attorney ${attorneyId} with assistant ID ${assistantId}`);

      const { data, error } = await supabase
        .from('attorneys')
        .update({ vapi_assistant_id: assistantId })
        .eq('id', attorneyId)
        .select()
        .single();

      if (error) {
        console.error('Error updating attorney with assistant ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error updating attorney with assistant ID:', error);
      return null;
    }
  }

  /**
   * Get assistant ID for an attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<string|null>} Assistant ID or null if not found
   */
  async getAssistantIdForAttorney(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id')
        .eq('id', attorneyId)
        .single();

      if (error) throw error;
      return data?.vapi_assistant_id || null;
    } catch (error) {
      console.error('Error getting assistant ID for attorney:', error);
      return null;
    }
  }

  /**
   * Get assistant ID by subdomain
   * @param {string} subdomain - Attorney subdomain
   * @returns {Promise<string|null>} Assistant ID or null if not found
   */
  async getAssistantIdBySubdomain(subdomain) {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id')
        .eq('subdomain', subdomain)
        .single();

      if (error) throw error;
      return data?.vapi_assistant_id || null;
    } catch (error) {
      console.error('Error getting assistant ID by subdomain:', error);
      return null;
    }
  }

  /**
   * Get all assistants from Vapi
   * @returns {Promise<Array>} Array of all assistants
   */
  async getAllAssistants() {
    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();

      if (!connected) {
        console.warn('VapiAssistantService: Not connected to Vapi MCP server. Returning empty array.');
        return [];
      }

      const assistants = await vapiMcpService.listAssistants();
      return assistants || [];
    } catch (error) {
      console.error('Error getting all assistants:', error);
      return [];
    }
  }

  /**
   * Get assistant by ID
   * @param {string} assistantId - Vapi assistant ID
   * @returns {Promise<Object|null>} Assistant object or null if not found
   */
  async getAssistant(assistantId) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    if (!assistantId) {
      logger.error('Assistant ID is required to get assistant');
      throw new Error('Assistant ID is required to get assistant');
    }

    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();

      if (!connected) {
        logger.error('Not connected to Vapi MCP server');
        throw new Error('Not connected to Vapi MCP server');
      }

      // Get the assistant from Vapi
      const assistant = await vapiMcpService.getAssistant(assistantId);

      if (!assistant) {
        logger.warn(`No assistant found with ID ${assistantId}`);
        return null;
      }

      logger.info(`Retrieved assistant: ${assistantId}`);
      return assistant;
    } catch (error) {
      logger.error(`Error getting assistant: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update an existing assistant's configuration
   * @param {string} assistantId - Vapi assistant ID
   * @param {Object} attorneyData - Attorney data from Supabase
   * @returns {Promise<Object>} Updated assistant data
   */
  async updateAssistantConfiguration(assistantId, attorneyData) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    if (!assistantId) {
      const errorMsg = 'Assistant ID is required to update configuration';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    if (!attorneyData) {
      const errorMsg = 'Attorney data is required to update configuration';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();

      if (!connected) {
        const errorMsg = 'Not connected to Vapi MCP server';
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // First get the current assistant to preserve other settings
      const assistant = await vapiMcpService.getAssistant(assistantId);

      if (!assistant) {
        const errorMsg = `Assistant with ID ${assistantId} not found`;
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // Generate structured data schema from custom fields
      let structuredDataSchema = null;
      if (attorneyData.custom_fields) {
        try {
          // If stored as string, parse it
          const customFields = typeof attorneyData.custom_fields === 'string'
            ? JSON.parse(attorneyData.custom_fields)
            : attorneyData.custom_fields;

          structuredDataSchema = generateStructuredDataSchema(customFields);
        } catch (error) {
          console.error('Error generating schema from custom fields:', error);
        }
      }

      // Create update configuration with new settings
      const updateConfig = {
        ...assistant,
        name: `${attorneyData.firm_name} Assistant`,
        instructions: attorneyData.vapi_instructions || assistant.instructions,
        firstMessage: attorneyData.first_message || attorneyData.welcome_message || attorneyData.information_gathering || '',
        firstMessageMode: "assistant-speaks-first",
        llm: {
          ...assistant.llm,
          model: attorneyData.ai_model || assistant.llm.model
        },
        voice: {
          ...assistant.voice,
          provider: attorneyData.voice_provider || assistant.voice.provider || '11labs',
          voiceId: attorneyData.voice_id || assistant.voice.voiceId || 'sarah'
        },
        // Update analysis plan configuration (correct Vapi format)
        analysisPlan: {
          // Summary configuration
          summaryPrompt: attorneyData.summary_prompt ||
                        (assistant.analysisPlan?.summaryPrompt || DEFAULT_SUMMARY_PROMPT),
          // Success evaluation configuration
          successEvaluationPrompt: attorneyData.success_evaluation_prompt ||
                                  (assistant.analysisPlan?.successEvaluationPrompt || DEFAULT_SUCCESS_EVALUATION_PROMPT)
        }
      };

      // Add structured data configuration to analysis plan
      // Use schema from attorney data if available, otherwise use existing, generated, or default schema
      const schema = attorneyData.structured_data_schema ||
                    structuredDataSchema ||
                    (assistant.analysisPlan?.structuredDataSchema || DEFAULT_STRUCTURED_DATA_SCHEMA);

      updateConfig.analysisPlan.structuredDataPrompt = attorneyData.structured_data_prompt ||
                                                      (assistant.analysisPlan?.structuredDataPrompt || DEFAULT_STRUCTURED_DATA_PROMPT);
      updateConfig.analysisPlan.structuredDataSchema = schema;

      // Ensure server URL configuration is present with attorney-specific webhook URL
      updateConfig.serverUrl = this.generateWebhookUrl(attorneyData.subdomain);
      updateConfig.serverUrlSecret = process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret';

      // Log webhook URL update for debugging
      logger.info(`Updating assistant ${assistantId} webhook URL to: ${updateConfig.serverUrl}`);

      // Update the assistant
      const updatedAssistant = await vapiMcpService.updateAssistant(assistantId, updateConfig);

      logger.info(`Successfully updated assistant: ${assistantId}`);
      return updatedAssistant;
    } catch (error) {
      logger.error(`Error updating assistant configuration: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update an existing assistant with specific fields
   * @param {string} assistantId - Vapi assistant ID
   * @param {Object} updatedFields - Fields to update in the assistant
   * @returns {Promise<Object>} Updated assistant data
   */
  async updateAssistant(assistantId, updatedFields) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    if (!assistantId) {
      const errorMsg = 'Assistant ID is required to update assistant';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    if (!updatedFields || Object.keys(updatedFields).length === 0) {
      const errorMsg = 'Updated fields are required to update assistant';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();

      if (!connected) {
        const errorMsg = 'Not connected to Vapi MCP server';
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // First get the current assistant to preserve other settings
      const assistant = await vapiMcpService.getAssistant(assistantId);

      if (!assistant) {
        const errorMsg = `Assistant with ID ${assistantId} not found`;
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // Create update configuration with new settings
      const updateConfig = {
        ...assistant,
        ...updatedFields
      };

      // Log the fields being updated
      logger.info(`Updating assistant ${assistantId} with fields: ${Object.keys(updatedFields).join(', ')}`);

      // Update the assistant
      const updatedAssistant = await vapiMcpService.updateAssistant(assistantId, updateConfig);

      logger.info(`Successfully updated assistant: ${assistantId}`);
      return updatedAssistant;
    } catch (error) {
      logger.error(`Error updating assistant: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update an existing assistant's instructions (legacy method)
   * @param {string} assistantId - Vapi assistant ID
   * @param {string} instructions - New instructions for the assistant
   * @param {string} firstMessage - New first message for the assistant
   * @returns {Promise<Object>} Updated assistant data
   * @deprecated Use updateAssistantConfiguration instead
   */
  async updateAssistantInstructions(assistantId, instructions, firstMessage) {
    if (!assistantId) {
      throw new Error('Assistant ID is required to update instructions');
    }

    try {
      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();

      if (!connected) {
        console.warn('VapiAssistantService: Not connected to Vapi MCP server. Returning mock updated assistant.');

        // Return a mock updated assistant for development
        return {
          id: assistantId,
          instructions: instructions,
          firstMessage: firstMessage,
          firstMessageMode: "assistant-speaks-first",
          updatedAt: new Date().toISOString(),
          voice: {
            provider: 'playht',
            voiceId: 'sarah'
          },
          mock: true
        };
      }

      // First get the current assistant to preserve other settings
      const assistant = await vapiMcpService.getAssistant(assistantId);

      if (!assistant) {
        console.warn(`VapiAssistantService: Assistant with ID ${assistantId} not found. Using mock assistant.`);

        // Return a mock assistant if the real one can't be found
        return {
          id: assistantId,
          instructions: instructions,
          firstMessage: firstMessage,
          firstMessageMode: "assistant-speaks-first",
          updatedAt: new Date().toISOString(),
          voice: {
            provider: 'playht',
            voiceId: 'sarah'
          },
          mock: true
        };
      }

      // Create update configuration with new instructions
      const updateConfig = {
        ...assistant,
        instructions: instructions || assistant.instructions,
        firstMessage: firstMessage || assistant.firstMessage,
        firstMessageMode: "assistant-speaks-first" // Always ensure this is set
      };

      console.log('Updating assistant with config:', {
        assistantId,
        instructions: updateConfig.instructions?.substring(0, 50) + '...',
        firstMessage: updateConfig.firstMessage,
        firstMessageMode: updateConfig.firstMessageMode
      });

      // Update the assistant
      const updatedAssistant = await vapiMcpService.updateAssistant(assistantId, updateConfig);

      return updatedAssistant;
    } catch (error) {
      console.error('Error updating assistant instructions:', error);

      // Return a mock updated assistant instead of throwing
      return {
        id: assistantId,
        instructions: instructions,
        firstMessage: firstMessage,
        firstMessageMode: "assistant-speaks-first",
        updatedAt: new Date().toISOString(),
        voice: {
          provider: 'playht',
          voiceId: 'sarah'
        },
        mock: true,
        error: error.message
      };
    }
  }

  /**
   * Synchronize attorney data from Supabase to Vapi
   * This is the primary method for ensuring a single source of truth
   * @param {Object} attorneyData - Complete attorney data from Supabase
   * @returns {Promise<Object>} Result of the synchronization
   */
  async syncAttorneyToVapi(attorneyData) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    if (!attorneyData) {
      const errorMsg = 'Attorney data is required for synchronization';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      logger.info(`Synchronizing attorney data to Vapi: ${attorneyData.id || attorneyData.subdomain || attorneyData.email}`);

      // Ensure connection to Vapi MCP server
      const connected = await this.ensureConnection();
      if (!connected) {
        const errorMsg = 'Not connected to Vapi MCP server';
        logger.error(errorMsg);
        throw new Error(errorMsg);
      }

      // Check if the attorney has an assistant ID
      const assistantId = attorneyData.vapi_assistant_id;

      // If no assistant ID or it's a mock ID, create a new assistant
      if (!assistantId || assistantId.includes('MOCK') || assistantId.includes('mock')) {
        logger.info('No valid assistant ID found (null or mock), creating new assistant');
        const assistant = await this.createAssistantForAttorney(attorneyData);

        // Update the attorney record in Supabase with the new assistant ID
        await this.updateAttorneyAssistantId(attorneyData.id, assistant.id);

        logger.info(`Created new Vapi assistant: ${assistant.id}`);
        return {
          success: true,
          assistantId: assistant.id,
          action: 'created',
          message: 'Created new Vapi assistant'
        };
      }

      // If assistant ID exists, update the existing assistant
      logger.info(`Updating existing assistant: ${assistantId}`);

      // First check if the assistant exists
      let assistant;
      try {
        assistant = await vapiMcpService.getAssistant(assistantId);
        logger.info(`Found existing assistant: ${assistantId}`);
      } catch (error) {
        logger.error(`Error getting assistant: ${error.message}`);

        // If the assistant doesn't exist, create a new one
        if (error.message.includes('not found') || error.message.includes('404')) {
          logger.info('Assistant not found, creating new one');
          const newAssistant = await this.createAssistantForAttorney(attorneyData);

          // Update attorney record with new assistant ID
          await this.updateAttorneyAssistantId(attorneyData.id, newAssistant.id);

          logger.info(`Created new replacement assistant: ${newAssistant.id}`);
          return {
            success: true,
            assistantId: newAssistant.id,
            action: 'recreated',
            message: 'Assistant not found, created new one'
          };
        }

        throw error;
      }

      // Update the assistant with all relevant fields from attorneyData
      logger.info(`Updating assistant configuration: ${assistantId}`);
      const updatedAssistant = await this.updateAssistantConfiguration(assistantId, attorneyData);

      logger.info(`Successfully updated assistant: ${assistantId}`);
      return {
        success: true,
        assistantId: updatedAssistant.id,
        action: 'updated',
        message: 'Updated existing Vapi assistant'
      };
    } catch (error) {
      logger.error(`Error synchronizing attorney to Vapi: ${error.message}`);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  }

  /**
   * Update attorney record with new assistant ID
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Vapi assistant ID
   * @returns {Promise<Object>} Updated attorney data
   */
  async updateAttorneyAssistantId(attorneyId, assistantId) {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    if (!attorneyId) {
      logger.error('Attorney ID is required to update assistant ID');
      throw new Error('Attorney ID is required to update assistant ID');
    }

    if (!assistantId) {
      logger.error('Assistant ID is required to update attorney record');
      throw new Error('Assistant ID is required to update attorney record');
    }

    try {
      logger.info(`Updating attorney ${attorneyId} with assistant ID ${assistantId}`);

      // Update the attorney record in Supabase
      const { data, error } = await supabase
        .from('attorneys')
        .update({ vapi_assistant_id: assistantId })
        .eq('id', attorneyId)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating attorney with assistant ID: ${error.message}`);
        throw error;
      }

      logger.info(`Successfully updated attorney ${attorneyId} with assistant ID ${assistantId}`);
      return data;
    } catch (error) {
      logger.error(`Error updating attorney with assistant ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create assistants for all attorneys who don't have one
   * @returns {Promise<Array>} Array of created assistants
   */
  async createAssistantsForAllAttorneys() {
    // Import the logger utility
    const { createLogger } = await import('../utils/loggerUtils');
    const logger = createLogger('VapiAssistantService');

    try {
      logger.info('Creating assistants for all attorneys without one');

      // Get all attorneys without assistant IDs
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('*')
        .is('vapi_assistant_id', null);

      if (error) throw error;

      logger.info(`Found ${attorneys.length} attorneys without assistant IDs`);

      // Create assistants for each attorney
      const results = [];
      for (const attorney of attorneys) {
        try {
          logger.info(`Creating assistant for attorney ${attorney.id}`);
          const assistant = await this.createAssistantForAttorney(attorney);

          // Update the attorney record with the new assistant ID
          await this.updateAttorneyAssistantId(attorney.id, assistant.id);

          results.push({
            attorneyId: attorney.id,
            assistantId: assistant.id,
            success: true
          });

          logger.info(`Successfully created assistant ${assistant.id} for attorney ${attorney.id}`);
        } catch (err) {
          logger.error(`Error creating assistant for attorney ${attorney.id}: ${err.message}`);
          results.push({
            attorneyId: attorney.id,
            error: err.message,
            success: false
          });
        }
      }

      return results;
    } catch (error) {
      logger.error(`Error creating assistants for all attorneys: ${error.message}`);
      throw error;
    }
  }
}

// Export a singleton instance
export const vapiAssistantService = new VapiAssistantService();

// Export the class for testing or custom instantiation
export default VapiAssistantService;
