/* Dashboard components specific styles */

/* Config tabs */
.config-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.config-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.5rem;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

.config-tab:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* Mobile responsiveness for config tabs */
@media (max-width: 768px) {
  .config-tabs {
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
  }

  .config-tab {
    width: 45px;
    height: 45px;
    font-size: 1.25rem;
    z-index: 100 !important;
    pointer-events: auto !important;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .config-tab:active {
    background-color: rgba(var(--primary-color-rgb), 0.2);
    transform: scale(0.95);
  }
}

.config-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* Tab content */
.profile-tab,
.agent-tab,
.consultations-tab,
.integrations-tab,
.custom-fields-tab,
.workflow-tab {
  padding: 0.5rem 0;
  max-width: 100%;
  overflow-x: hidden;
}

/* Dashboard card */
.dashboard-card {
  margin-bottom: 1.25rem;
  padding: 1.25rem;
  background-color: var(--card-bg, #ffffff);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  transition: all 0.3s ease;
}

/* Dark theme dashboard card */
[data-theme="dark"] .dashboard-card {
  background-color: var(--dark-card-bg, rgba(18, 18, 20, 0.5));
  border-color: var(--dark-border, rgba(100, 181, 246, 0.2));
}

.dashboard-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: var(--text-primary);
}

/* Make form elements more compact */
.form-group {
  margin-bottom: 0.75rem;
}

.form-control {
  padding: 0.6rem 0.75rem;
  font-size: 0.95rem;
  background-color: var(--bg-secondary, #ffffff);
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  color: var(--text-primary, #212121);
  border-radius: var(--radius-small);
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--accent-primary, #4B74AA);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

/* Dark theme form controls */
[data-theme="dark"] .form-control {
  background-color: var(--dark-input-bg, rgba(24, 24, 28, 0.4));
  border-color: var(--dark-border, rgba(100, 181, 246, 0.2));
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

[data-theme="dark"] .form-control:focus {
  border-color: var(--dark-accent, #64B5F6);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.1);
}

.tab-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: var(--radius-small);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-success {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid rgba(46, 204, 113, 0.3);
  color: #27ae60;
}

.alert-error {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
  color: #e74c3c;
}

/* Sync Status Styles */
.sync-status-error {
  padding: 1.5rem;
  background-color: rgba(231, 76, 60, 0.05);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: var(--radius-medium);
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.sync-status-error h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #e74c3c;
  font-size: 1.2rem;
}

.sync-status-error p {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.sync-status-error ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.sync-status-error li {
  margin-bottom: 0.5rem;
}

.sync-status-error .primary-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: var(--radius-small);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.sync-status-error .primary-button:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
}

.sync-status-error .primary-button:disabled {
  background-color: #e74c3c;
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Practice areas */
.practice-areas-container {
  margin-top: 0.5rem;
}

.practice-areas-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.practice-area-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: 1rem;
  font-size: 0.875rem;
}

.remove-tag-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-practice-area {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.add-button {
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
}

.add-button:hover {
  background-color: var(--primary-hover);
}

/* Theme selector */
.theme-selector {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.theme-option {
  flex: 1;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  cursor: pointer;
  transition: var(--transition-default);
  text-align: center;
}

.theme-option.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Consultations table */
.consultations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.search-filter {
  display: flex;
  gap: 0.75rem;
  flex: 1;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-box .form-control {
  padding-left: 2.5rem;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-default);
}

.filter-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.view-options {
  display: flex;
  gap: 0.5rem;
}

.view-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-small);
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-default);
}

.view-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.view-option.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.consultations-table {
  overflow-x: auto;
}

.consultations-table table {
  width: 100%;
  border-collapse: collapse;
}

.consultations-table th,
.consultations-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.consultations-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: rgba(0, 0, 0, 0.02);
}

.consultations-table tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.contact-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  margin-right: 0.5rem;
  transition: var(--transition-default);
}

.contact-link:hover {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  transform: translateY(-2px);
}

.summary-cell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-default);
}

.action-button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

/* Card view */
.consultations-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.consultation-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  overflow: hidden;
  transition: var(--transition-default);
}

.consultation-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-soft);
}

.card-header {
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.consultation-date {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.card-body {
  padding: 1rem;
}

.consultation-summary {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  line-height: 1.5;
}

.contact-info {
  margin-top: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.01);
  border-top: 1px solid var(--border-color);
}

.card-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.875rem;
}

.card-action-button:hover {
  color: var(--primary-color);
}

/* Map view */
.map-view {
  height: 400px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-secondary);
}

/* Custom columns */
.custom-column-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.custom-column-item:last-child {
  border-bottom: none;
}

.column-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.column-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.column-type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: capitalize;
}

.column-type-badge.text {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.column-type-badge.number {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}

.column-type-badge.select {
  background-color: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.column-type-badge.boolean {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f39c12;
}

/* Rules */
.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.rule-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Voice Configuration */
.voice-clone-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.voice-clone-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: var(--text-primary);
}

.section-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.voice-upload-container {
  margin: 1rem 0;
}

.voice-upload-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  border-radius: var(--radius-small);
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition-default);
}

.upload-button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.15);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.clone-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--radius-small);
  color: white;
  cursor: pointer;
  transition: var(--transition-default);
}

.clone-button:hover {
  background-color: var(--primary-hover);
}

.clone-button:disabled {
  background-color: rgba(var(--primary-color-rgb), 0.3);
  cursor: not-allowed;
}

.voice-requirements {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-small);
}

.voice-requirements h5 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.voice-requirements ul {
  margin: 0;
  padding-left: 1.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.voice-requirements li {
  margin-bottom: 0.25rem;
}

.condition-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* Checkbox group */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

/* Integration styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.status-badge.connected {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}

.status-badge.coming-soon {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.connect-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.875rem;
}

.connect-button:hover {
  background-color: var(--primary-hover);
}

/* Dark theme adjustments */
[data-theme="dark"] .theme-option {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .theme-option.active {
  border-color: var(--dark-accent);
  background-color: rgba(100, 181, 246, 0.1);
  color: var(--dark-accent);
}

[data-theme="dark"] .filter-button,
[data-theme="dark"] .view-option {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-secondary);
}

[data-theme="dark"] .filter-button:hover,
[data-theme="dark"] .view-option:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .view-option.active {
  background-color: var(--dark-accent);
  border-color: var(--dark-accent);
  color: #121212;
}

[data-theme="dark"] .consultations-table th {
  background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .consultations-table tr:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .contact-link {
  background-color: rgba(100, 181, 246, 0.1);
  color: var(--dark-accent);
}

[data-theme="dark"] .contact-link:hover {
  background-color: rgba(100, 181, 246, 0.2);
}

[data-theme="dark"] .action-button {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .action-button:hover {
  background-color: rgba(100, 181, 246, 0.1);
  color: var(--dark-accent);
}

[data-theme="dark"] .card-header,
[data-theme="dark"] .card-footer {
  background-color: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .map-view {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Dashboard cards mobile */
  .dashboard-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  .dashboard-card h3 {
    font-size: 1rem;
  }

  /* Form elements mobile */
  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 0.75rem;
    font-size: 1rem; /* Prevent zoom on iOS */
  }

  /* Button adjustments */
  .btn-primary,
  .btn-secondary {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-height: 44px; /* Touch-friendly minimum */
  }

  /* Consultations mobile */
  .consultations-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .search-filter {
    width: 100%;
  }

  .view-options {
    width: 100%;
    justify-content: flex-end;
  }

  .consultations-cards {
    grid-template-columns: 1fr;
  }

  /* Tab content mobile */
  .profile-tab,
  .agent-tab,
  .consultations-tab,
  .integrations-tab,
  .custom-fields-tab,
  .workflow-tab {
    padding: 0.5rem 0;
  }

  /* Config tabs mobile */
  .config-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }

  .config-tab {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
    /* Keep the circular design but slightly smaller */
  }

  /* Color picker mobile */
  .color-picker-container {
    flex-direction: column;
    align-items: stretch;
  }

  .color-input-wrapper {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  /* Practice areas mobile */
  .practice-areas-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .practice-area-item {
    padding: 0.75rem;
  }

  /* Voice settings mobile */
  .voice-settings {
    flex-direction: column;
    gap: 1rem;
  }

  .voice-preview {
    width: 100%;
  }
}

/* Small mobile breakpoint (480px and below) */
@media (max-width: 480px) {
  .config-tab {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .dashboard-card {
    padding: 0.75rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Extra small mobile breakpoint (360px and below) */
@media (max-width: 360px) {
  .config-tabs {
    gap: 0.25rem;
  }

  .config-tab {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
}

/* Workflow Tab Styles */
.workflow-tab {
  padding: 1rem 0;
}

.workflow-tab .tab-header {
  margin-bottom: 2rem;
}

.workflow-tab .tab-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.workflow-tab .tab-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

/* Section tabs for workflow */
.workflow-tab .section-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.workflow-tab .section-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.workflow-tab .section-tab:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.workflow-tab .section-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Error message */
.workflow-tab .error-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: var(--radius-small);
  color: #e74c3c;
  margin-bottom: 1.5rem;
}

.workflow-tab .error-message button {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
}

/* Section content */
.workflow-tab .section-content {
  min-height: 400px;
}

.workflow-tab .section-header {
  margin-bottom: 1.5rem;
}

.workflow-tab .section-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.workflow-tab .section-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Template grid */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.template-card {
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.template-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.template-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--radius-small);
  color: var(--primary-color);
  font-size: 1.5rem;
}

.template-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.template-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

.template-agents {
  margin-bottom: 1.5rem;
}

.template-agents h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.agent-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.agent-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-small);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.agent-item svg {
  color: var(--primary-color);
}

/* Buttons */
.btn-primary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
  width: 100%;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background-color: rgba(var(--primary-color-rgb), 0.5);
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.btn-secondary:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.btn-icon.danger:hover {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.3);
}

.btn-danger {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.btn-danger:hover {
  background-color: #e74c3c;
  color: white;
}

/* Squad list */
.squad-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.squad-card {
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.squad-card:hover {
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.squad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.squad-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.squad-actions {
  display: flex;
  gap: 0.5rem;
}

.squad-members {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.empty-state svg {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Workflow monitor */
.workflow-monitor {
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 1.5rem;
}

.workflow-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.workflow-status h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-indicator.active {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.workflow-controls {
  display: flex;
  gap: 0.5rem;
}

/* Enhanced workflow monitor styles */
.workflow-progress {
  margin: 1.5rem 0;
}

.agent-pipeline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-medium);
  margin-bottom: 1.5rem;
}

.agent-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  position: relative;
}

.agent-step.active .agent-avatar {
  background-color: var(--primary-color);
  color: white;
  animation: pulse 2s infinite;
}

.agent-step.pending .agent-avatar {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

.agent-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.agent-info {
  text-align: center;
}

.agent-info h5 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.agent-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.agent-step.active .agent-status {
  color: var(--primary-color);
  font-weight: 600;
}

.agent-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background-color: #27ae60;
  border-radius: 50%;
  animation: pulse-dot 1.5s infinite;
}

.pipeline-arrow {
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin: 0 1rem;
}

/* Workflow transcript */
.workflow-transcript {
  margin: 1.5rem 0;
}

.workflow-transcript h5 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.transcript-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-medium);
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.transcript-message {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: var(--radius-small);
}

.transcript-message.user {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-left: 3px solid var(--primary-color);
}

.transcript-message.assistant {
  background-color: rgba(255, 255, 255, 0.03);
  border-left: 3px solid var(--text-secondary);
}

.transcript-message .speaker {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--text-primary);
}

.transcript-message .message {
  font-size: 0.9rem;
  color: var(--text-primary);
  line-height: 1.4;
}

.transcript-message .timestamp {
  font-size: 0.75rem;
  color: var(--text-secondary);
  align-self: flex-end;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--primary-color-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0);
  }
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
  }
}

/* Collaborative workflow styles */
.template-participants {
  margin-bottom: 1.5rem;
}

.participant-section {
  margin-bottom: 1rem;
}

.participant-section h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: 600;
}

.participant-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.participant-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.5rem;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-small);
  font-size: 0.8rem;
}

.participant-item.human {
  color: var(--primary-color);
}

.participant-item.ai {
  color: #27ae60;
}

.participant-item svg {
  font-size: 0.9rem;
}

/* Participant grid for active workflows */
.participant-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.participant-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--card-bg, #ffffff);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
  position: relative;
  transition: all 0.2s ease;
}

/* Dark theme participant card */
[data-theme="dark"] .participant-card {
  background-color: var(--dark-card-bg, rgba(18, 18, 20, 0.5));
  border-color: var(--dark-border, rgba(100, 181, 246, 0.2));
}

.participant-card.human {
  border-left: 3px solid var(--primary-color);
}

.participant-card.ai {
  border-left: 3px solid #27ae60;
}

.participant-card.active {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.participant-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.1rem;
}

.participant-card.human .participant-avatar {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  color: var(--primary-color);
}

.participant-card.ai .participant-avatar {
  background-color: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.participant-info h6 {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.participant-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.participant-status {
  font-size: 0.7rem;
  font-weight: 500;
}

.participant-status.active {
  color: #27ae60;
}

.participant-status.invited {
  color: #f39c12;
}

.participant-status.standby {
  color: var(--text-secondary);
}

.participant-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  pointer-events: auto;
}

.modal-content {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  z-index: 10000;
  pointer-events: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  position: relative;
  z-index: 10001;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.template-info {
  margin-bottom: 1.5rem;
}

.template-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.template-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.human-roles {
  margin-bottom: 1.5rem;
}

.human-roles h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.role-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-small);
}

.role-item svg {
  color: var(--primary-color);
  font-size: 1.1rem;
}

.role-item div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.role-item strong {
  color: var(--text-primary);
  font-size: 0.9rem;
}

.role-item span {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.invite-section h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.invite-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.invite-input input {
  flex: 1;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.invite-input input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.invited-emails h6 {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  color: var(--text-primary);
}

.email-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-radius: var(--radius-small);
  font-size: 0.85rem;
}

.email-item span {
  color: var(--text-primary);
}

.remove-email {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-email:hover {
  color: #e74c3c;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* Ensure all modal interactive elements are clickable */
.modal-overlay button,
.modal-overlay input,
.modal-overlay select,
.modal-overlay textarea {
  pointer-events: auto;
  position: relative;
  z-index: 10001;
}

.modal-body {
  pointer-events: auto;
  position: relative;
  z-index: 10000;
}

.modal-header {
  pointer-events: auto;
  position: relative;
  z-index: 10000;
}

.modal-footer {
  pointer-events: auto;
  position: relative;
  z-index: 10000;
}

/* Prevent modal overlay from blocking clicks on modal content */
.modal-overlay > * {
  pointer-events: auto;
}

/* Dynamic participant addition styles */
.participants-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.participants-header h5 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.add-participant-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
}

.participant-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.participant-contact {
  font-size: 0.7rem;
  color: var(--text-secondary);
  opacity: 0.8;
}

.add-participant-card {
  border: 2px dashed var(--border-color);
  background-color: rgba(255, 255, 255, 0.01);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.add-participant-card:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.add-participant-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  color: var(--text-secondary);
}

.add-participant-content svg {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.add-participant-content span {
  font-weight: 500;
  color: var(--text-primary);
}

.add-participant-content small {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Participant type selector */
.participant-type-selector {
  margin-bottom: 1.5rem;
}

.participant-type-selector h5 {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.type-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  pointer-events: auto;
  position: relative;
  z-index: 10001;
}

.type-option:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.type-option.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.type-option svg {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.type-option span {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.type-option small {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

/* Participant form styles */
.participant-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-note {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-radius: var(--radius-small);
  border-left: 3px solid var(--primary-color);
}

.form-note small {
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Status indicators for different participant types */
.participant-status.calling {
  color: #3498db;
}

/* Enhanced modal for larger content */
.workflow-tab .modal-content {
  max-width: 600px;
  max-height: 85vh;
  background-color: #1F2937;
  border: 1px solid #374151;
  position: relative;
  z-index: 10000;
  pointer-events: auto;
}

.workflow-tab .modal-overlay {
  backdrop-filter: blur(4px);
  z-index: 9999;
  pointer-events: auto;
}

.workflow-tab .type-options {
  margin-top: 0.5rem;
}

.workflow-tab .participant-form {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* Template filtering styles */
.template-filters {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
}

.filter-btn:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-color: rgba(var(--primary-color-rgb), 0.3);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.demo-btn {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Client communication template styling */
.template-card[data-category="client-communication"] {
  border-left: 3px solid #27ae60;
}

.template-card[data-category="attorney-focused"] {
  border-left: 3px solid var(--primary-color);
}

.template-card[data-category="client-communication"] .template-icon {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

/* Enhanced template card for client roles */
.participant-item.client {
  background-color: rgba(39, 174, 96, 0.1);
  border-left: 2px solid #27ae60;
  color: #27ae60;
}

.participant-item.client svg {
  color: #27ae60;
}

/* Responsive design for workflow monitor */
@media (max-width: 768px) {
  .agent-pipeline {
    flex-direction: column;
    gap: 1rem;
  }

  .pipeline-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }

  .workflow-controls {
    flex-direction: column;
  }

  .template-grid {
    grid-template-columns: 1fr;
  }

  .participant-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }
}
