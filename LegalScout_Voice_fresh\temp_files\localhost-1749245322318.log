// IMMEDIATE TEST: Make a Call to Test Webhook
window.makeTestCallNow = async function() {
  console.log('📞 MAKING TEST CALL TO VERIFY WEBHOOK');
  console.log('='.repeat(50));
  
  const attorney = window.standaloneAttorneyManager?.attorney;
  
  console.log('📋 Test Call Setup:');
  console.log('• Assistant ID:', attorney.vapi_assistant_id);
  console.log('• Webhook URL: https://legalscout-voice.vercel.app/api/webhook/vapi-call');
  console.log('• Target: Your phone number for testing');
  
  console.log('\n🎯 OPTION 1: Dashboard Call Test');
  console.log('1. Go to your Calls tab');
  console.log('2. Enter your phone number');
  console.log('3. Click "Make Call"');
  console.log('4. Answer and have a brief conversation');
  console.log('5. End the call');
  console.log('6. Check Briefs tab immediately');
  
  console.log('\n🎯 OPTION 2: Vapi Dashboard Test');
  console.log('1. Go to https://dashboard.vapi.ai/assistants');
  console.log('2. Find your assistant: ' + attorney.vapi_assistant_id);
  console.log('3. Click "Test Assistant"');
  console.log('4. Have a conversation');
  console.log('5. End the test');
  console.log('6. Check Briefs tab');
  
  console.log('\n📊 WHAT TO EXPECT:');
  console.log('✅ Call completes → Webhook triggered');
  console.log('✅ Consultation auto-created in database');
  console.log('✅ Appears in Briefs tab immediately');
  console.log('✅ Custom fields populated from conversation');
  
  console.log('\n🔍 HOW TO VERIFY:');
  console.log('• Check Vercel logs for webhook activity');
  console.log('• Run diagnostic again to see consultation');
  console.log('• Click consultation in Briefs to see Brief modal');
  
  // Auto-navigate to Calls tab
  setTimeout(() => {
    const callsTab = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent?.trim() === 'Calls'
    );
    if (callsTab) {
      callsTab.click();
      console.log('📞 Navigated to Calls tab - ready to make test call!');
    }
  }, 2000);
  
  console.log('\n💡 READY TO TEST?');
  console.log('Your webhook is configured and waiting for a call!');
};

// Run the test setup
window.makeTestCallNow();
VM2985:3 📞 MAKING TEST CALL TO VERIFY WEBHOOK
VM2985:4 ==================================================
VM2985:8 📋 Test Call Setup:
VM2985:9 • Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VM2985:10 • Webhook URL: https://legalscout-voice.vercel.app/api/webhook/vapi-call
VM2985:11 • Target: Your phone number for testing
VM2985:13 
🎯 OPTION 1: Dashboard Call Test
VM2985:14 1. Go to your Calls tab
VM2985:15 2. Enter your phone number
VM2985:16 3. Click "Make Call"
VM2985:17 4. Answer and have a brief conversation
VM2985:18 5. End the call
VM2985:19 6. Check Briefs tab immediately
VM2985:21 
🎯 OPTION 2: Vapi Dashboard Test
VM2985:22 1. Go to https://dashboard.vapi.ai/assistants
VM2985:23 2. Find your assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VM2985:24 3. Click "Test Assistant"
VM2985:25 4. Have a conversation
VM2985:26 5. End the test
VM2985:27 6. Check Briefs tab
VM2985:29 
📊 WHAT TO EXPECT:
VM2985:30 ✅ Call completes → Webhook triggered
VM2985:31 ✅ Consultation auto-created in database
VM2985:32 ✅ Appears in Briefs tab immediately
VM2985:33 ✅ Custom fields populated from conversation
VM2985:35 
🔍 HOW TO VERIFY:
VM2985:36 • Check Vercel logs for webhook activity
VM2985:37 • Run diagnostic again to see consultation
VM2985:38 • Click consultation in Briefs to see Brief modal
VM2985:51 
💡 READY TO TEST?
VM2985:52 Your webhook is configured and waiting for a call!
Promise {<fulfilled>: undefined}
ActiveCheckHelper.ts:21 received intentional event
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 1 new preview iframes in added content
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiLogger.js:103 [21:28:04] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
vapiLogger.js:103 [21:28:04] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Vapi keys set globally
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:53 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:64 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:68 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:75 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:86 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:116 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:119 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:235 Supabase loaded from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:245 Creating Supabase client from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:249 Supabase client created from CDN
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM2807 SyncContext.jsx:136:24)
    at async Object.callback (VM2773 AuthContext.jsx:164:34)
    at async VM2843 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM2843 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM2843 @supabase_supabase-js.js:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM2807 SyncContext.jsx:136:24)
    at async Object.callback (VM2773 AuthContext.jsx:164:34)
    at async VM2843 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM2843 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM2843 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM2807 SyncContext.jsx:136:24)
    at async Object.callback (VM2773 AuthContext.jsx:164:34)
    at async VM2843 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM2843 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM2843 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [21:28:04] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:21 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:118 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
emergency-api-key-fix.js:19 🔑 [EmergencyApiKeyFix] Setting global environment variables...
emergency-api-key-fix.js:38 ✅ [EmergencyApiKeyFix] Global environment variables set
emergency-api-key-fix.js:43 🔧 [EmergencyApiKeyFix] Creating API key helper function...
emergency-api-key-fix.js:57 ✅ [EmergencyApiKeyFix] API key helper function created
emergency-api-key-fix.js:62 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
emergency-api-key-fix.js:129 ✅ [EmergencyApiKeyFix] Fetch override applied
emergency-api-key-fix.js:134 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
emergency-api-key-fix.js:138 [EmergencyApiKeyFix] Updating VapiMcpService API key...
emergency-api-key-fix.js:150 ✅ [EmergencyApiKeyFix] Existing services updated
emergency-api-key-fix.js:164 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
critical-production-fix.js:232 🚀 [CriticalProductionFix] Initializing all critical fixes...
critical-production-fix.js:19 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
critical-production-fix.js:54 ✅ [CriticalProductionFix] Vapi API key configuration fixed
critical-production-fix.js:175 🔧 [CriticalProductionFix] Fixing environment variables...
critical-production-fix.js:203 ✅ [CriticalProductionFix] Environment variables fixed
critical-production-fix.js:59 🌐 [CriticalProductionFix] Fixing CORS issues...
critical-production-fix.js:115 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
critical-production-fix.js:120 🛡️ [CriticalProductionFix] Fixing CSP issues...
critical-production-fix.js:170 ✅ [CriticalProductionFix] CSP issues fixed
critical-production-fix.js:208 📦 [CriticalProductionFix] Fixing import statement issues...
critical-production-fix.js:227 ✅ [CriticalProductionFix] Import statement issues fixed
critical-production-fix.js:241 🎉 [CriticalProductionFix] All critical fixes applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
production-cors-fix.js:181 [ProductionCorsFix] 🚀 Initializing all production fixes...
production-cors-fix.js:59 [ProductionCorsFix] 🌍 Ensuring production environment variables...
production-cors-fix.js:84 [ProductionCorsFix] ✅ Environment variables configured
production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
production-cors-fix.js:54 [ProductionCorsFix] ✅ API endpoint fixes applied
production-cors-fix.js:89 [ProductionCorsFix] 🛡️ Fixing CSP issues...
production-cors-fix.js:109 [ProductionCorsFix] ✅ CSP issues addressed
production-cors-fix.js:114 [ProductionCorsFix] 🚨 Enhancing error handling...
production-cors-fix.js:131 [ProductionCorsFix] ✅ Enhanced error handling installed
production-cors-fix.js:136 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
production-cors-fix.js:149 [ProductionCorsFix] ✅ Direct API mode configured
production-cors-fix.js:193 [ProductionCorsFix] 🎉 All production fixes initialized successfully
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [21:28:05] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ActiveCheckHelper.ts:8 updating page active status
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
vapiLogger.js:103 [21:28:05] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285409}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285409}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285409}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285409}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285409}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285418}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285418}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285418}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285418}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285418}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
 [StandaloneAttorneyManager] Updating attorney
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T21:28:05.505Z', subdomain: 'damon', firm_name: 'LegalScout', …}
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285481}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285481}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285481}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285481}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285481}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285504}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285504}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285504}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285504}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285504}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285521}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285521}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285521}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285521}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285521}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [21:28:05] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
 [StandaloneAttorneyManager] Updating attorney
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T21:28:05.568Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285582}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285582}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285582}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285582}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285582}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285586}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285586}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285586}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285586}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285586}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T21:28:05.568Z', subdomain: 'damon', firm_name: 'LegalScout', …}
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285647}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285647}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285647}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285647}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285647}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285651}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285651}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285651}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285651}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285651}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:98
await in initAuth
(anonymous) @ AuthContext.jsx:139
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285764}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285764}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285764}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285764}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285764}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 Supabase connection test successful!
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285818}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285818}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285818}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285818}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285818}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:41.098661+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'yooo swassuuup damon', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: yooo swassuuup damon
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285953}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749245285954}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
EnhancedPreviewNew.jsx:467 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:468 voiceId: echo
EnhancedPreviewNew.jsx:469 voiceProvider: openai
EnhancedPreviewNew.jsx:470 chatActive: false
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
production-cors-fix.js:154 [ProductionCorsFix] 🧪 Testing API connectivity...
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
debugConfig.js:30 [App] Available subdomains for testing: (4) ['damon', 'damonkost', 'generalcounselonline', 'scout']
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon', practice_description: ''}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T20:07:41.098661+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'yooo swassuuup damon', practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon'}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: LegalScout
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: yooo swassuuup damon
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
production-cors-fix.js:167 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:475 🚀 [PREVIEW CALL START] Starting consultation...
 🎯 [PREVIEW CALL START] Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 💬 [PREVIEW CALL START] Welcome message: yooo swassuuup damon
 🔊 [PREVIEW CALL START] Voice settings: {voiceId: 'echo', voiceProvider: 'openai'}
 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 [CallDebugger:VapiCall] Debugger initialized
 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 VapiCall: Setting processed config with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [useVapiCall] Using explicit assistantId without any overrides (including voice): cd0b44b7-397e-410d-8835-ce9c3ba584b2
 useVapiCall: Using explicit assistantId without overrides: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [useVapiCall] Checking for Vapi public key...
 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] Using API key: 310f0d43...
 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
 [useVapiCall] Creating Vapi instance directly using official pattern
 [useVapiCall] Loading Vapi SDK using vapiLoader
 [VapiLoader] Vapi SDK already loaded
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 🚀 VapiCall component mounted, preparing to start call...
 📊 Current status: idle
 🤖 Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🔧 Vapi instance available: false
 ⚙️ Processed config: null
 ⏸️ Not ready to initialize yet - missing assistantId or vapi
 ⏸️ Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 ⏸️ Vapi instance: false
 Status changed to: idle
 CALL_STATUS.CONNECTED value: connected
 🔄 VapiCall component received dossier update: {}
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: echo
 voiceProvider: openai
 chatActive: true
 🧹 Cleanup function for not ready state
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:525 useVapiCall: Using explicit assistantId without overrides: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:248 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:249 [VapiMcpService] API key being used: 310f0d43...
vapiMcpService.js:261 [VapiMcpService] Using direct API key for server operations: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: {}
useVapiCall.js:826 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
vapiLoader.js:165 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
vapiEmissionsService.js:35 VapiEmissionsService: Initialized successfully
vapiEmissionsService.js:35 VapiEmissionsService: Initialized successfully
useVapiCall.js:831 [useVapiCall] Vapi instance created successfully
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:525 useVapiCall: Using explicit assistantId without overrides: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:1102 Requesting microphone permission...
useVapiCall.js:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:538 Setting up call params with explicit assistantId and no overrides at all: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AttorneyProfileManager.js:385 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
useVapiCall.js:1107 Available audio input devices: (5) [{…}, {…}, {…}, {…}, {…}]
useVapiCall.js:1125 Using audio constraints: {audio: {…}}
AttorneyProfileManager.js:57 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
useVapiCall.js:1129 Microphone permission granted with device: Default - Microphone (Yeti Classic)
useVapiCall.js:1177 Starting call with existing Vapi instance using direct pattern
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/call/web
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/call/web
critical-production-fix.js:48 [CriticalProductionFix] Using PUBLIC key for client operations: client
critical-production-fix.js:49 [CriticalProductionFix] PUBLIC key value: 310f0d43...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/call/web
emergency-api-key-fix.js:88 [EmergencyApiKeyFix] Using SECRET key for call operations
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantOverrides: 'Set', vapi: true, processedConfig: true}
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', propAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', forceDefaultAssistant: false, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', subdomain: 'damon'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: idle -> connecting
useVapiCall.js:1217 [useVapiCall] Starting call with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:1228 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js:1230 [useVapiCall] Call started successfully: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: {firmName: 'LegalScout', welcomeMessage: 'yooo swassuuup damon', voiceId: 'echo', voiceProvider: 'openai', initialMessage: 'yooo swassuuup damon', …}
VapiCall.jsx:1790 mergedAssistantOverrides: {firstMessage: 'yooo swassuuup damon', model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1819 Found welcome message from mergedCustomInstructions.welcomeMessage: yooo swassuuup damon
VapiCall.jsx:1837 Adding welcome message to UI: yooo swassuuup damon
VapiCall.jsx:1842 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:1845 Voice settings: {voiceId: 'echo', voiceProvider: 'openai'}
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
VM3252 speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
VM3252 speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
VM3252 speech-particles.js:525 🎨 Converting user color: #4B74AA
VM3252 speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
VM3252 speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
VM3252 speech-particles.js:537 🎨 Converting assistant color: #2C3E50
VM3252 speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
VM3252 speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
VM3252 speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
speech-particles.js:525 🎨 Converting user color: #4B74AA
speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #2C3E50
speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1775 Added force-visible class to call interface
VM3252 speech-particles.js:309 Speech particles: Setting up visualization
VM3252 speech-particles.js:318 Speech particles: Canvas found, initializing visualization
VM3252 speech-particles.js:328 Speech particles: Visualization started
speech-particles.js:309 Speech particles: Setting up visualization
speech-particles.js:318 Speech particles: Canvas found, initializing visualization
speech-particles.js:328 Speech particles: Visualization started
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VM3254:4 enumerateDevices took longer than expected: 85
value @ VM3254:4
value @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
$ @ VM3254:4
g.enumDWrapper @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
_ @ VM3254:4
U.camPreferences @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
dispatch @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
C @ VM3254:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
VM3254:4 enumerateDevices took longer than expected: 63
value @ VM3254:4
value @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g.enumDWrapper @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
nA @ VM3254:4
A.stream @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
dispatch @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
C @ VM3254:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VM3254:4 enumerateDevices took longer than expected: 60
value @ VM3254:4
value @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g.enumDWrapper @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g.getCurrentDevices @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
u @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
Promise.then
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
g @ VM3254:4
i @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
eval @ VM3254:4
C @ VM3254:4
postMessage
value @ @vapi-ai_web.js?v=306847c5:4818
value @ @vapi-ai_web.js?v=306847c5:6681
(anonymous) @ @vapi-ai_web.js?v=306847c5:6035
d @ @vapi-ai_web.js?v=306847c5:195
s2 @ @vapi-ai_web.js?v=306847c5:207
Promise.then
d @ @vapi-ai_web.js?v=306847c5:199
s2 @ @vapi-ai_web.js?v=306847c5:207
(anonymous) @ @vapi-ai_web.js?v=306847c5:212
(anonymous) @ @vapi-ai_web.js?v=306847c5:204
o.value @ @vapi-ai_web.js?v=306847c5:6052
start @ @vapi-ai_web.js?v=306847c5:9971
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: signaling
 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:1181 Direct Vapi call started successfully: {id: '6e142f3c-abd1-48ff-a9dd-5cab6457bbb7', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-06T21:28:07.608Z', updatedAt: '2025-06-06T21:28:07.608Z', type: 'webCall', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492452897030.8903290071400214', error: null, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492452897030.8903290071400214', error: null, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492452897030.8903290071400214', error: null, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492452897030.8903290071400214', error: null, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17492452897030.8903290071400214', error: null, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-9rm99', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-9rm99', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: sfu
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-9rm99', what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-9rm99', what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-9rm99', what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '5720b647-0e33-466f-b7d1-866a0074500f', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '5720b647-0e33-466f-b7d1-866a0074500f', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '5720b647-0e33-466f-b7d1-866a0074500f', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '5720b647-0e33-466f-b7d1-866a0074500f', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '5720b647-0e33-466f-b7d1-866a0074500f', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: 'listening', fromId: '5720b647-0e33-466f-b7d1-866a0074500f', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: 'listening', fromId: '5720b647-0e33-466f-b7d1-866a0074500f', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: 'listening', fromId: '5720b647-0e33-466f-b7d1-866a0074500f', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: 'listening', fromId: '5720b647-0e33-466f-b7d1-866a0074500f', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: '5720b647-0e33-466f-b7d1-866a0074500f', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:56 Call started - setting status to CONNECTED
useVapiCall.js:56 Call started - setting status to CONNECTED
useVapiCall.js:56 Call started - setting status to CONNECTED
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'in-progress'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:476 SpeechParticles: Speech update event: {type: 'speech-update', status: 'started', role: 'assistant', turn: 0}
SpeechParticles.jsx:480 SpeechParticles: Assistant started speaking
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 0}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.3154786722400966
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.3154786722400966
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.3154786722400966
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.06309573444801933, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.5047658755841546
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.5047658755841546 frequency: 275.7148813376232 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.5047658755841546 externalFrequency: 275.7148813376232 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.50 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.3971641173621407
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.3971641173621407
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.3971641173621407
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.07943282347242814, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.6354625877794251
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.6354625877794251 frequency: 295.31938816691377 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.6354625877794251 externalFrequency: 295.31938816691377 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.64 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.2233417960754815
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.2233417960754815
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.2233417960754815
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.0446683592150963, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.3573468737207704
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.3573468737207704 frequency: 253.60203105811556 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.3573468737207704 externalFrequency: 253.60203105811556 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.36 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.5
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.5
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.5
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.1, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.8
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.8 frequency: 320 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.8 externalFrequency: 320 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.80 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VM3252 speech-particles.js:358 Speech particles: Animation using external audio data externalAmplitude: 0.8 externalFrequency: 320 externalSpeaker: assistant
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.28117066259517454
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.28117066259517454
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.28117066259517454
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.05623413251903491, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.4498730601522793
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.4498730601522793 frequency: 267.4809590228419 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.4498730601522793 externalFrequency: 267.4809590228419 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.45 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.9976311574844399
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.9976311574844399
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.9976311574844399
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.19952623149688797, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 1.00 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VM3252 speech-particles.js:358 Speech particles: Animation using external audio data externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Swast sub"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Swast sub"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Swast sub"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Swast sub"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Swast sub"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Swast sub'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.005610092271509815
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.005610092271509815
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.001122018454301963, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.008976147634415704
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window volume event detected: 0.5
 SpeechParticles: Custom volume event received: 0.5
 SpeechParticles: Custom volume event with level: 1
 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.5
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.1, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.8
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.8 frequency: 320 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.8 externalFrequency: 320 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.80 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.80
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.3154786722400966
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.3154786722400966
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.3154786722400966
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.06309573444801933, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.5047658755841546
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.5047658755841546 frequency: 275.7148813376232 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.5047658755841546 externalFrequency: 275.7148813376232 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.50 speaker: assistant secondaryColor: #2C3E50
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
VM3252 speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
speech-particles.js:393 Speech particles: assistant speaking at amplitude 0.50
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'active-speaker-change', activeSpeaker: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.2505936168136361
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.2505936168136361
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.2505936168136361
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.05011872336272722, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.40094978690181776
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.40094978690181776 frequency: 260.14246803527266 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.40094978690181776 externalFrequency: 260.14246803527266 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.40 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.09976311574844399
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.09976311574844399
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 0.49881557874221993
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.49881557874221993 frequency: 274.822336811333 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.49881557874221993 externalFrequency: 274.822336811333 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.09976311574844399
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.0199526231496888, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.1596209851975104
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.1596209851975104 frequency: 223.94314777962654 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.1596209851975104 externalFrequency: 223.94314777962654 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.16 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.00792446596230557
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.00792446596230557
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.001584893192461114, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.012679145539688913
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"assistant","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:476 SpeechParticles: Speech update event: {type: 'speech-update', status: 'stopped', role: 'assistant', turn: 0}
SpeechParticles.jsx:487 SpeechParticles: Assistant stopped speaking
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0 frequency: 200 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0 externalFrequency: 200 externalSpeaker: assistant
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'assistant', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'assistant', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'assistant', turn: 0}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Sois sup daemon"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Sois sup daemon"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Sois sup daemon"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Sois sup daemon"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Sois sup daemon"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Sois sup daemon
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Sois sup daemon
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Sois sup daemon'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Sois sup daemon
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…:1200,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…:1200,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…:1200,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…:1200,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…:1200,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(2), messages: Array(2), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(2), messages: Array(2), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(2), messages: Array(2), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'cpu-load-change', cpuLoadState: 'low', cpuLoadStateReason: 'none', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"partial","transcript":"What's up,"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"partial","transcript":"What's up,"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"partial","transcript":"What's up,"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"partial","transcript":"What's up,"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"partial","transcript":"What's up,"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'partial', transcript: "What's up,"}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"final","transcript":"What's up?"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"final","transcript":"What's up?"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"final","transcript":"What's up?"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"final","transcript":"What's up?"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: `{"type":"transcript","role":"user","transcriptType":"final","transcript":"What's up?"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:338 Processing user transcript: What's up?
useVapiCall.js:375 Adding new user transcript to message history with ID: transcript-1749245294817-bnbl0o6
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:338 Processing user transcript: What's up?
useVapiCall.js:213 Received message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'user', transcriptType: 'final', transcript: "What's up?"}
useVapiCall.js:338 Processing user transcript: What's up?
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:369 Skipping duplicate or similar user transcript: What's up?
useVapiCall.js:369 Skipping duplicate or similar user transcript: What's up?
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:369 Skipping duplicate or similar user transcript: What's up?
useVapiCall.js:369 Skipping duplicate or similar user transcript: What's up?
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1754 Adding new message to UI: What's up?
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1754 Adding new message to UI: What's up?
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…51,"duration":480}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…51,"duration":480}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…51,"duration":480}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…51,"duration":480}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…51,"duration":480}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(3), messages: Array(3), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(3), messages: Array(3), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(3), messages: Array(3), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:299 Skipping duplicate user message: What's up?
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"user","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"user","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"user","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"user","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"speech-update","status":"stopped","role":"user","turn":0}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:476 SpeechParticles: Speech update event: {type: 'speech-update', status: 'stopped', role: 'user', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'user', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'user', turn: 0}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'stopped', role: 'user', turn: 0}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":"Hi"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":"Hi"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":"Hi"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":"Hi"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":"Hi"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Hi'}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Hi'}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Hi'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" there"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" there"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" there"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" there"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" there"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' there'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' there'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' there'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":"!"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":"!"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":"!"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":"!"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":"!"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: '!'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '!'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '!'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: `{"type":"model-output","output":" I'm"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: `{"type":"model-output","output":" I'm"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: `{"type":"model-output","output":" I'm"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: `{"type":"model-output","output":" I'm"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: `{"type":"model-output","output":" I'm"}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: " I'm"}
 Received message: {type: 'model-output', output: " I'm"}
 Received message: {type: 'model-output', output: " I'm"}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' Scout'}
 Received message: {type: 'model-output', output: ' Scout'}
 Received message: {type: 'model-output', output: ' Scout'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":","}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":","}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":","}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":","}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":","}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ','}
 Received message: {type: 'model-output', output: ','}
 Received message: {type: 'model-output', output: ','}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' your'}
 Received message: {type: 'model-output', output: ' your'}
 Received message: {type: 'model-output', output: ' your'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' legal'}
 Received message: {type: 'model-output', output: ' legal'}
 Received message: {type: 'model-output', output: ' legal'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" assistant"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" assistant"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" assistant"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" assistant"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" assistant"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' assistant'}
 Received message: {type: 'model-output', output: ' assistant'}
 Received message: {type: 'model-output', output: ' assistant'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" here"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" here"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" here"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" here"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" here"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' here'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' here'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' here'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" at"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" at"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" at"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" at"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" at"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' at'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' at'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' at'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" Legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" Legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" Legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" Legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" Legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' Legal'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' Legal'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' Legal'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":"Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":"Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":"Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":"Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":"Scout"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Scout'}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Scout'}
useVapiCall.js:213 Received message: {type: 'model-output', output: 'Scout'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":"."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":"."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":"."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":"."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":"."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: '.'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '.'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '.'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" How"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" How"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" How"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" How"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" How"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' How'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' How'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' How'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" can"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" can"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" can"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" can"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" can"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' can'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' can'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' can'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" I"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" I"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" I"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" I"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" I"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' I'}
 Received message: {type: 'model-output', output: ' I'}
 Received message: {type: 'model-output', output: ' I'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" help"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" help"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" help"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" help"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" help"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' help'}
 Received message: {type: 'model-output', output: ' help'}
 Received message: {type: 'model-output', output: ' help'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" you"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" you"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" you"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" you"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" you"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' you'}
 Received message: {type: 'model-output', output: ' you'}
 Received message: {type: 'model-output', output: ' you'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" with"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" with"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" with"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" with"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" with"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' with'}
 Received message: {type: 'model-output', output: ' with'}
 Received message: {type: 'model-output', output: ' with'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" your"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' your'}
 Received message: {type: 'model-output', output: ' your'}
 Received message: {type: 'model-output', output: ' your'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" legal"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'model-output', output: ' legal'}
 Received message: {type: 'model-output', output: ' legal'}
 Received message: {type: 'model-output', output: ' legal'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" matter"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" matter"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" matter"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" matter"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" matter"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' matter'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' matter'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' matter'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":" today"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":" today"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":" today"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":" today"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":" today"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' today'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' today'}
useVapiCall.js:213 Received message: {type: 'model-output', output: ' today'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"model-output","output":"?"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"model-output","output":"?"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"model-output","output":"?"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"model-output","output":"?"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"model-output","output":"?"}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'model-output', output: '?'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '?'}
useVapiCall.js:213 Received message: {type: 'model-output', output: '?'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":1}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":1}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":1}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":1}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"speech-update","status":"started","role":"assistant","turn":1}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:476 SpeechParticles: Speech update event: {type: 'speech-update', status: 'started', role: 'assistant', turn: 1}
SpeechParticles.jsx:480 SpeechParticles: Assistant started speaking
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 1}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 1}
useVapiCall.js:213 Received message: {type: 'speech-update', status: 'started', role: 'assistant', turn: 1}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.05610092271509818
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.05610092271509818
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 0.2805046135754909
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.2805046135754909 frequency: 242.07569203632363 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.2805046135754909 externalFrequency: 242.07569203632363 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.05610092271509818
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.011220184543019636, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.08976147634415708
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.08976147634415708 frequency: 213.46422145162356 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.08976147634415708 externalFrequency: 213.46422145162356 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.09 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:103 Assistant started speaking
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"partial","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'partial', transcript: 'Hi there.'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window volume event detected: 0.8891397050194615
 SpeechParticles: Custom volume event received: 0.8891397050194615
 SpeechParticles: Custom volume event with level: 1
 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
 VapiCall: Dispatched volume change event: 0.8891397050194615
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.1778279410038923, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 1
 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 1.00 speaker: assistant secondaryColor: #2C3E50
 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 Window volume event detected: 0.5610092271509818
 SpeechParticles: Custom volume event received: 0.5610092271509818
 SpeechParticles: Custom volume event with level: 1
 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
 VapiCall: Dispatched volume change event: 0.5610092271509818
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.11220184543019636, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
 SpeechParticles: Calculated assistant audio level: 0.8976147634415709
 Speech particles: updateAudioSource called with amplitude: 0.8976147634415709 frequency: 334.64221451623564 speaker: assistant
 Speech particles: Global state updated to externalAmplitude: 0.8976147634415709 externalFrequency: 334.64221451623564 externalSpeaker: assistant
 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.90 speaker: assistant secondaryColor: #2C3E50
 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:499 Window volume event detected: 0.353972892192069
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.353972892192069
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 1
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 1 frequency: 350 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 1 externalFrequency: 350 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.353972892192069
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.0707945784384138, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.5663566275073104
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.5663566275073104 frequency: 284.95349412609653 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.5663566275073104 externalFrequency: 284.95349412609653 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.57 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"Hi there."}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Hi there.
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Hi there.
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: 'Hi there.'}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: Hi there.
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…":480,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…":480,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…":480,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…":480,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"conversation-update","conversation":[{"ro…":480,"source":""}],"messagesOpenAIFormatted":[]}', fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(4), messages: Array(4), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(4), messages: Array(4), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:213 Received message: {type: 'conversation-update', conversation: Array(4), messages: Array(4), messagesOpenAIFormatted: Array(0)}
useVapiCall.js:226 Filtering out default first message: Sois sup daemon
useVapiCall.js:241 Skipping system message, not for display: You are Scout, the AI legal assistant for LegalSco...
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:314 Adding new assistant message to history: Hi there.
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:299 Skipping duplicate assistant message: Hi there.
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:299 Skipping duplicate assistant message: Hi there.
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:314 Adding new assistant message to history: Hi there.
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:299 Skipping duplicate assistant message: Hi there.
useVapiCall.js:299 Skipping duplicate user message: What's up?
useVapiCall.js:299 Skipping duplicate assistant message: Hi there.
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1754 Adding new message to UI: Hi there.
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:1754 Adding new message to UI: Hi there.
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.0353972892192069
SpeechParticles.jsx:516 SpeechParticles: Custom volume event with level: 0.1769864460960345
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.1769864460960345 frequency: 226.54796691440518 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.1769864460960345 externalFrequency: 226.54796691440518 externalSpeaker: assistant
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.0353972892192069
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.00707945784384138, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.05663566275073104
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: 0.05663566275073104 frequency: 208.49534941260964 speaker: assistant
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: 0.05663566275073104 externalFrequency: 208.49534941260964 externalSpeaker: assistant
SpeechParticles.jsx:422 🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude 0.06 speaker: assistant secondaryColor: #2C3E50
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.0014091914656322274
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.0014091914656322274
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.0002818382931264455, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.002254706345011564
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0.0015811388300841897
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0.0015811388300841897
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495: 0.00031622776601683794, 5720b647-0e33-466f-b7d1-866a0074500f: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0.0025298221281347035
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
VapiCall.jsx:1944 End call button clicked, stopping call...
VapiCall.jsx:1949 Set window.vapiCallActive to false immediately on end call button click
VapiCall.jsx:1953 Assistant is still speaking - showing confirmation before ending call
VapiCall.jsx:1965 User confirmed ending the call while assistant is speaking
 Calling stopCall function from VapiCall component
 [CallDebugger:VapiCall] Stopping call {callId: null}
 [useVapiCall] Stopping call...
 stopCall function called successfully
chunk-Q72EVS5P.js:3671 [Violation] 'click' handler took 2138ms
[Violation] 'click' handler took 2148ms
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
useVapiCall.js:30 useVapiCall: Using assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
useVapiCall.js:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
useVapiCall.js:893 Stopping active call during cleanup
useVapiCall.js:899 Calling onEndCall callback during unmount
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: connecting -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:879 Setting up Vapi event listeners
VapiService.js:19 [VapiService] Setting up event listener for: call-start
VapiService.js:19 [VapiService] Setting up event listener for: call-end
VapiService.js:19 [VapiService] Setting up event listener for: speech-start
VapiService.js:19 [VapiService] Setting up event listener for: speech-end
VapiService.js:19 [VapiService] Setting up event listener for: message
VapiService.js:19 [VapiService] Setting up event listener for: error
VapiService.js:19 [VapiService] Setting up event listener for: volume-level
VapiService.js:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js:1262 [useVapiCall] Calling onEndCall callback
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {forcedWhileSpeaking: true}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:1273 [useVapiCall] Call stopped successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useVapiCall.js:884 Component unmounting - performing Vapi cleanup
VapiService.js:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:19 [VapiService] Event listeners removed from Vapi instance
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1473 Call was still initializing during unmount, cancelling initialization
VapiCall.jsx:1478 Skipping onEndCall during initialization cancellation to prevent mount/unmount cycle
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
EnhancedPreviewNew.jsx:467 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:468 voiceId: echo
EnhancedPreviewNew.jsx:469 voiceProvider: openai
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'leave-meeting', what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'leave-meeting', what: 'iframe-call-message', from: 'module', callClientId: '17492452875430.7012213740737707'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: `{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"I'm Scout."}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: `{"type":"transcript","role":"assistant","transcriptType":"final","transcript":"I'm Scout."}`, fromId: '3c8a2c94-23bc-48d1-af6e-b0ef2e3b9495', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: I'm Scout.
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: I'm Scout.
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: I'm Scout.
useVapiCall.js:213 Received message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:325 Received transcript message: {type: 'transcript', role: 'assistant', transcriptType: 'final', transcript: "I'm Scout."}
useVapiCall.js:386 Skipping assistant transcript, will be handled by model-output: I'm Scout.
useVapiCall.js:107 Assistant stopped speaking
useVapiCall.js:107 Assistant stopped speaking
useVapiCall.js:107 Assistant stopped speaking
useVapiCall.js:107 Assistant stopped speaking
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:78 Call ended
useVapiCall.js:83 Call ended while assistant is still speaking - passing forcedWhileSpeaking flag
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {forcedWhileSpeaking: true}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'customer-ended-call'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'customer-ended-call'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'customer-ended-call'}
useVapiCall.js:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'customer-ended-call'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17492452875430.7012213740737707', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'leave-meeting', what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'leave-meeting', what: 'iframe-call-message', from: 'embedded', callClientId: '17492452875430.7012213740737707'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
speech-particles.js:345 [Violation] 'requestAnimationFrame' handler took 78ms
speech-particles.js:345 [Violation] 'requestAnimationFrame' handler took 285ms
