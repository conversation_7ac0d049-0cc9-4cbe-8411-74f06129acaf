/**
 * Vapi Field Mapping Utility
 *
 * This utility provides standardized mapping between Supabase fields and Vapi API fields.
 * It ensures consistent data transformation between the two systems.
 */

import { createLogger } from './loggerUtils';

const logger = createLogger('vapiFieldMapping');

/**
 * Field mapping between Supabase and Vapi
 * 
 * This mapping defines how fields in the Supabase attorneys table
 * map to fields in the Vapi API.
 */
export const FIELD_MAPPING = {
  // Basic fields
  welcome_message: 'firstMessage',
  vapi_instructions: 'instructions',
  
  // Voice configuration
  voice_id: 'voice.voiceId',
  voice_provider: 'voice.provider',
  
  // LLM configuration
  ai_model: 'llm.model',
  
  // Analysis configuration
  summary_prompt: 'analysis.summary.prompt',
  structured_data_prompt: 'analysis.structuredData.prompt',
  structured_data_schema: 'analysis.structuredData.schema',
};

/**
 * Default values for Vapi fields
 * 
 * These values are used when the corresponding Supabase field is not provided.
 */
export const DEFAULT_VALUES = {
  firstMessage: "Hello, I'm Scout from your law firm. How can I help you today?",
  firstMessageMode: "assistant-speaks-first",
  instructions: "You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.",
  voice: {
    provider: "11labs",
    voiceId: "sarah",
    model: "eleven_turbo_v2_5"
  },
  llm: {
    provider: "openai",
    model: "gpt-4o"
  },
  transcriber: {
    provider: "deepgram",
    model: "nova-3"
  }
};

/**
 * Convert attorney data from Supabase to Vapi format
 * 
 * @param {Object} attorneyData - Attorney data from Supabase
 * @returns {Object} - Data in Vapi format
 */
export const attorneyToVapi = (attorneyData) => {
  if (!attorneyData) {
    logger.error('No attorney data provided');
    return { ...DEFAULT_VALUES };
  }

  // Start with default values
  const vapiData = { ...DEFAULT_VALUES };

  // Set name based on firm name
  vapiData.name = `${attorneyData.firm_name || 'Law Firm'} Legal Assistant`;

  // Map simple fields
  if (attorneyData.welcome_message) {
    vapiData.firstMessage = attorneyData.welcome_message;
  }

  if (attorneyData.vapi_instructions) {
    vapiData.instructions = attorneyData.vapi_instructions;
  }

  // Map voice configuration
  if (attorneyData.voice_id || attorneyData.voice_provider) {
    vapiData.voice = {
      ...vapiData.voice,
      voiceId: attorneyData.voice_id || vapiData.voice.voiceId,
      provider: attorneyData.voice_provider || vapiData.voice.provider
    };
  }

  // Map LLM configuration
  if (attorneyData.ai_model) {
    vapiData.llm = {
      ...vapiData.llm,
      model: attorneyData.ai_model
    };
  }

  // Map analysis configuration if available
  if (attorneyData.summary_prompt || attorneyData.structured_data_prompt || attorneyData.structured_data_schema) {
    vapiData.analysis = {
      summary: {
        enabled: !!attorneyData.summary_prompt,
        prompt: attorneyData.summary_prompt || ''
      },
      structuredData: {
        enabled: !!(attorneyData.structured_data_prompt && attorneyData.structured_data_schema),
        prompt: attorneyData.structured_data_prompt || '',
        schema: attorneyData.structured_data_schema || {}
      }
    };
  }

  logger.info('Converted attorney data to Vapi format');
  return vapiData;
};

/**
 * Extract fields from Vapi assistant data to update attorney record
 * 
 * @param {Object} vapiData - Assistant data from Vapi
 * @returns {Object} - Fields to update in Supabase
 */
export const vapiToAttorney = (vapiData) => {
  if (!vapiData) {
    logger.error('No Vapi data provided');
    return {};
  }

  const attorneyData = {};

  // Map simple fields
  if (vapiData.firstMessage) {
    attorneyData.welcome_message = vapiData.firstMessage;
  }

  if (vapiData.instructions) {
    attorneyData.vapi_instructions = vapiData.instructions;
  }

  // Map voice configuration
  if (vapiData.voice) {
    if (vapiData.voice.voiceId) {
      attorneyData.voice_id = vapiData.voice.voiceId;
    }
    
    if (vapiData.voice.provider) {
      attorneyData.voice_provider = vapiData.voice.provider;
    }
  }

  // Map LLM configuration
  if (vapiData.llm && vapiData.llm.model) {
    attorneyData.ai_model = vapiData.llm.model;
  }

  // Map analysis configuration if available
  if (vapiData.analysis) {
    if (vapiData.analysis.summary && vapiData.analysis.summary.prompt) {
      attorneyData.summary_prompt = vapiData.analysis.summary.prompt;
    }

    if (vapiData.analysis.structuredData) {
      if (vapiData.analysis.structuredData.prompt) {
        attorneyData.structured_data_prompt = vapiData.analysis.structuredData.prompt;
      }

      if (vapiData.analysis.structuredData.schema) {
        attorneyData.structured_data_schema = vapiData.analysis.structuredData.schema;
      }
    }
  }

  logger.info('Extracted attorney fields from Vapi data');
  return attorneyData;
};

/**
 * Create update data for Vapi assistant based on changed fields
 * 
 * @param {Object} changedFields - Fields that have been changed
 * @param {Object} currentAssistant - Current assistant data from Vapi
 * @returns {Object} - Update data for Vapi API
 */
export const createVapiUpdateData = (changedFields, currentAssistant = {}) => {
  if (!changedFields || Object.keys(changedFields).length === 0) {
    logger.warn('No changed fields provided');
    return {};
  }

  const updateData = {};

  // Map simple fields
  if (changedFields.welcome_message !== undefined) {
    updateData.firstMessage = changedFields.welcome_message;
  }

  if (changedFields.vapi_instructions !== undefined) {
    updateData.instructions = changedFields.vapi_instructions;
  }

  // Map voice configuration
  if (changedFields.voice_id !== undefined || changedFields.voice_provider !== undefined) {
    updateData.voice = {
      ...(currentAssistant.voice || {}),
      voiceId: changedFields.voice_id !== undefined ? changedFields.voice_id : (currentAssistant.voice?.voiceId || 'sarah'),
      provider: changedFields.voice_provider !== undefined ? changedFields.voice_provider : (currentAssistant.voice?.provider || '11labs')
    };
  }

  // Map LLM configuration
  if (changedFields.ai_model !== undefined) {
    updateData.llm = {
      ...(currentAssistant.llm || {}),
      model: changedFields.ai_model
    };
  }

  logger.info('Created Vapi update data from changed fields');
  return updateData;
};

export default {
  FIELD_MAPPING,
  DEFAULT_VALUES,
  attorneyToVapi,
  vapiToAttorney,
  createVapiUpdateData
};
