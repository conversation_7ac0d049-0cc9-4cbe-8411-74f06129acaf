/**
 * Test Authentication Fix
 * 
 * Simple test script to verify the authentication profile fix works correctly
 */

import { supabase } from '../lib/supabase';
import { fixAuthProfile, authHealthCheck } from './authProfileFixer';

/**
 * Test the authentication fix with current user
 */
export async function testAuthFix() {
  console.log('🧪 [TestAuthFix] Starting authentication fix test...');
  
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError || !userData.user) {
      console.log('❌ [TestAuthFix] No authenticated user found');
      return {
        success: false,
        message: 'No authenticated user found',
        error: userError
      };
    }
    
    console.log('✅ [TestAuthFix] Found authenticated user:', userData.user.email);
    
    // Run health check
    const healthResults = await authHealthCheck(userData.user);
    console.log('🏥 [TestAuthFix] Health check results:', healthResults);
    
    // Test the profile fix
    const attorneyProfile = await fixAuthProfile(userData.user);
    
    if (attorneyProfile) {
      console.log('✅ [TestAuthFix] Profile fix successful!');
      console.log('📋 [TestAuthFix] Attorney profile:', {
        id: attorneyProfile.id,
        firm_name: attorneyProfile.firm_name,
        email: attorneyProfile.email,
        subdomain: attorneyProfile.subdomain,
        vapi_assistant_id: attorneyProfile.vapi_assistant_id
      });
      
      return {
        success: true,
        message: 'Authentication fix successful',
        attorney: attorneyProfile,
        healthCheck: healthResults
      };
    } else {
      console.log('⚠️ [TestAuthFix] No profile found/created - user needs to complete profile');
      return {
        success: true,
        message: 'No profile found - user needs to complete profile',
        attorney: null,
        healthCheck: healthResults
      };
    }
    
  } catch (error) {
    console.error('❌ [TestAuthFix] Test failed:', error);
    return {
      success: false,
      message: 'Authentication fix test failed',
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * Test profile creation for a specific email
 */
export async function testProfileCreation(email) {
  console.log('🧪 [TestAuthFix] Testing profile creation for:', email);
  
  try {
    // Create a mock user object
    const mockUser = {
      id: 'test-user-' + Date.now(),
      email: email,
      user_metadata: {
        full_name: email.split('@')[0]
      }
    };
    
    // Test the profile fix
    const attorneyProfile = await fixAuthProfile(mockUser);
    
    if (attorneyProfile) {
      console.log('✅ [TestAuthFix] Profile creation successful!');
      return {
        success: true,
        message: 'Profile creation successful',
        attorney: attorneyProfile
      };
    } else {
      console.log('⚠️ [TestAuthFix] No profile created');
      return {
        success: false,
        message: 'No profile created',
        attorney: null
      };
    }
    
  } catch (error) {
    console.error('❌ [TestAuthFix] Profile creation test failed:', error);
    return {
      success: false,
      message: 'Profile creation test failed',
      error: error.message
    };
  }
}

/**
 * Run comprehensive authentication tests
 */
export async function runComprehensiveAuthTests() {
  console.log('🧪 [TestAuthFix] Running comprehensive authentication tests...');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: []
  };
  
  // Test 1: Current user authentication
  try {
    const currentUserTest = await testAuthFix();
    results.tests.push({
      name: 'Current User Authentication',
      ...currentUserTest
    });
  } catch (error) {
    results.tests.push({
      name: 'Current User Authentication',
      success: false,
      message: 'Test failed',
      error: error.message
    });
  }
  
  // Test 2: Known email profile creation
  const knownEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  for (const email of knownEmails) {
    try {
      const profileTest = await testProfileCreation(email);
      results.tests.push({
        name: `Profile Creation - ${email}`,
        ...profileTest
      });
    } catch (error) {
      results.tests.push({
        name: `Profile Creation - ${email}`,
        success: false,
        message: 'Test failed',
        error: error.message
      });
    }
  }
  
  // Calculate summary
  const totalTests = results.tests.length;
  const passedTests = results.tests.filter(t => t.success).length;
  const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
  
  results.summary = {
    total: totalTests,
    passed: passedTests,
    failed: totalTests - passedTests,
    successRate: successRate + '%'
  };
  
  console.log('📊 [TestAuthFix] Test summary:', results.summary);
  
  return results;
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.testAuthFix = testAuthFix;
  window.testProfileCreation = testProfileCreation;
  window.runComprehensiveAuthTests = runComprehensiveAuthTests;
  
  console.log('🧪 [TestAuthFix] Test functions available globally:');
  console.log('  - testAuthFix()');
  console.log('  - testProfileCreation(email)');
  console.log('  - runComprehensiveAuthTests()');
}
