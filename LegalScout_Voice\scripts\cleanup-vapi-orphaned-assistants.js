/**
 * Cleanup Orphaned Vapi Assistants
 * 
 * This script deletes orphaned assistants from Vapi that are not linked to any attorney
 */

import fetch from 'node-fetch';

// Vapi API configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564'; // Your private key

// The one assistant that should be kept (linked to "damon" attorney)
const KEEP_ASSISTANT_ID = 'eb8533fa-902e-46be-8ce9-df20f5c550d7';

// Orphaned assistants to delete
const ORPHANED_ASSISTANTS = [
  '368e963b-761c-45bb-91e9-8f96b8483f4d',
  'efcadef8-7d6e-49b1-91fb-e74d4223e695',
  'e0705a0d-0511-4d91-b185-56feba033b76',
  'edd3008b-ac5e-4026-97fb-d556cc1def1e',
  'a53344e3-1edb-4917-9b2d-fbd28f4e5dbf',
  'd1dae707-d4b9-4728-9a29-210ccc3b4f5e',
  '5b264951-c02d-43a0-99e0-902578192706',
  'fd273605-12af-438b-9fa4-31cc0dfb4af4',
  '2ecce4a5-a2ca-4a9a-a75d-8821b8294589'
];

async function makeVapiRequest(endpoint, method = 'GET', body = null) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json'
    }
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  console.log(`Making ${method} request to: ${url}`);
  
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  if (response.status === 204) {
    return null; // No content for successful delete
  }

  return await response.json();
}

async function listAllAssistants() {
  try {
    console.log('📋 Fetching all assistants from Vapi...');
    const assistants = await makeVapiRequest('/assistant');
    console.log(`Found ${assistants.length} assistants in Vapi`);
    
    assistants.forEach(assistant => {
      const status = assistant.id === KEEP_ASSISTANT_ID ? '✅ KEEP' : '❌ DELETE';
      console.log(`   ${status} ${assistant.id} - ${assistant.name} (Created: ${assistant.createdAt})`);
    });
    
    return assistants;
  } catch (error) {
    console.error('Error listing assistants:', error);
    throw error;
  }
}

async function deleteAssistant(assistantId) {
  try {
    console.log(`🗑️  Deleting assistant: ${assistantId}`);
    await makeVapiRequest(`/assistant/${assistantId}`, 'DELETE');
    console.log(`✅ Successfully deleted assistant: ${assistantId}`);
    return true;
  } catch (error) {
    console.error(`❌ Error deleting assistant ${assistantId}:`, error.message);
    return false;
  }
}

async function cleanupOrphanedAssistants(dryRun = true) {
  console.log(`🧹 ${dryRun ? 'DRY RUN - ' : ''}Cleaning up orphaned Vapi assistants...\n`);
  
  try {
    // List all current assistants
    const assistants = await listAllAssistants();
    
    console.log(`\n🎯 Target for cleanup:`);
    console.log(`   Keep: ${KEEP_ASSISTANT_ID} (linked to "damon" attorney)`);
    console.log(`   Delete: ${ORPHANED_ASSISTANTS.length} orphaned assistants\n`);
    
    if (dryRun) {
      console.log('🔍 DRY RUN - No actual deletions will be performed');
      console.log('   Run with --delete flag to perform actual cleanup\n');
      
      ORPHANED_ASSISTANTS.forEach(id => {
        const assistant = assistants.find(a => a.id === id);
        if (assistant) {
          console.log(`   Would delete: ${id} - ${assistant.name}`);
        } else {
          console.log(`   Assistant not found: ${id} (already deleted?)`);
        }
      });
      
      return;
    }
    
    // Perform actual deletions
    console.log('🚨 PERFORMING ACTUAL DELETIONS...\n');
    
    const results = {
      deleted: [],
      failed: [],
      notFound: []
    };
    
    for (const assistantId of ORPHANED_ASSISTANTS) {
      const assistant = assistants.find(a => a.id === assistantId);
      
      if (!assistant) {
        console.log(`⚠️  Assistant ${assistantId} not found (already deleted?)`);
        results.notFound.push(assistantId);
        continue;
      }
      
      const success = await deleteAssistant(assistantId);
      
      if (success) {
        results.deleted.push(assistantId);
      } else {
        results.failed.push(assistantId);
      }
      
      // Small delay between deletions to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Summary
    console.log('\n📊 Cleanup Summary:');
    console.log(`   ✅ Successfully deleted: ${results.deleted.length}`);
    console.log(`   ❌ Failed to delete: ${results.failed.length}`);
    console.log(`   ⚠️  Not found: ${results.notFound.length}`);
    
    if (results.deleted.length > 0) {
      console.log('\n✅ Deleted assistants:');
      results.deleted.forEach(id => console.log(`   - ${id}`));
    }
    
    if (results.failed.length > 0) {
      console.log('\n❌ Failed deletions:');
      results.failed.forEach(id => console.log(`   - ${id}`));
    }
    
    // Verify final state
    console.log('\n🔍 Verifying final state...');
    const finalAssistants = await listAllAssistants();
    
    const remainingOrphaned = finalAssistants.filter(a => 
      a.id !== KEEP_ASSISTANT_ID && ORPHANED_ASSISTANTS.includes(a.id)
    );
    
    if (remainingOrphaned.length === 0) {
      console.log('🎉 SUCCESS: All orphaned assistants have been cleaned up!');
      console.log(`   Only the linked assistant remains: ${KEEP_ASSISTANT_ID}`);
    } else {
      console.log(`⚠️  WARNING: ${remainingOrphaned.length} orphaned assistants still remain:`);
      remainingOrphaned.forEach(a => console.log(`   - ${a.id}`));
    }
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  }
}

// Main execution
async function main() {
  console.log('🚀 Vapi Orphaned Assistant Cleanup Tool\n');
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  const shouldDelete = args.includes('--delete');
  
  if (shouldDelete) {
    console.log('⚠️  DELETE MODE ENABLED - This will permanently delete orphaned assistants!\n');
    
    // Give user a chance to cancel
    console.log('Press Ctrl+C within 5 seconds to cancel...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    console.log('Proceeding with deletion...\n');
  }
  
  await cleanupOrphanedAssistants(!shouldDelete);
  
  if (!shouldDelete) {
    console.log('\n💡 To perform actual cleanup, run: node scripts/cleanup-vapi-orphaned-assistants.js --delete');
  }
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
