/* Map Dossier Styles */
.map-dossier-view {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: hidden;
    border-radius: 10px;
    background-color: #111;
}

/* Map container styles */
.map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Leaflet-specific styles */
.leaflet-container {
    width: 100%;
    height: 100%;
    z-index: 5;
    background-color: #1a1a1a !important;
}

/* Ensure Leaflet controls are visible */
.leaflet-control-container {
    z-index: 800;
}

.leaflet-control-zoom {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 2px;
    z-index: 1000;
}

.leaflet-control-zoom a {
    color: white !important;
    background-color: rgba(30, 40, 60, 0.7) !important;
    border: 1px solid rgba(100, 150, 255, 0.3) !important;
}

.leaflet-control-zoom a:hover {
    background-color: rgba(40, 60, 100, 0.8) !important;
}

.leaflet-control-attribution {
    background-color: rgba(0, 0, 0, 0.7) !important;
    color: white !important;
    z-index: 1000;
}

.leaflet-control-attribution a {
    color: #3b82f6 !important;
}

/* Ensure map tiles are visible but darker */
.leaflet-tile {
    filter: brightness(0.6) contrast(1.4) invert(1) hue-rotate(180deg) !important;
}

/* Style popups for dark theme */
.leaflet-popup-content-wrapper {
    background-color: rgba(20, 30, 50, 0.9);
    color: white;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
}

.leaflet-popup-tip {
    background-color: rgba(20, 30, 50, 0.9);
}

.leaflet-popup-close-button {
    color: white;
    z-index: 1001;
}

.leaflet-popup-close-button:hover {
    color: #3b82f6;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .leaflet-control-zoom {
        display: none;
    }
    .map-dossier-view {
        min-height: 300px;
    }
    .map-container {
        min-height: 300px;
    }
}

/* Map header */
.map-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(16, 25, 40, 0.8);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 900;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.map-title {
    color: #66b2ff;
    margin: 0;
    font-size: 1.2rem;
}

/* Dossier table styles */
.dossier-table {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--bg-secondary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 1.5rem;
    width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    border: 1px solid var(--border-color);
}

[data-theme="light"] .dossier-table {
    background-color: rgba(255, 255, 255, 0.8);
    border-color: rgba(0, 0, 0, 0.05);
}

.dossier-table.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Table row styles */
.case-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 8px;
}

.case-table tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    background-color: var(--nav-hover);
    margin-bottom: 8px;
}

[data-theme="light"] .case-table tr {
    background-color: rgba(255, 255, 255, 0.9);
}

.case-table tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    background-color: var(--nav-hover);
    border-color: rgba(59, 130, 246, 0.2);
}

.case-table td {
    padding: 1rem;
    vertical-align: top;
    border: 1px solid var(--border-color);
}

[data-theme="light"] .case-table td {
    border-color: rgba(0, 0, 0, 0.05);
}

.case-table td:first-child {
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
}

.case-table td:last-child {
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
}

.emoji {
    font-size: 1.2rem;
    width: 40px;
    text-align: center;
    opacity: 0.9;
    transition: transform 0.3s ease;
}

tr:hover .emoji {
    transform: scale(1.1);
}

.label {
    color: var(--text-secondary);
    width: 120px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.value {
    position: relative;
    color: var(--text-primary);
    max-width: 200px;
    font-size: 0.9rem;
    line-height: 1.5;
    font-weight: 400;
}

/* Minimize button */
.minimize-button {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--nav-hover);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

[data-theme="light"] .minimize-button {
    background-color: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 0, 0, 0.05);
}

.minimize-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
}

/* Animations */
@keyframes typing {
    from { width: 0; opacity: 0; }
    to { width: 100%; opacity: 1; }
}

.typing-effect {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    animation: typing 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.stamp {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border: 1px solid rgb(var(--color-interactive));
    border-radius: 6px;
    color: rgb(var(--color-interactive));
    animation: stamp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--nav-hover);
    font-size: 0.9rem;
    font-weight: 500;
}

[data-theme="light"] .stamp {
    background-color: rgba(255, 255, 255, 0.9);
}

@keyframes stamp {
    from { transform: scale(1.2); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Main location marker */
.main-location-marker {
    position: relative;
    width: 30px !important;
    height: 30px !important;
}

.marker-icon {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 24px;
    text-shadow: 0 0 10px rgba(102, 178, 255, 0.8);
}

.pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(102, 178, 255, 0.6);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Attorney markers */
.attorney-marker {
    position: relative;
    width: 24px !important;
    height: 24px !important;
}

.attorney-marker-icon {
    width: 24px;
    height: 24px;
    background: rgba(102, 178, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 0 10px rgba(102, 178, 255, 0.6);
    animation: appear 0.5s ease-out;
    z-index: 900;
}

@keyframes appear {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Attorney popup */
.attorney-popup {
    padding: 0.5rem;
}

.attorney-popup h3 {
    margin: 0 0 0.5rem 0;
    color: #66b2ff;
    font-size: 1rem;
}

.attorney-popup p {
    margin: 0.25rem 0;
    font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dossier-table {
        width: calc(100% - 40px);
        max-height: 50vh;
        overflow: auto;
    }
    
    .case-table td {
        padding: 0.8rem;
    }
    
    .value {
        font-size: 0.85rem;
    }
    
    .minimize-button {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Dark theme overrides for map elements */
.dark-map {
    background-color: #1a1a1a;
}

.dark-tiles {
    filter: brightness(0.7) saturate(0.8) hue-rotate(200deg);
}

/* Ensure map controls are visible in dark theme */
.leaflet-control-container .leaflet-control {
    background-color: rgba(30, 40, 60, 0.7);
    color: white;
    border: none !important;
    margin: 10px;
}

.leaflet-control-container .leaflet-control a {
    color: #fff;
}

.leaflet-control-container .leaflet-control a:hover {
    color: #4B74AA;
}

/* Tooltip styling */
.leaflet-tooltip {
    background-color: rgba(26, 26, 26, 0.9);
    color: #fff;
    border: 1px solid #333;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Map markers */
.attorney-marker-icon,
.location-marker-icon {
    z-index: 900;
}

/* Ensure markers are clickable */
.leaflet-marker-icon {
    z-index: 900 !important;
}

/* Ensure popup is above markers */
.leaflet-popup {
    z-index: 901 !important;
}

/* Map controls */
.map-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    gap: 10px;
}

.toggle-map-button {
    background-color: rgba(30, 40, 60, 0.7);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.toggle-map-button:hover {
    background-color: rgba(40, 60, 100, 0.8);
}

/* Hide the Map when not visible */
.map-dossier-view.hidden {
    display: none;
}

/* Responsive styles */
@media (max-width: 768px) {
    .map-controls {
        bottom: 5px;
        right: 5px;
    }
    
    .toggle-map-button {
        padding: 6px 10px;
        font-size: 12px;
    }
} 