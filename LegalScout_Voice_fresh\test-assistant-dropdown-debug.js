/**
 * Assistant Dropdown Debug Test
 * Diagnoses why the dropdown isn't showing assistants
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;
const VAPI_PRIVATE_KEY = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

console.log('🔍 Assistant Dropdown Debug Test');
console.log('================================');

async function testDirectVapiAPI() {
  console.log('\n1️⃣ Testing Direct Vapi API...');
  
  try {
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const assistants = await response.json();
    console.log(`✅ Found ${assistants.length} assistants from Vapi API`);
    
    // Show assistant details
    assistants.forEach((assistant, index) => {
      console.log(`   ${index + 1}. ${assistant.name || 'Unnamed'} (${assistant.id})`);
    });

    return assistants;
  } catch (error) {
    console.log(`❌ Direct Vapi API failed: ${error.message}`);
    return null;
  }
}

async function testAssistantFiltering(allAssistants) {
  console.log('\n2️⃣ Testing Assistant Filtering Logic...');
  
  if (!allAssistants) {
    console.log('❌ No assistants to filter');
    return [];
  }

  // Simulate the filtering logic from EnhancedAssistantDropdown
  const attorneySubdomain = 'damon'; // From the user's profile
  const relevantAssistants = allAssistants.filter(assistant => {
    const name = (assistant.name || '').toLowerCase();
    
    // Include assistants with attorney's subdomain
    if (name.includes(attorneySubdomain.toLowerCase())) return true;
    
    // Include assistants with attorney's name variations
    if (name.includes('kost')) return true;
    if (name.includes('damon')) return true;
    
    // Include generic LegalScout assistants
    if (name.includes('legalscout')) return true;
    
    return false;
  });

  console.log(`✅ Filtered ${allAssistants.length} assistants down to ${relevantAssistants.length} relevant ones`);
  
  if (relevantAssistants.length > 0) {
    console.log('   Relevant assistants:');
    relevantAssistants.forEach((assistant, index) => {
      console.log(`   ${index + 1}. ${assistant.name || 'Unnamed'} (${assistant.id})`);
    });
  } else {
    console.log('   ⚠️ No relevant assistants found for attorney "damon"');
    console.log('   This explains why the dropdown is empty!');
  }

  return relevantAssistants;
}

async function testSupabaseConnection() {
  console.log('\n3️⃣ Testing Supabase Connection...');
  
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    
    // Test connection by fetching attorney data
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('id, email, firm_name, subdomain, vapi_assistant_id')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      throw error;
    }

    console.log('✅ Supabase connection successful');
    console.log('   Attorney data:', {
      id: attorneys.id,
      email: attorneys.email,
      firm_name: attorneys.firm_name,
      subdomain: attorneys.subdomain,
      assistant_id: attorneys.vapi_assistant_id
    });

    return attorneys;
  } catch (error) {
    console.log(`❌ Supabase connection failed: ${error.message}`);
    return null;
  }
}

async function testAssistantUIConfigService() {
  console.log('\n4️⃣ Testing Assistant UI Config Service...');
  
  // Simulate the assistantUIConfigService logic
  const mockAttorneyId = '87756a2c-a398-43f2-889a-b8815684df71';
  const mockAssistantId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
  
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    
    // Check if there's a UI config for this assistant
    const { data: config, error } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('attorney_id', mockAttorneyId)
      .eq('assistant_id', mockAssistantId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
      throw error;
    }

    console.log('✅ Assistant UI Config Service test completed');
    console.log(`   Config exists: ${!!config}`);
    
    if (config) {
      console.log('   Config data:', {
        id: config.id,
        is_current: config.is_current,
        created_at: config.created_at
      });
    }

    return config;
  } catch (error) {
    console.log(`❌ Assistant UI Config Service failed: ${error.message}`);
    return null;
  }
}

async function testDropdownPopulation(relevantAssistants, attorney) {
  console.log('\n5️⃣ Testing Dropdown Population Logic...');
  
  if (!relevantAssistants || relevantAssistants.length === 0) {
    console.log('❌ Cannot test dropdown - no relevant assistants');
    return;
  }

  // Simulate the dropdown population
  const dropdownOptions = [];
  
  // Add default option
  dropdownOptions.push({ value: '', text: 'Select Assistant' });
  
  // Add relevant assistants
  relevantAssistants.forEach(assistant => {
    dropdownOptions.push({
      value: assistant.id,
      text: assistant.name || `Assistant ${assistant.id.slice(0, 8)}`
    });
  });
  
  // Add create new option
  dropdownOptions.push({ value: 'create_new', text: 'Create New Assistant' });
  
  console.log(`✅ Dropdown would show ${dropdownOptions.length} options:`);
  dropdownOptions.forEach((option, index) => {
    const marker = option.value === attorney?.vapi_assistant_id ? ' ← CURRENT' : '';
    console.log(`   ${index + 1}. "${option.text}" (${option.value})${marker}`);
  });
  
  // Check if current assistant would be selected
  const currentAssistantOption = dropdownOptions.find(opt => opt.value === attorney?.vapi_assistant_id);
  if (currentAssistantOption) {
    console.log(`✅ Current assistant "${currentAssistantOption.text}" would be selected`);
  } else {
    console.log(`⚠️ Current assistant "${attorney?.vapi_assistant_id}" not found in dropdown options`);
  }
}

async function runDiagnosticTest() {
  try {
    // Test 1: Direct Vapi API
    const allAssistants = await testDirectVapiAPI();
    
    // Test 2: Assistant filtering
    const relevantAssistants = await testAssistantFiltering(allAssistants);
    
    // Test 3: Supabase connection
    const attorney = await testSupabaseConnection();
    
    // Test 4: Assistant UI Config Service
    await testAssistantUIConfigService();
    
    // Test 5: Dropdown population
    await testDropdownPopulation(relevantAssistants, attorney);
    
    // Final diagnosis
    console.log('\n🎯 DIAGNOSIS SUMMARY');
    console.log('===================');
    
    if (!allAssistants || allAssistants.length === 0) {
      console.log('❌ ROOT CAUSE: Cannot fetch assistants from Vapi API');
      console.log('   - Check VAPI_PRIVATE_KEY environment variable');
      console.log('   - Verify Vapi API access');
    } else if (!relevantAssistants || relevantAssistants.length === 0) {
      console.log('❌ ROOT CAUSE: No assistants match filtering criteria');
      console.log('   - Assistant names don\'t contain "damon", "kost", or "legalscout"');
      console.log('   - Update assistant names or modify filtering logic');
    } else if (!attorney) {
      console.log('❌ ROOT CAUSE: Cannot fetch attorney data from Supabase');
      console.log('   - Check Supabase connection');
      console.log('   - Verify attorney record exists');
    } else {
      console.log('✅ All components working - dropdown should populate correctly');
      console.log('   - Check browser console for JavaScript errors');
      console.log('   - Verify EnhancedAssistantDropdown component is rendering');
    }
    
  } catch (error) {
    console.log(`\n❌ Test failed with error: ${error.message}`);
    console.log(error.stack);
  }
}

// Run the test
runDiagnosticTest();
