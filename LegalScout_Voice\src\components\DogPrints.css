/* Dog Paw Print Animation Styles */

.dog-paw {
  position: absolute;
  width: 5px;
  height: 8px;
  border-radius: 50% 50% 50% 50%;
  opacity: 0.45;
  will-change: opacity, transform;
  transition: opacity 0.2s ease;
  pointer-events: none;
  box-shadow: none !important;
  z-index: 6;
}

/* The dog paw pad (main pad) */
.dog-paw::before {
  content: '';
  position: absolute;
  background-color: inherit;
  width: 4px;
  height: 5px;
  border-radius: 50% 50% 45% 45%;
  bottom: 0;
  left: 0.5px;
  box-shadow: none !important;
  transition: opacity 0.2s ease;
}

/* The dog paw arc for toes */
.dog-paw::after {
  content: '';
  position: absolute;
  background-color: inherit;
  width: 9px;
  height: 3px;
  bottom: 5px;
  left: -2px;
  border-radius: 48% 48% 48% 48% / 70% 70% 40% 40%;
  transform: rotate(0deg);
  box-shadow: none !important;
  transition: opacity 0.2s ease;
}

/* Individual toes by using pseudo-elements */
.dog-paw .toe {
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  transform-origin: center;
  opacity: 0.9;
  box-shadow: none !important;
  z-index: 7;
  background-color: inherit;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Toe positions - creates a paw shape */
.toe-1 {
  top: -4px;
  left: -2px;
}

.toe-2 {
  top: -4px;
  left: 2px;
}

.toe-3 {
  top: -4px;
  left: 0px;
  transform: translateY(-2px);
}

/* Left paw specific styling */
.left-paw {
  transform: rotate(0deg);
}

.left-paw .toe-1 {
  left: -0.25px;
}

.left-paw .toe-2 {
  left: 1.5px;
}

.left-paw .toe-3 {
  left: 3px;
}

/* Right paw specific styling */
.right-paw {
  transform: rotate(0deg);
}

.right-paw .toe-1 {
  left: -0.25px;
}

.right-paw .toe-2 {
  left: 1.5px;
}

.right-paw .toe-3 {
  left: 3px;
}

/* Enhanced fade out animation for paws */
.fade-out {
  animation: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
  visibility: hidden !important;
  position: absolute !important;
  display: none !important;
  z-index: -10 !important;
  transform: scale(0) !important;
  transition: all 0.2s ease !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.fade-out::before, 
.fade-out::after,
.fade-out *,
.fade-out .toe {
  opacity: 0 !important;
  visibility: hidden !important;
  display: none !important;
  position: absolute !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.2s ease !important;
}

/* Ensure elements in logo area are completely hidden */
.dog-paw:not(.fade-out) {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Force hide any elements that might get stuck */
[class*="toe"],
[class*="paw"] {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

[class*="toe"].fade-out,
[class*="paw"].fade-out {
  opacity: 0 !important;
  visibility: hidden !important;
  display: none !important;
}

@keyframes fadeOut {
  to {
    opacity: 0;
    visibility: hidden;
    display: none;
    transform: scale(0);
  }
}

/* Ripple animation */
@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Bone cursor for light mode */
.bone-cursor {
  cursor: url('/dogbone.jpg') 8 8, auto;
  cursor: -webkit-image-set(
    url('/dogbone.jpg') 1x,
    url('/dogbone.jpg') 2x
  ) 8 8, auto;
}

/* Bone cursor for dark mode */
.dark-theme .bone-cursor {
  cursor: url('/dogbone.jpg') 8 8, auto;
  cursor: -webkit-image-set(
    url('/dogbone.jpg') 1x,
    url('/dogbone.jpg') 2x
  ) 8 8, auto;
  filter: invert(1);
}

/* Dog house icon */
.dog-house {
  position: absolute;
  width: 40px;
  height: 40px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-out;
  pointer-events: none;
  z-index: 10;
}

.dog-house.visible {
  opacity: 1;
  pointer-events: auto;
  animation: bounce-in 0.6s ease-out;
}

.dog-house:hover {
  transform: translate(-50%, -105%) scale(1.05);
  cursor: pointer;
  filter: brightness(1.1);
}

@keyframes bounce-in {
  0% {
    transform: translate(-50%, -120%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -90%) scale(1.2);
    opacity: 0.7;
  }
  70% {
    transform: translate(-50%, -105%) scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: translate(-50%, -100%) scale(1);
    opacity: 1;
  }
}

/* Enhanced bone projectile styles */
.bone-projectile {
  position: absolute;
  width: 16px;
  height: 8px;
  pointer-events: none;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.bone-projectile.active {
  opacity: 1;
  animation: boneAppear 0.2s ease-in;
}

@keyframes boneAppear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.2);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Trail particle animations */
.bone-trail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.trail-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: trailFade 0.8s ease-out infinite;
}

@keyframes trailFade {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
}

/* Add responsiveness */
@media (max-width: 768px) {
  .dog-paw {
    width: 4px;
    height: 6px;
    transform: scale(0.85);
  }
  
  .dog-paw::before {
    width: 3px;
    height: 4px;
  }
  
  .dog-paw::after {
    width: 7px;
    height: 2.5px;
    bottom: 4px;
  }
  
  .dog-paw .toe {
    width: 2px;
    height: 2.5px;
  }
  
  .dog-house {
    width: 30px;
    height: 30px;
  }
} 