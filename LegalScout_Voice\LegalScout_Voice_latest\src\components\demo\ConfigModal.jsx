import React from 'react';
import './ConfigModal.css';

const ConfigModal = ({
  isOpen,
  onClose,
  firmUrl,
  handleUrlChange,
  isValidUrl,
  handleUrlSubmit,
  isLoading,
  selectedPracticeArea,
  handlePracticeAreaChange,
  practiceAreas,
  setConfigMode
}) => {
  console.log('ConfigModal rendered with isOpen:', isOpen);

  if (!isOpen) return null;

  console.log('ConfigModal is open, rendering content');
  return (
    <div className="config-modal-overlay">
      <div className="config-modal">
        <button className="close-button" onClick={onClose}>×</button>
        <h2>Configure Your AI Assistant</h2>

        <div className="modal-content">
          <div className="start-option glass-effect">
            <h3>Use Your Website</h3>
            <div className="input-group">
              <label htmlFor="firmUrl">Enter your firm's website URL</label>
              <div className="url-input-group">
                <input
                  type="url"
                  id="firmUrl"
                  value={firmUrl}
                  onChange={handleUrlChange}
                  placeholder="https://www.yourfirm.com"
                  className={`modern-input ${isValidUrl ? 'valid' : ''}`}
                />
              </div>
              {isValidUrl && (
                <button
                  className="begin-config modern-button"
                  onClick={() => {
                    handleUrlSubmit();
                    setConfigMode('manual');
                    onClose();
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex-center">
                      <svg className="spinner" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <circle className="spinner-circle" cx="12" cy="12" r="10" />
                      </svg>
                      Processing...
                    </span>
                  ) : 'Auto-Configure'}
                </button>
              )}
            </div>
          </div>

          {/* OR divider */}
          <div className="or-divider">
            <div className="divider-line"></div>
            <span className="divider-text">OR</span>
            <div className="divider-line"></div>
          </div>

          <div className="start-option glass-effect">
            <h3>Quick Start Template</h3>
            <div className="input-group">
              <label htmlFor="quickStart">Select your practice area</label>
              <select
                id="quickStart"
                value={selectedPracticeArea}
                onChange={(e) => {
                  // Check if handlePracticeAreaChange is a function
                  if (typeof handlePracticeAreaChange === 'function') {
                    handlePracticeAreaChange(e);
                  } else {
                    console.error('handlePracticeAreaChange is not a function');
                  }
                  setConfigMode('manual');
                  onClose();
                }}
                className="modern-select"
              >
                <option value="">Choose a practice area</option>
                {practiceAreas && Object.keys(practiceAreas).length > 0 ? (
                  Object.keys(practiceAreas).map((area) => (
                    <option key={area} value={area}>
                      {area}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>No practice areas available</option>
                )}
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfigModal;
