#!/usr/bin/env node

/**
 * ULTRA-THINKING Assistant Conflict Resolver
 * 
 * Root Cause: Data inconsistency between Supabase and Vapi
 * Solution: Validate, reconcile, and auto-fix assistant ID conflicts
 * 
 * Usage: node scripts/fix-assistant-conflict.js [email]
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_KEY
);

class AssistantConflictResolver {
  constructor() {
    this.vapiApiKey = process.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
  }

  /**
   * Get assistant from Vapi API
   */
  async getVapiAssistant(assistantId) {
    try {
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${this.vapiApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error(`Error fetching assistant ${assistantId}:`, error.message);
      return null;
    }
  }

  /**
   * List all assistants from Vapi
   */
  async listVapiAssistants() {
    try {
      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${this.vapiApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Vapi API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error listing assistants:', error.message);
      return [];
    }
  }

  /**
   * ULTRA-THINKING: Find the best assistant match for an attorney
   */
  async findBestAssistant(attorney) {
    console.log(`🔍 Finding best assistant for ${attorney.email}...`);
    
    const allAssistants = await this.listVapiAssistants();
    console.log(`📋 Found ${allAssistants.length} total assistants in Vapi`);

    const firmName = attorney.firm_name?.toLowerCase() || '';
    const potentialMatches = [];

    // Find potential matches
    for (const assistant of allAssistants) {
      const assistantName = assistant.name?.toLowerCase() || '';
      
      // LegalScout specific matching
      if (assistantName.includes('legalscout')) {
        potentialMatches.push({
          ...assistant,
          matchReason: 'LegalScout pattern match',
          score: assistantName.includes('assistant') ? 100 : 80
        });
      }
      
      // Firm name matching
      if (firmName && assistantName.includes(firmName)) {
        potentialMatches.push({
          ...assistant,
          matchReason: `Firm name match: ${firmName}`,
          score: assistantName === firmName ? 90 : 70
        });
      }
    }

    if (potentialMatches.length === 0) {
      console.log('❌ No potential matches found');
      return null;
    }

    // ULTRA-THINKING: Score and select best match
    const scoredMatches = potentialMatches.map(match => {
      let finalScore = match.score;
      
      // Prefer "Assistant" in name (more complete)
      if (match.name.toLowerCase().includes('assistant')) {
        finalScore += 20;
      }
      
      // Prefer older assistants (more established)
      const createdAt = new Date(match.createdAt);
      const daysSinceCreation = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreation > 1) {
        finalScore += Math.min(daysSinceCreation, 30);
      }
      
      return { ...match, finalScore };
    });

    // Sort by final score (highest first)
    scoredMatches.sort((a, b) => b.finalScore - a.finalScore);

    const bestMatch = scoredMatches[0];
    
    console.log(`🎯 Best match: "${bestMatch.name}" (ID: ${bestMatch.id})`);
    console.log(`   Score: ${bestMatch.finalScore}, Reason: ${bestMatch.matchReason}`);
    
    if (scoredMatches.length > 1) {
      console.log(`📊 Other matches:`);
      scoredMatches.slice(1).forEach(match => {
        console.log(`   - "${match.name}" (Score: ${match.finalScore})`);
      });
    }

    return bestMatch;
  }

  /**
   * Update attorney's assistant ID in Supabase
   */
  async updateAttorneyAssistant(attorneyId, assistantId) {
    try {
      const { error } = await supabase
        .from('attorneys')
        .update({ 
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId);

      if (error) {
        throw error;
      }

      console.log(`✅ Updated attorney ${attorneyId} with assistant ${assistantId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to update attorney:`, error.message);
      return false;
    }
  }

  /**
   * Resolve conflict for a specific attorney
   */
  async resolveAttorneyConflict(email) {
    console.log(`\n🔧 Resolving assistant conflict for: ${email}`);
    
    try {
      // Get attorney from database
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', email)
        .single();

      if (error || !attorney) {
        console.error(`❌ Attorney not found: ${email}`);
        return false;
      }

      console.log(`👤 Attorney: ${attorney.firm_name} (${attorney.email})`);
      console.log(`🤖 Current assistant ID: ${attorney.vapi_assistant_id || 'None'}`);

      // Check if current assistant exists in Vapi
      let currentAssistantValid = false;
      if (attorney.vapi_assistant_id) {
        const currentAssistant = await this.getVapiAssistant(attorney.vapi_assistant_id);
        if (currentAssistant) {
          console.log(`✅ Current assistant "${currentAssistant.name}" is valid in Vapi`);
          currentAssistantValid = true;
        } else {
          console.log(`❌ Current assistant ID not found in Vapi`);
        }
      }

      // If current assistant is valid, no action needed
      if (currentAssistantValid) {
        console.log(`✅ No action needed - assistant is properly connected`);
        return true;
      }

      // Find the best assistant match
      const bestMatch = await this.findBestAssistant(attorney);
      
      if (!bestMatch) {
        console.log(`❌ No suitable assistant found for ${email}`);
        console.log(`💡 Suggestion: Create a new assistant for this attorney`);
        return false;
      }

      // Update the database
      const updateSuccess = await this.updateAttorneyAssistant(attorney.id, bestMatch.id);
      
      if (updateSuccess) {
        console.log(`🎉 Successfully resolved conflict for ${email}`);
        console.log(`   New assistant: "${bestMatch.name}" (${bestMatch.id})`);
        return true;
      } else {
        console.log(`❌ Failed to update database for ${email}`);
        return false;
      }

    } catch (error) {
      console.error(`💥 Error resolving conflict for ${email}:`, error.message);
      return false;
    }
  }

  /**
   * Resolve conflicts for all attorneys
   */
  async resolveAllConflicts() {
    console.log(`🚀 Resolving assistant conflicts for all attorneys...\n`);
    
    try {
      // Get all attorneys
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('*');

      if (error) {
        throw error;
      }

      console.log(`📋 Found ${attorneys.length} attorneys to check`);

      let resolved = 0;
      let failed = 0;

      for (const attorney of attorneys) {
        const success = await this.resolveAttorneyConflict(attorney.email);
        if (success) {
          resolved++;
        } else {
          failed++;
        }
      }

      console.log(`\n📊 Summary:`);
      console.log(`   ✅ Resolved: ${resolved}`);
      console.log(`   ❌ Failed: ${failed}`);
      console.log(`   📋 Total: ${attorneys.length}`);

      return { resolved, failed, total: attorneys.length };

    } catch (error) {
      console.error(`💥 Error resolving all conflicts:`, error.message);
      return null;
    }
  }

  /**
   * Main execution
   */
  async run() {
    const email = process.argv[2];
    
    console.log(`🔧 ULTRA-THINKING Assistant Conflict Resolver`);
    console.log(`🎯 Root Cause: Data inconsistency between Supabase and Vapi`);
    console.log(`💡 Solution: Validate, reconcile, and auto-fix conflicts\n`);

    try {
      if (email) {
        await this.resolveAttorneyConflict(email);
      } else {
        await this.resolveAllConflicts();
      }
    } catch (error) {
      console.error(`💥 Fatal error:`, error.message);
      process.exit(1);
    }
  }
}

// Run the resolver
const resolver = new AssistantConflictResolver();
resolver.run();
