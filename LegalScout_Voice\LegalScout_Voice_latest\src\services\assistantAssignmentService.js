/**
 * Assistant Assignment Service
 * 
 * Manages which assistants are assigned to which OAuth users
 * Implements RLS-style filtering for assistant access control
 */

import { supabase } from '../lib/supabase';
import { vapiMcpService } from './vapiMcpService';

class AssistantAssignmentService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get assistants assigned to a specific OAuth user
   * @param {string} oauthEmail - OAuth email (e.g., <EMAIL>)
   * @param {string} attorneyId - Attorney ID for fallback
   * @returns {Promise<Array>} Array of assistant IDs assigned to this user
   */
  async getAssignedAssistantIds(oauthEmail, attorneyId) {
    try {
      const cacheKey = `${oauthEmail}-${attorneyId}`;
      
      // Check cache first
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheExpiry) {
          console.log('📋 Using cached assistant assignments for:', oauthEmail);
          return cached.assistantIds;
        }
      }

      console.log('🔍 Loading assistant assignments for OAuth user:', oauthEmail);

      // Get assignments from Supabase
      const { data: assignments, error } = await supabase
        .from('assistant_assignments')
        .select('assistant_id')
        .eq('oauth_email', oauthEmail)
        .eq('is_active', true);

      if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist
        console.error('Error loading assistant assignments:', error);
      }

      let assignedIds = [];

      if (assignments && assignments.length > 0) {
        assignedIds = assignments.map(a => a.assistant_id);
        console.log(`✅ Found ${assignedIds.length} assigned assistants for ${oauthEmail}`);
      } else {
        // Fallback: Use attorney's primary assistant and name-based matching
        console.log('📋 No explicit assignments found, using fallback logic');
        assignedIds = await this.getFallbackAssistantIds(oauthEmail, attorneyId);
      }

      // Cache the result
      this.cache.set(cacheKey, {
        assistantIds: assignedIds,
        timestamp: Date.now()
      });

      return assignedIds;

    } catch (error) {
      console.error('Error getting assigned assistant IDs:', error);
      // Return fallback on error
      return await this.getFallbackAssistantIds(oauthEmail, attorneyId);
    }
  }

  /**
   * Fallback logic for when no explicit assignments exist
   * @param {string} oauthEmail - OAuth email
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Array>} Array of assistant IDs
   */
  async getFallbackAssistantIds(oauthEmail, attorneyId) {
    try {
      console.log('🔄 Using fallback assistant assignment logic');

      // Get attorney data
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id, firm_name, subdomain')
        .eq('id', attorneyId)
        .single();

      if (error) {
        console.error('Error loading attorney data:', error);
        return [];
      }

      const assignedIds = [];

      // Always include the attorney's primary assistant(s)
      if (attorney.vapi_assistant_id) {
        assignedIds.push(attorney.vapi_assistant_id);
        console.log(`📋 Added primary assistant: ${attorney.vapi_assistant_id}`);
      }

      // Also include current_assistant_id if different
      if (attorney.current_assistant_id && attorney.current_assistant_id !== attorney.vapi_assistant_id) {
        assignedIds.push(attorney.current_assistant_id);
        console.log(`📋 Added current assistant: ${attorney.current_assistant_id}`);
      }

      // Get all assistants and filter by OAuth user patterns
      const allAssistants = await vapiMcpService.listAssistants();
      
      const relevantAssistants = allAssistants.filter(assistant => {
        const name = assistant.name?.toLowerCase() || '';
        const firmName = attorney.firm_name?.toLowerCase() || '';
        const subdomain = attorney.subdomain?.toLowerCase() || '';
        
        // OAuth email-based matching
        if (oauthEmail.includes('damonandlaurakost') && name.includes('kost')) return true;
        if (oauthEmail.includes('damon') && name.includes('damon')) return true;
        if (oauthEmail.includes('robert') && name.includes('robert')) return true;
        
        // Firm/subdomain-based matching
        if (firmName && name.includes(firmName)) return true;
        if (subdomain && name.includes(subdomain)) return true;
        
        // Generic LegalScout assistants (for development)
        if (name.includes('legalscout')) return true;
        
        return false;
      });

      // Add relevant assistant IDs
      relevantAssistants.forEach(assistant => {
        if (!assignedIds.includes(assistant.id)) {
          assignedIds.push(assistant.id);
        }
      });

      console.log(`✅ Fallback logic found ${assignedIds.length} assistants for ${oauthEmail}`);
      return assignedIds;

    } catch (error) {
      console.error('Error in fallback assistant assignment:', error);
      return [];
    }
  }

  /**
   * Assign an assistant to an OAuth user
   * @param {string} oauthEmail - OAuth email
   * @param {string} assistantId - Assistant ID to assign
   * @param {string} assignedBy - Who assigned this (attorney ID)
   * @returns {Promise<boolean>} Success status
   */
  async assignAssistant(oauthEmail, assistantId, assignedBy) {
    try {
      console.log(`🔗 Assigning assistant ${assistantId} to ${oauthEmail}`);

      const { data, error } = await supabase
        .from('assistant_assignments')
        .upsert({
          oauth_email: oauthEmail,
          assistant_id: assistantId,
          assigned_by: assignedBy,
          assigned_at: new Date().toISOString(),
          is_active: true
        }, {
          onConflict: 'oauth_email,assistant_id'
        })
        .select();

      if (error) {
        console.error('Error assigning assistant:', error);
        return false;
      }

      // Clear cache for this user
      this.clearCacheForUser(oauthEmail);

      console.log('✅ Assistant assigned successfully');
      return true;

    } catch (error) {
      console.error('Error assigning assistant:', error);
      return false;
    }
  }

  /**
   * Remove an assistant assignment
   * @param {string} oauthEmail - OAuth email
   * @param {string} assistantId - Assistant ID to unassign
   * @returns {Promise<boolean>} Success status
   */
  async unassignAssistant(oauthEmail, assistantId) {
    try {
      console.log(`🔗 Unassigning assistant ${assistantId} from ${oauthEmail}`);

      const { error } = await supabase
        .from('assistant_assignments')
        .update({ is_active: false })
        .eq('oauth_email', oauthEmail)
        .eq('assistant_id', assistantId);

      if (error) {
        console.error('Error unassigning assistant:', error);
        return false;
      }

      // Clear cache for this user
      this.clearCacheForUser(oauthEmail);

      console.log('✅ Assistant unassigned successfully');
      return true;

    } catch (error) {
      console.error('Error unassigning assistant:', error);
      return false;
    }
  }

  /**
   * Get all assignments for an OAuth user
   * @param {string} oauthEmail - OAuth email
   * @returns {Promise<Array>} Array of assignment records
   */
  async getAssignments(oauthEmail) {
    try {
      const { data, error } = await supabase
        .from('assistant_assignments')
        .select('*')
        .eq('oauth_email', oauthEmail)
        .eq('is_active', true)
        .order('assigned_at', { ascending: false });

      if (error) {
        console.error('Error getting assignments:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Error getting assignments:', error);
      return [];
    }
  }

  /**
   * Clear cache for a specific user
   * @param {string} oauthEmail - OAuth email
   */
  clearCacheForUser(oauthEmail) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.startsWith(oauthEmail)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Clear all cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Check if a user has access to a specific assistant
   * @param {string} oauthEmail - OAuth email
   * @param {string} assistantId - Assistant ID
   * @param {string} attorneyId - Attorney ID for fallback
   * @returns {Promise<boolean>} Whether user has access
   */
  async hasAccess(oauthEmail, assistantId, attorneyId) {
    try {
      const assignedIds = await this.getAssignedAssistantIds(oauthEmail, attorneyId);
      return assignedIds.includes(assistantId);
    } catch (error) {
      console.error('Error checking assistant access:', error);
      return false;
    }
  }
}

// Export singleton instance
export const assistantAssignmentService = new AssistantAssignmentService();
export default assistantAssignmentService;
