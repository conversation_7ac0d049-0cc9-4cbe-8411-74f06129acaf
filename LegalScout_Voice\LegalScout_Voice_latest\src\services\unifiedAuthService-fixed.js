/**
 * FIXED Unified Authentication Service
 * 
 * This service fixes the OAuth callback and authentication issues by:
 * 1. Using the fixed Supabase client
 * 2. Implementing proper retry logic for OAuth callbacks
 * 3. Better error handling and state management
 * 4. Eliminating the need for fallback authentication
 */

import { 
  signInWithGoogle, 
  getSession, 
  getCurrentUser, 
  signOut,
  handleOAuthCallback,
  getSupabaseClient
} from '../lib/supabase-fixed.js';

class UnifiedAuthServiceFixed {
  constructor() {
    this.authState = {
      user: null,
      session: null,
      loading: false,
      error: null,
      initialized: false
    };
    this.listeners = new Set();
    this.initializationPromise = null;
  }

  /**
   * Initialize authentication service with proper error handling
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  async _performInitialization() {
    console.log('🔐 [UnifiedAuth-Fixed] Initializing authentication service...');
    
    this.authState.loading = true;
    this.authState.error = null;
    this.notifyListeners();
    
    try {
      // Ensure Supabase client is ready
      await getSupabaseClient();
      
      // Check for existing session with retry logic
      const session = await this._getSessionWithRetry();
      const user = session?.user || null;
      
      this.authState = {
        user,
        session,
        loading: false,
        error: null,
        initialized: true
      };
      
      console.log('✅ [UnifiedAuth-Fixed] Initialized successfully with user:', user?.email || 'No user');
      this.notifyListeners();
      
      return this.authState;
    } catch (error) {
      console.error('❌ [UnifiedAuth-Fixed] Initialization error:', error);
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: error.message,
        initialized: true
      };
      this.notifyListeners();
      return this.authState;
    }
  }

  /**
   * Get session with retry logic
   */
  async _getSessionWithRetry(maxRetries = 3, delay = 500) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const session = await getSession();
        if (session) {
          return session;
        }
        
        if (attempt < maxRetries) {
          console.log(`⚠️ [UnifiedAuth-Fixed] No session found, retrying in ${delay}ms... (${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        console.error(`❌ [UnifiedAuth-Fixed] Session attempt ${attempt} failed:`, error);
        if (attempt === maxRetries) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    return null;
  }

  /**
   * Sign in with Google OAuth
   */
  async signInWithGoogle() {
    console.log('🔐 [UnifiedAuth-Fixed] Starting Google sign-in...');
    
    this.authState.loading = true;
    this.authState.error = null;
    this.notifyListeners();
    
    try {
      const result = await signInWithGoogle();
      
      if (result.error) {
        throw new Error(result.error.message || 'Authentication failed');
      }
      
      console.log('✅ [UnifiedAuth-Fixed] Google sign-in initiated successfully');
      
      // Note: The actual user/session will be set after redirect callback
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ [UnifiedAuth-Fixed] Google sign-in error:', error);
      
      this.authState.loading = false;
      this.authState.error = error.message;
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Enhanced OAuth callback handling with proper retry logic
   */
  async handleOAuthCallback() {
    console.log('🔐 [UnifiedAuth-Fixed] Handling OAuth callback...');
    
    this.authState.loading = true;
    this.authState.error = null;
    this.notifyListeners();
    
    try {
      // Use the enhanced callback handler from supabase-fixed
      const result = await handleOAuthCallback(5, 1000); // 5 retries, 1 second delay
      
      if (!result.success) {
        throw new Error(result.error || 'OAuth callback failed');
      }
      
      const { user, session } = result;
      
      this.authState = {
        user,
        session,
        loading: false,
        error: null,
        initialized: true
      };
      
      console.log('✅ [UnifiedAuth-Fixed] OAuth callback handled successfully for:', user.email);
      this.notifyListeners();
      
      return { success: true, user, session };
    } catch (error) {
      console.error('❌ [UnifiedAuth-Fixed] OAuth callback error:', error);
      
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: error.message,
        initialized: true
      };
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Sign out user
   */
  async signOut() {
    console.log('🔐 [UnifiedAuth-Fixed] Signing out...');
    
    this.authState.loading = true;
    this.notifyListeners();
    
    try {
      const result = await signOut();
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Sign out failed');
      }
      
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: null,
        initialized: true
      };
      
      console.log('✅ [UnifiedAuth-Fixed] Successfully signed out');
      this.notifyListeners();
      
      return { success: true };
    } catch (error) {
      console.error('❌ [UnifiedAuth-Fixed] Sign out error:', error);
      
      this.authState.loading = false;
      this.authState.error = error.message;
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current authentication state
   */
  getAuthState() {
    return { ...this.authState };
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!(this.authState.user && this.authState.session);
  }

  /**
   * Subscribe to authentication state changes
   */
  subscribe(listener) {
    this.listeners.add(listener);
    
    // Immediately call with current state
    listener(this.getAuthState());
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  notifyListeners() {
    const state = this.getAuthState();
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('❌ [UnifiedAuth-Fixed] Listener error:', error);
      }
    });
  }

  /**
   * Refresh current session
   */
  async refreshSession() {
    console.log('🔄 [UnifiedAuth-Fixed] Refreshing session...');
    
    try {
      const session = await this._getSessionWithRetry();
      const user = session?.user || null;
      
      this.authState.user = user;
      this.authState.session = session;
      this.authState.error = null;
      
      console.log('✅ [UnifiedAuth-Fixed] Session refreshed for:', user?.email || 'No user');
      this.notifyListeners();
      
      return { success: true, user, session };
    } catch (error) {
      console.error('❌ [UnifiedAuth-Fixed] Session refresh error:', error);
      
      this.authState.error = error.message;
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Reset authentication state (for testing/debugging)
   */
  reset() {
    console.log('🔄 [UnifiedAuth-Fixed] Resetting authentication state...');
    
    this.authState = {
      user: null,
      session: null,
      loading: false,
      error: null,
      initialized: false
    };
    
    this.initializationPromise = null;
    this.notifyListeners();
  }
}

// Create singleton instance
const unifiedAuthServiceFixed = new UnifiedAuthServiceFixed();

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  unifiedAuthServiceFixed.initialize().catch(error => {
    console.error('❌ [UnifiedAuth-Fixed] Auto-initialization failed:', error);
  });
  
  // Make available globally for debugging
  window.unifiedAuthServiceFixed = unifiedAuthServiceFixed;
}

export default unifiedAuthServiceFixed;
