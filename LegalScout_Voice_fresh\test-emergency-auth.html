<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
        .warning { color: #f59e0b; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🚨 Emergency Auth Test</h1>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="results"></div>
        <div id="logs" class="log"></div>
    </div>

    <div class="test-container">
        <h2>Manual Tests</h2>
        <button onclick="testEmergencyAuthImport()">Test Emergency Auth Import</button>
        <button onclick="testEmergencyAuthFunction()">Test getCurrentUser Function</button>
        <button onclick="testWindowGlobal()">Test Window Global</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script type="module">
        const resultsDiv = document.getElementById('results');
        const logsDiv = document.getElementById('logs');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function showResult(test, success, message) {
            const resultDiv = document.createElement('div');
            resultDiv.className = success ? 'success' : 'error';
            resultDiv.innerHTML = `${success ? '✅' : '❌'} ${test}: ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        // Test 1: Import emergencyAuth
        window.testEmergencyAuthImport = async function() {
            try {
                log('Testing emergencyAuth import...', 'info');
                const { emergencyAuth } = await import('./src/lib/supabase.js');
                
                if (emergencyAuth) {
                    log('✅ emergencyAuth imported successfully', 'success');
                    showResult('Import Test', true, 'emergencyAuth object imported');
                    
                    // Check if it has the expected method
                    if (typeof emergencyAuth.getCurrentUser === 'function') {
                        log('✅ getCurrentUser method found', 'success');
                        showResult('Method Test', true, 'getCurrentUser method exists');
                    } else {
                        log('❌ getCurrentUser method not found', 'error');
                        showResult('Method Test', false, 'getCurrentUser method missing');
                    }
                } else {
                    log('❌ emergencyAuth is undefined', 'error');
                    showResult('Import Test', false, 'emergencyAuth is undefined');
                }
            } catch (error) {
                log(`❌ Import failed: ${error.message}`, 'error');
                showResult('Import Test', false, error.message);
            }
        };

        // Test 2: Test getCurrentUser function
        window.testEmergencyAuthFunction = async function() {
            try {
                log('Testing emergencyAuth.getCurrentUser()...', 'info');
                const { emergencyAuth } = await import('./src/lib/supabase.js');
                
                const result = await emergencyAuth.getCurrentUser();
                log(`getCurrentUser result: ${JSON.stringify(result)}`, 'info');
                
                if (result && typeof result === 'object') {
                    showResult('Function Test', true, 'getCurrentUser returned an object');
                    
                    if (result.user) {
                        log(`✅ User found: ${result.user.email}`, 'success');
                    } else if (result.error) {
                        log(`⚠️ No user found: ${result.error}`, 'warning');
                    }
                } else {
                    showResult('Function Test', false, 'getCurrentUser returned unexpected result');
                }
            } catch (error) {
                log(`❌ Function test failed: ${error.message}`, 'error');
                showResult('Function Test', false, error.message);
            }
        };

        // Test 3: Test window global
        window.testWindowGlobal = function() {
            try {
                log('Testing window.emergencyAuth global...', 'info');
                
                if (window.emergencyAuth) {
                    log('✅ window.emergencyAuth is available', 'success');
                    showResult('Global Test', true, 'window.emergencyAuth exists');
                    
                    if (typeof window.emergencyAuth.getCurrentUser === 'function') {
                        log('✅ window.emergencyAuth.getCurrentUser method found', 'success');
                    } else {
                        log('❌ window.emergencyAuth.getCurrentUser method not found', 'error');
                    }
                } else {
                    log('❌ window.emergencyAuth is not available', 'error');
                    showResult('Global Test', false, 'window.emergencyAuth not found');
                }
            } catch (error) {
                log(`❌ Global test failed: ${error.message}`, 'error');
                showResult('Global Test', false, error.message);
            }
        };

        // Clear logs
        window.clearLogs = function() {
            logsDiv.innerHTML = '';
            resultsDiv.innerHTML = '';
        };

        // Auto-run tests on page load
        window.addEventListener('load', async () => {
            log('🚀 Starting automatic tests...', 'info');
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for modules to load
            
            await testEmergencyAuthImport();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEmergencyAuthFunction();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testWindowGlobal();
            
            log('✅ Automatic tests completed', 'success');
        });
    </script>
</body>
</html>
