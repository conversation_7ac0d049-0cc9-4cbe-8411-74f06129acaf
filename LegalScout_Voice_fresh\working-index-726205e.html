<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- CRITICAL: CSP Meta Tag to Allow Eval (fixes module loading) -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live; script-src-elem 'self' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; media-src 'self' blob: data: https:; connect-src 'self' https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://cdn.vapi.ai https://vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live wss: ws:; frame-src 'self' https://c.daily.co https://*.daily.co; worker-src 'self' blob: data:; object-src 'none'; base-uri 'self';">

    <!-- FORCE NEW ASSISTANT: DISABLED - Breaks OAuth by clearing localStorage -->
    <!-- <script src="/force-new-assistant.js"></script> -->

    <!-- EMERGENCY API KEY FIX: Must load first to fix API key issues -->
    <script src="/emergency-api-key-fix.js"></script>

    <!-- CRITICAL PRODUCTION FIX: Must load first to fix all production issues -->
    <script src="/critical-production-fix.js"></script>

    <!-- SIMPLE FIX: Correct assistant ID and public key -->
    <script>
      // Simple fix: ensure both public and secret keys are available globally
      window.VITE_VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
      window.VITE_VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
      window.VAPI_TOKEN = '6734febc-fc65-4669-93b0-929b31ff6564';
      console.log('✅ Vapi keys set globally');

      // Fix: Force correct assistant ID for damon subdomain
      const correctAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

      // Clear wrong assistant from localStorage and set correct one
      try {
        const attorneyData = JSON.parse(localStorage.getItem('attorney_571390ac-5a83-46b2-ad3a-18b9cf39d701') || '{}');
        if (attorneyData.assistant_id !== correctAssistantId) {
          attorneyData.assistant_id = correctAssistantId;
          localStorage.setItem('attorney_571390ac-5a83-46b2-ad3a-18b9cf39d701', JSON.stringify(attorneyData));
          console.log('✅ Fixed assistant ID in localStorage');
        }
      } catch (e) {
        console.log('Could not update localStorage:', e.message);
      }

      // Fix: Ensure Supabase key is available globally (needed to load assistant by domain)
      window.VITE_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
      window.VITE_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
      window.VITE_SUPABASE_ANON_KEY = window.VITE_SUPABASE_KEY;
      console.log('✅ Supabase keys set globally - should load correct assistant by domain');
    </script>

    <!-- LOTS MORE FIX SCRIPTS... -->
    
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LegalScout</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    
    <!-- Load Supabase from CDN -->
    <script>
      // Only create Supabase client if not already created to prevent multiple instances
      if (!window.supabase) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
        script.onload = function() {
          if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
            console.log('Supabase loaded from CDN');
          }

          // Initialize Supabase client
          const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
          const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

          // Create Supabase client
          if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
              console.log('Creating Supabase client from CDN');
            }
            window.supabase = supabase.createClient(supabaseUrl, supabaseKey);
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
              console.log('Supabase client created from CDN');
            }
          } else {
            console.warn('Supabase not available from CDN');
          }
        };
        document.head.appendChild(script);
      } else if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
        console.log('Supabase client already exists, skipping CDN load');
      }
    </script>

    <!-- MORE FIX SCRIPTS... -->
    
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      // Remove any existing persistent call container
      (function() {
        function removePersistentCallContainer() {
          const persistentCallContainer = document.getElementById('persistent-call-container');
          if (persistentCallContainer) {
            persistentCallContainer.parentNode.removeChild(persistentCallContainer);
            console.log('Removed persistent call container');
          }
        }

        // Remove immediately
        removePersistentCallContainer();

        // Also set up a MutationObserver to remove it if it gets added again
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.id === 'persistent-call-container' ||
                    (node.nodeType === 1 && node.querySelector('#persistent-call-container'))) {
                  removePersistentCallContainer();
                }
              });
            }
          });
        });

        // Start observing the document body for changes
        observer.observe(document.body, { childList: true, subtree: true });
      })();
    </script>
  </body>
</html>
