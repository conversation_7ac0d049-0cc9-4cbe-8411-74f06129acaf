/**
 * API Routes
 *
 * This file defines the API routes for the application.
 */

import express from 'express';
import { createFunction, callFunction, listFunctions } from './ai-meta-mcp.js';
import {
  syncAttorneyProfileHandler,
  manageAuthStateHandler,
  validateConfigurationHandler,
  checkPreviewConsistencyHandler
} from './sync-tools.js';

const router = express.Router();

// AI Meta MCP routes
router.post('/ai-meta-mcp/create-function', createFunction);
router.post('/ai-meta-mcp/call-function', callFunction);
router.get('/ai-meta-mcp/list-functions', listFunctions);

// Sync Tools routes
router.post('/sync-tools/sync-attorney-profile', syncAttorneyProfileHandler);
router.post('/sync-tools/manage-auth-state', manageAuthStateHandler);
router.post('/sync-tools/validate-configuration', validateConfigurationHandler);
router.post('/sync-tools/check-preview-consistency', checkPreviewConsistencyHandler);

export default router;
