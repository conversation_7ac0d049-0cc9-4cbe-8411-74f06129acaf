export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Call MCP Gmail API to get user info
    const response = await fetch('https://api.makecomputer.io/v1/actions/gmail/get-people', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MCP_API_KEY}`
      },
      body: JSON.stringify(req.body)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to verify email');
    }

    // Extract email addresses from the response
    const emailAddresses = data.emailAddresses || [];
    
    // Format the response to match what the frontend expects
    return res.status(200).json({
      emailAddresses: emailAddresses.map(email => ({
        value: email.value || email.email,
        primary: email.primary || false,
        verified: email.verified || false
      }))
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ error: 'Failed to verify email' });
  }
} 