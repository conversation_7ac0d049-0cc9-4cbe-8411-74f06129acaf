/**
 * Enhanced Vapi Call Service
 *
 * This service handles all Vapi call functionality, including creating calls,
 * monitoring call status, and handling call events.
 *
 * It uses the VapiMcpService for communication with the Vapi API.
 */

import { supabase } from '../lib/supabase';
import { vapiMcpService } from './vapiMcpService';
import { enhancedVapiAssistantManager } from './EnhancedVapiAssistantManager';

class EnhancedVapiCallService {
  constructor() {
    this.mcpService = vapiMcpService;
    this.assistantManager = enhancedVapiAssistantManager;
    this.calls = new Map();
    this.eventListeners = new Map();
    this.initialized = false;
  }

  /**
   * Initialize the service
   * @returns {Promise<boolean>} - Initialization success status
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    // Connect to Vapi MCP service using SECRET key for server operations
    const apiKey = import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VITE_VAPI_PUBLIC_KEY;
    await this.mcpService.connect(apiKey);

    // Initialize assistant manager
    await this.assistantManager.initialize();

    this.initialized = true;
    return true;
  }

  /**
   * Create a new call
   * @param {string} attorneyId - Attorney ID
   * @param {string} phoneNumber - Customer phone number
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Created call
   */
  async createCall(attorneyId, phoneNumber, options = {}) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      console.log('[EnhancedVapiCallService] Creating call for attorney:', attorneyId);

      // Ensure attorney has an assistant
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error) {
        console.error('[EnhancedVapiCallService] Error getting attorney:', error);
        throw error;
      }

      let assistantId = attorney.vapi_assistant_id;

      // If attorney doesn't have an assistant, create one
      if (!assistantId) {
        console.log('[EnhancedVapiCallService] Attorney has no assistant, creating one');
        const assistant = await this.assistantManager.ensureAssistant(attorney);
        assistantId = assistant.vapi_assistant_id;
      }

      // Get a phone number to use
      const phoneNumbers = await this.mcpService.listPhoneNumbers();
      if (!phoneNumbers || phoneNumbers.length === 0) {
        throw new Error('No phone numbers available');
      }

      // Use the first available phone number
      const phoneNumberId = phoneNumbers[0].id;

      // Create the call
      const call = await this.mcpService.createCall(assistantId, phoneNumber, {
        phoneNumberId,
        scheduledAt: options.scheduledAt
      });

      // Store call in Supabase
      await supabase
        .from('calls')
        .insert({
          call_id: call.id,
          attorney_id: attorneyId,
          customer_phone: phoneNumber,
          status: call.status,
          created_at: new Date().toISOString()
        });

      // Add to local cache
      this.calls.set(call.id, {
        ...call,
        attorneyId
      });

      return call;
    } catch (error) {
      console.error('[EnhancedVapiCallService] Error creating call:', error);
      throw error;
    }
  }

  /**
   * Get call details
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call details
   */
  async getCall(callId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Check local cache first
      if (this.calls.has(callId)) {
        // Refresh from API to get latest status
        try {
          const freshCall = await this.mcpService.getCall(callId);

          // Update local cache
          this.calls.set(callId, {
            ...this.calls.get(callId),
            ...freshCall
          });
        } catch (refreshError) {
          console.warn(`[EnhancedVapiCallService] Error refreshing call ${callId}:`, refreshError);
          // Continue with cached data
        }

        return this.calls.get(callId);
      }

      // Otherwise fetch from API
      const call = await this.mcpService.getCall(callId);

      // Get attorney ID from Supabase
      const { data } = await supabase
        .from('calls')
        .select('attorney_id')
        .eq('call_id', callId)
        .single();

      // Add to local cache
      if (call) {
        this.calls.set(callId, {
          ...call,
          attorneyId: data?.attorney_id
        });
      }

      return call;
    } catch (error) {
      console.error('[EnhancedVapiCallService] Error getting call:', error);
      throw error;
    }
  }

  /**
   * List all calls for an attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Array>} - List of calls
   */
  async listCallsForAttorney(attorneyId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Get calls from Supabase
      const { data, error } = await supabase
        .from('calls')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('[EnhancedVapiCallService] Error getting calls from Supabase:', error);
        throw error;
      }

      // Get details for each call
      const calls = await Promise.all(
        data.map(async (callData) => {
          try {
            const call = await this.mcpService.getCall(callData.call_id);
            return {
              ...call,
              attorneyId
            };
          } catch (callError) {
            console.warn(`[EnhancedVapiCallService] Error getting call ${callData.call_id}:`, callError);
            return {
              id: callData.call_id,
              status: callData.status,
              customer: {
                phoneNumber: callData.customer_phone
              },
              createdAt: callData.created_at,
              attorneyId,
              error: callError.message
            };
          }
        })
      );

      return calls;
    } catch (error) {
      console.error('[EnhancedVapiCallService] Error listing calls for attorney:', error);
      throw error;
    }
  }

  /**
   * Add event listener for call events
   * @param {string} callId - Call ID
   * @param {string} eventType - Event type
   * @param {Function} callback - Callback function
   * @returns {Function} - Unsubscribe function
   */
  addEventListener(callId, eventType, callback) {
    const key = `${callId}:${eventType}`;

    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, new Set());
    }

    this.eventListeners.get(key).add(callback);

    return () => {
      this.eventListeners.get(key).delete(callback);
    };
  }

  /**
   * Trigger event for call
   * @param {string} callId - Call ID
   * @param {string} eventType - Event type
   * @param {any} data - Event data
   */
  triggerEvent(callId, eventType, data) {
    const key = `${callId}:${eventType}`;

    if (this.eventListeners.has(key)) {
      this.eventListeners.get(key).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[EnhancedVapiCallService] Error in event listener for ${key}:`, error);
        }
      });
    }
  }
}

// Export a singleton instance
export const enhancedVapiCallService = new EnhancedVapiCallService();
export default enhancedVapiCallService;
