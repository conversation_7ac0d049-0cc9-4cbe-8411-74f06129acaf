import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt the user for Supabase credentials
console.log('🗄️  Supabase Setup Helper');
console.log('=========================');
console.log('This script will help you update your .env file with Supabase credentials');
console.log('and set up your database tables.\n');

rl.question('Enter your Supabase project URL: ', (supabaseUrl) => {
  rl.question('Enter your Supabase anon key: ', (supabaseKey) => {
    // Update the .env file
    const envPath = path.join(__dirname, '.env');
    
    if (fs.existsSync(envPath)) {
      let envContent = fs.readFileSync(envPath, 'utf8');
      
      // Replace Supabase URL
      envContent = envContent.replace(/VITE_SUPABASE_URL=.*/, `VITE_SUPABASE_URL=${supabaseUrl}`);
      envContent = envContent.replace(/REACT_APP_SUPABASE_URL=.*/, `REACT_APP_SUPABASE_URL=${supabaseUrl}`);
      
      // Replace Supabase Key
      envContent = envContent.replace(/VITE_SUPABASE_KEY=.*/, `VITE_SUPABASE_KEY=${supabaseKey}`);
      envContent = envContent.replace(/REACT_APP_SUPABASE_KEY=.*/, `REACT_APP_SUPABASE_KEY=${supabaseKey}`);
      
      // Write back to .env file
      fs.writeFileSync(envPath, envContent);
      console.log('\n✅ .env file updated successfully with Supabase credentials');
      
      // Ask if the user wants to set up the attorneys table
      rl.question('\nDo you want to set up the attorneys table in your Supabase project? (y/n): ', (setupTable) => {
        if (setupTable.toLowerCase() === 'y') {
          console.log('\n📝 Instructions to set up the attorneys table:');
          console.log('1. Go to your Supabase dashboard at https://app.supabase.com');
          console.log('2. Select your project');
          console.log('3. Go to the SQL Editor');
          console.log('4. Copy and paste the contents of create_attorneys_table.sql');
          console.log('5. Click "Run"');
          console.log('\nOnce you have run the SQL, you can use the checkSupabaseTables.js script to verify the table was created.');
        }
        
        // Ask if the user wants to check the tables
        rl.question('\nDo you want to check the Supabase tables now? (y/n): ', (checkTables) => {
          if (checkTables.toLowerCase() === 'y') {
            console.log('\n🔍 Running table check...');
            exec('node checkSupabaseTables.js', (error, stdout, stderr) => {
              if (error) {
                console.error(`Error executing table check: ${error.message}`);
                rl.close();
                return;
              }
              if (stderr) {
                console.error(`Table check stderr: ${stderr}`);
              }
              console.log(stdout);
              rl.close();
            });
          } else {
            rl.close();
          }
        });
      });
    } else {
      console.error('❌ .env file not found. Please create it first.');
      rl.close();
    }
  });
});

// Handle readline close
rl.on('close', () => {
  console.log('\n👋 Setup completed. Thank you for using the Supabase Setup Helper!');
  process.exit(0);
}); 