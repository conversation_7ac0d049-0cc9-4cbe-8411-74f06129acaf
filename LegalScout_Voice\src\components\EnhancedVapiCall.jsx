import React, { useState, useEffect, useRef, useMemo } from 'react';
import useEnhancedVapi from '../hooks/useEnhancedVapi';
import useEnhancedVapiWithDebug from '../hooks/useEnhancedVapiWithDebug';
import EnhancedSpeechParticles from './EnhancedSpeechParticles';
import { getCallDebugger } from '../utils/callDebugger';
import CallDebugPanel from './CallDebugPanel';
import './EnhancedVapiCall.css';

/**
 * Enhanced Vapi Call Component
 *
 * This component provides a streamlined interface for voice calls with Vapi,
 * using the enhanced Vapi hook for consistent functionality. It's designed to be
 * a drop-in replacement for the existing VapiCall component but with a more
 * modern UI that matches LegalScout's design.
 *
 * @param {Object} props
 * @param {Function} props.onEndCall - Callback when call ends
 * @param {string} props.assistantId - The Vapi assistant ID to use
 * @param {Object} props.customInstructions - Custom instructions for the assistant
 * @param {boolean} props.showTranscript - Whether to show the transcript
 * @param {boolean} props.showDossier - Whether to show the dossier
 * @param {string} props.className - Additional CSS class
 */
const EnhancedVapiCall = ({
  onEndCall,
  assistantId,
  customInstructions = {},
  showTranscript = true,
  showDossier = true,
  className = '',
  showDebugPanel = false
}) => {
  // Initialize call debugger
  const callDebugger = useMemo(() => getCallDebugger('EnhancedVapiCall'), []);
  callDebugger.log('Initializing EnhancedVapiCall component', { assistantId });

  // Use our enhanced custom hook with debugging to manage call state and functionality
  const {
    volumeLevel,
    isSessionActive,
    conversation,
    currentSpeaker,
    toggleCall,
    startCall,
    stopCall,
    clearConversation,
    error: vapiError
  } = useEnhancedVapiWithDebug({
    assistantId,
    customInstructions,
    onCallEnd: (data) => {
      if (onEndCall) onEndCall(data);
    },
    onError: (error) => {
      console.error('Vapi error in EnhancedVapiCall:', error);
    }
  });

  // Our hook already has debugging built in, so we don't need to wrap these functions

  // State for dossier data
  const [dossierData, setDossierData] = useState({});
  // State for call status
  const [status, setStatus] = useState('idle');
  // State for error message
  const [errorMessage, setErrorMessage] = useState(null);
  // Ref for conversation container
  const conversationRef = useRef(null);

  // Start call on component mount
  useEffect(() => {
    const initializeCall = async () => {
      try {
        setStatus('connecting');

        // Start call with the provided assistant ID
        if (assistantId) {
          const success = await startCall();
          if (!success) {
            setErrorMessage('Failed to start call');
            setStatus('error');
          }
        } else {
          console.error('No assistant ID provided');
          setErrorMessage('No assistant ID provided');
          setStatus('error');
        }
      } catch (error) {
        console.error('Error starting call:', error);
        setErrorMessage(`Error starting call: ${error.message}`);
        setStatus('error');
      }
    };

    // Initialize call with a slight delay
    const timer = setTimeout(() => {
      initializeCall();
    }, 500);

    // Set global flag to prevent unmounting during active call
    window.vapiCallActive = true;

    // Cleanup function
    return () => {
      clearTimeout(timer);

      // End call if component is unmounted
      if (isSessionActive) {
        stopCall();
      }

      // Clear global flag
      window.vapiCallActive = false;
    };
  }, [assistantId, startCall, stopCall, isSessionActive]);

  // Update status based on session state
  useEffect(() => {
    if (isSessionActive) {
      setStatus('active');
    } else if (status === 'active') {
      setStatus('ended');
    }
  }, [isSessionActive, status]);

  // Scroll to bottom when conversation updates
  useEffect(() => {
    if (conversationRef.current) {
      conversationRef.current.scrollTop = conversationRef.current.scrollHeight;
    }
  }, [conversation]);

  // Handle end call
  const handleEndCall = async () => {
    try {
      setStatus('ending');
      const success = await stopCall();

      if (!success) {
        setErrorMessage('Failed to end call');
        setStatus('error');
        return;
      }

      // onEndCall callback is now handled by the hook
    } catch (error) {
      console.error('Error ending call:', error);
      setErrorMessage(`Error ending call: ${error.message}`);
    }
  };

  // Render status indicator
  const renderStatusIndicator = () => {
    let statusText = '';
    let statusClass = '';

    switch (status) {
      case 'connecting':
        statusText = 'Connecting...';
        statusClass = 'connecting';
        break;
      case 'active':
        statusText = 'Call in progress';
        statusClass = 'active';
        break;
      case 'ending':
        statusText = 'Ending call...';
        statusClass = 'ending';
        break;
      case 'ended':
        statusText = 'Call ended';
        statusClass = 'ended';
        break;
      case 'error':
        statusText = 'Error';
        statusClass = 'error';
        break;
      default:
        statusText = 'Ready';
        statusClass = 'idle';
    }

    return (
      <div className={`call-status ${statusClass}`}>
        <div className="status-indicator"></div>
        <span className="status-text">{statusText}</span>
      </div>
    );
  };

  // Render dossier
  const renderDossier = () => {
    if (!showDossier || Object.keys(dossierData).length === 0) {
      return null;
    }

    return (
      <div className="dossier-container">
        <h3 className="dossier-title">Consultation Information</h3>
        <div className="dossier-content">
          {Object.entries(dossierData).map(([key, value]) => (
            <div key={key} className="dossier-item">
              <span className="dossier-key">{key.replace(/_/g, ' ')}:</span>
              <span className="dossier-value">{value}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render conversation
  const renderConversation = () => {
    if (!showTranscript || conversation.length === 0) {
      return (
        <div className="empty-conversation">
          <p>Your conversation will appear here.</p>
        </div>
      );
    }

    return (
      <div className="conversation-container" ref={conversationRef}>
        {conversation.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <div className="message-header">
              <span className="message-role">
                {message.role === 'assistant' ? 'Assistant' : 'You'}
              </span>
            </div>
            <div className="message-content">
              <p>{message.text}</p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      {/* Debug Panel - DISABLED */}
      {false && <CallDebugPanel visible={true} />}

      <div className={`enhanced-vapi-call ${className} ${status}`}>
        {/* Header */}
        <div className="call-header">
          {renderStatusIndicator()}
          <button
            className="end-call-button"
            onClick={handleEndCall}
            disabled={status !== 'active'}
          >
            End Call
          </button>
        </div>

      {/* Visualization */}
      <div className="visualization-container">
        <EnhancedSpeechParticles
          volumeLevel={volumeLevel}
          currentSpeaker={currentSpeaker}
          className={currentSpeaker ? `${currentSpeaker}-speaking` : ''}
        />
      </div>

      {/* Error message */}
      {errorMessage && (
        <div className="error-message">
          <p>{errorMessage}</p>
        </div>
      )}

      {/* Main content */}
      <div className="call-content">
        {/* Dossier */}
        {renderDossier()}

        {/* Conversation */}
        {renderConversation()}
      </div>
    </div>
    </>
  );
};

export default EnhancedVapiCall;
