/**
 * Fix Orphaned Assistant Problem
 * 
 * This script diagnoses and fixes the orphaned assistant issue where
 * the Vapi assistant exists but is missing custom configuration data.
 */

// Your assistant ID from the memories
const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Expected configuration based on your profile
const EXPECTED_CONFIG = {
  name: "LegalScout Assistant",
  firstMessage: "Hello! I'm <PERSON>, your AI legal assistant. How can I help you today?",
  firstMessageMode: "assistant-speaks-first",
  model: {
    provider: "openai",
    model: "gpt-4o",
    messages: [
      {
        role: "system",
        content: "You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information."
      }
    ]
  },
  voice: {
    provider: "11labs",
    voiceId: "sarah",
    model: "eleven_turbo_v2_5"
  },
  transcriber: {
    provider: "deepgram",
    model: "nova-3"
  }
};

async function diagnoseAssistant() {
  console.log('🔍 Diagnosing assistant:', ASSISTANT_ID);
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const assistant = await response.json();
    
    console.log('📋 Current Assistant Configuration:');
    console.log('- Name:', assistant.name || 'MISSING');
    console.log('- First Message:', assistant.firstMessage || 'MISSING');
    console.log('- Instructions:', assistant.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING');
    console.log('- Voice:', `${assistant.voice?.provider}/${assistant.voice?.voiceId}` || 'MISSING');
    console.log('- Model:', `${assistant.llm?.provider}/${assistant.llm?.model}` || 'MISSING');
    
    // Check what's missing
    const issues = [];
    if (!assistant.firstMessage) issues.push('firstMessage');
    if (!assistant.model?.messages?.[0]?.content) issues.push('system instructions');
    if (!assistant.name?.includes('LegalScout')) issues.push('proper name');
    
    console.log('\n🚨 Issues Found:', issues.length > 0 ? issues.join(', ') : 'None');
    
    return { assistant, issues };
  } catch (error) {
    console.error('❌ Failed to diagnose assistant:', error.message);
    throw error;
  }
}

async function fixAssistant() {
  console.log('\n🔧 Fixing assistant configuration...');
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(EXPECTED_CONFIG)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const updatedAssistant = await response.json();
    
    console.log('✅ Assistant updated successfully!');
    console.log('- Name:', updatedAssistant.name);
    console.log('- First Message:', updatedAssistant.firstMessage);
    console.log('- Instructions:', updatedAssistant.model?.messages?.[0]?.content ? 'UPDATED' : 'STILL MISSING');
    
    return updatedAssistant;
  } catch (error) {
    console.error('❌ Failed to fix assistant:', error.message);
    throw error;
  }
}

async function verifyFix() {
  console.log('\n🔍 Verifying fix...');
  
  const { assistant, issues } = await diagnoseAssistant();
  
  if (issues.length === 0) {
    console.log('✅ All issues resolved!');
    return true;
  } else {
    console.log('❌ Some issues remain:', issues.join(', '));
    return false;
  }
}

async function checkMcpView() {
  console.log('\n🔍 Checking MCP Server view...');

  try {
    // Simulate what the MCP server would return
    const mcpResponse = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const mcpData = await mcpResponse.json();

    console.log('📋 MCP Server Response:');
    console.log(JSON.stringify(mcpData, null, 2));

    return mcpData;
  } catch (error) {
    console.error('❌ Failed to check MCP view:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Orphaned Assistant Fix Process\n');

    // Step 1: Diagnose current state
    const { issues } = await diagnoseAssistant();

    // Step 2: Check MCP view
    await checkMcpView();

    if (issues.length === 0) {
      console.log('✅ Direct API shows no issues, but MCP might be different.');
      console.log('This suggests the issue is in the MCP integration layer.');
      return;
    }

    // Step 3: Fix the assistant
    await fixAssistant();

    // Step 4: Verify the fix
    const success = await verifyFix();

    if (success) {
      console.log('\n🎉 Orphaned assistant problem resolved!');
      console.log('Your assistant should now have proper instructions and welcome message.');
    } else {
      console.log('\n⚠️ Some issues may remain. Check the logs above.');
    }

  } catch (error) {
    console.error('\n💥 Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
