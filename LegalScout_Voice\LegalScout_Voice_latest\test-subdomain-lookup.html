<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subdomain Lookup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Test Subdomain Lookup</h1>
    <p>Testing the database lookup for assistant1test subdomain</p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testSubdomainLookup()">Test Subdomain Lookup</button>
        <button onclick="testUIConfig()">Test UI Config</button>
        <button onclick="testVapiAPI()">Test Vapi API</button>
        <button onclick="testFullFlow()">Test Full Flow</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script>
        const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI5MDU2NzQsImV4cCI6MjA0ODQ4MTY3NH0.Ej8XZvB7qFpTqVrNVaOjmJlvjHlqcEWaQVgUhkJhGJo';
        const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.testSubdomainLookup = async function() {
            addResult('info', '🔍 Testing subdomain lookup for assistant1test...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.assistant1test&is_active=eq.true`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data && data.length > 0) {
                    addResult('success', `✅ Subdomain lookup successful:
Assistant ID: ${data[0].assistant_id}
Subdomain: ${data[0].subdomain}
Is Active: ${data[0].is_active}
Full data: ${JSON.stringify(data[0], null, 2)}`);
                } else {
                    addResult('error', '❌ No data returned for assistant1test subdomain');
                }
                
            } catch (error) {
                addResult('error', `❌ Subdomain lookup failed: ${error.message}`);
            }
        };

        window.testUIConfig = async function() {
            addResult('info', '🎨 Testing UI config lookup...');
            
            // First get the assistant ID
            try {
                const subdomainResponse = await fetch(`${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.assistant1test&is_active=eq.true`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                const subdomainData = await subdomainResponse.json();
                
                if (!subdomainData || subdomainData.length === 0) {
                    addResult('error', '❌ No assistant ID found for subdomain');
                    return;
                }

                const assistantId = subdomainData[0].assistant_id;
                addResult('info', `📋 Found assistant ID: ${assistantId}`);

                // Now get UI config
                const uiResponse = await fetch(`${SUPABASE_URL}/rest/v1/assistant_ui_configs?assistant_id=eq.${assistantId}`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!uiResponse.ok) {
                    throw new Error(`UI Config HTTP ${uiResponse.status}: ${uiResponse.statusText}`);
                }

                const uiData = await uiResponse.json();
                
                if (uiData && uiData.length > 0) {
                    const config = uiData[0];
                    addResult('success', `✅ UI Config found:
Firm Name: ${config.firm_name}
Primary Color: ${config.primary_color}
Voice ID: ${config.voice_id}
Welcome Message: ${config.welcome_message?.substring(0, 50)}...
Full config: ${JSON.stringify(config, null, 2)}`);
                } else {
                    addResult('error', '❌ No UI config found for assistant');
                }
                
            } catch (error) {
                addResult('error', `❌ UI config test failed: ${error.message}`);
            }
        };

        window.testVapiAPI = async function() {
            addResult('info', '📞 Testing Vapi API...');
            
            // First get the assistant ID
            try {
                const subdomainResponse = await fetch(`${SUPABASE_URL}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.assistant1test&is_active=eq.true`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                const subdomainData = await subdomainResponse.json();
                
                if (!subdomainData || subdomainData.length === 0) {
                    addResult('error', '❌ No assistant ID found for subdomain');
                    return;
                }

                const assistantId = subdomainData[0].assistant_id;
                addResult('info', `📋 Testing Vapi API for assistant: ${assistantId}`);

                // Test Vapi API
                const vapiResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
                    headers: {
                        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (vapiResponse.ok) {
                    const vapiData = await vapiResponse.json();
                    addResult('success', `✅ Vapi API successful:
Assistant Name: ${vapiData.name}
Voice Provider: ${vapiData.voice?.provider}
Voice ID: ${vapiData.voice?.voiceId}
LLM Model: ${vapiData.llm?.model}
Full data: ${JSON.stringify(vapiData, null, 2)}`);
                } else {
                    addResult('error', `❌ Vapi API failed: ${vapiResponse.status} ${vapiResponse.statusText}`);
                }
                
            } catch (error) {
                addResult('error', `❌ Vapi API test failed: ${error.message}`);
            }
        };

        window.testFullFlow = async function() {
            addResult('info', '🚀 Testing complete flow...');
            
            try {
                const { simpleSubdomainService } = await import('./src/services/simpleSubdomainService.js');
                
                const config = await simpleSubdomainService.getSubdomainConfig('assistant1test');
                
                if (config) {
                    addResult('success', `✅ Complete flow successful:
Firm Name: ${config.firmName}
Assistant ID: ${config.vapi_assistant_id}
Primary Color: ${config.primaryColor}
Voice ID: ${config.voiceId}
Has UI Config: ${config.hasUIConfig}
Has Call Config: ${config.hasCallConfig}
Loaded Via: ${config.loadedVia}

Full config: ${JSON.stringify(config, null, 2)}`);
                } else {
                    addResult('error', '❌ Complete flow returned null');
                }
                
            } catch (error) {
                addResult('error', `❌ Complete flow failed: ${error.message}`);
                console.error('Full flow error:', error);
            }
        };

        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSubdomainLookup();
                setTimeout(() => {
                    testUIConfig();
                    setTimeout(() => {
                        testVapiAPI();
                        setTimeout(() => {
                            testFullFlow();
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
