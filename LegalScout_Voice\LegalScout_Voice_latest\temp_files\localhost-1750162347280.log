dashboard:28 🚀 [LegalScout] Initializing environment...
dashboard:50 ✅ [LegalScout] Environment initialized
dashboard:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
chunk-Q72EVS5P.js?v=9711cfb6:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
standaloneAttorneyManagerFix.js:143 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
standaloneAttorneyManagerFix.js:122 [StandaloneAttorneyManagerFix] Initialized with current attorney: 695b5caf-4884-456d-a3b1-7765427b6095
standaloneAttorneyManagerFix.js:130 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:138 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
standaloneAttorneyManagerFix.js:158 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
main.jsx:28 🚀 [LegalScout] Starting React app...
main.jsx:44 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:59 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:90 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
AuthContext.jsx:62 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:446 Warning: React has detected a change in the order of Hooks called by App. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
1. useContext                 useContext
2. useContext                 useContext
3. useContext                 useContext
4. undefined                  useState
   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

    at App (http://localhost:5179/src/App.jsx:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5179/src/contexts/AuthContext.jsx:43:32)
    at InnerAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:20:30)
    at Provider (http://localhost:5179/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5179/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:28:29)
    at AttorneyStateProvider (http://localhost:5179/src/contexts/AttorneyStateContext.jsx:31:41)
    at ThemeProvider (http://localhost:5179/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5179/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5179/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=9711cfb6:521
error @ chunk-Q72EVS5P.js?v=9711cfb6:505
warnOnHookMismatchInDev @ chunk-Q72EVS5P.js?v=9711cfb6:11515
updateHookTypesDev @ chunk-Q72EVS5P.js?v=9711cfb6:11485
useState @ chunk-Q72EVS5P.js?v=9711cfb6:12665
useState @ chunk-2N3A5BUM.js?v=9711cfb6:1066
App @ App.jsx:446
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14602
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
chunk-Q72EVS5P.js?v=9711cfb6:521 Warning: Internal React error: Expected static flag was missing. Please notify the React team.
    at App (http://localhost:5179/src/App.jsx:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5179/src/contexts/AuthContext.jsx:43:32)
    at InnerAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:20:30)
    at Provider (http://localhost:5179/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5179/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:28:29)
    at AttorneyStateProvider (http://localhost:5179/src/contexts/AttorneyStateContext.jsx:31:41)
    at ThemeProvider (http://localhost:5179/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5179/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5179/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=9711cfb6:521
error @ chunk-Q72EVS5P.js?v=9711cfb6:505
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11610
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14602
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
chunk-Q72EVS5P.js?v=9711cfb6:521 Warning: Internal React error: Expected static flag was missing. Please notify the React team.
    at App (http://localhost:5179/src/App.jsx:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5179/src/contexts/AuthContext.jsx:43:32)
    at InnerAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:20:30)
    at Provider (http://localhost:5179/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5179/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:28:29)
    at AttorneyStateProvider (http://localhost:5179/src/contexts/AttorneyStateContext.jsx:31:41)
    at ThemeProvider (http://localhost:5179/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5179/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5179/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=9711cfb6:521
error @ chunk-Q72EVS5P.js?v=9711cfb6:505
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11610
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14607
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:06.653Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:06.656Z'}
chunk-Q72EVS5P.js?v=9711cfb6:521 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    at form
    at div
    at WebsiteImporter (http://localhost:5179/src/components/dashboard/WebsiteImporter.jsx:23:28)
    at form
    at div
    at ProfileTab (http://localhost:5179/src/components/dashboard/ProfileTab.jsx:31:23)
    at div
    at div
    at div
    at div
    at DashboardNew (http://localhost:5179/src/pages/DashboardNew.jsx:48:55)
    at RenderedRoute (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:5722:26)
    at Routes (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6454:3)
    at main
    at div
    at AssistantAwareProvider (http://localhost:5179/src/contexts/AssistantAwareContext.jsx:30:42)
    at App (http://localhost:5179/src/App.jsx:459:20)
    at LegalScoutApp
    at AuthProvider (http://localhost:5179/src/contexts/AuthContext.jsx:43:32)
    at InnerAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:20:30)
    at Provider (http://localhost:5179/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5179/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5179/src/components/SyncAuthProvider.jsx:28:29)
    at AttorneyStateProvider (http://localhost:5179/src/contexts/AttorneyStateContext.jsx:31:41)
    at ThemeProvider (http://localhost:5179/src/contexts/ThemeContext.jsx:44:33)
    at Router (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:6397:13)
    at BrowserRouter (http://localhost:5179/node_modules/.vite/deps/react-router-dom.js?v=9711cfb6:8631:3)
    at ErrorBoundary (http://localhost:5179/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5179/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=9711cfb6:521
error @ chunk-Q72EVS5P.js?v=9711cfb6:505
validateDOMNesting @ chunk-Q72EVS5P.js?v=9711cfb6:8267
createInstance @ chunk-Q72EVS5P.js?v=9711cfb6:8339
completeWork @ chunk-Q72EVS5P.js?v=9711cfb6:16311
completeUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19252
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19234
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
ProfileTab.jsx:57 Attorney object in ProfileTab: null
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
ProfileTab.jsx:92 ✅ Using OAuth user email: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
DashboardNew.jsx:145 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:185 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
DashboardNew.jsx:186 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', timestamp: '2025-06-17T12:12:06.859Z'}
DashboardNew.jsx:211 [DashboardNew] ✅ User authenticated: <EMAIL>
DashboardNew.jsx:226 [DashboardNew] 🔄 Loading attorney for user...
DashboardNew.jsx:238 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:239 [DashboardNew] Dependencies: user?.id=bafd37ba-a143-40ea-bcf5-e25fe149b55e, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:487 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:263 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:334 [DashboardNew] Attempting to load attorney for user: bafd37ba-a143-40ea-bcf5-e25fe149b55e
DashboardNew.jsx:339 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:342 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:380 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:380
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:384 [DashboardNew] 🔄 Using fallback attorney loading logic...
standaloneAttorneyManagerFix.js:76 [StandaloneAttorneyManagerFix] Loading attorney for user: bafd37ba-a143-40ea-bcf5-e25fe149b55e
AttorneyProfileManager.js:350 [AttorneyProfileManager] Loading attorney by userId: bafd37ba-a143-40ea-bcf5-e25fe149b55e
AttorneyProfileManager.js:367 [AttorneyProfileManager] Error loading attorney by userId: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:79 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:643 🚀 [App] Initializing with safe subdomain detection...
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
ProfileTab.jsx:57 Attorney object in ProfileTab: null
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
DashboardNew.jsx:145 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:185 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
DashboardNew.jsx:186 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', timestamp: '2025-06-17T12:12:06.870Z'}
DashboardNew.jsx:211 [DashboardNew] ✅ User authenticated: <EMAIL>
DashboardNew.jsx:226 [DashboardNew] 🔄 Loading attorney for user...
DashboardNew.jsx:238 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:239 [DashboardNew] Dependencies: user?.id=bafd37ba-a143-40ea-bcf5-e25fe149b55e, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:487 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:263 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:334 [DashboardNew] Attempting to load attorney for user: bafd37ba-a143-40ea-bcf5-e25fe149b55e
DashboardNew.jsx:339 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:342 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:380 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:380
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:384 [DashboardNew] 🔄 Using fallback attorney loading logic...
standaloneAttorneyManagerFix.js:76 [StandaloneAttorneyManagerFix] Loading attorney for user: bafd37ba-a143-40ea-bcf5-e25fe149b55e
AttorneyProfileManager.js:350 [AttorneyProfileManager] Loading attorney by userId: bafd37ba-a143-40ea-bcf5-e25fe149b55e
AttorneyProfileManager.js:367 [AttorneyProfileManager] Error loading attorney by userId: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:79 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:643 🚀 [App] Initializing with safe subdomain detection...
standaloneAttorneyManagerFix.js:91 [StandaloneAttorneyManagerFix] Error loading attorney: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
standaloneAttorneyManagerFix.js:91 [StandaloneAttorneyManagerFix] Error loading attorney: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
loadAttorneyForUser @ standaloneAttorneyManagerFix.js:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:178 [useStandaloneAttorney] Error loading attorney for user: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:178 [useStandaloneAttorney] Error loading attorney for user: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:390 [DashboardNew] Error loading attorney by user ID: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
fetchAttorneyData @ DashboardNew.jsx:390
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:396 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
DashboardNew.jsx:390 [DashboardNew] Error loading attorney by user ID: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
fetchAttorneyData @ DashboardNew.jsx:390
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:396 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:221 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:221
updateMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12230
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12746
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:186
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14602
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:221 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:221
updateMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12230
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12746
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:186
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14607
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.062Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.064Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
ProfileTab.jsx:92 ✅ Using OAuth user email: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
ProfileTab.jsx:92 ✅ Using OAuth user email: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:658 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:668 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:688 🔍 [App] Subdomain detected: default
App.jsx:696 🏠 [App] Localhost detected - treating as main domain
App.jsx:751 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:658 ✅ Production environment initialized
App.jsx:72 Supabase config initialization (fallback)
App.jsx:77 Supabase config verification (fallback)
App.jsx:668 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:688 🔍 [App] Subdomain detected: default
App.jsx:696 🏠 [App] Localhost detected - treating as main domain
App.jsx:751 🏁 [App] Initialization complete
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-4831c692.legalscout.net', currentAssistant: '695b5caf-4884-456d-a3b1-7765427b6095', isAssistantSelected: true, assistantSubdomain: 'thekosts'}
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VeryCoolAssistants.jsx:70 🔍 [VeryCoolAssistants] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
DashboardNew.jsx:185 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
DashboardNew.jsx:186 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', timestamp: '2025-06-17T12:12:07.158Z'}
DashboardNew.jsx:211 [DashboardNew] ✅ User authenticated: <EMAIL>
DashboardNew.jsx:221 [DashboardNew] ✅ Attorney already loaded: LegalScout
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
AssistantAwareContext.jsx:68 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: true, hasAttorney: true}
AssistantAwareContext.jsx:76 ✅ [AssistantAwareContext] Loading assistant data for: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://assistant-4831c692.legalscout.net', currentAssistant: '695b5caf-4884-456d-a3b1-7765427b6095', isAssistantSelected: true, assistantSubdomain: 'thekosts'}
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:209 ⏳ [AssistantAwareContext] Assistant subdomain loading...
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.247Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.251Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.252Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.254Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
VM567 simple-preview:4 🚀 [LegalScout] Initializing environment...
VM567 simple-preview:26 ✅ [LegalScout] Environment initialized
VM568 simple-preview:85 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM569 simple-preview:84 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
VM572 simple-preview:4 🚀 [LegalScout] Initializing environment...
VM572 simple-preview:26 ✅ [LegalScout] Environment initialized
VM573 simple-preview:85 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM574 simple-preview:84 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'Loading assistant URL...', currentAssistant: '695b5caf-4884-456d-a3b1-7765427b6095', isAssistantSelected: true, assistantSubdomain: 'thekosts'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.341Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.342Z'}
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
supabase.js:150 ✅ [Supabase-Fixed] Client test passed
supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.625Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.626Z'}
AuthContext.jsx:68 🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.720Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.721Z'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.836Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:07.837Z'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
vapiAssistantUtils.js:169 Found attorney by email: 695b5caf-4884-456d-a3b1-7765427b6095
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 695b5caf-4884-456d-a3b1-7765427b6095
DashboardNew.jsx:412 [DashboardNew] Error finding attorney by email: TypeError: window.standaloneAttorneyManager.saveToLocalStorage is not a function
    at fetchAttorneyData (DashboardNew.jsx:407:50)
fetchAttorneyData @ DashboardNew.jsx:412
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 695b5caf-4884-456d-a3b1-7765427b6095
vapiAssistantUtils.js:169 Found attorney by email: 695b5caf-4884-456d-a3b1-7765427b6095
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 695b5caf-4884-456d-a3b1-7765427b6095
DashboardNew.jsx:412 [DashboardNew] Error finding attorney by email: TypeError: window.standaloneAttorneyManager.saveToLocalStorage is not a function
    at fetchAttorneyData (DashboardNew.jsx:407:50)
fetchAttorneyData @ DashboardNew.jsx:412
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
VM714 supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM714 supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
VM714 supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
VM714 supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
VM624 unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
VM909 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM909 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM901 vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
VM901 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM893 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM758 AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
VM758 AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
VM758 AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
VM614 standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
VM614 standaloneAttorneyManagerFix.js:143 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
VM614 standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
VM614 standaloneAttorneyManagerFix.js:122 [StandaloneAttorneyManagerFix] Initialized with current attorney: 695b5caf-4884-456d-a3b1-7765427b6095
VM614 standaloneAttorneyManagerFix.js:130 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
VM614 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM614 standaloneAttorneyManagerFix.js:138 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
VM614 standaloneAttorneyManagerFix.js:158 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
VM614 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM614 standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
VM561 main.jsx:23 🚀 [LegalScout] Starting React app...
VM561 main.jsx:32 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
VM561 main.jsx:45 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
VM561 main.jsx:90 ✅ [LegalScout] React app rendered successfully
DashboardNew.jsx:1587 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
VM609 ProductionErrorBoundary.jsx:19 [ErrorBoundary] Creating React placeholder
VM609 ProductionErrorBoundary.jsx:23 [ErrorBoundary] Adding createContext placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useState placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useEffect placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useLayoutEffect placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useRef placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useCallback placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useMemo placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useContext placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding forwardRef placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding createElement placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding cloneElement placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding createRef placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Component placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding PureComponent placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Fragment placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Children placeholder
VM609 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding isValidElement placeholder
VM609 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
VM609 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM623 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM623 AssistantAwareContext.jsx:42
mountMemo @ VM600 chunk-Q72EVS5P.js:12214
useMemo @ VM600 chunk-Q72EVS5P.js:12538
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:37
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM600 chunk-Q72EVS5P.js:14946
beginWork @ VM600 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM623 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM623 AssistantAwareContext.jsx:43
mountMemo @ VM600 chunk-Q72EVS5P.js:12214
useMemo @ VM600 chunk-Q72EVS5P.js:12538
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:37
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM600 chunk-Q72EVS5P.js:14946
beginWork @ VM600 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM623 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM623 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM623 AssistantAwareContext.jsx:42
mountMemo @ VM600 chunk-Q72EVS5P.js:12214
useMemo @ VM600 chunk-Q72EVS5P.js:12538
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:37
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM600 chunk-Q72EVS5P.js:14996
beginWork @ VM600 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM623 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM623 AssistantAwareContext.jsx:43
mountMemo @ VM600 chunk-Q72EVS5P.js:12214
useMemo @ VM600 chunk-Q72EVS5P.js:12538
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:37
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM600 chunk-Q72EVS5P.js:14996
beginWork @ VM600 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM623 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM797 supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM797 supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
VM797 supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
VM797 supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
VM657 unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
VM1020 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM1020 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM1015 vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
VM1015 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM978 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM827 AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
VM827 AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
VM827 AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🔧 [StandaloneAttorneyManagerFix] Initializing...
 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
 [StandaloneAttorneyManagerFix] Initialized with current attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
 ✅ [StandaloneAttorneyManagerFix] Service loaded
 🚀 [LegalScout] Starting React app...
 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
 ✅ [LegalScout] React app rendered successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'light', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'light', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
VM955 assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM623 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM623 AssistantAwareContext.jsx:182
updateMemo @ VM600 chunk-Q72EVS5P.js:12230
useMemo @ VM600 chunk-Q72EVS5P.js:12746
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:153
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM600 chunk-Q72EVS5P.js:14602
beginWork @ VM600 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM623 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM623 AssistantAwareContext.jsx:182
updateMemo @ VM600 chunk-Q72EVS5P.js:12230
useMemo @ VM600 chunk-Q72EVS5P.js:12746
useMemo @ VM597 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM623 AssistantAwareContext.jsx:153
renderWithHooks @ VM600 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM600 chunk-Q72EVS5P.js:14607
beginWork @ VM600 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM600 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM600 chunk-Q72EVS5P.js:19226
workLoopSync @ VM600 chunk-Q72EVS5P.js:19165
renderRootSync @ VM600 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM600 chunk-Q72EVS5P.js:18706
workLoop @ VM600 chunk-Q72EVS5P.js:197
flushWork @ VM600 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM600 chunk-Q72EVS5P.js:384
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM623 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: true, hasAttorney: true}
VM623 AssistantAwareContext.jsx:66 ✅ [AssistantAwareContext] Loading assistant data for: 4831c692-c073-4518-b0c8-27bd34883ba4
VM1020 vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
VM1020 vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
VM693 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM693 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'light', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM693 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: thekosts
VM693 SimplePreviewPage.jsx:61 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM800 useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
VM800 useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
VM595 standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
VM655 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
VM655 AssistantAwareContext.jsx:69 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
VM585 App.jsx:689 🚀 [App] Initializing with safe subdomain detection...
VM654 AuthContext.jsx:52 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
VM693 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM693 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'light', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM693 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: thekosts
VM693 SimplePreviewPage.jsx:61 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM800 useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
VM800 useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
VM595 standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
VM655 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
VM655 AssistantAwareContext.jsx:69 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
VM585 App.jsx:689 🚀 [App] Initializing with safe subdomain detection...
VM654 AuthContext.jsx:52 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
VM646 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM646 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1099 simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: thekosts
VM1099 simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
VM1099 simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: thekosts
VM1099 simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
VM1100 productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
VM1100 productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
VM604 App.jsx:700 ✅ Production environment initialized
VM604 App.jsx:78 Supabase config initialization (fallback)
VM604 App.jsx:82 Supabase config verification (fallback)
VM604 App.jsx:708 ✅ Supabase configured successfully
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM604 App.jsx:721 🔍 [App] Subdomain detected: default
VM604 App.jsx:726 🏠 [App] Localhost detected - treating as main domain
VM604 App.jsx:769 🏁 [App] Initialization complete
VM1100 productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
VM1100 productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
VM604 App.jsx:700 ✅ Production environment initialized
VM604 App.jsx:78 Supabase config initialization (fallback)
VM604 App.jsx:82 Supabase config verification (fallback)
VM604 App.jsx:708 ✅ Supabase configured successfully
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM604 App.jsx:721 🔍 [App] Subdomain detected: default
VM604 App.jsx:726 🏠 [App] Localhost detected - treating as main domain
VM604 App.jsx:769 🏁 [App] Initialization complete
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM623 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM623 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM1046 assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM655 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM655 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM655 AssistantAwareContext.jsx:182
updateMemo @ VM603 chunk-Q72EVS5P.js:12230
useMemo @ VM603 chunk-Q72EVS5P.js:12746
useMemo @ VM601 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM655 AssistantAwareContext.jsx:153
renderWithHooks @ VM603 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM603 chunk-Q72EVS5P.js:14602
beginWork @ VM603 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM603 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM603 chunk-Q72EVS5P.js:19226
workLoopSync @ VM603 chunk-Q72EVS5P.js:19165
renderRootSync @ VM603 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM603 chunk-Q72EVS5P.js:18706
workLoop @ VM603 chunk-Q72EVS5P.js:197
flushWork @ VM603 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM603 chunk-Q72EVS5P.js:384
VM655 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM655 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM655 AssistantAwareContext.jsx:182
updateMemo @ VM603 chunk-Q72EVS5P.js:12230
useMemo @ VM603 chunk-Q72EVS5P.js:12746
useMemo @ VM601 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM655 AssistantAwareContext.jsx:153
renderWithHooks @ VM603 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM603 chunk-Q72EVS5P.js:14607
beginWork @ VM603 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM603 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM603 chunk-Q72EVS5P.js:19226
workLoopSync @ VM603 chunk-Q72EVS5P.js:19165
renderRootSync @ VM603 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM603 chunk-Q72EVS5P.js:18706
workLoop @ VM603 chunk-Q72EVS5P.js:197
flushWork @ VM603 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM603 chunk-Q72EVS5P.js:384
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM655 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: true, hasAttorney: true}
VM655 AssistantAwareContext.jsx:66 ✅ [AssistantAwareContext] Loading assistant data for: 4831c692-c073-4518-b0c8-27bd34883ba4
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM655 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM655 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM1101 simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: thekosts
VM1101 simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
VM1101 simpleSubdomainService.js:30 🔍 [SimpleSubdomain] Loading config for: thekosts
VM1101 simpleSubdomainService.js:95 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
VM1102 productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
VM1102 productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
VM585 App.jsx:700 ✅ Production environment initialized
VM585 App.jsx:78 Supabase config initialization (fallback)
VM585 App.jsx:82 Supabase config verification (fallback)
VM585 App.jsx:708 ✅ Supabase configured successfully
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM585 App.jsx:721 🔍 [App] Subdomain detected: default
VM585 App.jsx:726 🏠 [App] Localhost detected - treating as main domain
VM585 App.jsx:769 🏁 [App] Initialization complete
VM1102 productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
VM1102 productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
VM585 App.jsx:700 ✅ Production environment initialized
VM585 App.jsx:78 Supabase config initialization (fallback)
VM585 App.jsx:82 Supabase config verification (fallback)
VM585 App.jsx:708 ✅ Supabase configured successfully
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM585 App.jsx:721 🔍 [App] Subdomain detected: default
VM585 App.jsx:726 🏠 [App] Localhost detected - treating as main domain
VM585 App.jsx:769 🏁 [App] Initialization complete
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://thekosts.legalscout.net', currentAssistant: '695b5caf-4884-456d-a3b1-7765427b6095', isAssistantSelected: true, assistantSubdomain: 'thekosts'}
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM1099 simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM1099 simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM646 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1099 simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.344Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.345Z'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM654 AuthContext.jsx:102 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
VM654 AuthContext.jsx:104 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VM654 AuthContext.jsx:106 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
VM654 AuthContext.jsx:113 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM693 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
VM622 AuthContext.jsx:102 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
VM622 AuthContext.jsx:104 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VM622 AuthContext.jsx:106 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
VM622 AuthContext.jsx:113 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.435Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.436Z'}
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM604 App.jsx:458 🔥 [App.jsx] App component is starting!
VM604 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM604 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM604 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM682 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
VM1099 simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.461Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.461Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
VM1101 simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '695b5caf-4884-456d-a3b1-7765427b6095', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
VM1101 simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
VM1099 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
VM1099 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
VM646 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM646 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM646 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
VM646 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: null
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.569Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.570Z'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.620Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:10.621Z'}
 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
VM654 AuthContext.jsx:68 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VM654 AuthContext.jsx:70 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
VM654 AuthContext.jsx:77 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
VM654 AuthContext.jsx:92 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Using assistant name from UI config: The Kosts
AssistantAwareContext.jsx:153 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts', assistantName: 'The Kosts', hasSubdomain: true, hasName: true}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
AssistantAwareContext.jsx:242 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://thekosts.legalscout.net', currentAssistant: '695b5caf-4884-456d-a3b1-7765427b6095', isAssistantSelected: true, assistantSubdomain: 'thekosts'}
VM654 AuthContext.jsx:68 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VM654 AuthContext.jsx:70 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
VM654 AuthContext.jsx:77 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
VM654 AuthContext.jsx:92 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
assistantDataService.js:238 🔄 [AssistantDataService] Loading fresh data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM654 AuthContext.jsx:102 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
VM654 AuthContext.jsx:104 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
VM654 AuthContext.jsx:106 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
VM654 AuthContext.jsx:113 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM585 App.jsx:458 🔥 [App.jsx] App component is starting!
VM585 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM585 App.jsx:572 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM585 App.jsx:578 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM709 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM722 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM722 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM722 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM722 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM722 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
VM722 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM722 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM722 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM722 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM722 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM722 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
VM722 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
VM714 supabase.js:150 ✅ [Supabase-Fixed] Client test passed
VM714 supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM623 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM623 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM1099 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
VM1099 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
VM646 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM646 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM646 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
VM646 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'light', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM722 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM722 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM722 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM722 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM722 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
VM722 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM722 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM722 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM722 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM722 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM722 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
VM722 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=light&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
VM646 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM646 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM646 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM722 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM722 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM722 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM722 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
VM722 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
VM722 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
VM722 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM722 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
VM722 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM649 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM649 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
VM797 supabase.js:150 ✅ [Supabase-Fixed] Client test passed
VM797 supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VM623 AssistantAwareContext.jsx:101 ✅ [AssistantAwareContext] Using assistant name from UI config: The Kosts
VM623 AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts', assistantName: 'The Kosts', hasSubdomain: true, hasName: true}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM623 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM623 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM655 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
VM655 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
VM655 AssistantAwareContext.jsx:101 ✅ [AssistantAwareContext] Using assistant name from UI config: The Kosts
VM655 AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts', assistantName: 'The Kosts', hasSubdomain: true, hasName: true}
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM655 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
VM655 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
VM655 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:11.096Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:11.098Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:11.187Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:11.188Z'}
VM758 AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 695b5caf-4884-456d-a3b1-7765427b6095
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VM1101 simpleSubdomainService.js:253 ⚠️ [SimpleSubdomain] Vapi API failed: Failed to fetch
getCallConfigFromVapi @ VM1101 simpleSubdomainService.js:253
await in getCallConfigFromVapi
getSubdomainConfig @ VM1101 simpleSubdomainService.js:43
await in getSubdomainConfig
loadAssistantConfig @ VM693 SimplePreviewPage.jsx:63
await in loadAssistantConfig
loadConfig @ VM693 SimplePreviewPage.jsx:102
(anonymous) @ VM693 SimplePreviewPage.jsx:154
commitHookEffectListMount @ VM603 chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ VM603 chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ VM603 chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ VM603 chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ VM603 chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ VM603 chunk-Q72EVS5P.js:19518
flushPassiveEffects @ VM603 chunk-Q72EVS5P.js:19475
(anonymous) @ VM603 chunk-Q72EVS5P.js:19356
workLoop @ VM603 chunk-Q72EVS5P.js:197
flushWork @ VM603 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM603 chunk-Q72EVS5P.js:384
VM1101 simpleSubdomainService.js:257 🔄 [SimpleSubdomain] Using fallback config for: 4831c692-c073-4518-b0c8-27bd34883ba4
VM1101 simpleSubdomainService.js:253 ⚠️ [SimpleSubdomain] Vapi API failed: Failed to fetch
getCallConfigFromVapi @ VM1101 simpleSubdomainService.js:253
await in getCallConfigFromVapi
getSubdomainConfig @ VM1101 simpleSubdomainService.js:43
await in getSubdomainConfig
loadAssistantConfig @ VM693 SimplePreviewPage.jsx:63
await in loadAssistantConfig
loadConfig @ VM693 SimplePreviewPage.jsx:102
(anonymous) @ VM693 SimplePreviewPage.jsx:154
commitHookEffectListMount @ VM603 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM603 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM603 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM603 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM603 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM603 chunk-Q72EVS5P.js:19475
(anonymous) @ VM603 chunk-Q72EVS5P.js:19356
workLoop @ VM603 chunk-Q72EVS5P.js:197
flushWork @ VM603 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM603 chunk-Q72EVS5P.js:384
VM1101 simpleSubdomainService.js:257 🔄 [SimpleSubdomain] Using fallback config for: 4831c692-c073-4518-b0c8-27bd34883ba4
VM1101 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
VM1101 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
VM693 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM693 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM693 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM693 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
VM693 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
VM693 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
VM693 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
VM693 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:28 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:50 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:28 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:50 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:139 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:226 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
loadAssistants @ VeryCoolAssistants.jsx:74
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
VeryCoolAssistants.jsx:76 📋 [VeryCoolAssistants] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095&assistant_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095&assistant_id=eq.007f9075-d4e3-4266-b626-2495e1d54a60 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095&assistant_id=eq.4831c692-c073-4518-b0c8-27bd34883ba4 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095&assistant_id=eq.6f2406d9-b373-4988-b272-8392116f2261 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095&assistant_id=eq.cd0b44b7-397e-410d-8835-ce9c3ba584b2 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VeryCoolAssistants.jsx:124 Error loading stats for assistant 695b5caf-4884-456d-a3b1-7765427b6095: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:109 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:364 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:389 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
VeryCoolAssistants.jsx:124 Error loading stats for assistant 007f9075-d4e3-4266-b626-2495e1d54a60: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VeryCoolAssistants.jsx:124 Error loading stats for assistant 4831c692-c073-4518-b0c8-27bd34883ba4: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VeryCoolAssistants.jsx:124 Error loading stats for assistant 6f2406d9-b373-4988-b272-8392116f2261: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
VeryCoolAssistants.jsx:124 Error loading stats for assistant cd0b44b7-397e-410d-8835-ce9c3ba584b2: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
  GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: 695b5caf-4884-456d-a3b1-7765427b6095
(anonymous) @ AssistantAwareContext.jsx:42
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '695b5caf-4884-456d-a3b1-7765427b6095', current_assistant_id: '695b5caf-4884-456d-a3b1-7765427b6095', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '695b5caf-4884-456d-a3b1-7765427b6095', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: 695b5caf-4884-456d-a3b1-7765427b6095
(anonymous) @ AssistantAwareContext.jsx:42
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: '695b5caf-4884-456d-a3b1-7765427b6095', current_assistant_id: '695b5caf-4884-456d-a3b1-7765427b6095', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '695b5caf-4884-456d-a3b1-7765427b6095', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.540Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.540Z'}
  GET https://api.vapi.ai/assistant/695b5caf-4884-456d-a3b1-7765427b6095 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for 695b5caf-4884-456d-a3b1-7765427b6095: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
  GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
  GET https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for cd0b44b7-397e-410d-8835-ce9c3ba584b2: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
  GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
  GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: false, hasAttorney: true}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.755Z'}
 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.756Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.756Z'}
 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.757Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
  GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:76
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:179
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://select-assistant.legalscout.net', currentAssistant: undefined, isAssistantSelected: false, assistantSubdomain: undefined}
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.969Z'}
 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.971Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.971Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:12.972Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:109 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:364 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:389 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:109 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:364 🎯 [AssistantAwareContext] DIRECT assistant change: 695b5caf-4884-456d-a3b1-7765427b6095
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
AssistantAwareContext.jsx:389 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 4
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 695b5caf-4884-456d-a3b1-7765427b6095
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 007f9075-d4e3-4266-b626-2495e1d54a60: Kost Family Legal Assistant
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.090Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.091Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.091Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.092Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.151Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.152Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.153Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.154Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
assistantDataService.js:282 ✅ [AssistantDataService] Loaded Vapi data for 4831c692-c073-4518-b0c8-27bd34883ba4: LegalScout
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
assistantDataService.js:272 
            
            
           GET https://api.vapi.ai/assistant/6f2406d9-b373-4988-b272-8392116f2261 404 (Not Found)
(anonymous) @ assistantDataService.js:272
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:308 ⚠️ [AssistantDataService] Could not load Vapi data for 6f2406d9-b373-4988-b272-8392116f2261: Vapi API returned 404
(anonymous) @ assistantDataService.js:308
await in (anonymous)
getAssistantsForAttorney @ assistantDataService.js:268
await in getAssistantsForAttorney
(anonymous) @ EnhancedAssistantDropdown.jsx:82
await in (anonymous)
(anonymous) @ EnhancedAssistantDropdown.jsx:221
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
assistantDataService.js:333 ✅ [AssistantDataService] Loaded 5 assistants for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.193Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:13.193Z'}
supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
standaloneAttorneyManagerFix.js:143 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
standaloneAttorneyManagerFix.js:122 [StandaloneAttorneyManagerFix] Initialized with current attorney: 695b5caf-4884-456d-a3b1-7765427b6095
standaloneAttorneyManagerFix.js:130 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:138 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
standaloneAttorneyManagerFix.js:158 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
standaloneAttorneyManagerFix.js:182 ✅ [StandaloneAttorneyManagerFix] Service loaded
main.jsx:28 🚀 [LegalScout] Starting React app...
main.jsx:44 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:59 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:90 ✅ [LegalScout] React app rendered successfully
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:1587 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14946
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
AssistantAwareContext.jsx:46 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:46
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:47 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:47
mountMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12214
useMemo @ chunk-Q72EVS5P.js?v=9711cfb6:12538
useMemo @ chunk-2N3A5BUM.js?v=9711cfb6:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:30
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14996
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15934
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AssistantAwareContext.jsx:52 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
AssistantAwareContext.jsx:187 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
AssistantAwareContext.jsx:197 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 🚀 [Supabase-Fixed] Initializing client...
 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
 ✅ [Supabase-Fixed] Client created successfully
 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
 🔧 [StandaloneAttorneyManagerFix] Initializing...
 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
 [StandaloneAttorneyManagerFix] Initialized with current attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
 ✅ [StandaloneAttorneyManagerFix] Service loaded
 🚀 [LegalScout] Starting React app...
 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
 ✅ [LegalScout] React app rendered successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'thekosts', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: thekosts
 SimplePreviewPage: Loading assistant config using simple service for subdomain: thekosts
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.352Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.354Z'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔍 [SimpleSubdomain] Loading config for: thekosts
 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
 🔍 [SimpleSubdomain] Loading config for: thekosts
 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', attorney_id: '695b5caf-4884-456d-a3b1-7765427b6095', candidateId: '4831c692-c073-4518-b0c8-27bd34883ba4', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', attorneyId: '695b5caf-4884-456d-a3b1-7765427b6095', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.420Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.421Z'}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [SimpleSubdomain] Loading config for: thekosts
 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
 🔍 [SimpleSubdomain] Loading config for: thekosts
 🔍 [SimpleSubdomain] Looking up assistant ID for: thekosts
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: null, attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.536Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.537Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.611Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.611Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-17T12:05:48.975246+00:00', subdomain: 'thekosts', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 📋 [AssistantDataService] Returning cached data for attorney: 695b5caf-4884-456d-a3b1-7765427b6095
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (5) [{…}, {…}, {…}, {…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
SimplePreviewPage.jsx:211 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AuthContext.jsx:83 🔐 [AuthContext-P1] OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:91 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
AuthContext.jsx:103 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
AuthContext.jsx:118 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
App.jsx:495 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:502 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: null
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.826Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:14.826Z'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🔍 [SimpleSubdomain] Getting Vapi config for: 4831c692-c073-4518-b0c8-27bd34883ba4
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 ✅ [AssistantAwareContext] Using assistant name from UI config: The Kosts
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts', assistantName: 'The Kosts', hasSubdomain: true, hasName: true}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 🌐 Loaded assistant subdomains: {4831c692-c073-4518-b0c8-27bd34883ba4: 'thekosts'}
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 4831c692-c073-4518-b0c8-27bd34883ba4
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: The Kosts
 titleText: The Kosts
 logoUrl: null
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a legal assistant for The Kosts law firm. Help clients with their legal needs professionally and efficiently.
 vapiAssistantId: 4831c692-c073-4518-b0c8-27bd34883ba4
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:15.027Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:12:15.028Z'}
 ✅ [AssistantAwareContext] Using assistant name from UI config: The Kosts
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts', assistantName: 'The Kosts', hasSubdomain: true, hasName: true}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantSubdomain: 'thekosts', attorneySubdomain: 'thekosts', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://thekosts.legalscout.net', embedUrl: 'https://thekosts.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=thekosts&loadFromSupabas…&assistantId=4831c692-c073-4518-b0c8-27bd34883ba4', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://thekosts.legalscout.net'}
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout
 ✅ [SimpleSubdomain] Config loaded for thekosts: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'thekosts', firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: 'The Kosts', assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
 SimplePreviewPage: Final config: {firmName: 'The Kosts', primaryColor: '#2563eb', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', subdomain: 'thekosts'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'The Kosts', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#2563eb', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=thekosts&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 4831c692-c073-4518-b0c8-27bd34883ba4
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 4831c692-c073-4518-b0c8-27bd34883ba4
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '4831c692-c073-4518-b0c8-27bd34883ba4', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '695b5caf-4884-456d-a3b1-7765427b6095', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '695b5caf-4884-456d-a3b1-7765427b6095', vapi_assistant_id: '4831c692-c073-4518-b0c8-27bd34883ba4'}
AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 695b5caf-4884-456d-a3b1-7765427b6095
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
DashboardNew.jsx:480 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:480
setTimeout
(anonymous) @ DashboardNew.jsx:478
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
