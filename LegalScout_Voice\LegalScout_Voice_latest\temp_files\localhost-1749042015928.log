 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
dashboard:207 Supabase loaded from CDN
dashboard:217 Creating Supabase client from CDN
dashboard:221 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
consolidated-dashboard-fix.js:105 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
chunk-Q72EVS5P.js?v=704ffe31:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749032804912:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749032804912:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749032804912:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749032804912:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749032804912:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749032804912:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749032804912:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 Attorney object in ProfileTab: null
 User object in ProfileTab: null
 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
 Using email from previewConfig or previous state: 
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] Attorney is an array, taking first element
 [useStandaloneAttorney] Found attorney in localStorage: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 [useStandaloneAttorney] Setting initial attorney: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 [useStandaloneAttorney] Attorney updated via subscription: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:116 [useStandaloneAttorney] Setting initial attorney: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
DashboardNew.jsx:128 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js:266 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:170 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:171 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:387 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
DashboardNew.jsx:170 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:171 [DashboardNew] Dependencies: user?.id=dc133c1a-239e-4074-b7f5-873310636ff6, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:387 [DashboardNew] Waiting... (authIsLoading: true, userId: dc133c1a-239e-4074-b7f5-873310636ff6, managerReady: true)
DashboardNew.jsx:481 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:482 [DashboardNew] Attorney Vapi Assistant ID: 5c924e25-f521-4c5f-922b-1ffe052a8482
DashboardNew.jsx:512 [DashboardNew] Updated preview config with assistant ID: 5c924e25-f521-4c5f-922b-1ffe052a8482
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:512 [DashboardNew] Updated preview config with assistant ID: 5c924e25-f521-4c5f-922b-1ffe052a8482
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=158b513d:5023:7)
    at async @supabase_supabase-js.js?v=158b513d:4977:16
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
Fetch failed loading: POST "<URL>".
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
VM38633 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM38633 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM38633 consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM38633 consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
VM38633 consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM38633 consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
VM38633 consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
VM38633 consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
VM38633 consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
VM38633 consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM38630 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM38630 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM38630 consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM38630 consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
VM38630 consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM38630 consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
VM38630 consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
VM38630 consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
VM38630 consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
VM38630 consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
VM38639 disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
VM38639 disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
VM38639 disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
VM38639 disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:85 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
debugConfig.js:30 [App] Available subdomains for testing: (3) ['damon', 'damonkost', 'scout']
supabase.js?t=1749032804912:118 Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.dc133c1a-239e-4074-b7f5-873310636ff6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 122.84091789351852)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: Content-Type not acceptable: application/json, application/json
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:270
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
initializeForDashboard @ AutoAssistantReconciler.js:269
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
Fetch failed loading: PATCH "<URL>".
Fetch failed loading: PATCH "<URL>".
Fetch failed loading: PATCH "<URL>".
Fetch failed loading: PATCH "<URL>".
Fetch failed loading: PATCH "<URL>".
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:678 [DashboardNew] Config sent to 0 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:675
(anonymous) @ DashboardNew.jsx:515
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:678 [DashboardNew] Config sent to 0 iframes successfully
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
DashboardNew.jsx:377 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:377
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
(anonymous) @ DashboardNew.jsx:375
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
client.ts:19 [vite] connecting...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
client.ts:155 [vite] connected.
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
consolidated-dashboard-fix.js:105 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749032804912:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749032804912:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749032804912:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749032804912:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749032804912:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749032804912:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749032804912:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
DashboardNew.jsx:1394 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749032804912:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749032804912:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241052}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241052}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241054}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241054}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241176}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241176}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241179}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241179}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241198}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241198}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241199}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241199}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241227}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241227}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241228}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241228}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241419}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241419}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241426}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241426}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241614}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241614}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241615}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040241615}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=158b513d:5023:7)
    at async @supabase_supabase-js.js?v=158b513d:4977:16
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=158b513d:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4977
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '695b5caf-4884-456d-a3b1-7765427b6095', created_at: '2025-06-02T18:14:20.727881+00:00', updated_at: '2025-06-04T00:21:42.401864+00:00', subdomain: 'damonkost', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:93
await in initAuth
(anonymous) @ AuthContext.jsx:127
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:85 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242312}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: 5c924e25-f521-4c5f-922b-1ffe052a8482
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242327}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242327}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242327}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
supabase.js?t=1749032804912:118 Supabase connection test successful!
debugConfig.js:30 [App] Available subdomains for testing: (3) ['damon', 'damonkost', 'scout']
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
debugConfig.js:30 [App] Available subdomains for testing: (3) ['damon', 'damonkost', 'scout']
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: [{…}]
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabase.js?t=1749032804912:118 Supabase connection test successful!
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242573}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242573}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242573}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242573}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242575}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242575}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242575}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242575}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: 5c924e25-f521-4c5f-922b-1ffe052a8482
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242586}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242586}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242586}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749040242586}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: [{…}]
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.dc133c1a-239e-4074-b7f5-873310636ff6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 122.84784048611111)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: Content-Type not acceptable: application/json, application/json
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:284
setInterval
window.setInterval @ consolidated-dashboard-fix.js:83
initializeForDashboard @ AutoAssistantReconciler.js:283
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.dc133c1a-239e-4074-b7f5-873310636ff6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 122.85131407407407)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: Content-Type not acceptable: application/json, application/json
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:284
setInterval
window.setInterval @ consolidated-dashboard-fix.js:83
initializeForDashboard @ AutoAssistantReconciler.js:283
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.dc133c1a-239e-4074-b7f5-873310636ff6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 122.85479555555555)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: Content-Type not acceptable: application/json, application/json
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:284
setInterval
window.setInterval @ consolidated-dashboard-fix.js:83
initializeForDashboard @ AutoAssistantReconciler.js:283
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.dc133c1a-239e-4074-b7f5-873310636ff6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.R3qrI7ra_bZ04g8dMr_i4XH7vzLM8CKd_Yn1ZNP6gIc', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 122.85864662037037)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:105 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: Content-Type not acceptable: application/json, application/json
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:284
setInterval
window.setInterval @ consolidated-dashboard-fix.js:83
initializeForDashboard @ AutoAssistantReconciler.js:283
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
consolidated-dashboard-fix.js:105 Fetch finished loading: POST "https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/token?grant_type=refresh_token".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ supabase-js@2:7
Y.h.headers @ supabase-js@2:7
Y @ supabase-js@2:7
r @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
_refreshAccessToken @ supabase-js@2:7
_callRefreshToken @ supabase-js@2:7
_recoverAndRefresh @ supabase-js@2:7
await in _recoverAndRefresh
(anonymous) @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: TOKEN_REFRESHED
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async @supabase_supabase-js.js?v=158b513d:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async @supabase_supabase-js.js?v=158b513d:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=158b513d:6204:9)
    at async @supabase_supabase-js.js?v=158b513d:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=158b513d:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6455
(anonymous) @ @supabase_supabase-js.js?v=158b513d:5542
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
DashboardNew.jsx:528 [DashboardNew] Tab changed to: agent
 [AgentTab] Loading voice settings from Vapi assistant: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [AgentTab] Loading voice settings from Vapi assistant: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [VapiMcpService] Getting assistant: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 → http://localhost:5175/api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [12:59:47] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [VapiMcpService] Getting assistant: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 → http://localhost:5175/api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [12:59:47] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:343 [StandaloneAttorneyManager] Using default Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [vite] connecting...
 VapiAssistantService: Attempting to connect to Vapi MCP server
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 6734febc...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 6734febc...
 VapiAssistantService: Successfully connected to Vapi MCP server
 [VapiMcpService] Getting assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a → http://localhost:5175/api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [vite] connected.
 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [12:59:47] [VapiMcpService] Retrieving assistant {assistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
vapiMcpService.js:388 [VapiMcpService] Getting assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:392 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a → http://localhost:5175/api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=158b513d:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=158b513d:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=158b513d:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=158b513d:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=158b513d:6261
(anonymous) @ @supabase_supabase-js.js?v=158b513d:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
vapiMcpService.js:419 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
loggerUtils.js:133 [2025-06-04T12:59:48.114Z] [VapiAssistantService] Retrieved assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
vapiMcpService.js:419 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
consolidated-dashboard-fix.js:105 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749032804912:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749032804912:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749032804912:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
vapiMcpService.js:419 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
DashboardNew.jsx:1325 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damonkost', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damonkost
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damonkost
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
 [12:59:48] [VapiMcpService] Retrieving assistant {assistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482'}
 [Vapi MCP] GET https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482 with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [2025-06-04T12:59:48.571Z] [VapiAssistantService] Retrieved assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 [12:59:48] [VapiMcpService] Assistant verified in Vapi {id: '5c924e25-f521-4c5f-922b-1ffe052a8482', name: 'LegalScout'}
 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'alloy', provider: 'openai'}
 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'alloy', provider: 'openai'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988634}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988634}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988634}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988634}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988634}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988636}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988636}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988636}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988636}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988636}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
_recoverAndRefresh @ @supabase_supabase-js.js:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js:4977
(anonymous) @ @supabase_supabase-js.js:5542
(anonymous) @ @supabase_supabase-js.js:4828
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview:99
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:211
window.fetch @ disable-automatic-as…ant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988739}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988739}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988739}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988739}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988739}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988742}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988742}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988742}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988742}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988742}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:152
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: 5c924e25-f521-4c5f-922b-1ffe052a8482
 voiceId: alloy
 voiceProvider: openai
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: 5c924e25-f521-4c5f-922b-1ffe052a8482
 voiceId: alloy
 voiceProvider: openai
 chatActive: false
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988802}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988802}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988802}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988802}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988802}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988803}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988803}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988803}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988803}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988803}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [12:59:48] [VapiMcpService] Assistant verified in Vapi {id: '5c924e25-f521-4c5f-922b-1ffe052a8482', name: 'LegalScout'}
 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'alloy', provider: 'openai'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'alloy', provider: 'openai'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988851}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988851}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988851}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988851}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041988851}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989039}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989039}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989039}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989039}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989039}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989051}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989051}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989051}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989051}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989051}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:85 OAuth user data: {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'dc133c1a-239e-4074-b7f5-873310636ff6'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: 'dc133c1a-239e-4074-b7f5-873310636ff6', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:04:30.413656Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
consolidated-dashboard-fix.js:105 
            
            
           POST http://localhost:5175/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damonkost with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.GOhpwxkAoInAYHnQC2nCV_-05BphFtx4yX7b_qORO5g', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabase.js?t=1749032804912:118 Supabase connection test successful!
debugConfig.js:30 [App] Available subdomains for testing: (3) ['damon', 'damonkost', 'scout']
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: [{…}]
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989621}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989622}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #aa4b4b
EnhancedPreviewNew.jsx:465 secondaryColor: #46ce93
EnhancedPreviewNew.jsx:466 vapiInstructions: You are a legal assistant helping clients with their legal needs.
EnhancedPreviewNew.jsx:467 vapiAssistantId: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:468 voiceId: alloy
EnhancedPreviewNew.jsx:469 voiceProvider: openai
EnhancedPreviewNew.jsx:470 chatActive: false
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749041989671}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: null, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', vapi_assistant_id: '5c924e25-f521-4c5f-922b-1ffe052a8482', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 5c924e25-f521-4c5f-922b-1ffe052a8482
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '5c924e25-f521-4c5f-922b-1ffe052a8482', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: [{…}]
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
 [ReactPolyfill] Stopped monitoring React.createContext
