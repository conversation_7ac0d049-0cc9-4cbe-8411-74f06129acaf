import React, { useState } from 'react';
import MapDossierView from './MapDossierView.jsx';
import { withDevTools, createDebugger } from '../utils/debugConfig';

// Create debugger for MapView component
const debug = createDebugger('MapView');

const MapView = ({ attorney }) => {
  const [isVisible, setIsVisible] = useState(true);
  
  // Extract location from attorney data
  const location = attorney?.location || {
    lat: 40.7128, // Default to NYC if no location
    lng: -74.0060
  };
  
  // Format attorneys array from single attorney object
  const attorneys = attorney ? [{
    name: attorney.name || 'Attorney',
    practiceArea: attorney.practiceArea || 'General Practice',
    specialty: attorney.specialty,
    latitude: attorney.location?.lat,
    longitude: attorney.location?.lng,
    distance: attorney.distance || '< 5',
    phone: attorney.phone,
    email: attorney.email,
    website: attorney.website,
    address: attorney.address
  }] : [];
  
  return (
    <div className="map-view-container">
      <div className="map-controls">
        <button 
          className="toggle-map-button"
          onClick={() => setIsVisible(!isVisible)}
        >
          {isVisible ? 'Hide Map' : 'Show Map'}
        </button>
      </div>
      
      <MapDossierView 
        isVisible={isVisible} 
        location={location} 
        attorneys={attorneys}
      />
    </div>
  );
};

// Export the component with enhanced DevTools support
export default withDevTools(MapView, {
  displayName: 'MapView',
  type: 'container',
  description: 'Container component for the map display with attorney information',
  responsibleFor: ['map visibility toggle', 'attorney data formatting']
}); 