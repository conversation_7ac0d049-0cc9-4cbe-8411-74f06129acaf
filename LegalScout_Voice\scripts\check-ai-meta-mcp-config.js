#!/usr/bin/env node
/**
 * Check AI Meta MCP configuration
 *
 * This script checks if the AI Meta MCP server is properly configured in the .cursor/mcp.json file.
 *
 * Usage:
 *   node scripts/check-ai-meta-mcp-config.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the .cursor/mcp.json file
const mcpConfigPath = path.resolve(__dirname, '..', '.cursor', 'mcp.json');

console.log('Checking AI Meta MCP configuration...');
console.log('MCP config path:', mcpConfigPath);

try {
  // Read the MCP configuration file
  const mcpConfigContent = fs.readFileSync(mcpConfigPath, 'utf-8');
  const mcpConfig = JSON.parse(mcpConfigContent);
  
  console.log('MCP configuration loaded successfully');
  
  // Check if the AI Meta MCP server is configured
  if (mcpConfig.servers && mcpConfig.servers['ai-meta-mcp']) {
    console.log('✅ AI Meta MCP server is configured');
    
    // Check if the AI Meta MCP server has the correct configuration
    const aiMetaMcpServer = mcpConfig.servers['ai-meta-mcp'];
    
    if (aiMetaMcpServer.command === 'node' && 
        Array.isArray(aiMetaMcpServer.args) && 
        aiMetaMcpServer.args.length > 0 &&
        aiMetaMcpServer.args[0].includes('ai-meta-mcp-server\\build\\index.js')) {
      console.log('✅ AI Meta MCP server has the correct command and arguments');
      
      // Check if the environment variables are configured
      if (aiMetaMcpServer.env) {
        console.log('✅ AI Meta MCP server has environment variables configured');
        console.log('Environment variables:');
        Object.entries(aiMetaMcpServer.env).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`);
        });
      } else {
        console.log('❌ AI Meta MCP server is missing environment variables');
      }
    } else {
      console.log('❌ AI Meta MCP server has incorrect command or arguments');
      console.log('Expected: command="node", args=["...\\ai-meta-mcp-server\\build\\index.js"]');
      console.log('Actual: command="' + aiMetaMcpServer.command + '", args=' + JSON.stringify(aiMetaMcpServer.args));
    }
  } else {
    console.log('❌ AI Meta MCP server is not configured');
    console.log('Available servers:');
    Object.keys(mcpConfig.servers || {}).forEach(server => {
      console.log(`  ${server}`);
    });
  }
} catch (error) {
  console.error('Error checking AI Meta MCP configuration:', error);
  process.exit(1);
}
