dashboard:16 🚀 [LegalScout] Initializing environment...
dashboard:38 ✅ [LegalScout] Environment initialized
dashboard:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<' (at emergency-auth-fix.js:1:1)
index-3d96563a.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-3d96563a.js:54 [VapiLoader] Starting Vapi SDK loading process
index-3d96563a.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
index-3d96563a.js:141 [VapiMcpService] Created clean fetch from iframe
index-3d96563a.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-3d96563a.js:987 🚨 [App.jsx] Dashboard accessed without user - showing debug info
hwe @ index-3d96563a.js:987
T2 @ index-3d96563a.js:38
SR @ index-3d96563a.js:40
wR @ index-3d96563a.js:40
MU @ index-3d96563a.js:40
mv @ index-3d96563a.js:40
yR @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:305 ❌ No email found from any source!
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:305
Rp @ index-3d96563a.js:38
EU @ index-3d96563a.js:38
(anonymous) @ index-3d96563a.js:305
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
VM106 emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<' (at VM106 emergency-auth-fix.js:1:1)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
VM113 emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<' (at VM113 emergency-auth-fix.js:1:1)
VM107 index-3d96563a.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM107 index-3d96563a.js:54 [VapiLoader] Starting Vapi SDK loading process
VM107 index-3d96563a.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
VM107 index-3d96563a.js:141 [VapiMcpService] Created clean fetch from iframe
VM107 index-3d96563a.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM114 index-3d96563a.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM114 index-3d96563a.js:54 [VapiLoader] Starting Vapi SDK loading process
VM114 index-3d96563a.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
VM114 index-3d96563a.js:141 [VapiMcpService] Created clean fetch from iframe
VM114 index-3d96563a.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index.ts:5 Loaded contentScript
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/manage-auth-state 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
index-3d96563a.js:164 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (VM114 index-3d96563a.js:164:30367)
    at async VM114 index-3d96563a.js:48:80287
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/check-preview-consistency 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 Check consistency error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async VM114 index-3d96563a.js:164:28903
    at async Object.handleAuthState (VM114 index-3d96563a.js:164:30574)
    at async VM114 index-3d96563a.js:48:80287
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:399 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ index-3d96563a.js:399
setTimeout
(anonymous) @ index-3d96563a.js:399
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<' (at emergency-auth-fix.js:1:1)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<' (at emergency-auth-fix.js:1:1)
index-3d96563a.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-3d96563a.js:54 [VapiLoader] Starting Vapi SDK loading process
index-3d96563a.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
index-3d96563a.js:141 [VapiMcpService] Created clean fetch from iframe
index-3d96563a.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-3d96563a.js:48 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-3d96563a.js:54 [VapiLoader] Starting Vapi SDK loading process
index-3d96563a.js:54 [VapiLoader] Attempting to import @vapi-ai/web package
index-3d96563a.js:141 [VapiMcpService] Created clean fetch from iframe
index-3d96563a.js:141 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/manage-auth-state 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 Auth state error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (index-3d96563a.js:164:30367)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/check-preview-consistency 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 Check consistency error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async index-3d96563a.js:164:28903
    at async Object.handleAuthState (index-3d96563a.js:164:30574)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/manage-auth-state 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 Auth state error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (index-3d96563a.js:164:30367)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 
            
            
           POST https://legalscout.net/api/sync-tools/check-preview-consistency 405 (Method Not Allowed)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 Check consistency error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
index-3d96563a.js:164 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async index-3d96563a.js:164:28903
    at async Object.handleAuthState (index-3d96563a.js:164:30574)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:164
(anonymous) @ index-3d96563a.js:164
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
await in (anonymous)
(anonymous) @ index-3d96563a.js:48
__ @ index-3d96563a.js:40
Id @ index-3d96563a.js:40
(anonymous) @ index-3d96563a.js:40
M @ index-3d96563a.js:25
G @ index-3d96563a.js:25
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
