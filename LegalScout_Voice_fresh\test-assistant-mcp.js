#!/usr/bin/env node

/**
 * Test Assistant using MCP
 * This script tests the assistant configuration and voice settings
 */

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

async function testAssistantMCP() {
  console.log('🧪 Testing Assistant via MCP...');
  console.log('📋 Assistant ID:', ASSISTANT_ID);
  
  try {
    // Import MCP client
    const { Client } = await import('@modelcontextprotocol/sdk/client/index.js');
    const { StreamableHTTPClientTransport } = await import('@modelcontextprotocol/sdk/client/streamableHttp.js');
    
    // Set up MCP client
    const mcpClient = new Client({
      name: 'vapi-test-client',
      version: '1.0.0',
    });
    
    const serverUrl = 'https://mcp.vapi.ai/mcp';
    const headers = {
      Authorization: `Bearer 6734febc-fc65-4669-93b0-929b31ff6564`,
    };
    
    const transport = new StreamableHTTPClientTransport(
      new URL(serverUrl), 
      { requestInit: { headers } }
    );
    
    console.log('🔌 Connecting to Vapi MCP server...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');
    
    // Helper function to parse responses
    function parseResponse(response) {
      if (!response?.content) return response;
      const textItem = response.content.find(item => item.type === 'text');
      if (textItem?.text) {
        try {
          return JSON.parse(textItem.text);
        } catch {
          return textItem.text;
        }
      }
      return response;
    }
    
    // Test 1: Get assistant details
    console.log('\n📋 Test 1: Getting assistant details...');
    const assistantResponse = await mcpClient.callTool({
      name: 'get_assistant',
      arguments: { assistantId: ASSISTANT_ID }
    });
    
    const assistant = parseResponse(assistantResponse);
    console.log('✅ Assistant found:', assistant.name);
    console.log('🎤 Voice config:', JSON.stringify(assistant.voice, null, 2));
    console.log('🤖 Model config:', JSON.stringify(assistant.llm, null, 2));
    
    // Test 2: List recent calls
    console.log('\n📞 Test 2: Checking recent calls...');
    const callsResponse = await mcpClient.callTool({
      name: 'list_calls',
      arguments: {}
    });
    
    const calls = parseResponse(callsResponse);
    const recentCalls = calls.slice(0, 3); // Get last 3 calls
    
    console.log(`📊 Found ${calls.length} total calls, showing last 3:`);
    recentCalls.forEach((call, index) => {
      console.log(`   ${index + 1}. ${call.id} - Status: ${call.status} - Reason: ${call.endedReason || 'N/A'}`);
    });
    
    // Test 3: Voice configuration analysis
    console.log('\n🔍 Test 3: Voice configuration analysis...');
    const voiceConfig = assistant.voice;
    
    if (voiceConfig.provider === 'openai' && voiceConfig.voiceId === 'echo') {
      console.log('✅ Voice provider and voiceId are correct');
    } else {
      console.log('❌ Voice configuration issue detected');
      console.log('   Expected: provider=openai, voiceId=echo');
      console.log(`   Actual: provider=${voiceConfig.provider}, voiceId=${voiceConfig.voiceId}`);
    }
    
    if (voiceConfig.model) {
      console.log('⚠️  Warning: Voice config has "model" field which may cause issues with OpenAI voices');
      console.log(`   Model field value: ${voiceConfig.model}`);
    } else {
      console.log('✅ Voice config correctly has no "model" field for OpenAI provider');
    }
    
    // Test 4: Check for ElevenLabs quota issues in recent calls
    console.log('\n🔍 Test 4: Checking for ElevenLabs quota issues...');
    const elevenLabsErrors = recentCalls.filter(call => 
      call.endedReason && call.endedReason.includes('eleven-labs')
    );
    
    if (elevenLabsErrors.length > 0) {
      console.log(`❌ Found ${elevenLabsErrors.length} calls with ElevenLabs issues:`);
      elevenLabsErrors.forEach(call => {
        console.log(`   - ${call.id}: ${call.endedReason}`);
      });
      console.log('💡 This suggests the voice config still has ElevenLabs dependencies');
    } else {
      console.log('✅ No recent ElevenLabs quota errors found');
    }
    
    await mcpClient.close();
    console.log('\n🎉 Test completed successfully!');
    
    return {
      assistant,
      calls: recentCalls,
      voiceConfigOk: voiceConfig.provider === 'openai' && voiceConfig.voiceId === 'echo',
      hasElevenLabsIssues: elevenLabsErrors.length > 0
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return null;
  }
}

// Run the test
testAssistantMCP().then(result => {
  if (result) {
    console.log('\n📊 Test Summary:');
    console.log(`   Voice Config OK: ${result.voiceConfigOk ? '✅' : '❌'}`);
    console.log(`   ElevenLabs Issues: ${result.hasElevenLabsIssues ? '❌' : '✅'}`);
    
    if (result.voiceConfigOk && !result.hasElevenLabsIssues) {
      console.log('\n🎉 Assistant should be working properly now!');
    } else {
      console.log('\n⚠️  Assistant may still have issues. Check the details above.');
    }
  }
});
