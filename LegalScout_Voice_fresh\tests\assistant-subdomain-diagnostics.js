/**
 * Incisive Assistant Subdomain Diagnostics
 * 
 * This test suite provides comprehensive diagnostics for the assistant-level
 * subdomain system, pinpointing exactly where failures occur.
 */

// Test Configuration
const TEST_CONFIG = {
  subdomain: 'assistant1test',
  expectedAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d',
  expectedAttorneyId: '87756a2c-a398-43f2-889a-b8815684df71',
  expectedFirmName: 'LegalScout',
  supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'
};

class AssistantSubdomainDiagnostics {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  /**
   * Log test result
   */
  log(test, status, message, data = null) {
    const result = {
      test,
      status, // 'PASS', 'FAIL', 'WARN', 'INFO'
      message,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.results.push(result);
    
    const emoji = {
      'PASS': '✅',
      'FAIL': '❌', 
      'WARN': '⚠️',
      'INFO': 'ℹ️'
    }[status] || '📋';
    
    console.log(`${emoji} [${test}] ${message}`);
    if (data) {
      console.log('   Data:', data);
    }
  }

  /**
   * Test 1: Direct Database Connection
   */
  async testDirectDatabaseConnection() {
    try {
      const url = `${TEST_CONFIG.supabaseUrl}/rest/v1/`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': TEST_CONFIG.supabaseKey,
          'Authorization': `Bearer ${TEST_CONFIG.supabaseKey}`
        }
      });

      if (response.ok) {
        this.log('DB_CONNECTION', 'PASS', 'Direct database connection successful');
        return true;
      } else {
        this.log('DB_CONNECTION', 'FAIL', `Database connection failed: ${response.status}`);
        return false;
      }
    } catch (error) {
      this.log('DB_CONNECTION', 'FAIL', `Database connection error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test 2: Assistant Subdomain Mapping Exists
   */
  async testAssistantSubdomainMapping() {
    try {
      const url = `${TEST_CONFIG.supabaseUrl}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_CONFIG.subdomain}&is_active=eq.true`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': TEST_CONFIG.supabaseKey,
          'Authorization': `Bearer ${TEST_CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        this.log('SUBDOMAIN_MAPPING', 'FAIL', `Query failed: ${response.status}`);
        return null;
      }

      const data = await response.json();
      
      if (!data || data.length === 0) {
        this.log('SUBDOMAIN_MAPPING', 'FAIL', 'No mapping found in database');
        return null;
      }

      const mapping = data[0];
      
      // Validate mapping data
      const validations = [
        { field: 'assistant_id', expected: TEST_CONFIG.expectedAssistantId, actual: mapping.assistant_id },
        { field: 'attorney_id', expected: TEST_CONFIG.expectedAttorneyId, actual: mapping.attorney_id },
        { field: 'subdomain', expected: TEST_CONFIG.subdomain, actual: mapping.subdomain },
        { field: 'is_active', expected: true, actual: mapping.is_active }
      ];

      let allValid = true;
      validations.forEach(({ field, expected, actual }) => {
        if (actual !== expected) {
          this.log('SUBDOMAIN_MAPPING', 'FAIL', `${field} mismatch: expected ${expected}, got ${actual}`);
          allValid = false;
        }
      });

      if (allValid) {
        this.log('SUBDOMAIN_MAPPING', 'PASS', 'Assistant subdomain mapping is correct', mapping);
        return mapping;
      } else {
        this.log('SUBDOMAIN_MAPPING', 'FAIL', 'Assistant subdomain mapping has incorrect data', mapping);
        return null;
      }
    } catch (error) {
      this.log('SUBDOMAIN_MAPPING', 'FAIL', `Mapping query error: ${error.message}`);
      return null;
    }
  }

  /**
   * Test 3: Attorney Data Retrieval
   */
  async testAttorneyDataRetrieval() {
    try {
      const url = `${TEST_CONFIG.supabaseUrl}/rest/v1/attorneys?id=eq.${TEST_CONFIG.expectedAttorneyId}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': TEST_CONFIG.supabaseKey,
          'Authorization': `Bearer ${TEST_CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        this.log('ATTORNEY_DATA', 'FAIL', `Attorney query failed: ${response.status}`);
        return null;
      }

      const data = await response.json();
      
      if (!data || data.length === 0) {
        this.log('ATTORNEY_DATA', 'FAIL', 'Attorney not found in database');
        return null;
      }

      const attorney = data[0];
      
      // Validate critical fields
      const requiredFields = ['id', 'firm_name', 'email'];
      const missingFields = requiredFields.filter(field => !attorney[field]);
      
      if (missingFields.length > 0) {
        this.log('ATTORNEY_DATA', 'FAIL', `Missing required fields: ${missingFields.join(', ')}`, attorney);
        return null;
      }

      this.log('ATTORNEY_DATA', 'PASS', 'Attorney data retrieved successfully', {
        id: attorney.id,
        firm_name: attorney.firm_name,
        email: attorney.email
      });
      
      return attorney;
    } catch (error) {
      this.log('ATTORNEY_DATA', 'FAIL', `Attorney query error: ${error.message}`);
      return null;
    }
  }

  /**
   * Test 4: Assistant Routing Service
   */
  async testAssistantRoutingService() {
    try {
      // Simulate the assistant routing service logic
      const mappingUrl = `${TEST_CONFIG.supabaseUrl}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_CONFIG.subdomain}&is_active=eq.true`;
      
      const mappingResponse = await fetch(mappingUrl, {
        method: 'GET',
        headers: {
          'apikey': TEST_CONFIG.supabaseKey,
          'Authorization': `Bearer ${TEST_CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!mappingResponse.ok) {
        this.log('ROUTING_SERVICE', 'FAIL', 'Failed to get assistant mapping');
        return null;
      }

      const mappingData = await mappingResponse.json();
      if (!mappingData || mappingData.length === 0) {
        this.log('ROUTING_SERVICE', 'FAIL', 'No assistant mapping found');
        return null;
      }

      const mapping = mappingData[0];

      // Get attorney data
      const attorneyUrl = `${TEST_CONFIG.supabaseUrl}/rest/v1/attorneys?id=eq.${mapping.attorney_id}`;
      
      const attorneyResponse = await fetch(attorneyUrl, {
        method: 'GET',
        headers: {
          'apikey': TEST_CONFIG.supabaseKey,
          'Authorization': `Bearer ${TEST_CONFIG.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!attorneyResponse.ok) {
        this.log('ROUTING_SERVICE', 'FAIL', 'Failed to get attorney data');
        return null;
      }

      const attorneyData = await attorneyResponse.json();
      if (!attorneyData || attorneyData.length === 0) {
        this.log('ROUTING_SERVICE', 'FAIL', 'No attorney data found');
        return null;
      }

      const attorney = attorneyData[0];

      // Create assistant config (simulate the service)
      const assistantConfig = {
        id: attorney.id,
        assistant_id: mapping.assistant_id,
        attorney_id: mapping.attorney_id,
        subdomain: mapping.subdomain,
        firmName: attorney.firm_name || mapping.firm_name || "LegalScout",
        vapi_assistant_id: mapping.assistant_id,
        current_assistant_id: mapping.assistant_id,
        is_primary_assistant: mapping.is_primary,
        loadedVia: 'assistant_routing_service_test'
      };

      // Validate the config
      const configValidations = [
        { field: 'assistant_id', expected: TEST_CONFIG.expectedAssistantId, actual: assistantConfig.assistant_id },
        { field: 'attorney_id', expected: TEST_CONFIG.expectedAttorneyId, actual: assistantConfig.attorney_id },
        { field: 'subdomain', expected: TEST_CONFIG.subdomain, actual: assistantConfig.subdomain },
        { field: 'firmName', expected: TEST_CONFIG.expectedFirmName, actual: assistantConfig.firmName }
      ];

      let configValid = true;
      configValidations.forEach(({ field, expected, actual }) => {
        if (actual !== expected) {
          this.log('ROUTING_SERVICE', 'FAIL', `Config ${field} mismatch: expected ${expected}, got ${actual}`);
          configValid = false;
        }
      });

      if (configValid) {
        this.log('ROUTING_SERVICE', 'PASS', 'Assistant routing service logic works correctly', assistantConfig);
        return assistantConfig;
      } else {
        this.log('ROUTING_SERVICE', 'FAIL', 'Assistant routing service produces incorrect config', assistantConfig);
        return null;
      }
    } catch (error) {
      this.log('ROUTING_SERVICE', 'FAIL', `Routing service error: ${error.message}`);
      return null;
    }
  }

  /**
   * Test 5: Browser Environment Simulation
   */
  async testBrowserEnvironment() {
    try {
      // Test if we can simulate the browser environment
      const hasWindow = typeof window !== 'undefined';
      const hasFetch = typeof fetch !== 'undefined';
      const hasHeaders = typeof Headers !== 'undefined';
      
      this.log('BROWSER_ENV', 'INFO', 'Browser environment check', {
        hasWindow,
        hasFetch,
        hasHeaders,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'
      });

      if (hasFetch && hasHeaders) {
        this.log('BROWSER_ENV', 'PASS', 'Browser environment is compatible');
        return true;
      } else {
        this.log('BROWSER_ENV', 'WARN', 'Browser environment may have compatibility issues');
        return false;
      }
    } catch (error) {
      this.log('BROWSER_ENV', 'FAIL', `Browser environment error: ${error.message}`);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Assistant Subdomain Diagnostics...\n');
    
    const tests = [
      { name: 'Database Connection', fn: () => this.testDirectDatabaseConnection() },
      { name: 'Browser Environment', fn: () => this.testBrowserEnvironment() },
      { name: 'Assistant Subdomain Mapping', fn: () => this.testAssistantSubdomainMapping() },
      { name: 'Attorney Data Retrieval', fn: () => this.testAttorneyDataRetrieval() },
      { name: 'Assistant Routing Service', fn: () => this.testAssistantRoutingService() }
    ];

    const results = {};
    
    for (const test of tests) {
      console.log(`\n📋 Running: ${test.name}`);
      try {
        results[test.name] = await test.fn();
      } catch (error) {
        this.log(test.name.toUpperCase().replace(' ', '_'), 'FAIL', `Test crashed: ${error.message}`);
        results[test.name] = null;
      }
    }

    // Summary
    console.log('\n📊 Test Summary:');
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARN').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    
    if (failed === 0) {
      console.log('\n🎉 All critical tests passed! The assistant subdomain system should work.');
    } else {
      console.log('\n🚨 Critical failures detected. The assistant subdomain system needs fixes.');
    }

    return {
      passed,
      failed,
      warnings,
      results: this.results,
      testResults: results
    };
  }
}

// Export for use in browser or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AssistantSubdomainDiagnostics;
} else if (typeof window !== 'undefined') {
  window.AssistantSubdomainDiagnostics = AssistantSubdomainDiagnostics;
}

// Auto-run if called directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - expose global function
  window.runAssistantDiagnostics = async () => {
    const diagnostics = new AssistantSubdomainDiagnostics();
    return await diagnostics.runAllTests();
  };
  
  console.log('🧪 Assistant Subdomain Diagnostics loaded. Run runAssistantDiagnostics() in console.');
}
