import React, { useState, useEffect, useRef } from 'react';
import ReactDOMServer from 'react-dom/server';
import './SimpleDemoPage.css';
import CreateAgentButton from '../components/preview/CreateAgentButton';

// Add custom style tag for tab buttons
const tabStyles = `
  .config-tab:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: #D85722 !important;
  }
  .config-tab.active {
    border-bottom-color: #D85722 !important;
  }
  .config-tab {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }
  button.config-tab::-moz-focus-inner {
    border: 0 !important;
  }
  button:focus-visible {
    outline: none !important;
  }
  button.config-tab.active::after {
    display: none !important;
  }
  
  /* Consistent styling for all input elements */
  .config-section input[type="text"],
  .config-section input[type="tel"],
  .config-section input[type="url"],
  .config-section input[type="email"],
  .config-section textarea,
  .config-section select {
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    font-size: 14px;
    width: 100%;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .config-section input[type="text"]:focus,
  .config-section input[type="tel"]:focus,
  .config-section input[type="url"]:focus,
  .config-section input[type="email"]:focus,
  .config-section textarea:focus,
  .config-section select:focus {
    border-color: #D85722;
    box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.2);
    background-color: rgba(255, 255, 255, 0.07);
    outline: none;
  }
  
  [data-theme="dark"] .config-section input[type="text"],
  [data-theme="dark"] .config-section input[type="tel"],
  [data-theme="dark"] .config-section input[type="url"],
  [data-theme="dark"] .config-section input[type="email"],
  [data-theme="dark"] .config-section textarea,
  [data-theme="dark"] .config-section select {
    background-color: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(3px);
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
  }
  
  [data-theme="dark"] .config-section input[type="text"]:focus,
  [data-theme="dark"] .config-section input[type="tel"]:focus,
  [data-theme="dark"] .config-section input[type="url"]:focus,
  [data-theme="dark"] .config-section input[type="email"]:focus,
  [data-theme="dark"] .config-section textarea:focus,
  [data-theme="dark"] .config-section select:focus {
    border-color: #D85722;
    box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.3);
    background-color: rgba(0, 0, 0, 0.2);
  }
  
  /* File input styling */
  .config-section input[type="file"] {
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    cursor: pointer;
  }
  
  [data-theme="dark"] .config-section input[type="file"] {
    background-color: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(3px);
    border-color: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.8);
  }
  
  /* Color picker styling */
  .config-section .color-dot-button {
    border: 2px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  [data-theme="dark"] .config-section .color-dot-button {
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
`;

const SimpleDemoPage = ({
  firmName,
  logoUrl,
  state,
  primaryColor,
  secondaryColor,
  backgroundColor,
  backgroundOpacity,
  welcomeMessage,
  informationGathering,
  practiceDescription,
  previewHeight,
  setPreviewHeight,
  attorneyName,
  selectedPracticeArea,
  handlePracticeAreaChange,
  showPreview,
  setShowPreview,
  handleLogoUpload,
  handleRemoveLogo,
  practiceAreas,
  activeConfigTab,
  setActiveConfigTab,
  goToPreview,
  setFirmName,
  setAttorneyName,
  setPracticeDescription,
  setState,
  setWelcomeMessage,
  setInformationGathering,
  setPrimaryColor,
  setSecondaryColor,
  setBackgroundColor,
  setBackgroundOpacity,
  iframeRef,
  firmUrl,
  setFirmUrl,
  isLoading,
  handleUrlSubmit,
  isDarkTheme,
  setLogoUrl,
  buttonText,
  setButtonText,
  buttonOpacity,
  setButtonOpacity,
  practiceAreaBackgroundOpacity,
  setPracticeAreaBackgroundOpacity,
  // New params for attorney contact details
  attorneyAddress = '',
  setAttorneyAddress = () => {},
  attorneyPhone = '',
  setAttorneyPhone = () => {},
  schedulingLink = '',
  setSchedulingLink = () => {},
  // Add button color params
  buttonColor = '#2C3E50',
  setButtonColor = () => {}
}) => {
  const [frameWidth, setFrameWidth] = useState("100%");
  const [configMode, setConfigMode] = useState('url');
  const [isValidUrl, setIsValidUrl] = useState(false);
  const [firmNameAnimation, setFirmNameAnimation] = useState('fadeIn');
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');
  const [activeFlipboardItem, setActiveFlipboardItem] = useState(0);
  const flipboardRef = useRef(null);
  const iconRef = useRef(null);
  const [activeIconFlip, setActiveIconFlip] = useState(false);
  const [currentIcon, setCurrentIcon] = useState(0);
  const [currentAttrIcon, setCurrentAttrIcon] = useState(0);
  const [activeAttrFlip, setActiveAttrFlip] = useState(false);
  const attrFlipboardRef = useRef(null);
  const [isConfigVisible, setIsConfigVisible] = useState(false);
  const [isAppearanceExpanded, setIsAppearanceExpanded] = useState(false);
  
  // Icons for each role
  const roleIcons = [
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21 10L12 5L3 10L12 15L21 10ZM12 18.84L5 14.5V18L12 22L19 18V14.5L12 18.84Z" fill="currentColor" />
    </svg>,
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>, // Default right arrow
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m21 15-9-9-9 9"/><path d="m21 21-9-9-9 9"/></svg>, // Intake
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="m8 12 3 3 6-6"/></svg>, // Qualification
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8M12 8v8"/></svg>, // Add icon
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z"/></svg>, // Edit
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>, // Delete
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="18" height="11" x="3" y="11" rx="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>, // Lock
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>, // Legal Scout
  ];
  
  // Goal texts array for the animated flipboard
  const goalTexts = [
    "Acquire Higher-Value Clients",
    "Automate Client Intake Process",
    "Qualify Leads 24/7",
    "Improve Client Response Time",
    "Reduce Administrative Costs",
    "Focus On High-Value Work",
    "Enhance Client Experience",
    "Scale Your Practice Efficiently"
  ];
  
  // Additional value icons for display in emoji form
  const valueIcons = ["⚡", "🌐", "⚖️", "🔒"];
  
  // Attorney goal icons
  const goalIcons = [
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z"/></svg>, // Scale Operations (server/growth)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/></svg>, // Maximize ROI (chart/growth)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>, // Reduce Headcount (people management)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/><path d="M9 10h5v5H9z"/></svg>, // Streamline Operations (calendar/schedule)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>, // Increase Revenue (dollar/finances)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h16v13z"/></svg>, // Automate Intake (calendar/tasks)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/><path d="M9 10h5v5H9z"/></svg>, // Improve Client Experience (checklist/feedback)
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-9 8H3V5h9v6zm9 6h-9v-6h9v6z"/></svg>, // Balance Work Life (layout/organization)
  ];
  
  // Character attributes and icons
  const characterAttributes = [
    { text: "24/7", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z"/></svg> }, 
    { text: "Intelligent", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3C7.59 3 4 6.59 4 11c0 4.41 3.59 8 8 8 .28 0 .55-.04.81-.08C10.44 20.21 8 22.35 8 25h2c0-2.24 2.24-4.04 5-4.04s5 1.8 5 4.04h2c0-2.65-2.44-4.79-4.81-6.08.26.04.53.08.81.08 4.41 0 8-3.59 8-8 0-4.41-3.59-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6z"/></svg> },
    { text: "Efficient", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg> },
    { text: "Reliable", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm6 9.09c0 4-2.55 7.7-6 8.83-3.45-1.13-6-4.82-6-8.83v-4.7l6-2.25 6 2.25v4.7z"/><path d="M10.21 13.84L8.35 12 7 13.34l3.21 3.19 5.96-5.99L14.82 9 10.21 13.84z"/></svg> },
    { text: "Caring", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg> },
    { text: "Helpful", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M16 13h-3V3h-2v10H8l4 4 4-4zM4 19v2h16v-2H4z"/></svg> },
    { text: "Patient", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h12v-5.99h-.01L18 16l-4-4 4-3.99-.01-.01H18V2H6zm10 14.5V20H8v-3.5l4-4 4 4z"/></svg> },
    { text: "Detailed", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/></svg> },
    { text: "Balanced", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M13 7h-2v2h2V7zm0 4h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h16v13z"/></svg> },
    { text: "Powerful", emoji: <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M19.77 7.23l.01-.01-3.72-3.72L15 4.56l2.11 2.11c-.94.36-1.61 1.26-1.61 2.33 0 1.38 1.12 2.5 2.5 2.5.36 0 .69-.08 1-.21v7.21c0 .55-.45 1-1 1s-1-.45-1-1V14c0-1.1-.9-2-2-2h-1V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v16h10v-7.5h1.5v5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V9c0-.69-.28-1.32-.73-1.77zM18 10c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zM8 18v-4.5H6L10 6v5h2l-4 7z"/></svg> }
  ];
  
  // Function to determine if "A" or "An" should be used
  const getArticle = (word) => {
    if (!word) return "An"; // Default
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    // Check if the word starts with a vowel sound
    return vowels.includes(word.charAt(0).toLowerCase()) ? "An" : "A";
  };
  
  // Next icon to display (show the upcoming icon on the back face)
  const getNextIcon = (current) => {
    return (current + 1) % roleIcons.length;
  };
  
  const getNextAttrIndex = (current) => {
    return (current + 1) % characterAttributes.length;
  };
  
  // Solari board flipboard animation
  useEffect(() => {
    if (configMode !== 'url' || !flipboardRef.current) return;
    
    const roles = [
      "intake specialist",
      "lead generator", 
      "client relations expert",
      "legal consultant",
      "case evaluator",
      "case manager",
      "scheduler",
      "office assistant",
      "LegalScout Agent"
    ];
    
    try {
      const flipContainer = flipboardRef.current;
      if (!flipContainer) return;
      
      const flipElements = flipContainer.querySelectorAll('.solari-flap');
      
      // Exit if elements aren't found
      if (!flipElements || flipElements.length === 0) {
        console.warn('No flip elements found in the container');
        return;
      }
      
      let currentIndex = 0;
      let animationInterval;
      
      const startAnimation = () => {
        clearInterval(animationInterval);
        
        // Initialize with spaces
        resetFlaps("");
        setCurrentIcon(0); // Reset to first icon
        
        // Start animation sequence
        animationInterval = setInterval(() => {
          try {
          // Update current index, looping back to start when reaching the end
          currentIndex = (currentIndex + 1) % roles.length;
          
          // First trigger the icon flip
          if (iconRef.current) {
            // Determine which icon to show based on the current role index
            const iconIndex = currentIndex === roles.length - 1 
              ? 9  // Legal Scout gets icon at index 9
              : currentIndex + 1; // +1 because index 0 is the default arrow
            
            setCurrentIcon(iconIndex);
            setActiveIconFlip(true);
            
            // Unflip after animation completes
            setTimeout(() => {
                try {
              setActiveIconFlip(false);
                } catch (error) {
                  // Ignore errors during cleanup
                }
            }, 300); // Faster animation time to match mechanical style
            
            // Start the Solari board flip after a brief delay - synchronized with icon flip
            setTimeout(() => {
                try {
                  if (flipboardRef.current) {
              updateWord(roles[currentIndex]);
                  }
                } catch (error) {
                  console.warn('Error updating word in timeout:', error);
                }
            }, 50); // Quicker start to match mechanical style
          } else {
            // If no icon reference, just update the word
              if (flipboardRef.current) {
            updateWord(roles[currentIndex]);
              }
            }
          } catch (error) {
            console.warn('Error in animation interval:', error);
          }
        }, 5000); // Was 7000ms, now 5000ms for faster transitions while still providing reading time
      };
      
      // Reset all flaps to a specific character - main flipboard
      const resetFlaps = (initialText) => {
        if (!flipElements || flipElements.length === 0) return;
        
        const text = initialText || "";
        
        // Reset all flaps to blank or initialText
        flipElements.forEach((element, i) => {
          try {
            if (!element) return;
            
            element.style.transform = 'rotateX(0deg)';
            const frontEl = element.querySelector('.solari-front');
            const backEl = element.querySelector('.solari-back');
            
            // Use non-breaking space for empty characters to maintain consistent height
            if (frontEl) frontEl.textContent = i < text.length ? text[i].toLowerCase() : '\u00A0';
            if (backEl) backEl.textContent = '\u00A0';
            
            // Animate all flaps on initial load with random characters
            if (initialText === "") {
              setTimeout(() => {
                const flipSequence = generateFlipSequence(i < text.length ? text[i].toLowerCase() : '\u00A0');
                animateFlap(element, flipSequence, 0);
              }, i * 30);
            }
          } catch (error) {
            console.warn('Error resetting flap:', error);
          }
        });
      };
      
      // Animate a single flap through its sequence
      const animateFlap = (element, sequence, index) => {
        if (!element || index >= sequence.length) return;
        
        try {
          const backEl = element.querySelector('.solari-back');
          if (backEl) {
            backEl.textContent = sequence[index];
          }
          
          element.classList.add('flipping');
          
          // After flip completes
          setTimeout(() => {
            try {
              element.classList.remove('flipping');
              const frontEl = element.querySelector('.solari-front');
              if (frontEl) frontEl.textContent = sequence[index];
              element.style.transform = 'rotateX(0deg)';
              
              // Continue to next character in sequence
              if (index < sequence.length - 1) {
                setTimeout(() => {
                  animateFlap(element, sequence, index + 1);
                }, Math.random() * 30 + 30); // Faster random timing (was 50+50ms)
              }
            } catch (error) {
              console.warn('Error in animateFlap:', error);
            }
          }, 150); // Faster flip transition (was 200ms)
        } catch (error) {
          console.warn('Error animating flap:', error);
        }
      };
      
      // Update to display a new word character by character
      const updateWord = (newText) => {
        if (!flipElements || flipElements.length === 0) return;
        
        try {
          // Convert text to lowercase for consistency
          const formattedText = newText.toLowerCase();
          
          // For each character in the word
          for (let i = 0; i < flipElements.length; i++) {
            const element = flipElements[i];
            if (!element) continue;
            
            // Use non-breaking space for empty characters to maintain consistent height
            const newChar = i < formattedText.length ? formattedText[i] : '\u00A0';
            
            // Create a random sequence of characters to flip through
            const flipSequence = generateFlipSequence(newChar);
            
            // Start the animation with staggered delay
            setTimeout(() => {
              animateFlap(element, flipSequence, 0);
            }, i * 30); // Slightly faster stagger between characters
          }
        } catch (error) {
          console.warn('Error updating word:', error);
        }
      };
      
      // Generate a sequence of random characters that ends with the target character
      const generateFlipSequence = (targetChar) => {
        // Characters to randomly select from (train board style)
        const chars = "abcdefghijklmnopqrstuvwxyz0123456789 ";
        const sequence = [];
        
        // 6-9 random characters for a faster animation (was 8-12)
        const numFlips = 6 + Math.floor(Math.random() * 4);
        
        for (let i = 0; i < numFlips; i++) {
          const randomIndex = Math.floor(Math.random() * chars.length);
          sequence.push(chars[randomIndex]);
        }
        
        // End with the target character
        sequence.push(targetChar);
        return sequence;
      };
      
      startAnimation();
      
      return () => {
        clearInterval(animationInterval);
      };
    } catch (error) {
      console.warn('Error in Solari board animation:', error);
    }
  }, [configMode]);

  // Simplified goal text animation - replaces the complex flipboard
  useEffect(() => {
    const goalTextInterval = setInterval(() => {
      // Update to the next goal text
      setCurrentAttrIcon(prevIndex => (prevIndex + 1) % goalTexts.length);
      
      // Refresh the animation by re-applying the class
      const goalTextElement = document.querySelector('.goal-text');
      if (goalTextElement) {
        goalTextElement.style.animation = 'none';
        // Force reflow
        void goalTextElement.offsetWidth;
        goalTextElement.style.animation = 'fadeChange 1.2s ease-in-out';
      }
    }, 7000); // Changed from 5 to 7 seconds for better reading time
    
    return () => clearInterval(goalTextInterval);
  }, []);
  
  // Add character attributes flipboard animation
  useEffect(() => {
    if (!attrFlipboardRef.current) return;
    
    try {
      let currentAttrIndex = 0;
      let animationInterval;
      
      const startAnimation = () => {
        clearInterval(animationInterval);
        setCurrentAttrIcon(0);
        
        // Start with first attribute
        updateAttribute(0);
        
        // Start animation sequence with longer intervals
        animationInterval = setInterval(() => {
          // Start with index 1 since we want to keep "24/7" separate and static
          currentAttrIndex = (currentAttrIndex + 1) % characterAttributes.length;
          if (currentAttrIndex === 0) {
            currentAttrIndex = 1; // Skip the first attribute (24/7) in the rotation
          }
          setCurrentAttrIcon(currentAttrIndex);
          setActiveAttrFlip(true);
          
          // Ensure flip state is reset reliably
          const flipTimer = setTimeout(() => {
            try {
              setActiveAttrFlip(false);
            } catch (err) {
              console.warn("Error resetting flip state:", err);
            }
          }, 600); // Reduced from 800ms to 600ms for quicker flip animation
          
          updateAttribute(currentAttrIndex);
          
          // Safety cleanup for the timer
          return () => clearTimeout(flipTimer);
        }, 7000); // Slightly faster for attribute flips
      };
      
      const updateAttribute = (index) => {
        const attrFlipboard = attrFlipboardRef.current;
        if (!attrFlipboard) return;
        
        const frontEl = attrFlipboard.querySelector('.attr-front');
        const backEl = attrFlipboard.querySelector('.attr-back');
        
        if (frontEl && backEl) {
          const attribute = characterAttributes[index];
          
          // Create DOM elements for the back panel
          const emojiCircleDiv = document.createElement('div');
          emojiCircleDiv.className = 'emoji-circle';
          
          // Render the SVG into the emoji circle
          // We have to convert the React SVG element to HTML string
          const svgString = attribute.emoji.type === undefined 
            ? attribute.emoji 
            : ReactDOMServer.renderToString(attribute.emoji);
          
          emojiCircleDiv.innerHTML = svgString;
          
          // Create the text span
          const textSpan = document.createElement('span');
          textSpan.className = 'attr-text';
          textSpan.textContent = attribute.text;
          
          // Clear existing content and append new elements
          backEl.innerHTML = '';
          backEl.appendChild(emojiCircleDiv);
          backEl.appendChild(textSpan);
          
          // Update the A/An prefix
          const prefixEl = document.querySelector('.main-title-prefix');
          if (prefixEl) {
            prefixEl.textContent = getArticle(attribute.text);
          }
          
          // Flip the board
          attrFlipboard.classList.add('flipping');
          
          // After flip completes - use a shorter time for faster transition
          const flipCompleteTimer = setTimeout(() => {
            try {
              attrFlipboard.classList.remove('flipping');
              frontEl.innerHTML = backEl.innerHTML;
            } catch (err) {
              console.warn("Error completing attribute flip:", err);
            }
          }, 600); // Reduced from 800ms to 600ms for quicker flip animation
          
          // Safety cleanup
          return () => clearTimeout(flipCompleteTimer);
        }
      };
      
      // Start animation after a delay
      const startTimer = setTimeout(() => {
        startAnimation();
      }, 3000);
      
      return () => {
        clearInterval(animationInterval);
        clearTimeout(startTimer);
      };
    } catch (error) {
      console.warn('Error in attribute board animation:', error);
    }
  }, []);
  
  // Define the states array
  const states = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 
    'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 
    'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 
    'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 
    'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio', 
    'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota', 
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 
    'Wisconsin', 'Wyoming', 'District of Columbia'
  ];
  
  useEffect(() => {
    const handleResize = () => {
      setFrameWidth("100%");
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const returnToConfig = () => {
    setShowPreview(false);
  };

  // Animation for preview toggle
  const [previewAnimation, setPreviewAnimation] = useState({
    phase: 'initial', // initial, animating, complete
    direction: 'none' // in, out
  });

  // Handle preview transition
  useEffect(() => {
    if (showPreview) {
      setPreviewAnimation({ phase: 'animating', direction: 'in' });
      const timer = setTimeout(() => {
        setPreviewAnimation({ phase: 'complete', direction: 'in' });
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setPreviewAnimation({ phase: 'initial', direction: 'none' });
    }
  }, [showPreview]);
  
  // Handle iframe message events for resizing
  useEffect(() => {
    if (!showPreview) return;
    
    const handleIframeMessage = (event) => {
      if (event.data && event.data.type === 'iframeHeight') {
        const newHeight = Math.max(500, event.data.height);
        setPreviewHeight(newHeight);
      }
    };
    
    window.addEventListener('message', handleIframeMessage);
    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, [showPreview, setPreviewHeight]);
  
  // Add a resize observer to adjust iframe height responsively
  useEffect(() => {
    if (!showPreview || !iframeRef.current) return;
    
    const handleResize = () => {
      const windowHeight = window.innerHeight;
      const headerHeight = 60;
      const controlsHeight = 80;
      const marginSpace = 40;
      
      const availableHeight = windowHeight - headerHeight - controlsHeight - marginSpace;
      const newHeight = Math.max(500, availableHeight);
      
      setPreviewHeight(newHeight);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [showPreview, iframeRef, setPreviewHeight]);
  
  // Add useEffect to send customization updates to the iframe
  useEffect(() => {
    if (!iframeRef.current) return;
    
    const iframe = iframeRef.current;
    const iframeWindow = iframe.contentWindow;
    
    const sendCustomizations = () => {
      if (iframeWindow) {
        // Format colors to ensure they're valid hex values
        const formatColor = (color) => {
          if (!color) return '#000000';
          return color.startsWith('#') ? color : `#${color}`;
        };
        
        // Create the customizations object with properly formatted colors and firm details
        const customizations = {
          // Firm details
          firmName,
          attorneyName,
          practiceAreas: selectedPracticeArea ? [selectedPracticeArea] : [],
          state,
          practiceDescription,
          buttonText,
          buttonOpacity,
          
          // Attorney contact details
          attorneyAddress,
          attorneyPhone,
          schedulingLink,
          
          // Visual customizations
          primaryColor: formatColor(primaryColor),
          secondaryColor: formatColor(secondaryColor),
          buttonColor: formatColor(buttonColor || secondaryColor), // Add button color
          backgroundColor: formatColor(backgroundColor),
          backgroundOpacity,
          practiceAreaBackgroundOpacity,
          textBackgroundColor: formatColor(textBackgroundColor),
          
          // Animation
          firmNameAnimation,
          
          // Assets
          logoUrl: logoUrl || '',
          
          // Content
          welcomeMessage,
          informationGathering,
          
          // Theme
          theme: isDarkTheme ? 'dark' : 'light'
        };
        
        console.log('Sending customizations to iframe:', customizations);
        
        // Send the customizations to the iframe
        iframeWindow.postMessage({
          type: 'updateCustomizations',
          customizations
        }, '*');
      }
    };
    
    // Send customizations immediately and set up an interval
    sendCustomizations();
    const updateInterval = setInterval(sendCustomizations, 300);
    
    return () => clearInterval(updateInterval);
  }, [
    // Firm details
    firmName,
    attorneyName,
    selectedPracticeArea,
    state,
    practiceDescription,
    buttonText,
    buttonOpacity,
    
    // Attorney contact details
    attorneyAddress,
    attorneyPhone,
    schedulingLink,
    
    // Visual customizations
    primaryColor,
    secondaryColor,
    buttonColor, // Add button color
    backgroundColor,
    backgroundOpacity,
    practiceAreaBackgroundOpacity,
    textBackgroundColor,
    
    // Animation
    firmNameAnimation,
    
    // Assets
    logoUrl,
    
    // Content
    welcomeMessage,
    informationGathering,
    
    // Theme
    isDarkTheme,
    
    // References
    iframeRef
  ]);
  
  // Helper functions for color handling
  const hexToRgb = (hex) => {
    try {
      // Default to black if no color is provided
      if (!hex) return '0, 0, 0';
      
      // Remove the hash at the start if it exists
      hex = hex.replace(/^#/, '');
      
      // Ensure we have a valid hex string
      if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
        return '0, 0, 0'; // Return black for invalid hex
      }
      
      // Parse the hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
      
    return `${r}, ${g}, ${b}`;
    } catch (error) {
      console.error('Error converting hex to RGB:', error);
      return '0, 0, 0'; // Return black as fallback
    }
  };
  
  const getContrastColor = (hexColor) => {
    let hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // URL validation function
  const validateUrl = (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // Handle URL change
  const handleUrlChange = (e) => {
    const url = e.target.value;
    setFirmUrl(url);
    setIsValidUrl(validateUrl(url));
  };

  // Simplified carousel state
  const [currentIndex, setCurrentIndex] = useState(0);

  // Simplified card navigation function
  const scrollToCard = (index) => {
    // Ensure index is within bounds
    const newIndex = Math.max(0, Math.min(index, 3));
    setCurrentIndex(newIndex);
  };

  // Initialize carousel on mount - improved implementation
  useEffect(() => {
      // Add keyboard navigation
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowRight') {
        scrollToCard(currentIndex + 1);
      } else if (e.key === 'ArrowLeft') {
        scrollToCard(currentIndex - 1);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [currentIndex]);

  // Scroll to the config section when the scroll indicator is clicked
  const scrollToConfig = () => {
    const configSection = document.querySelector('.config-container');
    if (configSection) {
      // Set config as visible
      setIsConfigVisible(true);
      
      // Use scrollIntoView with smooth behavior to use the scroll-snap
      configSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Handle initial page scroll animations
  useEffect(() => {
    // Add smooth scroll behavior to all internal anchors
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Add scroll event listener to detect scroll position and update visibility
    let scrollTimeout = null;
    let lastScrollTop = 0;

    const handlePageScroll = () => {
      const heroSection = document.querySelector('.hero-section');
      const configSection = document.querySelector('.config-container');
      if (!heroSection || !configSection) return;

      // Get current scroll position
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const heroRect = heroSection.getBoundingClientRect();
      const configRect = configSection.getBoundingClientRect();
      
      // Skip scroll handling when preview is active
      if (showPreview) return;
      
      // Check if hero section is visible
      if (heroRect.top > -heroRect.height/2 && heroRect.top < window.innerHeight/2) {
        setIsConfigVisible(false);
      } 
      // Check if config section is visible
      else if (configRect.top < window.innerHeight/2) {
        setIsConfigVisible(true);
      }
      
      // Check if user is scrolling down from hero section (initial scroll)
      if (scrollTop > lastScrollTop && heroRect.bottom > 0 && scrollTop > 20 && !isConfigVisible) {      
        // Clear any existing timeout
        if (scrollTimeout) clearTimeout(scrollTimeout);
        
        // Scroll to config section
        scrollTimeout = setTimeout(() => {
          setIsConfigVisible(true);
          configSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }, 50);
      }
      
      // Update last scroll position
      lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
    };

    window.addEventListener('scroll', handlePageScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handlePageScroll);
    };
  }, [isConfigVisible, showPreview]);

  // Add this function after the scrollToConfig function
  const returnToHero = () => {
    // Don't return to hero if preview is active
    if (showPreview) return;
    
    setIsConfigVisible(false);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Ensure configMode is set to 'manual' when practice area is selected
  useEffect(() => {
    if (selectedPracticeArea) {
      setConfigMode('manual');
      console.log("Practice area selected, setting configMode to manual:", selectedPracticeArea);
    }
  }, [selectedPracticeArea]);

  return (
    <div className={`demo-page-container ${showPreview ? 'preview-active' : ''}`}>
      <style>{tabStyles}</style>
      <CreateAgentButton 
        primaryColor={primaryColor}
        secondaryColor={secondaryColor}
        isDark={isDarkTheme}
        isPreviewVisible={showPreview}
        logoUrl={logoUrl}
        buttonOpacity={buttonOpacity}
      />
      
      {/* Add transparency overlay when preview is active */}
      {showPreview && (
        <div className="background-overlay" style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -1,
          backgroundColor: isDarkTheme ? 'rgba(0, 0, 0, 0.4)' : 'rgba(255, 255, 255, 0.7)',
          backdropFilter: 'blur(2px)',
          pointerEvents: 'none'
        }}></div>
      )}
      
      {/* Navigation Dots */}
      {!showPreview && (
        <div className="page-navigation-dots">
          <button 
            className={`nav-dot ${!isConfigVisible ? 'active' : ''}`} 
            onClick={returnToHero}
            title="Hero Section"
          />
          <button 
            className={`nav-dot ${isConfigVisible ? 'active' : ''}`} 
            onClick={scrollToConfig}
            title="Configuration Section"
          />
        </div>
      )}
      
      {/* Hero Section - Only shown when config is not visible */}
      {!isConfigVisible && !showPreview && (
        <div className="hero-section" data-theme={isDarkTheme ? 'dark' : 'light'}>
          <div className="hero-content">
            {/* Main title: Meet Your Law Firm's new [attribute] - all white with subtle attribute flipper */}
            <div className="main-title-container">
              <span className="main-title-text main-title-white">Meet Your Law Firm's</span>
              <div className="attribute-flipboard-wrapper subtle-attribute">
                <div ref={attrFlipboardRef} className={`attribute-flipboard ${activeAttrFlip ? 'flipping' : ''}`}>
                  <div className="attr-front">
                    <div className="emoji-circle">
                      {characterAttributes[currentAttrIcon].emoji}
                    </div>
                    <span className="attr-text">{characterAttributes[currentAttrIcon].text}</span>
                  </div>
                  <div className="attr-back">
                    <div className="emoji-circle">
                      {characterAttributes[getNextAttrIndex(currentAttrIcon)].emoji}
                    </div>
                    <span className="attr-text">{characterAttributes[getNextAttrIndex(currentAttrIcon)].text}</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Role Flipper */}
            <div className="flipboard-container role-flipboard-container">
              <div className="icon-flip-container">
                <div ref={iconRef} className={`icon-flip ${activeIconFlip ? 'flipping' : ''}`}>
                  <div className="icon-flip-front">
                    {roleIcons[currentIcon]}
                  </div>
                  <div className="icon-flip-back">
                    {roleIcons[getNextIcon(currentIcon)]}
                  </div>
                </div>
              </div>
              
              <div ref={flipboardRef} className="solari-board">
                {Array(24).fill().map((_, i) => (
                  <div key={i} className="solari-flap">
                    <div className="solari-front">&nbsp;</div>
                    <div className="solari-back">&nbsp;</div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Extra vertical space */}
            <div className="vertical-spacer"></div>
            
            {/* Goal Flipboard - Replacing with simple text transition */}
            <div className="goal-text-transition">
              <h2 className="goal-text">{goalTexts[currentAttrIcon]}</h2>
            </div>
            
            {/* Logo and Agent text */}
            <div className="logo-agent-container">
              <img src="/organgelight clear.png" alt="LegalScout Logo" className="orange-logo" />
              <span className="agent-text">Agent</span>
            </div>
            
            {/* Add more vertical space here */}
            <div className="vertical-spacer"></div>
            
            {/* Configuration hint with scroll indicator */}
            <div className="config-hint">
              <p>Configure your law firm's AI assistant below</p>
              <div className="scroll-indicator" onClick={scrollToConfig}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6-6-6 6 1.41 1.41z" fill="currentColor"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="config-container" data-theme={isDarkTheme ? 'dark' : 'light'}>
        {isConfigVisible && (
          <button 
            className="return-to-hero-button" 
            onClick={returnToHero}
            title="Return to hero section"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6 1.41 1.41z" fill="currentColor"/>
            </svg>
            <span>Return to top</span>
          </button>
        )}

        {/* Add Auto-Config Button in the center above tabs */}
          {configMode === 'manual' && (
          <div className="auto-config-container" style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '20px',
            width: '100%'
          }}>
              <button
                className="config-tab auto-config-tab"
                onClick={() => {
                  setConfigMode('url');
                  setActiveConfigTab('firm');
                }}
                data-animate="true"
              style={{
                maxWidth: '600px',
                margin: '0 auto',
                padding: '12px 20px',
                backgroundColor: 'rgba(216, 87, 34, 0.08)',
                border: '1px dashed #D85722',
                borderRadius: '12px',
                color: '#D85722',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              <svg className="auto-config-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.65 6.35A7.958 7.958 0 0012 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0112 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="#D85722"/>
                </svg>
              <span className="auto-config-text" style={{ fontSize: '14px', fontWeight: '500' }}>
                  Use your Firm's website or Attorney profile to Automatically Configure your Agent
                <span className="emoji-container" style={{ marginLeft: '6px' }}>✨</span>
                </span>
              </button>
          </div>
        )}

        <div className="config-tabs" style={{
          display: 'flex',
          justifyContent: 'center',
          borderBottom: '1px solid rgba(0,0,0,0.05)',
          paddingBottom: '0',
          marginBottom: '24px',
        }}>
          {configMode === 'manual' && (
            <>
              <button
                className={`config-tab ${activeConfigTab === 'firm' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('firm')}
                style={{
                  backgroundColor: activeConfigTab === 'firm' ? 'rgba(216, 87, 34, 0.15)' : 'transparent',
                  color: '#D85722',
                  border: 'none',
                  borderBottom: activeConfigTab === 'firm' ? '2px solid #D85722' : '2px solid transparent',
                  borderRadius: '6px 6px 0 0',
                  padding: '10px 16px',
                  fontSize: activeConfigTab === 'firm' ? '15px' : '14px',
                  fontWeight: activeConfigTab === 'firm' ? '600' : '500',
                  transition: 'all 0.2s ease',
                  margin: '0 4px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                  outline: 'none',
                  boxShadow: 'none',
                }}>
                <div className="tab-number" style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  backgroundColor: activeConfigTab === 'firm' ? '#D85722' : 'rgba(216, 87, 34, 0.15)',
                  color: activeConfigTab === 'firm' ? 'white' : '#D85722',
                  fontSize: '16px',
                  fontWeight: '600',
                  marginBottom: '6px',
                  boxShadow: activeConfigTab === 'firm' ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
                  transition: 'all 0.2s ease',
                }}>1</div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '6px', flexShrink: 0 }}>
                    <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z" fill="currentColor"/>
                  </svg>
                  <span style={{ letterSpacing: '0.01em' }}>Firm Details</span>
                </div>
              </button>
              <button
                className={`config-tab ${activeConfigTab === 'appearance' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('appearance')}
                style={{
                  backgroundColor: activeConfigTab === 'appearance' ? 'rgba(216, 87, 34, 0.15)' : 'transparent',
                  color: '#D85722',
                  border: 'none',
                  borderBottom: activeConfigTab === 'appearance' ? '2px solid #D85722' : '2px solid transparent',
                  borderRadius: '6px 6px 0 0',
                  padding: '10px 16px',
                  fontSize: activeConfigTab === 'appearance' ? '15px' : '14px',
                  fontWeight: activeConfigTab === 'appearance' ? '600' : '500',
                  transition: 'all 0.2s ease',
                  margin: '0 4px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                  outline: 'none',
                  boxShadow: 'none',
                }}>
                <div className="tab-number" style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  backgroundColor: activeConfigTab === 'appearance' ? '#D85722' : 'rgba(216, 87, 34, 0.15)',
                  color: activeConfigTab === 'appearance' ? 'white' : '#D85722',
                  fontSize: '16px',
                  fontWeight: '600',
                  marginBottom: '6px',
                  boxShadow: activeConfigTab === 'appearance' ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
                  transition: 'all 0.2s ease',
                }}>2</div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '6px', flexShrink: 0 }}>
                    <path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67A2.5 2.5 0 0 1 12 22zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5a.54.54 0 0 0-.14-.35c-.41-.46-.63-1.05-.63-1.65a2.5 2.5 0 0 1 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z" fill="currentColor"/>
                    <circle cx="6.5" cy="11.5" r="1.5" fill="currentColor"/>
                    <circle cx="9.5" cy="7.5" r="1.5" fill="currentColor"/>
                    <circle cx="14.5" cy="7.5" r="1.5" fill="currentColor"/>
                    <circle cx="17.5" cy="11.5" r="1.5" fill="currentColor"/>
                  </svg>
                  <span style={{ letterSpacing: '0.01em' }}>Agent Appearance & Voice</span>
                </div>
              </button>
              <button
                className={`config-tab ${activeConfigTab === 'agent' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('agent')}
                style={{
                  backgroundColor: activeConfigTab === 'agent' ? 'rgba(216, 87, 34, 0.15)' : 'transparent',
                  color: '#D85722',
                  border: 'none',
                  borderBottom: activeConfigTab === 'agent' ? '2px solid #D85722' : '2px solid transparent',
                  borderRadius: '6px 6px 0 0',
                  padding: '10px 16px',
                  fontSize: activeConfigTab === 'agent' ? '15px' : '14px',
                  fontWeight: activeConfigTab === 'agent' ? '600' : '500',
                  transition: 'all 0.2s ease',
                  margin: '0 4px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                  outline: 'none',
                  boxShadow: 'none',
                }}>
                <div className="tab-number" style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  backgroundColor: activeConfigTab === 'agent' ? '#D85722' : 'rgba(216, 87, 34, 0.15)',
                  color: activeConfigTab === 'agent' ? 'white' : '#D85722',
                  fontSize: '16px',
                  fontWeight: '600',
                  marginBottom: '6px',
                  boxShadow: activeConfigTab === 'agent' ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
                  transition: 'all 0.2s ease',
                }}>3</div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '6px', flexShrink: 0 }}>
                    <path d="M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4-.01-18zM18 14H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" fill="currentColor"/>
                  </svg>
                  <span style={{ letterSpacing: '0.01em' }}>Agent Behavior & Objectives</span>
                </div>
              </button>
            </>
          )}
        </div>

        {activeConfigTab === 'firm' && (
          <div className="config-section">
            {configMode === 'url' && (
              // Remove the config-mode-section div that creates the overall transparent overlay
              <div className="start-options vertical">
                <div className="start-option glass-effect">
                  <h3>Use Your Website</h3>
                  <div className="input-group">
                    <label htmlFor="firmUrl">Enter your firm's website URL</label>
                    <div className="url-input-group">
                      <input
                        type="url"
                        id="firmUrl"
                        value={firmUrl}
                        onChange={handleUrlChange}
                        placeholder="https://www.yourfirm.com"
                        className={`modern-input ${isValidUrl ? 'valid' : ''}`}
                      />
                    </div>
                    {isValidUrl && (
                      <button 
                        className="begin-config modern-button"
                        onClick={() => {
                          handleUrlSubmit();
                          setConfigMode('manual');
                        }}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <span className="flex-center">
                            <svg className="spinner" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <circle className="spinner-circle" cx="12" cy="12" r="10" />
                            </svg>
                            Processing...
                          </span>
                        ) : 'Auto-Configure'}
                      </button>
                    )}
                  </div>
                </div>

                {/* OR divider */}
                <div className="or-divider">
                  <div className="divider-line"></div>
                  <span className="divider-text">OR</span>
                  <div className="divider-line"></div>
                </div>

                <div className="start-option glass-effect">
                  <h3>Quick Start Template</h3>
                  <div className="input-group">
                    <label htmlFor="quickStart">Select your practice area</label>
                    <select
                      id="quickStart"
                      value={selectedPracticeArea}
                      onChange={(e) => {
                        handlePracticeAreaChange(e);
                        setConfigMode('manual');
                        setTimeout(() => {
                          setConfigMode('manual');
                          setActiveConfigTab('firm');
                        }, 0);
                      }}
                      className="modern-select"
                    >
                      <option value="">Choose a practice area</option>
                      {Object.keys(practiceAreas).map((area) => (
                        <option key={area} value={area}>
                          {area}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            {configMode === 'manual' && (
              <>
                <div className="config-mode-section">
                  <div className="input-group with-color-picker">
                    <div className="input-header">
                      <label htmlFor="firmName">Firm Name</label>
                      <div className="control-group">
                        <input
                          type="text"
                          id="firmName"
                          value={firmName}
                          onChange={(e) => setFirmName(e.target.value)}
                          placeholder="Enter your firm name"
                          aria-label="Firm Name"
                        />
                        <select
                          id="state"
                          value={state}
                          onChange={(e) => setState(e.target.value)}
                          aria-label="State"
                        >
                          <option value="">Select a state</option>
                          {states.map((stateName) => (
                            <option key={stateName} value={stateName}>
                              {stateName}
                            </option>
                          ))}
                        </select>
                      </div>
                      <small style={{ color: 'rgba(60, 80, 100, 0.7)', marginTop: '4px' }}>
                        Choose how your firm name appears in the hero section
                      </small>
                    </div>
                  </div>

                  <div className="input-group">
                    <label htmlFor="attorneyName">Attorney Name</label>
                    <input
                      type="text"
                      id="attorneyName"
                      value={attorneyName}
                      onChange={(e) => setAttorneyName(e.target.value)}
                      placeholder="Enter attorney's name"
                    />
                  </div>

                  <div className="input-group">
                    <label htmlFor="attorneyAddress">Attorney Address</label>
                    <input
                      type="text"
                      id="attorneyAddress"
                      value={attorneyAddress || ""}
                      onChange={(e) => setAttorneyAddress(e.target.value)}
                      placeholder="Enter attorney's address"
                    />
                  </div>

                  <div className="input-group">
                    <label htmlFor="attorneyPhone">Attorney Phone</label>
                    <input
                      type="tel"
                      id="attorneyPhone"
                      value={attorneyPhone || ""}
                      onChange={(e) => setAttorneyPhone(e.target.value)}
                      placeholder="Enter attorney's phone number"
                    />
                  </div>

                  <div className="input-group">
                    <label htmlFor="schedulingLink">Scheduling App Link</label>
                    <input
                      type="url"
                      id="schedulingLink"
                      value={schedulingLink || ""}
                      onChange={(e) => setSchedulingLink(e.target.value)}
                      placeholder="Enter your Calendly/Cal.com link"
                    />
                    <small style={{ color: 'rgba(60, 80, 100, 0.7)', marginTop: '4px' }}>
                      Link to your online scheduling tool (Calendly, Cal.com, etc.)
                    </small>
                  </div>

                  <div className="input-group">
                    <label htmlFor="practiceArea">Practice Areas</label>
                    <select
                      id="practiceArea"
                      value={selectedPracticeArea}
                      onChange={(e) => {
                        // Call the parent handler with the event
                        if (typeof handlePracticeAreaChange === 'function') {
                          // If this is a valid practice area or empty string
                          if (e.target.value === '' || Object.keys(practiceAreas).includes(e.target.value)) {
                            handlePracticeAreaChange(e.target.value);
                          }
                        }
                      }}
                    >
                      <option value="">Select a practice area</option>
                      {Object.keys(practiceAreas).map((area) => (
                        <option key={area} value={area}>
                          {area}
                        </option>
                      ))}
                    </select>
                    <div style={{ 
                      display: 'flex', 
                      flexWrap: 'wrap', 
                      gap: '8px',
                      marginTop: '8px'
                    }}>
                      {selectedPracticeArea && (
                        <div className="practice-area-tag">
                          {selectedPracticeArea}
                          <button
                            onClick={() => {
                              if (typeof handlePracticeAreaChange === 'function') {
                                handlePracticeAreaChange('');
                              }
                            }}
                            aria-label="Remove practice area"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Practice Description has been moved to Appearance tab */}
                </div>
              </>
            )}
          </div>
        )}

        {activeConfigTab === 'appearance' && (
          <div className="config-section">
                  <div className="input-group">
                    <label htmlFor="practiceDescription">Practice Description</label>
                    <textarea
                      id="practiceDescription"
                      value={practiceDescription}
                      onChange={(e) => setPracticeDescription(e.target.value)}
                      rows={6}
                      placeholder="Enter practice description (supports Markdown)"
                    />
                  </div>

                  <div className="input-group with-color-picker">
                    <div className="input-header">
                      <label>Logo & Button Text</label>
                      <div className="control-group">
                        <div className="logo-control">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            style={{ flex: 1 }}
                          />
                          {logoUrl && (
                            <button 
                              className="button small danger"
                              onClick={handleRemoveLogo}
                              type="button"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                        <div className="button-text-control">
                          <input
                            type="text"
                            id="buttonText"
                            value={buttonText}
                            onChange={(e) => setButtonText(e.target.value)}
                            placeholder="Start Consultation"
                            aria-label="Button Text"
                          />
                          <small style={{ marginTop: '4px', color: 'rgba(60, 80, 100, 0.7)' }}>
                            Text displayed on the call-to-action button
                          </small>
                        </div>
                      </div>
                    </div>
                    {logoUrl && (
                      <div className="logo-preview" style={{ marginTop: '10px' }}>
                        <img 
                          src={logoUrl} 
                          alt="Uploaded Logo" 
                          style={{ 
                            maxWidth: '200px', 
                            maxHeight: '80px', 
                            objectFit: 'contain'
                          }} 
                        />
                        <small style={{ display: 'block', marginTop: '5px', color: 'rgba(60, 80, 100, 0.7)' }}>
                          Logo will appear on the consultation button and in the header of the interface.
                        </small>
                      </div>
                    )}
                  </div>

                    <div className="input-group">
                      <label htmlFor="firmNameAnimation">Entrance Animation</label>
                      <select
                        className="animation-select"
                        id="firmNameAnimation"
                        value={firmNameAnimation}
                        onChange={(e) => setFirmNameAnimation(e.target.value)}
                        aria-label="Animation Style"
                      >
                        <option value="">Select Effect</option>
                        <option value="fadeIn">Fade In</option>
                        <option value="slideIn">Slide In</option>
                        <option value="scaleIn">Scale In</option>
                        <option value="bounceIn">Bounce In</option>
                      </select>
                      <small style={{ color: 'rgba(60, 80, 100, 0.7)', marginTop: '4px' }}>
                        Animation effect for your firm name
                      </small>
                    </div>

                    <div className="input-group with-color-picker">
                      <label>Color Scheme</label>
                      <div className="color-controls">
                        <div className="color-picker-container">
                          <button 
                            className="color-dot-button" 
                            onClick={(e) => {
                              e.preventDefault();
                              document.getElementById('firmColorPicker').click();
                            }}
                            style={{
                              backgroundColor: primaryColor
                            }}
                          >
                          </button>
                          <input
                            type="color"
                            id="firmColorPicker"
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            className="hidden-color-input"
                          />
                          <label className="inline-label">Firm Color</label>
                        </div>

                        <div className="color-picker-container">
                          <button 
                            className="color-dot-button" 
                            onClick={(e) => {
                              e.preventDefault();
                              document.getElementById('secondaryColorPicker').click();
                            }}
                            style={{
                              backgroundColor: secondaryColor
                            }}
                          >
                          </button>
                          <input
                            type="color"
                            id="secondaryColorPicker"
                            value={secondaryColor}
                            onChange={(e) => setSecondaryColor(e.target.value)}
                            className="hidden-color-input"
                          />
                          <label className="inline-label">Secondary Color</label>
                        </div>
                        <div className="color-picker-container">
                          <button 
                            className="color-dot-button" 
                            onClick={(e) => {
                              e.preventDefault();
                              document.getElementById('buttonColorPicker').click();
                            }}
                            style={{
                              backgroundColor: buttonColor || secondaryColor
                            }}
                          >
                          </button>
                          <input
                            type="color"
                            id="buttonColorPicker"
                            value={buttonColor || secondaryColor}
                            onChange={(e) => setButtonColor(e.target.value)}
                            className="hidden-color-input"
                          />
                          <label className="inline-label">Button Color</label>
                        </div>
                        <div className="slider-container compact">
                          <label className="slider-label">
                            <span>Button Opacity</span>
                            <span className="slider-value">{Math.round(buttonOpacity * 100)}%</span>
                          </label>
                          <input
                            type="range"
                            className="modern-slider"
                            min="0"
                            max="1"
                            step="0.05"
                            value={buttonOpacity}
                            onChange={(e) => setButtonOpacity(parseFloat(e.target.value))}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="input-group with-color-picker">
                      <label>Background Settings</label>
                      <div className="color-controls">
                        <div className="color-picker-container">
                          <button 
                            className="color-dot-button" 
                            onClick={(e) => {
                              e.preventDefault();
                              document.getElementById('backgroundColorPicker').click();
                            }}
                            style={{
                              backgroundColor: backgroundColor
                            }}
                          >
                          </button>
                          <input
                            type="color"
                            id="backgroundColorPicker"
                            value={backgroundColor}
                            onChange={(e) => setBackgroundColor(e.target.value)}
                            className="hidden-color-input"
                          />
                          <label className="inline-label">Background</label>
                        </div>
                        <div className="slider-container compact">
                          <label className="slider-label">
                            <span>Background Opacity</span>
                            <span className="slider-value">{Math.round(backgroundOpacity * 100)}%</span>
                          </label>
                          <input
                            type="range"
                            className="modern-slider"
                            min="0"
                            max="1"
                            step="0.05"
                            value={backgroundOpacity}
                            onChange={(e) => setBackgroundOpacity(parseFloat(e.target.value))}
                          />
                        </div>

                        <div className="color-picker-container">
                          <button 
                            className="color-dot-button" 
                            onClick={(e) => {
                              e.preventDefault();
                              document.getElementById('textBgColorPicker').click();
                            }}
                            style={{
                              backgroundColor: textBackgroundColor
                            }}
                          >
                          </button>
                          <input
                            type="color"
                            id="textBgColorPicker"
                            value={textBackgroundColor}
                            onChange={(e) => setTextBackgroundColor(e.target.value)}
                            className="hidden-color-input"
                          />
                          <label className="inline-label">Text Background</label>
                        </div>
                        <div className="slider-container compact">
                          <label className="slider-label">
                            <span>Text Background Opacity</span>
                            <span className="slider-value">{Math.round(practiceAreaBackgroundOpacity * 100)}%</span>
                          </label>
                          <input
                            type="range"
                            className="modern-slider"
                            min="0"
                            max="1"
                            step="0.05"
                            value={practiceAreaBackgroundOpacity}
                            onChange={(e) => setPracticeAreaBackgroundOpacity(parseFloat(e.target.value))}
                          />
                        </div>
                      </div>
                      <small style={{ display: 'block', marginTop: '4px', color: 'rgba(60, 80, 100, 0.7)' }}>
                        Controls colors and transparency for various interface elements
                      </small>
                    </div>
          </div>
        )}

        {activeConfigTab === 'agent' && (
          <div className="config-section">
            <div className="input-group">
              <label htmlFor="welcomeMessage">Welcome Message</label>
              <textarea
                id="welcomeMessage"
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                rows={4}
                placeholder="Enter welcome message"
              />
            </div>

            <div className="input-group">
              <label htmlFor="informationGathering">Information Gathering</label>
              <textarea
                id="informationGathering"
                value={informationGathering}
                onChange={(e) => setInformationGathering(e.target.value)}
                rows={4}
                placeholder="Enter information gathering questions"
              />
            </div>
          </div>
        )}

        <div style={{ marginTop: '2rem', display: 'flex', justifyContent: 'space-between' }}>
          {activeConfigTab !== 'firm' && (
            <button 
              className="button secondary"
              onClick={() => {
                const tabs = ['firm', 'appearance', 'agent'];
                const currentIndex = tabs.indexOf(activeConfigTab);
                setActiveConfigTab(tabs[currentIndex - 1]);
              }}
            >
              Back
            </button>
          )}
          
          {(activeConfigTab === 'firm' || activeConfigTab === 'appearance') && configMode === 'manual' && (
            <button 
              className="button"
              onClick={() => {
                const tabs = ['firm', 'appearance', 'agent'];
                const currentIndex = tabs.indexOf(activeConfigTab);
                setActiveConfigTab(tabs[currentIndex + 1]);
              }}
            >
              Next
            </button>
          )}
        </div>
      </div>

      {/* Debug button rendering conditions */}
      {console.log("Button conditions:", {configMode, showPreview})}
      
      {configMode === 'manual' && !showPreview && (
        <button 
          className="floating-preview-button"
          onClick={goToPreview}
          title="Preview and Test your Agent"
          style={{
            border: '2px solid #D85722',
            boxShadow: '0 4px 20px rgba(216, 87, 34, 0.6)'
          }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" fill="currentColor"/>
          </svg>
          <span>Preview and Test your Agent</span>
        </button>
      )}

      <div className="preview-container">
        {showPreview && (
          <>
            <div className="preview-controls">
              <button className="icon-button" onClick={returnToConfig} title="Minimize Preview">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 13H5v-2h14v2z" fill="currentColor"/>
                </svg>
              </button>
              <button 
                className="icon-button" 
                onClick={() => {
                  if (iframeRef.current) {
                    iframeRef.current.src = iframeRef.current.src;
                  }
                }}
                title="Refresh Preview"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.65 6.35A7.958 7.958 0 0012 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0112 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
                </svg>
              </button>
            </div>
            <iframe
              ref={iframeRef}
              className="preview-iframe"
              src={`/preview?theme=${isDarkTheme ? 'dark' : 'light'}&production=true&centered=true&fullWidth=true&primaryColor=${encodeURIComponent(primaryColor)}&secondaryColor=${encodeURIComponent(secondaryColor)}&buttonOpacity=${buttonOpacity}&practiceAreaBackgroundOpacity=${practiceAreaBackgroundOpacity}&textBackgroundColor=${encodeURIComponent(textBackgroundColor)}&logoUrl=${encodeURIComponent(logoUrl || '')}`}
              style={{ height: `${previewHeight}px` }}
              title="Agent Preview"
            />
          </>
        )}
      </div>
    </div>
  );
};

export default SimpleDemoPage;