import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON>ser, FaGlobe, FaRobot, FaArrowR<PERSON>, <PERSON>a<PERSON><PERSON>ck, FaExclamationCircle } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import './ProgressiveSetupGuide.css';

/**
 * Contextual Setup Shepherd (2025 Design)
 * Highlights actual UI elements and provides granular step-by-step guidance
 */
const ProgressiveSetupGuide = ({
  attorney,
  activeTab,
  onNavigateToSignup,
  onNavigateToDomain,
  onNavigateToAgent,
  onHighlightElement
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [currentGuidance, setCurrentGuidance] = useState(null);
  const [completionStatus, setCompletionStatus] = useState({});

  // Check what's missing and provide granular guidance
  useEffect(() => {
    if (!user) {
      setCurrentGuidance({
        type: 'auth',
        title: 'Welcome to LegalScout',
        message: 'Sign up to create your AI legal assistant',
        action: 'Sign Up',
        priority: 'high',
        targetElement: null,
        onClick: () => onNavigateToSignup ? onNavigateToSignup() : navigate('/auth')
      });
      return;
    }

    // Check subdomain status - exclude template/demo subdomains
    const isTemplateSubdomain = attorney?.subdomain && (
      attorney.subdomain === 'default' ||
      attorney.subdomain === 'demo' ||
      attorney.subdomain === 'template' ||
      attorney.subdomain === 'example' ||
      attorney.subdomain.startsWith('temp-') ||
      attorney.subdomain.startsWith('demo-')
    );

    const hasRealSubdomain = attorney?.subdomain && !isTemplateSubdomain;
    const hasBarId = attorney?.bar_id;
    const hasFirmName = attorney?.firm_name;
    const hasAssistant = attorney?.current_assistant_id;

    if (!hasRealSubdomain) {
      // Generate suggested subdomain from firm name if available
      const firmNameSuggestion = attorney?.firm_name ?
        attorney.firm_name
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '')
          .substring(0, 30) : null;

      setCurrentGuidance({
        type: 'subdomain',
        title: isTemplateSubdomain ? 'Choose Your Subdomain' : 'Reserve Your Domain',
        message: firmNameSuggestion ?
          `We suggest "${firmNameSuggestion}.legalscout.net" based on your firm name` :
          isTemplateSubdomain ?
            'Replace template subdomain with your firm name' :
            'Choose your unique legal practice URL',
        action: 'Choose Subdomain',
        priority: 'high',
        targetElement: 'subdomain-field',
        onClick: () => {
          onNavigateToDomain ? onNavigateToDomain() : navigate('/dashboard?tab=agent');
          // Highlight the subdomain field after navigation
          setTimeout(() => onHighlightElement?.('subdomain-field'), 500);
        }
      });
    } else if (!hasBarId || !hasFirmName) {
      const missingFields = [];
      if (!hasFirmName) missingFields.push('Firm Name');
      if (!hasBarId) missingFields.push('Bar ID');

      // Generate suggested subdomain from firm name if available
      const suggestedSubdomain = attorney?.firm_name ?
        attorney.firm_name
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '')
          .substring(0, 30) :
        attorney.subdomain;

      setCurrentGuidance({
        type: 'profile',
        title: hasFirmName ?
          `${suggestedSubdomain}.legalscout.net Available!` :
          'Complete Your Profile',
        message: hasFirmName ?
          `Complete your profile: ${missingFields.join(', ')}` :
          `Add your firm name to get a suggested URL like "your-firm.legalscout.net"`,
        action: 'Complete Profile',
        priority: 'medium',
        targetElement: 'profile-fields',
        onClick: () => {
          navigate('/dashboard?tab=profile');
          setTimeout(() => onHighlightElement?.('profile-fields'), 500);
        }
      });
    } else if (!hasAssistant) {
      setCurrentGuidance({
        type: 'assistant',
        title: 'Profile Complete!',
        message: 'Now create your AI legal assistant',
        action: 'Create Assistant',
        priority: 'medium',
        targetElement: 'assistant-creation',
        onClick: () => {
          onNavigateToAgent ? onNavigateToAgent() : navigate('/dashboard?tab=agent');
          setTimeout(() => onHighlightElement?.('assistant-creation'), 500);
        }
      });
    } else {
      setCurrentGuidance(null); // Setup complete
    }

    // Update completion status
    setCompletionStatus({
      auth: !!user,
      subdomain: hasRealSubdomain,
      profile: hasBarId && hasFirmName,
      assistant: hasAssistant
    });

  }, [user, attorney, navigate, onNavigateToSignup, onNavigateToDomain, onNavigateToAgent, onHighlightElement]);

  if (!currentGuidance) return null;

  const getPriorityIcon = () => {
    switch (currentGuidance.priority) {
      case 'high': return <FaExclamationCircle className="priority-icon high" />;
      case 'medium': return <FaArrowRight className="priority-icon medium" />;
      default: return <FaCheck className="priority-icon low" />;
    }
  };

  return (
    <div className="contextual-shepherd">
      <div className={`shepherd-card ${currentGuidance.priority}`}>
        <div className="shepherd-content">
          <div className="shepherd-header">
            {getPriorityIcon()}
            <h4>{currentGuidance.title}</h4>
          </div>
          <p>{currentGuidance.message}</p>
        </div>
        <button
          className="shepherd-action"
          onClick={currentGuidance.onClick}
        >
          {currentGuidance.action}
          <FaArrowRight />
        </button>
      </div>

      {/* Progress dots */}
      <div className="progress-dots">
        <div className={`dot ${completionStatus.auth ? 'complete' : 'pending'}`} />
        <div className={`dot ${completionStatus.subdomain ? 'complete' : 'pending'}`} />
        <div className={`dot ${completionStatus.profile ? 'complete' : 'pending'}`} />
        <div className={`dot ${completionStatus.assistant ? 'complete' : 'pending'}`} />
      </div>
    </div>
  );
};

export default ProgressiveSetupGuide;
