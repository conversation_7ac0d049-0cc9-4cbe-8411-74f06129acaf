.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: transparent;
}

.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0; /* Prevent flex item from overflowing */
    background-color: transparent;
}

.call-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0; /* Prevent flex item from overflowing */
    background-color: transparent;
}

.vapi-call-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0; /* Prevent flex item from overflowing */
    background-color: transparent;
}

.call-controller {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    color: #ffffff;
    z-index: 2; /* Above the map layer */
    box-sizing: border-box;
    background-color: transparent;
}

.call-controller.with-map {
    background-color: transparent; /* Let the map show through */
}

/* End call button */
.vapi-end-call-button {
    position: fixed;
    top: 80px;
    right: 30px;
    background-color: rgba(10, 30, 60, 0.4);
    color: white;
    border: none;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    z-index: 50; /* Highest z-index, above everything */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.vapi-end-call-button:hover {
    background-color: rgba(15, 40, 80, 0.6);
    box-shadow: 0 5px 25px rgba(0, 40, 100, 0.5);
}

.call-detective-icon {
    width: 60px;
    height: 60px;
}

/* Facts card (previously call-controls) */
.facts-card {
    position: absolute;
    top: 80px;
    left: 20px;
    width: 340px;
    max-height: calc(100vh - 180px);
    overflow-y: auto;
    background-color: rgba(10, 20, 40, 0.85);
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 25; /* Above the map layer */
    color: white;
    border: 1px solid rgba(100, 150, 255, 0.1);
}

.facts-card h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    color: #4a90e2;
    font-weight: 600;
}

.dossier-info {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.dossier-item {
    display: flex;
    flex-direction: column;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    background-color: rgba(255, 255, 255, 0.05);
}

.dossier-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 4px;
}

.dossier-value {
    font-size: 14px;
    color: #CCEEFF;
}

.webhook-data {
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.webhook-data h3 {
    font-size: 1.2rem;
    margin: 0 0 10px 0;
    color: #3b82f6;
}

.webhook-data p {
    margin: 0;
    line-height: 1.5;
    font-size: 0.95rem;
}

.dossier-container {
    margin-bottom: 20px;
}

.dossier-container h3 {
    font-size: 1.2rem;
    margin: 0 0 15px 0;
    color: #f7df1e;
}

.empty-message {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.toggle-map-button,
.find-attorney-button {
    padding: 8px 16px;
    border-radius: 20px;
    border: none;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle-map-button {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
}

.toggle-map-button:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

.find-attorney-button {
    background-color: #3b82f6;
    color: white;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
}

.find-attorney-button:hover {
    background-color: #2563eb;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
}

/* Attorney results */
.attorney-results {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 340px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 10; /* Above the map layer */
    color: #333;
}

.attorney-results h3 {
    font-size: 1.2rem;
    margin: 0 0 15px 0;
    color: #3b82f6;
}

.attorney-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.attorney-card {
    background: #333;
    padding: 15px;
    border-radius: 6px;
}

.attorney-card h4 {
    margin: 0 0 10px 0;
    color: #fff;
}

.attorney-card p {
    margin: 5px 0;
    color: #ccc;
}

.specialty {
    color: #3b82f6;
    font-weight: 500;
}

.address {
    color: rgba(255, 255, 255, 0.8);
}

.distance {
    color: #f7df1e;
    font-weight: 500;
}

.rating {
    color: rgba(255, 255, 255, 0.8);
}

.contact-button {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.contact-button:hover {
    background-color: #059669;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    color: white;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error-message {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 68, 68, 0.95);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1000;
    max-width: 80%;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .facts-card,
    .attorney-results {
        width: calc(100% - 40px);
        max-width: none;
    }

    .attorney-results {
        bottom: auto;
        top: 50%;
        max-height: 45vh;
    }

    .vapi-end-call-button {
        top: 15px;
        right: 15px;
        width: 35px;
        height: 35px;
        font-size: 0.7rem;
    }
}

/* Call status styling */
.call-status {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    background-color: rgba(10, 20, 40, 0.85);
    padding: 8px 12px;
    border-radius: 8px;
    z-index: 30; /* Above most elements */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.status-dot {
    width: 10px;
    height: 10px;
    background-color: #ef5350; /* Red when not active */
    border-radius: 50%;
    margin-right: 6px;
}

.status-dot.active {
    background-color: #4CAF50; /* Green when active */
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.active-call-status {
    display: flex;
    align-items: center;
    margin-left: 12px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background-color: #ff5252;
    border-radius: 50%;
    margin-right: 6px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Suggested responses section */
.suggested-responses {
    margin-bottom: 15px;
}

.suggested-responses h3 {
    font-size: 14px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.response-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.response-button {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 18px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.response-button:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* New call UI controls */
.call-ui-controls {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.75);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    color: white;
    text-align: center;
    max-width: 90%;
    width: 500px;
}

.call-message {
    margin-bottom: 25px;
}

.call-message p {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
}

.hint-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem !important;
}

/* Volume control styles */
.volume-control {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.volume-icon {
    margin-right: 15px;
    font-size: 1.2rem;
}

.volume-slider {
    -webkit-appearance: none;
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    outline: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.volume-value {
    margin-left: 15px;
    font-size: 0.9rem;
    min-width: 45px;
    text-align: right;
}

.show-map-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 24px;
    font-size: 1rem;
    font-weight: 500;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
    cursor: pointer;
    transition: all 0.3s;
}

.show-map-button:hover {
    background-color: #2563eb;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    transform: translateY(-2px);
}

/* Dark map styling */
.dark-map {
    filter: invert(90%) hue-rotate(180deg);
}

.dark-tiles {
    filter: brightness(0.6) invert(1) saturate(3) hue-rotate(180deg);
}

.call-status-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.85);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
    background: #666;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.status-dot.error {
    background: #ff4444;
}

.status-dot.connected {
    background: #4CAF50;
    animation: pulse 2s infinite;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.status-dot.disconnected {
    background: #ff9800;
}

.call-content {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 10;
    padding: 20px;
}

.map-dossier-view {
    width: 100%;
    height: 500px;
    min-height: 400px;
    background: rgba(42, 42, 42, 0.95);
    border-radius: 12px;
    overflow: visible;
    margin: 20px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.map-container {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* Text input styles */
.text-input-container {
    display: flex;
    align-items: center;
    border-radius: 30px;
    background-color: rgba(29, 35, 42, 0.8);
    padding: 0 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    margin: 0 auto;
    max-width: 900px;
    width: calc(100% - 40px);
    height: 50px;
}

.text-input-container:focus-within {
    background-color: rgba(40, 48, 58, 0.9);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.text-input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    height: 40px;
    min-height: 40px;
    resize: none;
    padding: 10px 8px 10px 0;
    outline: none;
    line-height: 1.5;
}

.text-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.send-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #4a90e2;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 0;
    margin-left: 8px;
    flex-shrink: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.send-button:hover {
    background-color: #3a7fcf;
}

.send-button:disabled {
    background-color: rgba(74, 144, 226, 0.5);
    cursor: not-allowed;
}

/* Messages container */
.messages-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

/* Individual message styling */
.message {
    margin-bottom: 15px;
    max-width: 80%;
    line-height: 1.4;
    position: relative;
    font-size: 16px;
    animation: messageAppear 0.3s ease;
}

.message.user {
    background-color: #4a90e2; /* Blue for user messages */
    color: white;
    align-self: flex-end;
    margin-left: auto;
    border-radius: 18px;
    border-bottom-right-radius: 4px;
    padding: 10px 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.message.assistant {
    color: #CCEEFF;
    align-self: flex-start;
    margin-right: auto;
    padding: 0;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
    font-weight: normal;
}

@keyframes messageAppear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Map view container styling */
.map-view-container {
    position: fixed;
    top: 60px; /* Space for navbar */
    left: 0;
    right: 0;
    bottom: 70px; /* Space for input box */
    width: 100%;
    height: calc(100% - 130px); /* Subtract navbar and input box height */
    background-color: transparent !important;
    z-index: 0; /* Lowest z-index to ensure it's the background */
    overflow: hidden;
    display: block !important; /* Force display */
}

/* Conversation container - anchored to bottom */
.conversation-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 30; /* Above most elements */
    padding: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
    height: 70px; /* Fixed height for the input area */
    display: flex;
    align-items: center;
}

/* Messages area - float above the map */
.conversation-messages {
    position: absolute;
    bottom: 80px; /* Just above the input container */
    width: 100%;
    max-height: 30vh;
    overflow-y: auto;
    padding: 15px 20px;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    z-index: 20; /* Above the map layer */
}

/* Input area */
.conversation-input {
    background-color: transparent;
    width: 100%;
    padding: 0 20px;
    height: 50px; /* Fixed height */
    display: flex;
    align-items: center;
}

.text-input-container {
    display: flex;
    align-items: center;
    border-radius: 30px;
    background-color: rgba(29, 35, 42, 0.8);
    padding: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    margin: 0 auto;
    max-width: 900px;
    width: 100%;
    height: 50px;
}

.text-input-container:focus-within {
    background-color: rgba(40, 48, 58, 0.9);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.text-input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    height: 40px;
    min-height: 40px;
    resize: none;
    padding: 8px 8px 8px 16px;
    outline: none;
    line-height: 1.5;
}

.text-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.send-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #4a90e2;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 0;
    margin-left: 8px;
    flex-shrink: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.send-button:hover {
    background-color: #3a7fcf;
}

.send-button:disabled {
    background-color: rgba(74, 144, 226, 0.5);
    cursor: not-allowed;
}

/* Make sure all UI elements have higher z-index than the map */
.call-content,
.facts-card,
.attorney-results,
.conversation-container,
.conversation-messages,
.conversation-input,
.call-status,
.toggle-map-button,
.find-attorney-button {
    position: relative;
    z-index: 10; /* Above the map layer */
}

/* Call Controller Wrapper */
.call-controller-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 60px); /* Account for navbar */
    z-index: 15;
    color: white;
}

/* Dossier Component Container */
.dossier-component-container {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 300px;
    max-height: calc(100vh - 180px);
    background: rgba(10, 20, 30, 0.75);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 20;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(67, 126, 255, 0.2);
    overflow-y: auto;
}

.dossier-component-container h2 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 8px;
}

.dossier-component-container .dossier-info {
    margin-bottom: 20px;
}

.dossier-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dossier-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.dossier-label {
    font-weight: 600;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.dossier-value {
    font-size: 14px;
    line-height: 1.4;
    word-break: break-word;
}

.empty-message {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    font-size: 14px;
}

.webhook-data {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.webhook-data h3 {
    font-size: 14px;
    margin: 0 0 8px 0;
    color: rgba(255, 255, 255, 0.8);
}

.webhook-data p {
    font-size: 13px;
    line-height: 1.4;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.toggle-map-button,
.find-attorney-button {
    padding: 8px 12px;
    background: rgba(67, 126, 255, 0.2);
    border: 1px solid rgba(67, 126, 255, 0.4);
    border-radius: 5px;
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-map-button:hover,
.find-attorney-button:hover {
    background: rgba(67, 126, 255, 0.4);
}

.find-attorney-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* End Call Button Container */
.end-call-button-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 50;
}

.vapi-end-call-button {
    position: relative;
    background-color: rgba(10, 30, 60, 0.4);
    color: white;
    border: none;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.vapi-end-call-button:hover {
    background-color: rgba(15, 40, 80, 0.6);
    box-shadow: 0 5px 25px rgba(0, 40, 100, 0.5);
}

.call-detective-icon {
    width: 60px;
    height: 60px;
}

/* Conversation Container */
.conversation-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    max-width: 800px;
    z-index: 30;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.conversation-messages {
    max-height: 300px;
    overflow-y: auto;
    background-color: rgba(10, 20, 30, 0.6);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.2);
}

.conversation-messages::-webkit-scrollbar {
    width: 6px;
}

.conversation-messages::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
}

.conversation-messages::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
}

.message {
    padding: 12px 15px;
    border-radius: 10px;
    max-width: 80%;
    animation: messageAppear 0.3s ease-out;
    line-height: 1.4;
    font-size: 14px;
}

.message.user {
    align-self: flex-end;
    background-color: rgba(67, 126, 255, 0.3);
    border: 1px solid rgba(67, 126, 255, 0.5);
    margin-left: auto;
}

.message.assistant {
    align-self: flex-start;
    background-color: rgba(50, 50, 50, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-right: auto;
}

/* Text Entry Container */
.text-input-container {
    display: flex;
    background-color: rgba(30, 40, 60, 0.6);
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all 0.2s ease;
    border: 1px solid rgba(100, 150, 255, 0.2);
}

.text-input-container:focus-within {
    box-shadow: 0 4px 20px rgba(67, 126, 255, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.4);
}

.text-input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    font-size: 15px;
    padding: 8px 12px;
    outline: none;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.send-button {
    background-color: rgba(67, 126, 255, 0.6);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.send-button:hover {
    background-color: rgba(67, 126, 255, 0.8);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Attorney Results */
.attorney-results {
    position: absolute;
    top: 20px;
    right: 140px; /* Position to the left of the End Call button */
    width: 350px;
    max-height: calc(100vh - 180px);
    background-color: rgba(10, 20, 30, 0.75);
    border-radius: 10px;
    padding: 20px;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 25;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(67, 126, 255, 0.2);
    color: white;
}

.attorney-results h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 8px;
}

.attorney-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.attorney-card {
    background-color: rgba(30, 40, 60, 0.6);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(67, 126, 255, 0.2);
}

.attorney-card h4 {
    margin: 0 0 8px 0;
    color: white;
    font-size: 16px;
}

.attorney-card p {
    margin: 4px 0;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
}

.attorney-card .firm {
    font-weight: 500;
    color: rgba(67, 126, 255, 0.9);
}

.attorney-card .specialty {
    font-style: italic;
}

.attorney-card .rating {
    font-weight: 600;
}

.attorney-card .contact-button {
    margin-top: 10px;
    background-color: rgba(67, 126, 255, 0.6);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 13px;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.attorney-card .contact-button:hover {
    background-color: rgba(67, 126, 255, 0.8);
}

/* Loading overlay */
.loading-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: rgba(67, 126, 255, 0.8);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-message {
    background-color: rgba(255, 50, 50, 0.2);
    border: 1px solid rgba(255, 50, 50, 0.4);
    padding: 12px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
}

/* Call Status Indicator */
.call-status-indicator {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: rgba(10, 20, 30, 0.6);
    border-radius: 20px;
    padding: 8px 15px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    z-index: 40;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ccc;
}

.status-dot.connected {
    background-color: #4caf50;
}

.status-dot.connected.pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.status-text {
    font-size: 12px;
    color: white;
}

/* Message appearance animation */
@keyframes messageAppear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive styles */
@media (max-width: 1200px) {
    .attorney-results {
        width: 300px;
    }
}

@media (max-width: 992px) {
    .dossier-component-container {
        width: 250px;
    }

    .attorney-results {
        width: 280px;
        right: 130px;
    }
}

@media (max-width: 768px) {
    .dossier-component-container {
        position: static;
        width: 100%;
        max-width: 600px;
        margin: 0 auto 20px auto;
        max-height: none;
    }

    .attorney-results {
        position: static;
        width: 100%;
        max-width: 600px;
        margin: 20px auto;
        max-height: none;
    }

    .end-call-button-container {
        top: 10px;
        right: 10px;
    }

    .vapi-end-call-button {
        width: 80px;
        height: 80px;
    }

    .call-detective-icon {
        width: 50px;
        height: 50px;
    }

    .conversation-container {
        position: static;
        transform: none;
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
    }

    .call-controller-wrapper {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .call-status-indicator {
        position: static;
        width: fit-content;
        margin: 0 auto;
    }
}