:root {
  --primary-color: #4B74AA;
  --secondary-color: #2c3e50;
  --background-color: #000000;
  --text-color: #ffffff;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --error-color: #c0392b;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --navbar-height: 60px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
}

#root {
  height: 100%;
  width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

button, input, textarea {
  font-family: var(--font-family);
}

/* Link styling */
a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* App-wide utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Base text sizes */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5em;
  line-height: 1.2;
}

p {
  margin-bottom: 1em;
  line-height: 1.5;
}

/* Global Reset and Base Styles */
body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #000;
  color: #fff;
}

/* Header & Navigation */
header {
  background-color: var(--nav-bg, #050404);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 15px 20px;
}

nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container img {
  max-width: 150px;
  height: auto;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  color: #fff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed header */
  background-color: #000;
}

.statement {
  text-align: center;
  font-size: 1.5em;
  line-height: 1.5;
  margin: 30px auto;
}

.statement p span {
  display: block;
  margin: 0.5rem 0;
}

/* Button Container & Custom Button Styles */
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;
}

.mascot-button {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  box-shadow: 0 0 15px 2px rgba(0, 100, 255, 0.3);
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.mascot-button.hovered {
  transform: scale(1.05);
  animation: ripple 1.5s infinite;
}

.mascot-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* When connected, change button appearance to indicate end call */
.mascot-button.connected {
  background-color: #ff5555;
}

.mascot-image {
  width: 80%;
  height: 80%;
  border-radius: 50%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.mascot-button:hover .mascot-image {
  transform: scale(1.05);
}

.button-label {
  margin-top: 20px;
  color: white;
  font-size: 1.2rem;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Additional styling */
#app {
  padding: 20px;
  margin-top: 100px; /* Ensure content is not hidden under the fixed header */
}

/* index.css */

/* Animation classes for replacing Framer Motion */
.animate-fade-in {
  animation: fade-in 0.7s ease-out forwards;
}

.animate-fade-in-delay-200 {
  opacity: 0;
  animation: fade-in 0.7s ease-out 0.2s forwards;
}

.animate-fade-in-delay-400 {
  opacity: 0;
  animation: fade-in 0.7s ease-out 0.4s forwards;
}

.animate-fade-in-delay-800 {
  opacity: 0;
  animation: fade-in 0.5s ease-out 0.8s forwards;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.95);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

/* ... other styles ... */

@keyframes ripple {
  0% {
    box-shadow: 0 0 20px 5px rgba(0, 100, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 40px 10px rgba(0, 100, 255, 0.7);
  }
  100% {
    box-shadow: 0 0 20px 5px rgba(0, 100, 255, 0.5);
  }
}
.header {
    background-color: #333;
    color: #fff;
    padding: 10px 0;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* App container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

/* Fixed text input container - global styles */
.fixed-text-input-container {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999999 !important;
  margin: 0 !important;
  padding: 10px 20px !important;
  box-sizing: border-box !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.consultation-status {
  margin-top: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #0078ff;
  text-align: center;
  animation: fade-in 0.5s ease-in-out;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* End call indicator that appears on connected buttons */
.end-call-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.end-call-indicator span {
  color: #ff5555;
  font-size: 24px;
  font-weight: bold;
}

/* Adjust mascot image position in connected state */
.mascot-button.connected .mascot-image {
  opacity: 0.5;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.loading-dot {
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
  animation: loading-dot-fade 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-dot-fade {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes emanate {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.95);
  }
  100% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* Cosmic Button Animations */
@keyframes borderPulse {
  0% {
    border-color: rgba(41, 121, 255, 0.01);
    box-shadow: 0 0 0 0 rgba(41, 121, 255, 0.01);
  }
  50% {
    border-color: rgba(100, 181, 246, 0.1);
    box-shadow: 0 0 10px 2px rgba(100, 181, 246, 0.05);
  }
  100% {
    border-color: rgba(41, 121, 255, 0.01);
    box-shadow: 0 0 0 0 rgba(41, 121, 255, 0.01);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(41, 121, 255, 0.7));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(100, 181, 246, 0.9));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(41, 121, 255, 0.7));
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(var(--x, 0));
    opacity: 0;
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 5px rgba(41, 121, 255, 0.7);
  }
  50% {
    text-shadow: 0 0 15px rgba(100, 181, 246, 0.9), 0 0 30px rgba(41, 121, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 5px rgba(41, 121, 255, 0.7);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Markdown Styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 1.8em;
}

.markdown-content h2 {
  font-size: 1.5em;
}

.markdown-content h3 {
  font-size: 1.3em;
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.markdown-content li {
  margin-bottom: 0.5em;
}

.markdown-content a {
  color: #4B74AA;
  text-decoration: underline;
}

.markdown-content a:hover {
  text-decoration: none;
}

.markdown-content blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

.markdown-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.markdown-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1em;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.markdown-content th {
  background-color: rgba(0, 0, 0, 0.05);
}

.markdown-content img {
  max-width: 100%;
  height: auto;
}

/* Dark mode styles */
.dark .markdown-content a {
  color: #7EB3FF;
}

.dark .markdown-content blockquote {
  border-color: #555;
  color: #aaa;
}

.dark .markdown-content code,
.dark .markdown-content pre {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .markdown-content th {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .markdown-content th,
.dark .markdown-content td {
  border-color: #555;
}

/* Add loading container and spinner styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  width: 100%;
  color: #5C6BC0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(92, 107, 192, 0.2);
  border-radius: 50%;
  border-top-color: #5C6BC0;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 1rem;
  margin: 0;
  opacity: 0.8;
}

/* Voice Gender Color Coding */
.voice-select {
  position: relative;
}

.voice-option.voice-male {
  background-color: rgba(59, 130, 246, 0.1) !important; /* Subtle blue for male */
  color: #1e40af !important;
}

.voice-option.voice-female {
  background-color: rgba(236, 72, 153, 0.1) !important; /* Subtle pink for female */
  color: #be185d !important;
}

.voice-option.voice-neutral {
  background-color: rgba(107, 114, 128, 0.1) !important; /* Subtle gray for neutral */
  color: #374151 !important;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .voice-option.voice-male {
    background-color: rgba(59, 130, 246, 0.15) !important;
    color: #60a5fa !important;
  }

  .voice-option.voice-female {
    background-color: rgba(236, 72, 153, 0.15) !important;
    color: #f472b6 !important;
  }

  .voice-option.voice-neutral {
    background-color: rgba(107, 114, 128, 0.15) !important;
    color: #9ca3af !important;
  }
}

/* Hover effects for voice options */
.voice-option:hover {
  opacity: 0.8;
  font-weight: 500;
}

