/**
 * MCP Servers Configuration
 * 
 * Configuration for all MCP servers used in LegalScout Voice.
 * Based on PulseMCP.com directory analysis.
 */

// Helper function to get environment variables
const getEnvVar = (name) => {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[name];
  }
  if (typeof process !== 'undefined' && process.env) {
    return process.env[name];
  }
  return null;
};

export const mcpServersConfig = {
  // Priority 1: Core Business Operations
  supabase: {
    name: 'Supabase MCP Server',
    command: 'npx',
    args: ['@supabase/mcp-server'],
    env: {
      SUPABASE_URL: getEnvVar('VITE_SUPABASE_URL') || 'https://utopqxsvudgrtiwenlzl.supabase.co',
      SUPABASE_SERVICE_KEY: getEnvVar('SUPABASE_SERVICE_KEY') || getEnvVar('VITE_SUPABASE_KEY')
    },
    enabled: true,
    priority: 1,
    description: 'Enhanced database operations with natural language queries'
  },

  gmail: {
    name: 'Gmail MCP Server',
    command: 'npx',
    args: ['@anthropic/gmail-mcp-server'],
    env: {
      GMAIL_CLIENT_ID: getEnvVar('GMAIL_CLIENT_ID'),
      GMAIL_CLIENT_SECRET: getEnvVar('GMAIL_CLIENT_SECRET')
    },
    enabled: false, // Enable when credentials are configured
    priority: 1,
    description: 'Attorney email notifications and communication'
  },

  slack: {
    name: 'Slack MCP Server',
    command: 'npx',
    args: ['@anthropic/slack-mcp-server'],
    env: {
      SLACK_BOT_TOKEN: getEnvVar('SLACK_BOT_TOKEN')
    },
    enabled: false, // Enable when credentials are configured
    priority: 1,
    description: 'Team communication and real-time notifications'
  },

  // Priority 2: Legal-Specific Enhancements
  browserTools: {
    name: 'Browser Tools MCP',
    command: 'npx',
    args: ['@agentdeskai/browser-tools-mcp'],
    env: {},
    enabled: true,
    priority: 2,
    description: 'Browser automation for legal research and data collection',
    tools: ['capture_screenshot', 'get_console_logs', 'monitor_network', 'select_elements', 'lighthouse_audit']
  },

  // Future integrations (when packages become available)
  googleCalendar: {
    name: 'Google Calendar Integration',
    command: 'node',
    args: ['scripts/mcp-servers/google-calendar-server.js'],
    env: {
      GOOGLE_CALENDAR_CLIENT_ID: getEnvVar('GOOGLE_CALENDAR_CLIENT_ID'),
      GOOGLE_CALENDAR_CLIENT_SECRET: getEnvVar('GOOGLE_CALENDAR_CLIENT_SECRET')
    },
    enabled: false,
    priority: 2,
    description: 'Consultation scheduling and calendar integration',
    placeholder: true
  },

  pdf: {
    name: 'PDF Processing Server',
    command: 'node',
    args: ['scripts/mcp-servers/pdf-server.js'],
    env: {},
    enabled: false,
    priority: 2,
    description: 'Legal document processing and analysis',
    placeholder: true
  },

  // Existing servers
  vapi: {
    name: 'Vapi MCP Server',
    command: 'npx',
    args: ['@vapi-ai/mcp-server'],
    env: {
      VAPI_TOKEN: getEnvVar('VAPI_TOKEN') || getEnvVar('VITE_VAPI_SECRET_KEY') || '6734febc-fc65-4669-93b0-929b31ff6564'
    },
    enabled: true,
    priority: 1,
    description: 'Voice AI integration and call management'
  },

  aiMeta: {
    name: 'AI Meta MCP Server',
    command: 'node',
    args: ['ai-meta-mcp-server/build/index.js'],
    env: {},
    enabled: true,
    priority: 3,
    description: 'Dynamic tool creation and meta-functions'
  }
};

// Get enabled servers only
export const getEnabledServers = () => {
  return Object.entries(mcpServersConfig)
    .filter(([_, config]) => config.enabled)
    .reduce((acc, [key, config]) => {
      acc[key] = config;
      return acc;
    }, {});
};

// Get servers by priority
export const getServersByPriority = (priority) => {
  return Object.entries(mcpServersConfig)
    .filter(([_, config]) => config.priority === priority)
    .reduce((acc, [key, config]) => {
      acc[key] = config;
      return acc;
    }, {});
};

export default mcpServersConfig;
