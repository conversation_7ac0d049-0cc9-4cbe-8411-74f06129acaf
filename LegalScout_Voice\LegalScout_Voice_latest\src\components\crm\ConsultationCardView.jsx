import React from 'react';
import './CrmViews.css';

const ConsultationCardView = () => {
  return (
    <div className="crm-card-view">
      <div className="card-grid">
        <div className="consultation-card">
          <div className="card-header">
            <div className="client-info">
              <div className="client-avatar">JD</div>
              <div className="client-details">
                <div className="client-name">John <PERSON></div>
                <div className="client-email"><EMAIL></div>
              </div>
            </div>
            <div className="card-status">
              <span className="status-badge new">New Lead</span>
            </div>
          </div>
          
          <div className="card-body">
            <div className="consultation-detail">
              <div className="detail-label">Date & Time</div>
              <div className="detail-value">Apr 21, 2025 • 2:30 PM</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Duration</div>
              <div className="detail-value">12 minutes</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Topic</div>
              <div className="detail-value">Personal Injury</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Location</div>
              <div className="detail-value">Philadelphia, PA</div>
            </div>
            
            <div className="consultation-summary">
              <div className="summary-label">Summary</div>
              <div className="summary-text">
                Client seeking consultation about a workplace injury that occurred last week. Mentioned potential negligence by employer.
              </div>
            </div>
          </div>
          
          <div className="card-footer">
            <button className="card-action-btn view-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg> View
            </button>
            <button className="card-action-btn schedule-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg> Schedule
            </button>
            <button className="card-action-btn scout-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg> Send Scout
            </button>
          </div>
        </div>
        
        <div className="consultation-card">
          <div className="card-header">
            <div className="client-info">
              <div className="client-avatar">JS</div>
              <div className="client-details">
                <div className="client-name">Jane Smith</div>
                <div className="client-email"><EMAIL></div>
              </div>
            </div>
            <div className="card-status">
              <span className="status-badge follow-up">Follow-up</span>
            </div>
          </div>
          
          <div className="card-body">
            <div className="consultation-detail">
              <div className="detail-label">Date & Time</div>
              <div className="detail-value">Apr 20, 2025 • 10:15 AM</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Duration</div>
              <div className="detail-value">18 minutes</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Topic</div>
              <div className="detail-value">Divorce Consultation</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Location</div>
              <div className="detail-value">Pittsburgh, PA</div>
            </div>
            
            <div className="consultation-summary">
              <div className="summary-label">Summary</div>
              <div className="summary-text">
                Client inquiring about divorce proceedings. Married for 8 years with 2 children. Concerned about custody arrangements.
              </div>
            </div>
          </div>
          
          <div className="card-footer">
            <button className="card-action-btn view-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg> View
            </button>
            <button className="card-action-btn schedule-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg> Schedule
            </button>
            <button className="card-action-btn scout-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg> Send Scout
            </button>
          </div>
        </div>
        
        <div className="consultation-card">
          <div className="card-header">
            <div className="client-info">
              <div className="client-avatar">MJ</div>
              <div className="client-details">
                <div className="client-name">Michael Johnson</div>
                <div className="client-email"><EMAIL></div>
              </div>
            </div>
            <div className="card-status">
              <span className="status-badge completed">Completed</span>
            </div>
          </div>
          
          <div className="card-body">
            <div className="consultation-detail">
              <div className="detail-label">Date & Time</div>
              <div className="detail-value">Apr 19, 2025 • 3:45 PM</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Duration</div>
              <div className="detail-value">22 minutes</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Topic</div>
              <div className="detail-value">Business Law</div>
            </div>
            
            <div className="consultation-detail">
              <div className="detail-label">Location</div>
              <div className="detail-value">Philadelphia, PA</div>
            </div>
            
            <div className="consultation-summary">
              <div className="summary-label">Summary</div>
              <div className="summary-text">
                Client needed advice on forming an LLC for a new business venture. Discussed liability protection and tax implications.
              </div>
            </div>
          </div>
          
          <div className="card-footer">
            <button className="card-action-btn view-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg> View
            </button>
            <button className="card-action-btn schedule-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg> Schedule
            </button>
            <button className="card-action-btn scout-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg> Send Scout
            </button>
          </div>
        </div>
      </div>
      
      <div className="card-pagination">
        <button className="pagination-btn" disabled>Previous</button>
        <div className="pagination-info">Showing 1-6 of 24</div>
        <button className="pagination-btn">Next</button>
      </div>
    </div>
  );
};

export default ConsultationCardView;
