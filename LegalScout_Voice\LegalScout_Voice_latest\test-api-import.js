/**
 * Test API Import
 * This file tests if we can import the API handler without errors
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();
try {
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('No .env.local file found, using only .env');
}

console.log('Testing API import...');

try {
  // Try to import the API handler
  const { default: apiHandler } = await import('./api/index.js');
  console.log('✅ API handler imported successfully');
  console.log('API handler type:', typeof apiHandler);
} catch (error) {
  console.error('❌ Failed to import API handler:', error);
  console.error('Error stack:', error.stack);
}
