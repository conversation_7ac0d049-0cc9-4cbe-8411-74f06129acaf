export interface EmailTemplate {
  subject: string;
  includeAttachments?: boolean;
  includePDF?: boolean;
  includeClientInfo?: boolean;
  includeDossier?: boolean;
  includeCalendlyLink?: boolean;
}

export interface EmailServiceConfig {
  enabled: boolean;
  templates: {
    consultationSummary: EmailTemplate;
    attorneyNotification: EmailTemplate;
    followUp: EmailTemplate;
  };
}

export interface SearchConfig {
  enabled: boolean;
  services: {
    maps: boolean;
    news: boolean;
    scholar: boolean;
    images: boolean;
  };
  rateLimit: number;
}

export interface CalendlyEventType {
  duration: number;
  name: string;
}

export interface CalendlyConfig {
  enabled: boolean;
  eventTypes: {
    initialConsultation: CalendlyEventType;
    followUp: CalendlyEventType;
  };
}

export interface PerplexityConfig {
  enabled: boolean;
  features: {
    legalResearch: boolean;
    caseLaw: boolean;
    jurisdictionInfo: boolean;
  };
  model: string;
  temperature: number;
}

export interface DocumentConfig {
  enabled: boolean;
  templates: {
    consultationSummary: boolean;
    legalDocuments: boolean;
    agreements: boolean;
  };
  options: {
    encryption: boolean;
    watermark: boolean;
    signature: boolean;
  };
}

export interface VapiMcpConfig {
  enabled: boolean;
  apiKey?: string;
  features: {
    assistantManagement: boolean;
    callManagement: boolean;
    phoneNumberManagement: boolean;
  };
}

export interface MCPConfig {
  email: {
    gmail: EmailServiceConfig;
    outlook: EmailServiceConfig;
  };
  search: {
    serpApi: SearchConfig;
  };
  calendar: {
    calendly: CalendlyConfig;
  };
  ai: {
    perplexity: PerplexityConfig;
  };
  document: {
    textToPDF: DocumentConfig;
  };
  voice: {
    vapi: VapiMcpConfig;
  };
}