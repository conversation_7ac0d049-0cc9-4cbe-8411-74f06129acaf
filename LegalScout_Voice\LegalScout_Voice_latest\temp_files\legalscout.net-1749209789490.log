dashboard:11 ✅ Vapi public key set globally
dashboard:32 ✅ Supabase keys set globally - should load correct assistant by domain
dashboard:43 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:47 🔧 [EMERGENCY] Adding process polyfill
dashboard:54 ✅ [EMERGENCY] Process polyfill added
dashboard:65 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
dashboard:98 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at production-cors-fix.js:62:16)
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src-elem 'self' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io".

vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:93 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:93
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
overrideMethod @ hook.js:608
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
index.ts:5 Loaded contentScript
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:11 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:43 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:47 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:54 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:65 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:98 🎉 [EMERGENCY] Emergency fixes complete!
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:11 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:43 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:47 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:54 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:65 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:98 🎉 [EMERGENCY] Emergency fixes complete!
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
VM371 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
dashboard:93 
            
            
           GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a 401 (Unauthorized)
window.fetch @ dashboard:93
getAssistant @ vapiMcpService.js:409
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a: 401
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:424
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard:93 
            
            
           GET https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a net::ERR_FAILED
window.fetch @ dashboard:93
getAssistant @ vapiMcpService.js:409
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:427
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:432 [VapiMcpService] Assistant not found with ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:432
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:300 [RobustStateHandler] Assistant ID exists but assistant not found in Vapi: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ hook.js:608
getAttorneyAssistants @ robust-state-handler.js:300
await in getAttorneyAssistants
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:93 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:93
createAssistant @ vapiMcpService.js:611
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:626 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:626
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:707 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:707
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 1 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:93 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:93
createAssistant @ vapiMcpService.js:611
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:626 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:626
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:707 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:707
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 2 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (VM371 robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
dashboard:93 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:93
createAssistant @ vapiMcpService.js:611
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:626 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:626
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:707 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:707
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 3 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:496 ❌ [RobustStateHandler] Error creating default assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:635:19)
    at async createVapiAssistant (VM371 robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (VM371 robust-state-handler.js:527:29)
    at async createDefaultAssistant (VM371 robust-state-handler.js:486:27)
    at async resolveAssistantState (VM371 robust-state-handler.js:237:30)
    at async performStateResolution (VM371 robust-state-handler.js:104:34)
overrideMethod @ hook.js:608
createDefaultAssistant @ robust-state-handler.js:496
await in createDefaultAssistant
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:255 ❌ [RobustStateHandler] Failed to create default assistant
overrideMethod @ hook.js:608
resolveAssistantState @ robust-state-handler.js:255
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:11 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:43 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:47 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:54 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:65 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:98 🎉 [EMERGENCY] Emergency fixes complete!
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:11 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:43 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:47 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:54 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:65 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:98 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at production-cors-fix.js:62:16)
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at production-cors-fix.js:62:16)
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:88
Zn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
ActiveCheckHelper.ts:21 received intentional event
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:11 ✅ Vapi public key set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:43 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:47 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:54 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:65 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:98 🎉 [EMERGENCY] Emergency fixes complete!
dashboard:93 
            
            
           GET https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:93
listAssistants @ vapiMcpService.js:537
await in listAssistants
$e @ AgentTab.jsx:1829
await in $e
(anonymous) @ AgentTab.jsx:1887
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiMcpService.js:574 [VapiMcpService] Error listing assistants: Error: Direct API error: 401 
    at La.listAssistants (vapiMcpService.js:546:17)
    at async $e (AgentTab.jsx:1829:29)
overrideMethod @ hook.js:608
listAssistants @ vapiMcpService.js:574
await in listAssistants
$e @ AgentTab.jsx:1829
await in $e
(anonymous) @ AgentTab.jsx:1887
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
AgentTab.jsx:1867 [AgentTab] Error loading assistants from Vapi MCP: Error: Direct API error: 401 
    at La.listAssistants (vapiMcpService.js:546:17)
    at async $e (AgentTab.jsx:1829:29)
overrideMethod @ hook.js:608
$e @ AgentTab.jsx:1867
await in $e
(anonymous) @ AgentTab.jsx:1887
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiLogger.js:103 [11:35:25] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
VM454 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:93 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:93
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
(anonymous) @ AgentTab.jsx:495
await in (anonymous)
(anonymous) @ AgentTab.jsx:537
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
(anonymous) @ AgentTab.jsx:495
await in (anonymous)
(anonymous) @ AgentTab.jsx:537
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
dashboard:93 
            
            
           GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a 401 (Unauthorized)
window.fetch @ dashboard:93
getAssistant @ vapiMcpService.js:409
await in getAssistant
f @ AssistantInfoSection.jsx:50
await in f
(anonymous) @ AssistantInfoSection.jsx:37
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiMcpService.js:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a: 401
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:424
await in getAssistant
f @ AssistantInfoSection.jsx:50
await in f
(anonymous) @ AssistantInfoSection.jsx:37
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM459 disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
VM459 disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
VM459 disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
VM459 disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard:93 
            
            
           GET https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a net::ERR_FAILED
window.fetch @ dashboard:93
getAssistant @ vapiMcpService.js:409
await in getAssistant
f @ AssistantInfoSection.jsx:50
await in f
(anonymous) @ AssistantInfoSection.jsx:37
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiMcpService.js:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:427
await in getAssistant
f @ AssistantInfoSection.jsx:50
await in f
(anonymous) @ AssistantInfoSection.jsx:37
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiMcpService.js:432 [VapiMcpService] Assistant not found with ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:432
await in getAssistant
f @ AssistantInfoSection.jsx:50
await in f
(anonymous) @ AssistantInfoSection.jsx:37
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM460 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM460 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
vapiLogger.js:103 [11:35:25] [VapiMcpService] Retrieving assistant {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
VM461 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM461 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM461 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM461 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM462 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM462 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM462 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM463 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM463 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM463 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM464 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM464 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM464 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM465 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM465 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM465 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM454 robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
VM466 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM466 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM466 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM466 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM467 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM467 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM467 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM467 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM467 unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
VM467 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM467 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
ActiveCheckHelper.ts:8 updating page active status
VM468 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM468 dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (VM468 dashboard-iframe-manager.js:187:14)
    at VM468 dashboard-iframe-manager.js:242:3
    at VM468 dashboard-iframe-manager.js:266:3
setupIframeObserver @ VM468 dashboard-iframe-manager.js:187
(anonymous) @ VM468 dashboard-iframe-manager.js:242
(anonymous) @ VM468 dashboard-iframe-manager.js:266
VM469 production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at VM469 production-cors-fix.js:62:16)
VM470 clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
VM470 clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
VM470 clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
VM470 clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
VM470 clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
VM470 clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
VM470 clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
VM470 clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
VM470 clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
VM470 clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
VM470 clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
VM470 clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
VM454 robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
VM476 pages-3a847f4c.js:1 [VapiLoader] Starting Vapi SDK loading process
VM476 pages-3a847f4c.js:1 [VapiLoader] Attempting to import @vapi-ai/web package
VM476 pages-3a847f4c.js:5 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
VM476 pages-3a847f4c.js:5 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
VM476 pages-3a847f4c.js:5 Supabase Key configured: eyJhb...K4cRU
VM476 pages-3a847f4c.js:5 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
VM476 pages-3a847f4c.js:5 Supabase client initialized successfully with proper headers
VM476 pages-3a847f4c.js:5 Testing Supabase connection...
VM476 pages-3a847f4c.js:5 Supabase client ready for use
VM476 pages-3a847f4c.js:5 Attaching Supabase client to window.supabase
VM476 pages-3a847f4c.js:51 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
VM477 index-c0497284.js:172 Environment Variable Verification
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:93
send @ VM475 vendor-9bf0d152.js:55
await in send
(anonymous) @ VM475 vendor-9bf0d152.js:9
request @ VM475 vendor-9bf0d152.js:9
connect @ VM475 vendor-9bf0d152.js:9
await in connect
_connect @ VM476 pages-3a847f4c.js:51
await in _connect
connect @ VM476 pages-3a847f4c.js:51
initialize @ VM477 index-c0497284.js:17
Zn @ VM477 index-c0497284.js:172
(anonymous) @ VM477 index-c0497284.js:172
VM476 pages-3a847f4c.js:51 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ VM476 pages-3a847f4c.js:51
await in _connect
connect @ VM476 pages-3a847f4c.js:51
initialize @ VM477 index-c0497284.js:17
Zn @ VM477 index-c0497284.js:172
(anonymous) @ VM477 index-c0497284.js:172
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:473 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:473
setTimeout
(anonymous) @ DashboardNew.jsx:471
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
vapiLogger.js:103 [11:35:28] [VapiMcpService] Assistant verified in Vapi {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'LegalScout Assistant'}
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
ActiveCheckHelper.ts:8 updating page active status
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
VM470 clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ VM470 clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ VM470 clean-auth-solution.js:77
executeSolution @ VM470 clean-auth-solution.js:208
(anonymous) @ VM470 clean-auth-solution.js:219
(anonymous) @ VM470 clean-auth-solution.js:221
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
standalone-attorney-manager-fixed.js:468 [StandaloneAttorneyManager] Error saving to localStorage: QuotaExceededError: Failed to execute 'setItem' on 'Storage': Setting the value of 'attorney' exceeded the quota.
    at StandaloneAttorneyManager.saveToLocalStorage (VM461 standalone-attorney-manager-fixed.js:458:22)
    at StandaloneAttorneyManager.updateAttorney (VM461 standalone-attorney-manager-fixed.js:501:20)
    at VM476 pages-3a847f4c.js:74:12697
    at Be (VM476 pages-3a847f4c.js:226:53108)
    at F.onloadend (VM476 pages-3a847f4c.js:215:69736)
overrideMethod @ hook.js:608
saveToLocalStorage @ standalone-attorney-manager-fixed.js:468
updateAttorney @ standalone-attorney-manager-fixed.js:501
(anonymous) @ useStandaloneAttorney.js:159
Be @ DashboardNew.jsx:745
F.onloadend @ AgentTab.jsx:310
FileReader
ee @ AgentTab.jsx:312
up @ react-dom.production.min.js:54
fp @ react-dom.production.min.js:54
dp @ react-dom.production.min.js:55
sc @ react-dom.production.min.js:105
Gf @ react-dom.production.min.js:106
(anonymous) @ react-dom.production.min.js:117
Js @ react-dom.production.min.js:273
xf @ react-dom.production.min.js:52
Ni @ react-dom.production.min.js:109
_s @ react-dom.production.min.js:74
zp @ react-dom.production.min.js:73
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
VM454 robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ VM454 robust-state-handler.js:34
setTimeout
robustStateHandler @ VM454 robust-state-handler.js:32
(anonymous) @ VM454 robust-state-handler.js:650
ActiveCheckHelper.ts:21 received intentional event
standalone-attorney-manager-fixed.js:468 [StandaloneAttorneyManager] Error saving to localStorage: QuotaExceededError: Failed to execute 'setItem' on 'Storage': Setting the value of 'attorney' exceeded the quota.
    at StandaloneAttorneyManager.saveToLocalStorage (VM461 standalone-attorney-manager-fixed.js:458:22)
    at StandaloneAttorneyManager.updateAttorney (VM461 standalone-attorney-manager-fixed.js:501:20)
    at VM476 pages-3a847f4c.js:74:12697
    at Be (VM476 pages-3a847f4c.js:226:53108)
    at R (VM476 pages-3a847f4c.js:215:69527)
    at Object.up (VM474 vendor-react-f13bac96.js:29:9852)
    at fp (VM474 vendor-react-f13bac96.js:29:10006)
    at dp (VM474 vendor-react-f13bac96.js:29:10063)
    at sc (VM474 vendor-react-f13bac96.js:29:31443)
    at Gf (VM474 vendor-react-f13bac96.js:29:31860)
overrideMethod @ hook.js:608
saveToLocalStorage @ standalone-attorney-manager-fixed.js:468
updateAttorney @ standalone-attorney-manager-fixed.js:501
(anonymous) @ useStandaloneAttorney.js:159
Be @ DashboardNew.jsx:745
R @ AgentTab.jsx:282
up @ react-dom.production.min.js:54
fp @ react-dom.production.min.js:54
dp @ react-dom.production.min.js:55
sc @ react-dom.production.min.js:105
Gf @ react-dom.production.min.js:106
(anonymous) @ react-dom.production.min.js:117
Js @ react-dom.production.min.js:273
xf @ react-dom.production.min.js:52
Ni @ react-dom.production.min.js:109
_s @ react-dom.production.min.js:74
zp @ react-dom.production.min.js:73
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
standalone-attorney-manager-fixed.js:468 [StandaloneAttorneyManager] Error saving to localStorage: QuotaExceededError: Failed to execute 'setItem' on 'Storage': Setting the value of 'attorney' exceeded the quota.
    at StandaloneAttorneyManager.saveToLocalStorage (VM461 standalone-attorney-manager-fixed.js:458:22)
    at StandaloneAttorneyManager.updateAttorney (VM461 standalone-attorney-manager-fixed.js:501:20)
    at VM476 pages-3a847f4c.js:74:12697
    at Be (VM476 pages-3a847f4c.js:226:53108)
    at ve (VM476 pages-3a847f4c.js:215:69382)
    at Object.up (VM474 vendor-react-f13bac96.js:29:9852)
    at fp (VM474 vendor-react-f13bac96.js:29:10006)
    at dp (VM474 vendor-react-f13bac96.js:29:10063)
    at sc (VM474 vendor-react-f13bac96.js:29:31443)
    at Gf (VM474 vendor-react-f13bac96.js:29:31860)
overrideMethod @ hook.js:608
saveToLocalStorage @ standalone-attorney-manager-fixed.js:468
updateAttorney @ standalone-attorney-manager-fixed.js:501
(anonymous) @ useStandaloneAttorney.js:159
Be @ DashboardNew.jsx:745
ve @ AgentTab.jsx:267
up @ react-dom.production.min.js:54
fp @ react-dom.production.min.js:54
dp @ react-dom.production.min.js:55
sc @ react-dom.production.min.js:105
Gf @ react-dom.production.min.js:106
(anonymous) @ react-dom.production.min.js:117
Js @ react-dom.production.min.js:273
xf @ react-dom.production.min.js:52
Ni @ react-dom.production.min.js:109
_s @ react-dom.production.min.js:74
zp @ react-dom.production.min.js:73
onVisualColorChange_ @ unknown
onColorSelectionRingUpdate_ @ unknown
updateColor @ unknown
onPositionChange_ @ unknown
set @ unknown
moveTo @ unknown
moveColorSelectionRingTo_ @ unknown
pointerDown @ unknown
onColorWellMouseDown_ @ unknown
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
standalone-attorney-manager-fixed.js:468 [StandaloneAttorneyManager] Error saving to localStorage: QuotaExceededError: Failed to execute 'setItem' on 'Storage': Setting the value of 'attorney' exceeded the quota.
    at StandaloneAttorneyManager.saveToLocalStorage (VM461 standalone-attorney-manager-fixed.js:458:22)
    at StandaloneAttorneyManager.updateAttorney (VM461 standalone-attorney-manager-fixed.js:501:20)
    at VM476 pages-3a847f4c.js:74:12697
    at Be (VM476 pages-3a847f4c.js:226:53108)
    at J (VM476 pages-3a847f4c.js:215:161523)
overrideMethod @ hook.js:608
saveToLocalStorage @ standalone-attorney-manager-fixed.js:468
updateAttorney @ standalone-attorney-manager-fixed.js:501
(anonymous) @ useStandaloneAttorney.js:159
Be @ DashboardNew.jsx:745
J @ CustomFieldsTab.jsx:168
await in J
up @ react-dom.production.min.js:54
fp @ react-dom.production.min.js:54
dp @ react-dom.production.min.js:55
sc @ react-dom.production.min.js:105
Gf @ react-dom.production.min.js:106
(anonymous) @ react-dom.production.min.js:117
Js @ react-dom.production.min.js:273
xf @ react-dom.production.min.js:52
Ni @ react-dom.production.min.js:109
_s @ react-dom.production.min.js:74
zp @ react-dom.production.min.js:73
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
