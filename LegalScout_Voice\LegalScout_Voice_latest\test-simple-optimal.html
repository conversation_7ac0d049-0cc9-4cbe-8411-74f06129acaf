<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Optimal Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        .config-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .config-section {
            background: #333;
            padding: 15px;
            border-radius: 8px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255,255,255,0.05);
            border-radius: 4px;
        }
        .config-value {
            font-family: monospace;
            color: #34d399;
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎯 Test Simple Optimal Architecture</h1>
    <p><strong>UI from Supabase</strong> + <strong>Calls from Vapi</strong> = <strong>Simple & Optimal</strong></p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testSimpleService()">Test Simple Service</button>
        <button onclick="testBothSubdomains()">Test Both Subdomains</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script type="module">
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.addResult = addResult;
        window.clearResults = clearResults;

        window.testSimpleService = async function() {
            addResult('info', '🎯 Testing simple optimal service...');
            
            try {
                const { simpleSubdomainService } = await import('./src/services/simpleSubdomainService.js');
                
                addResult('success', '✅ Successfully imported simple subdomain service');
                
                // Test assistant1test subdomain
                const config = await simpleSubdomainService.getSubdomainConfig('assistant1test');
                
                if (config) {
                    addResult('success', `✅ assistant1test config loaded:
🏢 Firm Name: ${config.firmName}
🎨 Primary Color: ${config.primaryColor}
🎤 Voice: ${config.voiceId} (${config.voiceProvider})
🤖 Assistant ID: ${config.assistant_id}
📊 Has UI Config: ${config.hasUIConfig}
📞 Has Call Config: ${config.hasCallConfig}
🔧 Loaded Via: ${config.loadedVia}`);

                    // Show detailed config
                    const configDiv = document.createElement('div');
                    configDiv.className = 'config-display';
                    
                    configDiv.innerHTML = `
                        <div class="config-section">
                            <h4>🎨 UI Variables (from Supabase)</h4>
                            <div class="config-item">
                                <span>Firm Name:</span>
                                <span class="config-value">${config.firmName}</span>
                            </div>
                            <div class="config-item">
                                <span>Primary Color:</span>
                                <span class="config-value" style="color: ${config.primaryColor}">${config.primaryColor}</span>
                            </div>
                            <div class="config-item">
                                <span>Background:</span>
                                <span class="config-value" style="color: ${config.backgroundColor}">${config.backgroundColor}</span>
                            </div>
                            <div class="config-item">
                                <span>Welcome Message:</span>
                                <span class="config-value">${config.welcomeMessage?.substring(0, 30)}...</span>
                            </div>
                            <div class="config-item">
                                <span>Voice ID:</span>
                                <span class="config-value">${config.voiceId}</span>
                            </div>
                        </div>
                        <div class="config-section">
                            <h4>📞 Call Config (from Vapi)</h4>
                            <div class="config-item">
                                <span>Assistant ID:</span>
                                <span class="config-value">${config.assistant_id}</span>
                            </div>
                            <div class="config-item">
                                <span>Vapi Name:</span>
                                <span class="config-value">${config.vapiAssistant?.name || 'N/A'}</span>
                            </div>
                            <div class="config-item">
                                <span>LLM Model:</span>
                                <span class="config-value">${config.vapiAssistant?.llm?.model || 'N/A'}</span>
                            </div>
                            <div class="config-item">
                                <span>Voice Provider:</span>
                                <span class="config-value">${config.vapiAssistant?.voice?.provider || 'N/A'}</span>
                            </div>
                            <div class="config-item">
                                <span>Voice ID:</span>
                                <span class="config-value">${config.vapiAssistant?.voice?.voiceId || 'N/A'}</span>
                            </div>
                        </div>
                    `;
                    
                    const resultsDiv = document.getElementById('results');
                    const configSection = document.createElement('div');
                    configSection.className = 'test-section success';
                    configSection.innerHTML = '<h4>📊 Detailed Configuration</h4>';
                    configSection.appendChild(configDiv);
                    resultsDiv.appendChild(configSection);
                    
                } else {
                    addResult('error', '❌ No config returned for assistant1test');
                }
                
            } catch (error) {
                addResult('error', `❌ Simple service test failed: ${error.message}`);
                console.error('Simple service error:', error);
            }
        };

        window.testBothSubdomains = async function() {
            addResult('info', '🔍 Testing both subdomains with simple service...');
            
            try {
                const { simpleSubdomainService } = await import('./src/services/simpleSubdomainService.js');
                
                // Test both subdomains
                const [damonConfig, testConfig] = await Promise.all([
                    simpleSubdomainService.getSubdomainConfig('damon'),
                    simpleSubdomainService.getSubdomainConfig('assistant1test')
                ]);
                
                if (damonConfig && testConfig) {
                    addResult('success', `✅ Both subdomains loaded successfully!

🌐 damon.legalscout.net:
   Assistant: ${damonConfig.assistant_id}
   Colors: ${damonConfig.primaryColor}
   Voice: ${damonConfig.voiceId}
   
🌐 assistant1test.legalscout.net:
   Assistant: ${testConfig.assistant_id}
   Colors: ${testConfig.primaryColor}
   Voice: ${testConfig.voiceId}`);

                    // Verify they're different
                    const differentAssistants = damonConfig.assistant_id !== testConfig.assistant_id;
                    const differentColors = damonConfig.primaryColor !== testConfig.primaryColor;
                    
                    if (differentAssistants && differentColors) {
                        addResult('success', '🎉 PERFECT! Each subdomain has unique assistant and UI variables!');
                    } else {
                        addResult('info', `Analysis:
- Different Assistants: ${differentAssistants ? '✅' : '❌'}
- Different Colors: ${differentColors ? '✅' : '❌'}`);
                    }
                } else {
                    addResult('error', `❌ Failed to load configs:
- damon: ${!!damonConfig}
- assistant1test: ${!!testConfig}`);
                }
                
            } catch (error) {
                addResult('error', `❌ Both subdomains test failed: ${error.message}`);
                console.error('Both subdomains error:', error);
            }
        };

        // Auto-run test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSimpleService();
            }, 1000);
        });
    </script>
</body>
</html>
