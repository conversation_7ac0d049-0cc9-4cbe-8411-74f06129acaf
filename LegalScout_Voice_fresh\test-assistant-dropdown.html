<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant Dropdown Diagnostic Test - Updated</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Assistant Dropdown Test</h1>
    
    <div class="test-section info">
        <h2>Test Instructions</h2>
        <p>This page will test the assistant dropdown functionality by:</p>
        <ol>
            <li>Checking if <PERSON>pa<PERSON> is accessible</li>
            <li>Querying the attorney_assistants table</li>
            <li>Testing the Vapi API connection</li>
            <li>Simulating the dropdown loading logic</li>
        </ol>
    </div>

    <div id="test-results"></div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        
        function addResult(title, status, message, data = null) {
            const div = document.createElement('div');
            div.className = `test-section ${status}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <p>${message}</p>
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            resultsDiv.appendChild(div);
        }

        async function runTests() {
            addResult('Starting Tests', 'info', 'Running assistant dropdown tests...');

            // Test 1: Check if Supabase is available
            try {
                if (typeof window.supabase !== 'undefined') {
                    addResult('Supabase Client', 'success', 'Supabase client is available');
                    
                    // Test 2: Query attorney_assistants table
                    try {
                        const { data, error } = await window.supabase
                            .from('attorney_assistants')
                            .select('*')
                            .limit(5);
                        
                        if (error) {
                            addResult('Attorney Assistants Query', 'error', `Error: ${error.message}`, error);
                        } else {
                            addResult('Attorney Assistants Query', 'success', `Found ${data.length} assistant mappings`, data);
                        }
                    } catch (err) {
                        addResult('Attorney Assistants Query', 'error', `Exception: ${err.message}`, err);
                    }
                } else {
                    addResult('Supabase Client', 'error', 'Supabase client not found on window object');
                }
            } catch (err) {
                addResult('Supabase Client', 'error', `Exception: ${err.message}`, err);
            }

            // Test 3: Check if attorney data is in localStorage
            try {
                const attorneyData = localStorage.getItem('attorney');
                if (attorneyData) {
                    const attorney = JSON.parse(attorneyData);
                    addResult('Attorney Data', 'success', 'Attorney data found in localStorage', {
                        id: attorney.id,
                        firm_name: attorney.firm_name,
                        vapi_assistant_id: attorney.vapi_assistant_id
                    });
                } else {
                    addResult('Attorney Data', 'error', 'No attorney data found in localStorage');
                }
            } catch (err) {
                addResult('Attorney Data', 'error', `Exception: ${err.message}`, err);
            }

            // Test 4: Test Vapi API connection
            try {
                const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
                const response = await fetch(`/api/vapi-proxy/assistant/${assistantId}`);
                
                if (response.ok) {
                    const assistantData = await response.json();
                    addResult('Vapi API Test', 'success', 'Vapi API is accessible', {
                        id: assistantData.id,
                        name: assistantData.name,
                        status: response.status
                    });
                } else {
                    addResult('Vapi API Test', 'error', `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (err) {
                addResult('Vapi API Test', 'error', `Exception: ${err.message}`, err);
            }

            addResult('Tests Complete', 'info', 'All tests have been executed. Check results above.');
        }

        // Wait for page to load, then run tests
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // Give time for any global scripts to load
        });
    </script>
</body>
</html>
