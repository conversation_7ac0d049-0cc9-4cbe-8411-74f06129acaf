/**
 * Fast Bulk Delete for Vapi Assistants
 * Optimized for speed with parallel processing and batching
 */

const fetch = require('node-fetch');
const fs = require('fs');

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Your correct assistant ID that should be kept
const KEEP_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

// Optimized settings for faster deletion
const BATCH_SIZE = 20; // Delete 20 assistants in parallel
const DELAY_BETWEEN_BATCHES = 1000; // 1 second between batches (instead of 1 second per assistant)
const MAX_RETRIES = 3;

async function makeVapiRequest(endpoint, method = 'GET', retryCount = 0) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      
      // Handle rate limiting
      if (response.status === 429 && retryCount < MAX_RETRIES) {
        const retryAfter = response.headers.get('retry-after') || 5;
        console.log(`Rate limited, waiting ${retryAfter} seconds before retry...`);
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return makeVapiRequest(endpoint, method, retryCount + 1);
      }
      
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    if (response.status === 204) {
      return null; // No content for successful delete
    }

    return await response.json();
  } catch (error) {
    if (retryCount < MAX_RETRIES) {
      console.log(`Request failed, retrying... (${retryCount + 1}/${MAX_RETRIES})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
      return makeVapiRequest(endpoint, method, retryCount + 1);
    }
    throw error;
  }
}

async function getAllAssistants() {
  console.log('📋 Fetching ALL assistants from Vapi...');
  
  let allAssistants = [];
  let hasMore = true;
  let batchCount = 0;
  const limit = 1000;
  
  while (hasMore) {
    batchCount++;
    console.log(`   Fetching batch ${batchCount} (limit: ${limit})...`);
    
    let endpoint = `/assistant?limit=${limit}`;
    
    if (allAssistants.length > 0) {
      const oldestAssistant = allAssistants[allAssistants.length - 1];
      const createdAtFilter = encodeURIComponent(oldestAssistant.createdAt);
      endpoint = `/assistant?limit=${limit}&createdAtLt=${createdAtFilter}`;
    }
    
    const batch = await makeVapiRequest(endpoint);
    console.log(`   Retrieved ${batch.length} assistants in batch ${batchCount}`);
    
    if (batch.length === 0) {
      hasMore = false;
    } else {
      allAssistants = allAssistants.concat(batch);
      
      if (batch.length < limit) {
        hasMore = false;
      }
    }
    
    if (hasMore) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  console.log(`Found ${allAssistants.length} total assistants`);
  return allAssistants;
}

async function deleteAssistant(assistantId) {
  try {
    await makeVapiRequest(`/assistant/${assistantId}`, 'DELETE');
    return { success: true, id: assistantId };
  } catch (error) {
    return { success: false, id: assistantId, error: error.message };
  }
}

async function deleteAssistantsBatch(assistants) {
  const promises = assistants.map(assistant => deleteAssistant(assistant.id));
  return await Promise.all(promises);
}

async function performFastCleanup() {
  console.log('🚀 Starting FAST bulk cleanup...\n');
  
  try {
    // Get all assistants
    const allAssistants = await getAllAssistants();
    
    // Find assistants to delete (all except the one to keep)
    const toDelete = allAssistants.filter(a => a.id !== KEEP_ASSISTANT_ID);
    const toKeep = allAssistants.find(a => a.id === KEEP_ASSISTANT_ID);
    
    console.log('\n📊 Cleanup Plan:');
    console.log(`   Total assistants: ${allAssistants.length}`);
    console.log(`   ✅ Keep: ${toKeep ? 1 : 0} (${toKeep ? toKeep.name : 'NOT FOUND'})`);
    console.log(`   🗑️  Delete: ${toDelete.length}`);
    console.log(`   ⚡ Batch size: ${BATCH_SIZE} (parallel deletions)`);
    console.log(`   ⏱️  Estimated time: ${Math.ceil(toDelete.length / BATCH_SIZE)} minutes\n`);
    
    if (!toKeep) {
      console.log('⚠️  WARNING: Your assistant to keep was not found!');
      console.log(`   Looking for: ${KEEP_ASSISTANT_ID}`);
      console.log('   Proceeding anyway...\n');
    }
    
    if (toDelete.length === 0) {
      console.log('🎉 No assistants to delete!');
      return;
    }
    
    // Process in batches
    const results = {
      deleted: [],
      failed: [],
      totalBatches: Math.ceil(toDelete.length / BATCH_SIZE)
    };
    
    console.log('🗑️  Starting parallel deletion...\n');
    
    for (let i = 0; i < toDelete.length; i += BATCH_SIZE) {
      const batch = toDelete.slice(i, i + BATCH_SIZE);
      const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
      
      console.log(`📦 Batch ${batchNumber}/${results.totalBatches}: Deleting ${batch.length} assistants...`);
      
      const startTime = Date.now();
      const batchResults = await deleteAssistantsBatch(batch);
      const endTime = Date.now();
      
      // Process results
      batchResults.forEach(result => {
        if (result.success) {
          results.deleted.push(result.id);
        } else {
          results.failed.push(result);
        }
      });
      
      const successCount = batchResults.filter(r => r.success).length;
      const failCount = batchResults.filter(r => !r.success).length;
      const batchTime = ((endTime - startTime) / 1000).toFixed(1);
      
      console.log(`   ✅ Success: ${successCount}, ❌ Failed: ${failCount} (${batchTime}s)`);
      console.log(`   📊 Progress: ${results.deleted.length}/${toDelete.length} (${((results.deleted.length / toDelete.length) * 100).toFixed(1)}%)`);
      
      // Save progress to file
      const progress = {
        timestamp: new Date().toISOString(),
        totalToDelete: toDelete.length,
        deleted: results.deleted.length,
        failed: results.failed.length,
        batchesCompleted: batchNumber,
        totalBatches: results.totalBatches
      };
      fs.writeFileSync('cleanup-progress.json', JSON.stringify(progress, null, 2));
      
      // Delay between batches (much shorter than before)
      if (i + BATCH_SIZE < toDelete.length) {
        console.log(`   ⏳ Waiting ${DELAY_BETWEEN_BATCHES/1000}s before next batch...\n`);
        await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
      }
    }
    
    console.log('\n🎯 CLEANUP COMPLETE!');
    console.log('📊 Final Results:');
    console.log(`   ✅ Successfully deleted: ${results.deleted.length}`);
    console.log(`   ❌ Failed to delete: ${results.failed.length}`);
    console.log(`   🎉 Success rate: ${((results.deleted.length / toDelete.length) * 100).toFixed(1)}%`);
    
    if (results.failed.length > 0) {
      console.log('\n❌ Failed deletions:');
      results.failed.slice(0, 10).forEach(item => {
        console.log(`   - ${item.id}: ${item.error}`);
      });
      if (results.failed.length > 10) {
        console.log(`   ... and ${results.failed.length - 10} more`);
      }
    }
    
    // Verify final state
    console.log('\n🔍 Verifying final state...');
    const finalAssistants = await getAllAssistants();
    const remainingUnwanted = finalAssistants.filter(a => a.id !== KEEP_ASSISTANT_ID);
    
    if (remainingUnwanted.length === 0) {
      console.log('🎉 SUCCESS: All duplicate assistants have been cleaned up!');
      console.log(`   Only your correct assistant remains: ${KEEP_ASSISTANT_ID}`);
    } else {
      console.log(`⚠️  ${remainingUnwanted.length} assistants still remain (may be failed deletions)`);
    }
    
    // Clean up progress file
    if (fs.existsSync('cleanup-progress.json')) {
      fs.unlinkSync('cleanup-progress.json');
    }
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⏹️  Cleanup interrupted by user');
  console.log('📄 Progress saved to cleanup-progress.json');
  process.exit(0);
});

console.log('⚡ FAST Vapi Assistant Cleanup Tool');
console.log('===================================');
console.log(`🎯 Target: Keep only ${KEEP_ASSISTANT_ID}`);
console.log(`⚡ Speed: ${BATCH_SIZE} parallel deletions per batch`);
console.log(`⏱️  Delay: ${DELAY_BETWEEN_BATCHES/1000}s between batches`);
console.log('🛑 Press Ctrl+C to stop and save progress\n');

performFastCleanup();
