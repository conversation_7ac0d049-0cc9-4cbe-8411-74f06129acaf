/**
 * Config Validator
 * 
 * This component validates configuration before saving it to the database.
 * It displays validation errors and warnings to the user.
 */

import React, { useState } from 'react';
import { useSync } from '../contexts/SyncContext';

/**
 * Config Validator Component
 * 
 * @param {Object} props - The component props
 * @param {string} props.attorneyId - The ID of the attorney
 * @param {Object} props.configData - The configuration data to validate
 * @param {Function} props.onValidated - Callback function called when validation is complete
 * @param {boolean} props.autoValidate - Whether to validate automatically when the component mounts
 * @returns {JSX.Element} The component
 */
const ConfigValidator = ({ 
  attorneyId, 
  configData, 
  onValidated, 
  autoValidate = false 
}) => {
  const { validateConfig } = useSync();
  const [validationResult, setValidationResult] = useState(null);
  const [validating, setValidating] = useState(autoValidate);
  
  // Validate the configuration
  const validate = async () => {
    if (!attorneyId || !configData) {
      setValidationResult({
        valid: false,
        message: 'Missing attorney ID or configuration data'
      });
      return;
    }
    
    setValidating(true);
    
    try {
      const result = await validateConfig(attorneyId, configData);
      setValidationResult(result);
      
      if (onValidated) {
        onValidated(result);
      }
      
      return result;
    } catch (error) {
      console.error('Error validating configuration:', error);
      
      setValidationResult({
        valid: false,
        error: error.message,
        message: `Error validating configuration: ${error.message}`
      });
      
      if (onValidated) {
        onValidated({
          valid: false,
          error: error.message,
          message: `Error validating configuration: ${error.message}`
        });
      }
    } finally {
      setValidating(false);
    }
  };
  
  // Validate automatically if autoValidate is true
  React.useEffect(() => {
    if (autoValidate) {
      validate();
    }
  }, [autoValidate, attorneyId, configData]);
  
  // If there's no validation result, don't render anything
  if (!validationResult && !validating) {
    return null;
  }
  
  return (
    <div className="config-validator">
      {validating ? (
        <div className="validation-loading">
          Validating configuration...
        </div>
      ) : validationResult?.valid ? (
        <div className="validation-success">
          <p>{validationResult.message || 'Configuration is valid'}</p>
        </div>
      ) : (
        <div className="validation-error">
          <h4>Configuration Validation Failed</h4>
          <p>{validationResult?.message || 'Unknown validation error'}</p>
          
          {validationResult?.missingFields && (
            <div className="missing-fields">
              <h5>Missing Required Fields:</h5>
              <ul>
                {Object.entries(validationResult.missingFields).map(([section, fields]) => (
                  <li key={section}>
                    <strong>{section}:</strong> {fields.join(', ')}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {validationResult?.validationErrors && validationResult.validationErrors.length > 0 && (
            <div className="validation-errors">
              <h5>Validation Errors:</h5>
              <ul>
                {validationResult.validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      <style jsx>{`
        .config-validator {
          margin-bottom: 20px;
        }
        
        .validation-loading {
          padding: 10px;
          background-color: #f8f9fa;
          border-radius: 4px;
          color: #6c757d;
        }
        
        .validation-success {
          padding: 10px;
          background-color: #d4edda;
          border-radius: 4px;
          color: #155724;
        }
        
        .validation-error {
          padding: 10px;
          background-color: #f8d7da;
          border-radius: 4px;
          color: #721c24;
        }
        
        .validation-error h4 {
          margin-top: 0;
          font-size: 16px;
        }
        
        .validation-error h5 {
          margin-top: 10px;
          font-size: 14px;
        }
        
        .validation-error ul {
          margin-bottom: 0;
        }
      `}</style>
    </div>
  );
};

export default ConfigValidator;
