/**
 * Initialize Vapi Debugger
 * 
 * This module initializes the Vapi debugging tools and provides
 * a simple console interface for debugging Vapi MCP server issues.
 */

import { configureDebugger, checkVapiEnvironment } from './vapiMcpDebugger';
import { installVapiNetworkInterceptor } from './vapiNetworkInterceptor';
import { runAllDiagnostics } from './vapiMcpDiagnostics';

/**
 * Initialize the Vapi debugger
 * @param {Object} config - Configuration options
 */
export const initVapiDebugger = (config = {}) => {
  // Configure the debugger
  configureDebugger({
    enabled: true,
    logToConsole: true,
    logToMemory: true,
    maxLogEntries: 100,
    networkLogging: {
      enabled: true,
      includeHeaders: true,
      includeBody: true,
      maxBodyLength: 500
    },
    ...config
  });
  
  // Install network interceptor
  installVapiNetworkInterceptor();
  
  // Check environment
  const environment = checkVapiEnvironment();
  
  // Create global debug commands
  if (typeof window !== 'undefined') {
    window.VapiDebug = {
      // Run diagnostics
      diagnose: async () => {
        console.group('🔍 Vapi MCP Diagnostics');
        console.log('Running all diagnostics...');
        
        try {
          const results = await runAllDiagnostics();
          
          console.log('✅ Diagnostics completed');
          console.log('Results:', results);
          
          if (results.overallSuccess) {
            console.log('%c✅ At least one connection method works', 'color: green; font-weight: bold');
          } else {
            console.log('%c❌ No connection method works', 'color: red; font-weight: bold');
          }
          
          return results;
        } catch (error) {
          console.error('❌ Error running diagnostics:', error);
          return { success: false, error: error.message };
        } finally {
          console.groupEnd();
        }
      },
      
      // Check environment
      checkEnv: () => {
        console.group('🔍 Vapi Environment Check');
        const env = checkVapiEnvironment();
        console.log('Environment:', env);
        console.groupEnd();
        return env;
      },
      
      // Show help
      help: () => {
        console.group('🔧 Vapi Debug Commands');
        console.log('VapiDebug.diagnose() - Run all diagnostics');
        console.log('VapiDebug.checkEnv() - Check environment variables');
        console.log('VapiDebug.help() - Show this help');
        console.log('VapiDebug.clearLogs() - Clear all logs');
        console.log('VapiDebugger.getLogs() - Get connection logs');
        console.log('VapiDebugger.getNetworkLogs() - Get network logs');
        console.log('VapiDebugger.getErrors() - Get error logs');
        console.groupEnd();
      },
      
      // Clear logs
      clearLogs: () => {
        if (window.VapiMcpDebugger) {
          window.VapiMcpDebugger.clearAll();
        }
        console.log('✅ All logs cleared');
      }
    };
    
    // Log initialization
    console.log('🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.');
  }
  
  return { environment };
};

// Export default
export default initVapiDebugger;
