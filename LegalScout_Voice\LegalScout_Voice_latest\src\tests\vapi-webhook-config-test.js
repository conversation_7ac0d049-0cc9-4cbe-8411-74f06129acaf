/**
 * Vapi Webhook Configuration Test
 * 
 * This script checks and configures Vapi webhook settings
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config({ path: '../../.env' });

const VAPI_API_KEY = process.env.VAPI_TOKEN;
const ASSISTANT_ID = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
const WEBHOOK_URL = 'https://legalscout.app/api/webhook/vapi-call';
const WEBHOOK_SECRET = process.env.VAPI_WEBHOOK_SECRET;

async function vapiRequest(endpoint, options = {}) {
  const url = `https://api.vapi.ai${endpoint}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`${response.status} ${response.statusText}: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`❌ Vapi API error for ${endpoint}:`, error.message);
    return null;
  }
}

async function checkCurrentWebhookConfig() {
  console.log('🔍 Checking current webhook configuration...\n');
  
  // Check organization settings
  const org = await vapiRequest('/org');
  if (org) {
    console.log('Organization webhook settings:');
    console.log(`  Server URL: ${org.serverUrl || 'Not configured'}`);
    console.log(`  Server Secret: ${org.serverUrlSecret ? 'Configured' : 'Not configured'}`);
    
    if (org.serverUrl === WEBHOOK_URL) {
      console.log('  ✅ Webhook URL matches expected');
    } else {
      console.log('  ⚠️  Webhook URL does not match expected');
    }
  }

  // Check assistant settings
  const assistant = await vapiRequest(`/assistant/${ASSISTANT_ID}`);
  if (assistant) {
    console.log('\nAssistant webhook settings:');
    console.log(`  Server URL: ${assistant.serverUrl || 'Using org default'}`);
    console.log(`  Server Secret: ${assistant.serverUrlSecret ? 'Configured' : 'Using org default'}`);
  }

  return { org, assistant };
}

async function configureWebhook() {
  console.log('\n🔧 Configuring webhook settings...\n');
  
  // Update organization webhook settings
  const orgUpdate = await vapiRequest('/org', {
    method: 'PATCH',
    body: JSON.stringify({
      serverUrl: WEBHOOK_URL,
      serverUrlSecret: WEBHOOK_SECRET
    })
  });

  if (orgUpdate) {
    console.log('✅ Organization webhook configured successfully');
  } else {
    console.log('❌ Failed to configure organization webhook');
  }

  return orgUpdate;
}

async function testWebhookDelivery() {
  console.log('\n🧪 Testing webhook delivery...\n');
  
  // Get recent calls to see if webhooks are being delivered
  const calls = await vapiRequest('/call?limit=5');
  
  if (calls && calls.length > 0) {
    console.log(`Found ${calls.length} recent calls:`);
    calls.forEach((call, index) => {
      console.log(`  ${index + 1}. ${call.id} - ${call.status} (${call.createdAt})`);
      if (call.assistantId === ASSISTANT_ID) {
        console.log('     ✅ This call used your assistant');
      }
    });
  } else {
    console.log('No recent calls found');
  }

  return calls;
}

async function runWebhookConfigTest() {
  console.log('🚀 Vapi Webhook Configuration Test\n');
  
  if (!VAPI_API_KEY) {
    console.log('❌ VAPI_API_KEY not found in environment');
    return;
  }

  console.log('Configuration:');
  console.log(`  API Key: ${VAPI_API_KEY.substring(0, 8)}...`);
  console.log(`  Assistant ID: ${ASSISTANT_ID}`);
  console.log(`  Webhook URL: ${WEBHOOK_URL}`);
  console.log(`  Webhook Secret: ${WEBHOOK_SECRET ? 'Configured' : 'Not configured'}`);
  console.log('');

  // Check current configuration
  const { org, assistant } = await checkCurrentWebhookConfig();

  // Configure webhook if needed
  if (!org?.serverUrl || org.serverUrl !== WEBHOOK_URL) {
    console.log('\n⚙️  Webhook URL needs configuration...');
    await configureWebhook();
  } else {
    console.log('\n✅ Webhook URL is already configured correctly');
  }

  // Test webhook delivery
  await testWebhookDelivery();

  // Summary
  console.log('\n📊 CONFIGURATION SUMMARY:');
  console.log(`  Webhook URL configured: ${org?.serverUrl === WEBHOOK_URL ? '✅ Yes' : '❌ No'}`);
  console.log(`  Webhook secret configured: ${org?.serverUrlSecret ? '✅ Yes' : '❌ No'}`);
  console.log(`  Assistant found: ${assistant ? '✅ Yes' : '❌ No'}`);

  console.log('\n💡 NEXT STEPS:');
  console.log('  1. Make a test call to your assistant');
  console.log('  2. Check webhook endpoint logs');
  console.log('  3. Verify data appears in Supabase');
  console.log('  4. Check dashboard for new consultations');
}

runWebhookConfigTest().catch(console.error);
