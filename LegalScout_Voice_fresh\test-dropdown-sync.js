/**
 * Test script to verify dropdown sync issues are resolved
 * Run this in browser console after the fixes
 */

console.log('🔍 TESTING DROPDOWN SYNC FIXES');
console.log('===============================');

async function testDropdownSync() {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    issues: [],
    success: []
  };

  // Test 1: Check if dropdown shows real assistant names
  console.log('\n📋 Test 1: Checking Assistant Names in Dropdown');
  const dropdowns = document.querySelectorAll('.enhanced-assistant-dropdown');
  
  if (dropdowns.length > 0) {
    dropdowns.forEach((dropdown, index) => {
      const assistantNames = dropdown.querySelectorAll('.assistant-name, .dropdown-item');
      let mockCount = 0;
      let realCount = 0;
      
      assistantNames.forEach(nameEl => {
        const text = nameEl.textContent || '';
        if (text.includes('Mock Assistant') || text.includes('Error')) {
          mockCount++;
          results.issues.push(`❌ Found mock/error text: "${text}"`);
        } else if (text.trim() && !text.includes('Select') && !text.includes('Loading')) {
          realCount++;
          results.success.push(`✅ Found real assistant: "${text}"`);
        }
      });
      
      console.log(`Dropdown ${index}: ${realCount} real assistants, ${mockCount} mock/error entries`);
    });
  } else {
    results.issues.push('❌ No assistant dropdowns found');
  }

  // Test 2: Check localStorage for assistant data
  console.log('\n💾 Test 2: Checking Local Storage');
  try {
    const attorneyData = localStorage.getItem('attorney');
    if (attorneyData) {
      const attorney = JSON.parse(attorneyData);
      console.log('Attorney data:', {
        id: attorney.id,
        firm_name: attorney.firm_name,
        vapi_assistant_id: attorney.vapi_assistant_id,
        current_assistant_id: attorney.current_assistant_id
      });
      
      // Check for valid assistant IDs
      if (attorney.vapi_assistant_id) {
        if (attorney.vapi_assistant_id.length > 40) {
          results.issues.push(`❌ vapi_assistant_id looks like UUID: ${attorney.vapi_assistant_id}`);
        } else {
          results.success.push(`✅ Valid vapi_assistant_id format: ${attorney.vapi_assistant_id}`);
        }
      }
    } else {
      results.issues.push('❌ No attorney data in localStorage');
    }
  } catch (e) {
    results.issues.push('❌ Error parsing attorney data from localStorage');
  }

  // Test 3: Monitor network requests for 5 seconds
  console.log('\n🌐 Test 3: Monitoring Network Requests (5 seconds)');
  const originalFetch = window.fetch;
  const apiCalls = [];
  
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string') {
      if (url.includes('vapi') || url.includes('assistant')) {
        apiCalls.push({
          url: url,
          timestamp: new Date().toISOString(),
          type: url.includes('404') ? 'error' : 'success'
        });
        console.log('📡 API Call:', url);
      }
    }
    return originalFetch.apply(this, args);
  };

  // Test 4: Check console errors
  console.log('\n🚨 Test 4: Monitoring Console Errors (5 seconds)');
  const originalError = console.error;
  const errors = [];
  
  console.error = function(...args) {
    const errorMsg = args.join(' ');
    if (errorMsg.includes('404') || errorMsg.includes('Mock Assistant') || errorMsg.includes('assistant')) {
      errors.push({
        message: errorMsg.substring(0, 200),
        timestamp: new Date().toISOString()
      });
    }
    originalError.apply(console, args);
  };

  // Test 5: Check assistant switching functionality
  console.log('\n🔄 Test 5: Testing Assistant Switching');
  const assistantDropdownTriggers = document.querySelectorAll('.dropdown-trigger');
  
  if (assistantDropdownTriggers.length > 0) {
    console.log(`Found ${assistantDropdownTriggers.length} dropdown triggers`);
    results.success.push(`✅ Found ${assistantDropdownTriggers.length} dropdown triggers`);
    
    // Try to click the first dropdown to see if it opens
    try {
      const firstTrigger = assistantDropdownTriggers[0];
      firstTrigger.click();
      
      setTimeout(() => {
        const dropdownMenu = document.querySelector('.dropdown-menu');
        if (dropdownMenu && dropdownMenu.style.display !== 'none') {
          results.success.push('✅ Dropdown opens successfully');
          
          // Check dropdown options
          const options = dropdownMenu.querySelectorAll('.dropdown-item');
          console.log(`Found ${options.length} dropdown options`);
          
          options.forEach((option, i) => {
            const text = option.textContent || '';
            if (text.includes('Mock') || text.includes('Error')) {
              results.issues.push(`❌ Dropdown option ${i} has mock/error text: "${text}"`);
            } else if (text.trim()) {
              results.success.push(`✅ Valid dropdown option: "${text}"`);
            }
          });
          
          // Close dropdown
          firstTrigger.click();
        } else {
          results.issues.push('❌ Dropdown does not open');
        }
      }, 1000);
    } catch (clickError) {
      results.issues.push('❌ Error clicking dropdown trigger');
    }
  } else {
    results.issues.push('❌ No dropdown triggers found');
  }

  // Wait 5 seconds then generate report
  setTimeout(() => {
    // Restore original functions
    window.fetch = originalFetch;
    console.error = originalError;
    
    // Add API calls and errors to results
    results.tests.apiCalls = apiCalls;
    results.tests.errors = errors;
    
    // Generate final report
    console.log('\n📊 DROPDOWN SYNC TEST RESULTS');
    console.log('==============================');
    console.log(`✅ Successes: ${results.success.length}`);
    console.log(`❌ Issues: ${results.issues.length}`);
    console.log(`📡 API Calls: ${apiCalls.length}`);
    console.log(`🚨 Errors: ${errors.length}`);
    
    console.log('\n✅ SUCCESSES:');
    results.success.forEach(success => console.log(success));
    
    console.log('\n❌ ISSUES:');
    results.issues.forEach(issue => console.log(issue));
    
    console.log('\n📡 API CALLS:');
    apiCalls.forEach(call => console.log(`${call.timestamp}: ${call.url}`));
    
    console.log('\n🚨 ERRORS:');
    errors.forEach(error => console.log(`${error.timestamp}: ${error.message}`));
    
    // Overall assessment
    const overallSuccess = results.issues.length === 0 || results.success.length > results.issues.length;
    console.log(`\n🎯 OVERALL: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (overallSuccess) {
      console.log('🎉 Dropdown sync issues appear to be resolved!');
    } else {
      console.log('⚠️ Some dropdown sync issues remain. Check the issues above.');
    }
    
    // Store results globally
    window.dropdownSyncTestResults = results;
    console.log('\n💾 Results saved to window.dropdownSyncTestResults');
    
  }, 6000);

  return results;
}

// Auto-run test
testDropdownSync();

// Make available globally
window.testDropdownSync = testDropdownSync;
