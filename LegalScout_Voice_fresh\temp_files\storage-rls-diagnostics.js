/**
 * Storage RLS Diagnostics Script
 * 
 * Run this in the browser console to diagnose storage RLS issues
 */

window.storageRLSDiagnostics = {
  
  async runDiagnostics() {
    console.log('🔍 [Storage RLS Diagnostics] Starting storage RLS diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      issues: [],
      fixes: [],
      recommendations: []
    };
    
    try {
      // 1. Check authentication status
      console.log('🔐 [Diagnostics] Checking authentication status...');
      await this.checkAuthentication(results);
      
      // 2. Test storage policies
      console.log('📋 [Diagnostics] Testing storage policies...');
      await this.testStoragePolicies(results);
      
      // 3. Test file upload patterns
      console.log('📤 [Diagnostics] Testing file upload patterns...');
      await this.testUploadPatterns(results);
      
      // 4. Generate report
      this.generateReport(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ [Diagnostics] Error running diagnostics:', error);
      results.issues.push({
        type: 'diagnostic_error',
        message: error.message,
        severity: 'high'
      });
      return results;
    }
  },
  
  async checkAuthentication(results) {
    try {
      // Import Supabase client
      const { supabase } = await import('/src/lib/supabase.js');
      
      // Check current session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        results.issues.push({
          type: 'auth_error',
          message: `Authentication error: ${error.message}`,
          severity: 'high'
        });
        return;
      }
      
      if (!session?.user) {
        results.issues.push({
          type: 'no_session',
          message: 'No authenticated user session found',
          severity: 'high'
        });
        
        results.recommendations.push({
          type: 'auth_required',
          message: 'Please sign in to upload files. Storage RLS requires authentication.'
        });
      } else {
        results.fixes.push({
          type: 'auth_ok',
          message: `Authenticated as: ${session.user.email}`,
          data: {
            userId: session.user.id,
            email: session.user.email,
            role: session.user.role || 'authenticated'
          }
        });
        
        // Test the debug function
        try {
          const testFileName = '50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1234567890.webp';
          const { data, error: debugError } = await supabase.rpc('debug_storage_access', {
            file_path: testFileName
          });
          
          if (debugError) {
            results.issues.push({
              type: 'debug_function_error',
              message: `Debug function error: ${debugError.message}`,
              severity: 'medium'
            });
          } else if (data && data.length > 0) {
            const debugInfo = data[0];
            results.fixes.push({
              type: 'debug_info',
              message: 'Storage access debug info retrieved',
              data: debugInfo
            });
            
            if (!debugInfo.can_insert) {
              results.issues.push({
                type: 'no_insert_permission',
                message: 'User does not have permission to upload files',
                severity: 'high',
                data: debugInfo
              });
            }
          }
        } catch (debugError) {
          results.issues.push({
            type: 'debug_function_missing',
            message: 'Debug function not available',
            severity: 'low'
          });
        }
      }
      
    } catch (error) {
      results.issues.push({
        type: 'auth_check_error',
        message: error.message,
        severity: 'high'
      });
    }
  },
  
  async testStoragePolicies(results) {
    try {
      const { supabase } = await import('/src/lib/supabase.js');
      
      // Test if we can list files (should work with public policy)
      const { data, error } = await supabase.storage
        .from('legalscout_bucket1')
        .list('', { limit: 1 });
      
      if (error) {
        results.issues.push({
          type: 'storage_list_error',
          message: `Cannot list storage files: ${error.message}`,
          severity: 'medium'
        });
      } else {
        results.fixes.push({
          type: 'storage_list_ok',
          message: 'Storage bucket is accessible for listing',
          data: { fileCount: data?.length || 0 }
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'storage_test_error',
        message: error.message,
        severity: 'medium'
      });
    }
  },
  
  async testUploadPatterns(results) {
    const testPatterns = [
      '50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1234567890.webp',
      'logo_50e13a9e-22dd-4fe8-a03e-de627c5206c1_1234567890.webp',
      'assistant_image_1234567890.webp',
      'knowledge-50e13a9e-22dd-4fe8-a03e-de627c5206c1-test.pdf'
    ];
    
    for (const pattern of testPatterns) {
      // Test if pattern matches RLS policy regex
      const matches = {
        assistantFolder: /^[a-f0-9-]{36}\//.test(pattern),
        knowledgeFile: /knowledge-[a-f0-9-]{36}-/.test(pattern),
        voiceClone: /voice-clone-[a-f0-9-]{36}-/.test(pattern),
        logoFile: /logo_[0-9]+/.test(pattern),
        assistantImage: /assistant_image_[0-9]+/.test(pattern)
      };
      
      const shouldMatch = Object.values(matches).some(match => match);
      
      if (shouldMatch) {
        results.fixes.push({
          type: 'pattern_match',
          pattern,
          message: 'File pattern should be allowed by RLS policy',
          matches
        });
      } else {
        results.issues.push({
          type: 'pattern_no_match',
          pattern,
          message: 'File pattern does not match any RLS policy',
          severity: 'medium',
          matches
        });
      }
    }
  },
  
  generateReport(results) {
    console.log('\n📊 [Storage RLS Diagnostics] REPORT');
    console.log('='.repeat(60));
    
    console.log(`\n🕒 Timestamp: ${results.timestamp}`);
    
    if (results.issues.length > 0) {
      console.log(`\n❌ Issues Found (${results.issues.length}):`);
      results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.type}: ${issue.message}`);
        if (issue.data) {
          console.log(`     Data:`, issue.data);
        }
      });
    }
    
    if (results.fixes.length > 0) {
      console.log(`\n✅ Working Components (${results.fixes.length}):`);
      results.fixes.forEach((fix, index) => {
        console.log(`  ${index + 1}. ${fix.type}: ${fix.message}`);
        if (fix.data) {
          console.log(`     Data:`, fix.data);
        }
      });
    }
    
    if (results.recommendations.length > 0) {
      console.log(`\n💡 Recommendations (${results.recommendations.length}):`);
      results.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.type}: ${rec.message}`);
      });
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Summary and next steps
    const authIssues = results.issues.filter(i => i.type.includes('auth') || i.type.includes('session')).length;
    const permissionIssues = results.issues.filter(i => i.type.includes('permission')).length;
    
    if (authIssues > 0) {
      console.log('🚨 AUTHENTICATION ISSUES: User is not properly authenticated');
      console.log('💡 SOLUTION: Sign in with Google OAuth or check authentication flow');
    }
    
    if (permissionIssues > 0) {
      console.log('🔒 PERMISSION ISSUES: RLS policies are blocking uploads');
      console.log('💡 SOLUTION: Check storage policies and file naming patterns');
    }
    
    if (results.issues.length === 0) {
      console.log('🎉 SUCCESS: No RLS issues detected! Upload should work.');
    } else {
      console.log('\n🔧 NEXT STEPS:');
      console.log('1. Ensure user is signed in (check authentication)');
      console.log('2. Verify file naming patterns match RLS policies');
      console.log('3. Check that storage policies are properly configured');
      console.log('4. Test with a simple file upload');
    }
  }
};

// Auto-run diagnostics
console.log('🔧 [Storage RLS Diagnostics] Diagnostic tool loaded. Run window.storageRLSDiagnostics.runDiagnostics() to start.');
