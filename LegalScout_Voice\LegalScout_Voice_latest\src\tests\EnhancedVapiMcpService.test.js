/**
 * Tests for Enhanced Vapi MCP Service
 */

import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';

// Mock MCP client
jest.mock('@modelcontextprotocol/sdk/client/index.js', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    callTool: jest.fn().mockResolvedValue({ content: { id: 'test-assistant-id' } }),
    close: jest.fn().mockResolvedValue(true)
  }))
}));

// Mock SSE transport
jest.mock('@modelcontextprotocol/sdk/client/sse.js', () => ({
  SSEClientTransport: jest.fn().mockImplementation(() => ({}))
}));

// Mock fetch
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ id: 'test-assistant-id' })
  })
);

describe('EnhancedVapiMcpService', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Reset service state
    enhancedVapiMcpService.connected = false;
    enhancedVapiMcpService.useDirect = false;
    enhancedVapiMcpService.directApiKey = null;
    enhancedVapiMcpService.client = null;
  });
  
  describe('connect', () => {
    it('should connect with MCP', async () => {
      const result = await enhancedVapiMcpService.connect('test-api-key');
      
      expect(result).toBe(true);
      expect(enhancedVapiMcpService.connected).toBe(true);
      expect(enhancedVapiMcpService.useDirect).toBe(false);
    });
    
    it('should connect with direct API if forceDirect is true', async () => {
      const result = await enhancedVapiMcpService.connect('test-api-key', true);
      
      expect(result).toBe(true);
      expect(enhancedVapiMcpService.connected).toBe(true);
      expect(enhancedVapiMcpService.useDirect).toBe(true);
      expect(enhancedVapiMcpService.directApiKey).toBe('test-api-key');
    });
    
    it('should fall back to direct API if MCP connection fails', async () => {
      // Mock MCP client to throw an error
      require('@modelcontextprotocol/sdk/client/index.js').Client.mockImplementationOnce(() => ({
        connect: jest.fn().mockRejectedValue(new Error('Connection failed'))
      }));
      
      const result = await enhancedVapiMcpService.connect('test-api-key');
      
      expect(result).toBe(true);
      expect(enhancedVapiMcpService.connected).toBe(true);
      expect(enhancedVapiMcpService.useDirect).toBe(true);
      expect(enhancedVapiMcpService.directApiKey).toBe('test-api-key');
    });
    
    it('should throw an error if no API key is provided', async () => {
      await expect(enhancedVapiMcpService.connect()).rejects.toThrow('API key is required');
    });
  });
  
  describe('disconnect', () => {
    it('should disconnect from MCP', async () => {
      // Connect first
      await enhancedVapiMcpService.connect('test-api-key');
      
      // Then disconnect
      await enhancedVapiMcpService.disconnect();
      
      expect(enhancedVapiMcpService.connected).toBe(false);
      expect(enhancedVapiMcpService.client).toBe(null);
    });
    
    it('should reset direct API connection', async () => {
      // Connect with direct API
      await enhancedVapiMcpService.connect('test-api-key', true);
      
      // Then disconnect
      await enhancedVapiMcpService.disconnect();
      
      expect(enhancedVapiMcpService.connected).toBe(false);
      expect(enhancedVapiMcpService.useDirect).toBe(false);
    });
  });
  
  describe('getAssistant', () => {
    it('should get an assistant with MCP', async () => {
      // Connect first
      await enhancedVapiMcpService.connect('test-api-key');
      
      const result = await enhancedVapiMcpService.getAssistant('test-assistant-id');
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(enhancedVapiMcpService.client.callTool).toHaveBeenCalledWith({
        name: 'get_assistant_vapi-mcp-server',
        arguments: { assistantId: 'test-assistant-id' }
      });
    });
    
    it('should get an assistant with direct API', async () => {
      // Connect with direct API
      await enhancedVapiMcpService.connect('test-api-key', true);
      
      const result = await enhancedVapiMcpService.getAssistant('test-assistant-id');
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(fetch).toHaveBeenCalledWith('https://api.vapi.ai/assistants/test-assistant-id', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        }
      });
    });
    
    it('should throw an error if not connected', async () => {
      await expect(enhancedVapiMcpService.getAssistant('test-assistant-id')).rejects.toThrow('Not connected to Vapi');
    });
  });
  
  describe('listAssistants', () => {
    it('should list assistants with MCP', async () => {
      // Connect first
      await enhancedVapiMcpService.connect('test-api-key');
      
      const result = await enhancedVapiMcpService.listAssistants();
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(enhancedVapiMcpService.client.callTool).toHaveBeenCalledWith({
        name: 'list_assistants_vapi-mcp-server',
        arguments: {}
      });
    });
    
    it('should list assistants with direct API', async () => {
      // Connect with direct API
      await enhancedVapiMcpService.connect('test-api-key', true);
      
      const result = await enhancedVapiMcpService.listAssistants();
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(fetch).toHaveBeenCalledWith('https://api.vapi.ai/assistants', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        }
      });
    });
    
    it('should throw an error if not connected', async () => {
      await expect(enhancedVapiMcpService.listAssistants()).rejects.toThrow('Not connected to Vapi');
    });
  });
  
  describe('createAssistant', () => {
    it('should create an assistant with MCP', async () => {
      // Connect first
      await enhancedVapiMcpService.connect('test-api-key');
      
      const assistantConfig = {
        name: 'Test Assistant',
        firstMessage: 'Hello, how can I help you?',
        instructions: 'You are a helpful assistant',
        voice: {
          provider: 'playht',
          voiceId: 'ranger'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o'
        }
      };
      
      const result = await enhancedVapiMcpService.createAssistant(assistantConfig);
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(enhancedVapiMcpService.client.callTool).toHaveBeenCalledWith({
        name: 'create_assistant_vapi-mcp-server',
        arguments: assistantConfig
      });
    });
    
    it('should create an assistant with direct API', async () => {
      // Connect with direct API
      await enhancedVapiMcpService.connect('test-api-key', true);
      
      const assistantConfig = {
        name: 'Test Assistant',
        firstMessage: 'Hello, how can I help you?',
        instructions: 'You are a helpful assistant',
        voice: {
          provider: 'playht',
          voiceId: 'ranger'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o'
        }
      };
      
      const result = await enhancedVapiMcpService.createAssistant(assistantConfig);
      
      expect(result).toEqual({ id: 'test-assistant-id' });
      expect(fetch).toHaveBeenCalledWith('https://api.vapi.ai/assistants', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(assistantConfig)
      });
    });
    
    it('should throw an error if not connected', async () => {
      await expect(enhancedVapiMcpService.createAssistant({})).rejects.toThrow('Not connected to Vapi');
    });
  });
});
