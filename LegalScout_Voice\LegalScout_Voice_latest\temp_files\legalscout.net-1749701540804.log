 🚀 [PRODUCTION CSP] Environment: PRODUCTION
 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
 🚀 [PRODUCTION CSP] Final eval status: WORKING
 🔧 [ProductionEvalFix] Starting production eval fix...
 🔧 [ProductionEvalFix] Environment detected: Object
 🧪 [CSP TEST] Running CSP verification tests...
 🧪 [CSP TEST] ✅ Eval test PASSED: 4
 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
 🧪 [CSP TEST] All tests completed
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: Object
 🔧 [ProductionEvalFix] Initialization complete
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 🧪 [CSP TEST] ✅ setTimeout string test PASSED
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using default Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 🔧 [FinalLabelFix] Starting final label accessibility fix...
 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
 ✅ [FinalLabelFix] Final label fix initialized and monitoring
 🧪 [TestAssistantIDPropagation] Starting test...
 ✅ [TestAssistantIDPropagation] Test script loaded
 🧹 [VapiAssistantCleanup] Starting cleanup utility...
 ✅ [VapiAssistantCleanup] Cleanup utility loaded
 💡 Usage:
   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
 🗑️ [PURGE] Starting immediate assistant purge...
 🚀 STARTING PURGE NOW!
 ✅ [PURGE] Purge script loaded and will execute in 3 seconds...
 🛑 To cancel, run: clearTimeout() or refresh the page
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
fix-joespizza-assistant.js:1 Uncaught 
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 [DashboardIframeManager] Document body not ready, retrying in 100ms...
production-debug-vapi.js:82 Uncaught 
 [DashboardIframeManager] Iframe observer set up successfully
 ✅ [CleanAuthSolution] Auth state monitoring set up
 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: Object
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: Object
 🔧 [ProductionEvalFix] Initialization complete
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 Loaded contentScript
 🚀 Starting purge of 10 unused assistants...
 🛡️ Protected assistants will be skipped: Array(4)
 ⏳ [RobustStateHandler] Waiting for dependencies... Object
 [ProductionCorsFix] 🧪 Testing API connectivity...
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: Object
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: Array(1)
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
legalscout.net/:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
 🗑️ Deleting assistant: 840bfa3f-0b96-44d6-bb65-68c8836acce5
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5
 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
 🔄 Auto-running tests...
 🚀 Running all tests...
 🔍 Testing assistant ID propagation...
 ⚠️ Assistant dropdown not found
overrideMethod @ installHook.js:1
 🛡️ Testing CSP fix...
 ✅ CSP includes vercel.live domain
 🌐 Testing Vapi API endpoints...
 ⚠️ Vapi Direct API service not found
overrideMethod @ installHook.js:1
 ♿ Testing form accessibility...
 ✅ No problematic labels found
 📊 Test Results: Object
 🎯 Tests passed: 2/4
 ⚠️ Some tests failed. Check the logs above for details.
overrideMethod @ installHook.js:1
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5:1  Failed to load resource: the server responded with a status of 404 ()
 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ installHook.js:1
 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
 🗑️ Deleting assistant: b29f29c9-d3ff-409d-9385-55dd16a1f744
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744
api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744:1  Failed to load resource: the server responded with a status of 404 ()
 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ installHook.js:1
 🗑️ Deleting assistant: 246eca2b-f523-4bbe-ac77-066f4eb3616b
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b
 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b:1  Failed to load resource: the server responded with a status of 404 ()
 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ installHook.js:1
 🗑️ Deleting assistant: 459c8339-2b2b-4d9f-aff9-73b147ebcafa
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa
api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa:1  Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:35 🗑️ Deleting assistant: 80a0b06c-394b-40aa-987b-35ab17123efc
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc
api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc:1  Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:35 🗑️ Deleting assistant: a4e45c56-5f29-4171-8917-1a5fbb36dc8b
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b
api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:35 🗑️ Deleting assistant: f3298201-08e9-4d96-83e3-0b78d7460ea8
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8
api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:35 🗑️ Deleting assistant: 39515477-15a9-4b75-81d5-095bf3bb5691
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691
api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
purge-assistants.js:35 🗑️ Deleting assistant: 38719ef3-28af-49ab-bcaa-2fbc436c78d8
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8
hook.js:608 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:35 🗑️ Deleting assistant: 6e311928-9842-438f-b933-3817a0e63b5d
production-cors-fix.js:104 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d
api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
hook.js:608 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
purge-assistants.js:89 
📊 PURGE COMPLETE!
purge-assistants.js:90 ==================
purge-assistants.js:91 ✅ Successfully deleted: 0
purge-assistants.js:92 ❌ Failed to delete: 10
purge-assistants.js:93 🛡️ Skipped (protected): 0
purge-assistants.js:103 
❌ Failed deletions:
purge-assistants.js:105   1. 840bfa3f-0b96-44d6-bb65-68c8836acce5 - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   2. b29f29c9-d3ff-409d-9385-55dd16a1f744 - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   3. 246eca2b-f523-4bbe-ac77-066f4eb3616b - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   4. 459c8339-2b2b-4d9f-aff9-73b147ebcafa - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   5. 80a0b06c-394b-40aa-987b-35ab17123efc - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   6. a4e45c56-5f29-4171-8917-1a5fbb36dc8b - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   7. f3298201-08e9-4d96-83e3-0b78d7460ea8 - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   8. 39515477-15a9-4b75-81d5-095bf3bb5691 - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   9. 38719ef3-28af-49ab-bcaa-2fbc436c78d8 - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:105   10. 6e311928-9842-438f-b933-3817a0e63b5d - 404 - {"message":"Not Found","statusCode":404}
purge-assistants.js:121 
🎉 Purge operation completed!
purge-assistants.js:122 💡 Results stored in window.purgeResults
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
ActiveCheckHelper.ts:21 received intentional event
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
ActiveCheckHelper.ts:8 updating page active status
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
