/* Call Transition Animations */

/* Overlay that covers the entire screen during transition */
.call-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 9000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

.call-transition-overlay.active {
  opacity: 1;
  pointer-events: all;
}

/* Removed button clone styles */

/* Expanding circles animation */
.expanding-circle {
  position: absolute;
  border-radius: 50%;
  border: 2px solid rgba(41, 121, 255, 0.3);
  opacity: 0;
  pointer-events: none;
}

@keyframes expandCircle {
  0% {
    width: 200px;
    height: 200px;
    opacity: 0.7;
    border-width: 2px;
  }
  100% {
    width: 300vw;
    height: 300vw;
    opacity: 0;
    border-width: 1px;
  }
}

.expanding-circle.active {
  animation: expandCircle 2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

/* Particles animation */
.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: rgba(41, 121, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
}

@keyframes particleMove {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
  }
}

/* Connection text animation */
.connection-text {
  font-size: 24px;
  font-weight: 500;
  color: white;
  margin-top: 40px;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.connection-text.active {
  animation: textFadeIn 0.8s ease forwards;
  animation-delay: 0.5s;
}

/* Inline loading dots animation */
.loading-dots-inline {
  display: inline-block;
  opacity: 0;
  margin-left: 2px;
}

.loading-dots-inline.active {
  opacity: 1;
  transition: opacity 0.5s ease;
}

.loading-dots-inline .dot {
  display: inline-block;
  animation: dotFade 1.4s infinite;
  font-weight: bold;
  color: #2e7bf3;
}

.loading-dots-inline .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots-inline .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotFade {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
    transform: translateY(-2px);
  }
}

/* Neural network visualization */
.neural-network {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease;
  z-index: 9001; /* Ensure it's above the background */
  pointer-events: none;
}

.neural-network.active {
  opacity: 1;
  transition-delay: 0.5s;
}

.node {
  position: absolute;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  filter: blur(1px); /* Slight blur for softer appearance */
  z-index: 9002;
  box-shadow: 0 0 15px rgba(41, 121, 255, 0.8);
}

.connection-line {
  position: absolute;
  transform-origin: left center;
  z-index: 9001;
  filter: blur(0.5px); /* Slight blur for softer lines */
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
    filter: blur(2px);
  }
}

.pulse {
  animation: pulse 2s infinite ease-in-out;
}

/* Status indicators */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  opacity: 0;
}

.status-indicator.active {
  opacity: 1;
  transition: opacity 0.5s ease;
  transition-delay: 1.2s;
}

.status-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #4CAF50;
  position: relative;
}

.status-icon::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 2px solid #4CAF50;
  opacity: 0;
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.status-text {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

/* Call interface fade-in */
.call-card-container {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.call-card-container.active {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  @keyframes expandCircle {
    0% {
      width: 150px;
      height: 150px;
      opacity: 0.7;
    }
    100% {
      width: 300vw;
      height: 300vw;
      opacity: 0;
    }
  }

  .connection-text {
    font-size: 20px;
  }
}
