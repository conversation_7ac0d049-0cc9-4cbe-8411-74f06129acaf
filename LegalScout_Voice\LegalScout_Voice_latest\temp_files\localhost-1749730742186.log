home:24 🚀 [PRODUCTION CSP] Environment: DEVELOPMENT
home:29 🚀 [PRODUCTION CSP] ✅ EVAL WORKS! Result: 4
home:58 🚀 [PRODUCTION CSP] Final eval status: WORKING
 🔧 [ProductionEvalFix] Starting production eval fix...
 🔧 [ProductionEvalFix] Environment detected: {isProduction: false, isVercel: false, hostname: 'localhost'}
 🧪 [CSP TEST] Running CSP verification tests...
 🧪 [CSP TEST] ✅ Eval test PASSED: 4
 🧪 [CSP TEST] ✅ Function constructor test PASSED: 6
 🧪 [CSP TEST] All tests completed
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using default Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
final-label-fix.js:9 🔧 [FinalLabelFix] Starting final label accessibility fix...
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:183 ✅ [FinalLabelFix] Final label fix initialized and monitoring
production-eval-fix.js:163 🔧 [ProductionEvalFix] Initializing...
production-eval-fix.js:66 🔧 [ProductionEvalFix] CSP Policy check: {hasCSP: true, hasUnsafeEval: true, policyLength: 1159}
production-eval-fix.js:34 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
production-eval-fix.js:47 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
production-eval-fix.js:202 🔧 [ProductionEvalFix] Status: {evalWorks: true, functionConstructorWorks: true, cspInfo: {…}, isProduction: false, isVercel: false, …}
production-eval-fix.js:203 🔧 [ProductionEvalFix] Initialization complete
test-assistant-id-propagation.js:5 🧪 [TestAssistantIDPropagation] Starting test...
test-assistant-id-propagation.js:161 ✅ [TestAssistantIDPropagation] Test script loaded
vapi-assistant-cleanup.js:5 🧹 [VapiAssistantCleanup] Starting cleanup utility...
vapi-assistant-cleanup.js:280 ✅ [VapiAssistantCleanup] Cleanup utility loaded
vapi-assistant-cleanup.js:281 💡 Usage:
vapi-assistant-cleanup.js:282   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
vapi-assistant-cleanup.js:283   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
vapi-assistant-cleanup.js:284   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
call-sync-diagnostic.js:272 🚀 Call Sync Diagnostic loaded. Run window.diagnoseCallSync() to start.
home:330 Supabase loaded from CDN
home:340 Creating Supabase client from CDN
home:344 Supabase client created from CDN
VM1317:1 🧪 [CSP TEST] ✅ setTimeout string test PASSED
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 [Supabase] Created clean fetch from iframe to bypass interceptors
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development/production or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5174'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 🔧 [ProductionEvalFix] Initializing...
 🔧 [ProductionEvalFix] CSP Policy check: {hasCSP: true, hasUnsafeEval: true, policyLength: 1159}
 🔧 [ProductionEvalFix] ✅ Eval test passed: 2
 🔧 [ProductionEvalFix] ✅ Function constructor test passed: 4
 🔧 [ProductionEvalFix] Status: {evalWorks: true, functionConstructorWorks: true, cspInfo: {…}, isProduction: false, isVercel: false, …}
 🔧 [ProductionEvalFix] Initialization complete
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: true, firmName: undefined, firm_name: undefined, …}
 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: true, firmName: undefined, firm_name: undefined, …}
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] No attorney found anywhere
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] No attorney found anywhere
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
robust-state-handler.js:46 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:83 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:721 ✅ [RobustStateHandler] Robust state handling initialized
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabase.js:147 Supabase connection test successful!
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (6) ['attorney-08532', 'coscettelaw', 'damon', 'generalcounselonline', 'scout', 'thekosts']
App.jsx:125 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
App.jsx:125 🔍 [App.jsx] Condition check: {isAttorneySubdomain: false, hasAttorneyProfile: false, isLoading: false, firmName: undefined, firm_name: undefined, …}
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [ProductionCorsFix] 🧪 Testing API connectivity...
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
production-cors-fix.js:224 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
test-assistant-id-propagation.js:157 🔄 Auto-running tests...
test-assistant-id-propagation.js:122 🚀 Running all tests...
test-assistant-id-propagation.js:9 🔍 Testing assistant ID propagation...
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:71 🛡️ Testing CSP fix...
test-assistant-id-propagation.js:81 ✅ CSP includes vercel.live domain
test-assistant-id-propagation.js:91 🌐 Testing Vapi API endpoints...
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:105 ♿ Testing form accessibility...
test-assistant-id-propagation.js:115 ✅ No problematic labels found
test-assistant-id-propagation.js:131 📊 Test Results: {assistantPropagation: false, cspFix: true, vapiEndpoints: false, formAccessibility: true}
test-assistant-id-propagation.js:136 🎯 Tests passed: 2/4
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
