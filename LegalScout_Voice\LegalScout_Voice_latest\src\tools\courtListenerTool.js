/**
 * CourtListener Citation Lookup Tool
 * 
 * This tool provides integration with the CourtListener Citation Lookup API,
 * allowing verification and lookup of legal citations in text.
 * 
 * API Documentation: https://www.courtlistener.com/help/api/rest/v3/citation-lookup/
 */

const axios = require('axios');

/**
 * Lookup and verify legal citations in text
 * 
 * @param {string} text - The text containing legal citations to verify (up to 64,000 characters)
 * @param {string} apiKey - Optional CourtListener API key for authenticated requests
 * @returns {Promise<object>} - The citation lookup results
 */
async function lookupCitations(text, apiKey = null) {
  try {
    const url = 'https://www.courtlistener.com/api/rest/v3/citation-lookup/';
    
    // Prepare headers
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    
    // Add authorization header if API key is provided
    if (apiKey) {
      headers['Authorization'] = `Token ${apiKey}`;
    }
    
    // Make the API request
    const response = await axios.post(
      url,
      new URLSearchParams({ text }).toString(),
      { headers }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error looking up citations:', error);
    
    // Return a structured error response
    return {
      error: true,
      message: error.message,
      status: error.response?.status || 500,
      details: error.response?.data || 'Unknown error occurred'
    };
  }
}

/**
 * Format citation lookup results for display
 * 
 * @param {object} results - The citation lookup results from the API
 * @returns {string} - Formatted results as a string
 */
function formatCitationResults(results) {
  if (results.error) {
    return `Error: ${results.message}`;
  }
  
  if (!Array.isArray(results) || results.length === 0) {
    return 'No citations found in the provided text.';
  }
  
  let formattedResults = 'Citation Verification Results:\n\n';
  
  results.forEach((citation, index) => {
    formattedResults += `Citation ${index + 1}: "${citation.citation}"\n`;
    formattedResults += `Normalized: ${citation.normalized_citations?.join(', ') || 'N/A'}\n`;
    formattedResults += `Status: ${citation.status === 200 ? 'Valid ✓' : 'Invalid ✗'}\n`;
    
    if (citation.status !== 200) {
      formattedResults += `Error: ${citation.error_message || 'Unknown error'}\n`;
    } else if (citation.clusters && citation.clusters.length > 0) {
      const cluster = citation.clusters[0];
      formattedResults += `Case: ${cluster.case_name || 'Unknown'}\n`;
      formattedResults += `Court: ${cluster.court_name || 'Unknown'}\n`;
      formattedResults += `Date: ${cluster.date_filed || 'Unknown'}\n`;
      
      if (cluster.absolute_url) {
        formattedResults += `Link: https://www.courtlistener.com${cluster.absolute_url}\n`;
      }
    }
    
    formattedResults += '\n';
  });
  
  return formattedResults;
}

module.exports = {
  lookupCitations,
  formatCitationResults
};
