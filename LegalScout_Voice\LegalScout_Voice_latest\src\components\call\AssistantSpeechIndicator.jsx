import React from "react";

const AssistantSpeechIndicator = ({ isSpeaking }) => {
  return (
    <div style={{ display: "flex", alignItems: "center", marginBottom: "10px" }}> 
      <p style={{ 
        color: isSpeaking ? "var(--color-success)" : "var(--text-primary)",
        margin: 0,
        fontWeight: 600,
        fontSize: "1.2rem"
      }}>
        {isSpeaking ? "Barking" : "Listening"} 
      </p>
    </div>
  );
};

export default AssistantSpeechIndicator;
