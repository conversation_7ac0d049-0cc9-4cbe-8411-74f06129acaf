import React from 'react';

class ProductionErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };

    // Attempt to fix Framer Motion issues
    this.fixFramerMotionIssues();
  }

  fixFramerMotionIssues() {
    try {
      // Check if we're in the browser environment
      if (typeof window !== 'undefined') {
        // Create a global React object if it doesn't exist
        if (typeof window.React === 'undefined') {
          console.log('[ErrorBoundary] Creating React placeholder');
          window.React = {};
        }

        // Ensure createContext exists
        if (typeof window.React.createContext === 'undefined') {
          console.log('[ErrorBoundary] Adding createContext placeholder');
          window.React.createContext = function() {
            return {
              Provider: function() { return null; },
              Consumer: function() { return null; }
            };
          };
        }

        // Add other essential React methods
        const reactMethods = [
          'useState', 'useEffect', 'useLayoutEffect', 'useRef',
          'useCallback', 'useMemo', 'useContext', 'forwardRef',
          'createElement', 'cloneElement', 'createRef', 'Component',
          'PureComponent', 'Fragment', 'Children', 'isValidElement'
        ];

        reactMethods.forEach(method => {
          if (typeof window.React[method] === 'undefined') {
            console.log(`[ErrorBoundary] Adding ${method} placeholder`);
            window.React[method] = function() {
              return arguments[0] instanceof Function ? arguments[0]() : null;
            };
          }
        });

        console.log('[ErrorBoundary] React polyfills applied');
      }
    } catch (error) {
      console.error('[ErrorBoundary] Error applying React polyfills:', error);
    }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to the console
    console.error('ProductionErrorBoundary caught an error:', error, errorInfo);
    this.setState({ errorInfo });

    // Try to fix Framer Motion issues again
    if (error.message && (error.message.includes('createContext') || error.message.includes('framer-motion'))) {
      console.log('[ErrorBoundary] Detected Framer Motion error, attempting to fix...');
      this.fixFramerMotionIssues();

      // Disable Framer Motion modules
      if (typeof window !== 'undefined') {
        // Create a mock implementation of Framer Motion
        const mockFramerMotion = {
          motion: {
            div: 'div',
            span: 'span',
            button: 'button',
            a: 'a',
            ul: 'ul',
            li: 'li',
            p: 'p',
            h1: 'h1',
            h2: 'h2',
            h3: 'h3',
            img: 'img',
            section: 'section'
          },
          AnimatePresence: function(props) {
            return props.children || null;
          }
        };

        // Try to patch the module system
        try {
          // For ESM
          if (window.__vite_plugin_react_preamble_installed__) {
            console.log('[ErrorBoundary] Patching ESM imports for Framer Motion');
            const originalImport = window.import;
            window.import = function(specifier) {
              if (specifier === 'framer-motion' || specifier.includes('framer-motion/')) {
                console.log('[ErrorBoundary] Intercepted Framer Motion import:', specifier);
                return Promise.resolve(mockFramerMotion);
              }
              return originalImport.apply(this, arguments);
            };
          }

          console.log('[ErrorBoundary] Framer Motion disabled');
        } catch (patchError) {
          console.error('[ErrorBoundary] Error patching module system:', patchError);
        }
      }
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          padding: '20px',
          backgroundColor: '#f8f9fa',
          color: '#333',
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif'
        }}>
          <h1 style={{ color: '#d9534f', marginBottom: '20px' }}>Something went wrong</h1>
          <p style={{ maxWidth: '600px', marginBottom: '20px' }}>
            We're sorry, but there was an error loading the application. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              backgroundColor: '#4B74AA',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Refresh Page
          </button>

          {/* Show error details in development */}
          {process.env.NODE_ENV === 'development' && (
            <div style={{
              marginTop: '30px',
              textAlign: 'left',
              backgroundColor: '#f1f1f1',
              padding: '15px',
              borderRadius: '4px',
              maxWidth: '800px',
              overflow: 'auto'
            }}>
              <h3>Error Details:</h3>
              <p style={{ color: '#d9534f', fontFamily: 'monospace' }}>
                {this.state.error && this.state.error.toString()}
              </p>
              <h3>Component Stack:</h3>
              <pre style={{
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace',
                fontSize: '12px'
              }}>
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </pre>
            </div>
          )}
        </div>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

export default ProductionErrorBoundary;
