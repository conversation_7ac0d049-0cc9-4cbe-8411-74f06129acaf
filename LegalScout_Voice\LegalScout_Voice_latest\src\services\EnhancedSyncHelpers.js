/**
 * Enhanced Synchronization Helper Functions
 *
 * This file contains enhanced helper functions for synchronizing data between Supabase and Vapi.
 * It implements the one-way sync pattern (UI → Supabase → Vapi) with Supabase as the primary source of truth.
 */

import { supabase } from '../lib/supabase-fixed';
import { enhancedVapiMcpService } from './EnhancedVapiMcpService';

// Field mapping between Supabase and Vapi
const FIELD_MAPPING = {
  // Supabase field → Vapi field
  welcome_message: 'firstMessage',
  vapi_instructions: 'instructions',
  voice_id: 'voice.voiceId',
  voice_provider: 'voice.provider',
  ai_model: 'llm.model'
};



// Note: Using shared Supabase client from lib/supabase.js to avoid multiple instances

/**
 * Validate attorney ID format
 * @param {string} attorneyId - The ID to validate
 * @returns {boolean} Whether the ID is valid
 */
const isValidAttorneyId = (attorneyId) => {
  if (!attorneyId) return false;

  // Allow development IDs in development mode
  if (import.meta.env.DEV || import.meta.env.MODE === 'development') {
    if (typeof attorneyId === 'string' && attorneyId.startsWith('dev-')) {
      return true;
    }
  }

  // Regular UUID validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(attorneyId);
};

/**
 * Fetch attorney data from Supabase
 * @param {string} attorneyId - The ID of the attorney to fetch
 * @returns {Promise<Object>} - The attorney data
 */
export const fetchFromSupabase = async (attorneyId) => {
  // Validate attorney ID format
  if (!isValidAttorneyId(attorneyId)) {
    console.warn(`[EnhancedSyncHelpers] Invalid attorney ID: ${attorneyId}`);
    return null;
  }

  const { data, error } = await supabase
    .from('attorneys')
    .select('*')
    .eq('id', attorneyId)
    .single();

  if (error) throw error;
  return data;
};

/**
 * Update attorney data in Supabase
 * @param {string} attorneyId - The ID of the attorney to update
 * @param {Object} data - The data to update
 * @returns {Promise<Object>} - The updated attorney data
 */
export const updateSupabaseAttorney = async (attorneyId, data) => {
  // Add updated_at timestamp
  const dataWithTimestamp = {
    ...data,
    updated_at: new Date().toISOString()
  };

  const { data: updatedData, error } = await supabase
    .from('attorneys')
    .update(dataWithTimestamp)
    .eq('id', attorneyId)
    .select()
    .single();

  if (error) throw error;
  return updatedData;
};

/**
 * Get attorney by email
 * @param {string} email - The email of the attorney to fetch
 * @returns {Promise<Object|null>} - The attorney data or null if not found
 */
export const getAttorneyByEmail = async (email) => {
  // Handle duplicates by getting the most recent attorney
  const { data: attorneys, error } = await supabase
    .from('attorneys')
    .select('*')
    .eq('email', email)
    .order('updated_at', { ascending: false });

  if (error) throw error;

  if (attorneys && attorneys.length > 0) {
    if (attorneys.length > 1) {
      console.warn(`Found ${attorneys.length} attorneys for email ${email}, using the most recent one`);
    }
    return attorneys[0];
  }

  return null;
};

/**
 * Get attorney by auth ID
 * @param {string} authId - The auth ID of the attorney to fetch
 * @returns {Promise<Object|null>} - The attorney data or null if not found
 */
export const getAttorneyByAuthId = async (authId) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Fetching attorney by user_id: ${authId}`);
    }

    // Validate auth ID format
    if (!isValidAttorneyId(authId)) {
      console.warn(`Invalid auth ID format: ${authId}`);
      return null;
    }

    // Use user_id column instead of auth_id (which doesn't exist)
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('user_id', authId)
      .single();

    if (error) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Error response from Supabase:`, error);
      }

      // Only throw if it's not a "no rows returned" error
      if (error.code !== 'PGRST116') throw error;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`Attorney data from Supabase:`, data);
    }
    return data || null;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching attorney by auth ID:', error);
    }
    throw error;
  }
};

/**
 * Get attorney by ID
 * @param {string} id - The ID of the attorney to fetch
 * @returns {Promise<Object|null>} - The attorney data or null if not found
 */
export const getAttorneyById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data || null;
  } catch (error) {
    console.error('Error fetching attorney by ID:', error);
    throw error;
  }
};

/**
 * Create a new attorney in Supabase
 * @param {Object} data - The attorney data to create
 * @returns {Promise<Object>} - The created attorney data
 */
export const createAttorney = async (data) => {
  const { data: newAttorney, error } = await supabase
    .from('attorneys')
    .insert([data])
    .select()
    .single();

  if (error) throw error;
  return newAttorney;
};

/**
 * Update attorney data in Supabase
 * @param {string} id - The ID of the attorney to update
 * @param {Object} data - The data to update
 * @returns {Promise<Object>} - The updated attorney data
 */
export const updateAttorney = async (id, data) => {
  // Add updated_at timestamp
  const dataWithTimestamp = {
    ...data,
    updated_at: new Date().toISOString()
  };

  const { data: updatedAttorney, error } = await supabase
    .from('attorneys')
    .update(dataWithTimestamp)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return updatedAttorney;
};

/**
 * Fetch assistant data from Vapi
 * @param {string} assistantId - The ID of the assistant to fetch
 * @returns {Promise<Object>} - The assistant data
 */
export const fetchFromVapi = async (assistantId) => {
  // Ensure connection to Vapi
  if (!enhancedVapiMcpService.connected) {
    const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                  (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

    if (!apiKey) {
      throw new Error('No Vapi API key available');
    }

    await enhancedVapiMcpService.connect(apiKey);
  }

  return await enhancedVapiMcpService.getAssistant(assistantId);
};

/**
 * Create a new assistant in Vapi
 * @param {Object} data - The assistant data to create
 * @returns {Promise<Object>} - The created assistant data
 */
export const createVapiAssistant = async (data) => {
  // CRITICAL: Add global rate limiting to prevent mass assistant creation
  const globalRateLimitKey = 'global_assistant_creation_limit';
  const lastGlobalCreation = localStorage.getItem(globalRateLimitKey);
  const now = Date.now();
  const GLOBAL_RATE_LIMIT_MS = 30000; // 30 seconds between ANY assistant creation

  if (lastGlobalCreation && (now - parseInt(lastGlobalCreation)) < GLOBAL_RATE_LIMIT_MS) {
    console.warn('[EnhancedSyncHelpers] Global rate limit hit - assistant creation blocked');
    throw new Error('Assistant creation rate limited globally - please wait 30 seconds');
  }

  // Set global rate limit
  localStorage.setItem(globalRateLimitKey, now.toString());

  // Log assistant creation attempt with more context
  console.log('[EnhancedSyncHelpers] Creating Vapi assistant:', {
    name: data.name,
    hasInstructions: !!data.instructions,
    voiceProvider: data.voice?.provider,
    voiceId: data.voice?.voiceId,
    timestamp: new Date().toISOString(),
    url: window.location.href
  });

  // Ensure connection to Vapi using PRIVATE key for assistant management
  if (!enhancedVapiMcpService.connected) {
    // Use private key for assistant management operations
    const privateKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                       (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY);

    if (!privateKey) {
      console.warn('No Vapi private key available, falling back to public key');
      // Fallback to public key if private key not available
      const publicKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                        (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

      if (!publicKey) {
        throw new Error('No Vapi API key available');
      }

      await enhancedVapiMcpService.connect(publicKey);
    } else {
      console.log('🔑 [EnhancedSyncHelpers] Using private key for assistant management');
      await enhancedVapiMcpService.connect(privateKey);
    }
  }

  // Create assistant configuration using correct Vapi API format (2024 schema)
  const assistantConfig = {
    name: data.name,
    firstMessage: data.firstMessage,
    firstMessageMode: "assistant-speaks-first",
    model: {
      provider: "openai",
      model: data.ai_model || "gpt-4o",
      messages: [
        {
          role: "system",
          content: data.instructions || "You are a helpful assistant."
        }
      ]
    },
    voice: data.voice,
    transcriber: {
      provider: "deepgram",
      model: "nova-3"
    },
    // Web call configuration
    backgroundSound: "off",
    backgroundDenoisingEnabled: false,
    silenceTimeoutSeconds: 30,
    maxDurationSeconds: 600,
    // Client messages for real-time streaming
    clientMessages: [
      "conversation-update",
      "function-call",
      "hang",
      "model-output",
      "speech-update",
      "status-update",
      "transcript",
      "tool-calls",
      "user-interrupted",
      "voice-input"
    ]
  };

  return await enhancedVapiMcpService.createAssistant(assistantConfig);
};

/**
 * Update an existing assistant in Vapi
 * @param {string} assistantId - The ID of the assistant to update
 * @param {Object} data - The data to update
 * @returns {Promise<Object>} - The updated assistant data
 */
export const updateVapiAssistant = async (assistantId, data) => {
  // Ensure connection to Vapi using PRIVATE key for assistant management
  if (!enhancedVapiMcpService.connected) {
    // Use private key for assistant management operations
    const privateKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                       (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY);

    if (!privateKey) {
      console.warn('No Vapi private key available, falling back to public key');
      // Fallback to public key if private key not available
      const publicKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                        (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

      if (!publicKey) {
        throw new Error('No Vapi API key available');
      }

      await enhancedVapiMcpService.connect(publicKey);
    } else {
      console.log('🔑 [EnhancedSyncHelpers] Using private key for assistant management');
      await enhancedVapiMcpService.connect(privateKey);
    }
  }

  // Create assistant configuration using correct Vapi API format (2024 schema)
  const assistantConfig = {
    name: data.name,
    firstMessage: data.firstMessage,
    firstMessageMode: "assistant-speaks-first",
    model: {
      provider: "openai",
      model: data.model?.model || data.llm?.model || data.ai_model || "gpt-4o",
      messages: [
        {
          role: "system",
          content: data.instructions || "You are a helpful assistant."
        }
      ]
    },
    voice: data.voice,
    transcriber: {
      provider: "deepgram",
      model: "nova-3"
    },
    // Web call configuration
    backgroundSound: "off",
    backgroundDenoisingEnabled: false,
    silenceTimeoutSeconds: 30,
    maxDurationSeconds: 600,
    // Client messages for real-time streaming
    clientMessages: [
      "conversation-update",
      "function-call",
      "hang",
      "model-output",
      "speech-update",
      "status-update",
      "transcript",
      "tool-calls",
      "user-interrupted",
      "voice-input"
    ]
  };

  console.log('🔧 [EnhancedSyncHelpers] Updating assistant with instructions:', {
    assistantId: assistantId.substring(0, 8) + '...',
    instructionsLength: assistantConfig.model.messages[0].content.length,
    instructionsPreview: assistantConfig.model.messages[0].content.substring(0, 100) + '...'
  });

  return await enhancedVapiMcpService.updateAssistant(assistantId, assistantConfig);
};

/**
 * Get an assistant from Vapi
 * @param {string} assistantId - The ID of the assistant to get
 * @returns {Promise<Object>} - The assistant data
 */
export const getVapiAssistant = async (assistantId) => {
  return await fetchFromVapi(assistantId);
};

/**
 * Find discrepancies between Supabase and Vapi data
 * @param {Object} supabaseData - The attorney data from Supabase
 * @param {Object} vapiData - The assistant data from Vapi
 * @returns {Object} - The discrepancies found
 */
export const findProfileDiscrepancies = (supabaseData, vapiData) => {
  if (!supabaseData || !vapiData) {
    return {};
  }

  const discrepancies = {};

  // Check for discrepancies in mapped fields
  if (supabaseData.firm_name !== vapiData.name) {
    discrepancies.firm_name = {
      supabase: supabaseData.firm_name,
      vapi: vapiData.name
    };
  }

  if (supabaseData.welcome_message !== vapiData.firstMessage) {
    discrepancies.welcome_message = {
      supabase: supabaseData.welcome_message,
      vapi: vapiData.firstMessage
    };
  }

  if (supabaseData.vapi_instructions !== vapiData.instructions) {
    discrepancies.vapi_instructions = {
      supabase: supabaseData.vapi_instructions,
      vapi: vapiData.instructions
    };
  }

  // Check voice configuration
  if (supabaseData.voice_provider !== vapiData.voice?.provider ||
      supabaseData.voice_id !== vapiData.voice?.voiceId) {
    discrepancies.voice = {
      supabase: {
        provider: supabaseData.voice_provider,
        voiceId: supabaseData.voice_id
      },
      vapi: vapiData.voice
    };
  }

  // Check AI model
  if (supabaseData.ai_model && supabaseData.ai_model !== vapiData.llm?.model) {
    discrepancies.ai_model = {
      supabase: supabaseData.ai_model,
      vapi: vapiData.llm?.model
    };
  }

  return discrepancies;
};

/**
 * Get valid voices for a provider
 * @param {string} provider - The voice provider
 * @returns {Promise<Array>} - Array of valid voice IDs
 */
export const getValidVoicesForProvider = async (provider) => {
  // This would ideally come from an API or configuration
  // For now, we'll use a static mapping
  const voicesByProvider = {
    "11labs": ["sarah", "adam", "daniel", "josh", "rachel", "domi", "freya", "antoni", "thomas", "charlie", "emily", "elli", "callum", "patrick", "harry", "liam", "dorothy", "josh", "arnold", "charlotte", "matilda", "matthew", "james", "joseph", "jeremy", "michael", "ethan"],
    "playht": ["ranger", "waylon", "leyro", "nova", "stella", "cody", "maya", "ryan", "tyler", "luke", "jackson", "hudson", "brett", "theo", "ruby", "daisy", "olivia", "lily", "emma", "sophia", "ava", "mia", "isabella", "charlotte", "amelia", "harper", "evelyn"],
    "deepgram": ["nova-1", "nova-2", "nova-3", "aura-1", "aura-2", "stella-1", "stella-2", "luna-1", "luna-2"],
    "openai": ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
  };

  return voicesByProvider[provider] || [];
};

/**
 * Validate and fix voice configuration
 * @param {string} voiceId - The voice ID
 * @param {string} voiceProvider - The voice provider
 * @returns {Object} - The corrected voice configuration
 */
const validateVoiceConfiguration = (voiceId, voiceProvider) => {
  // Define valid voice/provider combinations
  const validVoices = {
    '11labs': ['sarah', 'adam', 'daniel', 'josh', 'rachel', 'domi', 'freya', 'antoni', 'thomas', 'charlie', 'emily', 'elli', 'callum', 'patrick', 'harry', 'liam', 'dorothy', 'arnold', 'charlotte', 'matilda', 'matthew', 'james', 'joseph', 'jeremy', 'michael', 'ethan'],
    'playht': ['waylon', 'leyro', 'nova-playht', 'stella', 'cody', 'maya', 'ryan', 'tyler', 'luke', 'jackson', 'hudson', 'brett', 'theo', 'ruby', 'daisy', 'olivia', 'lily', 'emma', 'sophia', 'ava', 'mia', 'isabella', 'charlotte-playht', 'amelia', 'harper', 'evelyn'],
    'openai': ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'],
    'vapi': ['jennifer', 'mark']
  };

  // Check if voice/provider combination is valid
  let finalVoiceId = voiceId;
  let finalVoiceProvider = voiceProvider;

  if (!validVoices[voiceProvider] || !validVoices[voiceProvider].includes(voiceId)) {
    console.log(`[EnhancedSyncHelpers] Invalid voice/provider combination: ${voiceId}/${voiceProvider}`);

    // Find the correct provider for this voice
    let foundProvider = null;
    for (const [provider, voices] of Object.entries(validVoices)) {
      if (voices.includes(voiceId)) {
        foundProvider = provider;
        break;
      }
    }

    if (foundProvider) {
      finalVoiceProvider = foundProvider;
      console.log(`[EnhancedSyncHelpers] Fixed voice provider: ${voiceId} -> ${foundProvider}`);
    } else {
      // If voice is not found anywhere, use default
      finalVoiceId = 'sarah';
      finalVoiceProvider = '11labs';
      console.log(`[EnhancedSyncHelpers] Voice ${voiceId} not found, using default: sarah/11labs`);
    }
  }

  return {
    voiceId: finalVoiceId,
    provider: finalVoiceProvider
  };
};

/**
 * Ensure profile persistence by synchronizing data between Supabase and Vapi
 *
 * This function implements the one-way sync pattern (UI → Supabase → Vapi)
 * with Supabase as the primary source of truth.
 *
 * @param {Object} params - The parameters for the function
 * @param {string} params.attorneyId - The ID of the attorney
 * @param {boolean} [params.forceUpdate=false] - Whether to force an update even if no discrepancies are found
 * @returns {Promise<Object>} - The result of the synchronization
 */
export const ensureProfilePersistence = async (params) => {
  const { attorneyId, forceUpdate = false } = params;

  if (process.env.NODE_ENV === 'development') {
    console.log(`[EnhancedSyncHelpers] Ensuring profile persistence for attorney ${attorneyId}`);
  }

  try {
    // Step 1: Get data from Supabase (source of truth)
    const supabaseData = await fetchFromSupabase(attorneyId);
    if (!supabaseData) {
      throw new Error(`Attorney not found in Supabase: ${attorneyId}`);
    }

    console.log('[EnhancedSyncHelpers] Retrieved data from Supabase:', supabaseData.id);

    // Step 2: Check if attorney has a Vapi assistant ID
    let vapiData = null;
    let vapiUpdateResult = null;

    if (!supabaseData.vapi_assistant_id) {
      // CRITICAL: Add rate limiting and checks before creating assistants
      console.log('[EnhancedSyncHelpers] No Vapi assistant ID found for attorney:', supabaseData.email);

      // Check if this is the system assistant (<EMAIL>)
      const isSystemAssistant = supabaseData.email === '<EMAIL>';

      // Add rate limiting to prevent mass creation
      const rateLimitKey = `assistant_creation_${attorneyId}`;
      const lastCreation = localStorage.getItem(rateLimitKey);
      const now = Date.now();
      const RATE_LIMIT_MS = 60000; // 1 minute between creations

      if (lastCreation && (now - parseInt(lastCreation)) < RATE_LIMIT_MS) {
        console.warn('[EnhancedSyncHelpers] Rate limit hit - assistant creation blocked');
        return {
          action: "rate_limited",
          success: false,
          message: "Assistant creation rate limited - please wait before trying again"
        };
      }

      // Only create assistant if explicitly requested or for system assistant
      if (!isSystemAssistant) {
        console.warn('[EnhancedSyncHelpers] Assistant creation skipped - manual creation required for non-system assistants');
        return {
          action: "creation_skipped",
          success: true,
          message: "No assistant ID found - manual creation required",
          requiresManualCreation: true
        };
      }

      try {
        console.log('[EnhancedSyncHelpers] Creating system <NAME_EMAIL>');

        // Set rate limit
        localStorage.setItem(rateLimitKey, now.toString());

        // Validate voice configuration before creating assistant
        const voiceConfig = validateVoiceConfiguration(
          supabaseData.voice_id || 'sarah',
          supabaseData.voice_provider || '11labs'
        );

        const newAssistant = await createVapiAssistant({
          name: supabaseData.firm_name || 'LegalScout Assistant',
          firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
          instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
          voice: {
            provider: voiceConfig.provider,
            voiceId: voiceConfig.voiceId
          },
          ai_model: supabaseData.ai_model || 'gpt-4o'
        });

        // Update Supabase with the new assistant ID
        await updateSupabaseAttorney(attorneyId, {
          vapi_assistant_id: newAssistant.id
        });

        console.log('[EnhancedSyncHelpers] Created new system assistant:', newAssistant.id);

        vapiUpdateResult = {
          action: "created",
          assistantId: newAssistant.id
        };
      } catch (createError) {
        console.error('[EnhancedSyncHelpers] Error creating Vapi assistant:', createError);
        throw createError;
      }
    } else {
      // Get the existing assistant from Vapi
      try {
        vapiData = await fetchFromVapi(supabaseData.vapi_assistant_id);
        console.log('[EnhancedSyncHelpers] Retrieved data from Vapi:', vapiData ? vapiData.id : 'not found');

        // If assistant not found in Vapi, DO NOT automatically create a new one
        if (!vapiData) {
          console.warn('[EnhancedSyncHelpers] Assistant not found in Vapi - this should be handled manually to prevent mass creation');
          console.warn('[EnhancedSyncHelpers] Assistant ID in Supabase:', supabaseData.vapi_assistant_id);
          console.warn('[EnhancedSyncHelpers] Attorney:', supabaseData.firm_name, supabaseData.email);

          // CRITICAL: Do not automatically create new assistants when old ones are missing
          // This was causing the mass creation of thousands of assistants

          // Instead, clear the invalid assistant ID and require manual intervention
          await updateSupabaseAttorney(attorneyId, {
            vapi_assistant_id: null
          });

          console.log('[EnhancedSyncHelpers] Cleared invalid assistant ID from Supabase - manual assistant creation required');

          vapiUpdateResult = {
            action: "cleared_invalid_id",
            assistantId: null,
            message: "Invalid assistant ID cleared - manual creation required"
          };
        } else {
          // Check for discrepancies and update if needed
          const discrepancies = findProfileDiscrepancies(supabaseData, vapiData);

          if (Object.keys(discrepancies).length > 0 || forceUpdate) {
            console.log('[EnhancedSyncHelpers] Discrepancies found, updating Vapi assistant:', discrepancies);

            try {
              // Validate voice configuration before updating assistant
              const voiceConfig = validateVoiceConfiguration(
                supabaseData.voice_id || 'sarah',
                supabaseData.voice_provider || '11labs'
              );

              const updatedAssistant = await updateVapiAssistant(supabaseData.vapi_assistant_id, {
                name: supabaseData.firm_name || 'LegalScout Assistant',
                firstMessage: supabaseData.welcome_message || 'Hello, how can I help you today?',
                instructions: supabaseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
                voice: {
                  provider: voiceConfig.provider,
                  voiceId: voiceConfig.voiceId
                },
                ai_model: supabaseData.ai_model || 'gpt-4o'
              });

              console.log('[EnhancedSyncHelpers] Updated Vapi assistant:', updatedAssistant.id);

              vapiUpdateResult = {
                action: "updated",
                assistantId: updatedAssistant.id,
                discrepancies: Object.keys(discrepancies).length > 0 ? discrepancies : null
              };
            } catch (updateError) {
              console.error('[EnhancedSyncHelpers] Error updating Vapi assistant:', updateError);
              throw updateError;
            }
          } else {
            console.log('[EnhancedSyncHelpers] No discrepancies found, Vapi assistant is up to date');

            vapiUpdateResult = {
              action: "none",
              assistantId: supabaseData.vapi_assistant_id
            };
          }
        }
      } catch (vapiError) {
        console.error('[EnhancedSyncHelpers] Error fetching from Vapi:', vapiError);
        throw vapiError;
      }
    }

    // Return the result
    return {
      action: vapiUpdateResult ? vapiUpdateResult.action : "checked",
      success: true,
      message: "Profile persistence ensured",
      sources: {
        supabase: !!supabaseData,
        vapi: !!vapiData
      },
      vapiResult: vapiUpdateResult
    };
  } catch (error) {
    // Only log in development to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      console.error('[EnhancedSyncHelpers] Error ensuring profile persistence:', error);
    }

    return {
      action: "error",
      success: false,
      message: `Error ensuring profile persistence: ${error.message}`,
      error: error.message
    };
  }
};
