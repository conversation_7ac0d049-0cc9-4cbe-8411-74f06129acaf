import React, { useRef, useState, useEffect } from "react";
import { FaBrain, FaRocket, FaHandshake, FaClock, FaAngleDown } from "react-icons/fa";
import { Link } from "react-router-dom";
import styles from "./AboutPage.module.css";

export default function AboutPage() {
  // Refs & Scroll Animations
  const containerRef = useRef(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Handle scroll progress
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = scrollTop / docHeight;
      setScrollProgress(scrollPercent);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // State Hooks
  const [isVisible, setIsVisible] = useState({});
  const [openFaq, setOpenFaq] = useState(null);

  // Refs for Intersection Observer
  const sectionRefs = {
    features: useRef(null),
    steps: useRef(null),
    faq: useRef(null)
  };

  // Intersection Observer Setup
  useEffect(() => {
    const observers = {};

    Object.entries(sectionRefs).forEach(([key, ref]) => {
      observers[key] = new IntersectionObserver(
        ([entry]) => {
          setIsVisible(prev => ({ ...prev, [key]: entry.isIntersecting }));
        },
        { threshold: 0.2 }
      );

      if (ref.current) {
        observers[key].observe(ref.current);
      }
    });

    return () => {
      Object.values(observers).forEach(observer => observer.disconnect());
    };
  }, []);

  // Features Data
  const features = [
    {
      icon: <FaBrain />,
      title: "AI-Powered Matching",
      description: "Our AI updates you as qualified attorneys review your anonymous brief.",
      highlight: "Experience real-time updates and intelligent matching."
    },
    {
      icon: <FaRocket />,
      title: "Quality-First Approach",
      description: "Attorneys pay to contact you, ensuring genuine interest and expertise.",
      highlight: "Only committed attorneys reach out to you."
    },
    {
      icon: <FaHandshake />,
      title: "Free For You",
      description: "Our service is completely free and respects your privacy.",
      highlight: "No hidden fees or commitments."
    },
    {
      icon: <FaClock />,
      title: "Time-Saving",
      description: "Share your story once and let us handle the rest.",
      highlight: "Focus on what matters most to you."
    }
  ];

  // Steps Data
  const steps = [
    {
      step: "1",
      title: "Introduce Your Legal Matter",
      description: "Start the conversation with Scout, our intelligent assistant."
    },
    {
      step: "2",
      title: "Detail Your Needs",
      description: "Scout guides you through a detailed exploration of your case."
    },
    {
      step: "3",
      title: "Match and Meet Your Attorney",
      description: "Get connected with an ideal attorney knowledgeable about your issue."
    }
  ];

  // FAQ Data
  const faqs = [
    {
      question: "If LegalScout is free to use, how do you make money?",
      answer: "Registered attorneys may view case summaries within their expertise, but must pay to contact you - ensuring genuine interest and expressing confidence.",
      detail: "This model ensures that only serious and qualified attorneys reach out to you, while keeping our service completely free for those seeking legal help."
    },
    {
      question: "How can I shape the platform?",
      answer: "Your insights directly influence LegalScout's development, ensuring it meets real-world needs.",
      detail: "We actively collect and implement user feedback to continuously improve our platform and better serve our community."
    },
    {
      question: "What does sharing LegalScout accomplish?",
      answer: "By sharing with your network, you're helping us grow a supportive legal community that benefits everyone.",
      detail: "A larger community means more attorneys, better matches, and improved access to legal help for all."
    },
    {
      question: "How does expanding the community benefit me?",
      answer: "As our community grows, enjoy broader coverage and quicker service, making legal assistance more accessible.",
      detail: "More attorneys in our network means faster response times and better matches for your specific legal needs."
    }
  ];

  return (
    <div ref={containerRef} className={styles.page}>
      {/* Scroll Progress Bar */}
      <div
        className={styles.scrollProgress}
        style={{ transform: `scaleX(${scrollProgress})` }}
      />

      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroBackground}></div>

        <div className={`${styles.heroContent} animate-fade-in`}>
          <h1 className={`${styles.heroTitle} animate-fade-in-delay-200`}>
            Revolutionizing Legal Help
          </h1>

          <p className={`${styles.heroDescription} animate-fade-in-delay-400`}>
            LegalScout changes the way you find legal assistance. Instead of you searching for an attorney,
            we have attorneys search for you.
          </p>
        </div>

        <div className={`${styles.scrollPrompt} animate-fade-in-delay-800 animate-bounce`}>
          <span>Scroll to explore LegalScout</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={styles.scrollArrow}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
            width="24"
            height="24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* Features Section */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Why Choose LegalScout?</h2>

        <div
          ref={sectionRefs.features}
          className={styles.featuresContainer}
        >
          {features.map((feature, index) => (
            <div
              key={index}
              className={`${styles.featureCard} ${isVisible.features ? 'animate-fade-in' : 'opacity-0'}`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className={styles.featureIcon}>
                {feature.icon}
              </div>

              <h3 className={styles.featureTitle}>{feature.title}</h3>
              <p className={styles.featureDescription}>{feature.description}</p>

              <div className={styles.featureHighlight}>
                {feature.highlight}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* How It Works Section */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>How It Works</h2>

        <div
          ref={sectionRefs.steps}
          className={styles.stepsContainer}
        >
          {steps.map((step, index) => (
            <div
              key={index}
              className={`${styles.stepCard} ${isVisible.steps ? 'animate-fade-in' : 'opacity-0'}`}
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className={styles.stepNumber}>{step.step}</div>
              <h3 className={styles.stepTitle}>{step.title}</h3>
              <p className={styles.stepDescription}>{step.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Availability Section */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Always Available</h2>

        <div className={styles.availabilityContainer}>
          <div
            className={`${styles.availabilityCard} animate-fade-in`}
          >
            <div className={styles.availabilityIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </div>
            <h3 className={styles.availabilityTitle}>24/7 Access</h3>
            <p className={styles.availabilityDescription}>
              Our platform is available around the clock, allowing you to submit your case information at any time that's convenient for you.
            </p>
          </div>

          <div
            className={`${styles.availabilityCard} animate-fade-in`}
            style={{ animationDelay: '200ms' }}
          >
            <div className={styles.availabilityIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
            </div>
            <h3 className={styles.availabilityTitle}>Secure Platform</h3>
            <p className={styles.availabilityDescription}>
              Your information is protected with enterprise-grade security, ensuring your legal matters remain confidential at all times.
            </p>
          </div>

          <div
            className={`${styles.availabilityCard} animate-fade-in`}
            style={{ animationDelay: '400ms' }}
          >
            <div className={styles.availabilityIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
            </div>
            <h3 className={styles.availabilityTitle}>Quick Response</h3>
            <p className={styles.availabilityDescription}>
              Attorneys are notified in real-time about potential matches, ensuring you receive responses as quickly as possible.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Frequently Asked Questions</h2>

        <div
          ref={sectionRefs.faq}
          className={styles.faqContainer}
        >
          {faqs.map((faq, index) => (
            <div
              key={index}
              className={`${styles.faqItem} ${isVisible.faq ? 'animate-fade-in' : 'opacity-0'}`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div
                className={styles.faqQuestion}
                onClick={() => setOpenFaq(openFaq === index ? null : index)}
              >
                {faq.question}
                <div
                  className={styles.faqArrow}
                  style={{ transform: `rotate(${openFaq === index ? 180 : 0}deg)`, transition: 'transform 0.3s ease' }}
                >
                  <FaAngleDown size={14} />
                </div>
              </div>

              <div
                className={styles.faqAnswerWrapper}
                style={{
                  height: openFaq === index ? 'auto' : '0',
                  opacity: openFaq === index ? 1 : 0,
                  overflow: 'hidden',
                  transition: 'opacity 0.3s ease'
                }}
              >
                <div className={styles.faqAnswer}>
                  <p>{faq.answer}</p>
                  <div className={styles.faqDetail}>
                    {faq.detail}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section
        className={`${styles.ctaSection} animate-fade-in`}
      >
        <h2 className={styles.ctaTitle}>Ready to Get Started?</h2>
        <p className={styles.ctaText}>
          Join LegalScout today and revolutionize your legal journey.
        </p>

        <div className="hover-scale">
          <Link to="/" className={styles.ctaButton}>
            Start Your Journey
          </Link>
        </div>
      </section>
    </div>
  );
}