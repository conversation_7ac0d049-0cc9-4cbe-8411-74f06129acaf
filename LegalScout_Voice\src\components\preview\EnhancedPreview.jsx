import React, { useState, useEffect, useRef } from 'react';
import { DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants';
import VapiCall from '../VapiCall';
import HybridVapiCall from '../HybridVapiCall';
import './EnhancedPreview.css';

// Utility to convert hex to RGB for opacity handling
const hexToRgb = (hex) => {
  if (!hex) return '0, 0, 0';

  // Remove the # if present
  hex = hex.replace('#', '');

  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Return the RGB values as a string
  return `${r}, ${g}, ${b}`;
};

/**
 * Enhanced Preview Component with Vapi Integration
 *
 * This component provides a preview of the attorney interface with full Vapi integration.
 * It accepts all configuration variables and passes them to the Vapi call.
 */
const EnhancedPreview = ({
  // Firm details
  firmName = 'Your Law Firm',
  attorneyName = 'Your Name',
  practiceAreas = [],
  state = '',
  practiceDescription = "Your AI legal assistant is ready to help",

  // Visual customization
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  backgroundColor = '#1a1a1a',
  backgroundOpacity = 0.9,
  buttonText = 'Start Consultation',
  buttonOpacity = 1,
  practiceAreaBackgroundOpacity = 0.1,
  textBackgroundColor = '#634C38',

  // Content
  welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",

  // Assets
  logoUrl = '/PRIMARY CLEAR.png',
  mascot = '/PRIMARY CLEAR.png',

  // Vapi configuration
  vapiInstructions = '',
  vapiContext = '',

  // Theme
  theme = 'dark'
}) => {
  // State for UI
  const [showStartButton, setShowStartButton] = useState(true);
  const [chatActive, setChatActive] = useState(false);

  // State for customizable properties
  const [firmNameState, setFirmName] = useState(firmName);
  const [attorneyNameState, setAttorneyName] = useState(attorneyName);
  const [practiceAreasState, setPracticeAreas] = useState(practiceAreas);
  const [stateState, setState] = useState(state);
  const [practiceDescriptionState, setPracticeDescription] = useState(practiceDescription);
  const [primaryColorState, setPrimaryColor] = useState(primaryColor);
  const [secondaryColorState, setSecondaryColor] = useState(secondaryColor);
  const [backgroundColorState, setBackgroundColor] = useState(backgroundColor);
  const [backgroundOpacityState, setBackgroundOpacity] = useState(backgroundOpacity);
  const [buttonTextState, setButtonText] = useState(buttonText);
  const [buttonOpacityState, setButtonOpacity] = useState(buttonOpacity);
  const [practiceAreaBackgroundOpacityState, setPracticeAreaBackgroundOpacity] = useState(practiceAreaBackgroundOpacity);
  const [textBackgroundColorState, setTextBackgroundColor] = useState(textBackgroundColor);
  const [welcomeMessageState, setWelcomeMessage] = useState(welcomeMessage);
  const [informationGatheringState, setInformationGathering] = useState(informationGathering);
  const [logoUrlState, setLogoUrl] = useState(logoUrl);
  const [mascotState, setMascot] = useState(mascot);
  const [vapiInstructionsState, setVapiInstructions] = useState(vapiInstructions);
  const [vapiContextState, setVapiContext] = useState(vapiContext);
  const [themeState, setTheme] = useState(theme);
  const [messages, setMessages] = useState([{ sender: 'bot', text: welcomeMessage }]);

  // Derived state
  const isDark = themeState !== 'light';

  // Refs
  const contentRef = useRef(null);

  // Update messages when welcome message changes
  useEffect(() => {
    setMessages([{ sender: 'bot', text: welcomeMessageState }]);
  }, [welcomeMessageState]);

  // Handle messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('EnhancedPreview received message:', event.data?.type);

      // Handle both message types: 'updateCustomizations' (from DemoPage) and 'UPDATE_PREVIEW_CONFIG' (from Dashboard)
      if (event.data && (event.data.type === 'updateCustomizations' || event.data.type === 'UPDATE_PREVIEW_CONFIG')) {
        // Get customizations from the appropriate property based on message type
        const customizations = event.data.type === 'updateCustomizations'
          ? event.data.customizations
          : event.data.config;

        console.log('Received customization updates:', customizations);

        // Update all state variables based on customizations
        if (customizations.firmName) setFirmName(customizations.firmName);
        if (customizations.attorneyName) setAttorneyName(customizations.attorneyName);
        if (customizations.practiceAreas) setPracticeAreas(customizations.practiceAreas);
        if (customizations.state) setState(customizations.state);
        if (customizations.practiceDescription) setPracticeDescription(customizations.practiceDescription);
        if (customizations.primaryColor) setPrimaryColor(customizations.primaryColor);
        if (customizations.secondaryColor) setSecondaryColor(customizations.secondaryColor);
        if (customizations.backgroundColor) setBackgroundColor(customizations.backgroundColor);
        if (customizations.backgroundOpacity) setBackgroundOpacity(customizations.backgroundOpacity);
        if (customizations.buttonText) setButtonText(customizations.buttonText);
        if (customizations.buttonOpacity) setButtonOpacity(customizations.buttonOpacity);
        if (customizations.practiceAreaBackgroundOpacity) setPracticeAreaBackgroundOpacity(customizations.practiceAreaBackgroundOpacity);
        if (customizations.textBackgroundColor) setTextBackgroundColor(customizations.textBackgroundColor);
        if (customizations.welcomeMessage) {
          setWelcomeMessage(customizations.welcomeMessage);
          setMessages([{ sender: 'bot', text: customizations.welcomeMessage }]);
        }
        if (customizations.informationGathering) setInformationGathering(customizations.informationGathering);
        if (customizations.logoUrl) setLogoUrl(customizations.logoUrl);
        if (customizations.mascot) setMascot(customizations.mascot);
        if (customizations.vapiInstructions) setVapiInstructions(customizations.vapiInstructions);
        if (customizations.vapiContext) setVapiContext(customizations.vapiContext);
        if (customizations.theme) setTheme(customizations.theme);
      }
    };

    window.addEventListener('message', handleMessage);

    // Send ready message to parent
    if (window !== window.parent) {
      try {
        window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');
        console.log('EnhancedPreview: Sent ready message to parent');
      } catch (e) {
        console.error('EnhancedPreview: Error sending message to parent:', e);
      }
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Debug logging for props
  useEffect(() => {
    console.log('EnhancedPreview state updated:');
    console.log('firmName:', firmNameState);
    console.log('logoUrl:', logoUrlState);
    console.log('primaryColor:', primaryColorState);
    console.log('secondaryColor:', secondaryColorState);
    console.log('vapiInstructions:', vapiInstructionsState);
  }, [firmNameState, logoUrlState, primaryColorState, secondaryColorState, vapiInstructionsState]);

  // Handle start consultation button click
  const handleStartConsultation = () => {
    console.log("Starting consultation...");
    setShowStartButton(false);
    setChatActive(true);
  };

  // Handle ending the call
  const handleEndCall = () => {
    console.log("Ending call...");
    setChatActive(false);
    setShowStartButton(true);
  };

  // Convert markdown to HTML for practice description
  const renderPracticeDescription = () => {
    if (!practiceDescriptionState) return '';

    // Simple markdown parser
    return practiceDescriptionState
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/### (.*?)$/gm, '<h3>$1</h3>') // H3
      .replace(/## (.*?)$/gm, '<h2>$1</h2>') // H2
      .replace(/# (.*?)$/gm, '<h1>$1</h1>') // H1
      .replace(/- (.*?)$/gm, '<li>$1</li>') // List items
      .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // Wrap list items
      .replace(/<\/ul><ul>/g, '') // Fix multiple lists
      .replace(/\n/g, '<br>'); // Line breaks
  };

  // Get the appropriate logo to use
  const getLogoToUse = () => {
    // Prioritize user uploaded logo
    if (logoUrlState && logoUrlState !== '/PRIMARY CLEAR.png') {
      console.log('Using custom logoUrl:', logoUrlState);
      return logoUrlState;
    }

    // Fallback to mascot
    if (mascotState && mascotState !== '/PRIMARY CLEAR.png') {
      console.log('Using mascot:', mascotState);
      return mascotState;
    }

    // Default fallback
    console.log('Using default logo: /PRIMARY CLEAR.png');
    return '/PRIMARY CLEAR.png';
  };

  const logoToUse = getLogoToUse();

  return (
    <div
      className="enhanced-preview-container"
      style={{
        backgroundColor: `rgba(${hexToRgb(backgroundColorState)}, ${backgroundOpacityState})`,
        color: isDark ? '#ffffff' : '#333333'
      }}
      ref={contentRef}
    >
      {/* Header removed - firm name header should not appear in preview */}

      {/* Main content area */}
      <div className="preview-content">
        {showStartButton ? (
          <div className="start-button-container">
            <h2
              className="preview-heading"
              style={{ color: primaryColorState }}
            >
              {firmNameState}
            </h2>
            <div
              className="practice-description"
              style={{
                backgroundColor: `rgba(${hexToRgb(textBackgroundColorState)}, ${practiceAreaBackgroundOpacityState})`
              }}
              dangerouslySetInnerHTML={{ __html: renderPracticeDescription() }}
            />
            <button
              className="start-consultation-button"
              onClick={handleStartConsultation}
              style={{
                backgroundColor: `rgba(${hexToRgb(secondaryColorState)}, ${buttonOpacityState})`,
                boxShadow: `0 4px 8px rgba(${hexToRgb(secondaryColorState)}, 0.3)`
              }}
            >
              <img
                src={logoToUse}
                alt="Logo"
                className="button-logo"
                onError={(e) => {
                  console.error('Error loading logo in button:', e);
                  e.target.style.display = 'none';
                }}
              />
              {buttonTextState || 'Start Consultation'}
            </button>
          </div>
        ) : (
          <div className="chat-container" style={{ position: 'relative' }}>
            {chatActive && (
              <HybridVapiCall
                onEndCall={handleEndCall}
                subdomain="default"
                customInstructions={{
                  initialMessage: welcomeMessageState,
                  firmName: firmNameState,
                  attorneyName: attorneyNameState,
                  practiceAreas: practiceAreasState,
                  state: stateState,
                  primaryColor: primaryColorState,
                  secondaryColor: secondaryColorState,
                  vapiInstructions: vapiInstructionsState,
                  vapiContext: vapiContextState
                }}
                // Hybrid-specific props
                enableFallback={true}
                fallbackTimeout={8000}
                showModeToggle={true}
                showDebugPanel={false}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedPreview;
