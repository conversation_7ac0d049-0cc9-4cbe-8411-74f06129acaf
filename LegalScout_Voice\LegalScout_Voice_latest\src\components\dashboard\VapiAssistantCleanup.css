.vapi-assistant-cleanup {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.cleanup-header {
  margin-bottom: 24px;
}

.cleanup-header h3 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 1.5rem;
}

.cleanup-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.cleanup-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.cleanup-actions .btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cleanup-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cleanup-actions .btn-primary {
  background: #007bff;
  color: white;
}

.cleanup-actions .btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.cleanup-actions .btn-secondary {
  background: #6c757d;
  color: white;
}

.cleanup-actions .btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.cleanup-actions .btn-danger {
  background: #dc3545;
  color: white;
}

.cleanup-actions .btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.cleanup-analysis {
  border-top: 1px solid #e9ecef;
  padding-top: 24px;
}

.cleanup-analysis h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 1.2rem;
}

.analysis-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #dee2e6;
}

.stat.safe-to-delete {
  border-left-color: #28a745;
  background: #f8fff9;
}

.stat-label {
  font-weight: 500;
  color: #495057;
}

.stat-value {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 1.1rem;
}

.safe-to-delete-list,
.protected-list,
.has-calls-default-list {
  margin-bottom: 24px;
}

.safe-to-delete-list h5,
.protected-list h5,
.has-calls-default-list h5 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
  font-size: 1rem;
}

.assistant-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assistant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #dee2e6;
}

.assistant-item.protected {
  border-left-color: #007bff;
  background: #f8fbff;
}

.assistant-item.warning {
  border-left-color: #ffc107;
  background: #fffdf5;
}

.assistant-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.assistant-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  align-self: flex-start;
}

.assistant-name {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.assistant-date,
.call-count {
  font-size: 0.8rem;
  color: #6c757d;
}

.assistant-reason {
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
  text-align: right;
  max-width: 200px;
}

.cleanup-results {
  border-top: 1px solid #e9ecef;
  padding-top: 24px;
  margin-top: 24px;
}

.cleanup-results h4 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 1.2rem;
}

.results-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.results-stats .stat.success {
  border-left-color: #28a745;
  background: #f8fff9;
}

.results-stats .stat.error {
  border-left-color: #dc3545;
  background: #fff8f8;
}

.results-stats .stat.skipped {
  border-left-color: #ffc107;
  background: #fffdf5;
}

@media (max-width: 768px) {
  .cleanup-actions {
    flex-direction: column;
  }
  
  .cleanup-actions .btn {
    width: 100%;
    justify-content: center;
  }
  
  .analysis-stats {
    grid-template-columns: 1fr;
  }
  
  .assistant-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .assistant-reason {
    text-align: left;
    max-width: none;
  }
}
