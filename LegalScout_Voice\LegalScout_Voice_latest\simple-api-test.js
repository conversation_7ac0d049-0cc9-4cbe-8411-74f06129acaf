import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Environment Variables Test:');
console.log('SUPABASE_URL:', !!process.env.SUPABASE_URL);
console.log('VITE_SUPABASE_URL:', !!process.env.VITE_SUPABASE_URL);

const app = express();
const PORT = 3001;

app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    env: {
      SUPABASE_URL: !!process.env.SUPABASE_URL,
      VITE_SUPABASE_URL: !!process.env.VITE_SUPABASE_URL
    }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple API server running on http://localhost:${PORT}`);
});
