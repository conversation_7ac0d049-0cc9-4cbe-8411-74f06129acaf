/**
 * Test script to verify conflict resolution is working
 * Run this to test the new conflict-aware sync logic
 */

import { supabase } from '../lib/supabase.js';
import { checkAssistantIdConflicts, ensureProfilePersistence } from '../services/syncHelpers.js';

async function testConflictResolution() {
  console.log('🔍 Testing Vapi Assistant Conflict Resolution...\n');

  try {
    // Get your attorney record
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      console.error('❌ Error fetching attorney:', error);
      return;
    }

    console.log('👤 Attorney found:', {
      email: attorney.email,
      firmName: attorney.firm_name,
      subdomain: attorney.subdomain,
      assistantId: attorney.vapi_assistant_id
    });

    // Check for conflicts
    console.log('\n🔍 Checking for assistant ID conflicts...');
    const conflictCheck = await checkAssistantIdConflicts(attorney.vapi_assistant_id, attorney.id);

    if (conflictCheck.hasConflicts) {
      console.log('⚠️ Conflicts detected!');
      console.log('Conflicting attorneys:', conflictCheck.conflicts);

      console.log('\n🔧 Resolving conflicts using enhanced sync...');
      const syncResult = await ensureProfilePersistence({
        attorneyId: attorney.id,
        localData: attorney,
        forceUpdate: true
      });

      console.log('✅ Sync result:', syncResult);

      if (syncResult.success && syncResult.vapiResult?.action === 'created_new_to_resolve_conflict') {
        console.log('🎉 New assistant created to resolve conflict!');
        console.log('New assistant ID:', syncResult.vapiResult.assistantId);
      }
    } else {
      console.log('✅ No conflicts found - assistant ID is unique to this attorney');
    }

  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testConflictResolution();
}

export { testConflictResolution };
