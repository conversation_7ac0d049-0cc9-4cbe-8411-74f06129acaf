-- Setup data for assistant1test subdomain
-- Run this in Supabase SQL Editor to create the required mapping

-- First, let's check if the attorney exists
DO $$
DECLARE
    attorney_uuid UUID;
    existing_attorney_count INTEGER;
BEGIN
    -- Check if attorney already exists
    SELECT COUNT(*) INTO existing_attorney_count 
    FROM attorneys 
    WHERE id = '87756a2c-a398-43f2-889a-b8815684df71';
    
    IF existing_attorney_count = 0 THEN
        -- Insert the attorney record
        INSERT INTO attorneys (
            id,
            firm_name,
            name,
            email,
            phone,
            logo_url,
            vapi_instructions,
            ai_model,
            voice_id,
            welcome_message,
            primary_color,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            '87756a2c-a398-43f2-889a-b8815684df71',
            'LegalScout',
            'LegalScout Assistant',
            '<EMAIL>',
            '(*************',
            'https://legalscout.net/logo.png',
            'You are a helpful legal assistant for LegalScout. You help potential clients understand their legal options and connect them with qualified attorneys.',
            'gpt-4o',
            'alloy',
            'Hello! I''m your LegalScout AI assistant. How can I help you with your legal questions today?',
            '#3B82F6',
            true,
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Attorney record created with ID: 87756a2c-a398-43f2-889a-b8815684df71';
    ELSE
        RAISE NOTICE 'Attorney record already exists';
    END IF;
    
    -- Now check if the assistant subdomain mapping exists
    DECLARE
        existing_mapping_count INTEGER;
    BEGIN
        SELECT COUNT(*) INTO existing_mapping_count 
        FROM assistant_subdomains 
        WHERE subdomain = 'assistant1test';
        
        IF existing_mapping_count = 0 THEN
            -- Insert the assistant subdomain mapping
            INSERT INTO assistant_subdomains (
                assistant_id,
                subdomain,
                attorney_id,
                is_primary,
                is_active,
                created_at,
                updated_at
            ) VALUES (
                'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d',
                'assistant1test',
                '87756a2c-a398-43f2-889a-b8815684df71',
                true,
                true,
                NOW(),
                NOW()
            );
            
            RAISE NOTICE 'Assistant subdomain mapping created for assistant1test';
        ELSE
            RAISE NOTICE 'Assistant subdomain mapping already exists for assistant1test';
        END IF;
    END;
END $$;

-- Verify the data was created correctly
SELECT 
    'Verification Results' as status,
    a.firm_name,
    a.name,
    a.email,
    asd.subdomain,
    asd.assistant_id,
    asd.is_primary,
    asd.is_active
FROM attorneys a
JOIN assistant_subdomains asd ON a.id = asd.attorney_id
WHERE asd.subdomain = 'assistant1test';

-- Also verify the view works
SELECT 
    'View Test Results' as status,
    subdomain,
    assistant_id,
    attorney_id,
    firm_name,
    is_primary,
    is_active
FROM v_subdomain_assistant_lookup
WHERE subdomain = 'assistant1test';
