import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useAttorneyState } from '../contexts/AttorneyStateContext';
import { FaPhone, FaUser, FaClock, FaCalendar, FaFileAlt } from 'react-icons/fa';
import './CallLogs.css';

/**
 * Call Logs Component
 * 
 * Displays call logs for the current attorney with row-level security.
 * Uses MCP for Vapi operations and server API for secure storage.
 */
const CallLogs = () => {
  const { user } = useAuth();
  const { attorney } = useAttorneyState();
  const [callLogs, setCallLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedLog, setSelectedLog] = useState(null);
  const [vapiCalls, setVapiCalls] = useState([]);

  // Fetch call logs from server
  useEffect(() => {
    const fetchCallLogs = async () => {
      if (!user || !attorney) return;
      
      try {
        setLoading(true);
        
        // Get token from Supabase
        const token = localStorage.getItem('supabase.auth.token');
        
        // Fetch call logs from server
        const response = await fetch('/api/call-logs', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch call logs: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setCallLogs(data);
      } catch (error) {
        console.error('Error fetching call logs:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCallLogs();
  }, [user, attorney]);

  // Fetch calls from Vapi using MCP
  useEffect(() => {
    const fetchVapiCalls = async () => {
      if (!window.mcp || !attorney?.vapi_assistant_id) return;
      
      try {
        // Get calls from Vapi via MCP
        const calls = await window.mcp.invoke('list_calls_vapi-mcp-server', {});
        
        // Filter calls for this attorney's assistant
        const filteredCalls = calls.filter(call => 
          call.assistantId === attorney.vapi_assistant_id
        );
        
        setVapiCalls(filteredCalls);
        
        // Sync new calls to our database
        syncNewCallsToDatabase(filteredCalls);
      } catch (error) {
        console.error('Error fetching Vapi calls:', error);
      }
    };
    
    fetchVapiCalls();
  }, [attorney?.vapi_assistant_id]);

  // Sync new calls to our database
  const syncNewCallsToDatabase = async (calls) => {
    if (!user || !attorney || !calls.length) return;
    
    try {
      // Get token from Supabase
      const token = localStorage.getItem('supabase.auth.token');
      
      // Find calls that aren't in our database yet
      const existingCallIds = callLogs.map(log => log.call_id);
      const newCalls = calls.filter(call => !existingCallIds.includes(call.id));
      
      // Create call logs for new calls
      for (const call of newCalls) {
        try {
          // Get call details from Vapi
          const callDetails = await window.mcp.invoke('get_call_vapi-mcp-server', {
            callId: call.id
          });
          
          // Create call log in our database
          await fetch('/api/call-logs', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              call_id: call.id,
              assistant_id: attorney.vapi_assistant_id,
              customer_phone: call.customer?.phoneNumber || 'Unknown',
              duration: callDetails.duration || 0,
              status: callDetails.status || 'unknown',
              transcript: callDetails.transcript || '',
              summary: callDetails.summary || ''
            })
          });
        } catch (callError) {
          console.error(`Error syncing call ${call.id}:`, callError);
        }
      }
      
      // Refresh call logs if we added any
      if (newCalls.length > 0) {
        // Fetch call logs again
        const response = await fetch('/api/call-logs', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          setCallLogs(data);
        }
      }
    } catch (error) {
      console.error('Error syncing calls to database:', error);
    }
  };

  // View call details
  const viewCallDetails = async (callLog) => {
    setSelectedLog(callLog);
    
    // If we don't have transcript or summary, try to get it from Vapi
    if ((!callLog.transcript || !callLog.summary) && window.mcp) {
      try {
        const callDetails = await window.mcp.invoke('get_call_vapi-mcp-server', {
          callId: callLog.call_id
        });
        
        // Update the selected log with the new details
        setSelectedLog(prev => ({
          ...prev,
          transcript: callDetails.transcript || prev.transcript,
          summary: callDetails.summary || prev.summary
        }));
        
        // Update the call log in our database
        const token = localStorage.getItem('supabase.auth.token');
        await fetch(`/api/call-logs/${callLog.id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            transcript: callDetails.transcript,
            summary: callDetails.summary
          })
        });
      } catch (error) {
        console.error('Error fetching call details:', error);
      }
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Format duration
  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Render loading state
  if (loading) {
    return (
      <div className="call-logs-container">
        <h2>Call Logs</h2>
        <div className="loading">Loading call logs...</div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="call-logs-container">
        <h2>Call Logs</h2>
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  // Render call logs
  return (
    <div className="call-logs-container">
      <h2>Call Logs</h2>
      
      {callLogs.length === 0 ? (
        <div className="no-logs">No call logs found.</div>
      ) : (
        <div className="call-logs-content">
          <div className="call-logs-list">
            {callLogs.map(log => (
              <div 
                key={log.id} 
                className={`call-log-item ${selectedLog?.id === log.id ? 'selected' : ''}`}
                onClick={() => viewCallDetails(log)}
              >
                <div className="call-log-icon">
                  <FaPhone />
                </div>
                <div className="call-log-info">
                  <div className="call-log-phone">{log.customer_phone}</div>
                  <div className="call-log-date">{formatDate(log.created_at)}</div>
                </div>
                <div className="call-log-status">
                  <span className={`status-badge ${log.status}`}>{log.status}</span>
                </div>
              </div>
            ))}
          </div>
          
          {selectedLog && (
            <div className="call-log-details">
              <h3>Call Details</h3>
              
              <div className="detail-item">
                <FaUser className="detail-icon" />
                <div className="detail-content">
                  <div className="detail-label">Customer</div>
                  <div className="detail-value">{selectedLog.customer_phone}</div>
                </div>
              </div>
              
              <div className="detail-item">
                <FaCalendar className="detail-icon" />
                <div className="detail-content">
                  <div className="detail-label">Date & Time</div>
                  <div className="detail-value">{formatDate(selectedLog.created_at)}</div>
                </div>
              </div>
              
              <div className="detail-item">
                <FaClock className="detail-icon" />
                <div className="detail-content">
                  <div className="detail-label">Duration</div>
                  <div className="detail-value">{formatDuration(selectedLog.duration)}</div>
                </div>
              </div>
              
              {selectedLog.summary && (
                <div className="detail-section">
                  <h4>Summary</h4>
                  <div className="detail-text">{selectedLog.summary}</div>
                </div>
              )}
              
              {selectedLog.transcript && (
                <div className="detail-section">
                  <h4>Transcript</h4>
                  <div className="detail-text transcript">{selectedLog.transcript}</div>
                </div>
              )}
              
              <div className="detail-actions">
                <button 
                  className="action-button"
                  onClick={() => setSelectedLog(null)}
                >
                  Back to List
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CallLogs;
